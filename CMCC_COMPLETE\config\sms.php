<?php
/**
 * SMS短信服务配置
 */
return [
    // 默认SMS服务商
    'default' => env('SMS_PROVIDER', 'aliyun'),

    // 服务商配置
    'providers' => [
        // 阿里云短信
        'aliyun' => [
            'access_key_id' => env('ALIYUN_SMS_ACCESS_KEY_ID', ''),
            'access_key_secret' => env('ALIYUN_SMS_ACCESS_KEY_SECRET', ''),
            'sign_name' => env('ALIYUN_SMS_SIGN_NAME', 'GVD交易平台'),
            'region' => env('ALIYUN_SMS_REGION', 'cn-hangzhou'),
            'templates' => [
                'register' => env('ALIYUN_SMS_TEMPLATE_REGISTER', 'SMS_001'),
                'login' => env('ALIYUN_SMS_TEMPLATE_LOGIN', 'SMS_002'),
                'reset_password' => env('ALIYUN_SMS_TEMPLATE_RESET', 'SMS_003'),
                'withdraw' => env('ALIYUN_SMS_TEMPLATE_WITHDRAW', 'SMS_004'),
                'security' => env('ALIYUN_SMS_TEMPLATE_SECURITY', 'SMS_005'),
            ]
        ],

        // 腾讯云短信
        'tencent' => [
            'secret_id' => env('TENCENT_SMS_SECRET_ID', ''),
            'secret_key' => env('TENCENT_SMS_SECRET_KEY', ''),
            'sdk_app_id' => env('TENCENT_SMS_SDK_APP_ID', ''),
            'sign_name' => env('TENCENT_SMS_SIGN_NAME', 'GVD交易平台'),
            'region' => env('TENCENT_SMS_REGION', 'ap-beijing'),
            'templates' => [
                'register' => env('TENCENT_SMS_TEMPLATE_REGISTER', ''),
                'login' => env('TENCENT_SMS_TEMPLATE_LOGIN', ''),
                'reset_password' => env('TENCENT_SMS_TEMPLATE_RESET', ''),
                'withdraw' => env('TENCENT_SMS_TEMPLATE_WITHDRAW', ''),
                'security' => env('TENCENT_SMS_TEMPLATE_SECURITY', ''),
            ]
        ],

        // 华为云短信
        'huawei' => [
            'app_key' => env('HUAWEI_SMS_APP_KEY', ''),
            'app_secret' => env('HUAWEI_SMS_APP_SECRET', ''),
            'sender' => env('HUAWEI_SMS_SENDER', ''),
            'signature' => env('HUAWEI_SMS_SIGNATURE', 'GVD交易平台'),
            'url' => env('HUAWEI_SMS_URL', 'https://smsapi.cn-north-4.myhuaweicloud.com:443'),
            'templates' => [
                'register' => env('HUAWEI_SMS_TEMPLATE_REGISTER', ''),
                'login' => env('HUAWEI_SMS_TEMPLATE_LOGIN', ''),
                'reset_password' => env('HUAWEI_SMS_TEMPLATE_RESET', ''),
                'withdraw' => env('HUAWEI_SMS_TEMPLATE_WITHDRAW', ''),
                'security' => env('HUAWEI_SMS_TEMPLATE_SECURITY', ''),
            ]
        ],

        // Twilio（国际短信）
        'twilio' => [
            'account_sid' => env('TWILIO_ACCOUNT_SID', ''),
            'auth_token' => env('TWILIO_AUTH_TOKEN', ''),
            'from_number' => env('TWILIO_FROM_NUMBER', ''),
            'webhook_url' => env('TWILIO_WEBHOOK_URL', ''),
        ],

        // 容联云通讯
        'ronglian' => [
            'account_sid' => env('RONGLIAN_ACCOUNT_SID', ''),
            'auth_token' => env('RONGLIAN_AUTH_TOKEN', ''),
            'app_id' => env('RONGLIAN_APP_ID', ''),
            'server_ip' => env('RONGLIAN_SERVER_IP', 'app.cloopen.com'),
            'server_port' => env('RONGLIAN_SERVER_PORT', '8883'),
            'soft_version' => env('RONGLIAN_SOFT_VERSION', '2013-12-26'),
            'templates' => [
                'register' => env('RONGLIAN_SMS_TEMPLATE_REGISTER', ''),
                'login' => env('RONGLIAN_SMS_TEMPLATE_LOGIN', ''),
                'reset_password' => env('RONGLIAN_SMS_TEMPLATE_RESET', ''),
                'withdraw' => env('RONGLIAN_SMS_TEMPLATE_WITHDRAW', ''),
                'security' => env('RONGLIAN_SMS_TEMPLATE_SECURITY', ''),
            ]
        ]
    ],

    // 验证码配置
    'verification' => [
        'length' => 6, // 验证码长度
        'expire' => 300, // 过期时间（秒）
        'rate_limit' => [
            'max_attempts' => 5, // 每小时最大发送次数
            'window' => 3600, // 时间窗口（秒）
            'min_interval' => 60, // 最小发送间隔（秒）
        ]
    ],

    // 短信内容模板
    'templates' => [
        'register' => [
            'zh-CN' => '您的注册验证码是：{code}，{expire}分钟内有效。【GVD交易平台】',
            'en-US' => 'Your registration verification code is: {code}, valid for {expire} minutes. [GVD Trading]',
            'ja-JP' => '登録確認コードは：{code}、{expire}分間有効です。【GVD取引プラットフォーム】',
            'ko-KR' => '등록 인증 코드는: {code}, {expire}분간 유효합니다. [GVD거래플랫폼]'
        ],
        'login' => [
            'zh-CN' => '您的登录验证码是：{code}，{expire}分钟内有效。【GVD交易平台】',
            'en-US' => 'Your login verification code is: {code}, valid for {expire} minutes. [GVD Trading]',
            'ja-JP' => 'ログイン確認コードは：{code}、{expire}分間有効です。【GVD取引プラットフォーム】',
            'ko-KR' => '로그인 인증 코드는: {code}, {expire}분간 유효합니다. [GVD거래플랫폼]'
        ],
        'reset_password' => [
            'zh-CN' => '您的密码重置验证码是：{code}，{expire}分钟内有效。【GVD交易平台】',
            'en-US' => 'Your password reset code is: {code}, valid for {expire} minutes. [GVD Trading]',
            'ja-JP' => 'パスワードリセットコードは：{code}、{expire}分間有効です。【GVD取引プラットフォーム】',
            'ko-KR' => '비밀번호 재설정 코드는: {code}, {expire}분간 유효합니다. [GVD거래플랫폼]'
        ],
        'withdraw' => [
            'zh-CN' => '您的提现验证码是：{code}，{expire}分钟内有效。请勿泄露给他人。【GVD交易平台】',
            'en-US' => 'Your withdrawal code is: {code}, valid for {expire} minutes. Do not share with others. [GVD Trading]',
            'ja-JP' => '出金確認コードは：{code}、{expire}分間有効です。他人に教えないでください。【GVD取引プラットフォーム】',
            'ko-KR' => '출금 인증 코드는: {code}, {expire}분간 유효합니다. 타인에게 알리지 마세요. [GVD거래플랫폼]'
        ],
        'security' => [
            'zh-CN' => '您的安全验证码是：{code}，{expire}分钟内有效。如非本人操作请忽略。【GVD交易平台】',
            'en-US' => 'Your security code is: {code}, valid for {expire} minutes. Ignore if not requested by you. [GVD Trading]',
            'ja-JP' => 'セキュリティコードは：{code}、{expire}分間有効です。ご本人でない場合は無視してください。【GVD取引プラットフォーム】',
            'ko-KR' => '보안 인증 코드는: {code}, {expire}분간 유효합니다. 본인이 아닌 경우 무시하세요. [GVD거래플랫폼]'
        ]
    ],

    // 国际化支持
    'international' => [
        'enabled' => true,
        'default_country_code' => '+86',
        'supported_countries' => [
            '+86' => 'China',
            '+1' => 'United States',
            '+44' => 'United Kingdom',
            '+81' => 'Japan',
            '+82' => 'South Korea',
            '+65' => 'Singapore',
            '+852' => 'Hong Kong',
            '+886' => 'Taiwan',
            '+60' => 'Malaysia',
            '+66' => 'Thailand',
            '+84' => 'Vietnam',
            '+62' => 'Indonesia',
            '+63' => 'Philippines',
            '+91' => 'India',
            '+49' => 'Germany',
            '+33' => 'France',
            '+39' => 'Italy',
            '+34' => 'Spain',
            '+7' => 'Russia',
            '+55' => 'Brazil',
            '+52' => 'Mexico',
            '+61' => 'Australia',
            '+64' => 'New Zealand'
        ]
    ],

    // 监控和统计
    'monitoring' => [
        'enabled' => true,
        'success_rate_threshold' => 95, // 成功率阈值（%）
        'alert_email' => env('SMS_ALERT_EMAIL', ''),
        'daily_limit' => 10000, // 每日发送限制
        'monthly_limit' => 300000, // 每月发送限制
    ],

    // 备用服务商配置
    'fallback' => [
        'enabled' => true,
        'providers' => ['tencent', 'huawei'], // 备用服务商顺序
        'retry_times' => 2, // 重试次数
        'retry_interval' => 5, // 重试间隔（秒）
    ],

    // 调试模式
    'debug' => [
        'enabled' => env('SMS_DEBUG', false),
        'log_level' => 'info',
        'test_phones' => [
            '***********', // 测试手机号
            '***********',
        ],
        'test_code' => '123456', // 测试验证码
    ]
];
