<!DOCTYPE html>
<html lang="zh-CN" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>秒合约 - {$site_name|default='加密货币交易平台'}</title>
    <link rel="stylesheet" href="/static/css/modern-theme.css">
    <link rel="stylesheet" href="/static/css/contract.css">
</head>
<body>
    <div class="container-fluid">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="container">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="page-title">
                        <h1>秒合约交易</h1>
                        <p class="text-secondary">简单快速的二元期权交易</p>
                    </div>
                    <div class="header-actions">
                        <button class="btn btn-outline btn-sm" onclick="toggleTheme()">
                            <span id="themeIcon">🌙</span>
                        </button>
                        <a href="/contract/contract_ty" class="btn btn-primary btn-sm">体验合约</a>
                        <a href="/user/assets" class="btn btn-outline btn-sm">我的资产</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="row">
                <!-- 左侧交易面板 -->
                <div class="col-4">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">合约交易</h3>
                        </div>
                        <div class="card-body">
                            <!-- 交易对选择 -->
                            <div class="form-group">
                                <label class="form-label">选择交易对</label>
                                <select class="form-control" id="contractSymbol" onchange="changeSymbol()">
                                    <option value="BTC/USDT">BTC/USDT</option>
                                    <option value="ETH/USDT">ETH/USDT</option>
                                    <option value="BNB/USDT">BNB/USDT</option>
                                </select>
                            </div>

                            <!-- 当前价格 -->
                            <div class="price-display">
                                <div class="current-price">
                                    <span class="price-label">当前价格</span>
                                    <span class="price-value text-success" id="contractPrice">45,280.50</span>
                                </div>
                                <div class="price-change">
                                    <span class="change-value positive" id="contractChange">+2.45%</span>
                                </div>
                            </div>

                            <!-- 投注金额 -->
                            <div class="form-group">
                                <label class="form-label">投注金额 (USDT)</label>
                                <input type="number" class="form-control" id="contractAmount" placeholder="请输入投注金额" min="10" step="10">
                                <div class="amount-buttons">
                                    <button class="amount-btn" onclick="setAmount(10)">10</button>
                                    <button class="amount-btn" onclick="setAmount(50)">50</button>
                                    <button class="amount-btn" onclick="setAmount(100)">100</button>
                                    <button class="amount-btn" onclick="setAmount(500)">500</button>
                                </div>
                            </div>

                            <!-- 时长选择 -->
                            <div class="form-group">
                                <label class="form-label">合约时长</label>
                                <div class="duration-selector">
                                    <button class="duration-btn active" onclick="setDuration(1)">60秒</button>
                                    <button class="duration-btn" onclick="setDuration(3)">3分钟</button>
                                    <button class="duration-btn" onclick="setDuration(5)">5分钟</button>
                                </div>
                            </div>

                            <!-- 盈利比例 -->
                            <div class="profit-info">
                                <div class="profit-row">
                                    <span class="profit-label">盈利比例</span>
                                    <span class="profit-value text-success" id="profitRate">80%</span>
                                </div>
                                <div class="profit-row">
                                    <span class="profit-label">预期收益</span>
                                    <span class="profit-value text-primary" id="expectedProfit">0.00 USDT</span>
                                </div>
                            </div>

                            <!-- 交易按钮 -->
                            <div class="trade-buttons">
                                <button class="trade-btn buy-btn" onclick="createContract('buy')">
                                    <span class="btn-icon">📈</span>
                                    <span class="btn-text">买涨</span>
                                    <span class="btn-desc">价格上涨获利</span>
                                </button>
                                <button class="trade-btn sell-btn" onclick="createContract('sell')">
                                    <span class="btn-icon">📉</span>
                                    <span class="btn-text">买跌</span>
                                    <span class="btn-desc">价格下跌获利</span>
                                </button>
                            </div>

                            <!-- 余额显示 -->
                            <div class="balance-info">
                                <div class="balance-item">
                                    <span class="balance-label">USDT余额</span>
                                    <span class="balance-value" id="usdtBalance">{$user.usdt_balance|default='0.00'}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 中间图表区域 -->
                <div class="col-5">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">价格走势</h3>
                            <div class="chart-controls">
                                <button class="btn btn-outline btn-sm active" onclick="setChartInterval('1m')">1分</button>
                                <button class="btn btn-outline btn-sm" onclick="setChartInterval('5m')">5分</button>
                                <button class="btn btn-outline btn-sm" onclick="setChartInterval('15m')">15分</button>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="chart-container" id="contractChart"></div>
                        </div>
                    </div>

                    <!-- 最新成交 -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h3 class="card-title">最新成交</h3>
                        </div>
                        <div class="card-body p-0">
                            <div class="trades-list" id="latestTrades">
                                <!-- 动态加载最新成交数据 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧订单列表 -->
                <div class="col-3">
                    <div class="card">
                        <div class="card-header">
                            <div class="contract-tabs">
                                <button class="contract-tab active" onclick="switchContractTab('current')">进行中</button>
                                <button class="contract-tab" onclick="switchContractTab('history')">历史记录</button>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <!-- 进行中的合约 -->
                            <div class="contract-list active" id="currentContracts">
                                <div class="contract-empty" id="emptyContracts">
                                    <div class="empty-icon">📊</div>
                                    <div class="empty-text">暂无进行中的合约</div>
                                </div>
                            </div>

                            <!-- 历史记录 -->
                            <div class="contract-list" id="historyContracts">
                                <!-- 动态加载历史合约 -->
                            </div>
                        </div>
                    </div>

                    <!-- 统计信息 -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h3 class="card-title">交易统计</h3>
                        </div>
                        <div class="card-body">
                            <div class="stat-item">
                                <span class="stat-label">总订单数</span>
                                <span class="stat-value" id="totalOrders">{$stats.total_orders|default='0'}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">盈利订单</span>
                                <span class="stat-value text-success" id="winOrders">{$stats.win_orders|default='0'}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">胜率</span>
                                <span class="stat-value text-primary" id="winRate">{$stats.win_rate|default='0'}%</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">总盈亏</span>
                                <span class="stat-value" id="totalProfitLoss">{$stats.total_profit_loss|default='0.00'}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
    <script src="/static/js/contract.js"></script>
    <script>
        // 全局变量
        let currentSymbol = 'BTC/USDT';
        let currentDuration = 1;
        let currentAmount = 0;
        let contractChart = null;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initTheme();
            initContractPage();
            initContractChart();
            loadCurrentContracts();
            
            // 启动实时更新
            setInterval(updateContractData, 1000);
        });
    </script>
</body>
</html>
