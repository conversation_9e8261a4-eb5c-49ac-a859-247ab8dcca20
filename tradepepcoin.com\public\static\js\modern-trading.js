/* 现代化交易页面JavaScript - 火币风格 */

// 全局变量
let currentSymbol = 'BTCUSDT';
let currentOrderType = 'limit';
let currentTradeType = 'buy';
let klineChart = null;
let wsConnection = null;

// 初始化交易页面
function initTradingPage() {
    setupEventListeners();
    loadInitialData();
    updateUserAssets();
}

// 设置事件监听器
function setupEventListeners() {
    // 价格和数量输入框事件
    const buyPrice = document.getElementById('buyPrice');
    const buyAmount = document.getElementById('buyAmount');
    const sellPrice = document.getElementById('sellPrice');
    const sellAmount = document.getElementById('sellAmount');
    
    if (buyPrice) buyPrice.addEventListener('input', calculateBuyTotal);
    if (buyAmount) buyAmount.addEventListener('input', calculateBuyTotal);
    if (sellPrice) sellPrice.addEventListener('input', calculateSellTotal);
    if (sellAmount) sellAmount.addEventListener('input', calculateSellTotal);
}

// 切换交易类型（买入/卖出）
function switchTab(type) {
    currentTradeType = type;
    
    // 更新标签样式
    document.querySelectorAll('.trading-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    const activeTab = document.querySelector(`.trading-tab.${type}`);
    if (activeTab) activeTab.classList.add('active');
    
    // 切换表单
    document.querySelectorAll('.trading-form').forEach(form => {
        form.classList.remove('active');
    });
    const activeForm = document.getElementById(`${type}Form`);
    if (activeForm) activeForm.classList.add('active');
}

// 设置订单类型（限价/市价）
function setOrderType(type) {
    currentOrderType = type;
    
    // 更新按钮样式
    const container = event.target.closest('.order-type-selector');
    if (container) {
        container.querySelectorAll('.order-type-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        event.target.classList.add('active');
    }
    
    // 显示/隐藏价格输入框
    const activeForm = document.querySelector('.trading-form.active');
    if (activeForm) {
        const priceGroup = activeForm.querySelector('.price-input-group');
        if (priceGroup) {
            priceGroup.style.display = type === 'market' ? 'none' : 'block';
        }
    }
}

// 设置百分比
function setPercentage(type, percentage) {
    const usdtElement = document.getElementById('usdtAvailable');
    const btcElement = document.getElementById('btcAvailable');
    const priceElement = document.getElementById('currentPrice');
    
    if (!usdtElement || !btcElement || !priceElement) return;
    
    const availableBalance = type === 'buy' ? 
        parseFloat(usdtElement.textContent) :
        parseFloat(btcElement.textContent);
    
    const currentPrice = parseFloat(priceElement.textContent.replace(/,/g, ''));
    
    if (type === 'buy') {
        const totalUsdt = availableBalance * (percentage / 100);
        const amount = totalUsdt / currentPrice;
        
        const buyAmountInput = document.getElementById('buyAmount');
        const buyPriceInput = document.getElementById('buyPrice');
        
        if (buyAmountInput) buyAmountInput.value = amount.toFixed(8);
        if (currentOrderType === 'limit' && buyPriceInput) {
            buyPriceInput.value = currentPrice.toFixed(2);
        }
        calculateBuyTotal();
    } else {
        const amount = availableBalance * (percentage / 100);
        const sellAmountInput = document.getElementById('sellAmount');
        const sellPriceInput = document.getElementById('sellPrice');
        
        if (sellAmountInput) sellAmountInput.value = amount.toFixed(8);
        if (currentOrderType === 'limit' && sellPriceInput) {
            sellPriceInput.value = currentPrice.toFixed(2);
        }
        calculateSellTotal();
    }
}

// 计算买入总额
function calculateBuyTotal() {
    const priceInput = document.getElementById('buyPrice');
    const amountInput = document.getElementById('buyAmount');
    const feeElement = document.getElementById('buyFee');
    const totalElement = document.getElementById('buyTotal');
    
    if (!priceInput || !amountInput || !feeElement || !totalElement) return;
    
    let price = parseFloat(priceInput.value) || 0;
    const amount = parseFloat(amountInput.value) || 0;
    
    if (currentOrderType === 'market') {
        const currentPriceElement = document.getElementById('currentPrice');
        if (currentPriceElement) {
            price = parseFloat(currentPriceElement.textContent.replace(/,/g, ''));
        }
    }
    
    const total = price * amount;
    const fee = amount * 0.001; // 0.1% 手续费
    
    feeElement.textContent = fee.toFixed(8) + ' BTC';
    totalElement.textContent = total.toFixed(2) + ' USDT';
}

// 计算卖出总额
function calculateSellTotal() {
    const priceInput = document.getElementById('sellPrice');
    const amountInput = document.getElementById('sellAmount');
    const feeElement = document.getElementById('sellFee');
    const totalElement = document.getElementById('sellTotal');
    
    if (!priceInput || !amountInput || !feeElement || !totalElement) return;
    
    let price = parseFloat(priceInput.value) || 0;
    const amount = parseFloat(amountInput.value) || 0;
    
    if (currentOrderType === 'market') {
        const currentPriceElement = document.getElementById('currentPrice');
        if (currentPriceElement) {
            price = parseFloat(currentPriceElement.textContent.replace(/,/g, ''));
        }
    }
    
    const total = price * amount;
    const fee = total * 0.001; // 0.1% 手续费
    
    feeElement.textContent = fee.toFixed(2) + ' USDT';
    totalElement.textContent = (total - fee).toFixed(2) + ' USDT';
}

// 提交订单
function submitOrder(type) {
    const priceInput = document.getElementById(type + 'Price');
    const amountInput = document.getElementById(type + 'Amount');
    
    if (!amountInput) return;
    
    const amount = amountInput.value;
    const price = priceInput ? priceInput.value : '';
    
    if (!amount || parseFloat(amount) <= 0) {
        showNotification('请输入有效的数量', 'error');
        return;
    }
    
    if (currentOrderType === 'limit' && (!price || parseFloat(price) <= 0)) {
        showNotification('请输入有效的价格', 'error');
        return;
    }
    
    const orderData = new FormData();
    orderData.append('symbol', currentSymbol);
    orderData.append('mprice', currentOrderType === 'limit' ? price : '0');
    orderData.append('mnum', amount);
    orderData.append('musdt', '0');
    orderData.append(type + 'type', currentOrderType === 'limit' ? '1' : '2');
    
    // 显示加载状态
    const submitBtn = document.querySelector(`.submit-order-btn.${type}`);
    if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.textContent = '提交中...';
    }
    
    // 发送订单请求
    fetch(`/trade/${type === 'buy' ? 'upbbbuy' : 'upbbsell'}`, {
        method: 'POST',
        body: orderData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            showNotification('订单提交成功', 'success');
            clearOrderForm(type);
            loadUserOrders();
            updateUserAssets();
        } else {
            showNotification(data.info || '订单提交失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('网络错误，请重试', 'error');
    })
    .finally(() => {
        // 恢复按钮状态
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.textContent = type === 'buy' ? '买入 BTC' : '卖出 BTC';
        }
    });
}

// 清空订单表单
function clearOrderForm(type) {
    const priceInput = document.getElementById(type + 'Price');
    const amountInput = document.getElementById(type + 'Amount');
    
    if (priceInput) priceInput.value = '';
    if (amountInput) amountInput.value = '';
    
    if (type === 'buy') {
        calculateBuyTotal();
    } else {
        calculateSellTotal();
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">×</button>
    `;
    
    // 添加样式
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 16px;
        border-radius: 4px;
        color: white;
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 8px;
        background: ${type === 'success' ? '#0ECB81' : type === 'error' ? '#F6465D' : '#1890FF'};
    `;
    
    document.body.appendChild(notification);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}

// 加载初始数据
function loadInitialData() {
    // 模拟价格数据
    updatePrice();
    
    // 定时更新价格
    setInterval(updatePrice, 2000);
}

// 更新价格
function updatePrice() {
    const priceElement = document.getElementById('currentPrice');
    const changeElement = document.getElementById('priceChange');
    
    if (priceElement) {
        // 模拟价格波动
        const basePrice = 45280;
        const variation = (Math.random() - 0.5) * 1000;
        const newPrice = basePrice + variation;
        
        priceElement.textContent = newPrice.toFixed(2);
        
        if (changeElement) {
            const change = ((variation / basePrice) * 100).toFixed(2);
            changeElement.textContent = (change >= 0 ? '+' : '') + change + '%';
            changeElement.className = 'pair-change ' + (change >= 0 ? 'positive' : 'negative');
        }
    }
}

// 更新用户资产
function updateUserAssets() {
    fetch('/user/assets/api')
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            const assets = data.data;
            
            // 更新USDT余额
            const usdtAvailable = document.getElementById('usdtAvailable');
            const usdtFrozen = document.getElementById('usdtFrozen');
            if (usdtAvailable && assets.USDT) {
                usdtAvailable.textContent = parseFloat(assets.USDT.available || 0).toFixed(8);
            }
            if (usdtFrozen && assets.USDT) {
                usdtFrozen.textContent = parseFloat(assets.USDT.frozen || 0).toFixed(8);
            }
            
            // 更新BTC余额
            const btcAvailable = document.getElementById('btcAvailable');
            const btcFrozen = document.getElementById('btcFrozen');
            if (btcAvailable && assets.BTC) {
                btcAvailable.textContent = parseFloat(assets.BTC.available || 0).toFixed(8);
            }
            if (btcFrozen && assets.BTC) {
                btcFrozen.textContent = parseFloat(assets.BTC.frozen || 0).toFixed(8);
            }
        }
    })
    .catch(error => {
        console.error('获取资产失败:', error);
    });
}

// 加载用户订单
function loadUserOrders() {
    fetch('/trade/orders')
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            updateOrdersTable(data.data);
        }
    })
    .catch(error => {
        console.error('获取订单失败:', error);
    });
}

// 更新订单表格
function updateOrdersTable(orders) {
    const tbody = document.getElementById('ordersTableBody');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    
    orders.forEach(order => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${order.created_at}</td>
            <td>${order.symbol}</td>
            <td class="text-${order.type == 1 ? 'success' : 'danger'}">${order.type_text}</td>
            <td>${order.price}</td>
            <td>${order.amount}</td>
            <td>${order.filled_amount}</td>
            <td>${order.status_text}</td>
            <td>
                ${order.status == 1 ? `<button class="btn btn-sm btn-outline" onclick="cancelOrder('${order.order_id}')">撤销</button>` : '-'}
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 撤销订单
function cancelOrder(orderId) {
    if (!confirm('确定要撤销这个订单吗？')) return;
    
    const formData = new FormData();
    formData.append('oid', orderId);
    
    fetch('/trade/cancelbborder', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            showNotification('订单撤销成功', 'success');
            loadUserOrders();
            updateUserAssets();
        } else {
            showNotification(data.info || '撤销失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('网络错误，请重试', 'error');
    });
}

// 主题切换
function toggleTheme() {
    const html = document.documentElement;
    const currentTheme = html.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    
    html.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
    
    const themeIcon = document.getElementById('themeIcon');
    if (themeIcon) {
        themeIcon.textContent = newTheme === 'dark' ? '🌙' : '☀️';
    }
}

// 初始化主题
function initTheme() {
    const savedTheme = localStorage.getItem('theme') || 'dark';
    document.documentElement.setAttribute('data-theme', savedTheme);

    const themeIcon = document.getElementById('themeIcon');
    if (themeIcon) {
        themeIcon.textContent = savedTheme === 'dark' ? '🌙' : '☀️';
    }
}

// 初始化K线图
function initKlineChart() {
    const chartContainer = document.getElementById('klineChart');
    if (!chartContainer) return;

    klineChart = echarts.init(chartContainer, 'dark');

    // 生成模拟K线数据
    const klineData = generateMockKlineData();

    const option = {
        backgroundColor: 'transparent',
        grid: {
            left: '10%',
            right: '10%',
            top: '10%',
            bottom: '15%'
        },
        xAxis: {
            type: 'category',
            data: klineData.map(item => item[0]),
            scale: true,
            boundaryGap: false,
            axisLine: { onZero: false },
            splitLine: { show: false },
            splitNumber: 20,
            min: 'dataMin',
            max: 'dataMax',
            axisPointer: {
                z: 100
            }
        },
        yAxis: {
            scale: true,
            splitArea: {
                show: true
            }
        },
        dataZoom: [
            {
                type: 'inside',
                start: 50,
                end: 100
            },
            {
                show: true,
                type: 'slider',
                top: '90%',
                start: 50,
                end: 100
            }
        ],
        series: [
            {
                name: 'K线',
                type: 'candlestick',
                data: klineData.map(item => [item[1], item[4], item[3], item[2]]),
                itemStyle: {
                    color: '#0ECB81',
                    color0: '#F6465D',
                    borderColor: '#0ECB81',
                    borderColor0: '#F6465D'
                }
            }
        ]
    };

    klineChart.setOption(option);

    // 响应式调整
    window.addEventListener('resize', () => {
        if (klineChart) {
            klineChart.resize();
        }
    });
}

// 生成模拟K线数据
function generateMockKlineData() {
    const data = [];
    let basePrice = 45000;
    const now = new Date();

    for (let i = 0; i < 100; i++) {
        const time = new Date(now.getTime() - (100 - i) * 60000);
        const timeStr = time.toLocaleTimeString();

        const open = basePrice + (Math.random() - 0.5) * 100;
        const close = open + (Math.random() - 0.5) * 200;
        const high = Math.max(open, close) + Math.random() * 50;
        const low = Math.min(open, close) - Math.random() * 50;

        data.push([timeStr, open, close, low, high]);
        basePrice = close;
    }

    return data;
}

// 设置K线图时间间隔
function setInterval(interval) {
    // 更新按钮状态
    document.querySelectorAll('.interval-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');

    // 重新加载K线数据
    if (klineChart) {
        const newData = generateMockKlineData();
        klineChart.setOption({
            xAxis: {
                data: newData.map(item => item[0])
            },
            series: [{
                data: newData.map(item => [item[1], item[4], item[3], item[2]])
            }]
        });
    }
}

// 加载订单簿数据
function loadOrderbook() {
    // 模拟订单簿数据
    const sellOrders = [];
    const buyOrders = [];

    let basePrice = 45280;

    // 生成卖盘数据
    for (let i = 0; i < 10; i++) {
        const price = basePrice + (i + 1) * 10;
        const amount = Math.random() * 5;
        sellOrders.push({
            price: price.toFixed(2),
            amount: amount.toFixed(6),
            total: (price * amount).toFixed(2)
        });
    }

    // 生成买盘数据
    for (let i = 0; i < 10; i++) {
        const price = basePrice - (i + 1) * 10;
        const amount = Math.random() * 5;
        buyOrders.push({
            price: price.toFixed(2),
            amount: amount.toFixed(6),
            total: (price * amount).toFixed(2)
        });
    }

    updateOrderbook(sellOrders, buyOrders);
}

// 更新订单簿显示
function updateOrderbook(sellOrders, buyOrders) {
    const sellOrdersBody = document.getElementById('sellOrdersBody');
    const buyOrdersBody = document.getElementById('buyOrdersBody');

    if (sellOrdersBody) {
        sellOrdersBody.innerHTML = '';
        sellOrders.forEach(order => {
            const row = document.createElement('tr');
            row.className = 'orderbook-row';
            row.innerHTML = `
                <td class="orderbook-sell">${order.price}</td>
                <td>${order.amount}</td>
                <td>${order.total}</td>
            `;
            row.onclick = () => fillPrice(order.price);
            sellOrdersBody.appendChild(row);
        });
    }

    if (buyOrdersBody) {
        buyOrdersBody.innerHTML = '';
        buyOrders.forEach(order => {
            const row = document.createElement('tr');
            row.className = 'orderbook-row';
            row.innerHTML = `
                <td class="orderbook-buy">${order.price}</td>
                <td>${order.amount}</td>
                <td>${order.total}</td>
            `;
            row.onclick = () => fillPrice(order.price);
            buyOrdersBody.appendChild(row);
        });
    }
}

// 点击订单簿价格填充到表单
function fillPrice(price) {
    const activeForm = document.querySelector('.trading-form.active');
    if (activeForm) {
        const priceInput = activeForm.querySelector('.price-input');
        if (priceInput) {
            priceInput.value = price;

            // 触发计算
            if (currentTradeType === 'buy') {
                calculateBuyTotal();
            } else {
                calculateSellTotal();
            }
        }
    }
}

// 切换表格视图
function switchTable(type) {
    // 更新标签状态
    document.querySelectorAll('.card-header .trading-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    event.target.classList.add('active');

    // 根据类型加载不同数据
    switch(type) {
        case 'orders':
            loadUserOrders();
            break;
        case 'history':
            loadTradeHistory();
            break;
        case 'trades':
            loadLatestTrades();
            break;
    }
}

// 加载交易历史
function loadTradeHistory() {
    // 实现交易历史加载逻辑
    console.log('加载交易历史');
}

// 加载最新成交
function loadLatestTrades() {
    // 实现最新成交加载逻辑
    console.log('加载最新成交');
}

// 实时数据更新
function updateRealTimeData() {
    updatePrice();

    // 每10秒更新一次订单簿
    if (Math.random() < 0.1) {
        loadOrderbook();
    }
}
