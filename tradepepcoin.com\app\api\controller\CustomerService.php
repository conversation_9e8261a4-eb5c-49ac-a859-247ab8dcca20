<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\common\service\CustomerServiceService;
use think\Request;

/**
 * 客服系统控制器
 */
class CustomerService extends BaseController
{
    protected $customerServiceService;

    public function __construct()
    {
        parent::__construct();
        $this->customerServiceService = new CustomerServiceService();
    }

    /**
     * 创建客服工单
     */
    public function createTicket(Request $request)
    {
        $data = $request->post();
        
        // 验证必填字段
        $validate = [
            'subject' => 'require|max:200',
            'content' => 'require|max:2000',
            'category' => 'in:general,trade,deposit,withdraw,technical',
            'priority' => 'in:1,2,3,4'
        ];

        $result = $this->validate($data, $validate);
        if ($result !== true) {
            return $this->error($result);
        }

        $userId = $this->getUserId();
        $result = $this->customerServiceService->createTicket($userId, $data);

        if ($result['code']) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 发送消息
     */
    public function sendMessage(Request $request)
    {
        $ticketId = $request->post('ticket_id');
        $content = $request->post('content');
        
        if (empty($ticketId) || empty($content)) {
            return $this->error('工单ID和消息内容不能为空');
        }

        $userId = $this->getUserId();
        $result = $this->customerServiceService->sendMessage($ticketId, $userId, $content);

        if ($result['code']) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取工单列表
     */
    public function getTickets(Request $request)
    {
        $userId = $this->getUserId();
        $filters = [
            'status' => $request->get('status', ''),
            'category' => $request->get('category', ''),
            'limit' => (int)$request->get('limit', 20)
        ];

        $result = $this->customerServiceService->getTickets($userId, $filters);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取工单详情
     */
    public function getTicketDetail(Request $request)
    {
        $ticketId = $request->get('ticket_id');
        
        if (empty($ticketId)) {
            return $this->error('工单ID不能为空');
        }

        $userId = $this->getUserId();
        $result = $this->customerServiceService->getTicketDetail($ticketId, $userId);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 关闭工单
     */
    public function closeTicket(Request $request)
    {
        $ticketId = $request->post('ticket_id');
        
        if (empty($ticketId)) {
            return $this->error('工单ID不能为空');
        }

        $userId = $this->getUserId();
        $result = $this->customerServiceService->closeTicket($ticketId, $userId);
        
        if ($result['code']) {
            return $this->success([], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取未读消息数量
     */
    public function getUnreadCount(Request $request)
    {
        $userId = $this->getUserId();
        $unreadCount = $this->customerServiceService->getUnreadCount($userId);
        
        return $this->success(['unread_count' => $unreadCount]);
    }

    /**
     * 获取常见问题分类
     */
    public function getCategories()
    {
        $categories = [
            'general' => '一般咨询',
            'trade' => '交易问题',
            'deposit' => '充值问题',
            'withdraw' => '提币问题',
            'technical' => '技术问题'
        ];
        
        return $this->success($categories);
    }

    /**
     * 获取优先级选项
     */
    public function getPriorities()
    {
        $priorities = [
            1 => '低',
            2 => '普通',
            3 => '高',
            4 => '紧急'
        ];
        
        return $this->success($priorities);
    }
}
