/* 全局样式 */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar {
    background: var(--gradient-primary) !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: bold;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: rgba(255,255,255,0.8) !important;
}

/* 主要内容区域 */
.main-content {
    margin-top: 76px;
    min-height: calc(100vh - 76px);
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4c93 100%);
    transform: translateY(-1px);
}

/* 表单样式 */
.form-control {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.form-floating > label {
    color: #6c757d;
}

/* 价格显示样式 */
.price-up {
    color: var(--success-color) !important;
}

.price-down {
    color: var(--danger-color) !important;
}

.price-neutral {
    color: var(--secondary-color) !important;
}

/* 市场行情滚动条 */
.market-ticker {
    background: #fff;
    border-bottom: 1px solid #dee2e6;
    overflow: hidden;
    white-space: nowrap;
}

.ticker-scroll {
    display: inline-block;
    animation: scroll-left 60s linear infinite;
}

@keyframes scroll-left {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

.ticker-item {
    display: inline-block;
    margin-right: 40px;
    font-size: 14px;
}

/* 交易卡片 */
.trading-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 12px;
    overflow: hidden;
}

.trading-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

/* 统计卡片 */
.stats-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

/* 特色图标 */
.feature-icon {
    width: 70px;
    height: 70px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 28px;
    transition: all 0.3s ease;
}

.feature-icon:hover {
    transform: scale(1.1);
}

/* 客服悬浮按钮 */
.customer-service {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
}

.customer-service .btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    font-size: 24px;
    box-shadow: 0 4px 15px rgba(0,123,255,0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* 聊天消息样式 */
.chat-messages {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.chat-message {
    margin-bottom: 15px;
    padding: 10px 15px;
    border-radius: 18px;
    max-width: 80%;
}

.chat-message.user {
    background: var(--primary-color);
    color: white;
    margin-left: auto;
    text-align: right;
}

.chat-message.service {
    background: white;
    border: 1px solid #dee2e6;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        margin-top: 70px;
    }
    
    .hero-section {
        padding: 80px 0 60px;
    }
    
    .stats-card {
        margin-bottom: 20px;
    }
    
    .customer-service {
        bottom: 20px;
        right: 20px;
    }
    
    .customer-service .btn {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .ticker-item {
        margin-right: 20px;
        font-size: 12px;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .auth-body {
        padding: 30px 20px;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e9ecef;
    }
    
    .card {
        background-color: #2d3748;
        color: #e9ecef;
    }
    
    .form-control {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e9ecef;
    }
    
    .form-control:focus {
        background-color: #2d3748;
        border-color: var(--primary-color);
        color: #e9ecef;
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 工具提示样式 */
.tooltip {
    font-size: 12px;
}

.tooltip-inner {
    background-color: #333;
    border-radius: 6px;
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    border-radius: 6px;
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 4px;
}

/* 表格样式 */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

/* 分页样式 */
.pagination .page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
}

.pagination .page-item.active .page-link {
    background: var(--gradient-primary);
    border-color: transparent;
}

/* 模态框样式 */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    border-radius: 12px 12px 0 0;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 12px 12px;
}
