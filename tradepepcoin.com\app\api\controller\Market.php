<?php
declare (strict_types = 1);

namespace app\api\controller;

use think\facade\Cache;
use think\facade\Db;
use think\facade\Config;

/**
 * 市场数据API控制器 - 基于旧版本逻辑重写
 */
class Market
{
    /**
     * 获取交易对行情数据 - 基于旧版本ticker接口
     */
    public function ticker()
    {
        $market = input('market', '');

        if (empty($market)) {
            return json([
                'code' => 0,
                'msg' => '交易对不能为空',
                'data' => [],
                'timestamp' => time()
            ]);
        }

        // 从缓存获取数据
        $cacheKey = 'ticker_' . $market;
        $data = Cache::get($cacheKey);

        if (!$data) {
            // 计算24小时交易量
            $volume_24h = Db::name('trade_log')
                ->where('market', $market)
                ->where('addtime', '>', time() - 86400)
                ->sum('num');

            // 获取市场配置（模拟旧版本的C('market')）
            $marketConfig = $this->getMarketConfig($market);

            if (!$marketConfig) {
                return json([
                    'code' => 0,
                    'msg' => '交易对不存在',
                    'data' => [],
                    'timestamp' => time()
                ]);
            }

            $data = [
                'date' => time(),
                'ticker' => [
                    'last' => $marketConfig['new_price'],      // 最新成交价
                    'high' => $marketConfig['max_price'],      // 最高价
                    'low' => $marketConfig['min_price'],       // 最低价
                    'buy' => $marketConfig['buy_price'],       // 买一价
                    'sell' => $marketConfig['sell_price'],     // 卖一价
                    'vol' => $volume_24h ?: 0,                // 成交量
                    'change' => $marketConfig['change']        // 涨跌幅
                ]
            ];

            // 缓存5秒
            Cache::set($cacheKey, $data, 5);
        }

        return json([
            'code' => 1,
            'msg' => 'success',
            'data' => $data,
            'timestamp' => time()
        ]);
    }

    /**
     * 获取深度数据 - 基于旧版本depth接口
     */
    public function depth()
    {
        $market = input('market', '');
        $trade_moshi = input('trade_moshi', 1);

        if (empty($market)) {
            return json([
                'code' => 0,
                'msg' => '交易对不能为空',
                'data' => [],
                'timestamp' => time()
            ]);
        }

        $cacheKey = 'depth_' . $market . '_' . $trade_moshi;
        $data = Cache::get($cacheKey);

        if (!$data) {
            // 根据模式设置限制数量
            $limit = ($trade_moshi == 1) ? 12 : 25;

            $data = ['asks' => [], 'bids' => []];

            if (in_array($trade_moshi, [1, 3])) {
                // 获取买单数据
                $buyOrders = Db::name('trade')
                    ->where([
                        'status' => 0,
                        'type' => 1,
                        'market' => $market
                    ])
                    ->field('price, sum(num-deal) as nums')
                    ->group('price')
                    ->order('price desc')
                    ->limit($limit)
                    ->select();

                foreach ($buyOrders as $order) {
                    $data['bids'][] = [
                        round(floatval($order['nums']), 3),
                        round(floatval($order['price']), 3)
                    ];
                }
            }

            if (in_array($trade_moshi, [1, 4])) {
                // 获取卖单数据
                $sellOrders = Db::name('trade')
                    ->where([
                        'status' => 0,
                        'type' => 2,
                        'market' => $market
                    ])
                    ->field('price, sum(num-deal) as nums')
                    ->group('price')
                    ->order('price asc')
                    ->limit($limit)
                    ->select();

                $sellData = [];
                foreach ($sellOrders as $order) {
                    $sellData[] = [
                        round(floatval($order['nums']), 3),
                        round(floatval($order['price']), 3)
                    ];
                }
                $data['asks'] = array_reverse($sellData);
            }

            // 缓存3秒
            Cache::set($cacheKey, $data, 3);
        }

        return json([
            'code' => 1,
            'msg' => 'success',
            'data' => $data,
            'timestamp' => time()
        ]);
    }

    /**
     * 获取交易记录 - 基于旧版本trades接口
     */
    public function trades()
    {
        $market = input('market', '');
        $since = input('since', 0);

        if (empty($market)) {
            return json([
                'code' => 0,
                'msg' => '交易对不能为空',
                'data' => [],
                'timestamp' => time()
            ]);
        }

        $where = ['market' => $market];
        if ($since > 0) {
            $where['id'] = ['>', $since];
        }

        $tradeLog = Db::name('trade_log')
            ->where($where)
            ->order('id desc')
            ->limit($since > 0 ? 100 : 1)
            ->select();

        $data = [];
        foreach ($tradeLog as $trade) {
            $data[] = [
                'date' => $trade['addtime'],
                'date_ms' => $trade['addtime'] * 1000,
                'price' => $trade['price'],
                'amount' => $trade['num'],
                'tid' => $trade['id'],
                'trade_type' => $trade['type'] == 1 ? 'buy' : 'sell'
            ];
        }

        return json([
            'code' => 1,
            'msg' => 'success',
            'data' => $data,
            'timestamp' => time()
        ]);
    }

    /**
     * 获取交易对列表 - symbols接口
     */
    public function symbols()
    {
        $cacheKey = 'market_symbols';
        $data = Cache::get($cacheKey);

        if (!$data) {
            // 模拟获取交易对数据
            $markets = $this->getAllMarkets();
            $data = [];

            foreach ($markets as $market => $config) {
                $data[] = [
                    'symbol' => $market,
                    'base_coin' => explode('_', $market)[0],
                    'quote_coin' => explode('_', $market)[1],
                    'price_precision' => 8,
                    'amount_precision' => 8,
                    'status' => 'TRADING'
                ];
            }

            // 缓存30秒
            Cache::set($cacheKey, $data, 30);
        }

        return json([
            'code' => 1,
            'msg' => 'success',
            'data' => $data,
            'timestamp' => time()
        ]);
    }

    /**
     * 获取24小时价格统计
     */
    public function ticker24hr()
    {
        $symbol = input('symbol', '');
        
        if ($symbol) {
            // 获取单个交易对统计
            $tradingPair = TradingPair::getBySymbol($symbol);
            if (!$tradingPair) {
                return $this->error('交易对不存在');
            }

            $data = [
                'symbol' => $tradingPair->symbol,
                'price' => $tradingPair->current_price,
                'price_change' => $tradingPair->change_24h,
                'price_change_percent' => $tradingPair->change_24h,
                'high_price' => $tradingPair->high_24h,
                'low_price' => $tradingPair->low_24h,
                'volume' => $tradingPair->volume_24h,
                'open_time' => time() - 86400,
                'close_time' => time()
            ];
        } else {
            // 获取所有交易对统计
            $tradingPairs = TradingPair::getEnabled();
            $data = [];
            
            foreach ($tradingPairs as $pair) {
                $data[] = [
                    'symbol' => $pair->symbol,
                    'price' => $pair->current_price,
                    'price_change' => $pair->change_24h,
                    'price_change_percent' => $pair->change_24h,
                    'high_price' => $pair->high_24h,
                    'low_price' => $pair->low_24h,
                    'volume' => $pair->volume_24h,
                    'open_time' => time() - 86400,
                    'close_time' => time()
                ];
            }
        }

        return json([
            'code' => 1,
            'msg' => 'success',
            'data' => $data,
            'timestamp' => time()
        ]);
    }

    /**
     * 获取市场配置 - 模拟旧版本的C('market')
     */
    private function getMarketConfig($market)
    {
        // 这里应该从数据库或配置文件获取市场数据
        // 暂时返回模拟数据
        $markets = $this->getAllMarkets();
        return $markets[$market] ?? null;
    }

    /**
     * 获取所有市场配置
     */
    private function getAllMarkets()
    {
        // 模拟市场数据，实际应该从数据库获取
        return [
            'btc_usdt' => [
                'new_price' => '45280.50',
                'max_price' => '46000.00',
                'min_price' => '44500.00',
                'buy_price' => '45275.00',
                'sell_price' => '45285.00',
                'change' => '+2.45',
                'volume' => '1234.56'
            ],
            'eth_usdt' => [
                'new_price' => '3200.80',
                'max_price' => '3250.00',
                'min_price' => '3150.00',
                'buy_price' => '3198.50',
                'sell_price' => '3202.30',
                'change' => '+1.85',
                'volume' => '5678.90'
            ]
        ];
    }
}

    /**
     * 获取K线数据
     */
    public function klines()
    {
        $symbol = input('symbol', '', 'trim');
        $interval = input('interval', '1m', 'trim');
        $limit = input('limit/d', 500);
        $startTime = input('startTime/d', 0);
        $endTime = input('endTime/d', 0);

        if (empty($symbol)) {
            return $this->error('交易对不能为空');
        }

        $tradingPair = TradingPair::getBySymbol($symbol);
        if (!$tradingPair) {
            return $this->error('交易对不存在');
        }

        // 从缓存获取K线数据
        $cacheKey = "klines_{$symbol}_{$interval}_{$limit}";
        $klines = Cache::get($cacheKey);

        if (!$klines) {
            // 生成模拟K线数据
            $klines = $this->generateKlineData($tradingPair, $interval, $limit);
            Cache::set($cacheKey, $klines, 60); // 缓存1分钟
        }

        return $this->success($klines);
    }

    /**
     * 获取交易对价格
     */
    public function price()
    {
        $symbol = input('symbol', '');
        
        if ($symbol) {
            $tradingPair = TradingPair::getBySymbol($symbol);
            if (!$tradingPair) {
                return $this->error('交易对不存在');
            }

            $data = [
                'symbol' => $tradingPair->symbol,
                'price' => $tradingPair->current_price
            ];
        } else {
            $tradingPairs = TradingPair::getEnabled();
            $data = [];
            
            foreach ($tradingPairs as $pair) {
                $data[] = [
                    'symbol' => $pair->symbol,
                    'price' => $pair->current_price
                ];
            }
        }

        return $this->success($data);
    }

    /**
     * 获取服务器时间
     */
    public function time()
    {
        return $this->success([
            'server_time' => time() * 1000
        ]);
    }

    /**
     * 获取交易规则
     */
    public function exchangeInfo()
    {
        $symbols = TradingPair::getEnabled();
        
        $data = [
            'timezone' => 'UTC',
            'server_time' => time() * 1000,
            'symbols' => []
        ];

        foreach ($symbols as $symbol) {
            $data['symbols'][] = [
                'symbol' => $symbol->symbol,
                'status' => $symbol->status == 1 ? 'TRADING' : 'BREAK',
                'base_asset' => $symbol->base_coin,
                'quote_asset' => $symbol->quote_coin,
                'base_asset_precision' => $symbol->amount_precision,
                'quote_precision' => $symbol->price_precision,
                'filters' => [
                    [
                        'filter_type' => 'PRICE_FILTER',
                        'min_price' => '0.00000001',
                        'max_price' => '1000000.00000000',
                        'tick_size' => '0.00000001'
                    ],
                    [
                        'filter_type' => 'LOT_SIZE',
                        'min_qty' => $symbol->min_amount,
                        'max_qty' => $symbol->max_amount ?: '9000000.00000000',
                        'step_size' => '0.00000001'
                    ],
                    [
                        'filter_type' => 'MIN_NOTIONAL',
                        'min_notional' => $symbol->min_total
                    ]
                ]
            ];
        }

        return $this->success($data);
    }

    /**
     * 生成模拟订单簿数据
     */
    private function generateDepthData($tradingPair, $limit)
    {
        $basePrice = $tradingPair->current_price;
        $bids = [];
        $asks = [];

        // 生成买单
        for ($i = 1; $i <= $limit; $i++) {
            $price = $basePrice * (1 - $i * 0.0001);
            $amount = mt_rand(1, 1000) / 100;
            $bids[] = [
                number_format($price, 8, '.', ''),
                number_format($amount, 8, '.', '')
            ];
        }

        // 生成卖单
        for ($i = 1; $i <= $limit; $i++) {
            $price = $basePrice * (1 + $i * 0.0001);
            $amount = mt_rand(1, 1000) / 100;
            $asks[] = [
                number_format($price, 8, '.', ''),
                number_format($amount, 8, '.', '')
            ];
        }

        return [
            'last_update_id' => time(),
            'bids' => $bids,
            'asks' => $asks
        ];
    }

    /**
     * 生成模拟K线数据
     */
    private function generateKlineData($tradingPair, $interval, $limit)
    {
        $intervalSeconds = $this->getIntervalSeconds($interval);
        $basePrice = $tradingPair->current_price;
        $klines = [];
        
        $startTime = time() - ($limit * $intervalSeconds);

        for ($i = 0; $i < $limit; $i++) {
            $openTime = $startTime + ($i * $intervalSeconds);
            $closeTime = $openTime + $intervalSeconds - 1;
            
            $open = $basePrice + mt_rand(-1000, 1000) / 100;
            $close = $open + mt_rand(-500, 500) / 100;
            $high = max($open, $close) + mt_rand(0, 200) / 100;
            $low = min($open, $close) - mt_rand(0, 200) / 100;
            $volume = mt_rand(100, 10000) / 100;
            $quoteVolume = $volume * ($open + $close) / 2;

            $klines[] = [
                $openTime * 1000,
                number_format($open, 8, '.', ''),
                number_format($high, 8, '.', ''),
                number_format($low, 8, '.', ''),
                number_format($close, 8, '.', ''),
                number_format($volume, 8, '.', ''),
                $closeTime * 1000,
                number_format($quoteVolume, 8, '.', ''),
                mt_rand(10, 100),
                number_format($volume * 0.6, 8, '.', ''),
                number_format($quoteVolume * 0.6, 8, '.', ''),
                '0'
            ];

            $basePrice = $close;
        }

        return $klines;
    }

    /**
     * 获取时间间隔秒数
     */
    private function getIntervalSeconds($interval)
    {
        $intervals = [
            '1m' => 60,
            '3m' => 180,
            '5m' => 300,
            '15m' => 900,
            '30m' => 1800,
            '1h' => 3600,
            '2h' => 7200,
            '4h' => 14400,
            '6h' => 21600,
            '8h' => 28800,
            '12h' => 43200,
            '1d' => 86400,
            '3d' => 259200,
            '1w' => 604800,
            '1M' => 2592000
        ];

        return $intervals[$interval] ?? 3600;
    }
}
