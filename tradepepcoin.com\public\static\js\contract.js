/* 合约交易页面JavaScript */

// 全局变量
let currentSymbol = 'BTC/USDT';
let currentDuration = 1;
let currentAmount = 0;
let contractChart = null;
let contractUpdateInterval = null;

// 初始化合约页面
function initContractPage() {
    setupContractEventListeners();
    loadContractData();
    updateContractPrice();
}

// 设置事件监听器
function setupContractEventListeners() {
    const amountInput = document.getElementById('contractAmount');
    if (amountInput) {
        amountInput.addEventListener('input', calculateExpectedProfit);
    }
}

// 切换交易对
function changeSymbol() {
    const symbolSelect = document.getElementById('contractSymbol');
    if (symbolSelect) {
        currentSymbol = symbolSelect.value;
        updateContractPrice();
        if (contractChart) {
            updateContractChart();
        }
    }
}

// 设置投注金额
function setAmount(amount) {
    currentAmount = amount;
    
    // 更新输入框
    const amountInput = document.getElementById('contractAmount');
    if (amountInput) {
        amountInput.value = amount;
    }
    
    // 更新按钮状态
    document.querySelectorAll('.amount-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // 计算预期收益
    calculateExpectedProfit();
}

// 设置合约时长
function setDuration(duration) {
    currentDuration = duration;
    
    // 更新按钮状态
    document.querySelectorAll('.duration-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
}

// 计算预期收益
function calculateExpectedProfit() {
    const amountInput = document.getElementById('contractAmount');
    const profitRateElement = document.getElementById('profitRate');
    const expectedProfitElement = document.getElementById('expectedProfit');
    
    if (!amountInput || !profitRateElement || !expectedProfitElement) return;
    
    const amount = parseFloat(amountInput.value) || 0;
    const profitRate = parseFloat(profitRateElement.textContent) || 80;
    
    const expectedProfit = amount * (profitRate / 100);
    expectedProfitElement.textContent = expectedProfit.toFixed(2) + ' USDT';
}

// 创建合约订单
function createContract(direction) {
    const amountInput = document.getElementById('contractAmount');
    if (!amountInput) return;
    
    const amount = parseFloat(amountInput.value);
    
    // 验证投注金额
    if (!amount || amount < 10) {
        showNotification('投注金额不能少于10 USDT', 'error');
        return;
    }
    
    // 检查余额
    const balanceElement = document.getElementById('usdtBalance');
    if (balanceElement) {
        const balance = parseFloat(balanceElement.textContent);
        if (balance < amount) {
            showNotification('USDT余额不足', 'error');
            return;
        }
    }
    
    // 获取当前价格
    const priceElement = document.getElementById('contractPrice');
    const currentPrice = priceElement ? parseFloat(priceElement.textContent.replace(/,/g, '')) : 0;
    
    // 构造订单数据
    const orderData = new FormData();
    orderData.append('ctzed', amount.toString());
    orderData.append('ctzfx', direction === 'buy' ? '1' : '2');
    orderData.append('ctime', currentDuration.toString());
    orderData.append('ccoinname', currentSymbol);
    orderData.append('cykbl', '80'); // 盈利比例
    
    // 显示加载状态
    const buyBtn = document.querySelector('.buy-btn');
    const sellBtn = document.querySelector('.sell-btn');
    const targetBtn = direction === 'buy' ? buyBtn : sellBtn;
    
    if (targetBtn) {
        targetBtn.disabled = true;
        targetBtn.style.opacity = '0.6';
    }
    
    // 发送请求
    fetch('/contract/creatorder', {
        method: 'POST',
        body: orderData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            showNotification('合约创建成功！', 'success');
            
            // 清空输入框
            if (amountInput) amountInput.value = '';
            calculateExpectedProfit();
            
            // 刷新合约列表和余额
            loadCurrentContracts();
            updateUserBalance();
            
            // 创建合约项显示
            addContractToList(data.data, direction, amount, currentPrice);
            
        } else {
            showNotification(data.msg || '合约创建失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('网络错误，请重试', 'error');
    })
    .finally(() => {
        // 恢复按钮状态
        if (targetBtn) {
            targetBtn.disabled = false;
            targetBtn.style.opacity = '1';
        }
    });
}

// 添加合约到列表
function addContractToList(contractData, direction, amount, buyPrice) {
    const currentContractsDiv = document.getElementById('currentContracts');
    const emptyDiv = document.getElementById('emptyContracts');
    
    if (!currentContractsDiv) return;
    
    // 隐藏空状态
    if (emptyDiv) {
        emptyDiv.style.display = 'none';
    }
    
    // 创建合约项
    const contractItem = document.createElement('div');
    contractItem.className = 'contract-item';
    contractItem.id = `contract-${contractData.order_id || Date.now()}`;
    
    const endTime = new Date(Date.now() + currentDuration * 60000);
    
    contractItem.innerHTML = `
        <div class="contract-header">
            <span class="contract-symbol">${currentSymbol}</span>
            <span class="contract-direction ${direction}">${direction === 'buy' ? '买涨' : '买跌'}</span>
        </div>
        <div class="contract-details">
            <div class="contract-detail">
                <span class="detail-label">投注</span>
                <span class="detail-value">${amount} USDT</span>
            </div>
            <div class="contract-detail">
                <span class="detail-label">买入价</span>
                <span class="detail-value">${buyPrice.toFixed(2)}</span>
            </div>
        </div>
        <div class="contract-progress">
            <div class="progress-bar">
                <div class="progress-fill" style="width: 0%"></div>
            </div>
            <div class="progress-text">
                <span>剩余时间</span>
                <span class="remaining-time">${currentDuration}:00</span>
            </div>
        </div>
    `;
    
    currentContractsDiv.appendChild(contractItem);
    
    // 启动倒计时
    startContractCountdown(contractItem, currentDuration * 60);
}

// 启动合约倒计时
function startContractCountdown(contractElement, totalSeconds) {
    let remainingSeconds = totalSeconds;
    
    const progressFill = contractElement.querySelector('.progress-fill');
    const remainingTimeSpan = contractElement.querySelector('.remaining-time');
    
    const countdown = setInterval(() => {
        remainingSeconds--;
        
        // 更新进度条
        const progress = ((totalSeconds - remainingSeconds) / totalSeconds) * 100;
        if (progressFill) {
            progressFill.style.width = progress + '%';
        }
        
        // 更新剩余时间
        if (remainingTimeSpan) {
            const minutes = Math.floor(remainingSeconds / 60);
            const seconds = remainingSeconds % 60;
            remainingTimeSpan.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
        
        // 倒计时结束
        if (remainingSeconds <= 0) {
            clearInterval(countdown);
            
            // 显示结算中状态
            if (remainingTimeSpan) {
                remainingTimeSpan.textContent = '结算中...';
            }
            
            // 3秒后移除合约项
            setTimeout(() => {
                contractElement.remove();
                
                // 检查是否需要显示空状态
                const currentContracts = document.getElementById('currentContracts');
                const emptyDiv = document.getElementById('emptyContracts');
                if (currentContracts && currentContracts.children.length === 1 && emptyDiv) {
                    emptyDiv.style.display = 'flex';
                }
                
                // 刷新历史记录和统计
                loadHistoryContracts();
                updateContractStats();
                updateUserBalance();
                
            }, 3000);
        }
    }, 1000);
}

// 切换合约标签
function switchContractTab(type) {
    // 更新标签状态
    document.querySelectorAll('.contract-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // 切换列表显示
    document.querySelectorAll('.contract-list').forEach(list => {
        list.classList.remove('active');
    });
    
    const targetList = document.getElementById(type === 'current' ? 'currentContracts' : 'historyContracts');
    if (targetList) {
        targetList.classList.add('active');
    }
    
    // 加载对应数据
    if (type === 'history') {
        loadHistoryContracts();
    }
}

// 加载当前合约
function loadCurrentContracts() {
    fetch('/contract/gethyorder?status=1&limit=10')
    .then(response => response.json())
    .then(data => {
        if (data.code === 1 && data.data.length > 0) {
            // 处理当前进行中的合约
            const currentContractsDiv = document.getElementById('currentContracts');
            const emptyDiv = document.getElementById('emptyContracts');
            
            if (currentContractsDiv && emptyDiv) {
                emptyDiv.style.display = 'none';
                
                // 清空现有合约（避免重复）
                const existingContracts = currentContractsDiv.querySelectorAll('.contract-item');
                existingContracts.forEach(item => item.remove());
                
                // 添加进行中的合约
                data.data.forEach(contract => {
                    if (contract.t > 0) { // 还有剩余时间
                        addExistingContractToList(contract);
                    }
                });
            }
        }
    })
    .catch(error => {
        console.error('加载当前合约失败:', error);
    });
}

// 添加已存在的合约到列表
function addExistingContractToList(contract) {
    const currentContractsDiv = document.getElementById('currentContracts');
    if (!currentContractsDiv) return;
    
    const contractItem = document.createElement('div');
    contractItem.className = 'contract-item';
    contractItem.id = `contract-${contract.id}`;
    
    contractItem.innerHTML = `
        <div class="contract-header">
            <span class="contract-symbol">${contract.coinanme}</span>
            <span class="contract-direction ${contract.hyzdstr === '买涨' ? 'buy' : 'sell'}">${contract.hyzdstr}</span>
        </div>
        <div class="contract-details">
            <div class="contract-detail">
                <span class="detail-label">投注</span>
                <span class="detail-value">${contract.num} USDT</span>
            </div>
            <div class="contract-detail">
                <span class="detail-label">买入价</span>
                <span class="detail-value">${contract.buyprice}</span>
            </div>
        </div>
        <div class="contract-progress">
            <div class="progress-bar">
                <div class="progress-fill" style="width: ${contract.bl}"></div>
            </div>
            <div class="progress-text">
                <span>剩余时间</span>
                <span class="remaining-time">${Math.floor(contract.t / 60)}:${(contract.t % 60).toString().padStart(2, '0')}</span>
            </div>
        </div>
    `;
    
    currentContractsDiv.appendChild(contractItem);
    
    // 启动倒计时
    if (contract.t > 0) {
        startContractCountdown(contractItem, contract.t);
    }
}

// 加载历史合约
function loadHistoryContracts() {
    fetch('/contract/gethyorder?status=2&limit=20')
    .then(response => response.json())
    .then(data => {
        const historyContractsDiv = document.getElementById('historyContracts');
        if (!historyContractsDiv) return;
        
        historyContractsDiv.innerHTML = '';
        
        if (data.code === 1 && data.data.length > 0) {
            data.data.forEach(contract => {
                const contractItem = document.createElement('div');
                contractItem.className = 'contract-item';
                
                const isWin = contract.statusstr.includes('盈利');
                const resultClass = isWin ? 'text-success' : 'text-danger';
                
                contractItem.innerHTML = `
                    <div class="contract-header">
                        <span class="contract-symbol">${contract.coinanme}</span>
                        <span class="contract-direction ${contract.hyzdstr === '买涨' ? 'buy' : 'sell'}">${contract.hyzdstr}</span>
                    </div>
                    <div class="contract-details">
                        <div class="contract-detail">
                            <span class="detail-label">投注</span>
                            <span class="detail-value">${contract.num} USDT</span>
                        </div>
                        <div class="contract-detail">
                            <span class="detail-label">结果</span>
                            <span class="detail-value ${resultClass}">${contract.statusstr}</span>
                        </div>
                    </div>
                `;
                
                historyContractsDiv.appendChild(contractItem);
            });
        } else {
            historyContractsDiv.innerHTML = `
                <div class="contract-empty">
                    <div class="empty-icon">📋</div>
                    <div class="empty-text">暂无历史记录</div>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('加载历史合约失败:', error);
    });
}

// 更新合约价格
function updateContractPrice() {
    // 模拟价格更新
    const priceElement = document.getElementById('contractPrice');
    const changeElement = document.getElementById('contractChange');
    
    if (priceElement) {
        const basePrice = currentSymbol === 'BTC/USDT' ? 45280 : 
                         currentSymbol === 'ETH/USDT' ? 2850 : 320;
        
        const variation = (Math.random() - 0.5) * (basePrice * 0.02);
        const newPrice = basePrice + variation;
        
        priceElement.textContent = newPrice.toFixed(2);
        
        if (changeElement) {
            const change = ((variation / basePrice) * 100).toFixed(2);
            changeElement.textContent = (change >= 0 ? '+' : '') + change + '%';
            changeElement.className = 'change-value ' + (change >= 0 ? 'positive' : 'negative');
        }
    }
}

// 更新用户余额
function updateUserBalance() {
    fetch('/user/assets/api')
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            const balanceElement = document.getElementById('usdtBalance');
            if (balanceElement && data.data.USDT) {
                balanceElement.textContent = parseFloat(data.data.USDT.available || 0).toFixed(2);
            }
        }
    })
    .catch(error => {
        console.error('更新余额失败:', error);
    });
}

// 更新合约统计
function updateContractStats() {
    // 这里可以添加统计数据更新逻辑
    console.log('更新合约统计');
}

// 加载合约数据
function loadContractData() {
    loadCurrentContracts();
    updateUserBalance();
}

// 更新合约数据（定时调用）
function updateContractData() {
    updateContractPrice();
    
    // 每30秒更新一次合约列表
    if (Math.random() < 0.033) { // 约30秒一次
        loadCurrentContracts();
    }
}

// 初始化合约图表
function initContractChart() {
    const chartContainer = document.getElementById('contractChart');
    if (!chartContainer) return;
    
    contractChart = echarts.init(chartContainer, 'dark');
    updateContractChart();
    
    // 响应式调整
    window.addEventListener('resize', () => {
        if (contractChart) {
            contractChart.resize();
        }
    });
}

// 更新合约图表
function updateContractChart() {
    if (!contractChart) return;
    
    // 生成模拟数据
    const data = [];
    const now = new Date();
    let basePrice = currentSymbol === 'BTC/USDT' ? 45280 : 
                   currentSymbol === 'ETH/USDT' ? 2850 : 320;
    
    for (let i = 0; i < 60; i++) {
        const time = new Date(now.getTime() - (60 - i) * 1000);
        basePrice += (Math.random() - 0.5) * (basePrice * 0.001);
        data.push([time, basePrice]);
    }
    
    const option = {
        backgroundColor: 'transparent',
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'time',
            splitLine: { show: false }
        },
        yAxis: {
            type: 'value',
            scale: true,
            splitLine: { show: true }
        },
        series: [{
            data: data,
            type: 'line',
            smooth: true,
            symbol: 'none',
            lineStyle: {
                color: '#F0B90B',
                width: 2
            },
            areaStyle: {
                color: {
                    type: 'linear',
                    x: 0, y: 0, x2: 0, y2: 1,
                    colorStops: [
                        { offset: 0, color: 'rgba(240, 185, 11, 0.3)' },
                        { offset: 1, color: 'rgba(240, 185, 11, 0.05)' }
                    ]
                }
            }
        }]
    };
    
    contractChart.setOption(option);
}

// 设置图表时间间隔
function setChartInterval(interval) {
    // 更新按钮状态
    document.querySelectorAll('.chart-controls .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // 重新加载图表数据
    updateContractChart();
}

// 显示通知（复用之前的函数）
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">×</button>
    `;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 16px;
        border-radius: 4px;
        color: white;
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 8px;
        background: ${type === 'success' ? '#0ECB81' : type === 'error' ? '#F6465D' : '#1890FF'};
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}

// 主题切换（复用之前的函数）
function toggleTheme() {
    const html = document.documentElement;
    const currentTheme = html.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    
    html.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
    
    const themeIcon = document.getElementById('themeIcon');
    if (themeIcon) {
        themeIcon.textContent = newTheme === 'dark' ? '🌙' : '☀️';
    }
    
    // 重新初始化图表以适应主题
    if (contractChart) {
        contractChart.dispose();
        initContractChart();
    }
}

// 初始化主题（复用之前的函数）
function initTheme() {
    const savedTheme = localStorage.getItem('theme') || 'dark';
    document.documentElement.setAttribute('data-theme', savedTheme);
    
    const themeIcon = document.getElementById('themeIcon');
    if (themeIcon) {
        themeIcon.textContent = savedTheme === 'dark' ? '🌙' : '☀️';
    }
}
