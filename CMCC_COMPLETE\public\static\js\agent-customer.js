/**
 * GVD代理端客服系统
 */

class AgentCustomerService {
    constructor() {
        this.currentSessionId = null;
        this.currentCustomerId = null;
        this.websocket = null;
        this.sessions = [];
        this.customers = [];
        this.quickReplies = [];
        this.token = localStorage.getItem('agent_token');
        this.agentId = this.getAgentId();
        this.onlineStatus = 'online';
        
        this.init();
    }

    init() {
        this.connectWebSocket();
        this.loadWorkStatistics();
        this.loadSessions();
        this.loadCustomers();
        this.loadQuickReplies();
        this.bindEvents();
        this.updateStatusIndicator();
        
        // 定时刷新数据
        setInterval(() => {
            this.loadWorkStatistics();
            this.loadSessions();
        }, 30000);
    }

    /**
     * 连接WebSocket
     */
    async connectWebSocket() {
        try {
            const wsUrl = `ws://${window.location.hostname}:2346`;
            this.websocket = new WebSocket(wsUrl);

            this.websocket.onopen = () => {
                console.log('代理端WebSocket连接成功');
                
                // 发送认证消息
                this.sendWebSocketMessage({
                    type: 'auth',
                    data: {
                        token: this.token,
                        user_type: 'agent'
                    }
                });
            };

            this.websocket.onmessage = (event) => {
                this.handleWebSocketMessage(JSON.parse(event.data));
            };

            this.websocket.onclose = () => {
                console.log('代理端WebSocket连接关闭');
                
                // 尝试重连
                setTimeout(() => {
                    this.connectWebSocket();
                }, 3000);
            };

            this.websocket.onerror = (error) => {
                console.error('代理端WebSocket错误:', error);
            };

        } catch (error) {
            console.error('WebSocket连接失败:', error);
        }
    }

    /**
     * 发送WebSocket消息
     */
    sendWebSocketMessage(message) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify(message));
        }
    }

    /**
     * 处理WebSocket消息
     */
    handleWebSocketMessage(message) {
        switch (message.type) {
            case 'auth_success':
                console.log('代理端认证成功');
                this.updateOnlineStatus();
                break;
                
            case 'new_message':
                this.handleNewMessage(message.data);
                break;
                
            case 'session_update':
                this.handleSessionUpdate(message.data);
                break;
                
            case 'user_typing':
                this.handleTypingIndicator(message.data);
                break;
        }
    }

    /**
     * 处理新消息
     */
    handleNewMessage(message) {
        // 更新会话列表中的最后消息
        this.updateSessionLastMessage(message.session_id, message);
        
        // 如果是当前会话，显示消息
        if (message.session_id === this.currentSessionId) {
            this.displayMessage(message);
        }
        
        // 播放通知声音
        this.playNotificationSound();
        
        // 更新未读消息计数
        this.updateUnreadCount();
    }

    /**
     * 加载工作统计
     */
    async loadWorkStatistics() {
        try {
            const response = await fetch('/agent/customer/statistics', {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            const result = await response.json();
            
            if (result.code === 1) {
                this.updateWorkStatistics(result.data);
            }
        } catch (error) {
            console.error('加载工作统计失败:', error);
        }
    }

    /**
     * 更新工作统计显示
     */
    updateWorkStatistics(data) {
        document.getElementById('todaySessions').textContent = data.today_stats?.today_sessions || 0;
        document.getElementById('totalCustomers').textContent = data.customer_stats?.total_customers || 0;
        document.getElementById('unreadMessages').textContent = data.today_stats?.unread_messages || 0;
        document.getElementById('avgResponseTime').textContent = Math.round(data.session_stats?.avg_session_duration || 0) + 's';
    }

    /**
     * 加载会话列表
     */
    async loadSessions() {
        try {
            const response = await fetch('/agent/customer/sessions', {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            const result = await response.json();
            
            if (result.code === 1) {
                this.sessions = result.data;
                this.renderSessionsList();
            }
        } catch (error) {
            console.error('加载会话列表失败:', error);
        }
    }

    /**
     * 渲染会话列表
     */
    renderSessionsList() {
        const container = document.getElementById('sessionsList');
        
        if (this.sessions.length === 0) {
            container.innerHTML = `
                <div class="empty-sessions">
                    <div class="empty-icon">💬</div>
                    <div class="empty-text">暂无客服会话</div>
                    <button class="btn btn-sm btn-primary" onclick="agentCustomer.showCustomerModal()">
                        联系客户
                    </button>
                </div>
            `;
            return;
        }

        const html = this.sessions.map(session => {
            const lastMessage = session.last_message;
            const unreadCount = session.unread_count_agent || 0;
            const statusClass = session.status === 'active' ? 'active' : session.status === 'waiting' ? 'waiting' : 'closed';
            
            return `
                <div class="session-item ${this.currentSessionId === session.session_id ? 'selected' : ''}" 
                     onclick="agentCustomer.selectSession('${session.session_id}', ${session.user_id})">
                    <div class="session-avatar">
                        ${session.username?.charAt(0)?.toUpperCase() || 'U'}
                    </div>
                    <div class="session-content">
                        <div class="session-header">
                            <div class="session-name">${session.username || '未知用户'}</div>
                            <div class="session-time">${this.formatTime(session.last_message_time || session.created_at)}</div>
                        </div>
                        <div class="session-preview">
                            <span class="session-status ${statusClass}">${this.getStatusText(session.status)}</span>
                            <span class="session-message">${this.getMessagePreview(lastMessage)}</span>
                        </div>
                    </div>
                    ${unreadCount > 0 ? `<div class="session-badge">${unreadCount}</div>` : ''}
                </div>
            `;
        }).join('');

        container.innerHTML = html;
    }

    /**
     * 选择会话
     */
    async selectSession(sessionId, customerId) {
        this.currentSessionId = sessionId;
        this.currentCustomerId = customerId;
        
        // 更新选中状态
        document.querySelectorAll('.session-item').forEach(item => {
            item.classList.remove('selected');
        });
        event.currentTarget.classList.add('selected');
        
        // 加入WebSocket会话
        this.sendWebSocketMessage({
            type: 'join_session',
            data: {
                session_id: sessionId
            }
        });
        
        // 加载消息历史
        await this.loadMessages(sessionId);
        
        // 加载客户信息
        await this.loadCustomerInfo(customerId);
        
        // 显示聊天界面
        this.showChatInterface();
        
        // 标记消息为已读
        this.markMessagesAsRead(sessionId);
    }

    /**
     * 加载消息历史
     */
    async loadMessages(sessionId) {
        try {
            const response = await fetch(`/agent/customer/messages?session_id=${sessionId}`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            const result = await response.json();
            
            if (result.code === 1) {
                this.renderMessages(result.data.messages);
            }
        } catch (error) {
            console.error('加载消息失败:', error);
        }
    }

    /**
     * 渲染消息列表
     */
    renderMessages(messages) {
        const container = document.getElementById('chatMessages');
        
        if (messages.length === 0) {
            container.innerHTML = `
                <div class="empty-chat">
                    <div class="empty-icon">💬</div>
                    <div class="empty-text">
                        <h3>开始对话</h3>
                        <p>向您的客户发送第一条消息</p>
                    </div>
                </div>
            `;
            return;
        }

        const html = messages.map(message => this.renderMessage(message)).join('');
        container.innerHTML = html;
        
        // 滚动到底部
        container.scrollTop = container.scrollHeight;
    }

    /**
     * 渲染单条消息
     */
    renderMessage(message) {
        const isAgent = message.sender_type === 'agent';
        const isAdmin = message.sender_type === 'admin';
        const time = new Date(message.created_at).toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });

        let messageContent = '';
        switch (message.message_type) {
            case 'image':
                messageContent = `<div class="message-image"><img src="${message.file_url}" alt="图片"></div>`;
                break;
            case 'emoji':
                messageContent = `<div class="message-emoji">${message.content}</div>`;
                break;
            default:
                messageContent = `<div class="message-text">${this.formatMessageText(message.content)}</div>`;
        }

        return `
            <div class="message ${isAgent ? 'agent' : isAdmin ? 'admin' : 'user'}">
                <div class="message-avatar">
                    ${message.sender_info?.username?.charAt(0)?.toUpperCase() || (isAgent ? 'A' : isAdmin ? 'M' : 'U')}
                </div>
                <div class="message-content">
                    <div class="message-header">
                        <span class="message-sender">${message.sender_info?.username || '未知'}</span>
                        <span class="message-time">${time}</span>
                    </div>
                    <div class="message-bubble">
                        ${messageContent}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 显示消息
     */
    displayMessage(message) {
        const container = document.getElementById('chatMessages');
        const messageHtml = this.renderMessage(message);
        
        // 移除空状态
        const emptyChat = container.querySelector('.empty-chat');
        if (emptyChat) {
            emptyChat.remove();
        }
        
        container.insertAdjacentHTML('beforeend', messageHtml);
        container.scrollTop = container.scrollHeight;
    }

    /**
     * 发送消息
     */
    async sendMessage() {
        const input = document.getElementById('messageInput');
        const content = input.value.trim();
        
        if (!content || !this.currentSessionId) return;

        try {
            // 通过WebSocket发送
            this.sendWebSocketMessage({
                type: 'send_message',
                data: {
                    session_id: this.currentSessionId,
                    content: content,
                    message_type: 'text'
                }
            });

            // 清空输入框
            input.value = '';
            this.autoResizeTextarea(input);

        } catch (error) {
            console.error('发送消息失败:', error);
            this.showNotification('消息发送失败', 'error');
        }
    }

    /**
     * 加载客户列表
     */
    async loadCustomers() {
        try {
            const response = await fetch('/agent/customer/customers', {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            const result = await response.json();
            
            if (result.code === 1) {
                this.customers = result.data.customers;
                this.renderCustomersGrid();
            }
        } catch (error) {
            console.error('加载客户列表失败:', error);
        }
    }

    /**
     * 渲染客户网格
     */
    renderCustomersGrid() {
        const container = document.getElementById('customersGrid');
        
        if (this.customers.length === 0) {
            container.innerHTML = `
                <div class="empty-customers">
                    <div class="empty-icon">👥</div>
                    <div class="empty-text">暂无客户</div>
                </div>
            `;
            return;
        }

        const html = this.customers.map(customer => `
            <div class="customer-card">
                <div class="customer-avatar">
                    ${customer.username?.charAt(0)?.toUpperCase() || 'U'}
                </div>
                <div class="customer-info">
                    <div class="customer-name">${customer.username || '未知用户'}</div>
                    <div class="customer-email">${customer.email || '-'}</div>
                    <div class="customer-balance">${customer.balance || 0} USDT</div>
                    <div class="customer-status ${customer.has_active_session ? 'active' : 'inactive'}">
                        ${customer.has_active_session ? '有活跃会话' : '无活跃会话'}
                    </div>
                </div>
                <div class="customer-actions">
                    ${customer.has_active_session ? 
                        `<button class="btn btn-sm btn-primary" onclick="agentCustomer.openSession('${customer.session_id}')">
                            打开会话
                        </button>` :
                        `<button class="btn btn-sm btn-outline" onclick="agentCustomer.contactCustomer(${customer.id})">
                            联系客户
                        </button>`
                    }
                </div>
                ${customer.unread_count > 0 ? `<div class="customer-badge">${customer.unread_count}</div>` : ''}
            </div>
        `).join('');

        container.innerHTML = html;
    }

    /**
     * 联系客户
     */
    async contactCustomer(customerId) {
        try {
            const response = await fetch('/agent/customer/contact', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.token}`
                },
                body: JSON.stringify({
                    user_id: customerId
                })
            });

            const result = await response.json();
            
            if (result.code === 1) {
                this.showNotification('会话创建成功', 'success');
                this.loadSessions();
                this.loadCustomers();
                
                // 自动选择新创建的会话
                setTimeout(() => {
                    this.selectSession(result.data.session_id, customerId);
                }, 1000);
            } else {
                this.showNotification(result.msg || '联系失败', 'error');
            }
        } catch (error) {
            console.error('联系客户失败:', error);
            this.showNotification('联系失败', 'error');
        }
    }

    /**
     * 打开会话
     */
    openSession(sessionId) {
        const session = this.sessions.find(s => s.session_id === sessionId);
        if (session) {
            this.selectSession(sessionId, session.user_id);
        }
    }

    /**
     * 加载快捷回复
     */
    async loadQuickReplies() {
        try {
            const response = await fetch('/agent/customer/quick-replies', {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            const result = await response.json();
            
            if (result.code === 1) {
                this.quickReplies = result.data;
                this.renderQuickReplies();
            }
        } catch (error) {
            console.error('加载快捷回复失败:', error);
        }
    }

    /**
     * 渲染快捷回复按钮
     */
    renderQuickReplies() {
        const container = document.getElementById('quickReplies');
        
        const html = this.quickReplies.slice(0, 6).map(reply => `
            <button class="quick-reply-btn" onclick="agentCustomer.useQuickReply('${reply.content}')">
                ${reply.title}
            </button>
        `).join('');

        container.innerHTML = html;
    }

    /**
     * 使用快捷回复
     */
    useQuickReply(content) {
        const input = document.getElementById('messageInput');
        input.value = content;
        this.autoResizeTextarea(input);
        input.focus();
    }

    /**
     * 更新在线状态
     */
    async updateStatus() {
        const status = document.getElementById('statusSelect').value;
        this.onlineStatus = status;
        
        try {
            await fetch('/agent/customer/online/status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.token}`
                },
                body: JSON.stringify({
                    status: status
                })
            });

            this.updateStatusIndicator();
            this.updateOnlineStatus();
        } catch (error) {
            console.error('更新状态失败:', error);
        }
    }

    /**
     * 更新状态指示器
     */
    updateStatusIndicator() {
        const indicator = document.getElementById('statusIndicator');
        const statusMap = {
            'online': { class: 'online', color: '#02c076' },
            'busy': { class: 'busy', color: '#f0b90b' },
            'offline': { class: 'offline', color: '#848e9c' }
        };
        
        const statusInfo = statusMap[this.onlineStatus] || statusMap.offline;
        indicator.className = `status-indicator ${statusInfo.class}`;
        indicator.style.backgroundColor = statusInfo.color;
    }

    /**
     * 更新WebSocket在线状态
     */
    updateOnlineStatus() {
        this.sendWebSocketMessage({
            type: 'status_update',
            data: {
                status: this.onlineStatus
            }
        });
    }

    /**
     * 显示客户模态框
     */
    showCustomerModal() {
        const modal = document.getElementById('customerModal');
        const customerList = document.getElementById('modalCustomerList');
        
        // 渲染客户列表
        const html = this.customers.filter(c => !c.has_active_session).map(customer => `
            <div class="modal-customer-item" onclick="agentCustomer.selectModalCustomer(${customer.id})">
                <div class="customer-avatar">
                    ${customer.username?.charAt(0)?.toUpperCase() || 'U'}
                </div>
                <div class="customer-info">
                    <div class="customer-name">${customer.username || '未知用户'}</div>
                    <div class="customer-email">${customer.email || '-'}</div>
                </div>
            </div>
        `).join('');
        
        customerList.innerHTML = html || '<div class="empty-text">所有客户都有活跃会话</div>';
        modal.style.display = 'block';
    }

    /**
     * 隐藏客户模态框
     */
    hideCustomerModal() {
        document.getElementById('customerModal').style.display = 'none';
    }

    /**
     * 选择模态框中的客户
     */
    async selectModalCustomer(customerId) {
        this.hideCustomerModal();
        await this.contactCustomer(customerId);
    }

    /**
     * 工具函数
     */
    getAgentId() {
        // 从JWT token或localStorage获取代理ID
        return 1; // 临时返回，实际应该从认证中获取
    }

    formatTime(timeString) {
        const time = new Date(timeString);
        const now = new Date();
        const diff = now - time;
        
        if (diff < 60000) { // 1分钟内
            return '刚刚';
        } else if (diff < 3600000) { // 1小时内
            return Math.floor(diff / 60000) + '分钟前';
        } else if (diff < 86400000) { // 24小时内
            return time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        } else {
            return time.toLocaleDateString('zh-CN');
        }
    }

    getStatusText(status) {
        const statusMap = {
            'active': '活跃',
            'waiting': '等待',
            'closed': '已关闭'
        };
        return statusMap[status] || status;
    }

    getMessagePreview(message) {
        if (!message) return '暂无消息';
        
        switch (message.message_type) {
            case 'image':
                return '[图片]';
            case 'emoji':
                return message.content;
            default:
                return message.content.length > 30 ? message.content.substring(0, 30) + '...' : message.content;
        }
    }

    formatMessageText(text) {
        return text.replace(/\n/g, '<br>').replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>');
    }

    autoResizeTextarea(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }

    playNotificationSound() {
        try {
            const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
            audio.volume = 0.3;
            audio.play().catch(() => {});
        } catch (error) {
            // 忽略音频播放错误
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">×</button>
        `;
        
        document.body.appendChild(notification);
        
        // 自动移除
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 消息输入
        const messageInput = document.getElementById('messageInput');
        if (messageInput) {
            messageInput.addEventListener('input', (e) => {
                this.autoResizeTextarea(e.target);
            });

            messageInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });
        }

        // 客户搜索
        const customerSearch = document.getElementById('customerSearch');
        if (customerSearch) {
            customerSearch.addEventListener('input', (e) => {
                this.searchCustomers(e.target.value);
            });
        }

        // 模态框关闭
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });
    }

    /**
     * 搜索客户
     */
    searchCustomers(keyword = '') {
        const searchInput = document.getElementById('customerSearch');
        keyword = keyword || searchInput.value.trim();
        
        if (!keyword) {
            this.renderCustomersGrid();
            return;
        }

        const filteredCustomers = this.customers.filter(customer => 
            customer.username?.toLowerCase().includes(keyword.toLowerCase()) ||
            customer.email?.toLowerCase().includes(keyword.toLowerCase())
        );

        this.renderFilteredCustomers(filteredCustomers);
    }

    /**
     * 渲染筛选后的客户
     */
    renderFilteredCustomers(customers) {
        const container = document.getElementById('customersGrid');
        
        if (customers.length === 0) {
            container.innerHTML = `
                <div class="empty-customers">
                    <div class="empty-icon">🔍</div>
                    <div class="empty-text">没有找到匹配的客户</div>
                </div>
            `;
            return;
        }

        const html = customers.map(customer => `
            <div class="customer-card">
                <div class="customer-avatar">
                    ${customer.username?.charAt(0)?.toUpperCase() || 'U'}
                </div>
                <div class="customer-info">
                    <div class="customer-name">${customer.username || '未知用户'}</div>
                    <div class="customer-email">${customer.email || '-'}</div>
                    <div class="customer-balance">${customer.balance || 0} USDT</div>
                    <div class="customer-status ${customer.has_active_session ? 'active' : 'inactive'}">
                        ${customer.has_active_session ? '有活跃会话' : '无活跃会话'}
                    </div>
                </div>
                <div class="customer-actions">
                    ${customer.has_active_session ? 
                        `<button class="btn btn-sm btn-primary" onclick="agentCustomer.openSession('${customer.session_id}')">
                            打开会话
                        </button>` :
                        `<button class="btn btn-sm btn-outline" onclick="agentCustomer.contactCustomer(${customer.id})">
                            联系客户
                        </button>`
                    }
                </div>
                ${customer.unread_count > 0 ? `<div class="customer-badge">${customer.unread_count}</div>` : ''}
            </div>
        `).join('');

        container.innerHTML = html;
    }

    /**
     * 刷新客户列表
     */
    refreshCustomers() {
        this.loadCustomers();
        this.showNotification('客户列表已刷新', 'success');
    }

    /**
     * 其他功能占位符
     */
    showWorkStatistics() {
        this.showNotification('工作统计功能开发中', 'info');
    }

    loadCustomerInfo(customerId) {
        // 加载客户详细信息
        console.log('加载客户信息:', customerId);
    }

    showChatInterface() {
        document.getElementById('chatInput').style.display = 'block';
        
        // 更新聊天头部
        const session = this.sessions.find(s => s.session_id === this.currentSessionId);
        if (session) {
            const chatHeader = document.getElementById('chatHeader');
            chatHeader.querySelector('.user-name').textContent = session.username || '未知用户';
            chatHeader.querySelector('.user-status').textContent = this.getStatusText(session.status);
            chatHeader.querySelector('.user-avatar').textContent = session.username?.charAt(0)?.toUpperCase() || 'U';
        }
    }

    markMessagesAsRead(sessionId) {
        // 标记消息为已读
        fetch('/agent/customer/message/read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.token}`
            },
            body: JSON.stringify({
                session_id: sessionId
            })
        }).catch(error => {
            console.error('标记已读失败:', error);
        });
    }

    updateSessionLastMessage(sessionId, message) {
        const sessionIndex = this.sessions.findIndex(s => s.session_id === sessionId);
        if (sessionIndex !== -1) {
            this.sessions[sessionIndex].last_message = message;
            this.sessions[sessionIndex].last_message_time = message.created_at;
            this.sessions[sessionIndex].unread_count_agent = (this.sessions[sessionIndex].unread_count_agent || 0) + 1;
            
            // 重新渲染会话列表
            this.renderSessionsList();
        }
    }

    handleSessionUpdate(data) {
        const sessionIndex = this.sessions.findIndex(s => s.session_id === data.session_id);
        if (sessionIndex !== -1) {
            this.sessions[sessionIndex] = { ...this.sessions[sessionIndex], ...data };
            this.renderSessionsList();
        }
    }

    handleTypingIndicator(data) {
        if (data.session_id === this.currentSessionId) {
            // 显示输入指示器
            console.log('用户正在输入:', data.typing);
        }
    }

    updateUnreadCount() {
        const totalUnread = this.sessions.reduce((sum, session) => sum + (session.unread_count_agent || 0), 0);
        document.getElementById('unreadMessages').textContent = totalUnread;
    }

    // 占位符方法
    viewCustomerInfo() { this.showNotification('客户信息功能开发中', 'info'); }
    viewCustomerOrders() { this.showNotification('客户订单功能开发中', 'info'); }
    viewCustomerTransactions() { this.showNotification('交易记录功能开发中', 'info'); }
    createVirtualOrder() { this.showNotification('虚拟订单功能开发中', 'info'); }
    sendTradingTip() { this.showNotification('交易提示功能开发中', 'info'); }
    sendMarketAnalysis() { this.showNotification('市场分析功能开发中', 'info'); }
    sendPromotionInfo() { this.showNotification('优惠信息功能开发中', 'info'); }
    showEmojiPanel() { this.showNotification('表情面板功能开发中', 'info'); }
    hideStatisticsModal() { document.getElementById('statisticsModal').style.display = 'none'; }
}

// 初始化代理端客服系统
document.addEventListener('DOMContentLoaded', function() {
    window.agentCustomer = new AgentCustomerService();
});
