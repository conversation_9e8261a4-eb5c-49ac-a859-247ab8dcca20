<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\BaseController;
use app\common\service\AssetService;
use app\common\model\User;
use think\facade\Request;
use think\facade\Validate;

/**
 * 资产管理控制器
 */
class Asset extends BaseController
{
    protected $assetService;

    public function initialize()
    {
        parent::initialize();
        $this->assetService = new AssetService();
    }

    /**
     * 获取用户资产列表
     * GET /api/asset/list
     */
    public function index()
    {
        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $result = $this->assetService->getUserAssets($userId);
        return json($result);
    }

    /**
     * 获取指定币种资产
     * GET /api/asset/detail
     */
    public function detail()
    {
        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $coinSymbol = Request::get('coin_symbol', '');
        if (empty($coinSymbol)) {
            return $this->error('币种不能为空');
        }

        $result = $this->assetService->getUserAsset($userId, $coinSymbol);
        return json($result);
    }

    /**
     * 获取充值地址
     * GET /api/asset/deposit-address
     */
    public function getDepositAddress()
    {
        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $coinSymbol = Request::get('coin_symbol', '');
        if (empty($coinSymbol)) {
            return $this->error('币种不能为空');
        }

        $result = $this->assetService->getDepositAddress($userId, $coinSymbol);
        return json($result);
    }

    /**
     * 充值
     * POST /api/asset/deposit
     */
    public function deposit()
    {
        if (!Request::isPost()) {
            return $this->error('请求方式错误');
        }

        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $data = Request::post();

        $validate = Validate::rule([
            'coin_symbol' => 'require|alphaNum',
            'amount' => 'require|float|gt:0',
            'tx_hash' => 'require'
        ])->message([
            'coin_symbol.require' => '币种不能为空',
            'amount.require' => '金额不能为空',
            'amount.gt' => '金额必须大于0',
            'tx_hash.require' => '交易哈希不能为空'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        $result = $this->assetService->deposit($userId, $data['coin_symbol'], $data['amount'], $data['tx_hash']);
        return json($result);
    }

    /**
     * 提现
     * POST /api/asset/withdraw
     */
    public function withdraw()
    {
        if (!Request::isPost()) {
            return $this->error('请求方式错误');
        }

        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $data = Request::post();

        $validate = Validate::rule([
            'coin_symbol' => 'require|alphaNum',
            'amount' => 'require|float|gt:0',
            'address' => 'require',
            'pay_password' => 'require'
        ])->message([
            'coin_symbol.require' => '币种不能为空',
            'amount.require' => '金额不能为空',
            'amount.gt' => '金额必须大于0',
            'address.require' => '提现地址不能为空',
            'pay_password.require' => '支付密码不能为空'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        $result = $this->assetService->withdraw(
            $userId, 
            $data['coin_symbol'], 
            $data['amount'], 
            $data['address'], 
            $data['pay_password']
        );
        
        return json($result);
    }

    /**
     * 内部转账
     * POST /api/asset/transfer
     */
    public function transfer()
    {
        if (!Request::isPost()) {
            return $this->error('请求方式错误');
        }

        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $data = Request::post();

        $validate = Validate::rule([
            'to_user' => 'require',
            'coin_symbol' => 'require|alphaNum',
            'amount' => 'require|float|gt:0',
            'pay_password' => 'require'
        ])->message([
            'to_user.require' => '接收用户不能为空',
            'coin_symbol.require' => '币种不能为空',
            'amount.require' => '金额不能为空',
            'amount.gt' => '金额必须大于0',
            'pay_password.require' => '支付密码不能为空'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        // 查找接收用户
        $toUser = User::where('username', $data['to_user'])
                     ->whereOr('email', $data['to_user'])
                     ->find();

        if (!$toUser) {
            return $this->error('接收用户不存在');
        }

        if ($toUser->id == $userId) {
            return $this->error('不能转账给自己');
        }

        $result = $this->assetService->transfer(
            $userId, 
            $toUser->id, 
            $data['coin_symbol'], 
            $data['amount'], 
            $data['pay_password']
        );
        
        return json($result);
    }

    /**
     * 获取资产变动记录
     * GET /api/asset/records
     */
    public function records()
    {
        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $coinSymbol = Request::get('coin_symbol', '');
        $page = Request::get('page/d', 1);
        $limit = Request::get('limit/d', 20);

        $result = $this->assetService->getAssetRecords($userId, $coinSymbol, $page, $limit);
        return json($result);
    }

    /**
     * 获取支持的币种列表
     * GET /api/asset/coins
     */
    public function getSupportedCoins()
    {
        $result = $this->assetService->getSupportedCoins();
        return json($result);
    }

    /**
     * 冻结资产（管理员功能）
     * POST /api/asset/freeze
     */
    public function freeze()
    {
        if (!Request::isPost()) {
            return $this->error('请求方式错误');
        }

        // 这里应该检查管理员权限
        $adminId = $this->getAdminId();
        if (!$adminId) {
            return $this->error('无权限操作', 403);
        }

        $data = Request::post();

        $validate = Validate::rule([
            'user_id' => 'require|integer',
            'coin_symbol' => 'require|alphaNum',
            'amount' => 'require|float|gt:0'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        $result = $this->assetService->freezeAsset($data['user_id'], $data['coin_symbol'], $data['amount']);
        return json($result);
    }

    /**
     * 解冻资产（管理员功能）
     * POST /api/asset/unfreeze
     */
    public function unfreeze()
    {
        if (!Request::isPost()) {
            return $this->error('请求方式错误');
        }

        // 这里应该检查管理员权限
        $adminId = $this->getAdminId();
        if (!$adminId) {
            return $this->error('无权限操作', 403);
        }

        $data = Request::post();

        $validate = Validate::rule([
            'user_id' => 'require|integer',
            'coin_symbol' => 'require|alphaNum',
            'amount' => 'require|float|gt:0'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        $result = $this->assetService->unfreezeAsset($data['user_id'], $data['coin_symbol'], $data['amount']);
        return json($result);
    }

    /**
     * 获取当前用户ID
     */
    private function getUserId(): int
    {
        $token = Request::header('Authorization');
        if (!$token) {
            return 0;
        }

        $token = str_replace('Bearer ', '', $token);
        $payload = User::verifyToken($token);
        return $payload ? $payload['user_id'] : 0;
    }

    /**
     * 获取管理员ID
     */
    private function getAdminId(): int
    {
        // 这里应该从JWT token或session中获取管理员ID
        return Request::header('admin-id', 0) ?: 0;
    }
}
