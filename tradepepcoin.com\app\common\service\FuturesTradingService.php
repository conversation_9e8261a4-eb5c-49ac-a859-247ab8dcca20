<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\FuturesContract;
use app\common\model\FuturesPosition;
use app\common\model\UserAsset;
use app\common\model\FinancialRecord;
use think\facade\Db;
use think\facade\Log;

/**
 * 期货交易服务类
 */
class FuturesTradingService
{
    protected $klineService;

    public function __construct()
    {
        $this->klineService = new KlineService();
    }

    /**
     * 开仓
     */
    public function openPosition(array $orderData): array
    {
        try {
            Db::startTrans();

            // 验证合约
            $contract = FuturesContract::getBySymbol($orderData['symbol']);
            if (!$contract || !$contract->isTradeEnabled()) {
                throw new \Exception('合约不存在或已禁用');
            }

            // 验证订单数据
            $validation = $this->validateOrderData($orderData, $contract);
            if (!$validation['valid']) {
                throw new \Exception($validation['message']);
            }

            // 计算保证金需求
            $currentPrice = $contract->getMarkPrice();
            $marginRequired = $contract->calculateInitialMargin(
                $orderData['size'], 
                $currentPrice, 
                $orderData['leverage']
            );

            // 检查用户资产
            $assetCheck = $this->checkUserMargin($orderData['user_id'], $marginRequired);
            if (!$assetCheck['valid']) {
                throw new \Exception($assetCheck['message']);
            }

            // 冻结保证金
            $this->freezeMargin($orderData['user_id'], $marginRequired);

            // 获取或创建持仓
            $position = FuturesPosition::getOrCreatePosition(
                $orderData['user_id'], 
                $orderData['symbol'], 
                $orderData['side']
            );

            // 更新持仓
            $position->updatePosition($orderData['size'], $currentPrice, $marginRequired);

            // 记录财务流水
            $this->recordFinancialFlow($orderData['user_id'], 'futures_open', -$marginRequired, $orderData['symbol']);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '开仓成功',
                'data' => [
                    'symbol' => $orderData['symbol'],
                    'side' => $orderData['side'],
                    'size' => $orderData['size'],
                    'entry_price' => $currentPrice,
                    'margin' => $marginRequired,
                    'liquidation_price' => $position->liquidation_price,
                    'leverage' => $orderData['leverage']
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('期货开仓失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 平仓
     */
    public function closePosition(int $userId, string $symbol, int $side, float $closeSize = 0): array
    {
        try {
            Db::startTrans();

            $position = FuturesPosition::where([
                'user_id' => $userId,
                'symbol' => $symbol,
                'side' => $side
            ])->find();

            if (!$position || $position->size <= 0) {
                throw new \Exception('持仓不存在');
            }

            $contract = $position->contract;
            if (!$contract) {
                throw new \Exception('合约不存在');
            }

            $closeSize = $closeSize ?: $position->size;
            $currentPrice = $contract->getMarkPrice();

            $result = $position->closePosition($closeSize, $currentPrice);
            if ($result['code'] != 1) {
                throw new \Exception($result['msg']);
            }

            // 释放保证金并结算盈亏
            $this->settlePosition($userId, $result['data']);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '平仓成功',
                'data' => $result['data']
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('期货平仓失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 强制平仓
     */
    public function liquidatePosition(int $positionId): array
    {
        try {
            Db::startTrans();

            $position = FuturesPosition::find($positionId);
            if (!$position) {
                throw new \Exception('持仓不存在');
            }

            $contract = $position->contract;
            if (!$contract) {
                throw new \Exception('合约不存在');
            }

            $markPrice = $contract->getMarkPrice();
            
            if (!$position->shouldLiquidate($markPrice)) {
                throw new \Exception('持仓不需要强平');
            }

            $liquidationPrice = $position->liquidation_price;
            $position->liquidate($liquidationPrice);

            // 强平结算
            $this->settleLiquidation($position, $liquidationPrice);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '强制平仓成功',
                'data' => [
                    'position_id' => $positionId,
                    'symbol' => $position->symbol,
                    'liquidation_price' => $liquidationPrice,
                    'pnl' => $position->realized_pnl
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('强制平仓失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 批量检查强平
     */
    public function checkLiquidations(string $symbol): array
    {
        try {
            $contract = FuturesContract::getBySymbol($symbol);
            if (!$contract) {
                throw new \Exception('合约不存在');
            }

            $markPrice = $contract->getMarkPrice();
            $liquidationPositions = FuturesPosition::getLiquidationPositions($symbol, $markPrice);

            $liquidatedCount = 0;
            foreach ($liquidationPositions as $position) {
                $result = $this->liquidatePosition($position->id);
                if ($result['code'] == 1) {
                    $liquidatedCount++;
                }
            }

            return [
                'code' => 1,
                'msg' => "检查完成，强平 {$liquidatedCount} 个持仓",
                'data' => [
                    'symbol' => $symbol,
                    'mark_price' => $markPrice,
                    'liquidated_count' => $liquidatedCount
                ]
            ];

        } catch (\Exception $e) {
            Log::error('批量检查强平失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 获取用户持仓
     */
    public function getUserPositions(int $userId, string $symbol = ''): array
    {
        try {
            $positions = FuturesPosition::getUserPositions($userId, $symbol);
            
            // 更新实时数据
            foreach ($positions as &$position) {
                $contract = FuturesContract::getBySymbol($position['symbol']);
                if ($contract) {
                    $markPrice = $contract->getMarkPrice();
                    $positionModel = new FuturesPosition($position);
                    
                    $position['mark_price'] = $markPrice;
                    $position['unrealized_pnl'] = $positionModel->calculateUnrealizedPnl($markPrice);
                    $position['margin_ratio'] = $positionModel->calculateMarginRatio($markPrice);
                    $position['funding_rate'] = $contract->getCurrentFundingRate();
                    $position['next_funding_time'] = $contract->getNextFundingTime();
                }
            }

            return [
                'code' => 1,
                'data' => $positions
            ];

        } catch (\Exception $e) {
            Log::error('获取用户持仓失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 获取用户期货交易统计
     */
    public function getUserFuturesStats(int $userId): array
    {
        try {
            $stats = FuturesPosition::getPositionStats($userId);
            
            return [
                'code' => 1,
                'data' => $stats
            ];

        } catch (\Exception $e) {
            Log::error('获取期货交易统计失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 计算资金费用（永续合约）
     */
    public function calculateFundingFee(int $userId, string $symbol): array
    {
        try {
            $contract = FuturesContract::getBySymbol($symbol);
            if (!$contract || $contract->contract_type != FuturesContract::TYPE_PERPETUAL) {
                return ['code' => 0, 'msg' => '不是永续合约'];
            }

            $positions = FuturesPosition::where('user_id', $userId)
                                       ->where('symbol', $symbol)
                                       ->where('size', '>', 0)
                                       ->select();

            $totalFundingFee = 0;
            $fundingRate = $contract->getCurrentFundingRate();
            $markPrice = $contract->getMarkPrice();

            foreach ($positions as $position) {
                $notionalValue = $position->size * $markPrice * $contract->contract_size;
                $fundingFee = $notionalValue * $fundingRate;
                
                // 多头支付，空头收取
                if ($position->side == FuturesPosition::SIDE_LONG) {
                    $fundingFee = -$fundingFee;
                }
                
                $totalFundingFee += $fundingFee;
            }

            return [
                'code' => 1,
                'data' => [
                    'symbol' => $symbol,
                    'funding_rate' => $fundingRate,
                    'funding_fee' => $totalFundingFee,
                    'next_funding_time' => $contract->getNextFundingTime()
                ]
            ];

        } catch (\Exception $e) {
            Log::error('计算资金费用失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '计算失败'];
        }
    }

    /**
     * 验证订单数据
     */
    private function validateOrderData(array $data, FuturesContract $contract): array
    {
        // 验证必要字段
        $required = ['user_id', 'symbol', 'side', 'size', 'leverage'];
        foreach ($required as $field) {
            if (!isset($data[$field]) || $data[$field] === '') {
                return ['valid' => false, 'message' => "字段 {$field} 不能为空"];
            }
        }

        // 验证持仓方向
        if (!in_array($data['side'], [FuturesPosition::SIDE_LONG, FuturesPosition::SIDE_SHORT])) {
            return ['valid' => false, 'message' => '无效的持仓方向'];
        }

        // 验证杠杆倍数
        if ($data['leverage'] < 1 || $data['leverage'] > $contract->max_leverage) {
            return ['valid' => false, 'message' => "杠杆倍数必须在1-{$contract->max_leverage}倍之间"];
        }

        // 验证数量
        if ($data['size'] <= 0) {
            return ['valid' => false, 'message' => '交易数量必须大于0'];
        }

        return ['valid' => true];
    }

    /**
     * 检查用户保证金
     */
    private function checkUserMargin(int $userId, float $marginRequired): array
    {
        $asset = UserAsset::where('user_id', $userId)
                         ->where('coin_symbol', 'USDT')
                         ->find();

        if (!$asset) {
            return ['valid' => false, 'message' => 'USDT资产不存在'];
        }

        if ($asset->available < $marginRequired) {
            return ['valid' => false, 'message' => '保证金不足'];
        }

        return ['valid' => true];
    }

    /**
     * 冻结保证金
     */
    private function freezeMargin(int $userId, float $margin): void
    {
        $asset = UserAsset::where('user_id', $userId)
                         ->where('coin_symbol', 'USDT')
                         ->find();

        $asset->available -= $margin;
        $asset->frozen += $margin;
        $asset->save();
    }

    /**
     * 结算持仓
     */
    private function settlePosition(int $userId, array $closeData): void
    {
        $pnl = $closeData['pnl'];
        $releasedMargin = $closeData['released_margin'];

        $asset = UserAsset::where('user_id', $userId)
                         ->where('coin_symbol', 'USDT')
                         ->find();

        // 释放保证金
        $asset->frozen -= $releasedMargin;
        
        // 结算盈亏
        $finalAmount = $releasedMargin + $pnl;
        $asset->available += $finalAmount;
        $asset->total = $asset->available + $asset->frozen;
        $asset->save();

        // 记录财务流水
        $this->recordFinancialFlow($userId, $pnl >= 0 ? 'futures_profit' : 'futures_loss', $finalAmount, '期货平仓');
    }

    /**
     * 强平结算
     */
    private function settleLiquidation(FuturesPosition $position, float $liquidationPrice): void
    {
        $asset = UserAsset::where('user_id', $position->user_id)
                         ->where('coin_symbol', 'USDT')
                         ->find();

        // 释放保证金（通常强平后保证金损失殆尽）
        $asset->frozen -= $position->margin;
        
        // 如果还有剩余价值，返还给用户
        $remainingValue = max(0, $position->margin + $position->realized_pnl);
        if ($remainingValue > 0) {
            $asset->available += $remainingValue;
        }
        
        $asset->total = $asset->available + $asset->frozen;
        $asset->save();

        // 记录财务流水
        $this->recordFinancialFlow($position->user_id, 'futures_liquidation', $position->realized_pnl, $position->symbol . '期货强制平仓');
    }

    /**
     * 记录财务流水
     */
    private function recordFinancialFlow(int $userId, string $type, float $amount, string $description): void
    {
        $asset = UserAsset::where('user_id', $userId)
                         ->where('coin_symbol', 'USDT')
                         ->find();

        FinancialRecord::create([
            'user_id' => $userId,
            'type' => $type,
            'coin_symbol' => 'USDT',
            'amount' => $amount,
            'balance' => $asset ? $asset->available : 0,
            'description' => $description,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
}
