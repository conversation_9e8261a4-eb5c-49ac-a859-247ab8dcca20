<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\InstitutionalAccount;
use app\common\model\OtcOrder;
use app\common\model\CustodyAccount;
use app\common\model\InstitutionalTrade;
use app\common\model\User;
use app\common\model\UserAsset;
use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

/**
 * 机构服务
 */
class InstitutionalService
{
    // 机构账户类型
    const ACCOUNT_TYPE_HEDGE_FUND = 'hedge_fund';        // 对冲基金
    const ACCOUNT_TYPE_INVESTMENT_BANK = 'investment_bank'; // 投资银行
    const ACCOUNT_TYPE_FAMILY_OFFICE = 'family_office';   // 家族办公室
    const ACCOUNT_TYPE_PENSION_FUND = 'pension_fund';     // 养老基金
    const ACCOUNT_TYPE_INSURANCE = 'insurance';           // 保险公司
    const ACCOUNT_TYPE_SOVEREIGN_FUND = 'sovereign_fund'; // 主权基金

    // 账户状态
    const STATUS_PENDING = 0;     // 待审核
    const STATUS_ACTIVE = 1;      // 活跃
    const STATUS_SUSPENDED = 2;   // 暂停
    const STATUS_CLOSED = 3;      // 关闭

    // OTC订单状态
    const OTC_STATUS_PENDING = 0;    // 待匹配
    const OTC_STATUS_MATCHED = 1;    // 已匹配
    const OTC_STATUS_EXECUTING = 2;  // 执行中
    const OTC_STATUS_COMPLETED = 3;  // 已完成
    const OTC_STATUS_CANCELLED = 4;  // 已取消

    // 托管服务类型
    const CUSTODY_TYPE_COLD = 'cold';     // 冷存储
    const CUSTODY_TYPE_HOT = 'hot';       // 热存储
    const CUSTODY_TYPE_MULTI_SIG = 'multi_sig'; // 多重签名

    /**
     * 创建机构账户
     */
    public function createInstitutionalAccount(array $data): array
    {
        try {
            // 验证机构信息
            $validation = $this->validateInstitutionalData($data);
            if (!$validation['code']) {
                return $validation;
            }

            // 创建机构账户
            $accountData = [
                'account_id' => $this->generateAccountId(),
                'company_name' => $data['company_name'],
                'company_type' => $data['company_type'],
                'registration_number' => $data['registration_number'],
                'registration_country' => $data['registration_country'],
                'business_license' => $data['business_license'],
                'contact_person' => $data['contact_person'],
                'contact_email' => $data['contact_email'],
                'contact_phone' => $data['contact_phone'],
                'address' => $data['address'],
                'aum' => $data['aum'] ?? 0, // 管理资产规模
                'trading_volume_requirement' => $data['trading_volume_requirement'] ?? 0,
                'fee_tier' => $this->calculateFeeTier($data['aum'] ?? 0),
                'status' => self::STATUS_PENDING,
                'created_at' => date('Y-m-d H:i:s')
            ];

            $account = InstitutionalAccount::create($accountData);

            if ($account) {
                Log::info("机构账户创建成功", [
                    'account_id' => $account->account_id,
                    'company_name' => $data['company_name']
                ]);

                return [
                    'code' => 1,
                    'msg' => '机构账户申请提交成功，等待审核',
                    'data' => ['account_id' => $account->account_id]
                ];
            } else {
                return ['code' => 0, 'msg' => '机构账户创建失败'];
            }
        } catch (\Exception $e) {
            Log::error('机构账户创建失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '机构账户创建失败'];
        }
    }

    /**
     * 审核机构账户
     */
    public function reviewInstitutionalAccount(string $accountId, array $reviewData): array
    {
        try {
            $account = InstitutionalAccount::where('account_id', $accountId)->find();
            if (!$account) {
                return ['code' => 0, 'msg' => '机构账户不存在'];
            }

            if ($account->status !== self::STATUS_PENDING) {
                return ['code' => 0, 'msg' => '账户状态不允许审核'];
            }

            if ($reviewData['action'] === 'approve') {
                // 通过审核
                $account->status = self::STATUS_ACTIVE;
                $account->approved_at = date('Y-m-d H:i:s');
                $account->reviewer_id = $reviewData['reviewer_id'] ?? 0;
                $account->review_notes = $reviewData['notes'] ?? '';

                // 分配专属API密钥
                $apiCredentials = $this->generateApiCredentials($account);
                $account->api_key = $apiCredentials['api_key'];
                $account->api_secret = $apiCredentials['api_secret'];

                $message = '机构账户审核通过';
            } else {
                // 拒绝审核
                $account->status = self::STATUS_CLOSED;
                $account->rejected_at = date('Y-m-d H:i:s');
                $account->reviewer_id = $reviewData['reviewer_id'] ?? 0;
                $account->reject_reason = $reviewData['reason'] ?? '';

                $message = '机构账户审核拒绝';
            }

            $account->save();

            Log::info("机构账户审核完成", [
                'account_id' => $accountId,
                'action' => $reviewData['action'],
                'reviewer_id' => $reviewData['reviewer_id'] ?? 0
            ]);

            return [
                'code' => 1,
                'msg' => $message
            ];
        } catch (\Exception $e) {
            Log::error('机构账户审核失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '机构账户审核失败'];
        }
    }

    /**
     * 创建OTC订单
     */
    public function createOtcOrder(string $accountId, array $data): array
    {
        try {
            $account = InstitutionalAccount::where('account_id', $accountId)->find();
            if (!$account || $account->status !== self::STATUS_ACTIVE) {
                return ['code' => 0, 'msg' => '机构账户不存在或未激活'];
            }

            // 验证OTC订单数据
            $validation = $this->validateOtcOrderData($data);
            if (!$validation['code']) {
                return $validation;
            }

            // 创建OTC订单
            $orderData = [
                'order_id' => $this->generateOtcOrderId(),
                'account_id' => $accountId,
                'type' => $data['type'], // buy/sell
                'coin_symbol' => $data['coin_symbol'],
                'amount' => $data['amount'],
                'price' => $data['price'] ?? 0,
                'total' => $data['total'] ?? ($data['amount'] * ($data['price'] ?? 0)),
                'min_amount' => $data['min_amount'] ?? $data['amount'],
                'max_amount' => $data['max_amount'] ?? $data['amount'],
                'payment_methods' => $data['payment_methods'] ?? [],
                'settlement_time' => $data['settlement_time'] ?? 'T+1',
                'remarks' => $data['remarks'] ?? '',
                'status' => self::OTC_STATUS_PENDING,
                'created_at' => date('Y-m-d H:i:s'),
                'expires_at' => date('Y-m-d H:i:s', strtotime('+24 hours'))
            ];

            $order = OtcOrder::create($orderData);

            if ($order) {
                // 通知OTC交易员
                $this->notifyOtcTraders($order);

                Log::info("OTC订单创建成功", [
                    'order_id' => $order->order_id,
                    'account_id' => $accountId,
                    'type' => $data['type'],
                    'amount' => $data['amount']
                ]);

                return [
                    'code' => 1,
                    'msg' => 'OTC订单创建成功',
                    'data' => ['order_id' => $order->order_id]
                ];
            } else {
                return ['code' => 0, 'msg' => 'OTC订单创建失败'];
            }
        } catch (\Exception $e) {
            Log::error('OTC订单创建失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'OTC订单创建失败'];
        }
    }

    /**
     * 匹配OTC订单
     */
    public function matchOtcOrder(string $orderId, array $matchData): array
    {
        try {
            $order = OtcOrder::where('order_id', $orderId)->find();
            if (!$order || $order->status !== self::OTC_STATUS_PENDING) {
                return ['code' => 0, 'msg' => 'OTC订单不存在或状态不正确'];
            }

            // 验证匹配数据
            if (empty($matchData['counterparty_id'])) {
                return ['code' => 0, 'msg' => '对手方信息不能为空'];
            }

            // 更新订单状态
            $order->status = self::OTC_STATUS_MATCHED;
            $order->counterparty_id = $matchData['counterparty_id'];
            $order->matched_price = $matchData['matched_price'] ?? $order->price;
            $order->matched_amount = $matchData['matched_amount'] ?? $order->amount;
            $order->matched_at = date('Y-m-d H:i:s');
            $order->save();

            // 创建交易记录
            $this->createInstitutionalTrade($order, $matchData);

            Log::info("OTC订单匹配成功", [
                'order_id' => $orderId,
                'counterparty_id' => $matchData['counterparty_id']
            ]);

            return [
                'code' => 1,
                'msg' => 'OTC订单匹配成功'
            ];
        } catch (\Exception $e) {
            Log::error('OTC订单匹配失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'OTC订单匹配失败'];
        }
    }

    /**
     * 创建托管账户
     */
    public function createCustodyAccount(string $accountId, array $data): array
    {
        try {
            $account = InstitutionalAccount::where('account_id', $accountId)->find();
            if (!$account || $account->status !== self::STATUS_ACTIVE) {
                return ['code' => 0, 'msg' => '机构账户不存在或未激活'];
            }

            // 创建托管账户
            $custodyData = [
                'custody_id' => $this->generateCustodyId(),
                'account_id' => $accountId,
                'custody_type' => $data['custody_type'],
                'coin_symbols' => $data['coin_symbols'] ?? [],
                'security_level' => $data['security_level'] ?? 'high',
                'multi_sig_threshold' => $data['multi_sig_threshold'] ?? 2,
                'authorized_signers' => $data['authorized_signers'] ?? [],
                'insurance_coverage' => $data['insurance_coverage'] ?? 0,
                'monthly_fee' => $this->calculateCustodyFee($data),
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s')
            ];

            $custody = CustodyAccount::create($custodyData);

            if ($custody) {
                // 生成托管地址
                $addresses = $this->generateCustodyAddresses($custody);
                $custody->addresses = $addresses;
                $custody->save();

                Log::info("托管账户创建成功", [
                    'custody_id' => $custody->custody_id,
                    'account_id' => $accountId
                ]);

                return [
                    'code' => 1,
                    'msg' => '托管账户创建成功',
                    'data' => [
                        'custody_id' => $custody->custody_id,
                        'addresses' => $addresses
                    ]
                ];
            } else {
                return ['code' => 0, 'msg' => '托管账户创建失败'];
            }
        } catch (\Exception $e) {
            Log::error('托管账户创建失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '托管账户创建失败'];
        }
    }

    /**
     * 获取机构账户列表
     */
    public function getInstitutionalAccounts(array $filters = []): array
    {
        $query = InstitutionalAccount::query();

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['company_type'])) {
            $query->where('company_type', $filters['company_type']);
        }

        $accounts = $query->order('created_at', 'desc')->select();

        return [
            'code' => 1,
            'data' => $accounts->toArray()
        ];
    }

    /**
     * 获取OTC订单列表
     */
    public function getOtcOrders(string $accountId = '', array $filters = []): array
    {
        $query = OtcOrder::query();

        if (!empty($accountId)) {
            $query->where('account_id', $accountId);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['coin_symbol'])) {
            $query->where('coin_symbol', $filters['coin_symbol']);
        }

        $orders = $query->order('created_at', 'desc')->select();

        return [
            'code' => 1,
            'data' => $orders->toArray()
        ];
    }

    /**
     * 获取机构交易统计
     */
    public function getInstitutionalStats(string $accountId): array
    {
        $account = InstitutionalAccount::where('account_id', $accountId)->find();
        if (!$account) {
            return ['code' => 0, 'msg' => '机构账户不存在'];
        }

        // 统计交易数据
        $totalTrades = InstitutionalTrade::where('account_id', $accountId)->count();
        $totalVolume = InstitutionalTrade::where('account_id', $accountId)->sum('total');
        $monthlyVolume = InstitutionalTrade::where('account_id', $accountId)
                                          ->where('created_at', '>=', date('Y-m-01'))
                                          ->sum('total');

        // 统计OTC订单
        $otcOrders = OtcOrder::where('account_id', $accountId)->count();
        $completedOtc = OtcOrder::where('account_id', $accountId)
                               ->where('status', self::OTC_STATUS_COMPLETED)
                               ->count();

        return [
            'code' => 1,
            'data' => [
                'total_trades' => $totalTrades,
                'total_volume' => $totalVolume,
                'monthly_volume' => $monthlyVolume,
                'otc_orders' => $otcOrders,
                'completed_otc' => $completedOtc,
                'fee_tier' => $account->fee_tier,
                'current_month_fee' => $this->calculateMonthlyFee($account)
            ]
        ];
    }

    /**
     * 验证机构数据
     */
    private function validateInstitutionalData(array $data): array
    {
        if (empty($data['company_name'])) {
            return ['code' => 0, 'msg' => '公司名称不能为空'];
        }

        if (empty($data['registration_number'])) {
            return ['code' => 0, 'msg' => '注册号不能为空'];
        }

        if (empty($data['contact_email'])) {
            return ['code' => 0, 'msg' => '联系邮箱不能为空'];
        }

        return ['code' => 1, 'msg' => '验证通过'];
    }

    /**
     * 验证OTC订单数据
     */
    private function validateOtcOrderData(array $data): array
    {
        if (empty($data['type']) || !in_array($data['type'], ['buy', 'sell'])) {
            return ['code' => 0, 'msg' => '订单类型不正确'];
        }

        if (empty($data['coin_symbol'])) {
            return ['code' => 0, 'msg' => '币种不能为空'];
        }

        if (empty($data['amount']) || $data['amount'] <= 0) {
            return ['code' => 0, 'msg' => '数量必须大于0'];
        }

        return ['code' => 1, 'msg' => '验证通过'];
    }

    /**
     * 其他辅助方法
     */
    private function generateAccountId(): string
    {
        return 'INST' . date('YmdHis') . mt_rand(1000, 9999);
    }

    private function generateOtcOrderId(): string
    {
        return 'OTC' . date('YmdHis') . mt_rand(1000, 9999);
    }

    private function generateCustodyId(): string
    {
        return 'CUST' . date('YmdHis') . mt_rand(1000, 9999);
    }

    private function calculateFeeTier(float $aum): string
    {
        if ($aum >= ********0) return 'VIP3'; // 1亿以上
        if ($aum >= ********) return 'VIP2';  // 5000万以上
        if ($aum >= ********) return 'VIP1';  // 1000万以上
        return 'Standard';
    }

    private function generateApiCredentials(InstitutionalAccount $account): array
    {
        return [
            'api_key' => 'API_' . strtoupper(md5($account->account_id . time())),
            'api_secret' => hash('sha256', $account->account_id . time() . mt_rand())
        ];
    }

    private function notifyOtcTraders(OtcOrder $order): void
    {
        // 通知OTC交易员的实现
    }

    private function createInstitutionalTrade(OtcOrder $order, array $matchData): void
    {
        // 创建机构交易记录的实现
    }

    private function calculateCustodyFee(array $data): float
    {
        // 计算托管费用的实现
        return 1000.0;
    }

    private function generateCustodyAddresses(CustodyAccount $custody): array
    {
        // 生成托管地址的实现
        return [];
    }

    private function calculateMonthlyFee(InstitutionalAccount $account): float
    {
        // 计算月度费用的实现
        return 5000.0;
    }
}
