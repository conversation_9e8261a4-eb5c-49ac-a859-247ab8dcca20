/* 前端客服系统样式 */

#customerServiceWidget {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 10000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* ==================== 客服按钮 ==================== */
.service-button {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  position: relative;
  animation: pulse 2s infinite;
}

.service-button:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
}

.service-icon {
  font-size: 24px;
  color: white;
}

.service-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  animation: bounce 1s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4); }
  50% { box-shadow: 0 4px 20px rgba(102, 126, 234, 0.8); }
  100% { box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-5px); }
  60% { transform: translateY(-3px); }
}

/* ==================== 客服窗口 ==================== */
.service-window {
  position: absolute;
  bottom: 80px;
  right: 0;
  width: 380px;
  height: 500px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  transform: scale(0.8) translateY(20px);
  opacity: 0;
  transition: all 0.3s ease;
}

.service-window-open {
  transform: scale(1) translateY(0);
  opacity: 1;
}

/* 窗口头部 */
.service-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-title {
  display: flex;
  align-items: center;
}

.service-avatar {
  font-size: 24px;
  margin-right: 12px;
}

.service-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 2px;
}

.service-status {
  font-size: 12px;
  opacity: 0.9;
}

.service-status.online {
  color: #2ed573;
}

.service-actions {
  display: flex;
  gap: 8px;
}

.service-minimize,
.service-close {
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease;
}

.service-minimize:hover,
.service-close:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 窗口主体 */
.service-body {
  height: calc(100% - 72px);
  display: flex;
  flex-direction: column;
}

/* ==================== 消息列表 ==================== */
.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #f8f9fa;
}

.message-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  color: #666;
  font-size: 14px;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e0e0e0;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 消息项 */
.message {
  display: flex;
  margin-bottom: 16px;
  animation: messageSlideIn 0.3s ease;
}

.message-own {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #667eea;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
  margin: 0 8px;
  flex-shrink: 0;
}

.message-own .message-avatar {
  background: #2ed573;
}

.message-content {
  max-width: 70%;
  min-width: 100px;
}

.message-info {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
  color: #666;
}

.message-own .message-info {
  flex-direction: row-reverse;
}

.message-sender {
  font-weight: 500;
  margin-right: 8px;
}

.message-own .message-sender {
  margin-right: 0;
  margin-left: 8px;
}

.message-time {
  opacity: 0.7;
}

/* 消息内容 */
.message-text {
  background: white;
  padding: 12px 16px;
  border-radius: 18px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  word-wrap: break-word;
  line-height: 1.4;
}

.message-own .message-text {
  background: #667eea;
  color: white;
}

.message-image {
  position: relative;
  cursor: pointer;
}

.message-image img {
  max-width: 200px;
  max-height: 200px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.message-image:hover img {
  transform: scale(1.05);
}

.image-fullscreen img {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(1.5);
  z-index: 10001;
  max-width: 90vw;
  max-height: 90vh;
  border-radius: 8px;
}

.message-video video {
  max-width: 200px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message-emoji {
  font-size: 32px;
  padding: 8px;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ==================== 输入区域 ==================== */
.message-input-area {
  border-top: 1px solid #e0e0e0;
  background: white;
}

.input-toolbar {
  display: flex;
  padding: 8px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.toolbar-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 6px;
  font-size: 16px;
  margin-right: 8px;
  transition: background 0.2s ease;
}

.toolbar-btn:hover {
  background: #f0f0f0;
}

/* 表情面板 */
.emoji-panel {
  background: white;
  border-top: 1px solid #e0e0e0;
  padding: 16px;
  max-height: 150px;
  overflow-y: auto;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 8px;
}

.emoji-item {
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  text-align: center;
  transition: background 0.2s ease;
}

.emoji-item:hover {
  background: #f0f0f0;
}

/* 输入容器 */
.input-container {
  display: flex;
  padding: 16px;
  align-items: flex-end;
}

#messageInput {
  flex: 1;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  padding: 12px 16px;
  resize: none;
  outline: none;
  font-size: 14px;
  line-height: 1.4;
  max-height: 80px;
  margin-right: 12px;
  transition: border-color 0.2s ease;
}

#messageInput:focus {
  border-color: #667eea;
}

.send-btn {
  background: #667eea;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 12px 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background 0.2s ease;
}

.send-btn:hover {
  background: #5a6fd8;
}

.send-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 480px) {
  .service-window {
    width: calc(100vw - 40px);
    height: calc(100vh - 100px);
    bottom: 80px;
    right: 20px;
  }
  
  .message-content {
    max-width: 85%;
  }
  
  .emoji-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}

/* ==================== 暗色主题支持 ==================== */
@media (prefers-color-scheme: dark) {
  .service-window {
    background: #2c2c2c;
    color: white;
  }
  
  .message-list {
    background: #1e1e1e;
  }
  
  .message-text {
    background: #3c3c3c;
    color: white;
  }
  
  .message-input-area {
    background: #2c2c2c;
    border-top-color: #444;
  }
  
  #messageInput {
    background: #3c3c3c;
    color: white;
    border-color: #555;
  }
  
  .toolbar-btn:hover {
    background: #444;
  }
  
  .emoji-panel {
    background: #2c2c2c;
    border-top-color: #444;
  }
  
  .emoji-item:hover {
    background: #444;
  }
}
