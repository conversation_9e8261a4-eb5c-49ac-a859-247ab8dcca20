<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\common\controller\BaseController;
use app\common\model\CustomerService as CustomerServiceModel;
use app\common\model\User;
use think\facade\View;
use think\facade\Session;

/**
 * 管理端客服控制器
 */
class CustomerService extends BaseController
{
    /**
     * 客服管理首页
     */
    public function index()
    {
        // 获取所有对话列表
        $conversations = CustomerServiceModel::getConversations(0, 50);
        
        // 获取客服统计
        $serviceStats = CustomerServiceModel::getServiceStats();
        
        // 获取代理商客服统计
        $agentStats = $this->getAgentServiceStats();
        
        View::assign([
            'conversations' => $conversations,
            'service_stats' => $serviceStats,
            'agent_stats' => $agentStats,
            'title' => '客服管理'
        ]);
        
        return View::fetch();
    }
    
    /**
     * 获取对话消息 - API
     */
    public function getConversationMessages()
    {
        $userId = input('user_id', 0);
        $page = input('page', 1);
        $limit = input('limit', 50);
        
        if (!$userId) {
            return json(['code' => 0, 'msg' => '用户ID不能为空']);
        }
        
        $result = CustomerServiceModel::getMessages($userId, 0, $page, $limit);
        
        return json([
            'code' => 1,
            'data' => $result
        ]);
    }
    
    /**
     * 发送客服消息 - API
     */
    public function sendServiceMessage()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }
        
        $adminId = Session::get('admin_id');
        $userId = input('user_id', 0);
        $type = input('type', 1);
        $content = input('content', '');
        $mediaUrl = input('media_url', '');
        
        if (!$userId) {
            return json(['code' => 0, 'msg' => '用户ID不能为空']);
        }
        
        if (empty($content) && empty($mediaUrl)) {
            return json(['code' => 0, 'msg' => '消息内容不能为空']);
        }
        
        // 创建消息
        $messageData = [
            'user_id' => $userId,
            'sender_id' => $adminId,
            'sender_type' => CustomerServiceModel::SENDER_ADMIN,
            'type' => $type,
            'content' => $content,
            'media_url' => $mediaUrl
        ];
        
        $result = CustomerServiceModel::createMessage($messageData);
        
        if ($result['code'] === 1) {
            // 推送消息给用户
            $this->pushMessageToUser($userId, $result['data']);
        }
        
        return json($result);
    }
    
    /**
     * 标记对话为已读 - API
     */
    public function markConversationRead()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }
        
        $userId = input('user_id', 0);
        
        if (!$userId) {
            return json(['code' => 0, 'msg' => '用户ID不能为空']);
        }
        
        $result = CustomerServiceModel::markAsRead($userId, CustomerServiceModel::SENDER_ADMIN);
        
        return json([
            'code' => $result ? 1 : 0,
            'msg' => $result ? '标记成功' : '标记失败'
        ]);
    }
    
    /**
     * 获取客服统计 - API
     */
    public function getServiceStats()
    {
        $stats = CustomerServiceModel::getServiceStats();
        $agentStats = $this->getAgentServiceStats();
        
        return json([
            'code' => 1,
            'data' => [
                'service_stats' => $stats,
                'agent_stats' => $agentStats
            ]
        ]);
    }
    
    /**
     * 获取对话列表 - API
     */
    public function getConversations()
    {
        $limit = input('limit', 50);
        $agentId = input('agent_id', 0);
        
        $conversations = CustomerServiceModel::getConversations($agentId, $limit);
        
        return json([
            'code' => 1,
            'data' => $conversations
        ]);
    }
    
    /**
     * 删除对话 - API
     */
    public function deleteConversation()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }
        
        $userId = input('user_id', 0);
        $agentId = input('agent_id', 0);
        
        if (!$userId) {
            return json(['code' => 0, 'msg' => '用户ID不能为空']);
        }
        
        $result = CustomerServiceModel::deleteConversation($userId, $agentId);
        
        return json([
            'code' => $result ? 1 : 0,
            'msg' => $result ? '删除成功' : '删除失败'
        ]);
    }
    
    /**
     * 客服设置
     */
    public function settings()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();
            
            // 保存客服设置
            $result = $this->saveServiceSettings($data);
            
            return json($result);
        }
        
        // 获取当前设置
        $settings = $this->getServiceSettings();
        
        View::assign([
            'settings' => $settings,
            'title' => '客服设置'
        ]);
        
        return View::fetch();
    }
    
    /**
     * 快速回复管理
     */
    public function quickReplies()
    {
        if ($this->request->isPost()) {
            $action = input('action', '');
            
            switch ($action) {
                case 'add':
                    return $this->addQuickReply();
                case 'edit':
                    return $this->editQuickReply();
                case 'delete':
                    return $this->deleteQuickReply();
                default:
                    return json(['code' => 0, 'msg' => '无效的操作']);
            }
        }
        
        // 获取快速回复列表
        $quickReplies = $this->getQuickReplies();
        
        View::assign([
            'quick_replies' => $quickReplies,
            'title' => '快速回复管理'
        ]);
        
        return View::fetch();
    }
    
    /**
     * 客服统计报表
     */
    public function statistics()
    {
        $startDate = input('start_date', date('Y-m-d', strtotime('-30 days')));
        $endDate = input('end_date', date('Y-m-d'));
        $agentId = input('agent_id', 0);
        
        // 获取统计数据
        $stats = $this->getServiceStatistics($startDate, $endDate, $agentId);
        
        View::assign([
            'stats' => $stats,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'agent_id' => $agentId,
            'title' => '客服统计'
        ]);
        
        return View::fetch();
    }
    
    /**
     * 获取代理商客服统计
     */
    private function getAgentServiceStats(): array
    {
        // 获取所有代理商的客服统计
        $agents = User::where('user_type', 2)->field('id, username')->select();
        $agentStats = [];
        
        foreach ($agents as $agent) {
            $stats = CustomerServiceModel::getServiceStats($agent->id);
            $unreadStats = CustomerServiceModel::getAgentUnreadStats($agent->id);
            
            $agentStats[] = [
                'agent_id' => $agent->id,
                'agent_name' => $agent->username,
                'stats' => $stats,
                'unread_stats' => $unreadStats
            ];
        }
        
        return $agentStats;
    }
    
    /**
     * 推送消息给用户
     */
    private function pushMessageToUser($userId, $message)
    {
        try {
            // 使用Redis发布订阅推送消息
            $redis = new \Redis();
            $redis->connect('127.0.0.1', 6379);
            $redis->publish("user_{$userId}", json_encode($message));
            $redis->close();
        } catch (\Exception $e) {
            error_log('推送消息给用户失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取客服设置
     */
    private function getServiceSettings(): array
    {
        // 从配置文件或数据库获取设置
        return [
            'auto_reply_enabled' => true,
            'auto_reply_message' => '您好，我们已收到您的消息，客服人员会尽快回复您。',
            'service_hours' => '9:00-21:00',
            'max_file_size' => 50, // MB
            'allowed_file_types' => ['jpg', 'png', 'gif', 'mp4', 'avi'],
            'notification_enabled' => true,
            'sound_enabled' => true
        ];
    }
    
    /**
     * 保存客服设置
     */
    private function saveServiceSettings($data): array
    {
        try {
            // 保存设置到配置文件或数据库
            // 这里可以实现具体的保存逻辑
            
            return ['code' => 1, 'msg' => '设置保存成功'];
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '设置保存失败：' . $e->getMessage()];
        }
    }
    
    /**
     * 获取快速回复列表
     */
    private function getQuickReplies(): array
    {
        // 从数据库获取快速回复模板
        return [
            ['id' => 1, 'category' => 'greeting', 'content' => '您好，有什么可以帮助您的吗？'],
            ['id' => 2, 'category' => 'common', 'content' => '请稍等，我来为您查询一下'],
            ['id' => 3, 'category' => 'closing', 'content' => '祝您使用愉快！']
        ];
    }
    
    /**
     * 添加快速回复
     */
    private function addQuickReply(): array
    {
        $category = input('category', '');
        $content = input('content', '');
        
        if (empty($content)) {
            return ['code' => 0, 'msg' => '回复内容不能为空'];
        }
        
        // 保存到数据库
        try {
            // 实现添加逻辑
            return ['code' => 1, 'msg' => '添加成功'];
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '添加失败：' . $e->getMessage()];
        }
    }
    
    /**
     * 编辑快速回复
     */
    private function editQuickReply(): array
    {
        $id = input('id', 0);
        $content = input('content', '');
        
        if (!$id || empty($content)) {
            return ['code' => 0, 'msg' => '参数错误'];
        }
        
        // 更新数据库
        try {
            // 实现编辑逻辑
            return ['code' => 1, 'msg' => '编辑成功'];
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '编辑失败：' . $e->getMessage()];
        }
    }
    
    /**
     * 删除快速回复
     */
    private function deleteQuickReply(): array
    {
        $id = input('id', 0);
        
        if (!$id) {
            return ['code' => 0, 'msg' => 'ID不能为空'];
        }
        
        // 从数据库删除
        try {
            // 实现删除逻辑
            return ['code' => 1, 'msg' => '删除成功'];
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '删除失败：' . $e->getMessage()];
        }
    }
    
    /**
     * 获取客服统计数据
     */
    private function getServiceStatistics($startDate, $endDate, $agentId): array
    {
        // 实现统计数据查询
        return [
            'message_trend' => [], // 消息趋势
            'response_time' => [], // 响应时间
            'satisfaction' => [],  // 满意度
            'agent_performance' => [] // 代理商表现
        ];
    }
}
