<?php
declare(strict_types=1);

namespace app\agent\controller;

use app\BaseController;

class Index extends BaseController
{
    public function index()
    {
        return view('agent/index');
    }
    
    public function users()
    {
        return view('agent/users');
    }
    
    public function orders()
    {
        return view('agent/orders');
    }
    
    public function commissions()
    {
        return view('agent/commissions');
    }

    public function dashboard()
    {
        // 调用Dashboard控制器的index方法
        $dashboard = new \app\agent\controller\Dashboard();
        return $dashboard->index();
    }

    public function customerService()
    {
        // 调用Dashboard控制器的customerService方法
        $dashboard = new \app\agent\controller\Dashboard();
        return $dashboard->customerService();
    }
}

    public function test()
    {
        return json(['status' => 'success', 'message' => 'Index控制器工作正常']);
    }
