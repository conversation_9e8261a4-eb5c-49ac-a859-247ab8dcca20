// Service Worker for PWA
const CACHE_NAME = 'exchange-pwa-v1.0.0';
const STATIC_CACHE_NAME = 'exchange-static-v1.0.0';
const DYNAMIC_CACHE_NAME = 'exchange-dynamic-v1.0.0';

// 需要缓存的静态资源
const STATIC_ASSETS = [
  '/',
  '/static/css/style.css',
  '/static/js/common.js',
  '/static/images/logo.png',
  '/static/images/icons/icon-192x192.png',
  '/static/images/icons/icon-512x512.png',
  '/manifest.json',
  'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
  'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
  'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
  'https://code.jquery.com/jquery-3.7.1.min.js'
];

// 需要网络优先的路径
const NETWORK_FIRST_PATHS = [
  '/api/',
  '/auth/',
  '/trade',
  '/wallet',
  '/contract'
];

// 需要缓存优先的路径
const CACHE_FIRST_PATHS = [
  '/static/',
  '/images/',
  '.css',
  '.js',
  '.png',
  '.jpg',
  '.jpeg',
  '.gif',
  '.webp',
  '.svg'
];

// Service Worker 安装事件
self.addEventListener('install', event => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then(cache => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('Service Worker: Static assets cached');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('Service Worker: Failed to cache static assets', error);
      })
  );
});

// Service Worker 激活事件
self.addEventListener('activate', event => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== STATIC_CACHE_NAME && cacheName !== DYNAMIC_CACHE_NAME) {
              console.log('Service Worker: Deleting old cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activated');
        return self.clients.claim();
      })
  );
});

// 网络请求拦截
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);
  
  // 跳过非 GET 请求
  if (request.method !== 'GET') {
    return;
  }
  
  // 跳过 Chrome 扩展请求
  if (url.protocol === 'chrome-extension:') {
    return;
  }

  // API 请求 - 网络优先策略
  if (isNetworkFirst(request.url)) {
    event.respondWith(networkFirst(request));
    return;
  }

  // 静态资源 - 缓存优先策略
  if (isCacheFirst(request.url)) {
    event.respondWith(cacheFirst(request));
    return;
  }

  // 页面请求 - 网络优先，缓存后备策略
  if (request.headers.get('accept').includes('text/html')) {
    event.respondWith(networkFirstWithFallback(request));
    return;
  }

  // 默认策略 - 网络优先
  event.respondWith(networkFirst(request));
});

// 网络优先策略
async function networkFirst(request) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // 缓存成功的响应
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Network failed, trying cache:', request.url);
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // 返回离线页面或错误响应
    return new Response('网络连接失败，请检查网络设置', {
      status: 503,
      statusText: 'Service Unavailable'
    });
  }
}

// 缓存优先策略
async function cacheFirst(request) {
  const cachedResponse = await caches.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(STATIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.error('Failed to fetch resource:', request.url, error);
    return new Response('资源加载失败', {
      status: 404,
      statusText: 'Not Found'
    });
  }
}

// 网络优先，带离线后备策略
async function networkFirstWithFallback(request) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // 返回离线页面
    return caches.match('/offline.html') || new Response(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>离线模式</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
          body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
          .offline-icon { font-size: 64px; color: #ccc; margin-bottom: 20px; }
          .offline-message { font-size: 18px; color: #666; margin-bottom: 20px; }
          .retry-btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        </style>
      </head>
      <body>
        <div class="offline-icon">📱</div>
        <div class="offline-message">您当前处于离线状态</div>
        <p>请检查网络连接后重试</p>
        <button class="retry-btn" onclick="window.location.reload()">重试</button>
      </body>
      </html>
    `, {
      headers: { 'Content-Type': 'text/html' }
    });
  }
}

// 判断是否使用网络优先策略
function isNetworkFirst(url) {
  return NETWORK_FIRST_PATHS.some(path => url.includes(path));
}

// 判断是否使用缓存优先策略
function isCacheFirst(url) {
  return CACHE_FIRST_PATHS.some(path => url.includes(path));
}

// 推送通知事件
self.addEventListener('push', event => {
  console.log('Push notification received:', event);
  
  const options = {
    body: '您有新的交易通知',
    icon: '/static/images/icons/icon-192x192.png',
    badge: '/static/images/icons/badge-72x72.png',
    vibrate: [200, 100, 200],
    data: {
      url: '/'
    },
    actions: [
      {
        action: 'view',
        title: '查看详情',
        icon: '/static/images/icons/view-icon.png'
      },
      {
        action: 'close',
        title: '关闭',
        icon: '/static/images/icons/close-icon.png'
      }
    ]
  };
  
  if (event.data) {
    const data = event.data.json();
    options.body = data.message || options.body;
    options.data = data;
  }
  
  event.waitUntil(
    self.registration.showNotification('数字货币交易平台', options)
  );
});

// 通知点击事件
self.addEventListener('notificationclick', event => {
  console.log('Notification clicked:', event);
  
  event.notification.close();
  
  const action = event.action;
  const data = event.notification.data;
  
  if (action === 'close') {
    return;
  }
  
  const url = action === 'view' && data.url ? data.url : '/';
  
  event.waitUntil(
    clients.matchAll({ type: 'window' })
      .then(clientList => {
        // 如果已有窗口打开，则聚焦到该窗口
        for (const client of clientList) {
          if (client.url === url && 'focus' in client) {
            return client.focus();
          }
        }
        
        // 否则打开新窗口
        if (clients.openWindow) {
          return clients.openWindow(url);
        }
      })
  );
});

// 后台同步事件
self.addEventListener('sync', event => {
  console.log('Background sync:', event.tag);
  
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

// 执行后台同步
async function doBackgroundSync() {
  try {
    // 同步离线时的操作
    const offlineActions = await getOfflineActions();
    
    for (const action of offlineActions) {
      await syncAction(action);
    }
    
    await clearOfflineActions();
    console.log('Background sync completed');
  } catch (error) {
    console.error('Background sync failed:', error);
  }
}

// 获取离线操作
async function getOfflineActions() {
  // 从 IndexedDB 或其他存储中获取离线操作
  return [];
}

// 同步单个操作
async function syncAction(action) {
  try {
    const response = await fetch(action.url, {
      method: action.method,
      headers: action.headers,
      body: action.body
    });
    
    if (!response.ok) {
      throw new Error(`Sync failed: ${response.status}`);
    }
    
    console.log('Action synced:', action);
  } catch (error) {
    console.error('Failed to sync action:', action, error);
    throw error;
  }
}

// 清除离线操作
async function clearOfflineActions() {
  // 清除已同步的离线操作
}

// 消息事件处理
self.addEventListener('message', event => {
  console.log('Service Worker received message:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({ version: CACHE_NAME });
  }
});

// 错误处理
self.addEventListener('error', event => {
  console.error('Service Worker error:', event.error);
});

self.addEventListener('unhandledrejection', event => {
  console.error('Service Worker unhandled rejection:', event.reason);
});

console.log('Service Worker: Loaded');
