<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 客服消息模型
 */
class CustomerServiceMessage extends Model
{
    protected $table = 'ce_customer_service_messages';
    
    protected $pk = 'id';
    
    // 设置字段信息
    protected $schema = [
        'id'         => 'int',
        'ticket_id'  => 'string',
        'user_id'    => 'int',
        'type'       => 'string',
        'content'    => 'text',
        'is_read'    => 'int',
        'read_at'    => 'datetime',
        'created_at' => 'datetime'
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 关联工单
     */
    public function ticket()
    {
        return $this->belongsTo(CustomerServiceTicket::class, 'ticket_id', 'ticket_id');
    }

    /**
     * 获取类型文本
     */
    public function getTypeTextAttr($value, $data)
    {
        $typeMap = [
            'user' => '用户',
            'service' => '客服',
            'system' => '系统'
        ];
        
        return $typeMap[$data['type']] ?? '未知';
    }

    /**
     * 获取工单的消息
     */
    public static function getTicketMessages(string $ticketId): array
    {
        return self::where('ticket_id', $ticketId)
                  ->order('created_at', 'asc')
                  ->select()
                  ->toArray();
    }

    /**
     * 标记消息为已读
     */
    public static function markAsRead(string $ticketId, string $type): bool
    {
        return self::where('ticket_id', $ticketId)
                  ->where('type', $type)
                  ->where('is_read', 0)
                  ->update([
                      'is_read' => 1,
                      'read_at' => date('Y-m-d H:i:s')
                  ]);
    }

    /**
     * 获取未读消息数量
     */
    public static function getUnreadCount(int $userId, string $type = 'service'): int
    {
        // 获取用户的工单
        $ticketIds = CustomerServiceTicket::where('user_id', $userId)->column('ticket_id');
        
        if (empty($ticketIds)) {
            return 0;
        }

        return self::whereIn('ticket_id', $ticketIds)
                  ->where('type', $type)
                  ->where('is_read', 0)
                  ->count();
    }
}
