<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 合约配置模型
 */
class ContractConfig extends Model
{
    protected $table = 'ce_contract_config';
    
    // 设置字段信息
    protected $schema = [
        'id'            => 'int',
        'symbol'        => 'string',
        'time_periods'  => 'string',
        'profit_rates'  => 'string',
        'quick_amounts' => 'string',
        'min_amount'    => 'decimal',
        'max_amount'    => 'decimal',
        'fee_rate'      => 'decimal',
        'status'        => 'int',
        'created_at'    => 'datetime',
        'updated_at'    => 'datetime',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 类型转换
    protected $type = [
        'min_amount' => 'decimal:8',
        'max_amount' => 'decimal:8',
        'fee_rate'   => 'decimal:6',
        'status'     => 'integer',
    ];

    /**
     * 关联交易对
     */
    public function tradingPair()
    {
        return $this->belongsTo(TradingPair::class, 'symbol', 'symbol');
    }

    /**
     * 获取启用的配置
     */
    public static function getEnabled()
    {
        return self::where('status', 1)->order('id', 'asc')->select();
    }

    /**
     * 根据符号获取配置
     */
    public static function getBySymbol(string $symbol)
    {
        return self::where('symbol', $symbol)->where('status', 1)->find();
    }

    /**
     * 状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [0 => '禁用', 1 => '启用'];
        return $status[$data['status']] ?? '未知';
    }

    /**
     * 时间周期数组
     */
    public function getTimePeriodsArrayAttr($value, $data)
    {
        return json_decode($data['time_periods'], true) ?: [];
    }

    /**
     * 盈利比例数组
     */
    public function getProfitRatesArrayAttr($value, $data)
    {
        return json_decode($data['profit_rates'], true) ?: [];
    }

    /**
     * 快捷金额数组
     */
    public function getQuickAmountsArrayAttr($value, $data)
    {
        return json_decode($data['quick_amounts'], true) ?: [];
    }

    /**
     * 时间周期文本
     */
    public function getTimePeriodsTextAttr($value, $data)
    {
        $periods = json_decode($data['time_periods'], true) ?: [];
        $texts = [];
        
        foreach ($periods as $period) {
            if ($period < 60) {
                $texts[] = $period . '秒';
            } elseif ($period < 3600) {
                $texts[] = ($period / 60) . '分钟';
            } else {
                $texts[] = ($period / 3600) . '小时';
            }
        }
        
        return implode(', ', $texts);
    }

    /**
     * 盈利比例文本
     */
    public function getProfitRatesTextAttr($value, $data)
    {
        $rates = json_decode($data['profit_rates'], true) ?: [];
        return implode('%, ', $rates) . '%';
    }

    /**
     * 快捷金额文本
     */
    public function getQuickAmountsTextAttr($value, $data)
    {
        $amounts = json_decode($data['quick_amounts'], true) ?: [];
        return implode(', ', $amounts) . ' USDT';
    }

    /**
     * 格式化最小金额
     */
    public function getMinAmountFormatAttr($value, $data)
    {
        return number_format($data['min_amount'], 2, '.', '');
    }

    /**
     * 格式化最大金额
     */
    public function getMaxAmountFormatAttr($value, $data)
    {
        return number_format($data['max_amount'], 2, '.', '');
    }

    /**
     * 手续费率百分比
     */
    public function getFeeRatePercentAttr($value, $data)
    {
        return number_format($data['fee_rate'] * 100, 2) . '%';
    }

    /**
     * 验证时间周期
     */
    public function validateTimePeriod(int $period): bool
    {
        $periods = $this->time_periods_array;
        return in_array($period, $periods);
    }

    /**
     * 获取盈利比例
     */
    public function getProfitRate(int $period): float
    {
        $periods = $this->time_periods_array;
        $rates = $this->profit_rates_array;
        
        $index = array_search($period, $periods);
        if ($index !== false && isset($rates[$index])) {
            return $rates[$index] / 100; // 转换为小数
        }
        
        return 0.75; // 默认75%
    }

    /**
     * 验证交易金额
     */
    public function validateAmount(float $amount): array
    {
        if ($amount < $this->min_amount) {
            return ['valid' => false, 'msg' => "最小交易金额为 {$this->min_amount_format} USDT"];
        }
        
        if ($amount > $this->max_amount) {
            return ['valid' => false, 'msg' => "最大交易金额为 {$this->max_amount_format} USDT"];
        }
        
        return ['valid' => true, 'msg' => ''];
    }

    /**
     * 计算手续费
     */
    public function calculateFee(float $amount): float
    {
        return $amount * $this->fee_rate;
    }

    /**
     * 计算盈利金额
     */
    public function calculateProfit(float $amount, int $period): float
    {
        $profitRate = $this->getProfitRate($period);
        return $amount * $profitRate;
    }

    /**
     * 获取配置摘要
     */
    public function getSummary(): array
    {
        return [
            'symbol' => $this->symbol,
            'periods' => $this->time_periods_array,
            'rates' => $this->profit_rates_array,
            'amounts' => $this->quick_amounts_array,
            'min_amount' => $this->min_amount,
            'max_amount' => $this->max_amount,
            'fee_rate' => $this->fee_rate
        ];
    }
}
