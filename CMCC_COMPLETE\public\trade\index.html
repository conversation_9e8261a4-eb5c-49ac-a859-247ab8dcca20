<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>币币交易 - GVD交易平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0b0e11;
            color: #ffffff;
            overflow: hidden;
        }

        .header {
            background: #1e2329;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            border-bottom: 1px solid #2b3139;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #f0b90b;
        }

        .nav-menu {
            display: flex;
            gap: 30px;
        }

        .nav-menu a {
            color: #848e9c;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-menu a:hover,
        .nav-menu a.active {
            color: #f0b90b;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-balance {
            font-size: 14px;
            color: #848e9c;
        }

        .user-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            font-weight: 500;
        }

        .btn-primary {
            background: #f0b90b;
            color: #000;
        }

        .btn-outline {
            background: transparent;
            color: #f0b90b;
            border: 1px solid #f0b90b;
        }

        .btn:hover {
            opacity: 0.8;
        }

        .main-container {
            display: grid;
            grid-template-columns: 1fr 300px 300px;
            grid-template-rows: 1fr 200px;
            height: calc(100vh - 60px);
            gap: 1px;
            background: #2b3139;
        }

        .chart-section {
            background: #1e2329;
            grid-row: 1 / 3;
            position: relative;
        }

        .orderbook-section {
            background: #1e2329;
            display: flex;
            flex-direction: column;
        }

        .trading-section {
            background: #1e2329;
            display: flex;
            flex-direction: column;
        }

        .trades-section {
            background: #1e2329;
            grid-column: 2 / 4;
        }

        .section-header {
            padding: 15px 20px;
            border-bottom: 1px solid #2b3139;
            font-weight: 600;
            font-size: 14px;
            color: #848e9c;
        }

        .section-content {
            flex: 1;
            padding: 15px 20px;
            overflow-y: auto;
        }

        /* 图表区域 */
        .chart-header {
            padding: 15px 20px;
            border-bottom: 1px solid #2b3139;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .symbol-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .symbol-name {
            font-size: 18px;
            font-weight: bold;
            color: #ffffff;
        }

        .price-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .current-price {
            font-size: 24px;
            font-weight: bold;
            color: #0ecb81;
        }

        .price-change {
            font-size: 14px;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .price-change.positive {
            background: rgba(14, 203, 129, 0.2);
            color: #0ecb81;
        }

        .price-change.negative {
            background: rgba(246, 70, 93, 0.2);
            color: #f6465d;
        }

        .chart-controls {
            display: flex;
            gap: 10px;
        }

        .interval-btn {
            padding: 6px 12px;
            background: transparent;
            border: 1px solid #2b3139;
            color: #848e9c;
            cursor: pointer;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.3s;
        }

        .interval-btn:hover,
        .interval-btn.active {
            background: #f0b90b;
            color: #000;
            border-color: #f0b90b;
        }

        .chart-iframe {
            width: 100%;
            height: calc(100% - 60px);
            border: none;
        }

        /* 订单簿 */
        .orderbook-table {
            width: 100%;
            font-size: 12px;
        }

        .orderbook-table th,
        .orderbook-table td {
            padding: 4px 8px;
            text-align: right;
        }

        .orderbook-table th {
            color: #848e9c;
            font-weight: 500;
            border-bottom: 1px solid #2b3139;
        }

        .sell-order {
            color: #f6465d;
        }

        .buy-order {
            color: #0ecb81;
        }

        .spread-info {
            padding: 10px;
            text-align: center;
            background: #2b3139;
            margin: 10px 0;
            border-radius: 4px;
            font-size: 12px;
            color: #848e9c;
        }

        /* 交易面板 */
        .trading-tabs {
            display: flex;
            border-bottom: 1px solid #2b3139;
        }

        .tab-btn {
            flex: 1;
            padding: 12px;
            background: transparent;
            border: none;
            color: #848e9c;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .tab-btn.active {
            color: #0ecb81;
            border-bottom: 2px solid #0ecb81;
        }

        .tab-btn.sell.active {
            color: #f6465d;
            border-bottom-color: #f6465d;
        }

        .trading-form {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
            color: #848e9c;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            background: #2b3139;
            border: 1px solid #3c4043;
            border-radius: 4px;
            color: #ffffff;
            font-size: 14px;
        }

        .form-group input:focus {
            outline: none;
            border-color: #f0b90b;
        }

        .percentage-buttons {
            display: flex;
            gap: 5px;
            margin-top: 5px;
        }

        .percentage-btn {
            flex: 1;
            padding: 6px;
            background: transparent;
            border: 1px solid #3c4043;
            color: #848e9c;
            cursor: pointer;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.3s;
        }

        .percentage-btn:hover {
            border-color: #f0b90b;
            color: #f0b90b;
        }

        .trade-btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .trade-btn.buy {
            background: #0ecb81;
            color: #ffffff;
        }

        .trade-btn.sell {
            background: #f6465d;
            color: #ffffff;
        }

        .trade-btn:hover {
            opacity: 0.8;
        }

        .trade-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 最新成交 */
        .trades-table {
            width: 100%;
            font-size: 12px;
        }

        .trades-table th,
        .trades-table td {
            padding: 4px 8px;
            text-align: right;
        }

        .trades-table th {
            color: #848e9c;
            font-weight: 500;
            border-bottom: 1px solid #2b3139;
        }

        .trade-buy {
            color: #0ecb81;
        }

        .trade-sell {
            color: #f6465d;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-container {
                grid-template-columns: 1fr 250px 250px;
            }
        }

        @media (max-width: 768px) {
            .main-container {
                grid-template-columns: 1fr;
                grid-template-rows: 300px 1fr;
            }
            
            .orderbook-section,
            .trading-section {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">GVD</div>
        <nav class="nav-menu">
            <a href="/trade/index.html" class="active">币币交易</a>
            <a href="/trade/futures.html">合约交易</a>
            <a href="/user/dashboard.html">资产</a>
            <a href="/user/orders.html">订单</a>
        </nav>
        <div class="user-info">
            <div class="user-balance">
                <span>USDT: </span>
                <span id="usdtBalance">0.00</span>
            </div>
            <div class="user-actions">
                <button class="btn btn-outline" onclick="showDepositModal()">充值</button>
                <button class="btn btn-primary" onclick="showWithdrawModal()">提现</button>
                <button class="btn btn-outline" onclick="logout()">退出</button>
            </div>
        </div>
    </div>

    <div class="main-container">
        <!-- 图表区域 -->
        <div class="chart-section">
            <div class="chart-header">
                <div class="symbol-info">
                    <div class="symbol-name" id="symbolName">BTC/USDT</div>
                    <div class="price-info">
                        <div class="current-price" id="currentPrice">$45,000.00</div>
                        <div class="price-change positive" id="priceChange">+2.5%</div>
                    </div>
                </div>
                <div class="chart-controls">
                    <button class="interval-btn active" data-interval="1m">1分</button>
                    <button class="interval-btn" data-interval="5m">5分</button>
                    <button class="interval-btn" data-interval="15m">15分</button>
                    <button class="interval-btn" data-interval="1h">1小时</button>
                    <button class="interval-btn" data-interval="4h">4小时</button>
                    <button class="interval-btn" data-interval="1d">1天</button>
                </div>
            </div>
            <iframe class="chart-iframe" src="/trade/chart.html?symbol=BTCUSDT" id="chartFrame"></iframe>
        </div>

        <!-- 订单簿 -->
        <div class="orderbook-section">
            <div class="section-header">订单簿</div>
            <div class="section-content">
                <table class="orderbook-table">
                    <thead>
                        <tr>
                            <th>价格(USDT)</th>
                            <th>数量(BTC)</th>
                            <th>累计</th>
                        </tr>
                    </thead>
                    <tbody id="sellOrders">
                        <!-- 卖单数据 -->
                    </tbody>
                </table>
                <div class="spread-info">
                    <div>价差: <span id="spread">0.01</span></div>
                </div>
                <table class="orderbook-table">
                    <tbody id="buyOrders">
                        <!-- 买单数据 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 交易面板 -->
        <div class="trading-section">
            <div class="trading-tabs">
                <button class="tab-btn active" id="buyTab" onclick="switchTab('buy')">买入</button>
                <button class="tab-btn sell" id="sellTab" onclick="switchTab('sell')">卖出</button>
            </div>
            <div class="trading-form">
                <div class="form-group">
                    <label>价格 (USDT)</label>
                    <input type="number" id="tradePrice" placeholder="0.00" step="0.01">
                </div>
                <div class="form-group">
                    <label>数量 (BTC)</label>
                    <input type="number" id="tradeAmount" placeholder="0.00" step="0.001">
                    <div class="percentage-buttons">
                        <button class="percentage-btn" onclick="setPercentage(25)">25%</button>
                        <button class="percentage-btn" onclick="setPercentage(50)">50%</button>
                        <button class="percentage-btn" onclick="setPercentage(75)">75%</button>
                        <button class="percentage-btn" onclick="setPercentage(100)">100%</button>
                    </div>
                </div>
                <div class="form-group">
                    <label>总额 (USDT)</label>
                    <input type="number" id="tradeTotal" placeholder="0.00" readonly>
                </div>
                <button class="trade-btn buy" id="tradeBtn" onclick="submitOrder()">买入 BTC</button>
            </div>
        </div>

        <!-- 最新成交 -->
        <div class="trades-section">
            <div class="section-header">最新成交</div>
            <div class="section-content">
                <table class="trades-table">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>价格(USDT)</th>
                            <th>数量(BTC)</th>
                        </tr>
                    </thead>
                    <tbody id="latestTrades">
                        <!-- 最新成交数据 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="/js/trading.js"></script>
</body>
</html>
