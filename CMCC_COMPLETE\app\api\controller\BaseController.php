<?php
declare (strict_types = 1);

namespace app\api\controller;

use think\App;
use think\Request;
use think\exception\ValidateException;
use think\Validate;

/**
 * API基础控制器
 * 兼容ThinkPHP 6.1
 */
abstract class BaseController
{
    /**
     * Request实例
     * @var Request
     */
    protected $request;

    /**
     * 应用实例
     * @var App
     */
    protected $app;

    /**
     * 当前用户ID
     * @var int
     */
    protected $userId = 0;

    /**
     * 当前用户信息
     * @var array
     */
    protected $user = [];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        $this->app     = $app;
        $this->request = $this->app->request;

        // 控制器初始化
        $this->initialize();
    }

    // 初始化
    protected function initialize()
    {
        // 获取用户信息
        if (isset($this->request->user_id)) {
            $this->userId = $this->request->user_id;
            $this->user = $this->request->user;
        }
    }

    /**
     * 获取当前用户ID
     */
    protected function getUserId(): int
    {
        return $this->userId;
    }

    /**
     * 获取当前用户信息
     */
    protected function getUser(): array
    {
        return $this->user;
    }

    /**
     * 成功响应
     */
    protected function success($data = [], string $message = 'success', int $code = 1)
    {
        return json([
            'code' => $code,
            'msg' => $message,
            'data' => $data,
            'timestamp' => time()
        ]);
    }

    /**
     * 错误响应
     */
    protected function error(string $message = 'error', int $httpCode = 400, $data = [], int $code = 0)
    {
        return json([
            'code' => $code,
            'msg' => $message,
            'data' => $data,
            'timestamp' => time()
        ])->code($httpCode);
    }

    /**
     * 参数验证
     */
    protected function validate(array $data, $validate, array $message = [], bool $batch = false)
    {
        if (is_array($validate)) {
            $v = new Validate();
            $v->rule($validate);
        } else {
            if (strpos($validate, '.')) {
                // 支持场景
                [$validate, $scene] = explode('.', $validate);
            }
            $class = false !== strpos($validate, '\\') ? $validate : $this->app->parseClass('validate', $validate);
            $v     = new $class();
            if (!empty($scene)) {
                $v->scene($scene);
            }
        }

        if (!empty($message)) {
            $v->message($message);
        }

        if ($batch || $this->batchValidate) {
            $v->batch(true);
        }

        return $v->failException(true)->check($data);
    }

    /**
     * 获取客户端IP
     */
    protected function getClientIp(): string
    {
        return $this->request->ip();
    }

    /**
     * 获取请求参数
     */
    protected function getParams(array $rules = []): array
    {
        $params = $this->request->param();
        
        if (!empty($rules)) {
            $filteredParams = [];
            foreach ($rules as $key => $rule) {
                if (is_numeric($key)) {
                    $filteredParams[$rule] = $params[$rule] ?? null;
                } else {
                    $filteredParams[$key] = $params[$key] ?? $rule;
                }
            }
            return $filteredParams;
        }
        
        return $params;
    }

    /**
     * 分页参数处理
     */
    protected function getPaginationParams(): array
    {
        $page = $this->request->param('page/d', 1);
        $limit = $this->request->param('limit/d', 20);
        
        // 限制每页最大数量
        $limit = min($limit, 100);
        
        return [
            'page' => max(1, $page),
            'limit' => max(1, $limit),
            'offset' => ($page - 1) * $limit
        ];
    }
}
