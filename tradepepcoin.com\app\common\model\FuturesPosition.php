<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 期货持仓模型
 */
class FuturesPosition extends Model
{
    protected $name = 'gvd_futures_positions';
    
    protected $type = [
        'size' => 'float',
        'entry_price' => 'float',
        'mark_price' => 'float',
        'liquidation_price' => 'float',
        'margin' => 'float',
        'unrealized_pnl' => 'float',
        'realized_pnl' => 'float'
    ];

    // 持仓方向
    const SIDE_LONG = 1;  // 多头
    const SIDE_SHORT = 2; // 空头

    /**
     * 获取持仓方向文本
     */
    public function getSideTextAttr($value, $data): string
    {
        $sides = [
            self::SIDE_LONG => '多头',
            self::SIDE_SHORT => '空头'
        ];
        return $sides[$data['side']] ?? '未知';
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联合约
     */
    public function contract()
    {
        return $this->belongsTo(FuturesContract::class, 'symbol', 'symbol');
    }

    /**
     * 计算未实现盈亏
     */
    public function calculateUnrealizedPnl(float $markPrice): float
    {
        if ($this->size <= 0) {
            return 0;
        }

        $contract = $this->contract;
        if (!$contract) {
            return 0;
        }

        $contractSize = $contract->contract_size;
        $priceDiff = $markPrice - $this->entry_price;
        
        if ($this->side == self::SIDE_SHORT) {
            $priceDiff = -$priceDiff; // 空头方向相反
        }

        return $priceDiff * $this->size * $contractSize;
    }

    /**
     * 计算强平价格
     */
    public function calculateLiquidationPrice(): float
    {
        $contract = $this->contract;
        if (!$contract || $this->size <= 0) {
            return 0;
        }

        $maintenanceMargin = $contract->calculateMaintenanceMargin($this->size, $this->entry_price);
        $contractSize = $contract->contract_size;
        
        if ($this->side == self::SIDE_LONG) {
            // 多头强平价格 = 开仓价格 - (保证金 - 维持保证金) / (数量 * 合约面值)
            $liquidationPrice = $this->entry_price - (($this->margin - $maintenanceMargin) / ($this->size * $contractSize));
        } else {
            // 空头强平价格 = 开仓价格 + (保证金 - 维持保证金) / (数量 * 合约面值)
            $liquidationPrice = $this->entry_price + (($this->margin - $maintenanceMargin) / ($this->size * $contractSize));
        }

        return max(0, $liquidationPrice);
    }

    /**
     * 计算保证金率
     */
    public function calculateMarginRatio(float $markPrice): float
    {
        $unrealizedPnl = $this->calculateUnrealizedPnl($markPrice);
        $equity = $this->margin + $unrealizedPnl;
        
        $contract = $this->contract;
        if (!$contract) {
            return 0;
        }
        
        $notionalValue = $this->size * $markPrice * $contract->contract_size;

        if ($notionalValue <= 0) {
            return 0;
        }

        return ($equity / $notionalValue) * 100;
    }

    /**
     * 检查是否需要强平
     */
    public function shouldLiquidate(float $markPrice): bool
    {
        $contract = $this->contract;
        if (!$contract) {
            return false;
        }

        $marginRatio = $this->calculateMarginRatio($markPrice);
        $maintenanceMarginRate = $contract->maintenance_margin_rate * 100;

        return $marginRatio <= $maintenanceMarginRate;
    }

    /**
     * 更新持仓
     */
    public function updatePosition(float $size, float $price, float $margin): void
    {
        if ($this->size == 0) {
            // 新开仓
            $this->size = $size;
            $this->entry_price = $price;
            $this->margin = $margin;
        } else {
            // 加仓或减仓
            $totalMargin = $this->margin + $margin;
            $totalSize = $this->size + $size;
            
            if ($totalSize > 0) {
                // 计算新的平均开仓价
                $totalValue = ($this->size * $this->entry_price) + ($size * $price);
                $this->entry_price = $totalValue / $totalSize;
                $this->size = $totalSize;
                $this->margin = $totalMargin;
            } else {
                // 完全平仓
                $this->size = 0;
                $this->entry_price = 0;
                $this->margin = 0;
            }
        }

        // 重新计算强平价格
        $this->liquidation_price = $this->calculateLiquidationPrice();
        $this->updated_at = date('Y-m-d H:i:s');
        $this->save();
    }

    /**
     * 平仓
     */
    public function closePosition(float $closeSize, float $closePrice): array
    {
        try {
            if ($closeSize > $this->size) {
                return ['code' => 0, 'msg' => '平仓数量超过持仓数量'];
            }

            // 计算平仓盈亏
            $contract = $this->contract;
            if (!$contract) {
                return ['code' => 0, 'msg' => '合约不存在'];
            }

            $contractSize = $contract->contract_size;
            $priceDiff = $closePrice - $this->entry_price;
            
            if ($this->side == self::SIDE_SHORT) {
                $priceDiff = -$priceDiff;
            }

            $closePnl = $priceDiff * $closeSize * $contractSize;
            
            // 计算释放的保证金
            $marginRatio = $closeSize / $this->size;
            $releasedMargin = $this->margin * $marginRatio;

            // 更新持仓
            $this->size -= $closeSize;
            $this->margin -= $releasedMargin;
            $this->realized_pnl += $closePnl;

            if ($this->size <= 0) {
                // 完全平仓，清零持仓
                $this->size = 0;
                $this->entry_price = 0;
                $this->margin = 0;
                $this->liquidation_price = 0;
            } else {
                // 部分平仓，重新计算强平价格
                $this->liquidation_price = $this->calculateLiquidationPrice();
            }

            $this->updated_at = date('Y-m-d H:i:s');
            $this->save();

            return [
                'code' => 1,
                'msg' => '平仓成功',
                'data' => [
                    'close_size' => $closeSize,
                    'close_price' => $closePrice,
                    'pnl' => $closePnl,
                    'released_margin' => $releasedMargin,
                    'remaining_size' => $this->size
                ]
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '平仓失败：' . $e->getMessage()];
        }
    }

    /**
     * 强制平仓
     */
    public function liquidate(float $liquidationPrice): bool
    {
        try {
            // 计算强平盈亏
            $liquidationPnl = $this->calculateUnrealizedPnl($liquidationPrice);
            
            $this->realized_pnl += $liquidationPnl;
            $this->size = 0;
            $this->entry_price = 0;
            $this->margin = 0;
            $this->liquidation_price = 0;
            $this->updated_at = date('Y-m-d H:i:s');

            return $this->save();

        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取用户的期货持仓
     */
    public static function getUserPositions(int $userId, string $symbol = ''): array
    {
        $query = self::where('user_id', $userId)
                    ->where('size', '>', 0);
        
        if (!empty($symbol)) {
            $query->where('symbol', $symbol);
        }
        
        return $query->with(['contract'])
                    ->order('created_at', 'desc')
                    ->select()
                    ->toArray();
    }

    /**
     * 获取或创建持仓
     */
    public static function getOrCreatePosition(int $userId, string $symbol, int $side): FuturesPosition
    {
        $position = self::where([
            'user_id' => $userId,
            'symbol' => $symbol,
            'side' => $side
        ])->find();

        if (!$position) {
            $position = self::create([
                'user_id' => $userId,
                'symbol' => $symbol,
                'side' => $side,
                'size' => 0,
                'entry_price' => 0,
                'mark_price' => 0,
                'liquidation_price' => 0,
                'margin' => 0,
                'unrealized_pnl' => 0,
                'realized_pnl' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }

        return $position;
    }

    /**
     * 获取需要强平的持仓
     */
    public static function getLiquidationPositions(string $symbol, float $markPrice): array
    {
        $positions = self::where('symbol', $symbol)
                        ->where('size', '>', 0)
                        ->with(['contract'])
                        ->select();

        $liquidationPositions = [];
        foreach ($positions as $position) {
            if ($position->shouldLiquidate($markPrice)) {
                $liquidationPositions[] = $position;
            }
        }

        return $liquidationPositions;
    }

    /**
     * 更新标记价格和未实现盈亏
     */
    public function updateMarkPrice(float $markPrice): void
    {
        $this->mark_price = $markPrice;
        $this->unrealized_pnl = $this->calculateUnrealizedPnl($markPrice);
        $this->updated_at = date('Y-m-d H:i:s');
        $this->save();
    }

    /**
     * 获取持仓统计
     */
    public static function getPositionStats(int $userId): array
    {
        $stats = self::where('user_id', $userId)
                    ->field([
                        'COUNT(*) as total_positions',
                        'SUM(CASE WHEN side = 1 THEN 1 ELSE 0 END) as long_positions',
                        'SUM(CASE WHEN side = 2 THEN 1 ELSE 0 END) as short_positions',
                        'SUM(margin) as total_margin',
                        'SUM(unrealized_pnl) as total_unrealized_pnl',
                        'SUM(realized_pnl) as total_realized_pnl'
                    ])
                    ->find();

        if (!$stats) {
            return [
                'total_positions' => 0,
                'long_positions' => 0,
                'short_positions' => 0,
                'total_margin' => 0,
                'total_unrealized_pnl' => 0,
                'total_realized_pnl' => 0,
                'total_pnl' => 0
            ];
        }

        $totalPnl = $stats['total_unrealized_pnl'] + $stats['total_realized_pnl'];

        return [
            'total_positions' => $stats['total_positions'],
            'long_positions' => $stats['long_positions'],
            'short_positions' => $stats['short_positions'],
            'total_margin' => $stats['total_margin'],
            'total_unrealized_pnl' => $stats['total_unrealized_pnl'],
            'total_realized_pnl' => $stats['total_realized_pnl'],
            'total_pnl' => $totalPnl
        ];
    }
}
