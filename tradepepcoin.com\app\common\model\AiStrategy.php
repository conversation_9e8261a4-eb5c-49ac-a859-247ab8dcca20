<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * AI策略模型
 */
class AiStrategy extends Model
{
    protected $table = 'ai_strategies';

    // 设置字段信息
    protected $schema = [
        'id' => 'int',
        'user_id' => 'int',
        'strategy_id' => 'string',
        'name' => 'string',
        'description' => 'text',
        'type' => 'string',
        'symbols' => 'json',
        'risk_level' => 'int',
        'investment_amount' => 'decimal',
        'parameters' => 'json',
        'ml_model' => 'string',
        'model_version' => 'string',
        'training_data' => 'json',
        'performance_metrics' => 'json',
        'backtest_results' => 'json',
        'live_performance' => 'json',
        'total_return' => 'decimal',
        'max_drawdown' => 'decimal',
        'sharpe_ratio' => 'decimal',
        'win_rate' => 'decimal',
        'confidence_score' => 'decimal',
        'last_prediction' => 'json',
        'prediction_accuracy' => 'decimal',
        'status' => 'int',
        'is_active' => 'int',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'trained_at' => 'datetime',
        'last_executed_at' => 'datetime'
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // JSON字段
    protected $json = ['symbols', 'parameters', 'training_data', 'performance_metrics', 'backtest_results', 'live_performance', 'last_prediction'];

    // 策略状态
    const STATUS_DRAFT = 0;          // 草稿
    const STATUS_TRAINING = 1;       // 训练中
    const STATUS_BACKTESTING = 2;    // 回测中
    const STATUS_READY = 3;          // 就绪
    const STATUS_RUNNING = 4;        // 运行中
    const STATUS_PAUSED = 5;         // 暂停
    const STATUS_STOPPED = 6;        // 停止
    const STATUS_ERROR = 7;          // 错误

    // 策略类型
    const TYPE_TREND_FOLLOWING = 'trend_following';
    const TYPE_MEAN_REVERSION = 'mean_reversion';
    const TYPE_MOMENTUM = 'momentum';
    const TYPE_ARBITRAGE = 'arbitrage';
    const TYPE_SENTIMENT = 'sentiment';
    const TYPE_NEURAL_NETWORK = 'neural_network';

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联AI交易记录
     */
    public function aiTrades()
    {
        return $this->hasMany(AiTrade::class, 'strategy_id', 'strategy_id');
    }

    /**
     * 关联预测记录
     */
    public function predictions()
    {
        return $this->hasMany(AiPrediction::class, 'strategy_id', 'strategy_id');
    }

    /**
     * 启动策略
     */
    public function start(): bool
    {
        if ($this->status !== self::STATUS_READY) {
            return false;
        }

        $this->status = self::STATUS_RUNNING;
        $this->is_active = 1;
        $this->last_executed_at = date('Y-m-d H:i:s');

        return $this->save();
    }

    /**
     * 暂停策略
     */
    public function pause(): bool
    {
        if ($this->status !== self::STATUS_RUNNING) {
            return false;
        }

        $this->status = self::STATUS_PAUSED;
        $this->is_active = 0;

        return $this->save();
    }

    /**
     * 停止策略
     */
    public function stop(): bool
    {
        if (!in_array($this->status, [self::STATUS_RUNNING, self::STATUS_PAUSED])) {
            return false;
        }

        $this->status = self::STATUS_STOPPED;
        $this->is_active = 0;

        return $this->save();
    }

    /**
     * 更新模型训练状态
     */
    public function updateTrainingStatus(string $status, array $metrics = []): bool
    {
        switch ($status) {
            case 'completed':
                $this->status = self::STATUS_READY;
                $this->trained_at = date('Y-m-d H:i:s');
                break;
            case 'failed':
                $this->status = self::STATUS_ERROR;
                break;
            case 'training':
                $this->status = self::STATUS_TRAINING;
                break;
        }

        if (!empty($metrics)) {
            $this->performance_metrics = array_merge($this->performance_metrics ?: [], $metrics);
        }

        return $this->save();
    }

    /**
     * 更新回测结果
     */
    public function updateBacktestResults(array $results): bool
    {
        $this->backtest_results = $results;
        $this->total_return = $results['total_return'] ?? 0;
        $this->max_drawdown = $results['max_drawdown'] ?? 0;
        $this->sharpe_ratio = $results['sharpe_ratio'] ?? 0;
        $this->win_rate = $results['win_rate'] ?? 0;

        return $this->save();
    }

    /**
     * 更新实时表现
     */
    public function updateLivePerformance(array $performance): bool
    {
        $livePerformance = $this->live_performance ?: [];
        $livePerformance[] = array_merge($performance, [
            'timestamp' => time(),
            'date' => date('Y-m-d H:i:s')
        ]);

        // 保留最近1000条记录
        if (count($livePerformance) > 1000) {
            $livePerformance = array_slice($livePerformance, -1000);
        }

        $this->live_performance = $livePerformance;

        // 更新汇总指标
        $this->updateSummaryMetrics();

        return $this->save();
    }

    /**
     * 更新预测记录
     */
    public function updatePrediction(array $prediction): bool
    {
        $this->last_prediction = array_merge($prediction, [
            'timestamp' => time(),
            'created_at' => date('Y-m-d H:i:s')
        ]);

        $this->confidence_score = $prediction['confidence'] ?? 0;

        return $this->save();
    }

    /**
     * 更新预测准确率
     */
    public function updatePredictionAccuracy(): bool
    {
        // 获取最近的预测记录
        $predictions = AiPrediction::where('strategy_id', $this->strategy_id)
                                 ->where('created_at', '>=', date('Y-m-d H:i:s', strtotime('-30 days')))
                                 ->select();

        if ($predictions->isEmpty()) {
            return true;
        }

        $correctPredictions = 0;
        $totalPredictions = count($predictions);

        foreach ($predictions as $prediction) {
            if ($prediction->is_accurate) {
                $correctPredictions++;
            }
        }

        $this->prediction_accuracy = ($correctPredictions / $totalPredictions) * 100;

        return $this->save();
    }

    /**
     * 更新汇总指标
     */
    private function updateSummaryMetrics(): void
    {
        if (empty($this->live_performance)) {
            return;
        }

        $performance = $this->live_performance;
        $returns = array_column($performance, 'return');
        $equity = array_column($performance, 'equity');

        // 计算总收益率
        if (!empty($equity)) {
            $initialEquity = reset($equity);
            $currentEquity = end($equity);
            $this->total_return = (($currentEquity - $initialEquity) / $initialEquity) * 100;
        }

        // 计算最大回撤
        $this->max_drawdown = $this->calculateMaxDrawdown($equity);

        // 计算夏普比率
        $this->sharpe_ratio = $this->calculateSharpeRatio($returns);

        // 计算胜率
        $this->win_rate = $this->calculateWinRate($returns);
    }

    /**
     * 计算最大回撤
     */
    private function calculateMaxDrawdown(array $equity): float
    {
        if (empty($equity)) {
            return 0;
        }

        $maxDrawdown = 0;
        $peak = $equity[0];

        foreach ($equity as $value) {
            if ($value > $peak) {
                $peak = $value;
            }

            $drawdown = (($peak - $value) / $peak) * 100;
            if ($drawdown > $maxDrawdown) {
                $maxDrawdown = $drawdown;
            }
        }

        return $maxDrawdown;
    }

    /**
     * 计算夏普比率
     */
    private function calculateSharpeRatio(array $returns): float
    {
        if (count($returns) < 2) {
            return 0;
        }

        $avgReturn = array_sum($returns) / count($returns);
        $variance = 0;

        foreach ($returns as $return) {
            $variance += pow($return - $avgReturn, 2);
        }

        $stdDev = sqrt($variance / count($returns));

        if ($stdDev == 0) {
            return 0;
        }

        // 假设无风险利率为0
        return $avgReturn / $stdDev;
    }

    /**
     * 计算胜率
     */
    private function calculateWinRate(array $returns): float
    {
        if (empty($returns)) {
            return 0;
        }

        $winningTrades = 0;
        foreach ($returns as $return) {
            if ($return > 0) {
                $winningTrades++;
            }
        }

        return ($winningTrades / count($returns)) * 100;
    }

    /**
     * 获取策略表现摘要
     */
    public function getPerformanceSummary(): array
    {
        return [
            'total_return' => $this->total_return,
            'max_drawdown' => $this->max_drawdown,
            'sharpe_ratio' => $this->sharpe_ratio,
            'win_rate' => $this->win_rate,
            'confidence_score' => $this->confidence_score,
            'prediction_accuracy' => $this->prediction_accuracy,
            'total_trades' => count($this->live_performance ?: []),
            'days_running' => $this->getDaysRunning(),
            'last_prediction' => $this->last_prediction
        ];
    }

    /**
     * 获取运行天数
     */
    private function getDaysRunning(): int
    {
        if (!$this->trained_at) {
            return 0;
        }

        $trainedDate = new \DateTime($this->trained_at);
        $currentDate = new \DateTime();
        
        return $currentDate->diff($trainedDate)->days;
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr(): string
    {
        $statusTexts = [
            self::STATUS_DRAFT => '草稿',
            self::STATUS_TRAINING => '训练中',
            self::STATUS_BACKTESTING => '回测中',
            self::STATUS_READY => '就绪',
            self::STATUS_RUNNING => '运行中',
            self::STATUS_PAUSED => '暂停',
            self::STATUS_STOPPED => '停止',
            self::STATUS_ERROR => '错误'
        ];

        return $statusTexts[$this->status] ?? '未知状态';
    }

    /**
     * 获取类型文本
     */
    public function getTypeTextAttr(): string
    {
        $typeTexts = [
            self::TYPE_TREND_FOLLOWING => '趋势跟踪',
            self::TYPE_MEAN_REVERSION => '均值回归',
            self::TYPE_MOMENTUM => '动量策略',
            self::TYPE_ARBITRAGE => '套利策略',
            self::TYPE_SENTIMENT => '情绪分析',
            self::TYPE_NEURAL_NETWORK => '神经网络'
        ];

        return $typeTexts[$this->type] ?? '未知类型';
    }

    /**
     * 获取风险等级文本
     */
    public function getRiskLevelTextAttr(): string
    {
        $riskTexts = [
            1 => '保守型',
            2 => '稳健型',
            3 => '激进型'
        ];

        return $riskTexts[$this->risk_level] ?? '未知';
    }

    /**
     * 搜索器：按用户
     */
    public function searchUserIdAttr($query, $value)
    {
        if ($value) {
            $query->where('user_id', $value);
        }
    }

    /**
     * 搜索器：按类型
     */
    public function searchTypeAttr($query, $value)
    {
        if ($value) {
            $query->where('type', $value);
        }
    }

    /**
     * 搜索器：按状态
     */
    public function searchStatusAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('status', $value);
        }
    }

    /**
     * 搜索器：按活跃状态
     */
    public function searchIsActiveAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('is_active', $value);
        }
    }

    /**
     * 搜索器：按关键词
     */
    public function searchKeywordAttr($query, $value)
    {
        if ($value) {
            $query->where('name|description', 'like', "%{$value}%");
        }
    }
}
