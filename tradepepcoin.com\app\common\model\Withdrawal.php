<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 提币模型
 */
class Withdrawal extends Model
{
    protected $table = 'ce_withdrawals'; // 假设表名为ce_withdrawals
    
    protected $pk = 'id';
    
    protected $autoWriteTimestamp = false;
    
    // 状态常量
    const STATUS_PENDING = 1;   // 待审核
    const STATUS_APPROVED = 2;  // 已通过
    const STATUS_REJECTED = 3;  // 已驳回
    const STATUS_PROCESSING = 4; // 处理中
    const STATUS_COMPLETED = 5;  // 已完成
    const STATUS_FAILED = 6;     // 失败
    
    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
    
    /**
     * 关联处理人
     */
    public function processor()
    {
        return $this->belongsTo(User::class, 'processor_id', 'id');
    }
    
    /**
     * 状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            self::STATUS_PENDING => '待审核',
            self::STATUS_APPROVED => '已通过',
            self::STATUS_REJECTED => '已驳回',
            self::STATUS_PROCESSING => '处理中',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_FAILED => '失败'
        ];
        
        return $status[$data['status']] ?? '未知';
    }
    
    /**
     * 实际到账金额
     */
    public function getActualAmountAttr($value, $data)
    {
        return $data['amount'] - $data['fee'];
    }
    
    /**
     * 获取提币统计
     */
    public static function getWithdrawalStats(): array
    {
        // 总提币申请数（只统计正式用户）
        $totalWithdrawals = self::alias('w')
                               ->join('ce_users u', 'w.user_id = u.id')
                               ->where('u.user_type', 1)
                               ->count();
        
        // 今日提币申请数
        $todayWithdrawals = self::alias('w')
                               ->join('ce_users u', 'w.user_id = u.id')
                               ->where('u.user_type', 1)
                               ->whereTime('w.created_at', 'today')
                               ->count();
        
        // 待审核提币数
        $pendingWithdrawals = self::alias('w')
                                 ->join('ce_users u', 'w.user_id = u.id')
                                 ->where('u.user_type', 1)
                                 ->where('w.status', self::STATUS_PENDING)
                                 ->count();
        
        // 今日提币金额
        $todayAmount = self::alias('w')
                          ->join('ce_users u', 'w.user_id = u.id')
                          ->where('u.user_type', 1)
                          ->where('w.status', self::STATUS_COMPLETED)
                          ->whereTime('w.created_at', 'today')
                          ->sum('w.amount');
        
        return [
            'total_withdrawals' => $totalWithdrawals,
            'today_withdrawals' => $todayWithdrawals,
            'pending_withdrawals' => $pendingWithdrawals,
            'today_amount' => $todayAmount ?: 0,
        ];
    }
    
    /**
     * 获取代理商提币统计
     */
    public static function getAgentWithdrawalStats(int $agentId): array
    {
        // 下级总提币申请数（只统计正式用户）
        $totalWithdrawals = self::alias('w')
                               ->join('ce_users u', 'w.user_id = u.id')
                               ->where('u.user_type', 1)
                               ->where('u.agent_id', $agentId)
                               ->count();
        
        // 下级今日提币申请数
        $todayWithdrawals = self::alias('w')
                               ->join('ce_users u', 'w.user_id = u.id')
                               ->where('u.user_type', 1)
                               ->where('u.agent_id', $agentId)
                               ->whereTime('w.created_at', 'today')
                               ->count();
        
        // 下级待审核提币数
        $pendingWithdrawals = self::alias('w')
                                 ->join('ce_users u', 'w.user_id = u.id')
                                 ->where('u.user_type', 1)
                                 ->where('u.agent_id', $agentId)
                                 ->where('w.status', self::STATUS_PENDING)
                                 ->count();
        
        // 下级待审核提币金额
        $pendingAmount = self::alias('w')
                            ->join('ce_users u', 'w.user_id = u.id')
                            ->where('u.user_type', 1)
                            ->where('u.agent_id', $agentId)
                            ->where('w.status', self::STATUS_PENDING)
                            ->sum('w.amount');
        
        return [
            'total_withdrawals' => $totalWithdrawals,
            'today_withdrawals' => $todayWithdrawals,
            'pending_withdrawals' => $pendingWithdrawals,
            'pending_amount' => $pendingAmount ?: 0,
        ];
    }
    
    /**
     * 创建提币申请
     */
    public static function createWithdrawal(array $data): array
    {
        try {
            // 计算手续费
            $fee = $data['amount'] * 0.01; // 假设1%手续费
            
            $withdrawal = self::create([
                'user_id' => $data['user_id'],
                'coin_symbol' => $data['coin_symbol'],
                'amount' => $data['amount'],
                'fee' => $fee,
                'address' => $data['address'],
                'status' => self::STATUS_PENDING,
                'created_at' => date('Y-m-d H:i:s'),
            ]);
            
            return ['code' => 1, 'msg' => '提币申请提交成功', 'data' => $withdrawal];
            
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '提币申请失败：' . $e->getMessage()];
        }
    }
    
    /**
     * 审核提币申请
     */
    public function approve(int $processorId, string $remark = ''): bool
    {
        try {
            $this->save([
                'status' => self::STATUS_APPROVED,
                'processor_id' => $processorId,
                'processed_at' => date('Y-m-d H:i:s'),
                'remark' => $remark ?: '审核通过'
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 驳回提币申请
     */
    public function reject(int $processorId, string $reason): bool
    {
        try {
            $this->save([
                'status' => self::STATUS_REJECTED,
                'processor_id' => $processorId,
                'processed_at' => date('Y-m-d H:i:s'),
                'remark' => $reason
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 完成提币
     */
    public function complete(string $txHash = ''): bool
    {
        try {
            $this->save([
                'status' => self::STATUS_COMPLETED,
                'tx_hash' => $txHash,
                'completed_at' => date('Y-m-d H:i:s')
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取待审核列表
     */
    public static function getPendingList(int $agentId = 0, int $limit = 20): array
    {
        $query = self::alias('w')
                    ->join('ce_users u', 'w.user_id = u.id')
                    ->where('w.status', self::STATUS_PENDING)
                    ->where('u.user_type', 1);
        
        if ($agentId > 0) {
            $query->where('u.agent_id', $agentId);
        }
        
        $withdrawals = $query->field('w.*, u.username')
                            ->order('w.created_at', 'desc')
                            ->limit($limit)
                            ->select()
                            ->toArray();
        
        return $withdrawals;
    }
    
    /**
     * 批量审核
     */
    public static function batchApprove(array $ids, int $processorId): array
    {
        try {
            $count = self::whereIn('id', $ids)
                        ->where('status', self::STATUS_PENDING)
                        ->update([
                            'status' => self::STATUS_APPROVED,
                            'processor_id' => $processorId,
                            'processed_at' => date('Y-m-d H:i:s'),
                            'remark' => '批量审核通过'
                        ]);
            
            return ['code' => 1, 'msg' => "成功审核 {$count} 个提币申请"];
            
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '批量审核失败：' . $e->getMessage()];
        }
    }
}
