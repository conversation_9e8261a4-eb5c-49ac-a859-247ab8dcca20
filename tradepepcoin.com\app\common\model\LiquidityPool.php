<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 流动性池模型
 */
class LiquidityPool extends Model
{
    protected $table = 'liquidity_pools';

    // 设置字段信息
    protected $schema = [
        'id' => 'int',
        'pool_id' => 'string',
        'name' => 'string',
        'description' => 'text',
        'type' => 'string',
        'token_a' => 'string',
        'token_b' => 'string',
        'reward_token' => 'string',
        'apy' => 'decimal',
        'min_deposit' => 'decimal',
        'max_deposit' => 'decimal',
        'lock_period' => 'int',
        'total_liquidity' => 'decimal',
        'total_rewards' => 'decimal',
        'distributed_rewards' => 'decimal',
        'participants_count' => 'int',
        'fee_rate' => 'decimal',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'status' => 'int',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 池子状态
    const STATUS_PENDING = 0;    // 待开始
    const STATUS_ACTIVE = 1;     // 活跃
    const STATUS_PAUSED = 2;     // 暂停
    const STATUS_ENDED = 3;      // 已结束
    const STATUS_CLOSED = 4;     // 已关闭

    // 池子类型
    const TYPE_STANDARD = 'standard';        // 标准池
    const TYPE_STABLE = 'stable';           // 稳定币池
    const TYPE_WEIGHTED = 'weighted';       // 权重池
    const TYPE_BOOTSTRAP = 'bootstrap';     // 启动池

    /**
     * 关联DeFi位置
     */
    public function positions()
    {
        return $this->hasMany(DeFiPosition::class, 'pool_id', 'pool_id');
    }

    /**
     * 关联流动性记录
     */
    public function liquidityRecords()
    {
        return $this->hasMany(LiquidityRecord::class, 'pool_id', 'pool_id');
    }

    /**
     * 启动池子
     */
    public function start(): bool
    {
        if ($this->status !== self::STATUS_PENDING) {
            return false;
        }

        $this->status = self::STATUS_ACTIVE;
        return $this->save();
    }

    /**
     * 暂停池子
     */
    public function pause(): bool
    {
        if ($this->status !== self::STATUS_ACTIVE) {
            return false;
        }

        $this->status = self::STATUS_PAUSED;
        return $this->save();
    }

    /**
     * 恢复池子
     */
    public function resume(): bool
    {
        if ($this->status !== self::STATUS_PAUSED) {
            return false;
        }

        $this->status = self::STATUS_ACTIVE;
        return $this->save();
    }

    /**
     * 结束池子
     */
    public function end(): bool
    {
        if (!in_array($this->status, [self::STATUS_ACTIVE, self::STATUS_PAUSED])) {
            return false;
        }

        $this->status = self::STATUS_ENDED;
        return $this->save();
    }

    /**
     * 增加流动性
     */
    public function addLiquidity(float $amount): bool
    {
        $this->total_liquidity += $amount;
        return $this->save();
    }

    /**
     * 减少流动性
     */
    public function removeLiquidity(float $amount): bool
    {
        $this->total_liquidity = max(0, $this->total_liquidity - $amount);
        return $this->save();
    }

    /**
     * 增加参与者
     */
    public function addParticipant(): bool
    {
        $this->participants_count++;
        return $this->save();
    }

    /**
     * 减少参与者
     */
    public function removeParticipant(): bool
    {
        $this->participants_count = max(0, $this->participants_count - 1);
        return $this->save();
    }

    /**
     * 分发奖励
     */
    public function distributeRewards(float $amount): bool
    {
        $this->distributed_rewards += $amount;
        return $this->save();
    }

    /**
     * 获取池子统计信息
     */
    public function getStatistics(): array
    {
        return [
            'total_liquidity' => $this->total_liquidity,
            'participants_count' => $this->participants_count,
            'total_rewards' => $this->total_rewards,
            'distributed_rewards' => $this->distributed_rewards,
            'remaining_rewards' => $this->total_rewards - $this->distributed_rewards,
            'apy' => $this->apy,
            'tvl_usd' => $this->calculateTVL(),
            'daily_volume' => $this->calculateDailyVolume(),
            'utilization_rate' => $this->calculateUtilizationRate()
        ];
    }

    /**
     * 计算TVL
     */
    public function calculateTVL(): float
    {
        // 这里应该根据实际价格计算
        // 简化处理
        return $this->total_liquidity * 100; // 假设每个LP代币价值100美元
    }

    /**
     * 计算日交易量
     */
    public function calculateDailyVolume(): float
    {
        // 这里应该从交易记录中计算
        // 简化处理
        return rand(10000, 100000);
    }

    /**
     * 计算利用率
     */
    public function calculateUtilizationRate(): float
    {
        if ($this->max_deposit <= 0) {
            return 0;
        }

        return ($this->total_liquidity / $this->max_deposit) * 100;
    }

    /**
     * 检查是否可以添加流动性
     */
    public function canAddLiquidity(float $amount): bool
    {
        if ($this->status !== self::STATUS_ACTIVE) {
            return false;
        }

        if (time() < strtotime($this->start_time) || time() > strtotime($this->end_time)) {
            return false;
        }

        if ($this->max_deposit > 0 && ($this->total_liquidity + $amount) > $this->max_deposit) {
            return false;
        }

        return true;
    }

    /**
     * 获取当前APY
     */
    public function getCurrentApy(): float
    {
        // 根据池子利用率动态调整APY
        $utilizationRate = $this->calculateUtilizationRate();
        
        if ($utilizationRate > 90) {
            return $this->apy * 1.2; // 高利用率时提高APY
        } elseif ($utilizationRate < 30) {
            return $this->apy * 0.8; // 低利用率时降低APY
        }
        
        return $this->apy;
    }

    /**
     * 获取池子健康度
     */
    public function getHealthScore(): array
    {
        $score = 100;
        $issues = [];

        // 检查流动性
        if ($this->total_liquidity < $this->min_deposit * 10) {
            $score -= 20;
            $issues[] = '流动性不足';
        }

        // 检查参与者数量
        if ($this->participants_count < 10) {
            $score -= 15;
            $issues[] = '参与者较少';
        }

        // 检查奖励余额
        $remainingRewards = $this->total_rewards - $this->distributed_rewards;
        if ($remainingRewards < $this->total_rewards * 0.1) {
            $score -= 25;
            $issues[] = '奖励余额不足';
        }

        // 检查时间
        $timeRemaining = strtotime($this->end_time) - time();
        if ($timeRemaining < 86400 * 7) { // 少于7天
            $score -= 10;
            $issues[] = '即将到期';
        }

        return [
            'score' => max(0, $score),
            'level' => $this->getHealthLevel($score),
            'issues' => $issues
        ];
    }

    /**
     * 获取健康等级
     */
    private function getHealthLevel(int $score): string
    {
        if ($score >= 90) {
            return 'excellent';
        } elseif ($score >= 70) {
            return 'good';
        } elseif ($score >= 50) {
            return 'fair';
        } else {
            return 'poor';
        }
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr(): string
    {
        $statusTexts = [
            self::STATUS_PENDING => '待开始',
            self::STATUS_ACTIVE => '活跃',
            self::STATUS_PAUSED => '暂停',
            self::STATUS_ENDED => '已结束',
            self::STATUS_CLOSED => '已关闭'
        ];

        return $statusTexts[$this->status] ?? '未知状态';
    }

    /**
     * 获取类型文本
     */
    public function getTypeTextAttr(): string
    {
        $typeTexts = [
            self::TYPE_STANDARD => '标准池',
            self::TYPE_STABLE => '稳定币池',
            self::TYPE_WEIGHTED => '权重池',
            self::TYPE_BOOTSTRAP => '启动池'
        ];

        return $typeTexts[$this->type] ?? '未知类型';
    }

    /**
     * 获取交易对名称
     */
    public function getPairNameAttr(): string
    {
        return $this->token_a . '/' . $this->token_b;
    }

    /**
     * 获取锁定期文本
     */
    public function getLockPeriodTextAttr(): string
    {
        if ($this->lock_period <= 0) {
            return '无锁定';
        }

        $days = $this->lock_period / 86400;
        
        if ($days < 1) {
            $hours = $this->lock_period / 3600;
            return $hours . '小时';
        } elseif ($days < 30) {
            return $days . '天';
        } else {
            $months = $days / 30;
            return round($months, 1) . '个月';
        }
    }

    /**
     * 搜索器：按状态
     */
    public function searchStatusAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('status', $value);
        }
    }

    /**
     * 搜索器：按类型
     */
    public function searchTypeAttr($query, $value)
    {
        if ($value) {
            $query->where('type', $value);
        }
    }

    /**
     * 搜索器：按代币
     */
    public function searchTokenAttr($query, $value)
    {
        if ($value) {
            $query->where('token_a|token_b|reward_token', 'like', "%{$value}%");
        }
    }

    /**
     * 搜索器：按APY范围
     */
    public function searchApyRangeAttr($query, $value)
    {
        if (is_array($value) && count($value) === 2) {
            $query->whereBetween('apy', $value);
        }
    }

    /**
     * 搜索器：按关键词
     */
    public function searchKeywordAttr($query, $value)
    {
        if ($value) {
            $query->where('name|description', 'like', "%{$value}%");
        }
    }
}
