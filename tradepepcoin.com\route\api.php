<?php
// +----------------------------------------------------------------------
// | API路由配置
// +----------------------------------------------------------------------

use think\facade\Route;

// API版本分组
Route::group('api/v1', function () {
    
    // 用户相关路由
    Route::group('user', function () {
        Route::post('register', 'User/register');
        Route::post('login', 'User/login');
        Route::post('logout', 'User/logout');
        Route::get('profile', 'User/profile');
        Route::put('profile', 'User/updateProfile');
        Route::post('kyc', 'User/submitKyc');
        Route::get('kyc/status', 'User/getKycStatus');
        Route::post('change-password', 'User/changePassword');
        Route::post('bind-google-auth', 'User/bindGoogleAuth');
        Route::post('verify-google-auth', 'User/verifyGoogleAuth');
    });

    // 交易相关路由
    Route::group('trade', function () {
        Route::post('order', 'Trade/createOrder');
        Route::delete('order/:order_id', 'Trade/cancelOrder');
        Route::get('orders', 'Trade/getUserOrders');
        Route::get('order/:order_id', 'Trade/getOrderDetail');
        Route::get('history', 'Trade/getTradeHistory');
        Route::post('stop-order', 'Trade/createStopOrder');
        Route::post('grid-order', 'Trade/createGridOrder');
        Route::post('dca-strategy', 'Trade/createDcaStrategy');
        Route::post('copy-order', 'Trade/createCopyOrder');
    });

    // 资产相关路由
    Route::group('asset', function () {
        Route::get('balance', 'Asset/getBalance');
        Route::get('balance/:coin_symbol', 'Asset/getCoinBalance');
        Route::post('deposit', 'Asset/createDeposit');
        Route::post('withdraw', 'Asset/createWithdraw');
        Route::get('deposits', 'Asset/getDeposits');
        Route::get('withdraws', 'Asset/getWithdraws');
        Route::get('records', 'Asset/getFinancialRecords');
        Route::get('address/:coin_symbol', 'Asset/getDepositAddress');
    });

    // 市场数据路由
    Route::group('market', function () {
        Route::get('ticker', 'Market/getTicker');
        Route::get('ticker/:symbol', 'Market/getSymbolTicker');
        Route::get('depth/:symbol', 'Market/getDepth');
        Route::get('trades/:symbol', 'Market/getTrades');
        Route::get('kline/:symbol', 'Market/getKline');
        Route::get('symbols', 'Market/getSymbols');
        Route::get('stats', 'Market/getStats');
    });

    // 量化交易路由
    Route::group('quant', function () {
        Route::post('strategy', 'Quant/createStrategy');
        Route::get('strategies', 'Quant/getStrategies');
        Route::get('strategy/:strategy_id', 'Quant/getStrategyDetail');
        Route::post('strategy/:strategy_id/backtest', 'Quant/backtest');
        Route::post('strategy/:strategy_id/start', 'Quant/startStrategy');
        Route::post('strategy/:strategy_id/stop', 'Quant/stopStrategy');
        Route::get('strategy/:strategy_id/signals', 'Quant/generateSignals');
        Route::post('strategy/:strategy_id/execute', 'Quant/executeStrategy');
    });

    // 机构服务路由
    Route::group('institutional', function () {
        Route::post('account', 'Institutional/createAccount');
        Route::get('accounts', 'Institutional/getAccounts');
        Route::post('account/:account_id/review', 'Institutional/reviewAccount');
        Route::post('account/:account_id/otc-order', 'Institutional/createOtcOrder');
        Route::get('account/:account_id/otc-orders', 'Institutional/getOtcOrders');
        Route::post('otc-order/:order_id/match', 'Institutional/matchOtcOrder');
        Route::post('account/:account_id/custody', 'Institutional/createCustodyAccount');
        Route::get('account/:account_id/stats', 'Institutional/getStats');
    });

    // 社交交易路由
    Route::group('social', function () {
        Route::post('strategy', 'Social/createStrategy');
        Route::post('strategy/:strategy_id/publish', 'Social/publishStrategy');
        Route::post('strategy/:strategy_id/subscribe', 'Social/subscribeStrategy');
        Route::get('strategies', 'Social/getStrategies');
        Route::post('copy-trade/:original_order_id', 'Social/copyTrade');
        Route::post('follow/:trader_id', 'Social/followTrader');
        Route::post('post', 'Social/createPost');
        Route::get('ranking', 'Social/getTraderRanking');
        Route::get('feed', 'Social/getSocialFeed');
    });

    // AI智能交易路由
    Route::group('ai', function () {
        Route::post('strategy', 'Ai/createStrategy');
        Route::get('strategies', 'Ai/getStrategies');
        Route::get('prediction/:symbol', 'Ai/generatePrediction');
        Route::get('investment-advice', 'Ai/getInvestmentAdvice');
        Route::get('risk-alert', 'Ai/riskAlert');
        Route::post('strategy/:strategy_id/execute', 'Ai/executeAiTrade');
    });

    // DeFi相关路由
    Route::group('defi', function () {
        Route::get('pools', 'DeFi/getLiquidityPools');
        Route::post('pool/:pool_id/add-liquidity', 'DeFi/addLiquidity');
        Route::post('pool/:pool_id/remove-liquidity', 'DeFi/removeLiquidity');
        Route::get('staking/opportunities', 'DeFi/getStakingOpportunities');
        Route::post('staking/:token_symbol/stake', 'DeFi/stakeTokens');
        Route::post('staking/:token_symbol/unstake', 'DeFi/unstakeTokens');
        Route::get('lending/markets', 'DeFi/getLendingMarkets');
        Route::post('lending/:token_symbol/lend', 'DeFi/lendTokens');
        Route::post('lending/:token_symbol/borrow', 'DeFi/borrowTokens');
        Route::get('yield-farming', 'DeFi/getYieldFarmingOpportunities');
    });

    // NFT相关路由
    Route::group('nft', function () {
        Route::get('collections', 'Nft/getCollections');
        Route::get('collection/:collection_id', 'Nft/getCollectionDetail');
        Route::get('tokens', 'Nft/getTokens');
        Route::get('token/:token_id', 'Nft/getTokenDetail');
        Route::post('mint', 'Nft/mintToken');
        Route::post('token/:token_id/list', 'Nft/listToken');
        Route::post('token/:token_id/buy', 'Nft/buyToken');
        Route::post('auction', 'Nft/createAuction');
        Route::post('auction/:auction_id/bid', 'Nft/placeBid');
        Route::get('marketplace', 'Nft/getMarketplace');
    });

    // 跨链相关路由
    Route::group('cross-chain', function () {
        Route::get('bridges', 'CrossChain/getBridges');
        Route::get('bridge/:bridge_id', 'CrossChain/getBridgeDetail');
        Route::post('transfer', 'CrossChain/initiateTransfer');
        Route::get('transfer/:transfer_id', 'CrossChain/getTransferStatus');
        Route::get('supported-chains', 'CrossChain/getSupportedChains');
        Route::get('supported-tokens', 'CrossChain/getSupportedTokens');
        Route::get('fees', 'CrossChain/getBridgeFees');
    });

    // 合约交易路由
    Route::group('contract', function () {
        Route::post('second-contract', 'Contract/createSecondContract');
        Route::get('config', 'Contract/getConfig');
        Route::get('orders', 'Contract/getUserOrders');
        Route::get('order/:order_id', 'Contract/getOrderDetail');
        Route::get('quick-amounts', 'Contract/getQuickAmounts');
        Route::get('stats', 'Contract/getUserStats');
        Route::get('today-stats', 'Contract/getTodayStats');
        Route::get('ranking', 'Contract/getRanking');
        Route::get('price-history', 'Contract/getPriceHistory');
        Route::get('current-price', 'Contract/getCurrentPrice');
        Route::post('settle-order', 'Contract/settleOrder'); // 测试用
    });

    // 客服系统路由
    Route::group('customer-service', function () {
        Route::post('ticket', 'CustomerService/createTicket');
        Route::post('message', 'CustomerService/sendMessage');
        Route::get('tickets', 'CustomerService/getTickets');
        Route::get('ticket/:ticket_id', 'CustomerService/getTicketDetail');
        Route::post('close-ticket', 'CustomerService/closeTicket');
        Route::get('unread-count', 'CustomerService/getUnreadCount');
        Route::get('categories', 'CustomerService/getCategories');
        Route::get('priorities', 'CustomerService/getPriorities');
    });

    // 代理系统路由
    Route::group('agent', function () {
        Route::get('users', 'Agent/getUsers');
        Route::get('user/:user_id', 'Agent/getUserDetail');
        Route::get('stats', 'Agent/getStats');
        Route::post('create-user', 'Agent/createUser');
        Route::post('change-user-type', 'Agent/changeUserType');
        Route::get('commission-stats', 'Agent/getCommissionStats');
        Route::get('invite-link', 'Agent/getInviteLink');
        Route::get('sub-agents', 'Agent/getSubAgents');
        Route::get('team-performance', 'Agent/getTeamPerformance');
        Route::get('user-trade-stats', 'Agent/getUserTradeStats');
        Route::get('user-asset-stats', 'Agent/getUserAssetStats');
        Route::post('reset-user-password', 'Agent/resetUserPassword');
        Route::post('update-user-status', 'Agent/updateUserStatus');
        Route::get('agent-level', 'Agent/getAgentLevel');
    });

    // 认证相关路由（不需要登录）
    Route::group('auth', function () {
        Route::post('register', 'Auth/register');
        Route::post('login', 'Auth/login');
        Route::post('send-register-code', 'Auth/sendRegisterCode');
        Route::post('send-reset-code', 'Auth/sendResetCode');
        Route::post('reset-password', 'Auth/resetPassword');
        Route::post('logout', 'Auth/logout');
        Route::post('refresh-token', 'Auth/refreshToken');
        Route::post('verify-token', 'Auth/verifyToken');
        Route::get('check-username', 'Auth/checkUsername');
        Route::get('check-email', 'Auth/checkEmail');
        Route::get('register-config', 'Auth/getRegisterConfig');
    })->allowCrossDomain();

    // 资产管理路由
    Route::group('asset', function () {
        Route::get('balance', 'Asset/getBalance');
        Route::get('deposit-address', 'Asset/getDepositAddress');
        Route::post('create-deposit', 'Asset/createDeposit');
        Route::get('deposit-history', 'Asset/getDepositHistory');
        Route::post('create-withdraw', 'Asset/createWithdraw');
        Route::get('withdraw-history', 'Asset/getWithdrawHistory');
        Route::post('cancel-withdraw/:withdraw_id', 'Asset/cancelWithdraw');
        Route::get('supported-coins', 'Asset/getSupportedCoins');
        Route::get('coin-info', 'Asset/getCoinInfo');
        Route::get('asset-stats', 'Asset/getAssetStats');
        Route::get('financial-records', 'Asset/getFinancialRecords');
        Route::post('internal-transfer', 'Asset/internalTransfer');
    });

    // 前端配置路由（不需要登录）
    Route::group('config', function () {
        Route::get('frontend', 'Config/getFrontendConfig');
        Route::get('contract', 'Config/getContractConfig');
        Route::get('quick-amounts', 'Config/getQuickAmounts');
        Route::get('trading-pairs', 'Config/getTradingPairs');
        Route::get('time-periods', 'Config/getTimePeriods');
        Route::get('announcements', 'Config/getAnnouncements');
        Route::get('customer-service', 'Config/getCustomerServiceConfig');
        Route::get('fee', 'Config/getFeeConfig');
        Route::get('version', 'Config/getVersion');
    })->allowCrossDomain();

    // 管理后台路由
    Route::group('admin', function () {
        Route::post('login', 'Admin/login');
        Route::get('dashboard', 'Admin/dashboard');
        Route::get('users', 'Admin/getUsers');
        Route::get('user/:user_id', 'Admin/getUserDetail');
        Route::put('user/:user_id/status', 'Admin/updateUserStatus');
        Route::get('kyc/pending', 'Admin/getPendingKyc');
        Route::post('kyc/:kyc_id/review', 'Admin/reviewKyc');
        Route::get('orders', 'Admin/getOrders');
        Route::get('trades', 'Admin/getTrades');
        Route::get('withdraws/pending', 'Admin/getPendingWithdraws');
        Route::post('withdraw/:withdraw_id/review', 'Admin/reviewWithdraw');
        Route::get('system/config', 'Admin/getSystemConfig');
        Route::put('system/config', 'Admin/updateSystemConfig');
        Route::get('statistics', 'Admin/getStatistics');

        // 系统配置管理
        Route::get('config/:type', 'Admin/getConfig');
        Route::post('config/:type/:key', 'Admin/updateConfig');
        Route::post('upload-image/:type/:key', 'Admin/uploadImage');

        // 合约配置管理
        Route::get('contract-config', 'Admin/getContractConfig');
        Route::post('contract-config/:symbol', 'Admin/updateContractConfig');

        // 财务报表
        Route::get('report/overview', 'Admin/getPlatformOverview');
        Route::get('report/assets', 'Admin/getUserAssetReport');
        Route::get('report/trades', 'Admin/getTradeReport');
        Route::get('report/contracts', 'Admin/getContractReport');
        Route::get('report/financial-flow', 'Admin/getFinancialFlowReport');

        // 客服管理
        Route::get('service/tickets', 'Admin/getServiceTickets');
        Route::post('service/reply', 'Admin/replyTicket');
        Route::post('service/assign', 'Admin/assignTicket');
    });

})->middleware(['api_auth', 'api_throttle']);

// WebSocket路由
Route::get('ws', 'WebSocket/connect');

// 公开API路由（无需认证）
Route::group('api/public', function () {
    Route::get('ticker', 'Market/getTicker');
    Route::get('depth/:symbol', 'Market/getDepth');
    Route::get('trades/:symbol', 'Market/getTrades');
    Route::get('kline/:symbol', 'Market/getKline');
    Route::get('symbols', 'Market/getSymbols');
    Route::get('server-time', 'Market/getServerTime');
});

// Webhook路由（用于自动充值等，不需要登录）
Route::group('api/webhook', function () {
    Route::post('deposit', 'Asset/processAutoDeposit');
    Route::post('withdraw-confirm', 'Asset/processWithdrawConfirm');
    Route::post('price-update', 'Market/updatePrice');
})->allowCrossDomain();
