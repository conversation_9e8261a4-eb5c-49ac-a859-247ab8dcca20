<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\LeverageOrder;
use app\common\model\TradingPair;
use app\common\model\UserAsset;
use app\common\model\FinancialRecord;
use think\facade\Db;
use think\facade\Log;

/**
 * 杠杆交易服务类
 */
class LeverageTradingService
{
    protected $klineService;

    public function __construct()
    {
        $this->klineService = new KlineService();
    }

    /**
     * 创建杠杆订单
     */
    public function createLeverageOrder(array $orderData): array
    {
        try {
            Db::startTrans();

            // 验证交易对
            $tradingPair = TradingPair::getBySymbol($orderData['symbol']);
            if (!$tradingPair || !$tradingPair->isTradeEnabled()) {
                throw new \Exception('交易对不存在或已禁用');
            }

            // 验证订单数据
            $validation = $this->validateLeverageOrderData($orderData, $tradingPair);
            if (!$validation['valid']) {
                throw new \Exception($validation['message']);
            }

            // 计算保证金需求
            $marginRequired = $this->calculateMarginRequired($orderData);

            // 检查用户资产
            $assetCheck = $this->checkUserMargin($orderData['user_id'], $marginRequired);
            if (!$assetCheck['valid']) {
                throw new \Exception($assetCheck['message']);
            }

            // 创建订单
            $order = $this->createLeverageOrderRecord($orderData, $tradingPair, $marginRequired);

            // 冻结保证金
            $this->freezeMargin($order->user_id, $marginRequired);

            // 立即成交（简化处理）
            $this->executeLeverageOrder($order, $tradingPair);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '杠杆订单创建成功',
                'data' => [
                    'order_id' => $order->order_id,
                    'symbol' => $order->symbol,
                    'type' => $order->type,
                    'leverage' => $order->leverage,
                    'margin' => $order->margin,
                    'amount' => $order->amount,
                    'liquidation_price' => $order->liquidation_price
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('创建杠杆订单失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 验证杠杆订单数据
     */
    private function validateLeverageOrderData(array $data, TradingPair $tradingPair): array
    {
        // 验证必要字段
        $required = ['user_id', 'symbol', 'type', 'amount', 'leverage'];
        foreach ($required as $field) {
            if (!isset($data[$field]) || $data[$field] === '') {
                return ['valid' => false, 'message' => "字段 {$field} 不能为空"];
            }
        }

        // 验证订单类型
        if (!in_array($data['type'], [LeverageOrder::TYPE_BUY, LeverageOrder::TYPE_SELL])) {
            return ['valid' => false, 'message' => '无效的订单类型'];
        }

        // 验证杠杆倍数
        if ($data['leverage'] < 1 || $data['leverage'] > 100) {
            return ['valid' => false, 'message' => '杠杆倍数必须在1-100倍之间'];
        }

        // 验证数量
        $amountValidation = $tradingPair->validateAmount($data['amount']);
        if (!$amountValidation['valid']) {
            return $amountValidation;
        }

        return ['valid' => true];
    }

    /**
     * 计算保证金需求
     */
    private function calculateMarginRequired(array $orderData): float
    {
        $currentPrice = $this->getCurrentPrice($orderData['symbol']);
        $notionalValue = $orderData['amount'] * $currentPrice;
        
        // 保证金 = 名义价值 / 杠杆倍数
        return $notionalValue / $orderData['leverage'];
    }

    /**
     * 检查用户保证金
     */
    private function checkUserMargin(int $userId, float $marginRequired): array
    {
        $asset = UserAsset::where('user_id', $userId)
                         ->where('coin_symbol', 'USDT')
                         ->find();

        if (!$asset) {
            return ['valid' => false, 'message' => 'USDT资产不存在'];
        }

        if ($asset->available < $marginRequired) {
            return ['valid' => false, 'message' => '保证金不足'];
        }

        return ['valid' => true];
    }

    /**
     * 创建杠杆订单记录
     */
    private function createLeverageOrderRecord(array $data, TradingPair $tradingPair, float $margin): LeverageOrder
    {
        $currentPrice = $this->getCurrentPrice($data['symbol']);

        $orderData = [
            'user_id' => $data['user_id'],
            'symbol' => $data['symbol'],
            'order_id' => LeverageOrder::generateOrderId(),
            'type' => $data['type'],
            'order_type' => LeverageOrder::ORDER_TYPE_MARKET, // 简化为市价单
            'price' => $currentPrice,
            'amount' => $tradingPair->formatAmount($data['amount']),
            'total' => $data['amount'] * $currentPrice,
            'margin' => $margin,
            'leverage' => $data['leverage'],
            'status' => LeverageOrder::STATUS_PENDING,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return LeverageOrder::create($orderData);
    }

    /**
     * 冻结保证金
     */
    private function freezeMargin(int $userId, float $margin): void
    {
        $asset = UserAsset::where('user_id', $userId)
                         ->where('coin_symbol', 'USDT')
                         ->find();

        $asset->available -= $margin;
        $asset->frozen += $margin;
        $asset->save();
    }

    /**
     * 执行杠杆订单
     */
    private function executeLeverageOrder(LeverageOrder $order, TradingPair $tradingPair): void
    {
        $currentPrice = $this->getCurrentPrice($order->symbol);

        // 更新订单状态
        $order->filled_amount = $order->amount;
        $order->filled_total = $order->amount * $currentPrice;
        $order->avg_price = $currentPrice;
        $order->status = LeverageOrder::STATUS_FILLED;
        $order->filled_at = date('Y-m-d H:i:s');

        // 计算强平价格
        $order->liquidation_price = $order->calculateLiquidationPrice();

        $order->save();

        // 记录财务流水
        FinancialRecord::create([
            'user_id' => $order->user_id,
            'type' => 'leverage_open',
            'coin_symbol' => 'USDT',
            'amount' => -$order->margin,
            'balance' => $this->getUserUSDTBalance($order->user_id),
            'description' => $order->symbol . '杠杆开仓，保证金：' . $order->margin,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 平仓
     */
    public function closePosition(int $userId, string $orderId, float $closeAmount = 0): array
    {
        try {
            Db::startTrans();

            $order = LeverageOrder::where('user_id', $userId)
                                 ->where('order_id', $orderId)
                                 ->find();

            if (!$order) {
                throw new \Exception('订单不存在');
            }

            if ($order->status != LeverageOrder::STATUS_FILLED) {
                throw new \Exception('订单状态不允许平仓');
            }

            $currentPrice = $this->getCurrentPrice($order->symbol);
            $result = $order->closePosition($currentPrice, $closeAmount);

            if ($result['code'] != 1) {
                throw new \Exception($result['msg']);
            }

            // 释放保证金并结算盈亏
            $this->settlePosition($order, $result['data']);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '平仓成功',
                'data' => $result['data']
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('平仓失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 结算持仓
     */
    private function settlePosition(LeverageOrder $order, array $closeData): void
    {
        $userId = $order->user_id;
        $pnl = $closeData['pnl'];
        $closeAmount = $closeData['close_amount'];
        
        // 计算释放的保证金比例
        $marginRatio = $closeAmount / ($order->filled_amount + $closeAmount);
        $releasedMargin = $order->margin * $marginRatio;

        $asset = UserAsset::where('user_id', $userId)
                         ->where('coin_symbol', 'USDT')
                         ->find();

        // 释放保证金
        $asset->frozen -= $releasedMargin;
        
        // 结算盈亏
        $finalAmount = $releasedMargin + $pnl;
        $asset->available += $finalAmount;
        $asset->total = $asset->available + $asset->frozen;
        $asset->save();

        // 记录财务流水
        FinancialRecord::create([
            'user_id' => $userId,
            'type' => $pnl >= 0 ? 'leverage_profit' : 'leverage_loss',
            'coin_symbol' => 'USDT',
            'amount' => $finalAmount,
            'balance' => $asset->available,
            'description' => $order->symbol . '杠杆平仓，盈亏：' . $pnl,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 强制平仓
     */
    public function liquidatePosition(string $orderId): array
    {
        try {
            Db::startTrans();

            $order = LeverageOrder::find($orderId);
            if (!$order) {
                throw new \Exception('订单不存在');
            }

            $currentPrice = $this->getCurrentPrice($order->symbol);
            
            if (!$order->shouldLiquidate($currentPrice)) {
                throw new \Exception('订单不需要强平');
            }

            $liquidationPrice = $order->liquidation_price;
            $order->liquidate($liquidationPrice);

            // 强平结算
            $this->settleLiquidation($order);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '强制平仓成功',
                'data' => [
                    'order_id' => $order->order_id,
                    'liquidation_price' => $liquidationPrice,
                    'pnl' => $order->pnl
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('强制平仓失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 强平结算
     */
    private function settleLiquidation(LeverageOrder $order): void
    {
        $asset = UserAsset::where('user_id', $order->user_id)
                         ->where('coin_symbol', 'USDT')
                         ->find();

        // 释放保证金（通常强平后保证金损失殆尽）
        $asset->frozen -= $order->margin;
        
        // 如果还有剩余价值，返还给用户
        $remainingValue = max(0, $order->margin + $order->pnl);
        if ($remainingValue > 0) {
            $asset->available += $remainingValue;
        }
        
        $asset->total = $asset->available + $asset->frozen;
        $asset->save();

        // 记录财务流水
        FinancialRecord::create([
            'user_id' => $order->user_id,
            'type' => 'leverage_liquidation',
            'coin_symbol' => 'USDT',
            'amount' => $order->pnl,
            'balance' => $asset->available,
            'description' => $order->symbol . '杠杆强制平仓，损失：' . abs($order->pnl),
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 批量检查强平
     */
    public function checkLiquidations(string $symbol): array
    {
        try {
            $currentPrice = $this->getCurrentPrice($symbol);
            $liquidationOrders = LeverageOrder::getLiquidationOrders($symbol, $currentPrice);

            $liquidatedCount = 0;
            foreach ($liquidationOrders as $order) {
                $result = $this->liquidatePosition($order->id);
                if ($result['code'] == 1) {
                    $liquidatedCount++;
                }
            }

            return [
                'code' => 1,
                'msg' => "检查完成，强平 {$liquidatedCount} 个订单",
                'data' => [
                    'symbol' => $symbol,
                    'current_price' => $currentPrice,
                    'liquidated_count' => $liquidatedCount
                ]
            ];

        } catch (\Exception $e) {
            Log::error('批量检查强平失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 获取用户持仓
     */
    public function getUserPositions(int $userId, string $symbol = ''): array
    {
        try {
            $positions = LeverageOrder::getUserPositions($userId, $symbol);
            
            // 计算实时盈亏
            foreach ($positions as &$position) {
                $currentPrice = $this->getCurrentPrice($position['symbol']);
                $position['current_price'] = $currentPrice;
                $position['unrealized_pnl'] = (new LeverageOrder($position))->calculateUnrealizedPnl($currentPrice);
                $position['margin_ratio'] = (new LeverageOrder($position))->calculateMarginRatio($currentPrice);
            }

            return [
                'code' => 1,
                'data' => $positions
            ];

        } catch (\Exception $e) {
            Log::error('获取用户持仓失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 获取用户杠杆交易统计
     */
    public function getUserLeverageStats(int $userId): array
    {
        try {
            $stats = LeverageOrder::getLeverageStats($userId);
            
            return [
                'code' => 1,
                'data' => $stats
            ];

        } catch (\Exception $e) {
            Log::error('获取杠杆交易统计失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 获取当前价格
     */
    private function getCurrentPrice(string $symbol): float
    {
        $tradingPair = TradingPair::getBySymbol($symbol);
        return $tradingPair ? $tradingPair->getLatestPrice() : 0;
    }

    /**
     * 获取用户USDT余额
     */
    private function getUserUSDTBalance(int $userId): float
    {
        $asset = UserAsset::where('user_id', $userId)
                         ->where('coin_symbol', 'USDT')
                         ->find();
        
        return $asset ? $asset->available : 0;
    }
}
