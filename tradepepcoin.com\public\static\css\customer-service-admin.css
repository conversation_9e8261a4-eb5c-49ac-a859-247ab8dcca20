/* 客服管理后台样式 */

/* ==================== 对话列表 ==================== */
.conversation-list {
  max-height: 600px;
  overflow-y: auto;
}

.conversation-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.conversation-item:hover {
  background: var(--bg-hover);
}

.conversation-item.active {
  background: rgba(240, 185, 11, 0.1);
  border-left: 3px solid var(--color-primary);
}

.conversation-item:last-child {
  border-bottom: none;
}

.conversation-avatar {
  position: relative;
  margin-right: 12px;
}

.conversation-avatar img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.unread-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  animation: pulse 2s infinite;
}

.conversation-content {
  flex: 1;
  min-width: 0;
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.conversation-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
}

.conversation-time {
  font-size: 12px;
  color: var(--text-secondary);
}

.conversation-preview {
  font-size: 13px;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
}

/* ==================== 聊天窗口 ==================== */
.chat-window {
  height: 600px;
  display: flex;
  flex-direction: column;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.chat-user-info {
  display: flex;
  align-items: center;
}

.chat-user-info img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 12px;
  object-fit: cover;
}

.chat-user-details {
  display: flex;
  flex-direction: column;
}

.chat-user-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 16px;
}

.chat-user-status {
  font-size: 12px;
  color: var(--color-success);
}

.chat-actions {
  display: flex;
  gap: 8px;
}

.chat-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 空状态 */
.chat-empty {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
}

.chat-empty .empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.chat-empty .empty-text {
  font-size: 16px;
}

/* 消息列表 */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: var(--bg-tertiary);
}

.message {
  display: flex;
  margin-bottom: 16px;
  animation: messageSlideIn 0.3s ease;
}

.message-own {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  margin: 0 12px;
  flex-shrink: 0;
}

.message-own .message-avatar {
  background: var(--color-success);
}

.message-content {
  max-width: 70%;
  min-width: 100px;
}

.message-info {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  font-size: 12px;
  color: var(--text-secondary);
}

.message-own .message-info {
  flex-direction: row-reverse;
}

.message-sender {
  font-weight: 500;
  margin-right: 8px;
}

.message-own .message-sender {
  margin-right: 0;
  margin-left: 8px;
}

.message-time {
  opacity: 0.8;
}

/* 消息内容样式 */
.message-text {
  background: white;
  padding: 12px 16px;
  border-radius: 18px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  word-wrap: break-word;
  line-height: 1.5;
  color: var(--text-primary);
}

.message-own .message-text {
  background: var(--color-primary);
  color: #000;
}

.message-image {
  position: relative;
  cursor: pointer;
}

.message-image img {
  max-width: 200px;
  max-height: 200px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.message-image:hover img {
  transform: scale(1.05);
}

.message-video video {
  max-width: 250px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message-emoji {
  font-size: 36px;
  padding: 8px;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ==================== 输入区域 ==================== */
.chat-input-area {
  border-top: 1px solid var(--border-color);
  background: var(--bg-card);
  padding: 0;
}

.input-toolbar {
  display: flex;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.toolbar-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 8px;
  font-size: 18px;
  margin-right: 8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toolbar-btn:hover {
  background: var(--bg-hover);
  transform: scale(1.1);
}

/* 表情面板 */
.emoji-panel {
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-color);
  padding: 16px;
  max-height: 150px;
  overflow-y: auto;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  gap: 8px;
}

.emoji-item {
  font-size: 24px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  text-align: center;
  transition: background 0.2s ease;
}

.emoji-item:hover {
  background: var(--bg-hover);
  transform: scale(1.2);
}

/* 快速回复面板 */
.quick-replies-panel {
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-color);
  padding: 16px;
  max-height: 200px;
  overflow-y: auto;
}

.quick-reply-item {
  padding: 8px 12px;
  margin-bottom: 8px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.quick-reply-item:hover {
  background: var(--bg-hover);
  border-color: var(--color-primary);
  transform: translateX(4px);
}

.quick-reply-item:last-child {
  margin-bottom: 0;
}

/* 输入容器 */
.input-container {
  display: flex;
  padding: 16px;
  align-items: flex-end;
  gap: 12px;
}

#messageInput {
  flex: 1;
  border: 1px solid var(--border-color);
  border-radius: 20px;
  padding: 12px 16px;
  resize: none;
  outline: none;
  font-size: 14px;
  line-height: 1.5;
  max-height: 100px;
  background: var(--bg-card);
  color: var(--text-primary);
  transition: border-color 0.2s ease;
}

#messageInput:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(240, 185, 11, 0.2);
}

.send-btn {
  background: var(--color-primary);
  color: #000;
  border: none;
  border-radius: 20px;
  padding: 12px 24px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.send-btn:hover {
  background: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(240, 185, 11, 0.3);
}

.send-btn:disabled {
  background: var(--bg-disabled);
  color: var(--text-disabled);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* ==================== 图片预览 ==================== */
.image-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.image-preview-backdrop {
  cursor: pointer;
  max-width: 90%;
  max-height: 90%;
}

.image-preview-backdrop img {
  max-width: 100%;
  max-height: 100%;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* ==================== 加载状态 ==================== */
.message-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  color: var(--text-secondary);
  font-size: 14px;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
  .chat-window {
    height: calc(100vh - 200px);
  }
  
  .conversation-item {
    padding: 12px;
  }
  
  .conversation-avatar img {
    width: 40px;
    height: 40px;
  }
  
  .message-content {
    max-width: 85%;
  }
  
  .emoji-grid {
    grid-template-columns: repeat(8, 1fr);
  }
  
  .input-container {
    flex-direction: column;
    gap: 8px;
  }
  
  .send-btn {
    align-self: flex-end;
  }
}

/* ==================== 暗色主题优化 ==================== */
[data-theme="dark"] .message-text {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

[data-theme="dark"] .message-own .message-text {
  background: var(--color-primary);
  color: #000;
}

[data-theme="dark"] .chat-messages {
  background: var(--bg-primary);
}

[data-theme="dark"] .input-toolbar {
  background: var(--bg-secondary);
}

[data-theme="dark"] .emoji-panel,
[data-theme="dark"] .quick-replies-panel {
  background: var(--bg-secondary);
}

[data-theme="dark"] .quick-reply-item {
  background: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .quick-reply-item:hover {
  background: var(--bg-hover);
}
