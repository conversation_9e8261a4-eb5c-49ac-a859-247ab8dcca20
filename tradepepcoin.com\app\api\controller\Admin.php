<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\common\service\UserService;
use app\common\service\AgentService;
use app\common\service\SystemConfigService;
use app\common\service\FinancialReportService;
use app\common\service\CustomerServiceService;
use app\common\service\ContractService;
use think\Request;

/**
 * 管理后台控制器
 */
class Admin extends BaseController
{
    protected $userService;
    protected $agentService;
    protected $configService;
    protected $reportService;
    protected $customerService;
    protected $contractService;

    public function __construct()
    {
        parent::__construct();
        $this->userService = new UserService();
        $this->agentService = new AgentService();
        $this->configService = new SystemConfigService();
        $this->reportService = new FinancialReportService();
        $this->customerService = new CustomerServiceService();
        $this->contractService = new ContractService();
    }

    /**
     * 管理员登录
     */
    public function login(Request $request)
    {
        $data = $request->post();
        
        // 验证必填字段
        $validate = [
            'username' => 'require',
            'password' => 'require'
        ];

        $result = $this->validate($data, $validate);
        if ($result !== true) {
            return $this->error($result);
        }

        $result = $this->userService->adminLogin($data);

        if ($result['code']) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取仪表板数据
     */
    public function dashboard(Request $request)
    {
        $adminId = $this->getUserId();
        
        // 获取平台总览数据
        $overview = $this->reportService->getPlatformOverview();
        
        // 获取今日数据
        $todayData = $this->reportService->getPlatformOverview([
            'start_date' => date('Y-m-d'),
            'end_date' => date('Y-m-d')
        ]);

        return $this->success([
            'overview' => $overview['data'] ?? [],
            'today' => $todayData['data'] ?? []
        ]);
    }

    /**
     * 获取用户列表
     */
    public function getUsers(Request $request)
    {
        $adminId = $this->getUserId();
        $filters = [
            'username' => $request->get('username', ''),
            'email' => $request->get('email', ''),
            'user_type' => $request->get('user_type', ''),
            'status' => $request->get('status', ''),
            'kyc_level' => $request->get('kyc_level', ''),
            'agent_id' => $request->get('agent_id', ''),
            'start_date' => $request->get('start_date', ''),
            'end_date' => $request->get('end_date', ''),
            'page' => (int)$request->get('page', 1),
            'limit' => (int)$request->get('limit', 20)
        ];

        $result = $this->userService->getAdminUserList($adminId, $filters);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取用户详情
     */
    public function getUserDetail(Request $request)
    {
        $userId = (int)$request->param('user_id');
        $adminId = $this->getUserId();
        
        if (!$userId) {
            return $this->error('用户ID不能为空');
        }

        $result = $this->userService->getAdminUserDetail($adminId, $userId);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 更新用户状态
     */
    public function updateUserStatus(Request $request)
    {
        $userId = (int)$request->param('user_id');
        $status = (int)$request->post('status');
        $adminId = $this->getUserId();
        
        if (!$userId) {
            return $this->error('用户ID不能为空');
        }

        $result = $this->userService->updateUserStatus($userId, $status, $adminId);
        
        if ($result['code']) {
            return $this->success([], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 转换用户类型
     */
    public function changeUserType(Request $request)
    {
        $userId = (int)$request->post('user_id');
        $userType = (int)$request->post('user_type');
        $adminId = $this->getUserId();
        
        if (!$userId || !in_array($userType, [1, 2])) {
            return $this->error('参数错误');
        }

        $result = $this->userService->changeUserType($userId, $userType, $adminId);
        
        if ($result['code']) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 创建用户
     */
    public function createUser(Request $request)
    {
        $data = $request->post();
        $adminId = $this->getUserId();
        
        // 验证必填字段
        $validate = [
            'username' => 'require|max:50',
            'password' => 'require|min:6',
            'email' => 'email',
            'agent_id' => 'integer'
        ];

        $result = $this->validate($data, $validate);
        if ($result !== true) {
            return $this->error($result);
        }

        $result = $this->userService->adminRegister($data, $adminId);
        
        if ($result['code']) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取系统配置
     */
    public function getConfig(Request $request)
    {
        $type = $request->param('type', '');
        
        $result = $this->configService->getConfig($type);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 更新系统配置
     */
    public function updateConfig(Request $request)
    {
        $type = $request->param('type');
        $key = $request->param('key');
        $value = $request->post('value');
        $adminId = $this->getUserId();
        
        if (empty($type) || empty($key)) {
            return $this->error('配置类型和键不能为空');
        }

        $result = $this->configService->updateConfig($type, $key, $value, $adminId);
        
        if ($result['code']) {
            return $this->success([], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 上传图片
     */
    public function uploadImage(Request $request)
    {
        $type = $request->param('type');
        $key = $request->param('key');
        $file = $request->file('image');
        $adminId = $this->getUserId();
        
        if (empty($type) || empty($key) || !$file) {
            return $this->error('参数不完整');
        }

        $result = $this->configService->uploadImage($type, $key, $file, $adminId);
        
        if ($result['code']) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取合约配置
     */
    public function getContractConfig(Request $request)
    {
        $symbol = $request->get('symbol', '');
        
        $result = $this->configService->getContractConfig($symbol);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 更新合约配置
     */
    public function updateContractConfig(Request $request)
    {
        $symbol = $request->param('symbol');
        $config = $request->post();
        $adminId = $this->getUserId();
        
        if (empty($symbol)) {
            return $this->error('交易对不能为空');
        }

        $result = $this->configService->updateContractConfig($symbol, $config, $adminId);
        
        if ($result['code']) {
            return $this->success([], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取平台总览报表
     */
    public function getPlatformOverview(Request $request)
    {
        $filters = [
            'start_date' => $request->get('start_date', ''),
            'end_date' => $request->get('end_date', '')
        ];

        $result = $this->reportService->getPlatformOverview($filters);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取用户资产报表
     */
    public function getUserAssetReport(Request $request)
    {
        $filters = [
            'coin_symbol' => $request->get('coin_symbol', ''),
            'min_amount' => $request->get('min_amount', ''),
            'agent_id' => $request->get('agent_id', ''),
            'limit' => (int)$request->get('limit', 100)
        ];

        $result = $this->reportService->getUserAssetReport($filters);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取交易报表
     */
    public function getTradeReport(Request $request)
    {
        $filters = [
            'start_date' => $request->get('start_date', ''),
            'end_date' => $request->get('end_date', '')
        ];

        $result = $this->reportService->getTradeReport($filters);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取合约报表
     */
    public function getContractReport(Request $request)
    {
        $filters = [
            'start_date' => $request->get('start_date', ''),
            'end_date' => $request->get('end_date', '')
        ];

        $result = $this->reportService->getContractReport($filters);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取资金流水报表
     */
    public function getFinancialFlowReport(Request $request)
    {
        $filters = [
            'start_date' => $request->get('start_date', ''),
            'end_date' => $request->get('end_date', ''),
            'type' => $request->get('type', ''),
            'coin_symbol' => $request->get('coin_symbol', ''),
            'user_id' => $request->get('user_id', ''),
            'limit' => (int)$request->get('limit', 100)
        ];

        $result = $this->reportService->getFinancialFlowReport($filters);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取客服工单列表
     */
    public function getServiceTickets(Request $request)
    {
        $filters = [
            'status' => $request->get('status', ''),
            'category' => $request->get('category', ''),
            'priority' => $request->get('priority', ''),
            'service_id' => $request->get('service_id', ''),
            'limit' => (int)$request->get('limit', 50)
        ];

        $result = $this->customerService->getServiceTickets($filters);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 回复工单
     */
    public function replyTicket(Request $request)
    {
        $ticketId = $request->post('ticket_id');
        $content = $request->post('content');
        $adminId = $this->getUserId();
        
        if (empty($ticketId) || empty($content)) {
            return $this->error('工单ID和回复内容不能为空');
        }

        $result = $this->customerService->sendMessage($ticketId, $adminId, $content, 'service');
        
        if ($result['code']) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 分配工单
     */
    public function assignTicket(Request $request)
    {
        $ticketId = $request->post('ticket_id');
        $serviceId = (int)$request->post('service_id');
        $adminId = $this->getUserId();
        
        if (empty($ticketId) || !$serviceId) {
            return $this->error('参数不完整');
        }

        $result = $this->customerService->assignTicket($ticketId, $serviceId, $adminId);
        
        if ($result['code']) {
            return $this->success([], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }
}
