<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Cache;
use think\facade\Log;
use think\facade\Db;

/**
 * 短信服务类
 * 支持多个SMS服务商：阿里云、腾讯云、华为云等
 */
class SmsService
{
    // SMS服务商
    const PROVIDER_ALIYUN = 'aliyun';
    const PROVIDER_TENCENT = 'tencent';
    const PROVIDER_HUAWEI = 'huawei';
    const PROVIDER_TWILIO = 'twilio';

    // 短信类型
    const TYPE_REGISTER = 'register';
    const TYPE_LOGIN = 'login';
    const TYPE_RESET_PASSWORD = 'reset_password';
    const TYPE_WITHDRAW = 'withdraw';
    const TYPE_SECURITY = 'security';

    private $config;
    private $provider;

    public function __construct()
    {
        $this->config = config('sms');
        $this->provider = $this->config['default'] ?? self::PROVIDER_ALIYUN;
    }

    /**
     * 发送验证码
     */
    public function sendVerificationCode(string $phone, string $type = self::TYPE_REGISTER): array
    {
        try {
            // 验证手机号格式
            if (!$this->validatePhone($phone)) {
                return ['code' => 0, 'msg' => '手机号格式不正确'];
            }

            // 检查发送频率限制
            $rateLimitResult = $this->checkRateLimit($phone, $type);
            if (!$rateLimitResult['allow']) {
                return ['code' => 0, 'msg' => $rateLimitResult['msg']];
            }

            // 生成验证码
            $code = $this->generateVerificationCode();

            // 获取短信模板
            $template = $this->getTemplate($type);
            if (!$template) {
                return ['code' => 0, 'msg' => '短信模板不存在'];
            }

            // 发送短信
            $result = $this->sendSms($phone, $template, ['code' => $code]);

            if ($result['success']) {
                // 保存验证码到缓存
                $this->saveVerificationCode($phone, $code, $type);
                
                // 记录发送日志
                $this->logSmsRecord($phone, $type, $code, 'success');

                return [
                    'code' => 1,
                    'msg' => '验证码发送成功',
                    'data' => [
                        'phone' => $this->maskPhone($phone),
                        'expires_in' => 300 // 5分钟
                    ]
                ];
            } else {
                $this->logSmsRecord($phone, $type, $code, 'failed', $result['error']);
                return ['code' => 0, 'msg' => '验证码发送失败：' . $result['error']];
            }

        } catch (\Exception $e) {
            Log::error('SMS发送异常：' . $e->getMessage());
            return ['code' => 0, 'msg' => '系统异常，请稍后重试'];
        }
    }

    /**
     * 验证验证码
     */
    public function verifyCode(string $phone, string $code, string $type = self::TYPE_REGISTER): array
    {
        try {
            $cacheKey = "sms_code:{$type}:{$phone}";
            $savedCode = Cache::get($cacheKey);

            if (!$savedCode) {
                return ['code' => 0, 'msg' => '验证码已过期或不存在'];
            }

            if ($savedCode !== $code) {
                // 记录验证失败
                $this->logVerificationAttempt($phone, $code, $type, false);
                return ['code' => 0, 'msg' => '验证码错误'];
            }

            // 验证成功，删除验证码
            Cache::delete($cacheKey);
            $this->logVerificationAttempt($phone, $code, $type, true);

            return [
                'code' => 1,
                'msg' => '验证成功',
                'data' => [
                    'phone' => $phone,
                    'verified_at' => date('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            Log::error('验证码验证异常：' . $e->getMessage());
            return ['code' => 0, 'msg' => '验证失败'];
        }
    }

    /**
     * 发送短信（根据不同服务商）
     */
    private function sendSms(string $phone, array $template, array $params = []): array
    {
        switch ($this->provider) {
            case self::PROVIDER_ALIYUN:
                return $this->sendAliyunSms($phone, $template, $params);
            case self::PROVIDER_TENCENT:
                return $this->sendTencentSms($phone, $template, $params);
            case self::PROVIDER_HUAWEI:
                return $this->sendHuaweiSms($phone, $template, $params);
            case self::PROVIDER_TWILIO:
                return $this->sendTwilioSms($phone, $template, $params);
            default:
                return ['success' => false, 'error' => '不支持的SMS服务商'];
        }
    }

    /**
     * 阿里云短信发送
     */
    private function sendAliyunSms(string $phone, array $template, array $params): array
    {
        try {
            $config = $this->config['providers']['aliyun'];
            
            // 构建请求参数
            $requestParams = [
                'PhoneNumbers' => $phone,
                'SignName' => $config['sign_name'],
                'TemplateCode' => $template['template_id'],
                'TemplateParam' => json_encode($params),
                'RegionId' => 'cn-hangzhou',
                'Action' => 'SendSms',
                'Version' => '2017-05-25',
                'AccessKeyId' => $config['access_key_id'],
                'SignatureMethod' => 'HMAC-SHA1',
                'Timestamp' => gmdate('Y-m-d\TH:i:s\Z'),
                'SignatureVersion' => '1.0',
                'SignatureNonce' => uniqid(),
                'Format' => 'JSON'
            ];

            // 生成签名
            $signature = $this->generateAliyunSignature($requestParams, $config['access_key_secret']);
            $requestParams['Signature'] = $signature;

            // 发送HTTP请求
            $url = 'https://dysmsapi.aliyuncs.com/?' . http_build_query($requestParams);
            $response = file_get_contents($url);
            $result = json_decode($response, true);

            if ($result && $result['Code'] === 'OK') {
                return ['success' => true, 'message_id' => $result['BizId']];
            } else {
                return ['success' => false, 'error' => $result['Message'] ?? '发送失败'];
            }

        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * 腾讯云短信发送
     */
    private function sendTencentSms(string $phone, array $template, array $params): array
    {
        try {
            $config = $this->config['providers']['tencent'];
            
            // 腾讯云SMS API调用逻辑
            // 这里简化实现，实际需要使用腾讯云SDK
            
            return ['success' => true, 'message_id' => 'tencent_' . time()];

        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * 华为云短信发送
     */
    private function sendHuaweiSms(string $phone, array $template, array $params): array
    {
        try {
            $config = $this->config['providers']['huawei'];
            
            // 华为云SMS API调用逻辑
            // 这里简化实现，实际需要使用华为云SDK
            
            return ['success' => true, 'message_id' => 'huawei_' . time()];

        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Twilio短信发送（国际短信）
     */
    private function sendTwilioSms(string $phone, array $template, array $params): array
    {
        try {
            $config = $this->config['providers']['twilio'];
            
            $message = str_replace(array_keys($params), array_values($params), $template['content']);
            
            $data = [
                'From' => $config['from_number'],
                'To' => $phone,
                'Body' => $message
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "https://api.twilio.com/2010-04-01/Accounts/{$config['account_sid']}/Messages.json");
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_USERPWD, $config['account_sid'] . ':' . $config['auth_token']);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode === 201) {
                $result = json_decode($response, true);
                return ['success' => true, 'message_id' => $result['sid']];
            } else {
                return ['success' => false, 'error' => 'HTTP ' . $httpCode];
            }

        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * 生成阿里云签名
     */
    private function generateAliyunSignature(array $params, string $accessKeySecret): string
    {
        ksort($params);
        $stringToSign = 'GET&%2F&' . urlencode(http_build_query($params, '', '&', PHP_QUERY_RFC3986));
        return base64_encode(hash_hmac('sha1', $stringToSign, $accessKeySecret . '&', true));
    }

    /**
     * 检查发送频率限制
     */
    private function checkRateLimit(string $phone, string $type): array
    {
        $cacheKey = "sms_rate_limit:{$phone}";
        $attempts = Cache::get($cacheKey, 0);

        // 每小时最多发送5次
        if ($attempts >= 5) {
            return ['allow' => false, 'msg' => '发送过于频繁，请1小时后再试'];
        }

        // 检查最近一次发送时间（60秒内不能重复发送）
        $lastSendKey = "sms_last_send:{$type}:{$phone}";
        $lastSendTime = Cache::get($lastSendKey);
        if ($lastSendTime && (time() - $lastSendTime) < 60) {
            return ['allow' => false, 'msg' => '请60秒后再试'];
        }

        return ['allow' => true, 'msg' => ''];
    }

    /**
     * 保存验证码到缓存
     */
    private function saveVerificationCode(string $phone, string $code, string $type): void
    {
        $cacheKey = "sms_code:{$type}:{$phone}";
        Cache::set($cacheKey, $code, 300); // 5分钟有效期

        // 更新发送频率计数
        $rateLimitKey = "sms_rate_limit:{$phone}";
        $attempts = Cache::get($rateLimitKey, 0);
        Cache::set($rateLimitKey, $attempts + 1, 3600); // 1小时

        // 记录最后发送时间
        $lastSendKey = "sms_last_send:{$type}:{$phone}";
        Cache::set($lastSendKey, time(), 3600);
    }

    /**
     * 生成验证码
     */
    private function generateVerificationCode(int $length = 6): string
    {
        return str_pad((string)mt_rand(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
    }

    /**
     * 验证手机号格式
     */
    private function validatePhone(string $phone): bool
    {
        // 支持国际手机号格式
        return preg_match('/^(\+\d{1,3}[- ]?)?\d{10,11}$/', $phone);
    }

    /**
     * 手机号脱敏
     */
    private function maskPhone(string $phone): string
    {
        if (strlen($phone) >= 7) {
            return substr($phone, 0, 3) . '****' . substr($phone, -4);
        }
        return $phone;
    }

    /**
     * 获取短信模板
     */
    private function getTemplate(string $type): ?array
    {
        $templates = [
            self::TYPE_REGISTER => [
                'template_id' => 'SMS_001',
                'content' => '您的注册验证码是：{code}，5分钟内有效。'
            ],
            self::TYPE_LOGIN => [
                'template_id' => 'SMS_002',
                'content' => '您的登录验证码是：{code}，5分钟内有效。'
            ],
            self::TYPE_RESET_PASSWORD => [
                'template_id' => 'SMS_003',
                'content' => '您的密码重置验证码是：{code}，5分钟内有效。'
            ],
            self::TYPE_WITHDRAW => [
                'template_id' => 'SMS_004',
                'content' => '您的提现验证码是：{code}，5分钟内有效。'
            ],
            self::TYPE_SECURITY => [
                'template_id' => 'SMS_005',
                'content' => '您的安全验证码是：{code}，5分钟内有效。'
            ]
        ];

        return $templates[$type] ?? null;
    }

    /**
     * 记录短信发送日志
     */
    private function logSmsRecord(string $phone, string $type, string $code, string $status, string $error = ''): void
    {
        try {
            Db::name('sms_records')->insert([
                'phone' => $phone,
                'type' => $type,
                'code' => $code,
                'status' => $status,
                'error_msg' => $error,
                'provider' => $this->provider,
                'ip' => request()->ip(),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            Log::error('记录短信日志失败：' . $e->getMessage());
        }
    }

    /**
     * 记录验证尝试日志
     */
    private function logVerificationAttempt(string $phone, string $code, string $type, bool $success): void
    {
        try {
            Db::name('sms_verifications')->insert([
                'phone' => $phone,
                'code' => $code,
                'type' => $type,
                'success' => $success ? 1 : 0,
                'ip' => request()->ip(),
                'user_agent' => request()->header('user-agent'),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            Log::error('记录验证日志失败：' . $e->getMessage());
        }
    }
}
