<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

/**
 * 期权交易服务类
 * 支持欧式期权和美式期权
 */
class OptionsService
{
    // 期权类型
    const TYPE_CALL = 'call';   // 看涨期权
    const TYPE_PUT = 'put';     // 看跌期权

    // 期权风格
    const STYLE_EUROPEAN = 'european'; // 欧式期权
    const STYLE_AMERICAN = 'american'; // 美式期权

    // 订单状态
    const STATUS_PENDING = 'pending';
    const STATUS_FILLED = 'filled';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_EXPIRED = 'expired';

    // 持仓状态
    const POSITION_OPEN = 'open';
    const POSITION_EXERCISED = 'exercised';
    const POSITION_EXPIRED = 'expired';
    const POSITION_CLOSED = 'closed';

    private $assetService;
    private $riskControlService;

    public function __construct()
    {
        $this->assetService = new AssetService();
        $this->riskControlService = new RiskControlService();
    }

    /**
     * 创建期权订单
     */
    public function createOrder(array $data): array
    {
        try {
            Db::startTrans();

            // 验证期权合约
            $contract = $this->getOptionContract($data['contract_id']);
            if (!$contract) {
                throw new \Exception('期权合约不存在或已停用');
            }

            // 检查合约是否已过期
            if (strtotime($contract['expiry_date']) <= time()) {
                throw new \Exception('期权合约已过期');
            }

            // 风控检测
            $riskResult = $this->riskControlService->checkTradeRisk($data['user_id'], $data);
            if ($riskResult['code'] === 1 && !$riskResult['data']['allow_trade']) {
                throw new \Exception('操作被风控系统阻止');
            }

            // 计算期权价格
            $optionPrice = $this->calculateOptionPrice($contract, $data);

            // 验证用户资产
            $requiredAmount = $data['quantity'] * $optionPrice;
            $userAsset = $this->assetService->getUserAsset($data['user_id'], 'USDT');
            if ($userAsset['available'] < $requiredAmount) {
                throw new \Exception('余额不足');
            }

            // 生成订单ID
            $orderId = 'OPT' . date('YmdHis') . mt_rand(1000, 9999);

            // 创建订单
            $orderData = [
                'order_id' => $orderId,
                'user_id' => $data['user_id'],
                'contract_id' => $data['contract_id'],
                'side' => $data['side'], // buy/sell
                'quantity' => $data['quantity'],
                'price' => $optionPrice,
                'total_amount' => $requiredAmount,
                'status' => self::STATUS_PENDING,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            Db::name('option_orders')->insert($orderData);

            // 冻结资产
            $this->assetService->freezeAsset($data['user_id'], 'USDT', $requiredAmount);

            // 记录财务流水
            Db::name('financial_records')->insert([
                'user_id' => $data['user_id'],
                'coin_symbol' => 'USDT',
                'amount' => -$requiredAmount,
                'type' => 'option_premium',
                'remark' => '期权权利金',
                'order_id' => $orderId,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            // 立即撮合（简化处理）
            $this->matchOrder($orderId);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '期权订单创建成功',
                'data' => [
                    'order_id' => $orderId,
                    'contract_symbol' => $contract['symbol'],
                    'option_type' => $contract['option_type'],
                    'strike_price' => $contract['strike_price'],
                    'expiry_date' => $contract['expiry_date'],
                    'quantity' => $data['quantity'],
                    'premium' => $optionPrice,
                    'total_amount' => $requiredAmount
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('创建期权订单失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 行权期权
     */
    public function exerciseOption(int $positionId, int $userId): array
    {
        try {
            Db::startTrans();

            // 获取期权持仓
            $position = Db::name('option_positions')
                ->where('id', $positionId)
                ->where('user_id', $userId)
                ->where('status', self::POSITION_OPEN)
                ->find();

            if (!$position) {
                throw new \Exception('期权持仓不存在或已处理');
            }

            // 获取期权合约
            $contract = $this->getOptionContract($position['contract_id']);
            if (!$contract) {
                throw new \Exception('期权合约不存在');
            }

            // 检查是否可以行权
            $canExercise = $this->canExercise($contract, $position);
            if (!$canExercise['can']) {
                throw new \Exception($canExercise['reason']);
            }

            // 计算行权收益
            $exerciseResult = $this->calculateExerciseProfit($contract, $position);

            if ($exerciseResult['profit'] > 0) {
                // 有收益，执行行权
                $this->executeExercise($position, $contract, $exerciseResult);
                
                $message = '期权行权成功';
            } else {
                // 无收益，放弃行权
                $this->abandonExercise($position);
                
                $message = '期权已放弃行权';
            }

            // 更新持仓状态
            Db::name('option_positions')
                ->where('id', $positionId)
                ->update([
                    'status' => self::POSITION_EXERCISED,
                    'exercise_price' => $exerciseResult['current_price'],
                    'exercise_profit' => $exerciseResult['profit'],
                    'exercised_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            Db::commit();

            return [
                'code' => 1,
                'msg' => $message,
                'data' => [
                    'position_id' => $positionId,
                    'exercise_price' => $exerciseResult['current_price'],
                    'exercise_profit' => $exerciseResult['profit'],
                    'total_profit' => $exerciseResult['profit'] - $position['premium_paid']
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('期权行权失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 获取期权合约列表
     */
    public function getContracts(array $params = []): array
    {
        try {
            $query = Db::name('option_contracts')
                ->where('status', 1)
                ->where('expiry_date', '>', date('Y-m-d H:i:s'));

            // 筛选条件
            if (!empty($params['underlying_asset'])) {
                $query->where('underlying_asset', $params['underlying_asset']);
            }

            if (!empty($params['option_type'])) {
                $query->where('option_type', $params['option_type']);
            }

            if (!empty($params['expiry_date'])) {
                $query->where('expiry_date', $params['expiry_date']);
            }

            $contracts = $query->order('expiry_date asc, strike_price asc')
                ->select()
                ->toArray();

            // 计算期权价格和希腊字母
            foreach ($contracts as &$contract) {
                $pricing = $this->calculateOptionPricing($contract);
                $contract['current_price'] = $pricing['price'];
                $contract['delta'] = $pricing['delta'];
                $contract['gamma'] = $pricing['gamma'];
                $contract['theta'] = $pricing['theta'];
                $contract['vega'] = $pricing['vega'];
                $contract['implied_volatility'] = $pricing['iv'];
            }

            return [
                'code' => 1,
                'msg' => '获取成功',
                'data' => $contracts
            ];

        } catch (\Exception $e) {
            Log::error('获取期权合约失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 获取用户期权持仓
     */
    public function getUserPositions(int $userId, array $params = []): array
    {
        try {
            $query = Db::name('option_positions')
                ->alias('op')
                ->join('option_contracts oc', 'op.contract_id = oc.id')
                ->field('op.*, oc.symbol, oc.underlying_asset, oc.option_type, oc.strike_price, oc.expiry_date')
                ->where('op.user_id', $userId);

            // 状态筛选
            if (!empty($params['status'])) {
                $query->where('op.status', $params['status']);
            }

            // 期权类型筛选
            if (!empty($params['option_type'])) {
                $query->where('oc.option_type', $params['option_type']);
            }

            $positions = $query->order('op.id desc')
                ->select()
                ->toArray();

            // 计算当前盈亏
            foreach ($positions as &$position) {
                if ($position['status'] === self::POSITION_OPEN) {
                    $currentValue = $this->calculateCurrentValue($position);
                    $position['current_value'] = $currentValue;
                    $position['unrealized_pnl'] = $currentValue - $position['premium_paid'];
                    $position['unrealized_pnl_rate'] = ($position['unrealized_pnl'] / $position['premium_paid']) * 100;
                }
            }

            return [
                'code' => 1,
                'msg' => '获取成功',
                'data' => $positions
            ];

        } catch (\Exception $e) {
            Log::error('获取用户期权持仓失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 计算期权价格（Black-Scholes模型）
     */
    private function calculateOptionPrice(array $contract, array $orderData): float
    {
        $S = $this->getCurrentPrice($contract['underlying_asset']); // 标的价格
        $K = $contract['strike_price']; // 行权价
        $T = (strtotime($contract['expiry_date']) - time()) / (365 * 24 * 3600); // 到期时间（年）
        $r = 0.05; // 无风险利率
        $sigma = $this->getImpliedVolatility($contract['underlying_asset']); // 隐含波动率

        if ($contract['option_type'] === self::TYPE_CALL) {
            return $this->blackScholesCall($S, $K, $T, $r, $sigma);
        } else {
            return $this->blackScholesPut($S, $K, $T, $r, $sigma);
        }
    }

    /**
     * Black-Scholes看涨期权定价
     */
    private function blackScholesCall(float $S, float $K, float $T, float $r, float $sigma): float
    {
        if ($T <= 0) return max($S - $K, 0);

        $d1 = (log($S / $K) + ($r + 0.5 * $sigma * $sigma) * $T) / ($sigma * sqrt($T));
        $d2 = $d1 - $sigma * sqrt($T);

        $callPrice = $S * $this->normalCDF($d1) - $K * exp(-$r * $T) * $this->normalCDF($d2);

        return max($callPrice, 0);
    }

    /**
     * Black-Scholes看跌期权定价
     */
    private function blackScholesPut(float $S, float $K, float $T, float $r, float $sigma): float
    {
        if ($T <= 0) return max($K - $S, 0);

        $d1 = (log($S / $K) + ($r + 0.5 * $sigma * $sigma) * $T) / ($sigma * sqrt($T));
        $d2 = $d1 - $sigma * sqrt($T);

        $putPrice = $K * exp(-$r * $T) * $this->normalCDF(-$d2) - $S * $this->normalCDF(-$d1);

        return max($putPrice, 0);
    }

    /**
     * 标准正态分布累积分布函数
     */
    private function normalCDF(float $x): float
    {
        return 0.5 * (1 + $this->erf($x / sqrt(2)));
    }

    /**
     * 误差函数近似
     */
    private function erf(float $x): float
    {
        $a1 =  0.254829592;
        $a2 = -0.284496736;
        $a3 =  1.421413741;
        $a4 = -1.453152027;
        $a5 =  1.061405429;
        $p  =  0.3275911;

        $sign = $x < 0 ? -1 : 1;
        $x = abs($x);

        $t = 1.0 / (1.0 + $p * $x);
        $y = 1.0 - ((((($a5 * $t + $a4) * $t) + $a3) * $t + $a2) * $t + $a1) * $t * exp(-$x * $x);

        return $sign * $y;
    }

    /**
     * 计算期权定价和希腊字母
     */
    private function calculateOptionPricing(array $contract): array
    {
        $S = $this->getCurrentPrice($contract['underlying_asset']);
        $K = $contract['strike_price'];
        $T = (strtotime($contract['expiry_date']) - time()) / (365 * 24 * 3600);
        $r = 0.05;
        $sigma = $this->getImpliedVolatility($contract['underlying_asset']);

        $price = $contract['option_type'] === self::TYPE_CALL ?
            $this->blackScholesCall($S, $K, $T, $r, $sigma) :
            $this->blackScholesPut($S, $K, $T, $r, $sigma);

        // 计算希腊字母
        $greeks = $this->calculateGreeks($S, $K, $T, $r, $sigma, $contract['option_type']);

        return [
            'price' => $price,
            'delta' => $greeks['delta'],
            'gamma' => $greeks['gamma'],
            'theta' => $greeks['theta'],
            'vega' => $greeks['vega'],
            'iv' => $sigma
        ];
    }

    /**
     * 计算希腊字母
     */
    private function calculateGreeks(float $S, float $K, float $T, float $r, float $sigma, string $optionType): array
    {
        if ($T <= 0) {
            return ['delta' => 0, 'gamma' => 0, 'theta' => 0, 'vega' => 0];
        }

        $d1 = (log($S / $K) + ($r + 0.5 * $sigma * $sigma) * $T) / ($sigma * sqrt($T));
        $d2 = $d1 - $sigma * sqrt($T);

        $nd1 = $this->normalCDF($d1);
        $nd2 = $this->normalCDF($d2);
        $npd1 = exp(-0.5 * $d1 * $d1) / sqrt(2 * M_PI);

        if ($optionType === self::TYPE_CALL) {
            $delta = $nd1;
            $theta = (-$S * $npd1 * $sigma / (2 * sqrt($T)) - $r * $K * exp(-$r * $T) * $nd2) / 365;
        } else {
            $delta = $nd1 - 1;
            $theta = (-$S * $npd1 * $sigma / (2 * sqrt($T)) + $r * $K * exp(-$r * $T) * (1 - $nd2)) / 365;
        }

        $gamma = $npd1 / ($S * $sigma * sqrt($T));
        $vega = $S * $npd1 * sqrt($T) / 100;

        return [
            'delta' => round($delta, 4),
            'gamma' => round($gamma, 4),
            'theta' => round($theta, 4),
            'vega' => round($vega, 4)
        ];
    }

    /**
     * 撮合订单
     */
    private function matchOrder(string $orderId): void
    {
        // 简化处理，直接成交
        $order = Db::name('option_orders')->where('order_id', $orderId)->find();
        
        if ($order && $order['status'] === self::STATUS_PENDING) {
            // 更新订单状态
            Db::name('option_orders')
                ->where('order_id', $orderId)
                ->update([
                    'status' => self::STATUS_FILLED,
                    'filled_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            // 创建持仓
            if ($order['side'] === 'buy') {
                Db::name('option_positions')->insert([
                    'user_id' => $order['user_id'],
                    'contract_id' => $order['contract_id'],
                    'quantity' => $order['quantity'],
                    'premium_paid' => $order['total_amount'],
                    'status' => self::POSITION_OPEN,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }

            // 解冻资产
            $this->assetService->unfreezeAsset($order['user_id'], 'USDT', $order['total_amount']);
        }
    }

    /**
     * 获取期权合约
     */
    private function getOptionContract(int $contractId): ?array
    {
        return Db::name('option_contracts')
            ->where('id', $contractId)
            ->where('status', 1)
            ->find();
    }

    /**
     * 获取当前价格
     */
    private function getCurrentPrice(string $symbol): float
    {
        // 从缓存或API获取当前价格
        $cacheKey = "current_price:{$symbol}";
        $price = Cache::get($cacheKey);
        
        if (!$price) {
            // 模拟价格，实际应该从交易所API获取
            $price = 50000; // BTC价格示例
            Cache::set($cacheKey, $price, 60);
        }
        
        return (float)$price;
    }

    /**
     * 获取隐含波动率
     */
    private function getImpliedVolatility(string $symbol): float
    {
        // 简化处理，返回固定波动率
        // 实际应该根据历史数据计算或从市场获取
        return 0.25; // 25%年化波动率
    }

    /**
     * 检查是否可以行权
     */
    private function canExercise(array $contract, array $position): array
    {
        // 检查是否已过期
        if (strtotime($contract['expiry_date']) <= time()) {
            return ['can' => false, 'reason' => '期权已过期'];
        }

        // 欧式期权只能在到期日行权
        if ($contract['style'] === self::STYLE_EUROPEAN) {
            $expiryDate = date('Y-m-d', strtotime($contract['expiry_date']));
            $today = date('Y-m-d');
            
            if ($expiryDate !== $today) {
                return ['can' => false, 'reason' => '欧式期权只能在到期日行权'];
            }
        }

        return ['can' => true, 'reason' => ''];
    }

    /**
     * 计算行权收益
     */
    private function calculateExerciseProfit(array $contract, array $position): array
    {
        $currentPrice = $this->getCurrentPrice($contract['underlying_asset']);
        $strikePrice = $contract['strike_price'];
        
        if ($contract['option_type'] === self::TYPE_CALL) {
            $profit = max($currentPrice - $strikePrice, 0) * $position['quantity'];
        } else {
            $profit = max($strikePrice - $currentPrice, 0) * $position['quantity'];
        }

        return [
            'current_price' => $currentPrice,
            'profit' => $profit
        ];
    }

    /**
     * 执行行权
     */
    private function executeExercise(array $position, array $contract, array $exerciseResult): void
    {
        if ($exerciseResult['profit'] > 0) {
            // 增加用户资产
            $this->assetService->addAsset($position['user_id'], 'USDT', $exerciseResult['profit']);
            
            // 记录财务流水
            Db::name('financial_records')->insert([
                'user_id' => $position['user_id'],
                'coin_symbol' => 'USDT',
                'amount' => $exerciseResult['profit'],
                'type' => 'option_exercise',
                'remark' => '期权行权收益',
                'order_id' => $position['id'],
                'created_at' => date('Y-m-d H:i:s')
            ]);
        }
    }

    /**
     * 放弃行权
     */
    private function abandonExercise(array $position): void
    {
        // 期权到期无价值，不需要额外操作
        Log::info("期权持仓 {$position['id']} 放弃行权");
    }

    /**
     * 计算当前价值
     */
    private function calculateCurrentValue(array $position): float
    {
        $contract = $this->getOptionContract($position['contract_id']);
        if (!$contract) {
            return 0;
        }

        return $this->calculateOptionPrice($contract, []) * $position['quantity'];
    }
}
