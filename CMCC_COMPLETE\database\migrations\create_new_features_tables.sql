-- GVD交易平台新功能数据库表结构
-- 创建时间：2024-01-15

-- 1. 平台充值地址配置表
CREATE TABLE `platform_addresses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `coin_symbol` varchar(20) NOT NULL COMMENT '币种符号',
  `address` varchar(255) NOT NULL COMMENT '地址',
  `type` enum('deposit','withdraw') NOT NULL DEFAULT 'deposit' COMMENT '地址类型',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_coin_type` (`coin_symbol`, `type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='平台地址配置表';

-- 2. 用户钱包表（增加用户地址字段）
-- 注意：如果使用gvd_user_assets表，请修改表名
ALTER TABLE `gvd_user_assets`
ADD COLUMN `user_address` varchar(255) DEFAULT NULL COMMENT '用户绑定的钱包地址' AFTER `coin_symbol`,
ADD INDEX `idx_user_address` (`user_address`);

-- 3. 充值记录表
CREATE TABLE `deposit_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `coin_symbol` varchar(20) NOT NULL COMMENT '币种符号',
  `amount` decimal(20,8) NOT NULL COMMENT '充值金额',
  `tx_hash` varchar(255) NOT NULL COMMENT '交易哈希',
  `from_address` varchar(255) DEFAULT NULL COMMENT '发送地址',
  `to_address` varchar(255) DEFAULT NULL COMMENT '接收地址',
  `confirmations` int(11) NOT NULL DEFAULT 0 COMMENT '确认数',
  `required_confirmations` int(11) NOT NULL DEFAULT 6 COMMENT '所需确认数',
  `status` enum('pending','success','failed','cancelled') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `confirmed_at` timestamp NULL DEFAULT NULL COMMENT '确认时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tx_hash` (`tx_hash`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_coin_symbol` (`coin_symbol`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值记录表';

-- 4. 用户控制策略表
CREATE TABLE `user_controls` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `control_type` enum('none','win','lose','random') NOT NULL DEFAULT 'none' COMMENT '控制类型',
  `control_level` enum('user','agent','global') NOT NULL DEFAULT 'user' COMMENT '控制级别',
  `win_rate` decimal(5,2) NOT NULL DEFAULT 50.00 COMMENT '胜率百分比',
  `max_win_amount` decimal(20,8) NOT NULL DEFAULT 0 COMMENT '最大盈利金额',
  `max_lose_amount` decimal(20,8) NOT NULL DEFAULT 0 COMMENT '最大亏损金额',
  `valid_until` timestamp NULL DEFAULT NULL COMMENT '有效期',
  `operator_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作员ID',
  `operator_type` enum('admin','agent','system') NOT NULL DEFAULT 'system' COMMENT '操作员类型',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_control_type` (`control_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户控制策略表';

-- 5. 代理控制策略表
CREATE TABLE `agent_controls` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `agent_id` int(11) NOT NULL COMMENT '代理ID',
  `control_type` enum('none','win','lose','random') NOT NULL DEFAULT 'none' COMMENT '控制类型',
  `win_rate` decimal(5,2) NOT NULL DEFAULT 50.00 COMMENT '胜率百分比',
  `max_win_amount` decimal(20,8) NOT NULL DEFAULT 0 COMMENT '最大盈利金额',
  `max_lose_amount` decimal(20,8) NOT NULL DEFAULT 0 COMMENT '最大亏损金额',
  `operator_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作员ID',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代理控制策略表';

-- 6. 控制操作日志表
CREATE TABLE `control_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `operator_id` int(11) NOT NULL COMMENT '操作员ID',
  `operator_type` enum('admin','agent','system') NOT NULL COMMENT '操作员类型',
  `operation` varchar(50) NOT NULL COMMENT '操作类型',
  `control_type` enum('none','win','lose','random') NOT NULL COMMENT '控制类型',
  `options` text COMMENT '操作选项JSON',
  `ip` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_operator` (`operator_id`, `operator_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='控制操作日志表';

-- 7. 结算日志表
CREATE TABLE `settlement_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `control_type` enum('none','win','lose','random') NOT NULL COMMENT '控制类型',
  `market_price` decimal(20,8) NOT NULL COMMENT '市场价格',
  `settlement_price` decimal(20,8) NOT NULL COMMENT '结算价格',
  `pnl` decimal(20,8) NOT NULL COMMENT '盈亏',
  `controlled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否被控制',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_controlled` (`controlled`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='结算日志表';

-- 8. 管理员操作记录表
CREATE TABLE `admin_operations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `operator_id` int(11) NOT NULL COMMENT '操作员ID',
  `operator_type` enum('admin','agent','system') NOT NULL COMMENT '操作员类型',
  `operation` varchar(50) NOT NULL COMMENT '操作类型',
  `data` text COMMENT '操作数据JSON',
  `ip` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_operator` (`operator_id`, `operator_type`),
  KEY `idx_operation` (`operation`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员操作记录表';

-- 9. 财务记录表
CREATE TABLE `financial_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(50) NOT NULL COMMENT '记录类型',
  `amount` decimal(20,8) NOT NULL COMMENT '金额',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `related_id` int(11) DEFAULT NULL COMMENT '关联ID',
  `related_type` varchar(50) DEFAULT NULL COMMENT '关联类型',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_related` (`related_id`, `related_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务记录表';

-- 10. 代理佣金表
CREATE TABLE `agent_commissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `agent_id` int(11) NOT NULL COMMENT '代理ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `order_id` int(11) DEFAULT NULL COMMENT '订单ID',
  `amount` decimal(20,8) NOT NULL COMMENT '佣金金额',
  `commission_rate` decimal(5,4) NOT NULL COMMENT '佣金比例',
  `status` enum('pending','available','paid') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `paid_at` timestamp NULL DEFAULT NULL COMMENT '支付时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代理佣金表';

-- 11. 钱包操作日志表
CREATE TABLE `wallet_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `operation` varchar(50) NOT NULL COMMENT '操作类型',
  `data` text COMMENT '操作数据JSON',
  `ip` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_operation` (`operation`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='钱包操作日志表';

-- 12. 系统告警表
CREATE TABLE `system_alerts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(50) NOT NULL COMMENT '告警类型',
  `level` enum('info','warning','error','critical') NOT NULL COMMENT '告警级别',
  `message` varchar(500) NOT NULL COMMENT '告警消息',
  `data` text COMMENT '告警数据JSON',
  `status` enum('new','processing','resolved','ignored') NOT NULL DEFAULT 'new' COMMENT '处理状态',
  `resolved_at` timestamp NULL DEFAULT NULL COMMENT '解决时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_level` (`level`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统告警表';

-- 13. 系统监控指标表
CREATE TABLE `system_metrics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(50) NOT NULL COMMENT '指标类型',
  `data` text NOT NULL COMMENT '指标数据JSON',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统监控指标表';

-- 14. 用户消息表
CREATE TABLE `user_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `title` varchar(255) NOT NULL COMMENT '消息标题',
  `content` text NOT NULL COMMENT '消息内容',
  `type` varchar(50) NOT NULL COMMENT '消息类型',
  `is_read` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读',
  `read_at` timestamp NULL DEFAULT NULL COMMENT '阅读时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户消息表';

-- 15. 更新用户表，添加用户类型字段
ALTER TABLE `users` 
ADD COLUMN `user_type` enum('normal','test') NOT NULL DEFAULT 'normal' COMMENT '用户类型' AFTER `status`,
ADD COLUMN `freeze_reason` varchar(255) DEFAULT NULL COMMENT '冻结原因' AFTER `user_type`,
ADD COLUMN `created_by` int(11) NOT NULL DEFAULT 0 COMMENT '创建者ID' AFTER `freeze_reason`,
ADD INDEX `idx_user_type` (`user_type`),
ADD INDEX `idx_created_by` (`created_by`);

-- 16. 更新合约订单表，添加开盘价和收盘价字段
ALTER TABLE `contract_orders` 
ADD COLUMN `open_price` decimal(20,8) NOT NULL DEFAULT 0 COMMENT '开盘价' AFTER `price`,
ADD COLUMN `close_price` decimal(20,8) NOT NULL DEFAULT 0 COMMENT '收盘价' AFTER `open_price`,
ADD COLUMN `pnl` decimal(20,8) NOT NULL DEFAULT 0 COMMENT '盈亏' AFTER `close_price`,
ADD COLUMN `closed_at` timestamp NULL DEFAULT NULL COMMENT '平仓时间' AFTER `pnl`;

-- 插入默认平台地址配置
INSERT INTO `platform_addresses` (`coin_symbol`, `address`, `type`, `status`, `remark`) VALUES
('USDT', '******************************************', 'deposit', 1, 'USDT充值地址'),
('BTC', '**********************************', 'deposit', 1, 'BTC充值地址'),
('ETH', '******************************************', 'deposit', 1, 'ETH充值地址');

-- 插入默认管理员权限
UPDATE `admins` SET `permissions` = '["balance_manage","user_manage","trading_control","system_monitor","financial_report"]' WHERE `role` = 'super_admin';

-- 创建索引优化查询性能（使用正确的表名）
CREATE INDEX `idx_user_assets_user_coin` ON `gvd_user_assets` (`user_id`, `coin_symbol`);
CREATE INDEX `idx_wallet_transactions_user_type` ON `gvd_wallet_transactions` (`user_id`, `type`);
CREATE INDEX `idx_orders_user_status` ON `gvd_orders` (`user_id`, `status`);
CREATE INDEX `idx_contract_orders_user_status` ON `gvd_contract_orders` (`user_id`, `status`);
