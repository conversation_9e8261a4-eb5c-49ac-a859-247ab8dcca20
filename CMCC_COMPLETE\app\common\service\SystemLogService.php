<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

/**
 * 系统日志服务类
 * 统一管理系统各类日志
 */
class SystemLogService
{
    // 日志级别
    const LEVEL_DEBUG = 'debug';
    const LEVEL_INFO = 'info';
    const LEVEL_WARNING = 'warning';
    const LEVEL_ERROR = 'error';
    const LEVEL_CRITICAL = 'critical';

    // 日志类型
    const TYPE_SYSTEM = 'system';
    const TYPE_USER = 'user';
    const TYPE_TRADING = 'trading';
    const TYPE_SECURITY = 'security';
    const TYPE_API = 'api';
    const TYPE_ADMIN = 'admin';
    const TYPE_ERROR = 'error';
    const TYPE_PERFORMANCE = 'performance';

    // 日志状态
    const STATUS_NORMAL = 'normal';
    const STATUS_PROCESSED = 'processed';
    const STATUS_IGNORED = 'ignored';

    /**
     * 记录系统日志
     */
    public function log(string $type, string $level, string $message, array $context = []): bool
    {
        try {
            $logData = [
                'type' => $type,
                'level' => $level,
                'message' => $message,
                'context' => json_encode($context, JSON_UNESCAPED_UNICODE),
                'user_id' => $context['user_id'] ?? 0,
                'ip' => $context['ip'] ?? request()->ip(),
                'user_agent' => $context['user_agent'] ?? request()->header('user-agent'),
                'url' => $context['url'] ?? request()->url(),
                'method' => $context['method'] ?? request()->method(),
                'status' => self::STATUS_NORMAL,
                'created_at' => date('Y-m-d H:i:s')
            ];

            // 写入数据库
            Db::name('system_logs')->insert($logData);

            // 写入文件日志
            $this->writeToFile($type, $level, $message, $context);

            // 实时告警检查
            $this->checkAlerts($type, $level, $message, $context);

            return true;

        } catch (\Exception $e) {
            // 避免日志记录失败导致的循环错误
            error_log("SystemLogService::log failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取日志列表
     */
    public function getLogs(array $params = []): array
    {
        try {
            $query = Db::name('system_logs');

            // 筛选条件
            if (!empty($params['type'])) {
                $query->where('type', $params['type']);
            }

            if (!empty($params['level'])) {
                $query->where('level', $params['level']);
            }

            if (!empty($params['user_id'])) {
                $query->where('user_id', $params['user_id']);
            }

            if (!empty($params['keyword'])) {
                $query->where('message', 'like', '%' . $params['keyword'] . '%');
            }

            if (!empty($params['start_date'])) {
                $query->where('created_at', '>=', $params['start_date']);
            }

            if (!empty($params['end_date'])) {
                $query->where('created_at', '<=', $params['end_date']);
            }

            if (!empty($params['ip'])) {
                $query->where('ip', $params['ip']);
            }

            // 分页
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 50;

            $total = $query->count();
            $logs = $query->page($page, $limit)
                ->order('id desc')
                ->select()
                ->toArray();

            // 解析context
            foreach ($logs as &$log) {
                $log['context'] = json_decode($log['context'], true) ?: [];
            }

            return [
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'list' => $logs,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取日志列表失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 获取日志统计
     */
    public function getLogStats(array $params = []): array
    {
        try {
            $startDate = $params['start_date'] ?? date('Y-m-d', strtotime('-7 days'));
            $endDate = $params['end_date'] ?? date('Y-m-d');

            // 按级别统计
            $levelStats = Db::name('system_logs')
                ->where('created_at', 'between', [$startDate, $endDate])
                ->field('level, COUNT(*) as count')
                ->group('level')
                ->select()
                ->toArray();

            // 按类型统计
            $typeStats = Db::name('system_logs')
                ->where('created_at', 'between', [$startDate, $endDate])
                ->field('type, COUNT(*) as count')
                ->group('type')
                ->select()
                ->toArray();

            // 按日期统计
            $dateStats = Db::name('system_logs')
                ->where('created_at', 'between', [$startDate, $endDate])
                ->field('DATE(created_at) as date, COUNT(*) as count')
                ->group('DATE(created_at)')
                ->order('date asc')
                ->select()
                ->toArray();

            // 错误趋势
            $errorTrend = Db::name('system_logs')
                ->where('level', 'in', ['error', 'critical'])
                ->where('created_at', 'between', [$startDate, $endDate])
                ->field('DATE(created_at) as date, COUNT(*) as count')
                ->group('DATE(created_at)')
                ->order('date asc')
                ->select()
                ->toArray();

            // 热门IP
            $topIPs = Db::name('system_logs')
                ->where('created_at', 'between', [$startDate, $endDate])
                ->field('ip, COUNT(*) as count')
                ->group('ip')
                ->order('count desc')
                ->limit(10)
                ->select()
                ->toArray();

            return [
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'level_stats' => $levelStats,
                    'type_stats' => $typeStats,
                    'date_stats' => $dateStats,
                    'error_trend' => $errorTrend,
                    'top_ips' => $topIPs
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取日志统计失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 清理过期日志
     */
    public function cleanExpiredLogs(int $days = 30): array
    {
        try {
            $expireDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
            
            $deletedCount = Db::name('system_logs')
                ->where('created_at', '<', $expireDate)
                ->delete();

            return [
                'code' => 1,
                'msg' => '清理完成',
                'data' => ['deleted_count' => $deletedCount]
            ];

        } catch (\Exception $e) {
            Log::error('清理过期日志失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '清理失败'];
        }
    }

    /**
     * 导出日志
     */
    public function exportLogs(array $params = []): array
    {
        try {
            $query = Db::name('system_logs');

            // 应用筛选条件
            if (!empty($params['type'])) {
                $query->where('type', $params['type']);
            }

            if (!empty($params['level'])) {
                $query->where('level', $params['level']);
            }

            if (!empty($params['start_date'])) {
                $query->where('created_at', '>=', $params['start_date']);
            }

            if (!empty($params['end_date'])) {
                $query->where('created_at', '<=', $params['end_date']);
            }

            $logs = $query->order('id desc')
                ->limit(10000) // 限制导出数量
                ->select()
                ->toArray();

            if (empty($logs)) {
                return ['code' => 0, 'msg' => '没有可导出的日志'];
            }

            // 生成CSV内容
            $csvContent = $this->generateCsvContent($logs);

            return [
                'code' => 1,
                'msg' => '导出成功',
                'data' => [
                    'content' => $csvContent,
                    'filename' => 'system_logs_' . date('Y-m-d_H-i-s') . '.csv',
                    'count' => count($logs)
                ]
            ];

        } catch (\Exception $e) {
            Log::error('导出日志失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '导出失败'];
        }
    }

    /**
     * 分析异常日志
     */
    public function analyzeAnomalies(array $params = []): array
    {
        try {
            $startDate = $params['start_date'] ?? date('Y-m-d', strtotime('-24 hours'));
            $endDate = $params['end_date'] ?? date('Y-m-d H:i:s');

            $anomalies = [];

            // 1. 错误率异常
            $errorRate = $this->calculateErrorRate($startDate, $endDate);
            if ($errorRate > 5) { // 错误率超过5%
                $anomalies[] = [
                    'type' => 'high_error_rate',
                    'severity' => 'high',
                    'message' => "错误率异常：{$errorRate}%",
                    'details' => ['error_rate' => $errorRate]
                ];
            }

            // 2. 频繁IP访问
            $suspiciousIPs = $this->findSuspiciousIPs($startDate, $endDate);
            foreach ($suspiciousIPs as $ip) {
                $anomalies[] = [
                    'type' => 'suspicious_ip',
                    'severity' => 'medium',
                    'message' => "IP {$ip['ip']} 访问频率异常：{$ip['count']} 次",
                    'details' => $ip
                ];
            }

            // 3. 性能异常
            $slowRequests = $this->findSlowRequests($startDate, $endDate);
            if (!empty($slowRequests)) {
                $anomalies[] = [
                    'type' => 'slow_requests',
                    'severity' => 'medium',
                    'message' => "发现 " . count($slowRequests) . " 个慢请求",
                    'details' => $slowRequests
                ];
            }

            // 4. 安全事件
            $securityEvents = $this->findSecurityEvents($startDate, $endDate);
            foreach ($securityEvents as $event) {
                $anomalies[] = [
                    'type' => 'security_event',
                    'severity' => 'critical',
                    'message' => $event['message'],
                    'details' => $event
                ];
            }

            return [
                'code' => 1,
                'msg' => '分析完成',
                'data' => [
                    'anomalies' => $anomalies,
                    'count' => count($anomalies),
                    'period' => [$startDate, $endDate]
                ]
            ];

        } catch (\Exception $e) {
            Log::error('分析异常日志失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '分析失败'];
        }
    }

    /**
     * 写入文件日志
     */
    private function writeToFile(string $type, string $level, string $message, array $context): void
    {
        try {
            $logDir = runtime_path() . 'log' . DIRECTORY_SEPARATOR . date('Y-m-d');
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }

            $filename = $logDir . DIRECTORY_SEPARATOR . $type . '.log';
            $logLine = sprintf(
                "[%s] %s.%s: %s %s\n",
                date('Y-m-d H:i:s'),
                $type,
                strtoupper($level),
                $message,
                !empty($context) ? json_encode($context, JSON_UNESCAPED_UNICODE) : ''
            );

            file_put_contents($filename, $logLine, FILE_APPEND | LOCK_EX);

        } catch (\Exception $e) {
            // 静默处理文件写入错误
        }
    }

    /**
     * 检查告警
     */
    private function checkAlerts(string $type, string $level, string $message, array $context): void
    {
        // 关键错误立即告警
        if (in_array($level, ['error', 'critical'])) {
            $this->sendAlert($type, $level, $message, $context);
        }

        // 检查频率告警
        $this->checkFrequencyAlerts($type, $level);
    }

    /**
     * 发送告警
     */
    private function sendAlert(string $type, string $level, string $message, array $context): void
    {
        try {
            // 防止告警风暴
            $alertKey = "alert:{$type}:{$level}:" . md5($message);
            if (Cache::get($alertKey)) {
                return;
            }
            Cache::set($alertKey, true, 300); // 5分钟内不重复告警

            // 发送邮件告警
            $emailService = new EmailService();
            $emailService->sendAlert([
                'type' => $type,
                'level' => $level,
                'message' => $message,
                'context' => $context,
                'time' => date('Y-m-d H:i:s')
            ]);

            // 发送钉钉/企业微信告警
            $this->sendWebhookAlert($type, $level, $message, $context);

        } catch (\Exception $e) {
            error_log("Send alert failed: " . $e->getMessage());
        }
    }

    /**
     * 检查频率告警
     */
    private function checkFrequencyAlerts(string $type, string $level): void
    {
        $cacheKey = "log_count:{$type}:{$level}:" . date('Y-m-d-H');
        $count = Cache::get($cacheKey, 0);
        Cache::set($cacheKey, $count + 1, 3600);

        // 错误日志每小时超过100条告警
        if ($level === 'error' && $count > 100) {
            $this->sendAlert('frequency', 'warning', "错误日志频率过高：{$count}/小时", [
                'type' => $type,
                'level' => $level,
                'count' => $count
            ]);
        }
    }

    /**
     * 发送Webhook告警
     */
    private function sendWebhookAlert(string $type, string $level, string $message, array $context): void
    {
        $webhookUrl = config('log.webhook_url');
        if (empty($webhookUrl)) {
            return;
        }

        $data = [
            'msgtype' => 'text',
            'text' => [
                'content' => sprintf(
                    "【系统告警】\n类型：%s\n级别：%s\n消息：%s\n时间：%s",
                    $type,
                    strtoupper($level),
                    $message,
                    date('Y-m-d H:i:s')
                )
            ]
        ];

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $webhookUrl);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
            curl_exec($ch);
            curl_close($ch);
        } catch (\Exception $e) {
            // 静默处理
        }
    }

    /**
     * 生成CSV内容
     */
    private function generateCsvContent(array $logs): string
    {
        $csv = "ID,类型,级别,消息,用户ID,IP,URL,方法,创建时间\n";

        foreach ($logs as $log) {
            $csv .= sprintf(
                '"%s","%s","%s","%s","%s","%s","%s","%s","%s"' . "\n",
                $log['id'],
                $log['type'],
                $log['level'],
                str_replace('"', '""', $log['message']),
                $log['user_id'],
                $log['ip'],
                str_replace('"', '""', $log['url']),
                $log['method'],
                $log['created_at']
            );
        }

        return $csv;
    }

    /**
     * 计算错误率
     */
    private function calculateErrorRate(string $startDate, string $endDate): float
    {
        $total = Db::name('system_logs')
            ->where('created_at', 'between', [$startDate, $endDate])
            ->count();

        if ($total === 0) {
            return 0;
        }

        $errors = Db::name('system_logs')
            ->where('level', 'in', ['error', 'critical'])
            ->where('created_at', 'between', [$startDate, $endDate])
            ->count();

        return round(($errors / $total) * 100, 2);
    }

    /**
     * 查找可疑IP
     */
    private function findSuspiciousIPs(string $startDate, string $endDate): array
    {
        return Db::name('system_logs')
            ->where('created_at', 'between', [$startDate, $endDate])
            ->field('ip, COUNT(*) as count')
            ->group('ip')
            ->having('count > 1000') // 超过1000次访问
            ->order('count desc')
            ->select()
            ->toArray();
    }

    /**
     * 查找慢请求
     */
    private function findSlowRequests(string $startDate, string $endDate): array
    {
        return Db::name('system_logs')
            ->where('type', self::TYPE_PERFORMANCE)
            ->where('created_at', 'between', [$startDate, $endDate])
            ->where('message', 'like', '%slow%')
            ->limit(10)
            ->select()
            ->toArray();
    }

    /**
     * 查找安全事件
     */
    private function findSecurityEvents(string $startDate, string $endDate): array
    {
        return Db::name('system_logs')
            ->where('type', self::TYPE_SECURITY)
            ->where('level', 'in', ['warning', 'error', 'critical'])
            ->where('created_at', 'between', [$startDate, $endDate])
            ->select()
            ->toArray();
    }
}
