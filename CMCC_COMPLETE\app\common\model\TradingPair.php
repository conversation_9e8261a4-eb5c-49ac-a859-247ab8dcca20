<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 交易对模型
 */
class TradingPair extends Model
{
    protected $name = 'gvd_trading_pairs';
    
    protected $type = [
        'min_amount' => 'float',
        'max_amount' => 'float',
        'min_total' => 'float',
        'maker_fee' => 'float',
        'taker_fee' => 'float',
        'is_active' => 'boolean'
    ];

    /**
     * 获取所有活跃的交易对
     */
    public static function getActivePairs(): array
    {
        return self::where('is_active', 1)
                  ->order('sort_order', 'asc')
                  ->select()
                  ->toArray();
    }

    /**
     * 根据符号获取交易对
     */
    public static function getBySymbol(string $symbol): ?TradingPair
    {
        return self::where('symbol', $symbol)->find();
    }

    /**
     * 验证交易数量
     */
    public function validateAmount(float $amount): array
    {
        if ($amount < $this->min_amount) {
            return [
                'valid' => false,
                'message' => "交易数量不能小于 {$this->min_amount}"
            ];
        }

        if ($amount > $this->max_amount) {
            return [
                'valid' => false,
                'message' => "交易数量不能大于 {$this->max_amount}"
            ];
        }

        return ['valid' => true];
    }

    /**
     * 验证交易金额
     */
    public function validateTotal(float $total): array
    {
        if ($total < $this->min_total) {
            return [
                'valid' => false,
                'message' => "交易金额不能小于 {$this->min_total}"
            ];
        }

        return ['valid' => true];
    }

    /**
     * 格式化价格
     */
    public function formatPrice(float $price): float
    {
        return round($price, $this->price_precision);
    }

    /**
     * 格式化数量
     */
    public function formatAmount(float $amount): float
    {
        return round($amount, $this->amount_precision);
    }

    /**
     * 计算手续费
     */
    public function calculateFee(float $amount, bool $isMaker = false): float
    {
        $feeRate = $isMaker ? $this->maker_fee : $this->taker_fee;
        return $amount * $feeRate;
    }

    /**
     * 检查是否可以交易
     */
    public function isTradeEnabled(): bool
    {
        return $this->is_active;
    }

    /**
     * 获取最新价格
     */
    public function getLatestPrice(): float
    {
        $latestKline = Kline::getLatestKline($this->symbol, Kline::INTERVAL_1M);
        return $latestKline ? $latestKline['close_price'] : 0;
    }

    /**
     * 获取24小时统计
     */
    public function get24hStats(): array
    {
        return Kline::get24hStats($this->symbol);
    }

    /**
     * 获取交易对配置信息
     */
    public function getConfig(): array
    {
        return [
            'symbol' => $this->symbol,
            'base_coin' => $this->base_coin,
            'quote_coin' => $this->quote_coin,
            'price_precision' => $this->price_precision,
            'amount_precision' => $this->amount_precision,
            'min_amount' => $this->min_amount,
            'max_amount' => $this->max_amount,
            'min_total' => $this->min_total,
            'maker_fee' => $this->maker_fee,
            'taker_fee' => $this->taker_fee,
            'is_active' => $this->is_active
        ];
    }

    /**
     * 获取交易对列表（用于前端显示）
     */
    public static function getTradingPairsList(): array
    {
        $pairs = self::getActivePairs();
        $result = [];
        
        foreach ($pairs as $pair) {
            $stats = (new self($pair))->get24hStats();
            
            $result[] = [
                'symbol' => $pair['symbol'],
                'base_coin' => $pair['base_coin'],
                'quote_coin' => $pair['quote_coin'],
                'price' => $stats['close'],
                'change_percent' => $stats['change_percent'],
                'volume' => $stats['volume'],
                'amount' => $stats['amount'],
                'high' => $stats['high'],
                'low' => $stats['low'],
                'is_active' => $pair['is_active']
            ];
        }
        
        return $result;
    }

    /**
     * 创建新的交易对
     */
    public static function createTradingPair(array $data): array
    {
        try {
            // 验证数据
            $required = ['symbol', 'base_coin', 'quote_coin'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return ['code' => 0, 'msg' => "字段 {$field} 不能为空"];
                }
            }

            // 检查交易对是否已存在
            $exists = self::where('symbol', $data['symbol'])->find();
            if ($exists) {
                return ['code' => 0, 'msg' => '交易对已存在'];
            }

            // 设置默认值
            $defaults = [
                'price_precision' => 8,
                'amount_precision' => 8,
                'min_amount' => 0.00000001,
                'max_amount' => 999999999.0,
                'min_total' => 0.00000001,
                'maker_fee' => 0.001,
                'taker_fee' => 0.001,
                'is_active' => 1,
                'sort_order' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $data = array_merge($defaults, $data);
            
            $pair = self::create($data);
            
            return [
                'code' => 1,
                'msg' => '交易对创建成功',
                'data' => $pair->toArray()
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '创建失败：' . $e->getMessage()];
        }
    }

    /**
     * 更新交易对配置
     */
    public function updateConfig(array $data): array
    {
        try {
            $allowedFields = [
                'price_precision', 'amount_precision', 'min_amount', 
                'max_amount', 'min_total', 'maker_fee', 'taker_fee', 
                'is_active', 'sort_order'
            ];

            $updateData = [];
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }

            if (empty($updateData)) {
                return ['code' => 0, 'msg' => '没有需要更新的数据'];
            }

            $updateData['updated_at'] = date('Y-m-d H:i:s');
            
            $this->save($updateData);
            
            return [
                'code' => 1,
                'msg' => '配置更新成功',
                'data' => $this->toArray()
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '更新失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取交易对的深度数据
     */
    public function getDepthData(int $limit = 20): array
    {
        // 这里应该从订单簿获取真实深度数据
        // 暂时返回模拟数据
        $currentPrice = $this->getLatestPrice();
        
        if ($currentPrice <= 0) {
            return ['bids' => [], 'asks' => []];
        }

        $bids = [];
        $asks = [];

        // 生成买盘数据
        for ($i = 1; $i <= $limit; $i++) {
            $price = $currentPrice - ($i * 0.01 * $currentPrice);
            $amount = mt_rand(100, 1000) / 100;
            $bids[] = [
                'price' => $this->formatPrice($price),
                'amount' => $this->formatAmount($amount),
                'total' => $this->formatPrice($price * $amount)
            ];
        }

        // 生成卖盘数据
        for ($i = 1; $i <= $limit; $i++) {
            $price = $currentPrice + ($i * 0.01 * $currentPrice);
            $amount = mt_rand(100, 1000) / 100;
            $asks[] = [
                'price' => $this->formatPrice($price),
                'amount' => $this->formatAmount($amount),
                'total' => $this->formatPrice($price * $amount)
            ];
        }

        return [
            'symbol' => $this->symbol,
            'bids' => $bids,
            'asks' => $asks,
            'timestamp' => time()
        ];
    }
}
