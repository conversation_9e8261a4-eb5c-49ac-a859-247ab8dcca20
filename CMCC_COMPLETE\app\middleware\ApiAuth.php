<?php
declare (strict_types = 1);

namespace app\middleware;

use Closure;
use think\Request;
use think\Response;
use think\facade\Config;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use app\common\service\UserService;

/**
 * API认证中间件
 */
class ApiAuth
{
    protected $userService;

    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }

    /**
     * 处理请求
     *
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 获取token
        $token = $request->header('Authorization');
        
        if (empty($token)) {
            return json([
                'code' => 401,
                'msg'  => '请先登录',
                'data' => null
            ])->code(401);
        }

        // 移除Bearer前缀
        if (strpos($token, 'Bearer ') === 0) {
            $token = substr($token, 7);
        }

        try {
            // 验证JWT token
            $key = Config::get('app.security.key', 'gvd_security_key_2024');
            $decoded = JWT::decode($token, new Key($key, 'HS256'));
            
            // 获取用户信息
            $user = $this->userService->getUserById($decoded->user_id);
            
            if (!$user || $user['status'] != 1) {
                return json([
                    'code' => 401,
                    'msg'  => '用户不存在或已被禁用',
                    'data' => null
                ])->code(401);
            }

            // 将用户信息存储到请求中
            $request->user = $user;
            $request->user_id = $user['id'];

        } catch (\Exception $e) {
            return json([
                'code' => 401,
                'msg'  => 'Token无效或已过期',
                'data' => null
            ])->code(401);
        }

        return $next($request);
    }
}
