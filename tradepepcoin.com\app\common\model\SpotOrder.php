<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 现货订单模型
 */
class SpotOrder extends Model
{
    protected $name = 'gvd_spot_orders';
    
    protected $type = [
        'price' => 'float',
        'amount' => 'float',
        'total' => 'float',
        'filled_amount' => 'float',
        'filled_total' => 'float',
        'avg_price' => 'float',
        'fee' => 'float'
    ];

    // 订单类型
    const TYPE_BUY = 1;   // 买入
    const TYPE_SELL = 2;  // 卖出

    // 订单方式
    const ORDER_TYPE_LIMIT = 1;  // 限价单
    const ORDER_TYPE_MARKET = 2; // 市价单

    // 订单状态
    const STATUS_PENDING = 1;    // 待成交
    const STATUS_PARTIAL = 2;    // 部分成交
    const STATUS_FILLED = 3;     // 完全成交
    const STATUS_CANCELLED = 4;  // 已取消

    /**
     * 生成订单号
     */
    public static function generateOrderId(): string
    {
        return 'SP' . date('YmdHis') . mt_rand(1000, 9999);
    }

    /**
     * 获取订单类型文本
     */
    public function getTypeTextAttr($value, $data): string
    {
        $types = [
            self::TYPE_BUY => '买入',
            self::TYPE_SELL => '卖出'
        ];
        return $types[$data['type']] ?? '未知';
    }

    /**
     * 获取订单方式文本
     */
    public function getOrderTypeTextAttr($value, $data): string
    {
        $types = [
            self::ORDER_TYPE_LIMIT => '限价',
            self::ORDER_TYPE_MARKET => '市价'
        ];
        return $types[$data['order_type']] ?? '未知';
    }

    /**
     * 获取订单状态文本
     */
    public function getStatusTextAttr($value, $data): string
    {
        $statuses = [
            self::STATUS_PENDING => '待成交',
            self::STATUS_PARTIAL => '部分成交',
            self::STATUS_FILLED => '完全成交',
            self::STATUS_CANCELLED => '已取消'
        ];
        return $statuses[$data['status']] ?? '未知';
    }

    /**
     * 获取成交进度
     */
    public function getProgressAttr($value, $data): float
    {
        if ($data['amount'] <= 0) {
            return 0;
        }
        return round(($data['filled_amount'] / $data['amount']) * 100, 2);
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联交易对
     */
    public function tradingPair()
    {
        return $this->belongsTo(TradingPair::class, 'symbol', 'symbol');
    }

    /**
     * 获取用户的活跃订单
     */
    public static function getUserActiveOrders(int $userId, string $symbol = ''): array
    {
        $query = self::where('user_id', $userId)
                    ->whereIn('status', [self::STATUS_PENDING, self::STATUS_PARTIAL]);
        
        if (!empty($symbol)) {
            $query->where('symbol', $symbol);
        }
        
        return $query->order('created_at', 'desc')
                    ->limit(50)
                    ->select()
                    ->toArray();
    }

    /**
     * 获取用户历史订单
     */
    public static function getUserHistoryOrders(int $userId, string $symbol = '', int $page = 1, int $limit = 20): array
    {
        $query = self::where('user_id', $userId);
        
        if (!empty($symbol)) {
            $query->where('symbol', $symbol);
        }
        
        $orders = $query->order('created_at', 'desc')
                       ->paginate([
                           'list_rows' => $limit,
                           'page' => $page
                       ]);
        
        return [
            'data' => $orders->items(),
            'total' => $orders->total(),
            'page' => $page,
            'limit' => $limit
        ];
    }

    /**
     * 取消订单
     */
    public function cancel(): bool
    {
        if (!in_array($this->status, [self::STATUS_PENDING, self::STATUS_PARTIAL])) {
            return false;
        }

        // 更新订单状态
        $this->status = self::STATUS_CANCELLED;
        $this->updated_at = date('Y-m-d H:i:s');
        
        if ($this->save()) {
            // 解冻资产
            $this->unfreezeAssets();
            return true;
        }
        
        return false;
    }

    /**
     * 解冻资产
     */
    private function unfreezeAssets(): void
    {
        $userAsset = UserAsset::where('user_id', $this->user_id);
        
        if ($this->type == self::TYPE_BUY) {
            // 买单：解冻USDT
            $asset = $userAsset->where('coin_symbol', 'USDT')->find();
            if ($asset) {
                $unfreezeAmount = $this->total - $this->filled_total;
                $asset->available += $unfreezeAmount;
                $asset->frozen -= $unfreezeAmount;
                $asset->save();
            }
        } else {
            // 卖单：解冻基础币种
            $baseCoin = explode('USDT', $this->symbol)[0];
            $asset = $userAsset->where('coin_symbol', $baseCoin)->find();
            if ($asset) {
                $unfreezeAmount = $this->amount - $this->filled_amount;
                $asset->available += $unfreezeAmount;
                $asset->frozen -= $unfreezeAmount;
                $asset->save();
            }
        }
    }

    /**
     * 部分成交
     */
    public function partialFill(float $fillAmount, float $fillPrice): bool
    {
        if ($this->status != self::STATUS_PENDING && $this->status != self::STATUS_PARTIAL) {
            return false;
        }

        $fillTotal = $fillAmount * $fillPrice;
        
        $this->filled_amount += $fillAmount;
        $this->filled_total += $fillTotal;
        $this->avg_price = $this->filled_total / $this->filled_amount;
        
        // 判断是否完全成交
        if ($this->filled_amount >= $this->amount) {
            $this->status = self::STATUS_FILLED;
            $this->filled_at = date('Y-m-d H:i:s');
        } else {
            $this->status = self::STATUS_PARTIAL;
        }
        
        $this->updated_at = date('Y-m-d H:i:s');
        
        return $this->save();
    }

    /**
     * 获取可撮合的买单
     */
    public static function getMatchableBuyOrders(string $symbol, float $price, int $limit = 10): array
    {
        return self::where([
            'symbol' => $symbol,
            'type' => self::TYPE_BUY,
            'order_type' => self::ORDER_TYPE_LIMIT,
            'status' => [self::STATUS_PENDING, self::STATUS_PARTIAL]
        ])
        ->where('price', '>=', $price)
        ->order('price', 'desc')
        ->order('created_at', 'asc')
        ->limit($limit)
        ->select()
        ->toArray();
    }

    /**
     * 获取可撮合的卖单
     */
    public static function getMatchableSellOrders(string $symbol, float $price, int $limit = 10): array
    {
        return self::where([
            'symbol' => $symbol,
            'type' => self::TYPE_SELL,
            'order_type' => self::ORDER_TYPE_LIMIT,
            'status' => [self::STATUS_PENDING, self::STATUS_PARTIAL]
        ])
        ->where('price', '<=', $price)
        ->order('price', 'asc')
        ->order('created_at', 'asc')
        ->limit($limit)
        ->select()
        ->toArray();
    }

    /**
     * 获取订单簿数据
     */
    public static function getOrderBook(string $symbol, int $depth = 20): array
    {
        // 获取买盘
        $buyOrders = self::where([
            'symbol' => $symbol,
            'type' => self::TYPE_BUY,
            'order_type' => self::ORDER_TYPE_LIMIT,
            'status' => [self::STATUS_PENDING, self::STATUS_PARTIAL]
        ])
        ->field('price, sum(amount - filled_amount) as amount')
        ->group('price')
        ->order('price', 'desc')
        ->limit($depth)
        ->select()
        ->toArray();

        // 获取卖盘
        $sellOrders = self::where([
            'symbol' => $symbol,
            'type' => self::TYPE_SELL,
            'order_type' => self::ORDER_TYPE_LIMIT,
            'status' => [self::STATUS_PENDING, self::STATUS_PARTIAL]
        ])
        ->field('price, sum(amount - filled_amount) as amount')
        ->group('price')
        ->order('price', 'asc')
        ->limit($depth)
        ->select()
        ->toArray();

        return [
            'bids' => $buyOrders,  // 买盘
            'asks' => $sellOrders  // 卖盘
        ];
    }
}
