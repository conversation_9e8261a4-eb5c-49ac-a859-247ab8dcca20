<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 客服工单模型
 */
class CustomerServiceTicket extends Model
{
    protected $table = 'ce_customer_service_tickets';
    
    protected $pk = 'id';
    
    // 设置字段信息
    protected $schema = [
        'id'            => 'int',
        'ticket_id'     => 'string',
        'user_id'       => 'int',
        'service_id'    => 'int',
        'subject'       => 'string',
        'category'      => 'string',
        'priority'      => 'int',
        'status'        => 'int',
        'last_reply_at' => 'datetime',
        'closed_at'     => 'datetime',
        'created_at'    => 'datetime',
        'updated_at'    => 'datetime'
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 关联客服
     */
    public function service()
    {
        return $this->belongsTo(User::class, 'service_id', 'id');
    }

    /**
     * 关联消息
     */
    public function messages()
    {
        return $this->hasMany(CustomerServiceMessage::class, 'ticket_id', 'ticket_id');
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = [
            1 => '开启',
            2 => '等待回复',
            3 => '已解决',
            4 => '已关闭'
        ];
        
        return $statusMap[$data['status']] ?? '未知';
    }

    /**
     * 获取优先级文本
     */
    public function getPriorityTextAttr($value, $data)
    {
        $priorityMap = [
            1 => '低',
            2 => '普通',
            3 => '高',
            4 => '紧急'
        ];
        
        return $priorityMap[$data['priority']] ?? '普通';
    }

    /**
     * 根据工单ID获取
     */
    public static function getByTicketId(string $ticketId): ?CustomerServiceTicket
    {
        return self::where('ticket_id', $ticketId)->find();
    }

    /**
     * 获取用户的工单
     */
    public static function getUserTickets(int $userId, array $filters = []): array
    {
        $query = self::where('user_id', $userId);

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['category'])) {
            $query->where('category', $filters['category']);
        }

        return $query->order('created_at', 'desc')
                    ->limit($filters['limit'] ?? 20)
                    ->select()
                    ->toArray();
    }
}
