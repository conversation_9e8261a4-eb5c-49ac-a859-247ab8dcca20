/* 代理端控制台JavaScript功能 */

// 全局变量
let currentMonitorTab = 'pending';
let contractOrdersData = {
    pending: [],
    processing: [],
    recent: []
};

// 加载合约订单
function loadContractOrders() {
    fetch('/agent/dashboard/contract-orders')
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            contractOrdersData = data.data;
            updateContractDisplay();
            updateContractCounts();
        }
    })
    .catch(error => {
        console.error('加载合约订单失败:', error);
    });
}

// 更新合约显示
function updateContractDisplay() {
    updatePendingContracts();
    updateProcessingContracts();
    updateRecentContracts();
}

// 更新待处理合约
function updatePendingContracts() {
    const container = document.getElementById('pendingContractsList');
    if (!container) return;

    const contracts = contractOrdersData.pending || [];
    
    if (contracts.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">⏳</div>
                <div class="empty-text">暂无待处理订单</div>
            </div>
        `;
        return;
    }

    // 添加批量操作头部
    let batchHeader = '';
    if (contracts.length > 0) {
        batchHeader = `
            <div class="batch-operations">
                <div class="batch-select">
                    <label class="checkbox-label">
                        <input type="checkbox" id="selectAllContracts" onchange="toggleSelectAll(this)">
                        <span>全选 (<span id="selectedCount">0</span>/${contracts.length})</span>
                    </label>
                </div>
                <div class="batch-actions">
                    <button class="btn btn-sm btn-success" onclick="batchOperateContracts('win_selected')">
                        选中盈利
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="batchOperateContracts('loss_selected')">
                        选中亏损
                    </button>
                </div>
            </div>
        `;
    }

    container.innerHTML = batchHeader + contracts.map(contract => `
        <div class="contract-item pending-contract" data-id="${contract.id}">
            <div class="contract-header">
                <div class="contract-select">
                    <input type="checkbox" class="contract-checkbox" value="${contract.id}" onchange="updateSelectedCount()">
                </div>
                <div class="contract-info">
                    <span class="contract-user">${contract.username}</span>
                    <span class="contract-symbol">${contract.coin_name}</span>
                    <span class="contract-direction ${contract.direction == 1 ? 'buy' : 'sell'}">
                        ${contract.direction == 1 ? '买涨' : '买跌'}
                    </span>
                </div>
                <div class="contract-amount">${contract.amount} USDT</div>
            </div>
            <div class="contract-details">
                <div class="detail-row">
                    <span>买入价:</span>
                    <span>${contract.buy_price}</span>
                </div>
                <div class="detail-row">
                    <span>时长:</span>
                    <span>${contract.duration}分钟</span>
                </div>
                <div class="detail-row">
                    <span>创建时间:</span>
                    <span>${formatTime(contract.created_at)}</span>
                </div>
            </div>
            <div class="contract-actions">
                <button class="btn btn-sm btn-success" onclick="setContractResult(${contract.id}, 'win')">
                    设为盈利
                </button>
                <button class="btn btn-sm btn-danger" onclick="setContractResult(${contract.id}, 'loss')">
                    设为亏损
                </button>
                <button class="btn btn-sm btn-outline" onclick="viewContractDetail(${contract.id})">
                    详情
                </button>
            </div>
        </div>
    `).join('');
}

// 更新进行中合约
function updateProcessingContracts() {
    const container = document.getElementById('processingContractsList');
    if (!container) return;

    const contracts = contractOrdersData.processing || [];
    
    if (contracts.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">⚡</div>
                <div class="empty-text">暂无进行中订单</div>
            </div>
        `;
        return;
    }

    container.innerHTML = contracts.map(contract => `
        <div class="contract-item processing-contract" data-id="${contract.id}">
            <div class="contract-header">
                <div class="contract-info">
                    <span class="contract-user">${contract.username}</span>
                    <span class="contract-symbol">${contract.coin_name}</span>
                    <span class="contract-direction ${contract.direction == 1 ? 'buy' : 'sell'}">
                        ${contract.direction == 1 ? '买涨' : '买跌'}
                    </span>
                </div>
                <div class="contract-amount">${contract.amount} USDT</div>
            </div>
            <div class="contract-progress">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${contract.progress}%"></div>
                </div>
                <div class="progress-text">
                    <span>剩余: ${contract.remaining_time}</span>
                    <span>${contract.progress}%</span>
                </div>
            </div>
            <div class="contract-details">
                <div class="detail-row">
                    <span>买入价:</span>
                    <span>${contract.buy_price}</span>
                </div>
                <div class="detail-row">
                    <span>当前价:</span>
                    <span class="${contract.current_price > contract.buy_price ? 'text-success' : 'text-danger'}">
                        ${contract.current_price}
                    </span>
                </div>
            </div>
        </div>
    `).join('');
}

// 更新最近完成合约
function updateRecentContracts() {
    const container = document.getElementById('recentContractsList');
    if (!container) return;

    const contracts = contractOrdersData.recent || [];
    
    if (contracts.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">✅</div>
                <div class="empty-text">暂无最近完成订单</div>
            </div>
        `;
        return;
    }

    container.innerHTML = contracts.map(contract => `
        <div class="contract-item completed-contract" data-id="${contract.id}">
            <div class="contract-header">
                <div class="contract-info">
                    <span class="contract-user">${contract.username}</span>
                    <span class="contract-symbol">${contract.coin_name}</span>
                    <span class="contract-direction ${contract.direction == 1 ? 'buy' : 'sell'}">
                        ${contract.direction == 1 ? '买涨' : '买跌'}
                    </span>
                </div>
                <div class="contract-result ${contract.is_win ? 'profit' : 'loss'}">
                    ${contract.is_win ? '盈利' : '亏损'} ${Math.abs(contract.profit_loss)} USDT
                </div>
            </div>
            <div class="contract-details">
                <div class="detail-row">
                    <span>买入价:</span>
                    <span>${contract.buy_price}</span>
                </div>
                <div class="detail-row">
                    <span>卖出价:</span>
                    <span>${contract.sell_price}</span>
                </div>
                <div class="detail-row">
                    <span>完成时间:</span>
                    <span>${formatTime(contract.updated_at)}</span>
                </div>
            </div>
        </div>
    `).join('');
}

// 更新合约数量
function updateContractCounts() {
    const pendingCount = document.getElementById('pendingCount');
    const processingCount = document.getElementById('processingCount');
    const recentCount = document.getElementById('recentCount');

    if (pendingCount) pendingCount.textContent = contractOrdersData.pending?.length || 0;
    if (processingCount) processingCount.textContent = contractOrdersData.processing?.length || 0;
    if (recentCount) recentCount.textContent = contractOrdersData.recent?.length || 0;

    // 更新待处理合约数量
    const pendingContractsEl = document.getElementById('pendingContracts');
    if (pendingContractsEl) {
        pendingContractsEl.textContent = contractOrdersData.pending?.length || 0;
    }
}

// 切换监控标签
function switchMonitorTab(tab) {
    currentMonitorTab = tab;

    // 更新标签状态
    document.querySelectorAll('.monitor-tab').forEach(t => t.classList.remove('active'));
    document.querySelector(`.monitor-tab[onclick="switchMonitorTab('${tab}')"]`).classList.add('active');

    // 切换面板
    document.querySelectorAll('.monitor-panel').forEach(panel => panel.classList.remove('active'));
    document.getElementById(`${tab}Panel`).classList.add('active');

    // 隐藏新订单提醒
    if (tab === 'pending') {
        const badge = document.getElementById('newOrdersBadge');
        if (badge) badge.style.display = 'none';
    }
}

// 设置合约结果
function setContractResult(contractId, result) {
    if (!confirm(`确定要将此合约设为${result === 'win' ? '盈利' : '亏损'}吗？`)) {
        return;
    }

    const formData = new FormData();
    formData.append('contract_id', contractId);
    formData.append('result', result);

    fetch('/agent/dashboard/set-contract-result', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            showNotification(`合约结果设置成功`, 'success');
            loadContractOrders(); // 重新加载数据
        } else {
            showNotification(data.msg || '设置失败', 'error');
        }
    })
    .catch(error => {
        console.error('设置合约结果失败:', error);
        showNotification('网络错误，请重试', 'error');
    });
}

// 查看合约详情
function viewContractDetail(contractId) {
    // 这里可以打开合约详情模态框
    window.open(`/agent/dashboard/contract-detail/${contractId}`, '_blank');
}

// 刷新合约数据
function refreshContracts() {
    loadContractOrders();
    showNotification('数据已刷新', 'success');
}

// 加载提币申请
function loadWithdrawalRequests() {
    fetch('/agent/dashboard/withdrawal-requests')
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            updateWithdrawalDisplay(data.data);
        }
    })
    .catch(error => {
        console.error('加载提币申请失败:', error);
    });
}

// 更新提币显示
function updateWithdrawalDisplay(withdrawals) {
    const container = document.getElementById('withdrawalList');
    if (!container) return;

    if (withdrawals.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">💸</div>
                <div class="empty-text">暂无待审核提币</div>
            </div>
        `;
        
        // 隐藏提醒徽章
        const badge = document.getElementById('withdrawalBadge');
        if (badge) badge.style.display = 'none';
        
        return;
    }

    // 显示提醒徽章
    const badge = document.getElementById('withdrawalBadge');
    if (badge) badge.style.display = 'inline-block';

    container.innerHTML = withdrawals.map(withdrawal => `
        <div class="withdrawal-item" data-id="${withdrawal.id}">
            <div class="withdrawal-header">
                <div class="withdrawal-user">${withdrawal.username}</div>
                <div class="withdrawal-amount">${withdrawal.amount} ${withdrawal.coin_symbol}</div>
            </div>
            <div class="withdrawal-details">
                <div class="detail-row">
                    <span>手续费:</span>
                    <span>${withdrawal.fee} ${withdrawal.coin_symbol}</span>
                </div>
                <div class="detail-row">
                    <span>实际到账:</span>
                    <span>${withdrawal.actual_amount} ${withdrawal.coin_symbol}</span>
                </div>
                <div class="detail-row">
                    <span>地址:</span>
                    <span class="withdrawal-address" title="${withdrawal.address}">
                        ${withdrawal.address.substring(0, 10)}...
                    </span>
                </div>
                <div class="detail-row">
                    <span>申请时间:</span>
                    <span>${formatTime(withdrawal.created_at)}</span>
                </div>
            </div>
            <div class="withdrawal-actions">
                <button class="btn btn-sm btn-success" onclick="approveWithdrawal(${withdrawal.id})">
                    通过
                </button>
                <button class="btn btn-sm btn-danger" onclick="rejectWithdrawal(${withdrawal.id})">
                    驳回
                </button>
            </div>
        </div>
    `).join('');

    // 更新待审核提币数量
    const pendingWithdrawalsEl = document.getElementById('pendingWithdrawals');
    if (pendingWithdrawalsEl) {
        pendingWithdrawalsEl.textContent = withdrawals.length;
    }

    // 更新提币金额
    const withdrawalAmountEl = document.getElementById('withdrawalAmount');
    if (withdrawalAmountEl) {
        const totalAmount = withdrawals.reduce((sum, w) => sum + parseFloat(w.amount), 0);
        withdrawalAmountEl.textContent = totalAmount.toFixed(2);
    }
}

// 通过提币申请
function approveWithdrawal(withdrawalId) {
    if (!confirm('确定要通过此提币申请吗？')) {
        return;
    }

    const formData = new FormData();
    formData.append('withdrawal_id', withdrawalId);
    formData.append('action', 'approve');

    fetch('/agent/dashboard/handle-withdrawal', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            showNotification('提币申请已通过', 'success');
            loadWithdrawalRequests();
        } else {
            showNotification(data.msg || '操作失败', 'error');
        }
    })
    .catch(error => {
        console.error('处理提币申请失败:', error);
        showNotification('网络错误，请重试', 'error');
    });
}

// 驳回提币申请
function rejectWithdrawal(withdrawalId) {
    const reason = prompt('请输入驳回原因:');
    if (!reason) return;

    const formData = new FormData();
    formData.append('withdrawal_id', withdrawalId);
    formData.append('action', 'reject');
    formData.append('reason', reason);

    fetch('/agent/dashboard/handle-withdrawal', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            showNotification('提币申请已驳回', 'success');
            loadWithdrawalRequests();
        } else {
            showNotification(data.msg || '操作失败', 'error');
        }
    })
    .catch(error => {
        console.error('处理提币申请失败:', error);
        showNotification('网络错误，请重试', 'error');
    });
}

// 加载财务记录
function loadFinanceRecords(page = 1, type = '') {
    const params = new URLSearchParams({
        page: page,
        limit: 20
    });
    
    if (type) {
        params.append('type', type);
    }

    fetch(`/agent/dashboard/finance-records?${params}`)
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            updateFinanceTable(data.data.records);
            updateFinancePagination(data.data.pagination);
        }
    })
    .catch(error => {
        console.error('加载财务记录失败:', error);
    });
}

// 更新财务表格
function updateFinanceTable(records) {
    const tbody = document.getElementById('financeRecordsTable');
    if (!tbody) return;

    if (records.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center">暂无财务记录</td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = records.map(record => `
        <tr>
            <td>${formatTime(record.created_at)}</td>
            <td>${record.username}</td>
            <td>
                <span class="badge badge-${getTypeColor(record.type)}">
                    ${getTypeName(record.type)}
                </span>
            </td>
            <td>${record.coin_symbol}</td>
            <td class="${record.amount >= 0 ? 'text-success' : 'text-danger'}">
                ${record.amount >= 0 ? '+' : ''}${record.amount}
            </td>
            <td>${record.balance_after}</td>
            <td>${record.description}</td>
        </tr>
    `).join('');
}

// 获取类型颜色
function getTypeColor(type) {
    const colors = {
        9: 'success',   // 充值
        10: 'danger',   // 提现
        7: 'warning',   // 合约收益
        5: 'info',      // 交易买入
        6: 'info'       // 交易卖出
    };
    return colors[type] || 'secondary';
}

// 获取类型名称
function getTypeName(type) {
    const names = {
        1: '管理员操作',
        2: '提币申请',
        5: '交易买入',
        6: '交易卖出',
        7: '合约收益',
        9: '充值',
        10: '提现',
        11: '佣金收入'
    };
    return names[type] || '其他';
}

// 筛选财务记录
function filterFinanceRecords() {
    const filter = document.getElementById('financeFilter').value;
    loadFinanceRecords(1, filter);
}

// 导出财务数据
function exportFinanceData() {
    const filter = document.getElementById('financeFilter').value;
    const params = new URLSearchParams();
    if (filter) params.append('type', filter);
    
    window.open(`/agent/dashboard/export-finance?${params}`, '_blank');
}

// 加载最近充值记录
function loadRecentDeposits(limit = 10) {
    fetch(`/agent/dashboard/recent-deposits?limit=${limit}`)
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            updateDepositHistory(data.data);
        }
    })
    .catch(error => {
        console.error('加载充值记录失败:', error);
    });
}

// 更新充值历史显示
function updateDepositHistory(deposits) {
    // 这里可以更新充值历史面板
    console.log('最近充值记录:', deposits);
}

// 监控充值状态变化
function monitorDepositChanges() {
    // 每30秒检查一次充值变化
    setInterval(() => {
        checkDepositChanges();
    }, 30000);
}

// 检查充值变化
function checkDepositChanges() {
    const lastDepositCheck = localStorage.getItem('last_deposit_check') || Date.now() - 300000;

    fetch(`/agent/dashboard/recent-deposits?limit=5`)
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            const newDeposits = data.data.filter(deposit =>
                new Date(deposit.created_at).getTime() > parseInt(lastDepositCheck)
            );

            if (newDeposits.length > 0) {
                showDepositNotifications(newDeposits);
            }

            localStorage.setItem('last_deposit_check', Date.now());
        }
    })
    .catch(error => {
        console.error('检查充值变化失败:', error);
    });
}

// 显示充值通知
function showDepositNotifications(deposits) {
    deposits.forEach((deposit, index) => {
        setTimeout(() => {
            showNotification(
                `💰 ${deposit.username} 充值 ${deposit.amount} ${deposit.coin_symbol} 到账！`,
                'success'
            );
        }, index * 1000); // 每个通知间隔1秒
    });
}

// 获取用户详细信息
function getUserDetail(userId) {
    return fetch(`/agent/dashboard/user-detail/${userId}`)
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            return data.data;
        }
        throw new Error(data.msg || '获取用户信息失败');
    });
}

// 显示用户详情模态框
function showUserDetailModal(userId) {
    getUserDetail(userId)
    .then(user => {
        // 创建用户详情模态框
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.display = 'flex';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>用户详情 - ${user.username}</h3>
                    <button class="modal-close" onclick="this.closest('.modal').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="user-detail-info">
                        <div class="info-row">
                            <span>用户ID:</span>
                            <span>${user.id}</span>
                        </div>
                        <div class="info-row">
                            <span>用户名:</span>
                            <span>${user.username}</span>
                        </div>
                        <div class="info-row">
                            <span>邮箱:</span>
                            <span>${user.email}</span>
                        </div>
                        <div class="info-row">
                            <span>注册时间:</span>
                            <span>${user.created_at}</span>
                        </div>
                        <div class="info-row">
                            <span>USDT余额:</span>
                            <span class="text-success">${user.usdt_balance || 0} USDT</span>
                        </div>
                        <div class="info-row">
                            <span>今日充值:</span>
                            <span class="text-primary">${user.today_deposit || 0} USDT</span>
                        </div>
                        <div class="info-row">
                            <span>总充值:</span>
                            <span class="text-info">${user.total_deposit || 0} USDT</span>
                        </div>
                        <div class="info-row">
                            <span>合约订单:</span>
                            <span>${user.contract_count || 0} 个</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal').remove()">关闭</button>
                    <button class="btn btn-primary" onclick="window.open('/agent/dashboard/user-finance/${user.id}', '_blank')">
                        查看财务记录
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    })
    .catch(error => {
        showNotification('获取用户信息失败: ' + error.message, 'error');
    });
}

// 批量操作合约
function batchOperateContracts(operation, contractIds = []) {
    const operations = {
        'select_all': '全选',
        'select_none': '取消全选',
        'win_selected': '选中盈利',
        'loss_selected': '选中亏损'
    };

    if (!operations[operation]) {
        showNotification('无效的操作类型', 'error');
        return;
    }

    switch (operation) {
        case 'select_all':
            selectAllContracts(true);
            break;
        case 'select_none':
            selectAllContracts(false);
            break;
        case 'win_selected':
            processSelectedContracts('win');
            break;
        case 'loss_selected':
            processSelectedContracts('loss');
            break;
    }
}

// 全选/取消全选合约
function selectAllContracts(select) {
    const checkboxes = document.querySelectorAll('.contract-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = select;
    });
    updateSelectedCount();
}

// 更新选中数量
function updateSelectedCount() {
    const selected = document.querySelectorAll('.contract-checkbox:checked');
    const countEl = document.getElementById('selectedCount');
    if (countEl) {
        countEl.textContent = selected.length;
    }
}

// 处理选中的合约
function processSelectedContracts(result) {
    const selected = document.querySelectorAll('.contract-checkbox:checked');
    const contractIds = Array.from(selected).map(cb => cb.value);

    if (contractIds.length === 0) {
        showNotification('请先选择要操作的合约', 'warning');
        return;
    }

    const action = result === 'win' ? 'all_win' : 'all_loss';
    const actionName = result === 'win' ? '盈利' : '亏损';

    if (!confirm(`确定要将选中的 ${contractIds.length} 个合约设为${actionName}吗？`)) {
        return;
    }

    const formData = new FormData();
    formData.append('action', action);
    formData.append('contract_ids', contractIds.join(','));

    fetch('/agent/dashboard/batch-set-contract-result', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            showNotification(data.msg, 'success');
            loadContractOrders(); // 刷新列表
            selectAllContracts(false); // 取消全选
        } else {
            showNotification(data.msg || '操作失败', 'error');
        }
    })
    .catch(error => {
        console.error('批量操作失败:', error);
        showNotification('网络错误，请重试', 'error');
    });
}

// 格式化时间
function formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;

    if (diff < 60000) return '刚刚';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;

    return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 切换全选状态
function toggleSelectAll(checkbox) {
    const contractCheckboxes = document.querySelectorAll('.contract-checkbox');
    contractCheckboxes.forEach(cb => {
        cb.checked = checkbox.checked;
    });
    updateSelectedCount();
}

// 初始化充值监控
function initDepositMonitoring() {
    // 启动充值监控
    monitorDepositChanges();

    // 加载初始充值记录
    loadRecentDeposits();
}
