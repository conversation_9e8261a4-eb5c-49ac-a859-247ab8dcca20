<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合约交易控制 - GVD管理后台</title>
    <link href="https://cdn.jsdelivr.net/npm/element-plus@2.4.4/dist/index.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f7fa;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-header {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }

        .page-title {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 10px;
        }

        .page-desc {
            color: #606266;
            font-size: 14px;
        }

        .control-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .control-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }

        .card-title {
            font-size: 18px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .card-title .icon {
            margin-right: 10px;
            font-size: 20px;
        }

        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            align-items: end;
        }

        .form-item {
            flex: 1;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }

        .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: #409eff;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #606266;
            font-size: 14px;
        }

        .data-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }

        .table-header {
            padding: 20px;
            border-bottom: 1px solid #ebeef5;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-title {
            font-size: 18px;
            font-weight: bold;
            color: #303133;
        }

        .control-type-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .control-none {
            background: #f0f9ff;
            color: #1890ff;
        }

        .control-win {
            background: #f6ffed;
            color: #52c41a;
        }

        .control-lose {
            background: #fff2f0;
            color: #ff4d4f;
        }

        .control-random {
            background: #fff7e6;
            color: #fa8c16;
        }

        .price-display {
            display: flex;
            gap: 20px;
            align-items: center;
            margin: 15px 0;
        }

        .price-item {
            text-align: center;
        }

        .price-label {
            font-size: 12px;
            color: #606266;
            margin-bottom: 5px;
        }

        .price-value {
            font-size: 18px;
            font-weight: bold;
        }

        .open-price {
            color: #409eff;
        }

        .close-price.win {
            color: #67c23a;
        }

        .close-price.lose {
            color: #f56c6c;
        }

        .pnl-positive {
            color: #67c23a;
        }

        .pnl-negative {
            color: #f56c6c;
        }

        /* 新订单提醒样式 */
        .alert-panel {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            color: white;
            animation: alertPulse 2s infinite;
        }

        @keyframes alertPulse {
            0%, 100% { box-shadow: 0 0 20px rgba(255, 107, 107, 0.5); }
            50% { box-shadow: 0 0 30px rgba(255, 107, 107, 0.8); }
        }

        .alert-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .alert-header h3 {
            margin: 0;
            font-size: 18px;
        }

        .alert-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .alert-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .alert-info {
            flex: 1;
        }

        .alert-actions {
            display: flex;
            gap: 8px;
        }

        .text-green {
            color: #02c076;
        }

        .text-red {
            color: #f84960;
        }

        /* 倒计时显示 */
        .countdown-display {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            font-size: 16px;
        }

        .countdown-urgent {
            color: #f84960;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        @media (max-width: 768px) {
            .control-panel {
                grid-template-columns: 1fr;
            }

            .form-row {
                flex-direction: column;
                align-items: stretch;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .alert-item {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
            }

            .alert-actions {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div id="app" class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">🎮 合约交易控制中心</h1>
            <p class="page-desc">管理用户合约交易输赢，实时监控交易状态，控制市场风险</p>
        </div>

        <!-- 统计数据 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{{ stats.totalUsers }}</div>
                <div class="stat-label">受控用户数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ stats.todayOrders }}</div>
                <div class="stat-label">今日订单数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ stats.controlledOrders }}</div>
                <div class="stat-label">受控订单数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ stats.winRate }}%</div>
                <div class="stat-label">平台胜率</div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
            <!-- 用户控制设置 -->
            <div class="control-card">
                <h3 class="card-title">
                    <span class="icon">👤</span>
                    用户控制设置
                </h3>

                <el-form :model="userControlForm" label-width="100px">
                    <div class="form-row">
                        <div class="form-item">
                            <el-form-item label="用户ID">
                                <el-input v-model="userControlForm.userId" placeholder="输入用户ID"></el-input>
                            </el-form-item>
                        </div>
                        <div class="form-item">
                            <el-form-item label="控制类型">
                                <el-select v-model="userControlForm.controlType" placeholder="选择控制类型">
                                    <el-option label="不控制" value="none"></el-option>
                                    <el-option label="控制赢" value="win"></el-option>
                                    <el-option label="控制输" value="lose"></el-option>
                                    <el-option label="随机控制" value="random"></el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                    </div>

                    <div class="form-row" v-if="userControlForm.controlType === 'random'">
                        <div class="form-item">
                            <el-form-item label="胜率(%)">
                                <el-input-number v-model="userControlForm.winRate" :min="0" :max="100" :step="1"></el-input-number>
                            </el-form-item>
                        </div>
                        <div class="form-item">
                            <el-form-item label="有效期">
                                <el-date-picker v-model="userControlForm.validUntil" type="datetime" placeholder="选择有效期"></el-date-picker>
                            </el-form-item>
                        </div>
                    </div>

                    <el-form-item>
                        <el-button type="primary" @click="setUserControl" :loading="loading">设置控制</el-button>
                        <el-button @click="getUserControl">查询控制</el-button>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 批量控制设置 -->
            <div class="control-card">
                <h3 class="card-title">
                    <span class="icon">👥</span>
                    批量控制设置
                </h3>

                <el-form :model="batchControlForm" label-width="100px">
                    <el-form-item label="用户列表">
                        <el-input v-model="batchControlForm.userIds" type="textarea" :rows="3" placeholder="输入用户ID，用逗号分隔"></el-input>
                    </el-form-item>

                    <div class="form-row">
                        <div class="form-item">
                            <el-form-item label="控制类型">
                                <el-select v-model="batchControlForm.controlType" placeholder="选择控制类型">
                                    <el-option label="不控制" value="none"></el-option>
                                    <el-option label="控制赢" value="win"></el-option>
                                    <el-option label="控制输" value="lose"></el-option>
                                    <el-option label="随机控制" value="random"></el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <div class="form-item" v-if="batchControlForm.controlType === 'random'">
                            <el-form-item label="胜率(%)">
                                <el-input-number v-model="batchControlForm.winRate" :min="0" :max="100" :step="1"></el-input-number>
                            </el-form-item>
                        </div>
                    </div>

                    <el-form-item>
                        <el-button type="primary" @click="setBatchControl" :loading="batchLoading">批量设置</el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>

        <!-- 新订单提醒 -->
        <div class="alert-panel" v-if="newOrders.length > 0">
            <div class="alert-header">
                <h3>🔔 新订单提醒</h3>
                <el-button size="small" @click="clearNewOrders">清除提醒</el-button>
            </div>
            <div class="alert-list">
                <div v-for="order in newOrders" :key="order.id" class="alert-item">
                    <div class="alert-info">
                        <strong>{{ order.username }}</strong> 下单 {{ order.symbol }}
                        <span :class="order.direction === 'buy' ? 'text-green' : 'text-red'">
                            {{ order.direction === 'buy' ? '买涨' : '买跌' }}
                        </span>
                        {{ order.amount }} USDT
                    </div>
                    <div class="alert-actions">
                        <el-button size="small" type="success" @click="controlOrder(order.id, 'win')">控制赢</el-button>
                        <el-button size="small" type="danger" @click="controlOrder(order.id, 'lose')">控制输</el-button>
                        <el-button size="small" @click="viewOrderDetail(order)">详情</el-button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时订单监控 -->
        <div class="data-table">
            <div class="table-header">
                <h3 class="table-title">📊 实时订单监控</h3>
                <div>
                    <el-button @click="refreshOrders" :loading="refreshing">刷新</el-button>
                    <el-button type="primary" @click="showSettlementDialog">手动结算</el-button>
                </div>
            </div>

            <el-table :data="orders" style="width: 100%" v-loading="tableLoading">
                <el-table-column prop="id" label="订单ID" width="80"></el-table-column>
                <el-table-column prop="username" label="用户" width="120"></el-table-column>
                <el-table-column prop="symbol" label="交易对" width="100"></el-table-column>
                <el-table-column prop="side" label="方向" width="80">
                    <template #default="scope">
                        <el-tag :type="scope.row.side === 'buy' ? 'success' : 'danger'">
                            {{ scope.row.side === 'buy' ? '买涨' : '买跌' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="quantity" label="数量" width="100"></el-table-column>
                
                <!-- 价格显示 -->
                <el-table-column label="价格信息" width="200">
                    <template #default="scope">
                        <div class="price-display">
                            <div class="price-item">
                                <div class="price-label">开盘价</div>
                                <div class="price-value open-price">${{ scope.row.open_price }}</div>
                            </div>
                            <div class="price-item" v-if="scope.row.close_price">
                                <div class="price-label">收盘价</div>
                                <div class="price-value close-price" :class="scope.row.pnl >= 0 ? 'win' : 'lose'">
                                    ${{ scope.row.close_price }}
                                </div>
                            </div>
                        </div>
                    </template>
                </el-table-column>

                <el-table-column prop="pnl" label="盈亏" width="100">
                    <template #default="scope">
                        <span v-if="scope.row.pnl !== 0" :class="scope.row.pnl >= 0 ? 'pnl-positive' : 'pnl-negative'">
                            {{ scope.row.pnl >= 0 ? '+' : '' }}{{ scope.row.pnl }}
                        </span>
                        <span v-else>-</span>
                    </template>
                </el-table-column>

                <el-table-column prop="control_type" label="控制状态" width="100">
                    <template #default="scope">
                        <span class="control-type-tag" :class="'control-' + scope.row.control_type">
                            {{ getControlTypeName(scope.row.control_type) }}
                        </span>
                    </template>
                </el-table-column>

                <el-table-column prop="status" label="状态" width="80">
                    <template #default="scope">
                        <el-tag :type="scope.row.status === 'open' ? 'warning' : 'info'">
                            {{ scope.row.status === 'open' ? '持仓中' : '已平仓' }}
                        </el-tag>
                    </template>
                </el-table-column>

                <el-table-column prop="created_at" label="创建时间" width="150"></el-table-column>

                <el-table-column label="操作" width="150">
                    <template #default="scope">
                        <el-button v-if="scope.row.status === 'open'" size="small" type="primary" @click="settleOrder(scope.row)">
                            结算
                        </el-button>
                        <el-button size="small" @click="viewOrderDetail(scope.row)">详情</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div style="padding: 20px; text-align: center;">
                <el-pagination
                    v-model:current-page="pagination.page"
                    v-model:page-size="pagination.limit"
                    :page-sizes="[10, 20, 50, 100]"
                    :total="pagination.total"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange">
                </el-pagination>
            </div>
        </div>

        <!-- 手动结算对话框 -->
        <el-dialog v-model="settlementDialog.visible" title="手动结算订单" width="500px">
            <el-form :model="settlementDialog.form" label-width="100px">
                <el-form-item label="订单ID">
                    <el-input v-model="settlementDialog.form.orderId" placeholder="输入订单ID"></el-input>
                </el-form-item>
                <el-form-item label="结算价格">
                    <el-input-number v-model="settlementDialog.form.settlementPrice" :precision="2" :step="0.01" placeholder="输入结算价格"></el-input-number>
                </el-form-item>
                <el-form-item label="备注">
                    <el-input v-model="settlementDialog.form.remark" type="textarea" placeholder="输入备注"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="settlementDialog.visible = false">取消</el-button>
                <el-button type="primary" @click="confirmSettlement" :loading="settlementDialog.loading">确认结算</el-button>
            </template>
        </el-dialog>
    </div>

    <!-- 引入Vue和Element Plus -->
    <script src="https://cdn.jsdelivr.net/npm/vue@3.3.8/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-plus@2.4.4/dist/index.full.js"></script>

    <script>
        const { createApp } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        createApp({
            data() {
                return {
                    loading: false,
                    batchLoading: false,
                    tableLoading: false,
                    refreshing: false,
                    
                    // 统计数据
                    stats: {
                        totalUsers: 156,
                        todayOrders: 1248,
                        controlledOrders: 892,
                        winRate: 65.8
                    },

                    // 用户控制表单
                    userControlForm: {
                        userId: '',
                        controlType: 'none',
                        winRate: 50,
                        validUntil: null
                    },

                    // 批量控制表单
                    batchControlForm: {
                        userIds: '',
                        controlType: 'none',
                        winRate: 50
                    },

                    // 订单数据
                    orders: [],
                    pagination: {
                        page: 1,
                        limit: 20,
                        total: 0
                    },

                    // 结算对话框
                    settlementDialog: {
                        visible: false,
                        loading: false,
                        form: {
                            orderId: '',
                            settlementPrice: 0,
                            remark: ''
                        }
                    }
                };
            },
            mounted() {
                this.loadOrders();
                this.loadStats();
                
                // 定时刷新数据
                setInterval(() => {
                    this.loadOrders();
                    this.loadStats();
                }, 30000); // 30秒刷新一次
            },
            methods: {
                // 设置用户控制
                async setUserControl() {
                    if (!this.userControlForm.userId) {
                        ElMessage.error('请输入用户ID');
                        return;
                    }

                    this.loading = true;
                    try {
                        // 模拟API调用
                        await this.mockApiCall();
                        
                        ElMessage.success('用户控制设置成功');
                        this.userControlForm = {
                            userId: '',
                            controlType: 'none',
                            winRate: 50,
                            validUntil: null
                        };
                    } catch (error) {
                        ElMessage.error('设置失败：' + error.message);
                    } finally {
                        this.loading = false;
                    }
                },

                // 查询用户控制
                async getUserControl() {
                    if (!this.userControlForm.userId) {
                        ElMessage.error('请输入用户ID');
                        return;
                    }

                    try {
                        // 模拟API调用
                        const result = {
                            controlType: 'random',
                            winRate: 30,
                            validUntil: '2024-02-01 00:00:00'
                        };

                        this.userControlForm.controlType = result.controlType;
                        this.userControlForm.winRate = result.winRate;
                        this.userControlForm.validUntil = result.validUntil;

                        ElMessage.success('查询成功');
                    } catch (error) {
                        ElMessage.error('查询失败：' + error.message);
                    }
                },

                // 批量设置控制
                async setBatchControl() {
                    if (!this.batchControlForm.userIds) {
                        ElMessage.error('请输入用户ID列表');
                        return;
                    }

                    this.batchLoading = true;
                    try {
                        // 模拟API调用
                        await this.mockApiCall();
                        
                        ElMessage.success('批量设置成功');
                        this.batchControlForm = {
                            userIds: '',
                            controlType: 'none',
                            winRate: 50
                        };
                    } catch (error) {
                        ElMessage.error('批量设置失败：' + error.message);
                    } finally {
                        this.batchLoading = false;
                    }
                },

                // 加载订单数据
                async loadOrders() {
                    this.tableLoading = true;
                    try {
                        // 模拟API调用
                        await this.mockApiCall();
                        
                        // 模拟订单数据
                        this.orders = [
                            {
                                id: 1001,
                                username: 'user001',
                                symbol: 'BTC/USDT',
                                side: 'buy',
                                quantity: 0.1,
                                open_price: 50000,
                                close_price: 50500,
                                pnl: 50,
                                control_type: 'win',
                                status: 'closed',
                                created_at: '2024-01-15 14:30:25'
                            },
                            {
                                id: 1002,
                                username: 'user002',
                                symbol: 'ETH/USDT',
                                side: 'sell',
                                quantity: 1.5,
                                open_price: 3000,
                                close_price: 0,
                                pnl: 0,
                                control_type: 'lose',
                                status: 'open',
                                created_at: '2024-01-15 14:35:10'
                            }
                        ];
                        
                        this.pagination.total = 156;
                    } catch (error) {
                        ElMessage.error('加载订单失败：' + error.message);
                    } finally {
                        this.tableLoading = false;
                    }
                },

                // 加载统计数据
                async loadStats() {
                    try {
                        // 模拟API调用
                        await this.mockApiCall();
                        
                        // 更新统计数据
                        this.stats = {
                            totalUsers: 156 + Math.floor(Math.random() * 10),
                            todayOrders: 1248 + Math.floor(Math.random() * 50),
                            controlledOrders: 892 + Math.floor(Math.random() * 30),
                            winRate: (65.8 + (Math.random() - 0.5) * 2).toFixed(1)
                        };
                    } catch (error) {
                        console.error('加载统计数据失败：', error);
                    }
                },

                // 刷新订单
                refreshOrders() {
                    this.refreshing = true;
                    this.loadOrders().finally(() => {
                        this.refreshing = false;
                    });
                },

                // 结算订单
                async settleOrder(order) {
                    try {
                        await ElMessageBox.confirm('确定要结算这个订单吗？', '确认结算', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        });

                        // 模拟API调用
                        await this.mockApiCall();
                        
                        ElMessage.success('订单结算成功');
                        this.loadOrders();
                    } catch (error) {
                        if (error !== 'cancel') {
                            ElMessage.error('结算失败：' + error.message);
                        }
                    }
                },

                // 显示结算对话框
                showSettlementDialog() {
                    this.settlementDialog.visible = true;
                    this.settlementDialog.form = {
                        orderId: '',
                        settlementPrice: 0,
                        remark: ''
                    };
                },

                // 确认结算
                async confirmSettlement() {
                    if (!this.settlementDialog.form.orderId) {
                        ElMessage.error('请输入订单ID');
                        return;
                    }

                    this.settlementDialog.loading = true;
                    try {
                        // 模拟API调用
                        await this.mockApiCall();
                        
                        ElMessage.success('手动结算成功');
                        this.settlementDialog.visible = false;
                        this.loadOrders();
                    } catch (error) {
                        ElMessage.error('结算失败：' + error.message);
                    } finally {
                        this.settlementDialog.loading = false;
                    }
                },

                // 查看订单详情
                viewOrderDetail(order) {
                    ElMessage.info('查看订单详情功能开发中...');
                },

                // 获取控制类型名称
                getControlTypeName(type) {
                    const names = {
                        'none': '不控制',
                        'win': '控制赢',
                        'lose': '控制输',
                        'random': '随机控制'
                    };
                    return names[type] || type;
                },

                // 分页处理
                handleSizeChange(size) {
                    this.pagination.limit = size;
                    this.loadOrders();
                },

                handleCurrentChange(page) {
                    this.pagination.page = page;
                    this.loadOrders();
                },

                // 模拟API调用
                mockApiCall() {
                    return new Promise((resolve) => {
                        setTimeout(resolve, 1000 + Math.random() * 1000);
                    });
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
