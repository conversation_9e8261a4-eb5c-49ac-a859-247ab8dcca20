<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\common\service\UserService;
use think\Request;

/**
 * 认证控制器（注册、登录等）
 */
class Auth extends BaseController
{
    protected $userService;

    public function __construct()
    {
        parent::__construct();
        $this->userService = new UserService();
    }

    /**
     * 获取不需要验证的方法
     */
    protected function getNoAuthMethods(): array
    {
        return [
            'register',
            'login',
            'sendRegisterCode',
            'sendResetCode',
            'resetPassword',
            'checkUsername',
            'checkEmail',
            'getRegisterConfig'
        ];
    }

    /**
     * 前端用户注册（正式用户）
     */
    public function register(Request $request)
    {
        $data = $request->post();
        
        // 验证必填字段
        $validate = [
            'username' => 'require|max:50',
            'email' => 'require|email',
            'password' => 'require|min:6',
            'code' => 'require|length:6'
        ];

        $result = $this->validate($data, $validate);
        if ($result !== true) {
            return $this->error($result);
        }

        $result = $this->userService->frontendRegister($data);

        if ($result['code']) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 用户登录
     */
    public function login(Request $request)
    {
        $data = $request->post();
        
        // 验证必填字段
        $validate = [
            'username' => 'require',
            'password' => 'require'
        ];

        $result = $this->validate($data, $validate);
        if ($result !== true) {
            return $this->error($result);
        }

        $result = $this->userService->login($data);

        if ($result['code']) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 发送注册验证码
     */
    public function sendRegisterCode(Request $request)
    {
        $email = $request->post('email');
        
        if (empty($email)) {
            return $this->error('邮箱不能为空');
        }

        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return $this->error('邮箱格式不正确');
        }

        // 检查邮箱是否已注册
        $existUser = \app\common\model\User::where('email', $email)->find();
        if ($existUser) {
            return $this->error('邮箱已被注册');
        }

        $result = $this->userService->sendVerifyCode($email);

        if ($result['code']) {
            return $this->success([], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 发送找回密码验证码
     */
    public function sendResetCode(Request $request)
    {
        $email = $request->post('email');
        
        if (empty($email)) {
            return $this->error('邮箱不能为空');
        }

        // 检查邮箱是否存在
        $user = \app\common\model\User::where('email', $email)->find();
        if (!$user) {
            return $this->error('邮箱未注册');
        }

        $result = $this->userService->sendVerifyCode($email);

        if ($result['code']) {
            return $this->success([], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 重置密码
     */
    public function resetPassword(Request $request)
    {
        $data = $request->post();
        
        // 验证必填字段
        $validate = [
            'email' => 'require|email',
            'code' => 'require|length:6',
            'new_password' => 'require|min:6'
        ];

        $result = $this->validate($data, $validate);
        if ($result !== true) {
            return $this->error($result);
        }

        $result = $this->userService->resetPasswordByEmail($data);

        if ($result['code']) {
            return $this->success([], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 退出登录
     */
    public function logout(Request $request)
    {
        $token = $request->header('Authorization');
        if ($token && strpos($token, 'Bearer ') === 0) {
            $token = substr($token, 7);
            
            // 将token加入黑名单
            $this->userService->blacklistToken($token);
        }

        return $this->success([], '退出成功');
    }

    /**
     * 刷新token
     */
    public function refreshToken(Request $request)
    {
        $refreshToken = $request->post('refresh_token');
        
        if (empty($refreshToken)) {
            return $this->error('刷新token不能为空');
        }

        $result = $this->userService->refreshToken($refreshToken);

        if ($result['code']) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 验证token
     */
    public function verifyToken(Request $request)
    {
        $token = $request->header('Authorization');
        if (empty($token)) {
            return $this->error('token不能为空');
        }

        if (strpos($token, 'Bearer ') === 0) {
            $token = substr($token, 7);
        }

        $result = $this->userService->verifyToken($token);

        if ($result['valid']) {
            return $this->success([
                'user_id' => $result['user_id'],
                'username' => $result['username']
            ]);
        } else {
            return $this->error('token无效');
        }
    }

    /**
     * 检查用户名是否可用
     */
    public function checkUsername(Request $request)
    {
        $username = $request->get('username');
        
        if (empty($username)) {
            return $this->error('用户名不能为空');
        }

        $existUser = \app\common\model\User::where('username', $username)->find();
        
        return $this->success([
            'available' => !$existUser,
            'message' => $existUser ? '用户名已存在' : '用户名可用'
        ]);
    }

    /**
     * 检查邮箱是否可用
     */
    public function checkEmail(Request $request)
    {
        $email = $request->get('email');
        
        if (empty($email)) {
            return $this->error('邮箱不能为空');
        }

        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return $this->error('邮箱格式不正确');
        }

        $existUser = \app\common\model\User::where('email', $email)->find();
        
        return $this->success([
            'available' => !$existUser,
            'message' => $existUser ? '邮箱已被注册' : '邮箱可用'
        ]);
    }

    /**
     * 获取注册配置
     */
    public function getRegisterConfig()
    {
        return $this->success([
            'allow_register' => true,
            'require_email_verify' => true,
            'require_invite_code' => false,
            'password_min_length' => 6,
            'username_min_length' => 3,
            'username_max_length' => 50
        ]);
    }
}
