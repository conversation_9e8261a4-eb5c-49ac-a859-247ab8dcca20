<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服工作台 - GVD代理后台</title>
    <link rel="stylesheet" href="/static/css/app.css">
    <link rel="stylesheet" href="/static/css/agent.css">
    <link rel="stylesheet" href="/static/css/customer-admin.css">
</head>
<body>
    <div class="agent-layout">
        <!-- 侧边栏 -->
        <aside class="agent-sidebar">
            <div class="sidebar-header">
                <h2>GVD代理后台</h2>
            </div>
            <nav class="sidebar-nav">
                <a href="/agent/dashboard" class="nav-item">
                    <span class="nav-icon">📊</span>
                    <span class="nav-text">数据概览</span>
                </a>
                <a href="/agent/team" class="nav-item">
                    <span class="nav-icon">👥</span>
                    <span class="nav-text">团队管理</span>
                </a>
                <a href="/agent/customer" class="nav-item active">
                    <span class="nav-icon">💬</span>
                    <span class="nav-text">客服工作台</span>
                </a>
                <a href="/agent/commission" class="nav-item">
                    <span class="nav-icon">💰</span>
                    <span class="nav-text">佣金管理</span>
                </a>
                <a href="/agent/reports" class="nav-item">
                    <span class="nav-icon">📈</span>
                    <span class="nav-text">数据报表</span>
                </a>
            </nav>
        </aside>

        <!-- 主要内容 -->
        <main class="agent-main">
            <div class="agent-header">
                <div class="header-title">
                    <h1>客服工作台</h1>
                    <p>为您的客户提供专业的服务支持</p>
                </div>
                <div class="header-actions">
                    <div class="online-status">
                        <span class="status-indicator" id="statusIndicator"></span>
                        <select id="statusSelect" onchange="agentCustomer.updateStatus()">
                            <option value="online">在线</option>
                            <option value="busy">忙碌</option>
                            <option value="offline">离线</option>
                        </select>
                    </div>
                    <button class="btn btn-outline" onclick="agentCustomer.showWorkStatistics()">
                        <span class="icon">📊</span>
                        工作统计
                    </button>
                </div>
            </div>

            <div class="agent-content">
                <!-- 工作统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">💬</div>
                        <div class="stat-content">
                            <div class="stat-number" id="todaySessions">0</div>
                            <div class="stat-label">今日会话</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">👥</div>
                        <div class="stat-content">
                            <div class="stat-number" id="totalCustomers">0</div>
                            <div class="stat-label">我的客户</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">📨</div>
                        <div class="stat-content">
                            <div class="stat-number" id="unreadMessages">0</div>
                            <div class="stat-label">未读消息</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">⏱️</div>
                        <div class="stat-content">
                            <div class="stat-number" id="avgResponseTime">0s</div>
                            <div class="stat-label">平均响应</div>
                        </div>
                    </div>
                </div>

                <!-- 客服界面 -->
                <div class="customer-interface">
                    <!-- 会话列表 -->
                    <div class="sessions-panel">
                        <div class="panel-header">
                            <h3>我的会话</h3>
                            <div class="panel-actions">
                                <button class="btn btn-sm btn-primary" onclick="agentCustomer.showCustomerModal()">
                                    <span class="icon">➕</span>
                                    联系客户
                                </button>
                            </div>
                        </div>
                        <div class="sessions-list" id="sessionsList">
                            <!-- 会话列表内容 -->
                        </div>
                    </div>

                    <!-- 聊天区域 -->
                    <div class="chat-panel">
                        <div class="chat-header" id="chatHeader">
                            <div class="chat-info">
                                <div class="user-avatar">?</div>
                                <div class="user-details">
                                    <div class="user-name">选择一个会话</div>
                                    <div class="user-status">开始为客户服务</div>
                                </div>
                            </div>
                            <div class="chat-actions">
                                <button class="action-btn" onclick="agentCustomer.viewCustomerInfo()" title="客户信息">
                                    <span class="icon">👤</span>
                                </button>
                                <button class="action-btn" onclick="agentCustomer.viewCustomerOrders()" title="客户订单">
                                    <span class="icon">📋</span>
                                </button>
                            </div>
                        </div>

                        <div class="chat-messages" id="chatMessages">
                            <div class="empty-chat">
                                <div class="empty-icon">💬</div>
                                <div class="empty-text">
                                    <h3>选择一个会话开始对话</h3>
                                    <p>从左侧选择一个客服会话来查看消息历史</p>
                                </div>
                            </div>
                        </div>

                        <div class="chat-input" id="chatInput" style="display: none;">
                            <div class="quick-replies" id="quickReplies">
                                <!-- 快捷回复按钮 -->
                            </div>
                            <div class="input-area">
                                <button class="tool-btn" onclick="agentCustomer.showEmojiPanel()" title="表情">
                                    <span class="icon">😊</span>
                                </button>
                                <textarea 
                                    id="messageInput" 
                                    placeholder="输入回复消息..."
                                    rows="1"
                                    maxlength="1000"
                                ></textarea>
                                <button class="send-btn" id="sendBtn" onclick="agentCustomer.sendMessage()">
                                    <span class="icon">📤</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 客户信息面板 -->
                    <div class="customer-info-panel">
                        <div class="panel-header">
                            <h3>客户信息</h3>
                        </div>
                        <div class="customer-profile" id="customerProfile">
                            <div class="profile-avatar">
                                <div class="avatar-placeholder">?</div>
                            </div>
                            <div class="profile-info">
                                <div class="info-item">
                                    <label>用户名</label>
                                    <span id="customerUsername">-</span>
                                </div>
                                <div class="info-item">
                                    <label>邮箱</label>
                                    <span id="customerEmail">-</span>
                                </div>
                                <div class="info-item">
                                    <label>余额</label>
                                    <span id="customerBalance">-</span>
                                </div>
                                <div class="info-item">
                                    <label>注册时间</label>
                                    <span id="customerRegTime">-</span>
                                </div>
                                <div class="info-item">
                                    <label>最后登录</label>
                                    <span id="customerLastLogin">-</span>
                                </div>
                            </div>
                        </div>

                        <div class="customer-actions">
                            <button class="btn btn-sm btn-outline" onclick="agentCustomer.viewCustomerOrders()">
                                查看订单
                            </button>
                            <button class="btn btn-sm btn-outline" onclick="agentCustomer.viewCustomerTransactions()">
                                交易记录
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="agentCustomer.createVirtualOrder()">
                                创建虚拟订单
                            </button>
                        </div>

                        <!-- 快捷操作 -->
                        <div class="quick-actions">
                            <h4>快捷操作</h4>
                            <div class="action-buttons">
                                <button class="action-btn-block" onclick="agentCustomer.sendTradingTip()">
                                    <span class="icon">💡</span>
                                    发送交易提示
                                </button>
                                <button class="action-btn-block" onclick="agentCustomer.sendMarketAnalysis()">
                                    <span class="icon">📊</span>
                                    发送市场分析
                                </button>
                                <button class="action-btn-block" onclick="agentCustomer.sendPromotionInfo()">
                                    <span class="icon">🎁</span>
                                    发送优惠信息
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 我的客户列表 -->
                <div class="customers-section">
                    <div class="section-header">
                        <h3>我的客户</h3>
                        <div class="section-filters">
                            <input type="text" id="customerSearch" placeholder="搜索客户..." onchange="agentCustomer.searchCustomers()">
                            <button class="btn btn-sm btn-primary" onclick="agentCustomer.refreshCustomers()">
                                <span class="icon">🔄</span>
                                刷新
                            </button>
                        </div>
                    </div>
                    <div class="customers-grid" id="customersGrid">
                        <!-- 客户列表 -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 联系客户模态框 -->
    <div class="modal" id="customerModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>联系客户</h3>
                <button class="modal-close" onclick="agentCustomer.hideCustomerModal()">×</button>
            </div>
            <div class="modal-body">
                <div class="customer-list" id="modalCustomerList">
                    <!-- 客户选择列表 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 工作统计模态框 -->
    <div class="modal" id="statisticsModal">
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h3>工作统计</h3>
                <button class="modal-close" onclick="agentCustomer.hideStatisticsModal()">×</button>
            </div>
            <div class="modal-body">
                <div class="statistics-content" id="statisticsContent">
                    <!-- 统计图表和数据 -->
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/static/js/agent-customer.js"></script>
</body>
</html>
