/**
 * GVD数字货币交易平台 - 火币风格前端应用
 * 版本: 2.0.0
 */

class GVDApp {
    constructor() {
        this.apiBase = '/api';
        this.token = localStorage.getItem('gvd_token');
        this.user = null;
        this.marketData = new Map();
        this.priceUpdateInterval = null;
        this.init();
    }

    init() {
        this.setupAxios();
        this.checkAuth();
        this.bindEvents();
        this.initMarketData();
        this.startPriceUpdates();
        this.initAnimations();
        this.initMobileAdaptation();
        this.initTouchGestures();
        this.optimizeMobilePerformance();
    }

    // 设置Axios默认配置
    setupAxios() {
        // 设置请求拦截器
        axios.interceptors.request.use(
            config => {
                if (this.token) {
                    config.headers.Authorization = `Bearer ${this.token}`;
                }
                return config;
            },
            error => {
                return Promise.reject(error);
            }
        );

        // 设置响应拦截器
        axios.interceptors.response.use(
            response => {
                return response;
            },
            error => {
                if (error.response && error.response.status === 401) {
                    this.logout();
                    this.showNotification('登录已过期，请重新登录', 'error');
                }
                return Promise.reject(error);
            }
        );
    }

    // 检查用户认证状态
    async checkAuth() {
        if (this.token) {
            try {
                const response = await this.api('/user/info');
                if (response.data.code === 1) {
                    this.user = response.data.data;
                    this.updateUserUI();
                } else {
                    this.logout();
                }
            } catch (error) {
                this.logout();
            }
        }
    }

    // API请求封装
    async api(url, method = 'GET', data = null) {
        const config = {
            method,
            url: this.apiBase + url,
        };

        if (data) {
            if (method === 'GET') {
                config.params = data;
            } else {
                config.data = data;
            }
        }

        return await axios(config);
    }

    // 用户登录
    async login(username, password) {
        try {
            const response = await this.api('/auth/login', 'POST', {
                username,
                password
            });

            if (response.data.code === 1) {
                this.token = response.data.data.token;
                this.user = response.data.data.user;
                localStorage.setItem('gvd_token', this.token);
                this.updateUserUI();
                this.showNotification('登录成功', 'success');
                return true;
            } else {
                this.showNotification(response.data.msg, 'error');
                return false;
            }
        } catch (error) {
            this.showNotification('登录失败，请重试', 'error');
            return false;
        }
    }

    // 用户注册
    async register(userData) {
        try {
            const response = await this.api('/auth/register', 'POST', userData);

            if (response.data.code === 1) {
                this.showNotification('注册成功，请登录', 'success');
                return true;
            } else {
                this.showNotification(response.data.msg, 'error');
                return false;
            }
        } catch (error) {
            this.showNotification('注册失败，请重试', 'error');
            return false;
        }
    }

    // 用户登出
    logout() {
        this.token = null;
        this.user = null;
        localStorage.removeItem('gvd_token');
        this.updateUserUI();
        window.location.href = '/';
    }

    // 更新用户界面
    updateUserUI() {
        const guestMenu = document.querySelector('.guest-menu');
        const userInfo = document.querySelector('.user-info');

        if (!guestMenu || !userInfo) return;

        if (this.user) {
            guestMenu.style.display = 'none';
            userInfo.style.display = 'flex';

            // 更新用户信息
            const userAvatar = userInfo.querySelector('.user-avatar');
            const userName = userInfo.querySelector('.user-name');
            const userBalance = userInfo.querySelector('.user-balance');

            if (userAvatar) userAvatar.textContent = this.user.username.charAt(0).toUpperCase();
            if (userName) userName.textContent = this.user.username;
            if (userBalance) userBalance.textContent = `≈ ${this.formatNumber(this.user.balance || 0)} USDT`;
        } else {
            guestMenu.style.display = 'flex';
            userInfo.style.display = 'none';
        }
    }

    // 初始化市场数据
    initMarketData() {
        const marketPairs = [
            { symbol: 'BTCUSDT', name: 'Bitcoin', icon: 'BTC', basePrice: 50000 },
            { symbol: 'ETHUSDT', name: 'Ethereum', icon: 'ETH', basePrice: 3000 },
            { symbol: 'BNBUSDT', name: 'BNB', icon: 'BNB', basePrice: 300 },
            { symbol: 'ADAUSDT', name: 'Cardano', icon: 'ADA', basePrice: 1.2 },
            { symbol: 'DOTUSDT', name: 'Polkadot', icon: 'DOT', basePrice: 30 },
            { symbol: 'LINKUSDT', name: 'Chainlink', icon: 'LINK', basePrice: 25 },
            { symbol: 'LTCUSDT', name: 'Litecoin', icon: 'LTC', basePrice: 200 },
            { symbol: 'BCHUSDT', name: 'Bitcoin Cash', icon: 'BCH', basePrice: 500 }
        ];

        marketPairs.forEach(pair => {
            this.marketData.set(pair.symbol, {
                ...pair,
                price: pair.basePrice,
                change24h: (Math.random() - 0.5) * 10,
                volume24h: Math.random() * 1000000 + 100000
            });
        });

        this.renderMarketTable();
    }

    // 渲染市场表格
    renderMarketTable() {
        const tableBody = document.getElementById('marketTableBody');
        if (!tableBody) return;

        const rows = Array.from(this.marketData.values()).map(pair => {
            const changeClass = pair.change24h >= 0 ? 'positive' : 'negative';
            const changeSign = pair.change24h >= 0 ? '+' : '';

            return `
                <div class="table-row" onclick="app.goToTrade('${pair.symbol}')">
                    <div class="td symbol-cell">
                        <div class="symbol-icon">${pair.icon}</div>
                        <div class="symbol-info">
                            <div class="symbol-name">${pair.symbol}</div>
                            <div class="symbol-desc">${pair.name}</div>
                        </div>
                    </div>
                    <div class="td price-cell">$${this.formatNumber(pair.price, 2)}</div>
                    <div class="td change-cell ${changeClass}">
                        ${changeSign}${this.formatNumber(pair.change24h, 2)}%
                    </div>
                    <div class="td volume-cell">$${this.formatNumber(pair.volume24h, 0)}</div>
                    <div class="td action-cell">
                        <button class="trade-btn">交易</button>
                    </div>
                </div>
            `;
        }).join('');

        tableBody.innerHTML = rows;
    }

    // 开始价格更新
    startPriceUpdates() {
        this.updatePrices();
        this.priceUpdateInterval = setInterval(() => {
            this.updatePrices();
        }, 3000);
    }

    // 更新价格数据
    updatePrices() {
        this.marketData.forEach((pair, symbol) => {
            // 模拟价格波动
            const volatility = 0.02; // 2%波动
            const change = (Math.random() - 0.5) * volatility;
            pair.price = Math.max(pair.price * (1 + change), 0.01);

            // 更新24小时涨跌幅
            pair.change24h += (Math.random() - 0.5) * 0.5;
            pair.change24h = Math.max(Math.min(pair.change24h, 20), -20);

            // 更新交易量
            pair.volume24h += (Math.random() - 0.5) * 10000;
            pair.volume24h = Math.max(pair.volume24h, 10000);
        });

        this.updatePriceDisplay();
        this.renderMarketTable();
    }

    // 更新价格显示
    updatePriceDisplay() {
        // 更新英雄区域的价格显示
        const btcData = this.marketData.get('BTCUSDT');
        const ethData = this.marketData.get('ETHUSDT');
        const bnbData = this.marketData.get('BNBUSDT');

        if (btcData) {
            this.updatePriceElement('btc-price', btcData.price);
            this.updateChangeElement('btc-change', btcData.change24h);
        }

        if (ethData) {
            this.updatePriceElement('eth-price', ethData.price);
            this.updateChangeElement('eth-change', ethData.change24h);
        }

        if (bnbData) {
            this.updatePriceElement('bnb-price', bnbData.price);
            this.updateChangeElement('bnb-change', bnbData.change24h);
        }
    }

    // 更新价格元素
    updatePriceElement(elementId, price) {
        const element = document.getElementById(elementId);
        if (element) {
            const oldPrice = parseFloat(element.textContent.replace(/[$,]/g, ''));
            const newPrice = price;

            element.textContent = `$${this.formatNumber(newPrice, 2)}`;

            // 价格变化动画
            if (oldPrice !== newPrice) {
                element.style.transform = 'scale(1.05)';
                element.style.color = newPrice > oldPrice ? 'var(--color-success)' : 'var(--color-danger)';

                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                    element.style.color = 'var(--text-primary)';
                }, 300);
            }
        }
    }

    // 更新涨跌幅元素
    updateChangeElement(elementId, change) {
        const element = document.getElementById(elementId);
        if (element) {
            const changeSign = change >= 0 ? '+' : '';
            element.textContent = `${changeSign}${this.formatNumber(change, 2)}%`;
            element.className = `change ${change >= 0 ? 'positive' : 'negative'}`;
        }
    }

    // 初始化动画效果
    initAnimations() {
        // 添加滚动动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // 观察需要动画的元素
        document.querySelectorAll('.feature-card, .market-table-container').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });
    }

    // 跳转到交易页面
    goToTrade(symbol) {
        window.location.href = `/trade?symbol=${symbol}`;
    }

    // 切换移动端菜单
    toggleMobileMenu() {
        const mobileMenu = document.getElementById('mobileMenu');
        if (mobileMenu) {
            mobileMenu.classList.toggle('active');
        }
    }

    // 初始化移动端适配
    initMobileAdaptation() {
        // 检测屏幕尺寸
        const checkScreenSize = () => {
            const isMobile = window.innerWidth <= 768;
            const mobileToggle = document.querySelector('.mobile-menu-toggle');
            const nav = document.querySelector('.nav');

            if (mobileToggle && nav) {
                if (isMobile) {
                    mobileToggle.style.display = 'block';
                    nav.style.display = 'none';
                } else {
                    mobileToggle.style.display = 'none';
                    nav.style.display = 'flex';
                    // 关闭移动端菜单
                    const mobileMenu = document.getElementById('mobileMenu');
                    if (mobileMenu) {
                        mobileMenu.classList.remove('active');
                    }
                }
            }
        };

        // 初始检查
        checkScreenSize();

        // 监听窗口大小变化
        window.addEventListener('resize', checkScreenSize);

        // 点击外部关闭移动端菜单
        document.addEventListener('click', (e) => {
            const mobileMenu = document.getElementById('mobileMenu');
            const mobileToggle = document.querySelector('.mobile-menu-toggle');

            if (mobileMenu && mobileToggle &&
                !mobileMenu.contains(e.target) &&
                !mobileToggle.contains(e.target)) {
                mobileMenu.classList.remove('active');
            }
        });
    }

    // 添加触摸手势支持
    initTouchGestures() {
        let startY = 0;
        let startX = 0;

        document.addEventListener('touchstart', (e) => {
            startY = e.touches[0].clientY;
            startX = e.touches[0].clientX;
        });

        document.addEventListener('touchend', (e) => {
            const endY = e.changedTouches[0].clientY;
            const endX = e.changedTouches[0].clientX;
            const diffY = startY - endY;
            const diffX = startX - endX;

            // 检测向上滑动手势（刷新价格）
            if (Math.abs(diffY) > Math.abs(diffX) && diffY > 50) {
                this.updatePrices();
                this.showNotification('价格已更新', 'success');
            }
        });
    }

    // 优化移动端性能
    optimizeMobilePerformance() {
        // 减少移动端的更新频率
        if (window.innerWidth <= 768) {
            // 移动端每5秒更新一次价格
            if (this.priceUpdateInterval) {
                clearInterval(this.priceUpdateInterval);
            }
            this.priceUpdateInterval = setInterval(() => {
                this.updatePrices();
            }, 5000);
        }

        // 启用被动事件监听器
        document.addEventListener('scroll', this.handleScroll, { passive: true });
        document.addEventListener('touchstart', this.handleTouchStart, { passive: true });
    }

    // 处理滚动事件
    handleScroll() {
        // 滚动时的性能优化
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // 视差效果（仅在非移动端启用）
        if (window.innerWidth > 768) {
            const heroSection = document.querySelector('.hero-section');
            if (heroSection) {
                const offset = scrollTop * 0.5;
                heroSection.style.transform = `translateY(${offset}px)`;
            }
        }
    }

    // 处理触摸开始事件
    handleTouchStart(e) {
        // 触摸反馈
        if (e.target.classList.contains('btn') ||
            e.target.classList.contains('nav-link') ||
            e.target.classList.contains('table-row')) {
            e.target.style.opacity = '0.7';
            setTimeout(() => {
                e.target.style.opacity = '1';
            }, 150);
        }
    }

    // 显示通知
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // 格式化数字
    formatNumber(num, decimals = 2) {
        return parseFloat(num).toFixed(decimals);
    }

    // 格式化货币
    formatCurrency(amount, symbol = 'USDT') {
        return `${this.formatNumber(amount)} ${symbol}`;
    }

    // 格式化时间
    formatTime(timestamp) {
        const date = new Date(timestamp * 1000);
        return date.toLocaleString('zh-CN');
    }

    // 绑定事件
    bindEvents() {
        // 登录表单
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                const formData = new FormData(loginForm);
                const username = formData.get('username');
                const password = formData.get('password');
                
                if (await this.login(username, password)) {
                    window.location.href = '/trade';
                }
            });
        }

        // 注册表单
        const registerForm = document.getElementById('registerForm');
        if (registerForm) {
            registerForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                const formData = new FormData(registerForm);
                const userData = {
                    username: formData.get('username'),
                    email: formData.get('email'),
                    password: formData.get('password'),
                    confirm_password: formData.get('confirm_password')
                };

                if (userData.password !== userData.confirm_password) {
                    this.showNotification('两次密码输入不一致', 'error');
                    return;
                }

                if (await this.register(userData)) {
                    window.location.href = '/login';
                }
            });
        }

        // 交易表单
        const tradeForm = document.getElementById('tradeForm');
        if (tradeForm) {
            tradeForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.submitTrade();
            });
        }
    }

    // 提交交易
    async submitTrade() {
        const formData = new FormData(document.getElementById('tradeForm'));
        const tradeData = {
            symbol: formData.get('symbol'),
            order_type: formData.get('order_type'),
            margin: parseFloat(formData.get('margin')),
            leverage: parseInt(formData.get('leverage')),
            stop_loss: formData.get('stop_loss') || null,
            take_profit: formData.get('take_profit') || null
        };

        try {
            const response = await this.api('/leverage/order', 'POST', tradeData);
            
            if (response.data.code === 1) {
                this.showNotification('订单创建成功', 'success');
                this.refreshOrders();
                document.getElementById('tradeForm').reset();
            } else {
                this.showNotification(response.data.msg, 'error');
            }
        } catch (error) {
            this.showNotification('订单创建失败', 'error');
        }
    }

    // 刷新订单列表
    async refreshOrders() {
        try {
            const response = await this.api('/leverage/orders');
            if (response.data.code === 1) {
                this.updateOrdersTable(response.data.data);
            }
        } catch (error) {
            console.error('刷新订单失败:', error);
        }
    }

    // 更新订单表格
    updateOrdersTable(orders) {
        const tbody = document.querySelector('#ordersTable tbody');
        if (!tbody) return;

        tbody.innerHTML = orders.map(order => `
            <tr>
                <td>${order.id}</td>
                <td>${order.symbol}</td>
                <td class="${order.order_type == 1 ? 'text-success' : 'text-danger'}">
                    ${order.order_type == 1 ? '做多' : '做空'}
                </td>
                <td>${this.formatCurrency(order.margin)}</td>
                <td>${order.leverage}x</td>
                <td>${this.formatNumber(order.open_price)}</td>
                <td class="${parseFloat(order.profit_loss) >= 0 ? 'text-success' : 'text-danger'}">
                    ${this.formatCurrency(order.profit_loss)}
                </td>
                <td>
                    <span class="badge badge-${order.status === 'open' ? 'success' : 'secondary'}">
                        ${order.status === 'open' ? '持仓中' : '已平仓'}
                    </span>
                </td>
                <td>
                    ${order.status === 'open' ? 
                        `<button onclick="app.closeOrder(${order.id})" class="btn btn-sm btn-danger">平仓</button>` : 
                        '-'
                    }
                </td>
            </tr>
        `).join('');
    }

    // 平仓
    async closeOrder(orderId) {
        if (!confirm('确定要平仓吗？')) return;

        try {
            const response = await this.api('/leverage/close-order', 'POST', { order_id: orderId });
            
            if (response.data.code === 1) {
                this.showNotification('平仓成功', 'success');
                this.refreshOrders();
            } else {
                this.showNotification(response.data.msg, 'error');
            }
        } catch (error) {
            this.showNotification('平仓失败', 'error');
        }
    }
}

// 初始化应用
const app = new GVDApp();

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化图表
    if (typeof TradingView !== 'undefined' && document.getElementById('tradingview_chart')) {
        new TradingView.widget({
            "width": "100%",
            "height": 400,
            "symbol": "BINANCE:BTCUSDT",
            "interval": "1",
            "timezone": "Asia/Shanghai",
            "theme": "dark",
            "style": "1",
            "locale": "zh_CN",
            "toolbar_bg": "#1e2329",
            "enable_publishing": false,
            "hide_top_toolbar": false,
            "hide_legend": true,
            "save_image": false,
            "container_id": "tradingview_chart"
        });
    }

    // 定时刷新数据
    setInterval(() => {
        if (app.user && window.location.pathname === '/trade') {
            app.refreshOrders();
        }
    }, 5000);
});
