<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - GVD交易平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
        }

        .register-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 450px;
            max-width: 90%;
        }

        .register-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .register-header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .register-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .register-form {
            padding: 40px 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .verify-group {
            display: flex;
            gap: 10px;
        }

        .verify-group input {
            flex: 1;
        }

        .verify-btn {
            padding: 12px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            white-space: nowrap;
            transition: background 0.3s;
        }

        .verify-btn:hover {
            background: #5a6fd8;
        }

        .verify-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .register-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .register-btn:hover {
            transform: translateY(-2px);
        }

        .register-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .form-links {
            text-align: center;
            margin-top: 25px;
        }

        .form-links a {
            color: #667eea;
            text-decoration: none;
        }

        .form-links a:hover {
            text-decoration: underline;
        }

        .error-message {
            background: #fee;
            color: #c33;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }

        .success-message {
            background: #efe;
            color: #3c3;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 10px;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #667eea;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .password-strength {
            margin-top: 5px;
            font-size: 12px;
        }

        .strength-weak { color: #ff4757; }
        .strength-medium { color: #ffa502; }
        .strength-strong { color: #2ed573; }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-header">
            <h1>注册账号</h1>
            <p>加入GVD，开启您的数字货币交易之旅</p>
        </div>
        
        <div class="register-form">
            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>
            
            <form id="registerForm">
                <div class="form-group">
                    <label for="email">邮箱地址 *</label>
                    <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" placeholder="可选，默认使用邮箱">
                </div>
                
                <div class="form-group">
                    <label for="password">密码 *</label>
                    <input type="password" id="password" name="password" required>
                    <div class="password-strength" id="passwordStrength"></div>
                </div>
                
                <div class="form-group">
                    <label for="confirmPassword">确认密码 *</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" required>
                </div>
                
                <div class="form-group">
                    <label for="verifyCode">邮箱验证码 *</label>
                    <div class="verify-group">
                        <input type="text" id="verifyCode" name="verifyCode" placeholder="请输入验证码" required>
                        <button type="button" class="verify-btn" id="sendCodeBtn">发送验证码</button>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="inviteCode">邀请码</label>
                    <input type="text" id="inviteCode" name="inviteCode" placeholder="可选">
                </div>
                
                <button type="submit" class="register-btn" id="registerBtn">注册</button>
                <div class="loading" id="loading"></div>
            </form>
            
            <div class="form-links">
                <a href="login.html">已有账号？立即登录</a>
            </div>
        </div>
    </div>

    <script>
        class RegisterManager {
            constructor() {
                this.form = document.getElementById('registerForm');
                this.registerBtn = document.getElementById('registerBtn');
                this.sendCodeBtn = document.getElementById('sendCodeBtn');
                this.loading = document.getElementById('loading');
                this.errorMessage = document.getElementById('errorMessage');
                this.successMessage = document.getElementById('successMessage');
                this.passwordStrength = document.getElementById('passwordStrength');
                
                this.countdown = 0;
                this.bindEvents();
            }
            
            bindEvents() {
                this.form.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleRegister();
                });
                
                this.sendCodeBtn.addEventListener('click', () => {
                    this.sendVerificationCode();
                });
                
                document.getElementById('password').addEventListener('input', (e) => {
                    this.checkPasswordStrength(e.target.value);
                });
            }
            
            async handleRegister() {
                const formData = new FormData(this.form);
                const data = {
                    email: formData.get('email'),
                    username: formData.get('username'),
                    password: formData.get('password'),
                    confirm_password: formData.get('confirmPassword'),
                    verify_code: formData.get('verifyCode'),
                    invite_code: formData.get('inviteCode')
                };
                
                if (!this.validateForm(data)) {
                    return;
                }
                
                this.setLoading(true);
                
                try {
                    const response = await fetch('/api/auth/register', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(data)
                    });
                    
                    const result = await response.json();
                    
                    if (result.code === 1) {
                        this.showSuccess('注册成功！正在跳转到登录页面...');
                        
                        setTimeout(() => {
                            window.location.href = 'login.html';
                        }, 2000);
                    } else {
                        this.showError(result.msg || '注册失败');
                    }
                } catch (error) {
                    console.error('注册请求失败:', error);
                    this.showError('网络错误，请稍后重试');
                } finally {
                    this.setLoading(false);
                }
            }
            
            async sendVerificationCode() {
                const email = document.getElementById('email').value;
                
                if (!email) {
                    this.showError('请先输入邮箱地址');
                    return;
                }
                
                if (!this.validateEmail(email)) {
                    this.showError('邮箱格式不正确');
                    return;
                }
                
                this.sendCodeBtn.disabled = true;
                
                try {
                    const response = await fetch('/api/auth/send-code', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            email: email,
                            type: 'register'
                        })
                    });
                    
                    const result = await response.json();
                    
                    if (result.code === 1) {
                        this.showSuccess('验证码发送成功，请查收邮件');
                        this.startCountdown();
                        
                        // 开发环境显示验证码
                        if (result.data && result.data.code) {
                            console.log('验证码:', result.data.code);
                            this.showSuccess(`验证码发送成功：${result.data.code}`);
                        }
                    } else {
                        this.showError(result.msg || '发送失败');
                        this.sendCodeBtn.disabled = false;
                    }
                } catch (error) {
                    console.error('发送验证码失败:', error);
                    this.showError('网络错误，请稍后重试');
                    this.sendCodeBtn.disabled = false;
                }
            }
            
            validateForm(data) {
                if (!data.email) {
                    this.showError('请输入邮箱地址');
                    return false;
                }
                
                if (!this.validateEmail(data.email)) {
                    this.showError('邮箱格式不正确');
                    return false;
                }
                
                if (!data.password) {
                    this.showError('请输入密码');
                    return false;
                }
                
                if (data.password.length < 6) {
                    this.showError('密码长度不能少于6位');
                    return false;
                }
                
                if (data.password !== data.confirm_password) {
                    this.showError('两次输入的密码不一致');
                    return false;
                }
                
                if (!data.verify_code) {
                    this.showError('请输入验证码');
                    return false;
                }
                
                return true;
            }
            
            validateEmail(email) {
                const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return re.test(email);
            }
            
            checkPasswordStrength(password) {
                let strength = 0;
                let text = '';
                let className = '';
                
                if (password.length >= 6) strength++;
                if (password.match(/[a-z]/)) strength++;
                if (password.match(/[A-Z]/)) strength++;
                if (password.match(/[0-9]/)) strength++;
                if (password.match(/[^a-zA-Z0-9]/)) strength++;
                
                if (strength < 2) {
                    text = '密码强度：弱';
                    className = 'strength-weak';
                } else if (strength < 4) {
                    text = '密码强度：中等';
                    className = 'strength-medium';
                } else {
                    text = '密码强度：强';
                    className = 'strength-strong';
                }
                
                this.passwordStrength.textContent = text;
                this.passwordStrength.className = `password-strength ${className}`;
            }
            
            startCountdown() {
                this.countdown = 60;
                const timer = setInterval(() => {
                    this.countdown--;
                    this.sendCodeBtn.textContent = `${this.countdown}秒后重发`;
                    
                    if (this.countdown <= 0) {
                        clearInterval(timer);
                        this.sendCodeBtn.textContent = '发送验证码';
                        this.sendCodeBtn.disabled = false;
                    }
                }, 1000);
            }
            
            setLoading(loading) {
                this.registerBtn.disabled = loading;
                this.loading.style.display = loading ? 'block' : 'none';
                this.registerBtn.textContent = loading ? '注册中...' : '注册';
            }
            
            showError(message) {
                this.errorMessage.textContent = message;
                this.errorMessage.style.display = 'block';
                this.successMessage.style.display = 'none';
            }
            
            showSuccess(message) {
                this.successMessage.textContent = message;
                this.successMessage.style.display = 'block';
                this.errorMessage.style.display = 'none';
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            new RegisterManager();
        });
    </script>
</body>
</html>
