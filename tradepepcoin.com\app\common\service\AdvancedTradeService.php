<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\AdvancedOrder;
use app\common\model\GridStrategy;
use app\common\model\TradingPair;
use app\common\model\UserAsset;
use think\facade\Db;
use think\facade\Log;

/**
 * 高级交易服务
 */
class AdvancedTradeService
{
    /**
     * 创建止盈止损订单
     */
    public function createStopOrder(int $userId, array $data): array
    {
        try {
            // 验证交易对
            $tradingPair = TradingPair::getBySymbol($data['symbol']);
            if (!$tradingPair) {
                return ['code' => 0, 'msg' => '交易对不存在'];
            }

            // 验证用户资产
            $assetCheck = $this->checkUserAsset($userId, $data);
            if (!$assetCheck['code']) {
                return $assetCheck;
            }

            // 创建高级订单
            $orderData = [
                'user_id' => $userId,
                'symbol' => $data['symbol'],
                'type' => $data['type'],
                'side' => $data['side'],
                'amount' => $data['amount'],
                'trigger_price' => $data['trigger_price'],
                'order_price' => $data['order_price'] ?? 0,
                'stop_price' => $data['stop_price'] ?? 0,
                'take_profit_price' => $data['take_profit_price'] ?? 0,
                'trailing_percent' => $data['trailing_percent'] ?? 0
            ];

            $result = AdvancedOrder::createAdvancedOrder($orderData);
            
            if ($result['code']) {
                // 冻结用户资产
                $this->freezeUserAsset($userId, $data);
                
                Log::info("创建高级订单成功", [
                    'user_id' => $userId,
                    'order_id' => $result['data']->order_id,
                    'type' => $data['type']
                ]);
            }

            return $result;
        } catch (\Exception $e) {
            Log::error("创建高级订单失败: " . $e->getMessage());
            return ['code' => 0, 'msg' => '创建失败：' . $e->getMessage()];
        }
    }

    /**
     * 创建网格策略
     */
    public function createGridStrategy(int $userId, array $data): array
    {
        try {
            // 验证参数
            $validation = $this->validateGridStrategy($data);
            if (!$validation['code']) {
                return $validation;
            }

            // 验证用户资产
            $assetCheck = $this->checkGridStrategyAsset($userId, $data);
            if (!$assetCheck['code']) {
                return $assetCheck;
            }

            // 创建网格策略
            $strategyData = [
                'user_id' => $userId,
                'symbol' => $data['symbol'],
                'name' => $data['name'],
                'type' => $data['type'],
                'min_price' => $data['min_price'],
                'max_price' => $data['max_price'],
                'grid_count' => $data['grid_count'],
                'investment_amount' => $data['investment_amount'],
                'stop_loss_price' => $data['stop_loss_price'] ?? 0,
                'take_profit_price' => $data['take_profit_price'] ?? 0,
                'settings' => $data['settings'] ?? []
            ];

            $result = GridStrategy::createStrategy($strategyData);
            
            if ($result['code']) {
                Log::info("创建网格策略成功", [
                    'user_id' => $userId,
                    'strategy_id' => $result['data']->strategy_id,
                    'symbol' => $data['symbol']
                ]);
            }

            return $result;
        } catch (\Exception $e) {
            Log::error("创建网格策略失败: " . $e->getMessage());
            return ['code' => 0, 'msg' => '创建失败：' . $e->getMessage()];
        }
    }

    /**
     * 启动网格策略
     */
    public function startGridStrategy(int $userId, string $strategyId): array
    {
        try {
            $strategy = GridStrategy::where('user_id', $userId)
                                  ->where('strategy_id', $strategyId)
                                  ->find();

            if (!$strategy) {
                return ['code' => 0, 'msg' => '策略不存在'];
            }

            if ($strategy->start()) {
                Log::info("启动网格策略成功", [
                    'user_id' => $userId,
                    'strategy_id' => $strategyId
                ]);
                
                return ['code' => 1, 'msg' => '策略启动成功'];
            } else {
                return ['code' => 0, 'msg' => '策略启动失败'];
            }
        } catch (\Exception $e) {
            Log::error("启动网格策略失败: " . $e->getMessage());
            return ['code' => 0, 'msg' => '启动失败：' . $e->getMessage()];
        }
    }

    /**
     * 停止网格策略
     */
    public function stopGridStrategy(int $userId, string $strategyId): array
    {
        try {
            $strategy = GridStrategy::where('user_id', $userId)
                                  ->where('strategy_id', $strategyId)
                                  ->find();

            if (!$strategy) {
                return ['code' => 0, 'msg' => '策略不存在'];
            }

            if ($strategy->stop()) {
                Log::info("停止网格策略成功", [
                    'user_id' => $userId,
                    'strategy_id' => $strategyId
                ]);
                
                return ['code' => 1, 'msg' => '策略停止成功'];
            } else {
                return ['code' => 0, 'msg' => '策略停止失败'];
            }
        } catch (\Exception $e) {
            Log::error("停止网格策略失败: " . $e->getMessage());
            return ['code' => 0, 'msg' => '停止失败：' . $e->getMessage()];
        }
    }

    /**
     * 取消高级订单
     */
    public function cancelAdvancedOrder(int $userId, string $orderId): array
    {
        try {
            $order = AdvancedOrder::where('user_id', $userId)
                                 ->where('order_id', $orderId)
                                 ->find();

            if (!$order) {
                return ['code' => 0, 'msg' => '订单不存在'];
            }

            if ($order->cancel()) {
                // 解冻用户资产
                $this->unfreezeUserAsset($userId, $order);
                
                Log::info("取消高级订单成功", [
                    'user_id' => $userId,
                    'order_id' => $orderId
                ]);
                
                return ['code' => 1, 'msg' => '订单取消成功'];
            } else {
                return ['code' => 0, 'msg' => '订单取消失败'];
            }
        } catch (\Exception $e) {
            Log::error("取消高级订单失败: " . $e->getMessage());
            return ['code' => 0, 'msg' => '取消失败：' . $e->getMessage()];
        }
    }

    /**
     * 检查高级订单触发条件
     */
    public function checkAdvancedOrderTriggers(): void
    {
        try {
            $pendingOrders = AdvancedOrder::where('status', AdvancedOrder::STATUS_PENDING)
                                        ->select();

            foreach ($pendingOrders as $order) {
                $currentPrice = $this->getCurrentPrice($order->symbol);
                
                if ($order->shouldTrigger($currentPrice)) {
                    $order->trigger();
                    
                    // 执行订单
                    if ($order->execute()) {
                        Log::info("高级订单触发执行成功", [
                            'order_id' => $order->order_id,
                            'trigger_price' => $currentPrice
                        ]);
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error("检查高级订单触发失败: " . $e->getMessage());
        }
    }

    /**
     * 验证用户资产
     */
    private function checkUserAsset(int $userId, array $data): array
    {
        $symbol = $data['symbol'];
        $side = $data['side'];
        $amount = $data['amount'];
        $price = $data['order_price'] ?? $data['trigger_price'];

        // 解析交易对
        $parts = explode('/', $symbol);
        $baseCoin = $parts[0];
        $quoteCoin = $parts[1];

        if ($side === 'buy') {
            // 买入需要计价币种
            $requiredAmount = $amount * $price;
            $userAsset = UserAsset::getUserAsset($userId, $quoteCoin);
            
            if (!$userAsset || $userAsset->available < $requiredAmount) {
                return ['code' => 0, 'msg' => "{$quoteCoin}余额不足"];
            }
        } else {
            // 卖出需要基础币种
            $userAsset = UserAsset::getUserAsset($userId, $baseCoin);
            
            if (!$userAsset || $userAsset->available < $amount) {
                return ['code' => 0, 'msg' => "{$baseCoin}余额不足"];
            }
        }

        return ['code' => 1, 'msg' => '资产验证通过'];
    }

    /**
     * 验证网格策略资产
     */
    private function checkGridStrategyAsset(int $userId, array $data): array
    {
        $investmentAmount = $data['investment_amount'];
        
        // 网格策略通常使用USDT作为投资金额
        $userAsset = UserAsset::getUserAsset($userId, 'USDT');
        
        if (!$userAsset || $userAsset->available < $investmentAmount) {
            return ['code' => 0, 'msg' => 'USDT余额不足'];
        }

        return ['code' => 1, 'msg' => '资产验证通过'];
    }

    /**
     * 验证网格策略参数
     */
    private function validateGridStrategy(array $data): array
    {
        // 验证价格范围
        if ($data['min_price'] >= $data['max_price']) {
            return ['code' => 0, 'msg' => '最低价格必须小于最高价格'];
        }

        // 验证网格数量
        if ($data['grid_count'] < 2 || $data['grid_count'] > 100) {
            return ['code' => 0, 'msg' => '网格数量必须在2-100之间'];
        }

        // 验证投资金额
        if ($data['investment_amount'] <= 0) {
            return ['code' => 0, 'msg' => '投资金额必须大于0'];
        }

        // 验证当前价格是否在网格范围内
        $currentPrice = $this->getCurrentPrice($data['symbol']);
        if ($currentPrice < $data['min_price'] || $currentPrice > $data['max_price']) {
            return ['code' => 0, 'msg' => '当前价格不在网格价格范围内'];
        }

        return ['code' => 1, 'msg' => '参数验证通过'];
    }

    /**
     * 冻结用户资产
     */
    private function freezeUserAsset(int $userId, array $data): void
    {
        $symbol = $data['symbol'];
        $side = $data['side'];
        $amount = $data['amount'];
        $price = $data['order_price'] ?? $data['trigger_price'];

        // 解析交易对
        $parts = explode('/', $symbol);
        $baseCoin = $parts[0];
        $quoteCoin = $parts[1];

        if ($side === 'buy') {
            $freezeAmount = $amount * $price;
            UserAsset::freezeAsset($userId, $quoteCoin, $freezeAmount);
        } else {
            UserAsset::freezeAsset($userId, $baseCoin, $amount);
        }
    }

    /**
     * 解冻用户资产
     */
    private function unfreezeUserAsset(int $userId, AdvancedOrder $order): void
    {
        $parts = explode('/', $order->symbol);
        $baseCoin = $parts[0];
        $quoteCoin = $parts[1];

        if ($order->side === 'buy') {
            $unfreezeAmount = $order->amount * $order->order_price;
            UserAsset::unfreezeAsset($userId, $quoteCoin, $unfreezeAmount);
        } else {
            UserAsset::unfreezeAsset($userId, $baseCoin, $order->amount);
        }
    }

    /**
     * 获取当前价格
     */
    private function getCurrentPrice(string $symbol): float
    {
        $tradingPair = TradingPair::getBySymbol($symbol);
        return $tradingPair ? $tradingPair->current_price : 0;
    }

    /**
     * 获取用户高级订单列表
     */
    public function getUserAdvancedOrders(int $userId, array $params = []): array
    {
        $where = ['user_id' => $userId];
        
        if (!empty($params['symbol'])) {
            $where['symbol'] = $params['symbol'];
        }
        
        if (!empty($params['type'])) {
            $where['type'] = $params['type'];
        }
        
        if (isset($params['status']) && $params['status'] !== '') {
            $where['status'] = $params['status'];
        }

        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 20;

        $orders = AdvancedOrder::where($where)
                              ->order('created_at', 'desc')
                              ->paginate([
                                  'list_rows' => $limit,
                                  'page' => $page
                              ]);

        return [
            'data' => $orders->items(),
            'total' => $orders->total(),
            'page' => $page,
            'limit' => $limit
        ];
    }

    /**
     * 获取用户网格策略列表
     */
    public function getUserGridStrategies(int $userId, array $params = []): array
    {
        $where = ['user_id' => $userId];
        
        if (!empty($params['symbol'])) {
            $where['symbol'] = $params['symbol'];
        }
        
        if (isset($params['status']) && $params['status'] !== '') {
            $where['status'] = $params['status'];
        }

        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 20;

        $strategies = GridStrategy::where($where)
                                 ->order('created_at', 'desc')
                                 ->paginate([
                                     'list_rows' => $limit,
                                     'page' => $page
                                 ]);

        return [
            'data' => $strategies->items(),
            'total' => $strategies->total(),
            'page' => $page,
            'limit' => $limit
        ];
    }
}
