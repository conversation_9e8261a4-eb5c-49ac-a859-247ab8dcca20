<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\common\service\LeverageService;
use app\common\service\FuturesService;
use think\facade\Log;

/**
 * 交易结算定时任务
 */
class TradingSettle extends Command
{
    protected function configure()
    {
        $this->setName('trading:settle')
            ->setDescription('执行交易结算任务');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始执行交易结算任务...');
        
        try {
            // 1. 检查杠杆交易强制平仓
            $this->checkLeverageLiquidation($output);
            
            // 2. 执行期货订单撮合
            $this->matchFuturesOrders($output);
            
            // 3. 检查期货合约交割
            $this->checkFuturesSettlement($output);
            
            // 4. 更新用户资产统计
            $this->updateUserAssetStats($output);
            
            $output->writeln('交易结算任务执行完成');
            Log::info('交易结算任务执行成功');
            
        } catch (\Exception $e) {
            $output->writeln('交易结算任务执行失败: ' . $e->getMessage());
            Log::error('交易结算任务执行失败: ' . $e->getMessage());
        }
    }

    /**
     * 检查杠杆交易强制平仓
     */
    private function checkLeverageLiquidation(Output $output): void
    {
        $output->writeln('检查杠杆交易强制平仓...');
        
        $leverageService = new LeverageService();
        $leverageService->checkLiquidation();
        
        $output->writeln('杠杆交易强制平仓检查完成');
    }

    /**
     * 执行期货订单撮合
     */
    private function matchFuturesOrders(Output $output): void
    {
        $output->writeln('执行期货订单撮合...');
        
        $futuresService = new FuturesService();
        $futuresService->matchOrders();
        
        $output->writeln('期货订单撮合完成');
    }

    /**
     * 检查期货合约交割
     */
    private function checkFuturesSettlement(Output $output): void
    {
        $output->writeln('检查期货合约交割...');
        
        $futuresService = new FuturesService();
        $futuresService->autoSettle();
        
        $output->writeln('期货合约交割检查完成');
    }

    /**
     * 更新用户资产统计
     */
    private function updateUserAssetStats(Output $output): void
    {
        $output->writeln('更新用户资产统计...');
        
        try {
            // 更新用户总资产
            \think\facade\Db::execute("
                UPDATE gvd_user_assets 
                SET total = available + frozen 
                WHERE total != available + frozen
            ");
            
            // 清理过期的缓存
            \think\facade\Cache::clear();
            
            $output->writeln('用户资产统计更新完成');
            
        } catch (\Exception $e) {
            $output->writeln('用户资产统计更新失败: ' . $e->getMessage());
            Log::error('用户资产统计更新失败: ' . $e->getMessage());
        }
    }
}
