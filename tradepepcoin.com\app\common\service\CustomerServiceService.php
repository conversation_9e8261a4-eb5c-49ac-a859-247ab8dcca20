<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\CustomerServiceTicket;
use app\common\model\CustomerServiceMessage;
use app\common\model\User;
use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

/**
 * 客服系统服务
 */
class CustomerServiceService
{
    // 工单状态
    const TICKET_STATUS_OPEN = 1;      // 开启
    const TICKET_STATUS_PENDING = 2;   // 等待回复
    const TICKET_STATUS_RESOLVED = 3;  // 已解决
    const TICKET_STATUS_CLOSED = 4;    // 已关闭

    // 消息类型
    const MESSAGE_TYPE_USER = 'user';        // 用户消息
    const MESSAGE_TYPE_SERVICE = 'service';  // 客服消息
    const MESSAGE_TYPE_SYSTEM = 'system';    // 系统消息

    // 优先级
    const PRIORITY_LOW = 1;     // 低
    const PRIORITY_NORMAL = 2;  // 普通
    const PRIORITY_HIGH = 3;    // 高
    const PRIORITY_URGENT = 4;  // 紧急

    /**
     * 创建客服工单
     */
    public function createTicket(int $userId, array $data): array
    {
        try {
            // 验证用户
            $user = User::find($userId);
            if (!$user) {
                return ['code' => 0, 'msg' => '用户不存在'];
            }

            // 验证必填字段
            if (empty($data['subject']) || empty($data['content'])) {
                return ['code' => 0, 'msg' => '标题和内容不能为空'];
            }

            // 检查用户是否有未关闭的工单
            $openTicket = CustomerServiceTicket::where('user_id', $userId)
                                              ->whereIn('status', [self::TICKET_STATUS_OPEN, self::TICKET_STATUS_PENDING])
                                              ->find();

            if ($openTicket) {
                return ['code' => 0, 'msg' => '您还有未处理的工单，请等待客服回复'];
            }

            // 创建工单
            $ticketData = [
                'ticket_id' => $this->generateTicketId(),
                'user_id' => $userId,
                'subject' => $data['subject'],
                'category' => $data['category'] ?? 'general',
                'priority' => $data['priority'] ?? self::PRIORITY_NORMAL,
                'status' => self::TICKET_STATUS_OPEN,
                'created_at' => date('Y-m-d H:i:s')
            ];

            $ticket = CustomerServiceTicket::create($ticketData);

            if ($ticket) {
                // 创建初始消息
                $this->createMessage($ticket->ticket_id, $userId, self::MESSAGE_TYPE_USER, $data['content']);

                // 通知客服
                $this->notifyCustomerService($ticket);

                Log::info("客服工单创建成功", [
                    'ticket_id' => $ticket->ticket_id,
                    'user_id' => $userId,
                    'subject' => $data['subject']
                ]);

                return [
                    'code' => 1,
                    'msg' => '工单创建成功',
                    'data' => [
                        'ticket_id' => $ticket->ticket_id,
                        'status' => $ticket->status
                    ]
                ];
            } else {
                return ['code' => 0, 'msg' => '工单创建失败'];
            }

        } catch (\Exception $e) {
            Log::error('创建客服工单失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '工单创建失败'];
        }
    }

    /**
     * 发送消息
     */
    public function sendMessage(string $ticketId, int $userId, string $content, string $type = self::MESSAGE_TYPE_USER): array
    {
        try {
            // 验证工单
            $ticket = CustomerServiceTicket::where('ticket_id', $ticketId)->find();
            if (!$ticket) {
                return ['code' => 0, 'msg' => '工单不存在'];
            }

            // 验证权限
            if ($type === self::MESSAGE_TYPE_USER && $ticket->user_id !== $userId) {
                return ['code' => 0, 'msg' => '无权限操作'];
            }

            // 验证工单状态
            if ($ticket->status === self::TICKET_STATUS_CLOSED) {
                return ['code' => 0, 'msg' => '工单已关闭，无法发送消息'];
            }

            // 创建消息
            $message = $this->createMessage($ticketId, $userId, $type, $content);

            if ($message) {
                // 更新工单状态
                if ($type === self::MESSAGE_TYPE_USER) {
                    $ticket->status = self::TICKET_STATUS_PENDING;
                    $ticket->last_reply_at = date('Y-m-d H:i:s');
                } elseif ($type === self::MESSAGE_TYPE_SERVICE) {
                    $ticket->status = self::TICKET_STATUS_OPEN;
                    $ticket->service_id = $userId;
                    $ticket->last_reply_at = date('Y-m-d H:i:s');
                }
                $ticket->save();

                // 发送实时通知
                $this->sendRealtimeNotification($ticketId, $message, $type);

                return [
                    'code' => 1,
                    'msg' => '消息发送成功',
                    'data' => $message->toArray()
                ];
            } else {
                return ['code' => 0, 'msg' => '消息发送失败'];
            }

        } catch (\Exception $e) {
            Log::error('发送客服消息失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '消息发送失败'];
        }
    }

    /**
     * 获取工单列表
     */
    public function getTickets(int $userId, array $filters = []): array
    {
        try {
            $query = CustomerServiceTicket::where('user_id', $userId);

            // 筛选条件
            if (isset($filters['status'])) {
                $query->where('status', $filters['status']);
            }

            if (!empty($filters['category'])) {
                $query->where('category', $filters['category']);
            }

            $tickets = $query->order('created_at', 'desc')
                            ->limit($filters['limit'] ?? 20)
                            ->select();

            return [
                'code' => 1,
                'data' => $tickets->toArray()
            ];

        } catch (\Exception $e) {
            Log::error('获取工单列表失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取工单列表失败'];
        }
    }

    /**
     * 获取工单详情和消息
     */
    public function getTicketDetail(string $ticketId, int $userId): array
    {
        try {
            // 获取工单
            $ticket = CustomerServiceTicket::where('ticket_id', $ticketId)->find();
            if (!$ticket) {
                return ['code' => 0, 'msg' => '工单不存在'];
            }

            // 验证权限
            if ($ticket->user_id !== $userId) {
                return ['code' => 0, 'msg' => '无权限查看'];
            }

            // 获取消息列表
            $messages = CustomerServiceMessage::where('ticket_id', $ticketId)
                                             ->order('created_at', 'asc')
                                             ->select();

            // 标记消息为已读
            CustomerServiceMessage::where('ticket_id', $ticketId)
                                 ->where('type', self::MESSAGE_TYPE_SERVICE)
                                 ->where('is_read', 0)
                                 ->update(['is_read' => 1, 'read_at' => date('Y-m-d H:i:s')]);

            return [
                'code' => 1,
                'data' => [
                    'ticket' => $ticket->toArray(),
                    'messages' => $messages->toArray()
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取工单详情失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取工单详情失败'];
        }
    }

    /**
     * 关闭工单
     */
    public function closeTicket(string $ticketId, int $userId): array
    {
        try {
            $ticket = CustomerServiceTicket::where('ticket_id', $ticketId)->find();
            if (!$ticket) {
                return ['code' => 0, 'msg' => '工单不存在'];
            }

            // 验证权限
            if ($ticket->user_id !== $userId) {
                return ['code' => 0, 'msg' => '无权限操作'];
            }

            if ($ticket->status === self::TICKET_STATUS_CLOSED) {
                return ['code' => 0, 'msg' => '工单已关闭'];
            }

            // 关闭工单
            $ticket->status = self::TICKET_STATUS_CLOSED;
            $ticket->closed_at = date('Y-m-d H:i:s');
            $ticket->save();

            // 添加系统消息
            $this->createMessage($ticketId, 0, self::MESSAGE_TYPE_SYSTEM, '用户关闭了工单');

            return [
                'code' => 1,
                'msg' => '工单已关闭'
            ];

        } catch (\Exception $e) {
            Log::error('关闭工单失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '关闭工单失败'];
        }
    }

    /**
     * 获取未读消息数量
     */
    public function getUnreadCount(int $userId): int
    {
        try {
            // 获取用户的工单
            $ticketIds = CustomerServiceTicket::where('user_id', $userId)->column('ticket_id');
            
            if (empty($ticketIds)) {
                return 0;
            }

            // 统计未读的客服消息
            $unreadCount = CustomerServiceMessage::whereIn('ticket_id', $ticketIds)
                                                 ->where('type', self::MESSAGE_TYPE_SERVICE)
                                                 ->where('is_read', 0)
                                                 ->count();

            return $unreadCount;

        } catch (\Exception $e) {
            Log::error('获取未读消息数量失败: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 客服端获取工单列表
     */
    public function getServiceTickets(array $filters = []): array
    {
        try {
            $query = CustomerServiceTicket::with(['user']);

            // 筛选条件
            if (isset($filters['status'])) {
                $query->where('status', $filters['status']);
            }

            if (!empty($filters['category'])) {
                $query->where('category', $filters['category']);
            }

            if (isset($filters['priority'])) {
                $query->where('priority', $filters['priority']);
            }

            if (!empty($filters['service_id'])) {
                $query->where('service_id', $filters['service_id']);
            }

            $tickets = $query->order('priority', 'desc')
                            ->order('created_at', 'asc')
                            ->limit($filters['limit'] ?? 50)
                            ->select();

            return [
                'code' => 1,
                'data' => $tickets->toArray()
            ];

        } catch (\Exception $e) {
            Log::error('获取客服工单列表失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取工单列表失败'];
        }
    }

    /**
     * 创建消息
     */
    private function createMessage(string $ticketId, int $userId, string $type, string $content): ?CustomerServiceMessage
    {
        try {
            $messageData = [
                'ticket_id' => $ticketId,
                'user_id' => $userId,
                'type' => $type,
                'content' => $content,
                'is_read' => 0,
                'created_at' => date('Y-m-d H:i:s')
            ];

            return CustomerServiceMessage::create($messageData);

        } catch (\Exception $e) {
            Log::error('创建客服消息失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 生成工单ID
     */
    private function generateTicketId(): string
    {
        return 'CS' . date('YmdHis') . mt_rand(1000, 9999);
    }

    /**
     * 通知客服
     */
    private function notifyCustomerService($ticket): void
    {
        try {
            // 这里可以发送邮件、短信或推送通知给客服
            // 暂时记录日志
            Log::info("新客服工单通知", [
                'ticket_id' => $ticket->ticket_id,
                'subject' => $ticket->subject,
                'priority' => $ticket->priority
            ]);

            // 可以集成第三方通知服务
            // $notificationService = new NotificationService();
            // $notificationService->notifyCustomerService($ticket);

        } catch (\Exception $e) {
            Log::error('通知客服失败: ' . $e->getMessage());
        }
    }

    /**
     * 发送实时通知
     */
    private function sendRealtimeNotification(string $ticketId, $message, string $type): void
    {
        try {
            // 通过WebSocket发送实时通知
            $notificationData = [
                'type' => 'customer_service_message',
                'ticket_id' => $ticketId,
                'message' => $message->toArray(),
                'sender_type' => $type
            ];

            // 这里应该集成WebSocket服务
            // $webSocketService = new WebSocketService();
            // $webSocketService->sendNotification($userId, $notificationData);

            // 暂时使用缓存存储通知
            Cache::set("cs_notification_{$ticketId}", $notificationData, 300);

        } catch (\Exception $e) {
            Log::error('发送实时通知失败: ' . $e->getMessage());
        }
    }

    /**
     * 分配工单给客服
     */
    public function assignTicket(string $ticketId, int $serviceId, int $operatorId): array
    {
        try {
            $ticket = CustomerServiceTicket::where('ticket_id', $ticketId)->find();
            if (!$ticket) {
                return ['code' => 0, 'msg' => '工单不存在'];
            }

            // 验证客服用户
            $serviceUser = \app\common\model\User::find($serviceId);
            if (!$serviceUser || !in_array($serviceUser->role, ['admin', 'service'])) {
                return ['code' => 0, 'msg' => '无效的客服用户'];
            }

            $ticket->service_id = $serviceId;
            $ticket->updated_at = date('Y-m-d H:i:s');
            $ticket->save();

            // 添加系统消息
            $this->createMessage($ticketId, 0, self::MESSAGE_TYPE_SYSTEM,
                "工单已分配给客服：{$serviceUser->username}");

            Log::info("工单分配成功", [
                'ticket_id' => $ticketId,
                'service_id' => $serviceId,
                'operator_id' => $operatorId
            ]);

            return [
                'code' => 1,
                'msg' => '工单分配成功'
            ];

        } catch (\Exception $e) {
            Log::error('分配工单失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '分配工单失败'];
        }
    }
}
