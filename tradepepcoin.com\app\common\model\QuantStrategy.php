<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 量化策略模型
 */
class QuantStrategy extends Model
{
    protected $table = 'ce_quant_strategies';
    protected $pk = 'id';

    // 设置字段信息
    protected $schema = [
        'id'                => 'int',
        'strategy_id'       => 'string',
        'user_id'          => 'int',
        'name'             => 'string',
        'description'      => 'string',
        'type'             => 'string',
        'symbols'          => 'json',
        'parameters'       => 'json',
        'risk_level'       => 'int',
        'max_drawdown'     => 'float',
        'investment_amount' => 'float',
        'ml_model'         => 'string',
        'status'           => 'int',
        'backtest_result'  => 'json',
        'started_at'       => 'datetime',
        'stopped_at'       => 'datetime',
        'created_at'       => 'datetime',
        'updated_at'       => 'datetime',
    ];

    // JSON字段自动转换
    protected $json = ['symbols', 'parameters', 'backtest_result'];

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 关联回测结果
     */
    public function backtests()
    {
        return $this->hasMany(QuantBacktest::class, 'strategy_id', 'strategy_id');
    }

    /**
     * 关联交易信号
     */
    public function signals()
    {
        return $this->hasMany(QuantSignal::class, 'strategy_id', 'strategy_id');
    }

    /**
     * 获取策略状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusTexts = [
            0 => '草稿',
            1 => '回测中',
            2 => '就绪',
            3 => '运行中',
            4 => '暂停',
            5 => '停止',
            6 => '错误'
        ];

        return $statusTexts[$data['status']] ?? '未知';
    }

    /**
     * 获取风险等级文本
     */
    public function getRiskLevelTextAttr($value, $data)
    {
        $riskTexts = [
            1 => '低风险',
            2 => '中风险',
            3 => '高风险'
        ];

        return $riskTexts[$data['risk_level']] ?? '未知';
    }

    /**
     * 根据策略ID获取策略
     */
    public static function getByStrategyId(string $strategyId)
    {
        return self::where('strategy_id', $strategyId)->find();
    }

    /**
     * 获取用户的策略列表
     */
    public static function getUserStrategies(int $userId, array $filters = [])
    {
        $query = self::where('user_id', $userId);

        if (!empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        return $query->order('created_at', 'desc')->select();
    }

    /**
     * 获取运行中的策略
     */
    public static function getRunningStrategies()
    {
        return self::where('status', 3)->select();
    }
}
