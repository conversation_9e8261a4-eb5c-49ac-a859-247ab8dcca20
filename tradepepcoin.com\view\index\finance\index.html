{extend name="index/layout" /}

{block name="css"}
<style>
.finance-container {
    padding: 20px 0;
    background: #f8f9fa;
    min-height: 100vh;
}

.asset-overview {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    border-radius: 15px;
    margin-bottom: 30px;
    text-align: center;
}

.total-asset {
    font-size: 48px;
    font-weight: bold;
    margin: 20px 0;
}

.asset-breakdown {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.asset-item {
    text-align: center;
}

.asset-value {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.asset-label {
    opacity: 0.8;
    font-size: 14px;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.action-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.action-card:hover {
    transform: translateY(-5px);
}

.action-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.action-icon.deposit {
    color: #28a745;
}

.action-icon.withdraw {
    color: #dc3545;
}

.action-icon.history {
    color: #667eea;
}

.action-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 10px;
}

.action-desc {
    color: #666;
    margin-bottom: 20px;
}

.action-btn {
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    font-weight: bold;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s;
}

.action-btn.deposit {
    background: #28a745;
    color: white;
}

.action-btn.withdraw {
    background: #dc3545;
    color: white;
}

.action-btn.history {
    background: #667eea;
    color: white;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.assets-table {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #333;
    padding: 15px;
}

.table td {
    padding: 15px;
    vertical-align: middle;
}

.coin-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.coin-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #667eea;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.coin-name {
    font-weight: 600;
}

.coin-fullname {
    color: #666;
    font-size: 12px;
}

.balance-amount {
    font-weight: 600;
    font-size: 16px;
}

.balance-value {
    color: #666;
    font-size: 12px;
}

.action-buttons {
    display: flex;
    gap: 10px;
}

.btn-sm {
    padding: 6px 15px;
    font-size: 12px;
    border-radius: 15px;
}

.recent-records {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.records-tabs {
    display: flex;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 20px;
}

.records-tab {
    padding: 15px 20px;
    background: transparent;
    border: none;
    color: #666;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s;
}

.records-tab.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

.record-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #f8f9fa;
}

.record-info {
    flex: 1;
}

.record-type {
    font-weight: 600;
    margin-bottom: 5px;
}

.record-time {
    color: #666;
    font-size: 12px;
}

.record-amount {
    text-align: right;
}

.record-value {
    font-weight: 600;
    font-size: 16px;
}

.record-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 10px;
}

.record-status.completed {
    background: #d4edda;
    color: #155724;
}

.record-status.pending {
    background: #fff3cd;
    color: #856404;
}

.record-status.failed {
    background: #f8d7da;
    color: #721c24;
}
</style>
{/block}

{block name="content"}
<div class="finance-container">
    <div class="container">
        <!-- 资产总览 -->
        <div class="asset-overview">
            <h2>我的资产</h2>
            <div class="total-asset">
                ${$total_asset.total_value|default=0|number_format:2} USDT
            </div>
            <p>总资产估值（USDT）</p>
            
            <div class="asset-breakdown">
                <div class="asset-item">
                    <div class="asset-value">${$total_asset.total_available|default=0|number_format:2}</div>
                    <div class="asset-label">可用资产</div>
                </div>
                <div class="asset-item">
                    <div class="asset-value">${$total_asset.total_frozen|default=0|number_format:2}</div>
                    <div class="asset-label">冻结资产</div>
                </div>
            </div>
        </div>

        <!-- 快捷操作 -->
        <div class="quick-actions">
            <div class="action-card">
                <div class="action-icon deposit">
                    <i class="fas fa-plus-circle"></i>
                </div>
                <div class="action-title">充值</div>
                <div class="action-desc">向您的账户充值数字货币</div>
                <a href="/finance/deposit" class="action-btn deposit">立即充值</a>
            </div>
            
            <div class="action-card">
                <div class="action-icon withdraw">
                    <i class="fas fa-minus-circle"></i>
                </div>
                <div class="action-title">提现</div>
                <div class="action-desc">将数字货币提现到外部地址</div>
                <a href="/finance/withdraw" class="action-btn withdraw">立即提现</a>
            </div>
            
            <div class="action-card">
                <div class="action-icon history">
                    <i class="fas fa-history"></i>
                </div>
                <div class="action-title">交易记录</div>
                <div class="action-desc">查看充值提现历史记录</div>
                <a href="/finance/depositHistory" class="action-btn history">查看记录</a>
            </div>
        </div>

        <!-- 资产列表 -->
        <div class="assets-table">
            <h4 class="mb-4">我的资产</h4>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>币种</th>
                            <th>可用余额</th>
                            <th>冻结余额</th>
                            <th>估值(USDT)</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {volist name="user_assets" id="asset"}
                        <tr>
                            <td>
                                <div class="coin-info">
                                    <div class="coin-icon">{$asset.coin_symbol|substr=0,1}</div>
                                    <div>
                                        <div class="coin-name">{$asset.coin_symbol}</div>
                                        <div class="coin-fullname">{$asset.coin_symbol} Coin</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="balance-amount">{$asset.available|number_format:6}</div>
                            </td>
                            <td>
                                <div class="balance-amount">{$asset.frozen|number_format:6}</div>
                            </td>
                            <td>
                                <div class="balance-value">≈ ${$asset.available * 1|number_format:2}</div>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="/finance/deposit?coin={$asset.coin_symbol}" class="btn btn-success btn-sm">充值</a>
                                    <a href="/finance/withdraw?coin={$asset.coin_symbol}" class="btn btn-danger btn-sm">提现</a>
                                </div>
                            </td>
                        </tr>
                        {/volist}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 最近记录 -->
        <div class="recent-records">
            <h4 class="mb-4">最近记录</h4>
            
            <div class="records-tabs">
                <button class="records-tab active" data-tab="deposits">充值记录</button>
                <button class="records-tab" data-tab="withdraws">提现记录</button>
            </div>

            <!-- 充值记录 -->
            <div class="records-content" id="deposits-content">
                {volist name="recent_deposits" id="deposit"}
                <div class="record-item">
                    <div class="record-info">
                        <div class="record-type">充值 {$deposit.coin_symbol}</div>
                        <div class="record-time">{$deposit.created_at}</div>
                    </div>
                    <div class="record-amount">
                        <div class="record-value">+{$deposit.amount|number_format:6}</div>
                        <span class="record-status {$deposit.status}">{$deposit.status_text}</span>
                    </div>
                </div>
                {/volist}
            </div>

            <!-- 提现记录 -->
            <div class="records-content" id="withdraws-content" style="display: none;">
                {volist name="recent_withdraws" id="withdraw"}
                <div class="record-item">
                    <div class="record-info">
                        <div class="record-type">提现 {$withdraw.coin_symbol}</div>
                        <div class="record-time">{$withdraw.created_at}</div>
                    </div>
                    <div class="record-amount">
                        <div class="record-value">-{$withdraw.amount|number_format:6}</div>
                        <span class="record-status {$withdraw.status}">{$withdraw.status_text}</span>
                    </div>
                </div>
                {/volist}
            </div>
        </div>
    </div>
</div>
{/block}

{block name="js"}
<script>
$(document).ready(function() {
    // 记录标签切换
    $('.records-tab').click(function() {
        $('.records-tab').removeClass('active');
        $(this).addClass('active');
        
        const tab = $(this).data('tab');
        $('.records-content').hide();
        $('#' + tab + '-content').show();
    });
});
</script>
{/block}
