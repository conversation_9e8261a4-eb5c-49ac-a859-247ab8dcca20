<!DOCTYPE html>
<html lang="zh-CN" id="htmlLang">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="pageTitle">GVD</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: Arial, sans-serif; 
            background: #1a1a1a; 
            color: #fff; 
            min-height: 100vh;
        }
        .header { 
            background: #2d2d2d; 
            padding: 15px 0; 
            border-bottom: 1px solid #333;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 0 20px; 
        }
        .nav { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
        }
        .logo { 
            font-size: 24px; 
            font-weight: bold; 
            color: #fff;
        }
        .nav-links { 
            display: flex; 
            gap: 30px; 
            align-items: center;
        }
        .nav-links a { 
            color: #fff; 
            text-decoration: none; 
            padding: 10px 15px;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .nav-links a:hover { 
            background: #007bff; 
        }
        .dropdown { position: relative; display: inline-block; }
        .dropdown-content { 
            display: none; position: absolute; right: 0; background: #2d2d2d; 
            min-width: 150px; box-shadow: 0 8px 16px rgba(0,0,0,0.3); z-index: 1;
            border: 1px solid #333; border-radius: 5px;
        }
        .dropdown-content a { 
            color: #fff; padding: 10px 15px; text-decoration: none; 
            display: block; border-radius: 0;
        }
        .dropdown-content a:hover { background: #007bff; }
        .dropdown:hover .dropdown-content { display: block; }
        .lang-selector { 
            background: #333; color: #fff; border: none; padding: 8px 12px; 
            border-radius: 5px; cursor: pointer; font-size: 14px;
        }
        .lang-selector:hover { background: #007bff; }
        
        .ticker { 
            background: #2d2d2d; 
            padding: 10px 0; 
            border-bottom: 1px solid #333;
            overflow: hidden;
            margin-top: 64px;
        }
        .ticker-scroll { 
            white-space: nowrap; 
            animation: scroll 30s linear infinite;
        }
        .ticker-item { 
            display: inline-block; 
            margin-right: 40px; 
            font-size: 14px;
        }
        .price-up { color: #28a745; }
        .price-down { color: #dc3545; }
        @keyframes scroll {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
        }
        
        .hero { 
            text-align: center; 
            padding: 100px 0; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .hero h1 { 
            font-size: 48px; 
            margin-bottom: 20px; 
        }
        .hero p { 
            font-size: 20px; 
            margin-bottom: 40px; 
            opacity: 0.9;
        }
        .buttons { 
            display: flex; 
            gap: 20px; 
            justify-content: center; 
            flex-wrap: wrap;
        }
        .btn { 
            padding: 15px 30px; 
            border: none; 
            border-radius: 25px; 
            font-size: 16px; 
            font-weight: bold; 
            text-decoration: none; 
            display: inline-block;
            transition: all 0.3s;
        }
        .btn-primary { 
            background: #007bff; 
            color: #fff; 
        }
        .btn-secondary { 
            background: rgba(255,255,255,0.2); 
            color: #fff; 
            border: 1px solid rgba(255,255,255,0.3);
        }
        .btn:hover { 
            transform: translateY(-2px); 
        }
        .features { 
            padding: 80px 0; 
        }
        .features-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 30px; 
            margin-top: 50px;
        }
        .feature { 
            background: #2d2d2d; 
            padding: 40px 30px; 
            border-radius: 10px; 
            text-align: center;
            border: 1px solid #333;
            transition: transform 0.3s;
        }
        .feature:hover { 
            transform: translateY(-5px); 
            border-color: #007bff;
        }
        .feature-icon { 
            font-size: 48px; 
            margin-bottom: 20px; 
        }
        .feature h3 { 
            margin-bottom: 15px; 
            font-size: 24px;
        }
        .feature p { 
            color: #999; 
            line-height: 1.6;
        }
        
        @media (max-width: 768px) {
            .nav-links { display: none; }
            .hero h1 { font-size: 36px; }
            .buttons { flex-direction: column; align-items: center; }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="nav">
                <div class="logo">🚀 <span id="siteName">GVD</span></div>
                <div class="nav-links">
                    <a href="/" id="navHome">首页</a>
                    <div class="dropdown">
                        <a href="#" id="navTrade">交易</a>
                        <div class="dropdown-content">
                            <a href="#" onclick="checkLoginAndTrade()" id="navFutures">期货交易</a>
                            <a href="/contract/" id="navContract">秒合约</a>
                        </div>
                    </div>
                    <a href="/ido/" id="navIdo">新币认购</a>
                    <div class="dropdown">
                        <a href="#" id="navAssets">资产</a>
                        <div class="dropdown-content">
                            <a href="/wallet/" id="navWallet">我的钱包</a>
                            <a href="/wallet/deposit.html" id="navDeposit">充值</a>
                            <a href="/wallet/withdraw.html" id="navWithdraw">提现</a>
                        </div>
                    </div>
                    <a href="/auth/login.html" id="navLogin">登录</a>
                    <a href="/auth/register.html" id="navRegister">注册</a>
                    <select class="lang-selector" id="langSelector">
                        <option value="zh-CN">简体中文</option>
                        <option value="en-US">English</option>
                        <option value="ja-JP">日本語</option>
                        <option value="ko-KR">한국어</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <div class="ticker">
        <div class="container">
            <div class="ticker-scroll">
                <span class="ticker-item">
                    <strong>BTC/USDT</strong> 
                    <span class="price-up">$45,230.50 +2.34%</span>
                </span>
                <span class="ticker-item">
                    <strong>ETH/USDT</strong> 
                    <span class="price-down">$2,845.20 -1.25%</span>
                </span>
                <span class="ticker-item">
                    <strong>BNB/USDT</strong> 
                    <span class="price-up">$315.80 +0.85%</span>
                </span>
                <span class="ticker-item">
                    <strong>ADA/USDT</strong> 
                    <span class="price-up">$0.4520 +3.15%</span>
                </span>
            </div>
        </div>
    </div>

    <div class="hero">
        <div class="container">
            <h1 id="heroTitle">专业GVD</h1>
            <p id="heroDesc">安全、稳定、高效的数字资产交易服务</p>
            <div class="buttons">
                <a href="/auth/register.html" class="btn btn-primary" id="btnRegister">立即注册</a>
                <a href="#" onclick="checkLoginAndTrade()" class="btn btn-secondary" id="btnTrade">开始交易</a>
            </div>
        </div>
    </div>

    <div class="features">
        <div class="container">
            <div class="text-center mb-5">
                <h2 id="featuresTitle">核心功能</h2>
                <p id="featuresDesc">为您提供全方位的数字资产交易服务</p>
            </div>
            
            <div class="features-grid">
                <div class="feature">
                    <div class="feature-icon">📊</div>
                    <h3 id="feature1Title">期货交易</h3>
                    <p id="feature1Desc">专业期货交易，支持多倍杠杆，做多做空双向盈利</p>
                    <a href="#" onclick="checkLoginAndTrade()" class="btn btn-primary" style="margin-top: 20px;" id="feature1Btn">立即交易</a>
                </div>
                <div class="feature">
                    <div class="feature-icon">⚡</div>
                    <h3 id="feature2Title">秒合约</h3>
                    <p id="feature2Desc">60秒快速合约，高收益机会，简单易懂</p>
                    <a href="/contract/" class="btn btn-primary" style="margin-top: 20px;" id="feature2Btn">开始合约</a>
                </div>
                <div class="feature">
                    <div class="feature-icon">🚀</div>
                    <h3 id="feature3Title">新币认购</h3>
                    <p id="feature3Desc">优质项目首发，抢先参与，获得超额收益</p>
                    <a href="/ido/" class="btn btn-primary" style="margin-top: 20px;" id="feature3Btn">查看项目</a>
                </div>
            </div>
        </div>
    </div>

        </div>
    </div>

    <script>
        // 多语言配置
        const languages = {
            'zh-CN': {
                pageTitle: 'GVD',
                siteName: 'GVD',
                navHome: '首页',
                navTrade: '交易',
                navFutures: '期货交易',
                navContract: '秒合约',
                navIdo: '新币认购',
                navAssets: '资产',
                navWallet: '我的钱包',
                navDeposit: '充值',
                navWithdraw: '提现',
                navLogin: '登录',
                navRegister: '注册',
                heroTitle: '专业GVD',
                heroDesc: '安全、稳定、高效的数字资产交易服务',
                btnRegister: '立即注册',
                btnTrade: '开始交易',
                featuresTitle: '核心功能',
                featuresDesc: '为您提供全方位的数字资产交易服务',
                feature1Title: '期货交易',
                feature1Desc: '专业期货交易，支持多倍杠杆，做多做空双向盈利',
                feature1Btn: '立即交易',
                feature2Title: '秒合约',
                feature2Desc: '60秒快速合约，高收益机会，简单易懂',
                feature2Btn: '开始合约',
                feature3Title: '新币认购',
                feature3Desc: '优质项目首发，抢先参与，获得超额收益',
                feature3Btn: '查看项目',
                footerText: '© 2024 GVD - 专业的数字资产交易服务',
                adminBtn: '管理后台',
                agentBtn: '代理后台'
            },
            'en-US': {
                pageTitle: 'Cryptocurrency Trading Platform',
                siteName: 'Crypto Exchange',
                navHome: 'Home',
                navTrade: 'Trade',
                navFutures: 'Futures',
                navContract: 'Contracts',
                navIdo: 'IDO',
                navAssets: 'Assets',
                navWallet: 'Wallet',
                navDeposit: 'Deposit',
                navWithdraw: 'Withdraw',
                navLogin: 'Login',
                navRegister: 'Register',
                heroTitle: 'Professional Cryptocurrency Trading Platform',
                heroDesc: 'Safe, stable and efficient digital asset trading services',
                btnRegister: 'Register Now',
                btnTrade: 'Start Trading',
                featuresTitle: 'Core Features',
                featuresDesc: 'Comprehensive digital asset trading services',
                feature1Title: 'Futures Trading',
                feature1Desc: 'Professional futures trading with leverage support',
                feature1Btn: 'Trade Now',
                feature2Title: 'Quick Contracts',
                feature2Desc: '60-second quick contracts, high profit opportunities',
                feature2Btn: 'Start Contract',
                feature3Title: 'Token Launch',
                feature3Desc: 'Premium project launches, early participation',
                feature3Btn: 'View Projects',
                footerText: '© 2024 Cryptocurrency Trading Platform',
                adminBtn: 'Admin Panel',
                agentBtn: 'Agent Panel'
            }
        };

        // 语言切换功能
        function switchLanguage(lang) {
            const texts = languages[lang];
            if (!texts) return;

            // 更新页面文本
            Object.keys(texts).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    if (key === 'pageTitle') {
                        document.title = texts[key];
                    } else {
                        element.textContent = texts[key];
                    }
                }
            });

            // 更新HTML lang属性
            document.getElementById('htmlLang').setAttribute('lang', lang);
            
            // 保存语言设置
            localStorage.setItem('selectedLanguage', lang);
            
            console.log('语言已切换到:', lang);
        }

        // 语言选择器事件
        document.getElementById('langSelector').addEventListener('change', function() {
            switchLanguage(this.value);
        });

        // 页面加载时恢复语言设置
        document.addEventListener('DOMContentLoaded', function() {
            const savedLang = localStorage.getItem('selectedLanguage') || 'zh-CN';
            document.getElementById('langSelector').value = savedLang;
            switchLanguage(savedLang);
            
            // 模拟价格更新
            setInterval(updatePrices, 5000);
        });

        // 模拟价格更新
        function updatePrices() {
            const priceElements = document.querySelectorAll('.ticker-item .price-up, .ticker-item .price-down');
            priceElements.forEach(element => {
                const currentText = element.textContent;
                const match = currentText.match(/\$?([\d,]+\.?\d*)/);
                if (match) {
                    const price = parseFloat(match[1].replace(',', ''));
                    const change = (Math.random() - 0.5) * price * 0.02; // ±2% 变化
                    const newPrice = Math.max(0.01, price + change);
                    const changePercent = ((newPrice - price) / price * 100).toFixed(2);
                    
                    element.textContent = `$${newPrice.toLocaleString()} ${changePercent > 0 ? '+' : ''}${changePercent}%`;
                    element.className = changePercent > 0 ? 'price-up' : 'price-down';
                }
            });
        }
    </script>
</body>
</html>
<script>
// 检查登录状态
function isLoggedIn() {
    return localStorage.getItem('isLoggedIn') === 'true';
}

// 检查登录并跳转到秒合约
function checkLoginAndTrade() {
    if (!isLoggedIn()) {
        alert('请先登录或注册');
        window.location.href = '/auth/login.html';
    } else {
        window.location.href = '/contract/';
    }
}
</script>
    <div style="background: #2d2d2d; padding: 40px 0; text-align: center; border-top: 1px solid #333;">
        <div class="container">
            <h2>下载GVD APP</h2>
            <p>随时随地进行数字资产交易</p>
            <div style="display: flex; gap: 20px; justify-content: center; margin-top: 20px; flex-wrap: wrap;">
                <a href="/download/gvd-android.apk" style="display: flex; align-items: center; gap: 10px; padding: 12px 24px; background: #333; color: #fff; text-decoration: none; border-radius: 8px;">
                    <div style="font-size: 24px;">🤖</div>
                    <div>
                        <div style="font-size: 12px; opacity: 0.8;">Android</div>
                        <div style="font-size: 14px; font-weight: bold;">立即下载</div>
                    </div>
                </a>
                <a href="/download/gvd-ios.ipa" style="display: flex; align-items: center; gap: 10px; padding: 12px 24px; background: #333; color: #fff; text-decoration: none; border-radius: 8px;">
                    <div style="font-size: 24px;">🍎</div>
                    <div>
                        <div style="font-size: 12px; opacity: 0.8;">iOS</div>
                        <div style="font-size: 14px; font-weight: bold;">立即下载</div>
                    </div>
                </a>
            </div>
        </div>
    </div>

    <div style="background: #1a1a1a; padding: 20px 0; text-align: center; border-top: 1px solid #333;">
        <div class="container">
            <p style="margin: 0; color: #999;">© 2024 GVD - 全球数字资产交易平台</p>
        </div>
    </div>

<script>
// 检查登录并跳转到秒合约
function checkLoginAndTrade() {
    const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
    if (!isLoggedIn) {
        alert('请先登录或注册');
        window.location.href = '/auth/login.html';
    } else {
        window.location.href = '/contract/';
    }
}

// 检查登录并跳转到秒合约（用于功能按钮）
function checkLoginAndContract() {
    checkLoginAndTrade();
}
</script>
<script>
function checkLoginAndTrade() {
    const isLoggedIn = localStorage.getItem("isLoggedIn") === "true";
    if (!isLoggedIn) {
        alert("请先登录或注册");
        window.location.href = "/auth/login.html";
    } else {
        window.location.href = "/contract/";
    }
}
function checkLoginAndContract() {
    checkLoginAndTrade();
}
</script>
