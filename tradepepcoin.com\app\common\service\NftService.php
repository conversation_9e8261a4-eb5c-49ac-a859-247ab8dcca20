<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\NftCollection;
use app\common\model\NftToken;
use app\common\model\NftOrder;
use app\common\model\NftAuction;
use app\common\model\User;
use app\common\model\UserAsset;
use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

/**
 * NFT服务类
 */
class NftService
{
    // NFT状态
    const STATUS_DRAFT = 0;      // 草稿
    const STATUS_MINTED = 1;     // 已铸造
    const STATUS_LISTED = 2;     // 已上架
    const STATUS_SOLD = 3;       // 已售出
    const STATUS_BURNED = 4;     // 已销毁

    // 订单状态
    const ORDER_PENDING = 0;     // 待支付
    const ORDER_PAID = 1;        // 已支付
    const ORDER_COMPLETED = 2;   // 已完成
    const ORDER_CANCELLED = 3;   // 已取消

    // 拍卖状态
    const AUCTION_PENDING = 0;   // 待开始
    const AUCTION_ACTIVE = 1;    // 进行中
    const AUCTION_ENDED = 2;     // 已结束
    const AUCTION_CANCELLED = 3; // 已取消

    /**
     * 创建NFT集合
     */
    public function createCollection(int $userId, array $data): array
    {
        try {
            // 验证用户权限
            $user = User::find($userId);
            if (!$user || !$this->canCreateCollection($user)) {
                return ['code' => 0, 'msg' => '没有创建集合的权限'];
            }

            // 验证集合数据
            $validation = $this->validateCollectionData($data);
            if (!$validation['code']) {
                return $validation;
            }

            // 创建集合
            $collectionData = [
                'collection_id' => $this->generateCollectionId(),
                'creator_id' => $userId,
                'name' => $data['name'],
                'symbol' => $data['symbol'],
                'description' => $data['description'] ?? '',
                'image' => $data['image'] ?? '',
                'banner_image' => $data['banner_image'] ?? '',
                'category' => $data['category'] ?? 'art',
                'royalty_rate' => $data['royalty_rate'] ?? 0,
                'max_supply' => $data['max_supply'] ?? 0,
                'mint_price' => $data['mint_price'] ?? 0,
                'mint_currency' => $data['mint_currency'] ?? 'ETH',
                'is_verified' => 0,
                'status' => NftCollection::STATUS_ACTIVE
            ];

            $collection = NftCollection::create($collectionData);

            if ($collection) {
                Log::info("NFT集合创建成功", [
                    'collection_id' => $collection->collection_id,
                    'creator_id' => $userId
                ]);

                return [
                    'code' => 1,
                    'msg' => 'NFT集合创建成功',
                    'data' => ['collection_id' => $collection->collection_id]
                ];
            } else {
                return ['code' => 0, 'msg' => 'NFT集合创建失败'];
            }
        } catch (\Exception $e) {
            Log::error('NFT集合创建失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'NFT集合创建失败'];
        }
    }

    /**
     * 铸造NFT
     */
    public function mintNft(int $userId, array $data): array
    {
        try {
            // 获取集合信息
            $collection = NftCollection::where('collection_id', $data['collection_id'])
                                      ->where('status', NftCollection::STATUS_ACTIVE)
                                      ->find();

            if (!$collection) {
                return ['code' => 0, 'msg' => 'NFT集合不存在或已关闭'];
            }

            // 验证铸造权限
            if (!$this->canMintNft($userId, $collection)) {
                return ['code' => 0, 'msg' => '没有铸造权限'];
            }

            // 检查供应量限制
            if ($collection->max_supply > 0 && $collection->total_supply >= $collection->max_supply) {
                return ['code' => 0, 'msg' => '已达到最大供应量'];
            }

            // 验证铸造费用
            if ($collection->mint_price > 0) {
                $paymentCheck = $this->checkMintPayment($userId, $collection);
                if (!$paymentCheck['code']) {
                    return $paymentCheck;
                }
            }

            // 开始事务
            Db::startTrans();
            try {
                // 扣除铸造费用
                if ($collection->mint_price > 0) {
                    $this->deductMintFee($userId, $collection);
                }

                // 创建NFT
                $nftData = [
                    'token_id' => $this->generateTokenId(),
                    'collection_id' => $collection->collection_id,
                    'owner_id' => $userId,
                    'creator_id' => $data['creator_id'] ?? $userId,
                    'name' => $data['name'],
                    'description' => $data['description'] ?? '',
                    'image' => $data['image'],
                    'animation_url' => $data['animation_url'] ?? '',
                    'external_url' => $data['external_url'] ?? '',
                    'attributes' => $data['attributes'] ?? [],
                    'metadata' => $data['metadata'] ?? [],
                    'token_uri' => $this->generateTokenUri($data),
                    'blockchain' => $data['blockchain'] ?? 'ETH',
                    'contract_address' => $collection->contract_address ?? '',
                    'status' => self::STATUS_MINTED,
                    'minted_at' => date('Y-m-d H:i:s')
                ];

                $nft = NftToken::create($nftData);

                // 更新集合统计
                $collection->total_supply++;
                $collection->save();

                Db::commit();

                Log::info("NFT铸造成功", [
                    'token_id' => $nft->token_id,
                    'collection_id' => $collection->collection_id,
                    'owner_id' => $userId
                ]);

                return [
                    'code' => 1,
                    'msg' => 'NFT铸造成功',
                    'data' => [
                        'token_id' => $nft->token_id,
                        'token_uri' => $nft->token_uri
                    ]
                ];
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('NFT铸造失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'NFT铸造失败'];
        }
    }

    /**
     * 上架NFT
     */
    public function listNft(int $userId, string $tokenId, array $listingData): array
    {
        try {
            // 获取NFT
            $nft = NftToken::where('token_id', $tokenId)
                          ->where('owner_id', $userId)
                          ->find();

            if (!$nft) {
                return ['code' => 0, 'msg' => 'NFT不存在或不属于您'];
            }

            if ($nft->status !== self::STATUS_MINTED) {
                return ['code' => 0, 'msg' => 'NFT状态不允许上架'];
            }

            // 验证上架数据
            $validation = $this->validateListingData($listingData);
            if (!$validation['code']) {
                return $validation;
            }

            // 开始事务
            Db::startTrans();
            try {
                // 创建订单
                $orderData = [
                    'order_id' => $this->generateOrderId(),
                    'token_id' => $tokenId,
                    'seller_id' => $userId,
                    'price' => $listingData['price'],
                    'currency' => $listingData['currency'] ?? 'ETH',
                    'sale_type' => $listingData['sale_type'] ?? 'fixed',
                    'start_time' => $listingData['start_time'] ?? date('Y-m-d H:i:s'),
                    'end_time' => $listingData['end_time'] ?? null,
                    'status' => self::ORDER_PENDING
                ];

                $order = NftOrder::create($orderData);

                // 更新NFT状态
                $nft->status = self::STATUS_LISTED;
                $nft->listed_price = $listingData['price'];
                $nft->listed_currency = $listingData['currency'] ?? 'ETH';
                $nft->listed_at = date('Y-m-d H:i:s');
                $nft->save();

                Db::commit();

                Log::info("NFT上架成功", [
                    'token_id' => $tokenId,
                    'order_id' => $order->order_id,
                    'price' => $listingData['price']
                ]);

                return [
                    'code' => 1,
                    'msg' => 'NFT上架成功',
                    'data' => ['order_id' => $order->order_id]
                ];
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('NFT上架失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'NFT上架失败'];
        }
    }

    /**
     * 购买NFT
     */
    public function buyNft(int $userId, string $orderId): array
    {
        try {
            // 获取订单
            $order = NftOrder::where('order_id', $orderId)
                            ->where('status', self::ORDER_PENDING)
                            ->find();

            if (!$order) {
                return ['code' => 0, 'msg' => '订单不存在或已失效'];
            }

            if ($order->seller_id === $userId) {
                return ['code' => 0, 'msg' => '不能购买自己的NFT'];
            }

            // 检查订单有效期
            if ($order->end_time && time() > strtotime($order->end_time)) {
                return ['code' => 0, 'msg' => '订单已过期'];
            }

            // 获取NFT
            $nft = NftToken::where('token_id', $order->token_id)->find();
            if (!$nft) {
                return ['code' => 0, 'msg' => 'NFT不存在'];
            }

            // 验证买家资产
            $assetCheck = $this->checkBuyerAssets($userId, $order);
            if (!$assetCheck['code']) {
                return $assetCheck;
            }

            // 开始事务
            Db::startTrans();
            try {
                // 扣除买家资产
                UserAsset::freezeAsset($userId, $order->currency, $order->price);

                // 计算费用分配
                $feeDistribution = $this->calculateFeeDistribution($order, $nft);

                // 转移资产给卖家
                UserAsset::addAsset($order->seller_id, $order->currency, $feeDistribution['seller_amount']);

                // 支付版税
                if ($feeDistribution['royalty_amount'] > 0) {
                    UserAsset::addAsset($nft->creator_id, $order->currency, $feeDistribution['royalty_amount']);
                }

                // 支付平台费用
                if ($feeDistribution['platform_fee'] > 0) {
                    // 平台费用处理
                }

                // 转移NFT所有权
                $nft->owner_id = $userId;
                $nft->status = self::STATUS_SOLD;
                $nft->last_sale_price = $order->price;
                $nft->last_sale_currency = $order->currency;
                $nft->last_sale_at = date('Y-m-d H:i:s');
                $nft->save();

                // 更新订单状态
                $order->buyer_id = $userId;
                $order->status = self::ORDER_COMPLETED;
                $order->completed_at = date('Y-m-d H:i:s');
                $order->save();

                // 更新集合统计
                $this->updateCollectionStats($nft->collection_id, $order->price, $order->currency);

                Db::commit();

                Log::info("NFT购买成功", [
                    'order_id' => $orderId,
                    'token_id' => $nft->token_id,
                    'buyer_id' => $userId,
                    'seller_id' => $order->seller_id,
                    'price' => $order->price
                ]);

                return [
                    'code' => 1,
                    'msg' => 'NFT购买成功',
                    'data' => [
                        'token_id' => $nft->token_id,
                        'transaction_hash' => $this->generateTransactionHash()
                    ]
                ];
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('NFT购买失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'NFT购买失败'];
        }
    }

    /**
     * 创建拍卖
     */
    public function createAuction(int $userId, string $tokenId, array $auctionData): array
    {
        try {
            // 获取NFT
            $nft = NftToken::where('token_id', $tokenId)
                          ->where('owner_id', $userId)
                          ->find();

            if (!$nft) {
                return ['code' => 0, 'msg' => 'NFT不存在或不属于您'];
            }

            // 验证拍卖数据
            $validation = $this->validateAuctionData($auctionData);
            if (!$validation['code']) {
                return $validation;
            }

            // 开始事务
            Db::startTrans();
            try {
                // 创建拍卖
                $auction = NftAuction::create([
                    'auction_id' => $this->generateAuctionId(),
                    'token_id' => $tokenId,
                    'seller_id' => $userId,
                    'starting_price' => $auctionData['starting_price'],
                    'reserve_price' => $auctionData['reserve_price'] ?? 0,
                    'currency' => $auctionData['currency'] ?? 'ETH',
                    'start_time' => $auctionData['start_time'],
                    'end_time' => $auctionData['end_time'],
                    'bid_increment' => $auctionData['bid_increment'] ?? 0,
                    'status' => self::AUCTION_PENDING
                ]);

                // 更新NFT状态
                $nft->status = self::STATUS_LISTED;
                $nft->save();

                Db::commit();

                return [
                    'code' => 1,
                    'msg' => '拍卖创建成功',
                    'data' => ['auction_id' => $auction->auction_id]
                ];
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('拍卖创建失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '拍卖创建失败'];
        }
    }

    /**
     * 竞拍
     */
    public function placeBid(int $userId, string $auctionId, float $bidAmount): array
    {
        try {
            // 获取拍卖
            $auction = NftAuction::where('auction_id', $auctionId)
                                ->where('status', self::AUCTION_ACTIVE)
                                ->find();

            if (!$auction) {
                return ['code' => 0, 'msg' => '拍卖不存在或已结束'];
            }

            if ($auction->seller_id === $userId) {
                return ['code' => 0, 'msg' => '不能竞拍自己的NFT'];
            }

            // 验证竞拍金额
            $validation = $this->validateBidAmount($auction, $bidAmount);
            if (!$validation['code']) {
                return $validation;
            }

            // 验证用户资产
            $userAsset = UserAsset::getUserAsset($userId, $auction->currency);
            if (!$userAsset || $userAsset->available < $bidAmount) {
                return ['code' => 0, 'msg' => '余额不足'];
            }

            // 开始事务
            Db::startTrans();
            try {
                // 冻结竞拍资金
                UserAsset::freezeAsset($userId, $auction->currency, $bidAmount);

                // 退还上一个竞拍者的资金
                if ($auction->highest_bidder_id) {
                    UserAsset::unfreezeAsset($auction->highest_bidder_id, $auction->currency, $auction->highest_bid);
                }

                // 更新拍卖信息
                $auction->highest_bidder_id = $userId;
                $auction->highest_bid = $bidAmount;
                $auction->bid_count++;
                $auction->last_bid_at = date('Y-m-d H:i:s');
                $auction->save();

                // 如果接近结束时间，延长拍卖
                $this->extendAuctionIfNeeded($auction);

                Db::commit();

                return [
                    'code' => 1,
                    'msg' => '竞拍成功',
                    'data' => [
                        'bid_amount' => $bidAmount,
                        'is_highest' => true
                    ]
                ];
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('竞拍失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '竞拍失败'];
        }
    }

    /**
     * 获取NFT列表
     */
    public function getNftList(array $filters = [], int $page = 1, int $limit = 20): array
    {
        $query = NftToken::with(['collection', 'owner']);

        // 状态筛选
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // 集合筛选
        if (!empty($filters['collection_id'])) {
            $query->where('collection_id', $filters['collection_id']);
        }

        // 价格范围筛选
        if (!empty($filters['min_price'])) {
            $query->where('listed_price', '>=', $filters['min_price']);
        }
        if (!empty($filters['max_price'])) {
            $query->where('listed_price', '<=', $filters['max_price']);
        }

        // 分类筛选
        if (!empty($filters['category'])) {
            $query->whereHas('collection', function($q) use ($filters) {
                $q->where('category', $filters['category']);
            });
        }

        // 排序
        $orderBy = $filters['order_by'] ?? 'created_at';
        $orderDirection = $filters['order_direction'] ?? 'desc';
        $query->order($orderBy, $orderDirection);

        $nfts = $query->paginate([
            'list_rows' => $limit,
            'page' => $page
        ]);

        return [
            'code' => 1,
            'data' => [
                'items' => $nfts->items(),
                'total' => $nfts->total(),
                'page' => $page,
                'limit' => $limit
            ]
        ];
    }

    /**
     * 获取用户NFT
     */
    public function getUserNfts(int $userId, array $filters = []): array
    {
        $query = NftToken::where('owner_id', $userId)
                        ->with(['collection']);

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['collection_id'])) {
            $query->where('collection_id', $filters['collection_id']);
        }

        $nfts = $query->order('created_at', 'desc')->select();

        return [
            'code' => 1,
            'data' => $nfts->toArray()
        ];
    }

    /**
     * 获取NFT详情
     */
    public function getNftDetail(string $tokenId): array
    {
        $nft = NftToken::where('token_id', $tokenId)
                      ->with(['collection', 'owner', 'creator'])
                      ->find();

        if (!$nft) {
            return ['code' => 0, 'msg' => 'NFT不存在'];
        }

        // 获取交易历史
        $tradeHistory = $this->getNftTradeHistory($tokenId);

        // 获取当前订单/拍卖
        $currentListing = $this->getCurrentListing($tokenId);

        return [
            'code' => 1,
            'data' => [
                'nft' => $nft->toArray(),
                'trade_history' => $tradeHistory,
                'current_listing' => $currentListing,
                'price_history' => $this->getNftPriceHistory($tokenId)
            ]
        ];
    }

    /**
     * 验证集合数据
     */
    private function validateCollectionData(array $data): array
    {
        if (empty($data['name'])) {
            return ['code' => 0, 'msg' => '集合名称不能为空'];
        }

        if (empty($data['symbol'])) {
            return ['code' => 0, 'msg' => '集合符号不能为空'];
        }

        // 检查符号是否已存在
        $existing = NftCollection::where('symbol', $data['symbol'])->find();
        if ($existing) {
            return ['code' => 0, 'msg' => '集合符号已存在'];
        }

        return ['code' => 1, 'msg' => '验证通过'];
    }

    /**
     * 验证上架数据
     */
    private function validateListingData(array $data): array
    {
        if (empty($data['price']) || $data['price'] <= 0) {
            return ['code' => 0, 'msg' => '价格必须大于0'];
        }

        if (!empty($data['end_time']) && strtotime($data['end_time']) <= time()) {
            return ['code' => 0, 'msg' => '结束时间必须在未来'];
        }

        return ['code' => 1, 'msg' => '验证通过'];
    }

    /**
     * 验证拍卖数据
     */
    private function validateAuctionData(array $data): array
    {
        if (empty($data['starting_price']) || $data['starting_price'] <= 0) {
            return ['code' => 0, 'msg' => '起拍价必须大于0'];
        }

        if (strtotime($data['start_time']) <= time()) {
            return ['code' => 0, 'msg' => '开始时间必须在未来'];
        }

        if (strtotime($data['end_time']) <= strtotime($data['start_time'])) {
            return ['code' => 0, 'msg' => '结束时间必须晚于开始时间'];
        }

        return ['code' => 1, 'msg' => '验证通过'];
    }

    /**
     * 其他辅助方法
     */
    private function generateCollectionId(): string
    {
        return 'COL' . date('YmdHis') . mt_rand(1000, 9999);
    }

    private function generateTokenId(): string
    {
        return 'NFT' . date('YmdHis') . mt_rand(10000, 99999);
    }

    private function generateOrderId(): string
    {
        return 'ORD' . date('YmdHis') . mt_rand(1000, 9999);
    }

    private function generateAuctionId(): string
    {
        return 'AUC' . date('YmdHis') . mt_rand(1000, 9999);
    }

    private function generateTokenUri(array $data): string
    {
        // 生成IPFS或其他存储的URI
        return 'https://ipfs.io/ipfs/' . md5(json_encode($data));
    }

    private function generateTransactionHash(): string
    {
        return '0x' . bin2hex(random_bytes(32));
    }

    private function canCreateCollection(User $user): bool
    {
        // 检查用户是否有创建集合的权限
        return $user->kyc_level >= 2; // 需要高级认证
    }

    private function canMintNft(int $userId, NftCollection $collection): bool
    {
        // 检查是否是集合创建者或有铸造权限
        return $collection->creator_id === $userId || $collection->is_public_mint;
    }

    private function checkMintPayment(int $userId, NftCollection $collection): array
    {
        $userAsset = UserAsset::getUserAsset($userId, $collection->mint_currency);
        
        if (!$userAsset || $userAsset->available < $collection->mint_price) {
            return ['code' => 0, 'msg' => '余额不足支付铸造费用'];
        }

        return ['code' => 1, 'msg' => '支付检查通过'];
    }

    private function deductMintFee(int $userId, NftCollection $collection): void
    {
        UserAsset::freezeAsset($userId, $collection->mint_currency, $collection->mint_price);
    }

    private function checkBuyerAssets(int $userId, NftOrder $order): array
    {
        $userAsset = UserAsset::getUserAsset($userId, $order->currency);
        
        if (!$userAsset || $userAsset->available < $order->price) {
            return ['code' => 0, 'msg' => '余额不足'];
        }

        return ['code' => 1, 'msg' => '资产检查通过'];
    }

    private function calculateFeeDistribution(NftOrder $order, NftToken $nft): array
    {
        $totalAmount = $order->price;
        $platformFeeRate = 0.025; // 2.5%平台费
        
        $collection = NftCollection::where('collection_id', $nft->collection_id)->find();
        $royaltyRate = $collection ? $collection->royalty_rate / 100 : 0;

        $platformFee = $totalAmount * $platformFeeRate;
        $royaltyAmount = $totalAmount * $royaltyRate;
        $sellerAmount = $totalAmount - $platformFee - $royaltyAmount;

        return [
            'total_amount' => $totalAmount,
            'platform_fee' => $platformFee,
            'royalty_amount' => $royaltyAmount,
            'seller_amount' => $sellerAmount
        ];
    }

    private function updateCollectionStats(string $collectionId, float $price, string $currency): void
    {
        $collection = NftCollection::where('collection_id', $collectionId)->find();
        if ($collection) {
            $collection->total_volume += $price;
            $collection->floor_price = min($collection->floor_price ?: $price, $price);
            $collection->save();
        }
    }

    private function validateBidAmount(NftAuction $auction, float $bidAmount): array
    {
        if ($bidAmount < $auction->starting_price) {
            return ['code' => 0, 'msg' => '竞拍金额不能低于起拍价'];
        }

        if ($auction->highest_bid > 0) {
            $minBid = $auction->highest_bid + $auction->bid_increment;
            if ($bidAmount < $minBid) {
                return ['code' => 0, 'msg' => "竞拍金额不能低于{$minBid}"];
            }
        }

        return ['code' => 1, 'msg' => '竞拍金额验证通过'];
    }

    private function extendAuctionIfNeeded(NftAuction $auction): void
    {
        $timeRemaining = strtotime($auction->end_time) - time();
        
        // 如果剩余时间少于15分钟，延长15分钟
        if ($timeRemaining < 900) {
            $auction->end_time = date('Y-m-d H:i:s', time() + 900);
            $auction->save();
        }
    }

    private function getNftTradeHistory(string $tokenId): array
    {
        // 获取NFT的交易历史
        return [];
    }

    private function getCurrentListing(string $tokenId): array
    {
        // 获取当前的上架信息
        return [];
    }

    private function getNftPriceHistory(string $tokenId): array
    {
        // 获取NFT的价格历史
        return [];
    }
}
