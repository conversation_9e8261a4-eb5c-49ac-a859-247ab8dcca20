<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Db;
use think\facade\Cache;
use think\facade\Log;

/**
 * 风控服务类
 */
class RiskControlService
{
    // 风险等级
    const RISK_LOW = 1;      // 低风险
    const RISK_MEDIUM = 2;   // 中风险
    const RISK_HIGH = 3;     // 高风险
    const RISK_CRITICAL = 4; // 极高风险

    // 风控动作
    const ACTION_ALLOW = 'allow';           // 允许
    const ACTION_WARNING = 'warning';       // 警告
    const ACTION_RESTRICT = 'restrict';     // 限制
    const ACTION_BLOCK = 'block';           // 阻止

    /**
     * 登录风控检测
     */
    public function checkLoginRisk(int $userId, string $ip, string $userAgent): array
    {
        try {
            $riskScore = 0;
            $riskFactors = [];

            // 1. IP地址风险检测
            $ipRisk = $this->checkIpRisk($userId, $ip);
            $riskScore += $ipRisk['score'];
            if ($ipRisk['risk']) {
                $riskFactors[] = $ipRisk['reason'];
            }

            // 2. 设备指纹检测
            $deviceRisk = $this->checkDeviceRisk($userId, $userAgent);
            $riskScore += $deviceRisk['score'];
            if ($deviceRisk['risk']) {
                $riskFactors[] = $deviceRisk['reason'];
            }

            // 3. 登录频率检测
            $frequencyRisk = $this->checkLoginFrequency($userId, $ip);
            $riskScore += $frequencyRisk['score'];
            if ($frequencyRisk['risk']) {
                $riskFactors[] = $frequencyRisk['reason'];
            }

            // 4. 地理位置风险
            $locationRisk = $this->checkLocationRisk($userId, $ip);
            $riskScore += $locationRisk['score'];
            if ($locationRisk['risk']) {
                $riskFactors[] = $locationRisk['reason'];
            }

            // 计算风险等级和动作
            $riskLevel = $this->calculateRiskLevel($riskScore);
            $action = $this->determineAction($riskLevel, $riskFactors);

            // 记录风控日志
            $this->logRiskEvent('login', $userId, $ip, $riskScore, $riskLevel, $action, $riskFactors);

            return [
                'code' => 1,
                'data' => [
                    'risk_score' => $riskScore,
                    'risk_level' => $riskLevel,
                    'action' => $action,
                    'risk_factors' => $riskFactors,
                    'allow_login' => in_array($action, [self::ACTION_ALLOW, self::ACTION_WARNING])
                ]
            ];

        } catch (\Exception $e) {
            Log::error('登录风控检测失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '风控检测失败'];
        }
    }

    /**
     * 交易风控检测
     */
    public function checkTradeRisk(int $userId, array $tradeData): array
    {
        try {
            $riskScore = 0;
            $riskFactors = [];

            // 1. 交易金额风险
            $amountRisk = $this->checkTradeAmount($userId, $tradeData['amount']);
            $riskScore += $amountRisk['score'];
            if ($amountRisk['risk']) {
                $riskFactors[] = $amountRisk['reason'];
            }

            // 2. 交易频率风险
            $frequencyRisk = $this->checkTradeFrequency($userId);
            $riskScore += $frequencyRisk['score'];
            if ($frequencyRisk['risk']) {
                $riskFactors[] = $frequencyRisk['reason'];
            }

            // 3. 用户资产风险
            $assetRisk = $this->checkUserAssetRisk($userId, $tradeData);
            $riskScore += $assetRisk['score'];
            if ($assetRisk['risk']) {
                $riskFactors[] = $assetRisk['reason'];
            }

            // 4. 交易时间风险
            $timeRisk = $this->checkTradeTimeRisk();
            $riskScore += $timeRisk['score'];
            if ($timeRisk['risk']) {
                $riskFactors[] = $timeRisk['reason'];
            }

            $riskLevel = $this->calculateRiskLevel($riskScore);
            $action = $this->determineAction($riskLevel, $riskFactors);

            // 记录风控日志
            $this->logRiskEvent('trade', $userId, request()->ip(), $riskScore, $riskLevel, $action, $riskFactors);

            return [
                'code' => 1,
                'data' => [
                    'risk_score' => $riskScore,
                    'risk_level' => $riskLevel,
                    'action' => $action,
                    'risk_factors' => $riskFactors,
                    'allow_trade' => in_array($action, [self::ACTION_ALLOW, self::ACTION_WARNING])
                ]
            ];

        } catch (\Exception $e) {
            Log::error('交易风控检测失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '风控检测失败'];
        }
    }

    /**
     * IP风险检测
     */
    private function checkIpRisk(int $userId, string $ip): array
    {
        // 检查IP黑名单
        $isBlacklisted = Cache::get("ip_blacklist:{$ip}");
        if ($isBlacklisted) {
            return ['risk' => true, 'score' => 50, 'reason' => 'IP地址在黑名单中'];
        }

        // 检查IP白名单
        $isWhitelisted = Cache::get("ip_whitelist:{$ip}");
        if ($isWhitelisted) {
            return ['risk' => false, 'score' => -10, 'reason' => 'IP地址在白名单中'];
        }

        // 检查用户历史IP
        $userIps = Cache::get("user_ips:{$userId}", []);
        if (!in_array($ip, $userIps)) {
            // 新IP地址
            $userIps[] = $ip;
            Cache::set("user_ips:{$userId}", array_slice($userIps, -10), 86400 * 30); // 保留最近10个IP，30天
            return ['risk' => true, 'score' => 20, 'reason' => '使用新的IP地址登录'];
        }

        return ['risk' => false, 'score' => 0, 'reason' => ''];
    }

    /**
     * 设备指纹检测
     */
    private function checkDeviceRisk(int $userId, string $userAgent): array
    {
        $deviceFingerprint = md5($userAgent);
        
        // 检查用户历史设备
        $userDevices = Cache::get("user_devices:{$userId}", []);
        if (!in_array($deviceFingerprint, $userDevices)) {
            // 新设备
            $userDevices[] = $deviceFingerprint;
            Cache::set("user_devices:{$userId}", array_slice($userDevices, -5), 86400 * 30); // 保留最近5个设备，30天
            return ['risk' => true, 'score' => 15, 'reason' => '使用新设备登录'];
        }

        return ['risk' => false, 'score' => 0, 'reason' => ''];
    }

    /**
     * 登录频率检测
     */
    private function checkLoginFrequency(int $userId, string $ip): array
    {
        $cacheKey = "login_freq:{$userId}:{$ip}";
        $loginCount = Cache::get($cacheKey, 0);
        
        if ($loginCount > 10) {
            return ['risk' => true, 'score' => 30, 'reason' => '登录频率过高'];
        } elseif ($loginCount > 5) {
            return ['risk' => true, 'score' => 15, 'reason' => '登录频率较高'];
        }

        // 增加计数
        Cache::set($cacheKey, $loginCount + 1, 3600); // 1小时窗口

        return ['risk' => false, 'score' => 0, 'reason' => ''];
    }

    /**
     * 地理位置风险检测
     */
    private function checkLocationRisk(int $userId, string $ip): array
    {
        // 简化的地理位置检测（实际应该使用IP地理位置服务）
        $location = $this->getIpLocation($ip);
        $userLocations = Cache::get("user_locations:{$userId}", []);
        
        if (!empty($location) && !in_array($location, $userLocations)) {
            $userLocations[] = $location;
            Cache::set("user_locations:{$userId}", array_slice($userLocations, -3), 86400 * 30);
            return ['risk' => true, 'score' => 25, 'reason' => '从新地理位置登录'];
        }

        return ['risk' => false, 'score' => 0, 'reason' => ''];
    }

    /**
     * 交易金额风险检测
     */
    private function checkTradeAmount(int $userId, float $amount): array
    {
        // 获取用户历史交易金额
        $avgAmount = $this->getUserAverageTradeAmount($userId);
        
        if ($amount > $avgAmount * 10) {
            return ['risk' => true, 'score' => 40, 'reason' => '交易金额异常过大'];
        } elseif ($amount > $avgAmount * 5) {
            return ['risk' => true, 'score' => 20, 'reason' => '交易金额较大'];
        }

        return ['risk' => false, 'score' => 0, 'reason' => ''];
    }

    /**
     * 交易频率风险检测
     */
    private function checkTradeFrequency(int $userId): array
    {
        $cacheKey = "trade_freq:{$userId}";
        $tradeCount = Cache::get($cacheKey, 0);
        
        if ($tradeCount > 100) {
            return ['risk' => true, 'score' => 35, 'reason' => '交易频率过高'];
        } elseif ($tradeCount > 50) {
            return ['risk' => true, 'score' => 15, 'reason' => '交易频率较高'];
        }

        // 增加计数
        Cache::set($cacheKey, $tradeCount + 1, 3600); // 1小时窗口

        return ['risk' => false, 'score' => 0, 'reason' => ''];
    }

    /**
     * 用户资产风险检测
     */
    private function checkUserAssetRisk(int $userId, array $tradeData): array
    {
        // 检查用户资产是否充足
        $userAsset = Db::name('user_assets')
            ->where('user_id', $userId)
            ->where('coin_symbol', 'USDT')
            ->find();

        if (!$userAsset || $userAsset['available'] < $tradeData['amount']) {
            return ['risk' => true, 'score' => 50, 'reason' => '资产不足进行交易'];
        }

        // 检查是否使用大部分资产
        if ($tradeData['amount'] > $userAsset['available'] * 0.8) {
            return ['risk' => true, 'score' => 25, 'reason' => '使用大部分资产进行交易'];
        }

        return ['risk' => false, 'score' => 0, 'reason' => ''];
    }

    /**
     * 交易时间风险检测
     */
    private function checkTradeTimeRisk(): array
    {
        $hour = (int)date('H');
        
        // 深夜交易风险较高
        if ($hour >= 2 && $hour <= 6) {
            return ['risk' => true, 'score' => 10, 'reason' => '深夜时段交易'];
        }

        return ['risk' => false, 'score' => 0, 'reason' => ''];
    }

    /**
     * 计算风险等级
     */
    private function calculateRiskLevel(int $score): int
    {
        if ($score >= 80) {
            return self::RISK_CRITICAL;
        } elseif ($score >= 50) {
            return self::RISK_HIGH;
        } elseif ($score >= 20) {
            return self::RISK_MEDIUM;
        } else {
            return self::RISK_LOW;
        }
    }

    /**
     * 确定风控动作
     */
    private function determineAction(int $riskLevel, array $riskFactors): string
    {
        switch ($riskLevel) {
            case self::RISK_CRITICAL:
                return self::ACTION_BLOCK;
            case self::RISK_HIGH:
                return self::ACTION_RESTRICT;
            case self::RISK_MEDIUM:
                return self::ACTION_WARNING;
            default:
                return self::ACTION_ALLOW;
        }
    }

    /**
     * 记录风控事件
     */
    private function logRiskEvent(string $type, int $userId, string $ip, int $score, int $level, string $action, array $factors): void
    {
        try {
            Db::name('risk_control_log')->insert([
                'user_id' => $userId,
                'type' => $type,
                'ip' => $ip,
                'risk_score' => $score,
                'risk_level' => $level,
                'action' => $action,
                'risk_factors' => json_encode($factors),
                'user_agent' => request()->header('user-agent'),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            Log::error('记录风控日志失败：' . $e->getMessage());
        }
    }

    /**
     * 获取IP地理位置（简化版）
     */
    private function getIpLocation(string $ip): string
    {
        // 这里应该调用真实的IP地理位置服务
        // 暂时返回模拟数据
        if (strpos($ip, '192.168.') === 0 || strpos($ip, '127.') === 0) {
            return 'Local';
        }
        
        return 'Unknown';
    }

    /**
     * 获取用户平均交易金额
     */
    private function getUserAverageTradeAmount(int $userId): float
    {
        $avgAmount = Db::name('orders')
            ->where('user_id', $userId)
            ->where('created_at', '>=', date('Y-m-d H:i:s', time() - 86400 * 30)) // 最近30天
            ->avg('amount');

        return $avgAmount ?: 100; // 默认100
    }

    /**
     * IP白名单管理
     */
    public function addIpWhitelist(string $ip, int $userId = 0, string $remark = ''): array
    {
        try {
            Cache::set("ip_whitelist:{$ip}", true, 86400 * 365); // 1年有效
            
            // 记录到数据库
            Db::name('ip_whitelist')->insert([
                'ip' => $ip,
                'user_id' => $userId,
                'remark' => $remark,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            return ['code' => 1, 'msg' => 'IP白名单添加成功'];
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '添加失败：' . $e->getMessage()];
        }
    }

    /**
     * IP黑名单管理
     */
    public function addIpBlacklist(string $ip, string $reason = ''): array
    {
        try {
            Cache::set("ip_blacklist:{$ip}", true, 86400 * 365); // 1年有效
            
            // 记录到数据库
            Db::name('ip_blacklist')->insert([
                'ip' => $ip,
                'reason' => $reason,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            return ['code' => 1, 'msg' => 'IP黑名单添加成功'];
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '添加失败：' . $e->getMessage()];
        }
    }
}
