<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit6cfa549e1e9810425484c355b5559751
{
    public static $files = array (
        '9b552a3cc426e3287cc811caefa3cf53' => __DIR__ . '/..' . '/topthink/think-helper/src/helper.php',
        '35fab96057f1bf5e7aba31a8a6d5fdde' => __DIR__ . '/..' . '/topthink/think-orm/stubs/load_stubs.php',
        '7b11c4dc42b3b3023073cb14e519683c' => __DIR__ . '/..' . '/ralouphie/getallheaders/src/getallheaders.php',
        '6e3fae29631ef280660b3cdad06f25a8' => __DIR__ . '/..' . '/symfony/deprecation-contracts/function.php',
        '37a3dc5111fe8f707ab4c132ef1dbc62' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/functions_include.php',
        '1cfd2761b63b0a29ed23657ea394cb2d' => __DIR__ . '/..' . '/topthink/think-captcha/src/helper.php',
    );

    public static $prefixLengthsPsr4 = array (
        't' => 
        array (
            'think\\view\\driver\\' => 18,
            'think\\captcha\\' => 14,
            'think\\app\\' => 10,
            'think\\' => 6,
        ),
        'e' => 
        array (
            'extend\\' => 7,
        ),
        'a' => 
        array (
            'app\\' => 4,
        ),
        'P' => 
        array (
            'Psr\\SimpleCache\\' => 16,
            'Psr\\Log\\' => 8,
            'Psr\\Http\\Message\\' => 17,
            'Psr\\Http\\Client\\' => 16,
            'Psr\\Container\\' => 14,
            'PHPMailer\\PHPMailer\\' => 20,
        ),
        'M' => 
        array (
            'Monolog\\' => 8,
        ),
        'G' => 
        array (
            'GuzzleHttp\\Psr7\\' => 16,
            'GuzzleHttp\\Promise\\' => 19,
            'GuzzleHttp\\' => 11,
        ),
        'F' => 
        array (
            'Firebase\\JWT\\' => 13,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'think\\view\\driver\\' => 
        array (
            0 => __DIR__ . '/..' . '/topthink/think-view/src',
        ),
        'think\\captcha\\' => 
        array (
            0 => __DIR__ . '/..' . '/topthink/think-captcha/src',
        ),
        'think\\app\\' => 
        array (
            0 => __DIR__ . '/..' . '/topthink/think-multi-app/src',
        ),
        'think\\' => 
        array (
            0 => __DIR__ . '/..' . '/topthink/think-template/src',
            1 => __DIR__ . '/..' . '/topthink/framework/src/think',
            2 => __DIR__ . '/..' . '/topthink/think-orm/src',
            3 => __DIR__ . '/..' . '/topthink/think-helper/src',
        ),
        'extend\\' => 
        array (
            0 => __DIR__ . '/../..' . '/extend',
        ),
        'app\\' => 
        array (
            0 => __DIR__ . '/../..' . '/app',
        ),
        'Psr\\SimpleCache\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/simple-cache/src',
        ),
        'Psr\\Log\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/log/Psr/Log',
        ),
        'Psr\\Http\\Message\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/http-factory/src',
            1 => __DIR__ . '/..' . '/psr/http-message/src',
        ),
        'Psr\\Http\\Client\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/http-client/src',
        ),
        'Psr\\Container\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/container/src',
        ),
        'PHPMailer\\PHPMailer\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpmailer/phpmailer/src',
        ),
        'Monolog\\' => 
        array (
            0 => __DIR__ . '/..' . '/monolog/monolog/src/Monolog',
        ),
        'GuzzleHttp\\Psr7\\' => 
        array (
            0 => __DIR__ . '/..' . '/guzzlehttp/psr7/src',
        ),
        'GuzzleHttp\\Promise\\' => 
        array (
            0 => __DIR__ . '/..' . '/guzzlehttp/promises/src',
        ),
        'GuzzleHttp\\' => 
        array (
            0 => __DIR__ . '/..' . '/guzzlehttp/guzzle/src',
        ),
        'Firebase\\JWT\\' => 
        array (
            0 => __DIR__ . '/..' . '/firebase/php-jwt/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'Firebase\\JWT\\BeforeValidException' => __DIR__ . '/..' . '/firebase/php-jwt/src/BeforeValidException.php',
        'Firebase\\JWT\\CachedKeySet' => __DIR__ . '/..' . '/firebase/php-jwt/src/CachedKeySet.php',
        'Firebase\\JWT\\ExpiredException' => __DIR__ . '/..' . '/firebase/php-jwt/src/ExpiredException.php',
        'Firebase\\JWT\\JWK' => __DIR__ . '/..' . '/firebase/php-jwt/src/JWK.php',
        'Firebase\\JWT\\JWT' => __DIR__ . '/..' . '/firebase/php-jwt/src/JWT.php',
        'Firebase\\JWT\\JWTExceptionWithPayloadInterface' => __DIR__ . '/..' . '/firebase/php-jwt/src/JWTExceptionWithPayloadInterface.php',
        'Firebase\\JWT\\Key' => __DIR__ . '/..' . '/firebase/php-jwt/src/Key.php',
        'Firebase\\JWT\\SignatureInvalidException' => __DIR__ . '/..' . '/firebase/php-jwt/src/SignatureInvalidException.php',
        'GuzzleHttp\\BodySummarizer' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/BodySummarizer.php',
        'GuzzleHttp\\BodySummarizerInterface' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/BodySummarizerInterface.php',
        'GuzzleHttp\\Client' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Client.php',
        'GuzzleHttp\\ClientInterface' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/ClientInterface.php',
        'GuzzleHttp\\ClientTrait' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/ClientTrait.php',
        'GuzzleHttp\\Cookie\\CookieJar' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Cookie/CookieJar.php',
        'GuzzleHttp\\Cookie\\CookieJarInterface' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Cookie/CookieJarInterface.php',
        'GuzzleHttp\\Cookie\\FileCookieJar' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Cookie/FileCookieJar.php',
        'GuzzleHttp\\Cookie\\SessionCookieJar' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Cookie/SessionCookieJar.php',
        'GuzzleHttp\\Cookie\\SetCookie' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Cookie/SetCookie.php',
        'GuzzleHttp\\Exception\\BadResponseException' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Exception/BadResponseException.php',
        'GuzzleHttp\\Exception\\ClientException' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Exception/ClientException.php',
        'GuzzleHttp\\Exception\\ConnectException' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Exception/ConnectException.php',
        'GuzzleHttp\\Exception\\GuzzleException' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Exception/GuzzleException.php',
        'GuzzleHttp\\Exception\\InvalidArgumentException' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Exception/InvalidArgumentException.php',
        'GuzzleHttp\\Exception\\RequestException' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Exception/RequestException.php',
        'GuzzleHttp\\Exception\\ServerException' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Exception/ServerException.php',
        'GuzzleHttp\\Exception\\TooManyRedirectsException' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Exception/TooManyRedirectsException.php',
        'GuzzleHttp\\Exception\\TransferException' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Exception/TransferException.php',
        'GuzzleHttp\\HandlerStack' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/HandlerStack.php',
        'GuzzleHttp\\Handler\\CurlFactory' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Handler/CurlFactory.php',
        'GuzzleHttp\\Handler\\CurlFactoryInterface' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Handler/CurlFactoryInterface.php',
        'GuzzleHttp\\Handler\\CurlHandler' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Handler/CurlHandler.php',
        'GuzzleHttp\\Handler\\CurlMultiHandler' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Handler/CurlMultiHandler.php',
        'GuzzleHttp\\Handler\\EasyHandle' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Handler/EasyHandle.php',
        'GuzzleHttp\\Handler\\HeaderProcessor' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Handler/HeaderProcessor.php',
        'GuzzleHttp\\Handler\\MockHandler' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Handler/MockHandler.php',
        'GuzzleHttp\\Handler\\Proxy' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Handler/Proxy.php',
        'GuzzleHttp\\Handler\\StreamHandler' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Handler/StreamHandler.php',
        'GuzzleHttp\\MessageFormatter' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/MessageFormatter.php',
        'GuzzleHttp\\MessageFormatterInterface' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/MessageFormatterInterface.php',
        'GuzzleHttp\\Middleware' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Middleware.php',
        'GuzzleHttp\\Pool' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Pool.php',
        'GuzzleHttp\\PrepareBodyMiddleware' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/PrepareBodyMiddleware.php',
        'GuzzleHttp\\Promise\\AggregateException' => __DIR__ . '/..' . '/guzzlehttp/promises/src/AggregateException.php',
        'GuzzleHttp\\Promise\\CancellationException' => __DIR__ . '/..' . '/guzzlehttp/promises/src/CancellationException.php',
        'GuzzleHttp\\Promise\\Coroutine' => __DIR__ . '/..' . '/guzzlehttp/promises/src/Coroutine.php',
        'GuzzleHttp\\Promise\\Create' => __DIR__ . '/..' . '/guzzlehttp/promises/src/Create.php',
        'GuzzleHttp\\Promise\\Each' => __DIR__ . '/..' . '/guzzlehttp/promises/src/Each.php',
        'GuzzleHttp\\Promise\\EachPromise' => __DIR__ . '/..' . '/guzzlehttp/promises/src/EachPromise.php',
        'GuzzleHttp\\Promise\\FulfilledPromise' => __DIR__ . '/..' . '/guzzlehttp/promises/src/FulfilledPromise.php',
        'GuzzleHttp\\Promise\\Is' => __DIR__ . '/..' . '/guzzlehttp/promises/src/Is.php',
        'GuzzleHttp\\Promise\\Promise' => __DIR__ . '/..' . '/guzzlehttp/promises/src/Promise.php',
        'GuzzleHttp\\Promise\\PromiseInterface' => __DIR__ . '/..' . '/guzzlehttp/promises/src/PromiseInterface.php',
        'GuzzleHttp\\Promise\\PromisorInterface' => __DIR__ . '/..' . '/guzzlehttp/promises/src/PromisorInterface.php',
        'GuzzleHttp\\Promise\\RejectedPromise' => __DIR__ . '/..' . '/guzzlehttp/promises/src/RejectedPromise.php',
        'GuzzleHttp\\Promise\\RejectionException' => __DIR__ . '/..' . '/guzzlehttp/promises/src/RejectionException.php',
        'GuzzleHttp\\Promise\\TaskQueue' => __DIR__ . '/..' . '/guzzlehttp/promises/src/TaskQueue.php',
        'GuzzleHttp\\Promise\\TaskQueueInterface' => __DIR__ . '/..' . '/guzzlehttp/promises/src/TaskQueueInterface.php',
        'GuzzleHttp\\Promise\\Utils' => __DIR__ . '/..' . '/guzzlehttp/promises/src/Utils.php',
        'GuzzleHttp\\Psr7\\AppendStream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/AppendStream.php',
        'GuzzleHttp\\Psr7\\BufferStream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/BufferStream.php',
        'GuzzleHttp\\Psr7\\CachingStream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/CachingStream.php',
        'GuzzleHttp\\Psr7\\DroppingStream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/DroppingStream.php',
        'GuzzleHttp\\Psr7\\Exception\\MalformedUriException' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/Exception/MalformedUriException.php',
        'GuzzleHttp\\Psr7\\FnStream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/FnStream.php',
        'GuzzleHttp\\Psr7\\Header' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/Header.php',
        'GuzzleHttp\\Psr7\\HttpFactory' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/HttpFactory.php',
        'GuzzleHttp\\Psr7\\InflateStream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/InflateStream.php',
        'GuzzleHttp\\Psr7\\LazyOpenStream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/LazyOpenStream.php',
        'GuzzleHttp\\Psr7\\LimitStream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/LimitStream.php',
        'GuzzleHttp\\Psr7\\Message' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/Message.php',
        'GuzzleHttp\\Psr7\\MessageTrait' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/MessageTrait.php',
        'GuzzleHttp\\Psr7\\MimeType' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/MimeType.php',
        'GuzzleHttp\\Psr7\\MultipartStream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/MultipartStream.php',
        'GuzzleHttp\\Psr7\\NoSeekStream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/NoSeekStream.php',
        'GuzzleHttp\\Psr7\\PumpStream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/PumpStream.php',
        'GuzzleHttp\\Psr7\\Query' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/Query.php',
        'GuzzleHttp\\Psr7\\Request' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/Request.php',
        'GuzzleHttp\\Psr7\\Response' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/Response.php',
        'GuzzleHttp\\Psr7\\Rfc7230' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/Rfc7230.php',
        'GuzzleHttp\\Psr7\\ServerRequest' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/ServerRequest.php',
        'GuzzleHttp\\Psr7\\Stream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/Stream.php',
        'GuzzleHttp\\Psr7\\StreamDecoratorTrait' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/StreamDecoratorTrait.php',
        'GuzzleHttp\\Psr7\\StreamWrapper' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/StreamWrapper.php',
        'GuzzleHttp\\Psr7\\UploadedFile' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/UploadedFile.php',
        'GuzzleHttp\\Psr7\\Uri' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/Uri.php',
        'GuzzleHttp\\Psr7\\UriComparator' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/UriComparator.php',
        'GuzzleHttp\\Psr7\\UriNormalizer' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/UriNormalizer.php',
        'GuzzleHttp\\Psr7\\UriResolver' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/UriResolver.php',
        'GuzzleHttp\\Psr7\\Utils' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/Utils.php',
        'GuzzleHttp\\RedirectMiddleware' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/RedirectMiddleware.php',
        'GuzzleHttp\\RequestOptions' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/RequestOptions.php',
        'GuzzleHttp\\RetryMiddleware' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/RetryMiddleware.php',
        'GuzzleHttp\\TransferStats' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/TransferStats.php',
        'GuzzleHttp\\Utils' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Utils.php',
        'Monolog\\Attribute\\AsMonologProcessor' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Attribute/AsMonologProcessor.php',
        'Monolog\\DateTimeImmutable' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/DateTimeImmutable.php',
        'Monolog\\ErrorHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/ErrorHandler.php',
        'Monolog\\Formatter\\ChromePHPFormatter' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Formatter/ChromePHPFormatter.php',
        'Monolog\\Formatter\\ElasticaFormatter' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Formatter/ElasticaFormatter.php',
        'Monolog\\Formatter\\ElasticsearchFormatter' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Formatter/ElasticsearchFormatter.php',
        'Monolog\\Formatter\\FlowdockFormatter' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Formatter/FlowdockFormatter.php',
        'Monolog\\Formatter\\FluentdFormatter' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Formatter/FluentdFormatter.php',
        'Monolog\\Formatter\\FormatterInterface' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Formatter/FormatterInterface.php',
        'Monolog\\Formatter\\GelfMessageFormatter' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Formatter/GelfMessageFormatter.php',
        'Monolog\\Formatter\\GoogleCloudLoggingFormatter' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Formatter/GoogleCloudLoggingFormatter.php',
        'Monolog\\Formatter\\HtmlFormatter' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Formatter/HtmlFormatter.php',
        'Monolog\\Formatter\\JsonFormatter' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Formatter/JsonFormatter.php',
        'Monolog\\Formatter\\LineFormatter' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Formatter/LineFormatter.php',
        'Monolog\\Formatter\\LogglyFormatter' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Formatter/LogglyFormatter.php',
        'Monolog\\Formatter\\LogmaticFormatter' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Formatter/LogmaticFormatter.php',
        'Monolog\\Formatter\\LogstashFormatter' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Formatter/LogstashFormatter.php',
        'Monolog\\Formatter\\MongoDBFormatter' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Formatter/MongoDBFormatter.php',
        'Monolog\\Formatter\\NormalizerFormatter' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Formatter/NormalizerFormatter.php',
        'Monolog\\Formatter\\ScalarFormatter' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Formatter/ScalarFormatter.php',
        'Monolog\\Formatter\\WildfireFormatter' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Formatter/WildfireFormatter.php',
        'Monolog\\Handler\\AbstractHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/AbstractHandler.php',
        'Monolog\\Handler\\AbstractProcessingHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/AbstractProcessingHandler.php',
        'Monolog\\Handler\\AbstractSyslogHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/AbstractSyslogHandler.php',
        'Monolog\\Handler\\AmqpHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/AmqpHandler.php',
        'Monolog\\Handler\\BrowserConsoleHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/BrowserConsoleHandler.php',
        'Monolog\\Handler\\BufferHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/BufferHandler.php',
        'Monolog\\Handler\\ChromePHPHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/ChromePHPHandler.php',
        'Monolog\\Handler\\CouchDBHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/CouchDBHandler.php',
        'Monolog\\Handler\\CubeHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/CubeHandler.php',
        'Monolog\\Handler\\Curl\\Util' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/Curl/Util.php',
        'Monolog\\Handler\\DeduplicationHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/DeduplicationHandler.php',
        'Monolog\\Handler\\DoctrineCouchDBHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/DoctrineCouchDBHandler.php',
        'Monolog\\Handler\\DynamoDbHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/DynamoDbHandler.php',
        'Monolog\\Handler\\ElasticaHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/ElasticaHandler.php',
        'Monolog\\Handler\\ElasticsearchHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/ElasticsearchHandler.php',
        'Monolog\\Handler\\ErrorLogHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/ErrorLogHandler.php',
        'Monolog\\Handler\\FallbackGroupHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/FallbackGroupHandler.php',
        'Monolog\\Handler\\FilterHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/FilterHandler.php',
        'Monolog\\Handler\\FingersCrossedHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/FingersCrossedHandler.php',
        'Monolog\\Handler\\FingersCrossed\\ActivationStrategyInterface' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/FingersCrossed/ActivationStrategyInterface.php',
        'Monolog\\Handler\\FingersCrossed\\ChannelLevelActivationStrategy' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/FingersCrossed/ChannelLevelActivationStrategy.php',
        'Monolog\\Handler\\FingersCrossed\\ErrorLevelActivationStrategy' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/FingersCrossed/ErrorLevelActivationStrategy.php',
        'Monolog\\Handler\\FirePHPHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/FirePHPHandler.php',
        'Monolog\\Handler\\FleepHookHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/FleepHookHandler.php',
        'Monolog\\Handler\\FlowdockHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/FlowdockHandler.php',
        'Monolog\\Handler\\FormattableHandlerInterface' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/FormattableHandlerInterface.php',
        'Monolog\\Handler\\FormattableHandlerTrait' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/FormattableHandlerTrait.php',
        'Monolog\\Handler\\GelfHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/GelfHandler.php',
        'Monolog\\Handler\\GroupHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/GroupHandler.php',
        'Monolog\\Handler\\Handler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/Handler.php',
        'Monolog\\Handler\\HandlerInterface' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/HandlerInterface.php',
        'Monolog\\Handler\\HandlerWrapper' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/HandlerWrapper.php',
        'Monolog\\Handler\\IFTTTHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/IFTTTHandler.php',
        'Monolog\\Handler\\InsightOpsHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/InsightOpsHandler.php',
        'Monolog\\Handler\\LogEntriesHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/LogEntriesHandler.php',
        'Monolog\\Handler\\LogglyHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/LogglyHandler.php',
        'Monolog\\Handler\\LogmaticHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/LogmaticHandler.php',
        'Monolog\\Handler\\MailHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/MailHandler.php',
        'Monolog\\Handler\\MandrillHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/MandrillHandler.php',
        'Monolog\\Handler\\MissingExtensionException' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/MissingExtensionException.php',
        'Monolog\\Handler\\MongoDBHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/MongoDBHandler.php',
        'Monolog\\Handler\\NativeMailerHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/NativeMailerHandler.php',
        'Monolog\\Handler\\NewRelicHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/NewRelicHandler.php',
        'Monolog\\Handler\\NoopHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/NoopHandler.php',
        'Monolog\\Handler\\NullHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/NullHandler.php',
        'Monolog\\Handler\\OverflowHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/OverflowHandler.php',
        'Monolog\\Handler\\PHPConsoleHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/PHPConsoleHandler.php',
        'Monolog\\Handler\\ProcessHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/ProcessHandler.php',
        'Monolog\\Handler\\ProcessableHandlerInterface' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/ProcessableHandlerInterface.php',
        'Monolog\\Handler\\ProcessableHandlerTrait' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/ProcessableHandlerTrait.php',
        'Monolog\\Handler\\PsrHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/PsrHandler.php',
        'Monolog\\Handler\\PushoverHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/PushoverHandler.php',
        'Monolog\\Handler\\RedisHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/RedisHandler.php',
        'Monolog\\Handler\\RedisPubSubHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/RedisPubSubHandler.php',
        'Monolog\\Handler\\RollbarHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/RollbarHandler.php',
        'Monolog\\Handler\\RotatingFileHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/RotatingFileHandler.php',
        'Monolog\\Handler\\SamplingHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/SamplingHandler.php',
        'Monolog\\Handler\\SendGridHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/SendGridHandler.php',
        'Monolog\\Handler\\SlackHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/SlackHandler.php',
        'Monolog\\Handler\\SlackWebhookHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/SlackWebhookHandler.php',
        'Monolog\\Handler\\Slack\\SlackRecord' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/Slack/SlackRecord.php',
        'Monolog\\Handler\\SocketHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/SocketHandler.php',
        'Monolog\\Handler\\SqsHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/SqsHandler.php',
        'Monolog\\Handler\\StreamHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/StreamHandler.php',
        'Monolog\\Handler\\SwiftMailerHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/SwiftMailerHandler.php',
        'Monolog\\Handler\\SymfonyMailerHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/SymfonyMailerHandler.php',
        'Monolog\\Handler\\SyslogHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/SyslogHandler.php',
        'Monolog\\Handler\\SyslogUdpHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/SyslogUdpHandler.php',
        'Monolog\\Handler\\SyslogUdp\\UdpSocket' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/SyslogUdp/UdpSocket.php',
        'Monolog\\Handler\\TelegramBotHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/TelegramBotHandler.php',
        'Monolog\\Handler\\TestHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/TestHandler.php',
        'Monolog\\Handler\\WebRequestRecognizerTrait' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/WebRequestRecognizerTrait.php',
        'Monolog\\Handler\\WhatFailureGroupHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/WhatFailureGroupHandler.php',
        'Monolog\\Handler\\ZendMonitorHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Handler/ZendMonitorHandler.php',
        'Monolog\\LogRecord' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/LogRecord.php',
        'Monolog\\Logger' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Logger.php',
        'Monolog\\Processor\\GitProcessor' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Processor/GitProcessor.php',
        'Monolog\\Processor\\HostnameProcessor' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Processor/HostnameProcessor.php',
        'Monolog\\Processor\\IntrospectionProcessor' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Processor/IntrospectionProcessor.php',
        'Monolog\\Processor\\MemoryPeakUsageProcessor' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Processor/MemoryPeakUsageProcessor.php',
        'Monolog\\Processor\\MemoryProcessor' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Processor/MemoryProcessor.php',
        'Monolog\\Processor\\MemoryUsageProcessor' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Processor/MemoryUsageProcessor.php',
        'Monolog\\Processor\\MercurialProcessor' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Processor/MercurialProcessor.php',
        'Monolog\\Processor\\ProcessIdProcessor' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Processor/ProcessIdProcessor.php',
        'Monolog\\Processor\\ProcessorInterface' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Processor/ProcessorInterface.php',
        'Monolog\\Processor\\PsrLogMessageProcessor' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Processor/PsrLogMessageProcessor.php',
        'Monolog\\Processor\\TagProcessor' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Processor/TagProcessor.php',
        'Monolog\\Processor\\UidProcessor' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Processor/UidProcessor.php',
        'Monolog\\Processor\\WebProcessor' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Processor/WebProcessor.php',
        'Monolog\\Registry' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Registry.php',
        'Monolog\\ResettableInterface' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/ResettableInterface.php',
        'Monolog\\SignalHandler' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/SignalHandler.php',
        'Monolog\\Test\\TestCase' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Test/TestCase.php',
        'Monolog\\Utils' => __DIR__ . '/..' . '/monolog/monolog/src/Monolog/Utils.php',
        'PHPMailer\\PHPMailer\\DSNConfigurator' => __DIR__ . '/..' . '/phpmailer/phpmailer/src/DSNConfigurator.php',
        'PHPMailer\\PHPMailer\\Exception' => __DIR__ . '/..' . '/phpmailer/phpmailer/src/Exception.php',
        'PHPMailer\\PHPMailer\\OAuth' => __DIR__ . '/..' . '/phpmailer/phpmailer/src/OAuth.php',
        'PHPMailer\\PHPMailer\\OAuthTokenProvider' => __DIR__ . '/..' . '/phpmailer/phpmailer/src/OAuthTokenProvider.php',
        'PHPMailer\\PHPMailer\\PHPMailer' => __DIR__ . '/..' . '/phpmailer/phpmailer/src/PHPMailer.php',
        'PHPMailer\\PHPMailer\\POP3' => __DIR__ . '/..' . '/phpmailer/phpmailer/src/POP3.php',
        'PHPMailer\\PHPMailer\\SMTP' => __DIR__ . '/..' . '/phpmailer/phpmailer/src/SMTP.php',
        'Psr\\Container\\ContainerExceptionInterface' => __DIR__ . '/..' . '/psr/container/src/ContainerExceptionInterface.php',
        'Psr\\Container\\ContainerInterface' => __DIR__ . '/..' . '/psr/container/src/ContainerInterface.php',
        'Psr\\Container\\NotFoundExceptionInterface' => __DIR__ . '/..' . '/psr/container/src/NotFoundExceptionInterface.php',
        'Psr\\Http\\Client\\ClientExceptionInterface' => __DIR__ . '/..' . '/psr/http-client/src/ClientExceptionInterface.php',
        'Psr\\Http\\Client\\ClientInterface' => __DIR__ . '/..' . '/psr/http-client/src/ClientInterface.php',
        'Psr\\Http\\Client\\NetworkExceptionInterface' => __DIR__ . '/..' . '/psr/http-client/src/NetworkExceptionInterface.php',
        'Psr\\Http\\Client\\RequestExceptionInterface' => __DIR__ . '/..' . '/psr/http-client/src/RequestExceptionInterface.php',
        'Psr\\Http\\Message\\MessageInterface' => __DIR__ . '/..' . '/psr/http-message/src/MessageInterface.php',
        'Psr\\Http\\Message\\RequestFactoryInterface' => __DIR__ . '/..' . '/psr/http-factory/src/RequestFactoryInterface.php',
        'Psr\\Http\\Message\\RequestInterface' => __DIR__ . '/..' . '/psr/http-message/src/RequestInterface.php',
        'Psr\\Http\\Message\\ResponseFactoryInterface' => __DIR__ . '/..' . '/psr/http-factory/src/ResponseFactoryInterface.php',
        'Psr\\Http\\Message\\ResponseInterface' => __DIR__ . '/..' . '/psr/http-message/src/ResponseInterface.php',
        'Psr\\Http\\Message\\ServerRequestFactoryInterface' => __DIR__ . '/..' . '/psr/http-factory/src/ServerRequestFactoryInterface.php',
        'Psr\\Http\\Message\\ServerRequestInterface' => __DIR__ . '/..' . '/psr/http-message/src/ServerRequestInterface.php',
        'Psr\\Http\\Message\\StreamFactoryInterface' => __DIR__ . '/..' . '/psr/http-factory/src/StreamFactoryInterface.php',
        'Psr\\Http\\Message\\StreamInterface' => __DIR__ . '/..' . '/psr/http-message/src/StreamInterface.php',
        'Psr\\Http\\Message\\UploadedFileFactoryInterface' => __DIR__ . '/..' . '/psr/http-factory/src/UploadedFileFactoryInterface.php',
        'Psr\\Http\\Message\\UploadedFileInterface' => __DIR__ . '/..' . '/psr/http-message/src/UploadedFileInterface.php',
        'Psr\\Http\\Message\\UriFactoryInterface' => __DIR__ . '/..' . '/psr/http-factory/src/UriFactoryInterface.php',
        'Psr\\Http\\Message\\UriInterface' => __DIR__ . '/..' . '/psr/http-message/src/UriInterface.php',
        'Psr\\Log\\AbstractLogger' => __DIR__ . '/..' . '/psr/log/Psr/Log/AbstractLogger.php',
        'Psr\\Log\\InvalidArgumentException' => __DIR__ . '/..' . '/psr/log/Psr/Log/InvalidArgumentException.php',
        'Psr\\Log\\LogLevel' => __DIR__ . '/..' . '/psr/log/Psr/Log/LogLevel.php',
        'Psr\\Log\\LoggerAwareInterface' => __DIR__ . '/..' . '/psr/log/Psr/Log/LoggerAwareInterface.php',
        'Psr\\Log\\LoggerAwareTrait' => __DIR__ . '/..' . '/psr/log/Psr/Log/LoggerAwareTrait.php',
        'Psr\\Log\\LoggerInterface' => __DIR__ . '/..' . '/psr/log/Psr/Log/LoggerInterface.php',
        'Psr\\Log\\LoggerTrait' => __DIR__ . '/..' . '/psr/log/Psr/Log/LoggerTrait.php',
        'Psr\\Log\\NullLogger' => __DIR__ . '/..' . '/psr/log/Psr/Log/NullLogger.php',
        'Psr\\Log\\Test\\DummyTest' => __DIR__ . '/..' . '/psr/log/Psr/Log/Test/DummyTest.php',
        'Psr\\Log\\Test\\LoggerInterfaceTest' => __DIR__ . '/..' . '/psr/log/Psr/Log/Test/LoggerInterfaceTest.php',
        'Psr\\Log\\Test\\TestLogger' => __DIR__ . '/..' . '/psr/log/Psr/Log/Test/TestLogger.php',
        'Psr\\SimpleCache\\CacheException' => __DIR__ . '/..' . '/psr/simple-cache/src/CacheException.php',
        'Psr\\SimpleCache\\CacheInterface' => __DIR__ . '/..' . '/psr/simple-cache/src/CacheInterface.php',
        'Psr\\SimpleCache\\InvalidArgumentException' => __DIR__ . '/..' . '/psr/simple-cache/src/InvalidArgumentException.php',
        'app\\BaseController' => __DIR__ . '/../..' . '/app/BaseController.php',
        'app\\admin\\controller\\Coin' => __DIR__ . '/../..' . '/app/admin/controller/Coin.php',
        'app\\admin\\controller\\CustomerService' => __DIR__ . '/../..' . '/app/admin/controller/CustomerService.php',
        'app\\admin\\controller\\Index' => __DIR__ . '/../..' . '/app/admin/controller/Index.php',
        'app\\admin\\controller\\Statistics' => __DIR__ . '/../..' . '/app/admin/controller/Statistics.php',
        'app\\admin\\controller\\System' => __DIR__ . '/../..' . '/app/admin/controller/System.php',
        'app\\admin\\controller\\TradingPair' => __DIR__ . '/../..' . '/app/admin/controller/TradingPair.php',
        'app\\admin\\controller\\User' => __DIR__ . '/../..' . '/app/admin/controller/User.php',
        'app\\agent\\controller\\Auth' => __DIR__ . '/../..' . '/app/agent/controller/Auth.php',
        'app\\agent\\controller\\Dashboard' => __DIR__ . '/../..' . '/app/agent/controller/Dashboard.php',
        'app\\agent\\controller\\Index' => __DIR__ . '/../..' . '/app/agent/controller/Index.php',
        'app\\api\\controller\\Admin' => __DIR__ . '/../..' . '/app/api/controller/Admin.php',
        'app\\api\\controller\\Agent' => __DIR__ . '/../..' . '/app/api/controller/Agent.php',
        'app\\api\\controller\\Ai' => __DIR__ . '/../..' . '/app/api/controller/Ai.php',
        'app\\api\\controller\\Asset' => __DIR__ . '/../..' . '/app/api/controller/Asset.php',
        'app\\api\\controller\\Auth' => __DIR__ . '/../..' . '/app/api/controller/Auth.php',
        'app\\api\\controller\\BaseController' => __DIR__ . '/../..' . '/app/api/controller/BaseController.php',
        'app\\api\\controller\\Config' => __DIR__ . '/../..' . '/app/api/controller/Config.php',
        'app\\api\\controller\\Contract' => __DIR__ . '/../..' . '/app/api/controller/Contract.php',
        'app\\api\\controller\\CustomerService' => __DIR__ . '/../..' . '/app/api/controller/CustomerService.php',
        'app\\api\\controller\\Institutional' => __DIR__ . '/../..' . '/app/api/controller/Institutional.php',
        'app\\api\\controller\\Market' => __DIR__ . '/../..' . '/app/api/controller/Market.php',
        'app\\api\\controller\\Quant' => __DIR__ . '/../..' . '/app/api/controller/Quant.php',
        'app\\api\\controller\\Social' => __DIR__ . '/../..' . '/app/api/controller/Social.php',
        'app\\api\\controller\\Trade' => __DIR__ . '/../..' . '/app/api/controller/Trade.php',
        'app\\api\\controller\\User' => __DIR__ . '/../..' . '/app/api/controller/User.php',
        'app\\command\\ContractSettle' => __DIR__ . '/../..' . '/app/command/ContractSettle.php',
        'app\\command\\MarketDataSync' => __DIR__ . '/../..' . '/app/command/MarketDataSync.php',
        'app\\command\\QuantRunner' => __DIR__ . '/../..' . '/app/command/QuantRunner.php',
        'app\\command\\TradeMatch' => __DIR__ . '/../..' . '/app/command/TradeMatch.php',
        'app\\common\\model\\AdvancedOrder' => __DIR__ . '/../..' . '/app/common/model/AdvancedOrder.php',
        'app\\common\\model\\AiStrategy' => __DIR__ . '/../..' . '/app/common/model/AiStrategy.php',
        'app\\common\\model\\Coin' => __DIR__ . '/../..' . '/app/common/model/Coin.php',
        'app\\common\\model\\Commission' => __DIR__ . '/../..' . '/app/common/model/Commission.php',
        'app\\common\\model\\ContractConfig' => __DIR__ . '/../..' . '/app/common/model/ContractConfig.php',
        'app\\common\\model\\ContractOrder' => __DIR__ . '/../..' . '/app/common/model/ContractOrder.php',
        'app\\common\\model\\CrossChainBridge' => __DIR__ . '/../..' . '/app/common/model/CrossChainBridge.php',
        'app\\common\\model\\CustomerService' => __DIR__ . '/../..' . '/app/common/model/CustomerService.php',
        'app\\common\\model\\CustomerServiceMessage' => __DIR__ . '/../..' . '/app/common/model/CustomerServiceMessage.php',
        'app\\common\\model\\CustomerServiceTicket' => __DIR__ . '/../..' . '/app/common/model/CustomerServiceTicket.php',
        'app\\common\\model\\DepositAddress' => __DIR__ . '/../..' . '/app/common/model/DepositAddress.php',
        'app\\common\\model\\DepositRecord' => __DIR__ . '/../..' . '/app/common/model/DepositRecord.php',
        'app\\common\\model\\FinancialRecord' => __DIR__ . '/../..' . '/app/common/model/FinancialRecord.php',
        'app\\common\\model\\GridStrategy' => __DIR__ . '/../..' . '/app/common/model/GridStrategy.php',
        'app\\common\\model\\IdoOrder' => __DIR__ . '/../..' . '/app/common/model/IdoOrder.php',
        'app\\common\\model\\IdoProject' => __DIR__ . '/../..' . '/app/common/model/IdoProject.php',
        'app\\common\\model\\InstitutionalAccount' => __DIR__ . '/../..' . '/app/common/model/InstitutionalAccount.php',
        'app\\common\\model\\LiquidityPool' => __DIR__ . '/../..' . '/app/common/model/LiquidityPool.php',
        'app\\common\\model\\NftToken' => __DIR__ . '/../..' . '/app/common/model/NftToken.php',
        'app\\common\\model\\Notification' => __DIR__ . '/../..' . '/app/common/model/Notification.php',
        'app\\common\\model\\Order' => __DIR__ . '/../..' . '/app/common/model/Order.php',
        'app\\common\\model\\QuantStrategy' => __DIR__ . '/../..' . '/app/common/model/QuantStrategy.php',
        'app\\common\\model\\SystemConfig' => __DIR__ . '/../..' . '/app/common/model/SystemConfig.php',
        'app\\common\\model\\Trade' => __DIR__ . '/../..' . '/app/common/model/Trade.php',
        'app\\common\\model\\TradingPair' => __DIR__ . '/../..' . '/app/common/model/TradingPair.php',
        'app\\common\\model\\TradingStrategy' => __DIR__ . '/../..' . '/app/common/model/TradingStrategy.php',
        'app\\common\\model\\User' => __DIR__ . '/../..' . '/app/common/model/User.php',
        'app\\common\\model\\UserAsset' => __DIR__ . '/../..' . '/app/common/model/UserAsset.php',
        'app\\common\\model\\WithdrawRecord' => __DIR__ . '/../..' . '/app/common/model/WithdrawRecord.php',
        'app\\common\\model\\Withdrawal' => __DIR__ . '/../..' . '/app/common/model/Withdrawal.php',
        'app\\common\\service\\AdvancedTradeService' => __DIR__ . '/../..' . '/app/common/service/AdvancedTradeService.php',
        'app\\common\\service\\AgentService' => __DIR__ . '/../..' . '/app/common/service/AgentService.php',
        'app\\common\\service\\AiTradingService' => __DIR__ . '/../..' . '/app/common/service/AiTradingService.php',
        'app\\common\\service\\AssetService' => __DIR__ . '/../..' . '/app/common/service/AssetService.php',
        'app\\common\\service\\ContractService' => __DIR__ . '/../..' . '/app/common/service/ContractService.php',
        'app\\common\\service\\CrossChainService' => __DIR__ . '/../..' . '/app/common/service/CrossChainService.php',
        'app\\common\\service\\CustomerServiceService' => __DIR__ . '/../..' . '/app/common/service/CustomerServiceService.php',
        'app\\common\\service\\DeFiService' => __DIR__ . '/../..' . '/app/common/service/DeFiService.php',
        'app\\common\\service\\DepositWithdrawService' => __DIR__ . '/../..' . '/app/common/service/DepositWithdrawService.php',
        'app\\common\\service\\EmailService' => __DIR__ . '/../..' . '/app/common/service/EmailService.php',
        'app\\common\\service\\FinancialReportService' => __DIR__ . '/../..' . '/app/common/service/FinancialReportService.php',
        'app\\common\\service\\IdoService' => __DIR__ . '/../..' . '/app/common/service/IdoService.php',
        'app\\common\\service\\InstitutionalService' => __DIR__ . '/../..' . '/app/common/service/InstitutionalService.php',
        'app\\common\\service\\KycService' => __DIR__ . '/../..' . '/app/common/service/KycService.php',
        'app\\common\\service\\LanguageService' => __DIR__ . '/../..' . '/app/common/service/LanguageService.php',
        'app\\common\\service\\NftService' => __DIR__ . '/../..' . '/app/common/service/NftService.php',
        'app\\common\\service\\NotificationService' => __DIR__ . '/../..' . '/app/common/service/NotificationService.php',
        'app\\common\\service\\PaymentService' => __DIR__ . '/../..' . '/app/common/service/PaymentService.php',
        'app\\common\\service\\PerformanceService' => __DIR__ . '/../..' . '/app/common/service/PerformanceService.php',
        'app\\common\\service\\QuantTradingService' => __DIR__ . '/../..' . '/app/common/service/QuantTradingService.php',
        'app\\common\\service\\SecurityService' => __DIR__ . '/../..' . '/app/common/service/SecurityService.php',
        'app\\common\\service\\SimpleKycService' => __DIR__ . '/../..' . '/app/common/service/SimpleKycService.php',
        'app\\common\\service\\SimpleTradeService' => __DIR__ . '/../..' . '/app/common/service/SimpleTradeService.php',
        'app\\common\\service\\SimpleWebSocketService' => __DIR__ . '/../..' . '/app/common/service/SimpleWebSocketService.php',
        'app\\common\\service\\SocialTradingService' => __DIR__ . '/../..' . '/app/common/service/SocialTradingService.php',
        'app\\common\\service\\StatisticsService' => __DIR__ . '/../..' . '/app/common/service/StatisticsService.php',
        'app\\common\\service\\SystemConfigService' => __DIR__ . '/../..' . '/app/common/service/SystemConfigService.php',
        'app\\common\\service\\TradeService' => __DIR__ . '/../..' . '/app/common/service/TradeService.php',
        'app\\common\\service\\UserService' => __DIR__ . '/../..' . '/app/common/service/UserService.php',
        'app\\common\\service\\WalletService' => __DIR__ . '/../..' . '/app/common/service/WalletService.php',
        'app\\index\\controller\\Auth' => __DIR__ . '/../..' . '/app/index/controller/Auth.php',
        'app\\index\\controller\\Contract' => __DIR__ . '/../..' . '/app/index/controller/Contract.php',
        'app\\index\\controller\\CustomerService' => __DIR__ . '/../..' . '/app/index/controller/CustomerService.php',
        'app\\index\\controller\\Ido' => __DIR__ . '/../..' . '/app/index/controller/Ido.php',
        'app\\index\\controller\\Index' => __DIR__ . '/../..' . '/app/index/controller/Index.php',
        'app\\index\\controller\\Language' => __DIR__ . '/../..' . '/app/index/controller/Language.php',
        'app\\index\\controller\\Notification' => __DIR__ . '/../..' . '/app/index/controller/Notification.php',
        'app\\index\\controller\\Trade' => __DIR__ . '/../..' . '/app/index/controller/Trade.php',
        'app\\index\\controller\\User' => __DIR__ . '/../..' . '/app/index/controller/User.php',
        'app\\index\\controller\\Wallet' => __DIR__ . '/../..' . '/app/index/controller/Wallet.php',
        'app\\job\\EmailJob' => __DIR__ . '/../..' . '/app/job/EmailJob.php',
        'app\\job\\QuantExecuteJob' => __DIR__ . '/../..' . '/app/job/QuantExecuteJob.php',
        'app\\middleware\\ApiAuth' => __DIR__ . '/../..' . '/app/middleware/ApiAuth.php',
        'app\\middleware\\ApiThrottle' => __DIR__ . '/../..' . '/app/middleware/ApiThrottle.php',
        'app\\middleware\\LanguageMiddleware' => __DIR__ . '/../..' . '/app/middleware/LanguageMiddleware.php',
        'app\\middleware\\SecurityMiddleware' => __DIR__ . '/../..' . '/app/middleware/SecurityMiddleware.php',
        'app\\websocket\\controller\\Market' => __DIR__ . '/../..' . '/app/websocket/controller/Market.php',
        'app\\websocket\\controller\\User' => __DIR__ . '/../..' . '/app/websocket/controller/User.php',
        'think\\App' => __DIR__ . '/..' . '/topthink/framework/src/think/App.php',
        'think\\Cache' => __DIR__ . '/..' . '/topthink/framework/src/think/Cache.php',
        'think\\Collection' => __DIR__ . '/..' . '/topthink/think-helper/src/Collection.php',
        'think\\Config' => __DIR__ . '/..' . '/topthink/framework/src/think/Config.php',
        'think\\Console' => __DIR__ . '/..' . '/topthink/framework/src/think/Console.php',
        'think\\Container' => __DIR__ . '/..' . '/topthink/framework/src/think/Container.php',
        'think\\Cookie' => __DIR__ . '/..' . '/topthink/framework/src/think/Cookie.php',
        'think\\Db' => __DIR__ . '/..' . '/topthink/framework/src/think/Db.php',
        'think\\DbManager' => __DIR__ . '/..' . '/topthink/think-orm/src/DbManager.php',
        'think\\Env' => __DIR__ . '/..' . '/topthink/framework/src/think/Env.php',
        'think\\Event' => __DIR__ . '/..' . '/topthink/framework/src/think/Event.php',
        'think\\Exception' => __DIR__ . '/..' . '/topthink/framework/src/think/Exception.php',
        'think\\Facade' => __DIR__ . '/..' . '/topthink/framework/src/think/Facade.php',
        'think\\File' => __DIR__ . '/..' . '/topthink/framework/src/think/File.php',
        'think\\Http' => __DIR__ . '/..' . '/topthink/framework/src/think/Http.php',
        'think\\Lang' => __DIR__ . '/..' . '/topthink/framework/src/think/Lang.php',
        'think\\Log' => __DIR__ . '/..' . '/topthink/framework/src/think/Log.php',
        'think\\Manager' => __DIR__ . '/..' . '/topthink/framework/src/think/Manager.php',
        'think\\Middleware' => __DIR__ . '/..' . '/topthink/framework/src/think/Middleware.php',
        'think\\Model' => __DIR__ . '/..' . '/topthink/think-orm/src/Model.php',
        'think\\Paginator' => __DIR__ . '/..' . '/topthink/think-orm/src/Paginator.php',
        'think\\Pipeline' => __DIR__ . '/..' . '/topthink/framework/src/think/Pipeline.php',
        'think\\Request' => __DIR__ . '/..' . '/topthink/framework/src/think/Request.php',
        'think\\Response' => __DIR__ . '/..' . '/topthink/framework/src/think/Response.php',
        'think\\Route' => __DIR__ . '/..' . '/topthink/framework/src/think/Route.php',
        'think\\Service' => __DIR__ . '/..' . '/topthink/framework/src/think/Service.php',
        'think\\Session' => __DIR__ . '/..' . '/topthink/framework/src/think/Session.php',
        'think\\Template' => __DIR__ . '/..' . '/topthink/think-template/src/Template.php',
        'think\\Validate' => __DIR__ . '/..' . '/topthink/framework/src/think/Validate.php',
        'think\\View' => __DIR__ . '/..' . '/topthink/framework/src/think/View.php',
        'think\\app\\MultiApp' => __DIR__ . '/..' . '/topthink/think-multi-app/src/MultiApp.php',
        'think\\app\\Service' => __DIR__ . '/..' . '/topthink/think-multi-app/src/Service.php',
        'think\\app\\Url' => __DIR__ . '/..' . '/topthink/think-multi-app/src/Url.php',
        'think\\app\\command\\Build' => __DIR__ . '/..' . '/topthink/think-multi-app/src/command/Build.php',
        'think\\app\\command\\Clear' => __DIR__ . '/..' . '/topthink/think-multi-app/src/command/Clear.php',
        'think\\cache\\Driver' => __DIR__ . '/..' . '/topthink/framework/src/think/cache/Driver.php',
        'think\\cache\\TagSet' => __DIR__ . '/..' . '/topthink/framework/src/think/cache/TagSet.php',
        'think\\cache\\driver\\File' => __DIR__ . '/..' . '/topthink/framework/src/think/cache/driver/File.php',
        'think\\cache\\driver\\Memcache' => __DIR__ . '/..' . '/topthink/framework/src/think/cache/driver/Memcache.php',
        'think\\cache\\driver\\Memcached' => __DIR__ . '/..' . '/topthink/framework/src/think/cache/driver/Memcached.php',
        'think\\cache\\driver\\Redis' => __DIR__ . '/..' . '/topthink/framework/src/think/cache/driver/Redis.php',
        'think\\cache\\driver\\Wincache' => __DIR__ . '/..' . '/topthink/framework/src/think/cache/driver/Wincache.php',
        'think\\captcha\\Captcha' => __DIR__ . '/..' . '/topthink/think-captcha/src/Captcha.php',
        'think\\captcha\\CaptchaController' => __DIR__ . '/..' . '/topthink/think-captcha/src/CaptchaController.php',
        'think\\captcha\\CaptchaService' => __DIR__ . '/..' . '/topthink/think-captcha/src/CaptchaService.php',
        'think\\captcha\\facade\\Captcha' => __DIR__ . '/..' . '/topthink/think-captcha/src/facade/Captcha.php',
        'think\\console\\Command' => __DIR__ . '/..' . '/topthink/framework/src/think/console/Command.php',
        'think\\console\\Input' => __DIR__ . '/..' . '/topthink/framework/src/think/console/Input.php',
        'think\\console\\Output' => __DIR__ . '/..' . '/topthink/framework/src/think/console/Output.php',
        'think\\console\\Table' => __DIR__ . '/..' . '/topthink/framework/src/think/console/Table.php',
        'think\\console\\command\\Clear' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/Clear.php',
        'think\\console\\command\\Help' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/Help.php',
        'think\\console\\command\\Lists' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/Lists.php',
        'think\\console\\command\\Make' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/Make.php',
        'think\\console\\command\\RouteList' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/RouteList.php',
        'think\\console\\command\\RunServer' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/RunServer.php',
        'think\\console\\command\\ServiceDiscover' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/ServiceDiscover.php',
        'think\\console\\command\\VendorPublish' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/VendorPublish.php',
        'think\\console\\command\\Version' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/Version.php',
        'think\\console\\command\\make\\Command' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Command.php',
        'think\\console\\command\\make\\Controller' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Controller.php',
        'think\\console\\command\\make\\Event' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Event.php',
        'think\\console\\command\\make\\Listener' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Listener.php',
        'think\\console\\command\\make\\Middleware' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Middleware.php',
        'think\\console\\command\\make\\Model' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Model.php',
        'think\\console\\command\\make\\Service' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Service.php',
        'think\\console\\command\\make\\Subscribe' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Subscribe.php',
        'think\\console\\command\\make\\Validate' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Validate.php',
        'think\\console\\command\\optimize\\Route' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/optimize/Route.php',
        'think\\console\\command\\optimize\\Schema' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/optimize/Schema.php',
        'think\\console\\input\\Argument' => __DIR__ . '/..' . '/topthink/framework/src/think/console/input/Argument.php',
        'think\\console\\input\\Definition' => __DIR__ . '/..' . '/topthink/framework/src/think/console/input/Definition.php',
        'think\\console\\input\\Option' => __DIR__ . '/..' . '/topthink/framework/src/think/console/input/Option.php',
        'think\\console\\output\\Ask' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/Ask.php',
        'think\\console\\output\\Descriptor' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/Descriptor.php',
        'think\\console\\output\\Formatter' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/Formatter.php',
        'think\\console\\output\\Question' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/Question.php',
        'think\\console\\output\\descriptor\\Console' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/descriptor/Console.php',
        'think\\console\\output\\driver\\Buffer' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/driver/Buffer.php',
        'think\\console\\output\\driver\\Console' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/driver/Console.php',
        'think\\console\\output\\driver\\Nothing' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/driver/Nothing.php',
        'think\\console\\output\\formatter\\Stack' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/formatter/Stack.php',
        'think\\console\\output\\formatter\\Style' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/formatter/Style.php',
        'think\\console\\output\\question\\Choice' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/question/Choice.php',
        'think\\console\\output\\question\\Confirmation' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/question/Confirmation.php',
        'think\\contract\\Arrayable' => __DIR__ . '/..' . '/topthink/think-helper/src/contract/Arrayable.php',
        'think\\contract\\CacheHandlerInterface' => __DIR__ . '/..' . '/topthink/framework/src/think/contract/CacheHandlerInterface.php',
        'think\\contract\\Jsonable' => __DIR__ . '/..' . '/topthink/think-helper/src/contract/Jsonable.php',
        'think\\contract\\LogHandlerInterface' => __DIR__ . '/..' . '/topthink/framework/src/think/contract/LogHandlerInterface.php',
        'think\\contract\\ModelRelationInterface' => __DIR__ . '/..' . '/topthink/framework/src/think/contract/ModelRelationInterface.php',
        'think\\contract\\SessionHandlerInterface' => __DIR__ . '/..' . '/topthink/framework/src/think/contract/SessionHandlerInterface.php',
        'think\\contract\\TemplateHandlerInterface' => __DIR__ . '/..' . '/topthink/framework/src/think/contract/TemplateHandlerInterface.php',
        'think\\db\\BaseQuery' => __DIR__ . '/..' . '/topthink/think-orm/src/db/BaseQuery.php',
        'think\\db\\Builder' => __DIR__ . '/..' . '/topthink/think-orm/src/db/Builder.php',
        'think\\db\\CacheItem' => __DIR__ . '/..' . '/topthink/think-orm/src/db/CacheItem.php',
        'think\\db\\Connection' => __DIR__ . '/..' . '/topthink/think-orm/src/db/Connection.php',
        'think\\db\\ConnectionInterface' => __DIR__ . '/..' . '/topthink/think-orm/src/db/ConnectionInterface.php',
        'think\\db\\Fetch' => __DIR__ . '/..' . '/topthink/think-orm/src/db/Fetch.php',
        'think\\db\\Mongo' => __DIR__ . '/..' . '/topthink/think-orm/src/db/Mongo.php',
        'think\\db\\PDOConnection' => __DIR__ . '/..' . '/topthink/think-orm/src/db/PDOConnection.php',
        'think\\db\\Query' => __DIR__ . '/..' . '/topthink/think-orm/src/db/Query.php',
        'think\\db\\Raw' => __DIR__ . '/..' . '/topthink/think-orm/src/db/Raw.php',
        'think\\db\\Where' => __DIR__ . '/..' . '/topthink/think-orm/src/db/Where.php',
        'think\\db\\builder\\Mongo' => __DIR__ . '/..' . '/topthink/think-orm/src/db/builder/Mongo.php',
        'think\\db\\builder\\Mysql' => __DIR__ . '/..' . '/topthink/think-orm/src/db/builder/Mysql.php',
        'think\\db\\builder\\Oracle' => __DIR__ . '/..' . '/topthink/think-orm/src/db/builder/Oracle.php',
        'think\\db\\builder\\Pgsql' => __DIR__ . '/..' . '/topthink/think-orm/src/db/builder/Pgsql.php',
        'think\\db\\builder\\Sqlite' => __DIR__ . '/..' . '/topthink/think-orm/src/db/builder/Sqlite.php',
        'think\\db\\builder\\Sqlsrv' => __DIR__ . '/..' . '/topthink/think-orm/src/db/builder/Sqlsrv.php',
        'think\\db\\concern\\AggregateQuery' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/AggregateQuery.php',
        'think\\db\\concern\\JoinAndViewQuery' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/JoinAndViewQuery.php',
        'think\\db\\concern\\ModelRelationQuery' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/ModelRelationQuery.php',
        'think\\db\\concern\\ParamsBind' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/ParamsBind.php',
        'think\\db\\concern\\ResultOperation' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/ResultOperation.php',
        'think\\db\\concern\\TableFieldInfo' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/TableFieldInfo.php',
        'think\\db\\concern\\TimeFieldQuery' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/TimeFieldQuery.php',
        'think\\db\\concern\\Transaction' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/Transaction.php',
        'think\\db\\concern\\WhereQuery' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/WhereQuery.php',
        'think\\db\\connector\\Mongo' => __DIR__ . '/..' . '/topthink/think-orm/src/db/connector/Mongo.php',
        'think\\db\\connector\\Mysql' => __DIR__ . '/..' . '/topthink/think-orm/src/db/connector/Mysql.php',
        'think\\db\\connector\\Oracle' => __DIR__ . '/..' . '/topthink/think-orm/src/db/connector/Oracle.php',
        'think\\db\\connector\\Pgsql' => __DIR__ . '/..' . '/topthink/think-orm/src/db/connector/Pgsql.php',
        'think\\db\\connector\\Sqlite' => __DIR__ . '/..' . '/topthink/think-orm/src/db/connector/Sqlite.php',
        'think\\db\\connector\\Sqlsrv' => __DIR__ . '/..' . '/topthink/think-orm/src/db/connector/Sqlsrv.php',
        'think\\db\\exception\\BindParamException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/BindParamException.php',
        'think\\db\\exception\\DataNotFoundException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/DataNotFoundException.php',
        'think\\db\\exception\\DbEventException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/DbEventException.php',
        'think\\db\\exception\\DbException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/DbException.php',
        'think\\db\\exception\\InvalidArgumentException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/InvalidArgumentException.php',
        'think\\db\\exception\\ModelEventException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/ModelEventException.php',
        'think\\db\\exception\\ModelNotFoundException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/ModelNotFoundException.php',
        'think\\db\\exception\\PDOException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/PDOException.php',
        'think\\event\\AppInit' => __DIR__ . '/..' . '/topthink/framework/src/think/event/AppInit.php',
        'think\\event\\HttpEnd' => __DIR__ . '/..' . '/topthink/framework/src/think/event/HttpEnd.php',
        'think\\event\\HttpRun' => __DIR__ . '/..' . '/topthink/framework/src/think/event/HttpRun.php',
        'think\\event\\LogRecord' => __DIR__ . '/..' . '/topthink/framework/src/think/event/LogRecord.php',
        'think\\event\\LogWrite' => __DIR__ . '/..' . '/topthink/framework/src/think/event/LogWrite.php',
        'think\\event\\RouteLoaded' => __DIR__ . '/..' . '/topthink/framework/src/think/event/RouteLoaded.php',
        'think\\exception\\ClassNotFoundException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/ClassNotFoundException.php',
        'think\\exception\\ErrorException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/ErrorException.php',
        'think\\exception\\FileException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/FileException.php',
        'think\\exception\\FuncNotFoundException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/FuncNotFoundException.php',
        'think\\exception\\Handle' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/Handle.php',
        'think\\exception\\HttpException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/HttpException.php',
        'think\\exception\\HttpResponseException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/HttpResponseException.php',
        'think\\exception\\InvalidArgumentException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/InvalidArgumentException.php',
        'think\\exception\\RouteNotFoundException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/RouteNotFoundException.php',
        'think\\exception\\ValidateException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/ValidateException.php',
        'think\\facade\\App' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/App.php',
        'think\\facade\\Cache' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Cache.php',
        'think\\facade\\Config' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Config.php',
        'think\\facade\\Console' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Console.php',
        'think\\facade\\Cookie' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Cookie.php',
        'think\\facade\\Db' => __DIR__ . '/..' . '/topthink/think-orm/src/facade/Db.php',
        'think\\facade\\Env' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Env.php',
        'think\\facade\\Event' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Event.php',
        'think\\facade\\Lang' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Lang.php',
        'think\\facade\\Log' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Log.php',
        'think\\facade\\Middleware' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Middleware.php',
        'think\\facade\\Request' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Request.php',
        'think\\facade\\Route' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Route.php',
        'think\\facade\\Session' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Session.php',
        'think\\facade\\Template' => __DIR__ . '/..' . '/topthink/think-template/src/facade/Template.php',
        'think\\facade\\Validate' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Validate.php',
        'think\\facade\\View' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/View.php',
        'think\\file\\UploadedFile' => __DIR__ . '/..' . '/topthink/framework/src/think/file/UploadedFile.php',
        'think\\helper\\Arr' => __DIR__ . '/..' . '/topthink/think-helper/src/helper/Arr.php',
        'think\\helper\\Macroable' => __DIR__ . '/..' . '/topthink/think-helper/src/helper/Macroable.php',
        'think\\helper\\Str' => __DIR__ . '/..' . '/topthink/think-helper/src/helper/Str.php',
        'think\\initializer\\BootService' => __DIR__ . '/..' . '/topthink/framework/src/think/initializer/BootService.php',
        'think\\initializer\\Error' => __DIR__ . '/..' . '/topthink/framework/src/think/initializer/Error.php',
        'think\\initializer\\RegisterService' => __DIR__ . '/..' . '/topthink/framework/src/think/initializer/RegisterService.php',
        'think\\log\\Channel' => __DIR__ . '/..' . '/topthink/framework/src/think/log/Channel.php',
        'think\\log\\ChannelSet' => __DIR__ . '/..' . '/topthink/framework/src/think/log/ChannelSet.php',
        'think\\log\\driver\\File' => __DIR__ . '/..' . '/topthink/framework/src/think/log/driver/File.php',
        'think\\log\\driver\\Socket' => __DIR__ . '/..' . '/topthink/framework/src/think/log/driver/Socket.php',
        'think\\middleware\\AllowCrossDomain' => __DIR__ . '/..' . '/topthink/framework/src/think/middleware/AllowCrossDomain.php',
        'think\\middleware\\CheckRequestCache' => __DIR__ . '/..' . '/topthink/framework/src/think/middleware/CheckRequestCache.php',
        'think\\middleware\\FormTokenCheck' => __DIR__ . '/..' . '/topthink/framework/src/think/middleware/FormTokenCheck.php',
        'think\\middleware\\LoadLangPack' => __DIR__ . '/..' . '/topthink/framework/src/think/middleware/LoadLangPack.php',
        'think\\middleware\\SessionInit' => __DIR__ . '/..' . '/topthink/framework/src/think/middleware/SessionInit.php',
        'think\\model\\Collection' => __DIR__ . '/..' . '/topthink/think-orm/src/model/Collection.php',
        'think\\model\\Pivot' => __DIR__ . '/..' . '/topthink/think-orm/src/model/Pivot.php',
        'think\\model\\Relation' => __DIR__ . '/..' . '/topthink/think-orm/src/model/Relation.php',
        'think\\model\\concern\\Attribute' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/Attribute.php',
        'think\\model\\concern\\Conversion' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/Conversion.php',
        'think\\model\\concern\\ModelEvent' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/ModelEvent.php',
        'think\\model\\concern\\OptimLock' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/OptimLock.php',
        'think\\model\\concern\\RelationShip' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/RelationShip.php',
        'think\\model\\concern\\SoftDelete' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/SoftDelete.php',
        'think\\model\\concern\\TimeStamp' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/TimeStamp.php',
        'think\\model\\concern\\Virtual' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/Virtual.php',
        'think\\model\\relation\\BelongsTo' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/BelongsTo.php',
        'think\\model\\relation\\BelongsToMany' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/BelongsToMany.php',
        'think\\model\\relation\\HasMany' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/HasMany.php',
        'think\\model\\relation\\HasManyThrough' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/HasManyThrough.php',
        'think\\model\\relation\\HasOne' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/HasOne.php',
        'think\\model\\relation\\HasOneThrough' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/HasOneThrough.php',
        'think\\model\\relation\\MorphMany' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/MorphMany.php',
        'think\\model\\relation\\MorphOne' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/MorphOne.php',
        'think\\model\\relation\\MorphTo' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/MorphTo.php',
        'think\\model\\relation\\MorphToMany' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/MorphToMany.php',
        'think\\model\\relation\\OneToOne' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/OneToOne.php',
        'think\\paginator\\driver\\Bootstrap' => __DIR__ . '/..' . '/topthink/think-orm/src/paginator/driver/Bootstrap.php',
        'think\\response\\File' => __DIR__ . '/..' . '/topthink/framework/src/think/response/File.php',
        'think\\response\\Html' => __DIR__ . '/..' . '/topthink/framework/src/think/response/Html.php',
        'think\\response\\Json' => __DIR__ . '/..' . '/topthink/framework/src/think/response/Json.php',
        'think\\response\\Jsonp' => __DIR__ . '/..' . '/topthink/framework/src/think/response/Jsonp.php',
        'think\\response\\Redirect' => __DIR__ . '/..' . '/topthink/framework/src/think/response/Redirect.php',
        'think\\response\\View' => __DIR__ . '/..' . '/topthink/framework/src/think/response/View.php',
        'think\\response\\Xml' => __DIR__ . '/..' . '/topthink/framework/src/think/response/Xml.php',
        'think\\route\\Dispatch' => __DIR__ . '/..' . '/topthink/framework/src/think/route/Dispatch.php',
        'think\\route\\Domain' => __DIR__ . '/..' . '/topthink/framework/src/think/route/Domain.php',
        'think\\route\\Resource' => __DIR__ . '/..' . '/topthink/framework/src/think/route/Resource.php',
        'think\\route\\ResourceRegister' => __DIR__ . '/..' . '/topthink/framework/src/think/route/ResourceRegister.php',
        'think\\route\\Rule' => __DIR__ . '/..' . '/topthink/framework/src/think/route/Rule.php',
        'think\\route\\RuleGroup' => __DIR__ . '/..' . '/topthink/framework/src/think/route/RuleGroup.php',
        'think\\route\\RuleItem' => __DIR__ . '/..' . '/topthink/framework/src/think/route/RuleItem.php',
        'think\\route\\RuleName' => __DIR__ . '/..' . '/topthink/framework/src/think/route/RuleName.php',
        'think\\route\\Url' => __DIR__ . '/..' . '/topthink/framework/src/think/route/Url.php',
        'think\\route\\dispatch\\Callback' => __DIR__ . '/..' . '/topthink/framework/src/think/route/dispatch/Callback.php',
        'think\\route\\dispatch\\Controller' => __DIR__ . '/..' . '/topthink/framework/src/think/route/dispatch/Controller.php',
        'think\\route\\dispatch\\Url' => __DIR__ . '/..' . '/topthink/framework/src/think/route/dispatch/Url.php',
        'think\\service\\ModelService' => __DIR__ . '/..' . '/topthink/framework/src/think/service/ModelService.php',
        'think\\service\\PaginatorService' => __DIR__ . '/..' . '/topthink/framework/src/think/service/PaginatorService.php',
        'think\\service\\ValidateService' => __DIR__ . '/..' . '/topthink/framework/src/think/service/ValidateService.php',
        'think\\session\\Store' => __DIR__ . '/..' . '/topthink/framework/src/think/session/Store.php',
        'think\\session\\driver\\Cache' => __DIR__ . '/..' . '/topthink/framework/src/think/session/driver/Cache.php',
        'think\\session\\driver\\File' => __DIR__ . '/..' . '/topthink/framework/src/think/session/driver/File.php',
        'think\\template\\TagLib' => __DIR__ . '/..' . '/topthink/think-template/src/template/TagLib.php',
        'think\\template\\driver\\File' => __DIR__ . '/..' . '/topthink/think-template/src/template/driver/File.php',
        'think\\template\\exception\\TemplateNotFoundException' => __DIR__ . '/..' . '/topthink/think-template/src/template/exception/TemplateNotFoundException.php',
        'think\\template\\taglib\\Cx' => __DIR__ . '/..' . '/topthink/think-template/src/template/taglib/Cx.php',
        'think\\validate\\ValidateRule' => __DIR__ . '/..' . '/topthink/framework/src/think/validate/ValidateRule.php',
        'think\\view\\driver\\Php' => __DIR__ . '/..' . '/topthink/framework/src/think/view/driver/Php.php',
        'think\\view\\driver\\Think' => __DIR__ . '/..' . '/topthink/think-view/src/Think.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit6cfa549e1e9810425484c355b5559751::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit6cfa549e1e9810425484c355b5559751::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit6cfa549e1e9810425484c355b5559751::$classMap;

        }, null, ClassLoader::class);
    }
}
