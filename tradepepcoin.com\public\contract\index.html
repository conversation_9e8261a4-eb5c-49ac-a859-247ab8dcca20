<script>
function checkLoginStatus() {
    const isLoggedIn = localStorage.getItem("isLoggedIn") === "true";
    if (!isLoggedIn) {
        alert("请先登录或注册");
        window.location.href = "/auth/login.html";
        return false;
    }
    return true;
}
document.addEventListener("DOMContentLoaded", function() {
    if (!checkLoginStatus()) return;
});
</script>
<script>
function checkLoginStatus() {
    const isLoggedIn = localStorage.getItem("isLoggedIn") === "true";
    if (!isLoggedIn) {
        alert("请先登录或注册");
        window.location.href = "/auth/login.html";
        return false;
    }
    return true;
}
document.addEventListener("DOMContentLoaded", function() {
    if (!checkLoginStatus()) return;
});
</script>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <script src="/Public/Static/js/lang.js"></script>
    <script src="/Public/Static/js/lang.js"></script>
    <script src="/Public/Static/js/lang.js"></script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>秒合约交易 - 数字货币交易平台</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #151617; color: #fff; }
        
        .header { background: #2d2d2d; padding: 15px 0; border-bottom: 1px solid #333; }
        .container { max-width: 1400px; margin: 0 auto; padding: 0 20px; }
        .nav { display: flex; justify-content: space-between; align-items: center; }
        .logo { font-size: 24px; font-weight: bold; color: #007bff; }
        .nav-links a { color: #fff; text-decoration: none; margin: 0 15px; }
        
        .main-layout { display: flex; height: calc(100vh - 64px); }
        
        /* 左侧K线图区域 */
        .chart-section { flex: 1; background: #1a1b1c; border-right: 1px solid #2c2d2e; }
        .chart-header { height: 50px; background: #1a1b1c; padding: 0 20px; display: flex; align-items: center; border-bottom: 1px solid #2c2d2e; }
        .price-info { display: flex; align-items: center; gap: 30px; }
        .current-price { font-size: 24px; font-weight: bold; color: #2ebd85; }
        .price-change { font-size: 14px; color: #2ebd85; }
        .price-stats { display: flex; gap: 20px; font-size: 12px; color: #999; }
        
        .chart-container { height: calc(100% - 50px); position: relative; }
        .chart-iframe { width: 100%; height: 100%; border: none; }
        
        /* 右侧交易面板 */
        .trading-section { width: 350px; background: #151617; padding: 20px; }
        .section-title { font-size: 14px; color: #999; margin-bottom: 15px; }
        
        /* 时间周期选择 */
        .time-periods { display: flex; flex-wrap: wrap; gap: 8px; margin-bottom: 20px; }
        .time-btn { 
            flex: 1; min-width: 60px; height: 35px; background: #2c2d2e; color: #999; 
            border: none; border-radius: 4px; cursor: pointer; font-size: 12px;
            display: flex; flex-direction: column; align-items: center; justify-content: center;
            transition: all 0.3s;
        }
        .time-btn.active { background: #007bff; color: #fff; }
        .time-btn:hover { background: #007bff; color: #fff; }
        .time-text { font-size: 11px; }
        .profit-text { font-size: 10px; opacity: 0.8; }
        
        /* 投注金额 */
        .amount-section { margin-bottom: 20px; }
        .amount-input { 
            width: 100%; height: 40px; background: #2c2d2e; border: 1px solid #333; 
            color: #fff; padding: 0 15px; border-radius: 4px; font-size: 16px; text-align: center;
        }
        .quick-amounts { display: flex; flex-wrap: wrap; gap: 8px; margin-top: 10px; }
        .quick-btn { 
            flex: 1; height: 30px; background: #2c2d2e; color: #999; border: none; 
            border-radius: 4px; cursor: pointer; font-size: 12px; transition: all 0.3s;
        }
        .quick-btn:hover { background: #007bff; color: #fff; }
        
        /* 余额显示 */
        .balance-info { 
            background: #2c2d2e; padding: 15px; border-radius: 4px; margin-bottom: 20px;
            text-align: center; font-size: 14px;
        }
        
        /* 交易按钮 */
        .trade-buttons { display: flex; gap: 10px; margin-bottom: 30px; }
        .trade-btn { 
            flex: 1; height: 50px; border: none; border-radius: 8px; font-size: 16px; 
            font-weight: bold; cursor: pointer; transition: all 0.3s;
            display: flex; flex-direction: column; align-items: center; justify-content: center;
        }
        .buy-btn { background: #2ebd85; color: #fff; }
        .sell-btn { background: #f5465c; color: #fff; }
        .trade-btn:hover { transform: translateY(-1px); opacity: 0.9; }
        .trade-btn:disabled { opacity: 0.5; cursor: not-allowed; }
        
        /* 订单列表 */
        .orders-section { border-top: 1px solid #2c2d2e; padding-top: 20px; }
        .order-tabs { display: flex; margin-bottom: 15px; }
        .order-tab { 
            flex: 1; height: 35px; background: transparent; color: #999; border: none; 
            cursor: pointer; font-size: 14px; border-bottom: 2px solid transparent;
        }
        .order-tab.active { color: #fff; border-bottom-color: #007bff; }
        
        .order-list { max-height: 300px; overflow-y: auto; }
        .order-item { 
            background: #2c2d2e; padding: 10px; border-radius: 4px; margin-bottom: 8px;
            font-size: 12px;
        }
        .order-header { display: flex; justify-content: space-between; margin-bottom: 5px; }
        .order-details { display: flex; justify-content: space-between; color: #999; }
        
        /* 确认下单弹窗 */
        .modal { 
            display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
            background: rgba(0,0,0,0.8); z-index: 1000; align-items: center; justify-content: center;
        }
        .modal.show { display: flex; }
        .modal-content { 
            background: #2d2d2d; padding: 30px; border-radius: 10px; width: 400px; 
            max-width: 90vw; border: 1px solid #333; position: relative;
        }
        .modal-header { text-align: center; margin-bottom: 20px; font-size: 18px; font-weight: bold; }
        .modal-body { margin-bottom: 20px; }
        .modal-row { display: flex; justify-content: space-between; margin-bottom: 10px; }
        .modal-buttons { display: flex; gap: 15px; }
        .modal-btn { 
            flex: 1; height: 45px; border: none; border-radius: 8px; font-size: 16px; 
            font-weight: bold; cursor: pointer;
        }
        .confirm-btn { background: #2ebd85; color: #fff; }
        .cancel-btn { background: #666; color: #fff; }
        
        /* 关闭按钮 */
        .close-btn { 
            position: absolute; top: 10px; right: 15px; background: none; border: none; 
            color: #999; font-size: 24px; cursor: pointer; width: 30px; height: 30px;
            display: flex; align-items: center; justify-content: center;
        }
        .close-btn:hover { color: #fff; }
        
        /* 倒计时界面 */
        .countdown-modal .modal-content { text-align: center; }
        .countdown-display { 
            font-size: 48px; font-weight: bold; color: #007bff; margin: 20px 0;
            font-family: 'Courier New', monospace;
        }
        .countdown-info { margin-bottom: 20px; }
        .countdown-price { font-size: 24px; font-weight: bold; color: #2ebd85; margin: 10px 0; }
        .server-time { font-size: 12px; color: #666; margin-top: 10px; }
        
        /* 进度条 */
        .progress-bar { 
            width: 100%; height: 6px; background: #333; border-radius: 3px; 
            margin: 15px 0; overflow: hidden;
        }
        .progress-fill { 
            height: 100%; background: linear-gradient(90deg, #007bff, #00d4ff); 
            border-radius: 3px; transition: width 1s linear;
        }
        
        /* 结果显示 */
        .result-display { font-size: 32px; font-weight: bold; margin: 20px 0; }
        .result-win { color: #2ebd85; }
        .result-lose { color: #f5465c; }
        
        @media (max-width: 768px) {
            .main-layout { flex-direction: column; }
            .trading-section { width: 100%; }
        }
    </style>
</head>
<body>
    <select class="lang-selector" id="langSelector" style="position: absolute; top: 20px; right: 20px; background: #333; color: #fff; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; font-size: 14px; z-index: 9999;">
        <option value="zh-cn">中文简体</option>
        <option value="en-us">English</option>
        <option value="fr-fr">Français</option>
        <option value="de-de">Deutsch</option>
        <option value="it-it">Italiano</option>
        <option value="ja-jp">日本語</option>
        <option value="ko-kr">한국어</option>
        <option value="tr-tr">Türkçe</option>
    </select>
    <select class="lang-selector" id="langSelector" style="position: absolute; top: 20px; right: 20px; background: #333; color: #fff; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; font-size: 14px; z-index: 9999;">
        <option value="zh-cn">中文简体</option>
        <option value="en-us">English</option>
        <option value="fr-fr">Français</option>
        <option value="de-de">Deutsch</option>
        <option value="it-it">Italiano</option>
        <option value="ja-jp">日本語</option>
        <option value="ko-kr">한국어</option>
        <option value="tr-tr">Türkçe</option>
    </select>
    <select class="lang-selector" id="langSelector" style="position: absolute; top: 20px; right: 20px; background: #333; color: #fff; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; font-size: 14px; z-index: 9999;">
        <option value="zh-cn">中文简体</option>
        <option value="en-us">English</option>
        <option value="fr-fr">Français</option>
        <option value="de-de">Deutsch</option>
        <option value="it-it">Italiano</option>
        <option value="ja-jp">日本語</option>
        <option value="ko-kr">한국어</option>
        <option value="tr-tr">Türkçe</option>
    </select>
<script>
function checkLogin() {
    const isLoggedIn = localStorage.getItem("isLoggedIn") === "true";
    if (!isLoggedIn) {
        alert("请先登录或注册");
        window.location.href = "/auth/login.html";
        return false;
    }
    return true;
}

// 页面加载时检查登录状态
document.addEventListener("DOMContentLoaded", function() {
    if (!checkLogin()) return;
});
</script>
    <div class="header">
        <div class="container">
            <div class="nav">
                <div class="logo">⚡ 秒合约交易</div>
                <div class="nav-links">
                    <a href="/">首页</a>
                    <a href="/futures/">期货交易</a>
                    <a href="/wallet/">钱包</a>
                    <a href="/auth/login.html">登录</a>
                </div>
            </div>
        </div>
    </div>

    <div class="main-layout">
        <!-- K线图区域 -->
        <div class="chart-section">
            <div class="chart-header">
                <div class="price-info">
                    <div>
                        <div class="current-price" id="currentPrice">$45,230.50</div>
                        <div class="price-change" id="priceChange">+2.34% (+$1,032.15)</div>
                    </div>
                    <div class="price-stats">
                        <div>最高: <span id="highPrice">$45,890.20</span></div>
                        <div>最低: <span id="lowPrice">$44,120.80</span></div>
                        <div>24h量: <span id="volume">12,345.67</span></div>
                    </div>
                </div>
            </div>
            <div class="chart-container">
                <iframe class="chart-iframe" src="https://s.tradingview.com/widgetembed/?frameElementId=tradingview_chart&symbol=BINANCE%3ABTCUSDT&interval=1&hidesidetoolbar=1&hidetoptoolbar=1&symboledit=1&saveimage=1&toolbarbg=f1f3f6&studies=%5B%5D&hideideas=1&theme=dark&style=1&timezone=Etc%2FUTC&studies_overrides=%7B%7D&overrides=%7B%7D&enabled_features=%5B%5D&disabled_features=%5B%5D&locale=zh_CN&utm_source=localhost&utm_medium=widget&utm_campaign=chart&utm_term=BINANCE%3ABTCUSDT"></iframe>
            </div>
        </div>

        <!-- 交易面板 -->
        <div class="trading-section">
            <div class="section-title">选择周期</div>
            <div class="time-periods">
                <button class="time-btn active" data-time="60" data-profit="75">
                    <span class="time-text">60秒</span>
                    <span class="profit-text">75%</span>
                </button>
                <button class="time-btn" data-time="180" data-profit="77">
                    <span class="time-text">180秒</span>
                    <span class="profit-text">77%</span>
                </button>
                <button class="time-btn" data-time="300" data-profit="80">
                    <span class="time-text">300秒</span>
                    <span class="profit-text">80%</span>
                </button>
                <button class="time-btn" data-time="1800" data-profit="85">
                    <span class="time-text">30分钟</span>
                    <span class="profit-text">85%</span>
                </button>
            </div>

            <div class="section-title">投注金额 (USDT)</div>
            <div class="amount-section">
                <input type="number" class="amount-input" id="amountInput" placeholder="请输入投注金额" value="100">
                <div class="quick-amounts">
                    <button class="quick-btn" data-amount="50">50</button>
                    <button class="quick-btn" data-amount="100">100</button>
                    <button class="quick-btn" data-amount="500">500</button>
                    <button class="quick-btn" data-amount="1000">1000</button>
                </div>
            </div>

            <div class="balance-info">
                账户余额: <span id="balance">1,000.00</span> USDT
            </div>

            <div class="trade-buttons">
                <button class="trade-btn buy-btn" onclick="showOrderModal('up')">
                    📈<br>买涨
                </button>
                <button class="trade-btn sell-btn" onclick="showOrderModal('down')">
                    📉<br>买跌
                </button>
            </div>

            <div class="orders-section">
                <div class="order-tabs">
                    <button class="order-tab active" onclick="switchOrderTab('current')">当前委托</button>
                    <button class="order-tab" onclick="switchOrderTab('history')">历史委托</button>
                </div>
                <div class="order-list" id="orderList">
                    <!-- 订单列表将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 确认下单弹窗 -->
    <div class="modal" id="orderModal">
        <div class="modal-content">
            <button class="close-btn" onclick="closeModal()">&times;</button>
            <div class="modal-header">确认下单</div>
            <div class="modal-body">
                <div class="modal-row">
                    <span>交易对:</span>
                    <span>BTC/USDT</span>
                </div>
                <div class="modal-row">
                    <span>方向:</span>
                    <span id="modalDirection" class="result-win">买涨</span>
                </div>
                <div class="modal-row">
                    <span>现价:</span>
                    <span id="modalPrice">$45,230.50</span>
                </div>
                <div class="modal-row">
                    <span>周期:</span>
                    <span id="modalTime">60秒</span>
                </div>
                <div class="modal-row">
                    <span>投注金额:</span>
                    <span id="modalAmount">100 USDT</span>
                </div>
                <div class="modal-row">
                    <span>盈利率:</span>
                    <span id="modalProfit">75%</span>
                </div>
                <div class="modal-row">
                    <span>预期收益:</span>
                    <span id="modalExpected" class="result-win">+75 USDT</span>
                </div>
            </div>
            <div class="modal-buttons">
                <button class="modal-btn cancel-btn" onclick="closeModal()">取消</button>
                <button class="modal-btn confirm-btn" onclick="confirmOrder()">确认下单</button>
            </div>
        </div>
    </div>

    <!-- 倒计时弹窗 -->
    <div class="modal countdown-modal" id="countdownModal">
        <div class="modal-content">
            <button class="close-btn" onclick="hideCountdownModal()" title="退出倒计时界面（订单继续执行）">&times;</button>
            <div class="modal-header">合约进行中</div>
            <div class="countdown-info">
                <div>交易对: BTC/USDT</div>
                <div>方向: <span id="countdownDirection">买涨</span></div>
                <div>投注: <span id="countdownAmount">100</span> USDT</div>
                <div>开盘价: <span id="countdownOpenPrice">$45,230.50</span></div>
                <div style="font-size: 12px; color: #ffc107; margin-top: 5px;">
                    订单ID: <span id="orderIdDisplay"></span>
                </div>
            </div>
            <div class="countdown-display" id="countdownDisplay">00:60</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="countdown-price" id="countdownPrice">$45,230.50</div>
            <div class="server-time">服务器时间: <span id="serverTime"></span></div>
            <div style="color: #999; font-size: 12px; margin-top: 10px;">
                实时价格更新中... | 点击 × 可退出界面，订单继续执行
            </div>
        </div>
    </div>

    <!-- 结果弹窗 -->
    <div class="modal" id="resultModal">
        <div class="modal-content">
            <button class="close-btn" onclick="closeModal()">&times;</button>
            <div class="modal-header">结算结果</div>
            <div class="result-display" id="resultDisplay">+75 USDT</div>
            <div class="modal-body">
                <div class="modal-row">
                    <span>订单ID:</span>
                    <span id="resultOrderId">ORDER_123456</span>
                </div>
                <div class="modal-row">
                    <span>开盘价:</span>
                    <span id="resultOpenPrice">$45,230.50</span>
                </div>
                <div class="modal-row">
                    <span>收盘价:</span>
                    <span id="resultClosePrice">$45,330.20</span>
                </div>
                <div class="modal-row">
                    <span>涨跌:</span>
                    <span id="resultChange" class="result-win">+$99.70</span>
                </div>
                <div class="modal-row">
                    <span>实际用时:</span>
                    <span id="actualTime">60秒</span>
                </div>
            </div>
            <div class="modal-buttons">
                <button class="modal-btn confirm-btn" onclick="closeModal()">继续交易</button>
            </div>
        </div>
    </div>

    <script>
        let selectedTime = 60;
        let selectedProfit = 75;
        let currentDirection = 'up';
        let countdownTimer = null;
        let orderData = {};
        let serverTimeOffset = 0;
        let orderStartTime = 0;
        let orderEndTime = 0;
        let isCountdownHidden = false; // 倒计时是否被隐藏

        // 获取服务器时间
        function getServerTime() {
            return Date.now() + serverTimeOffset;
        }

        // 同步服务器时间
        function syncServerTime() {
            const startTime = Date.now();
            setTimeout(() => {
                const endTime = Date.now();
                const networkDelay = (endTime - startTime) / 2;
                const serverTime = Date.now();
                serverTimeOffset = serverTime - Date.now() + networkDelay;
                console.log('服务器时间同步完成，偏移:', serverTimeOffset + 'ms');
            }, 50);
        }

        // 更新服务器时间显示
        function updateServerTimeDisplay() {
            const serverTime = new Date(getServerTime());
            document.getElementById('serverTime').textContent = 
                serverTime.toLocaleTimeString('zh-CN', { hour12: false });
        }

        // 时间周期选择
        document.querySelectorAll('.time-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.time-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                selectedTime = parseInt(this.dataset.time);
                selectedProfit = parseInt(this.dataset.profit);
            });
        });

        // 快捷金额选择
        document.querySelectorAll('.quick-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.getElementById('amountInput').value = this.dataset.amount;
            });
        });

        // 显示下单确认弹窗
        function showOrderModal(direction) {
            const amount = document.getElementById('amountInput').value;
            if (!amount || amount <= 0) {
                alert('请输入有效的投注金额');
                return;
            }

            currentDirection = direction;
            const currentPrice = document.getElementById('currentPrice').textContent;
            
            document.getElementById('modalDirection').textContent = direction === 'up' ? '买涨' : '买跌';
            document.getElementById('modalDirection').className = direction === 'up' ? 'result-win' : 'result-lose';
            document.getElementById('modalPrice').textContent = currentPrice;
            document.getElementById('modalTime').textContent = selectedTime + '秒';
            document.getElementById('modalAmount').textContent = amount + ' USDT';
            document.getElementById('modalProfit').textContent = selectedProfit + '%';
            document.getElementById('modalExpected').textContent = '+' + (amount * selectedProfit / 100).toFixed(2) + ' USDT';
            
            document.getElementById('orderModal').classList.add('show');
        }

        // 确认下单
        function confirmOrder() {
            const amount = document.getElementById('amountInput').value;
            const currentPrice = parseFloat(document.getElementById('currentPrice').textContent.replace('$', '').replace(',', ''));
            
            syncServerTime();
            
            orderStartTime = getServerTime();
            orderEndTime = orderStartTime + (selectedTime * 1000);
            
            // 生成订单ID
            const orderId = 'ORDER_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            
            orderData = {
                id: orderId,
                direction: currentDirection,
                amount: parseFloat(amount),
                openPrice: currentPrice,
                time: selectedTime,
                profit: selectedProfit,
                startTime: orderStartTime,
                endTime: orderEndTime,
                status: 'running' // 订单状态：running, completed, cancelled
            };

            console.log('✅ 订单已创建:', orderData);
            
            // 这里应该调用API创建订单
            // createOrderAPI(orderData);
            
            closeModal();
            startCountdown();
        }

        // 开始倒计时
        function startCountdown() {
            isCountdownHidden = false;
            document.getElementById('countdownDirection').textContent = orderData.direction === 'up' ? '买涨' : '买跌';
            document.getElementById('countdownAmount').textContent = orderData.amount;
            document.getElementById('countdownOpenPrice').textContent = '$' + orderData.openPrice.toLocaleString();
            document.getElementById('orderIdDisplay').textContent = orderData.id;
            document.getElementById('countdownModal').classList.add('show');
            
            updateCountdownDisplay();
            
            countdownTimer = setInterval(() => {
                updateCountdownDisplay();
            }, 100);
        }

        // 更新倒计时显示
        function updateCountdownDisplay() {
            const currentTime = getServerTime();
            const timeLeft = Math.max(0, orderData.endTime - currentTime);
            const totalTime = orderData.time * 1000;
            const progress = Math.max(0, Math.min(100, ((totalTime - timeLeft) / totalTime) * 100));
            
            // 更新倒计时显示
            const seconds = Math.ceil(timeLeft / 1000);
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            document.getElementById('countdownDisplay').textContent = 
                `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
            
            // 更新进度条
            document.getElementById('progressFill').style.width = progress + '%';
            
            // 更新服务器时间
            updateServerTimeDisplay();
            
            // 模拟价格变化
            updatePrice();
            
            // 检查是否结束
            if (timeLeft <= 0) {
                clearInterval(countdownTimer);
                countdownTimer = null;
                settleOrder();
            }
        }

        // 结算订单
        function settleOrder() {
            const currentPrice = parseFloat(document.getElementById('currentPrice').textContent.replace('$', '').replace(',', ''));
            const openPrice = orderData.openPrice;
            const isWin = (orderData.direction === 'up' && currentPrice > openPrice) || 
                         (orderData.direction === 'down' && currentPrice < openPrice);
            
            const profit = isWin ? orderData.amount * orderData.profit / 100 : -orderData.amount;
            const actualTime = Math.round((getServerTime() - orderData.startTime) / 1000);
            
            // 更新订单状态
            orderData.status = 'completed';
            orderData.closePrice = currentPrice;
            orderData.profit = profit;
            orderData.isWin = isWin;
            
            console.log('✅ 订单已结算:', orderData);
            
            // 这里应该调用API结算订单
            // settleOrderAPI(orderData);
            
            document.getElementById('resultOrderId').textContent = orderData.id;
            document.getElementById('resultOpenPrice').textContent = '$' + openPrice.toLocaleString();
            document.getElementById('resultClosePrice').textContent = '$' + currentPrice.toLocaleString();
            document.getElementById('resultChange').textContent = (currentPrice > openPrice ? '+' : '') + 
                '$' + (currentPrice - openPrice).toFixed(2);
            document.getElementById('resultChange').className = currentPrice > openPrice ? 'result-win' : 'result-lose';
            document.getElementById('actualTime').textContent = actualTime + '秒';
            
            document.getElementById('resultDisplay').textContent = (profit > 0 ? '+' : '') + profit.toFixed(2) + ' USDT';
            document.getElementById('resultDisplay').className = profit > 0 ? 'result-win' : 'result-lose';
            
            // 隐藏倒计时界面，显示结果
            closeModal();
            document.getElementById('resultModal').classList.add('show');
        }

        // 隐藏倒计时界面（订单继续执行）
        function hideCountdownModal() {
            isCountdownHidden = true;
            document.getElementById('countdownModal').classList.remove('show');
            console.log('⚠️ 用户隐藏倒计时界面，订单继续执行:', orderData.id);
            
            // 显示提示信息
            showNotification('倒计时界面已隐藏，订单继续执行中...', 'info');
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 1001;
                background: ${type === 'info' ? '#007bff' : '#28a745'}; color: white;
                padding: 15px 20px; border-radius: 5px; font-size: 14px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            `;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // 模拟价格更新
        function updatePrice() {
            const currentPrice = parseFloat(document.getElementById('currentPrice').textContent.replace('$', '').replace(',', ''));
            const change = (Math.random() - 0.5) * 50;
            const newPrice = Math.max(1000, currentPrice + change);
            
            document.getElementById('currentPrice').textContent = '$' + newPrice.toLocaleString();
            if (!isCountdownHidden) {
                document.getElementById('countdownPrice').textContent = '$' + newPrice.toLocaleString();
            }
            
            // 更新涨跌显示
            const changePercent = ((newPrice - 45230.50) / 45230.50 * 100).toFixed(2);
            const changeAmount = (newPrice - 45230.50).toFixed(2);
            const changeText = (changeAmount > 0 ? '+' : '') + changePercent + '% (' + 
                              (changeAmount > 0 ? '+' : '') + '$' + changeAmount + ')';
            
            document.getElementById('priceChange').textContent = changeText;
            document.getElementById('priceChange').style.color = changeAmount > 0 ? '#2ebd85' : '#f5465c';
        }

        // 关闭弹窗
        function closeModal() {
            document.querySelectorAll('.modal').forEach(modal => {
                modal.classList.remove('show');
            });
        }

        // 切换订单标签
        function switchOrderTab(tab) {
            document.querySelectorAll('.order-tab').forEach(t => t.classList.remove('active'));
            event.target.classList.add('active');
            loadOrders(tab);
        }

        // 加载订单数据
        function loadOrders(tab) {
            const orderList = document.getElementById('orderList');
            if (tab === 'current') {
                orderList.innerHTML = '<div style="text-align: center; color: #999; padding: 20px;">暂无当前委托</div>';
            } else {
                orderList.innerHTML = '<div style="text-align: center; color: #999; padding: 20px;">暂无历史记录</div>';
            }
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                if (document.getElementById('countdownModal').classList.contains('show')) {
                    hideCountdownModal();
                } else {
                    closeModal();
                }
            }
        });

        // 页面可见性变化处理
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden && countdownTimer) {
                syncServerTime();
                console.log('页面重新显示，重新同步时间');
            }
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadOrders('current');
            syncServerTime();
            
            // 定期更新价格
            setInterval(updatePrice, 3000);
            
            // 定期同步服务器时间
            setInterval(syncServerTime, 30000);
            
            console.log('秒合约交易系统初始化完成');
        });
    </script>
</body>
</html>
