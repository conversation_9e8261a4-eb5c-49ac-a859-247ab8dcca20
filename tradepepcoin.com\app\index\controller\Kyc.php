<?php
declare (strict_types = 1);

namespace app\index\controller;

use app\BaseController;
use app\common\model\User;
use app\common\model\KycRecord;
use app\common\service\KycService;
use think\facade\View;
use think\facade\Session;
use think\facade\Request;
use think\facade\Validate;

/**
 * KYC实名认证控制器
 */
class Kyc extends BaseController
{
    protected $kycService;

    public function initialize()
    {
        parent::initialize();
        $this->kycService = new KycService();
        
        // 检查登录状态
        if (!Session::has('user_id')) {
            $this->redirect('/auth/login');
        }
    }

    /**
     * KYC认证首页
     */
    public function index()
    {
        $userId = Session::get('user_id');
        
        // 获取用户信息
        $user = User::find($userId);
        
        // 获取KYC记录
        $kycRecord = KycRecord::where('user_id', $userId)
                             ->order('id desc')
                             ->find();
        
        View::assign([
            'user' => $user,
            'kyc_record' => $kycRecord,
            'kyc_status' => $user->kyc_status,
            'title' => '实名认证 - GVD数字货币交易平台'
        ]);
        
        return View::fetch('kyc/index');
    }

    /**
     * 提交KYC认证
     */
    public function submit()
    {
        if (!Request::isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }
        
        $userId = Session::get('user_id');
        $data = Request::post();
        
        // 验证数据
        $validate = Validate::rule([
            'real_name' => 'require|length:2,20',
            'id_card' => 'require|length:15,18',
            'id_card_front' => 'require',
            'id_card_back' => 'require',
            'id_card_hand' => 'require'
        ])->message([
            'real_name.require' => '请输入真实姓名',
            'real_name.length' => '姓名长度2-20位',
            'id_card.require' => '请输入身份证号',
            'id_card.length' => '身份证号格式错误',
            'id_card_front.require' => '请上传身份证正面照片',
            'id_card_back.require' => '请上传身份证反面照片',
            'id_card_hand.require' => '请上传手持身份证照片'
        ]);

        if (!$validate->check($data)) {
            return json(['code' => 0, 'msg' => $validate->getError()]);
        }
        
        // 提交KYC认证
        $result = $this->kycService->submitKyc($userId, $data);
        
        return json($result);
    }

    /**
     * 上传认证图片
     */
    public function uploadImage()
    {
        if (!Request::isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }
        
        $file = Request::file('image');
        if (!$file) {
            return json(['code' => 0, 'msg' => '请选择图片']);
        }
        
        // 验证图片
        $validate = Validate::rule([
            'image' => 'fileSize:5242880|fileExt:jpg,jpeg,png|image:200,200,2000,2000'
        ])->message([
            'image.fileSize' => '图片大小不能超过5MB',
            'image.fileExt' => '只支持jpg、jpeg、png格式',
            'image.image' => '图片尺寸200x200到2000x2000像素'
        ]);

        if (!$validate->check(['image' => $file])) {
            return json(['code' => 0, 'msg' => $validate->getError()]);
        }
        
        // 上传图片
        $result = $this->kycService->uploadImage($file);
        
        return json($result);
    }

    /**
     * 获取KYC状态
     */
    public function status()
    {
        $userId = Session::get('user_id');
        
        $user = User::find($userId);
        $kycRecord = KycRecord::where('user_id', $userId)
                             ->order('id desc')
                             ->find();
        
        return json([
            'code' => 1,
            'data' => [
                'kyc_status' => $user->kyc_status,
                'kyc_status_text' => $user->kyc_status_text,
                'kyc_record' => $kycRecord
            ]
        ]);
    }

    /**
     * KYC认证指南
     */
    public function guide()
    {
        View::assign([
            'title' => '认证指南 - GVD数字货币交易平台'
        ]);
        
        return View::fetch('kyc/guide');
    }
}
