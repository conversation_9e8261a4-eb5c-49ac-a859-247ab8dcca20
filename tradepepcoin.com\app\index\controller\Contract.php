<?php
declare (strict_types = 1);

namespace app\index\controller;

use app\BaseController;
use app\common\model\ContractOrder;
use app\common\model\UserAsset;
use app\common\service\ContractService;
use think\facade\View;
use think\facade\Session;
use think\facade\Request;
use think\facade\Validate;

/**
 * 合约交易控制器
 */
class Contract extends BaseController
{
    protected $contractService;

    public function initialize()
    {
        parent::initialize();
        $this->contractService = new ContractService();
        
        // 检查登录状态
        if (!Session::has('user_id') && Request::isGet()) {
            $this->redirect('/auth/login');
        }
    }

    /**
     * 合约交易首页
     */
    public function index()
    {
        $userId = Session::get('user_id');
        
        // 获取用户USDT余额
        $userAsset = UserAsset::where('user_id', $userId)
                             ->where('coin_symbol', 'USDT')
                             ->find();
        
        // 获取合约配置
        $contractConfig = $this->contractService->getContractConfig();
        
        // 获取用户进行中的合约
        $activeContracts = ContractOrder::where('user_id', $userId)
                                       ->where('status', 1)
                                       ->order('id desc')
                                       ->limit(10)
                                       ->select();
        
        View::assign([
            'user_balance' => $userAsset ? $userAsset->available : 0,
            'contract_config' => $contractConfig,
            'active_contracts' => $activeContracts,
            'title' => '合约交易 - GVD数字货币交易平台'
        ]);
        
        return View::fetch('contract/index');
    }

    /**
     * 创建合约订单
     */
    public function createOrder()
    {
        if (!Request::isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }
        
        $data = Request::post();
        $userId = Session::get('user_id');
        
        // 验证数据
        $validate = Validate::rule([
            'coin_name' => 'require',
            'direction' => 'require|in:1,2', // 1买涨 2买跌
            'amount' => 'require|float|gt:0',
            'time' => 'require|in:1,3,5,10,15,30' // 合约时长(分钟)
        ])->message([
            'coin_name.require' => '请选择交易币种',
            'direction.require' => '请选择交易方向',
            'direction.in' => '交易方向参数错误',
            'amount.require' => '请输入交易金额',
            'amount.float' => '交易金额格式错误',
            'amount.gt' => '交易金额必须大于0',
            'time.require' => '请选择合约时长',
            'time.in' => '合约时长参数错误'
        ]);

        if (!$validate->check($data)) {
            return json(['code' => 0, 'msg' => $validate->getError()]);
        }
        
        // 创建合约订单
        $result = $this->contractService->createOrder($userId, $data);
        
        return json($result);
    }

    /**
     * 获取合约历史
     */
    public function history()
    {
        $userId = Session::get('user_id');
        $page = Request::get('page', 1);
        $limit = 20;
        
        $contracts = ContractOrder::where('user_id', $userId)
                                 ->order('id desc')
                                 ->paginate([
                                     'list_rows' => $limit,
                                     'page' => $page
                                 ]);
        
        if (Request::isAjax()) {
            return json([
                'code' => 1,
                'data' => $contracts->items(),
                'total' => $contracts->total()
            ]);
        }
        
        View::assign([
            'contracts' => $contracts,
            'title' => '合约历史 - GVD数字货币交易平台'
        ]);
        
        return View::fetch('contract/history');
    }

    /**
     * 获取合约详情
     */
    public function detail()
    {
        $id = Request::get('id');
        $userId = Session::get('user_id');
        
        $contract = ContractOrder::where('id', $id)
                                ->where('user_id', $userId)
                                ->find();
        
        if (!$contract) {
            return json(['code' => 0, 'msg' => '合约不存在']);
        }
        
        return json([
            'code' => 1,
            'data' => $contract
        ]);
    }

    /**
     * 获取实时价格
     */
    public function getPrice()
    {
        $coinName = Request::get('coin_name', 'BTC');
        
        $price = $this->contractService->getRealTimePrice($coinName);
        
        return json([
            'code' => 1,
            'data' => [
                'coin_name' => $coinName,
                'price' => $price,
                'timestamp' => time()
            ]
        ]);
    }

    /**
     * 获取合约倒计时信息
     */
    public function getContractTimer()
    {
        $id = Request::get('id');
        $userId = Session::get('user_id');
        
        $contract = ContractOrder::where('id', $id)
                                ->where('user_id', $userId)
                                ->where('status', 1)
                                ->find();
        
        if (!$contract) {
            return json(['code' => 0, 'msg' => '合约不存在或已结束']);
        }
        
        $result = $this->contractService->getContractTimer($contract);
        
        return json($result);
    }

    /**
     * 合约结算（定时任务调用）
     */
    public function settle()
    {
        // 这个方法通常由定时任务调用
        $result = $this->contractService->settleExpiredContracts();
        
        return json($result);
    }
}
