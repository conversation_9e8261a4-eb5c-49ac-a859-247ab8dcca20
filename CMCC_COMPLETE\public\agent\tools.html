<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推广工具 - GVD代理商后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
        }

        .nav-menu {
            display: flex;
            gap: 30px;
        }

        .nav-menu a {
            text-decoration: none;
            color: white;
            font-weight: 500;
            transition: opacity 0.3s;
        }

        .nav-menu a:hover {
            opacity: 0.8;
        }

        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 30px;
            color: #333;
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
        }

        .tool-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .tool-header {
            padding: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-align: center;
        }

        .tool-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }

        .tool-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .tool-description {
            opacity: 0.9;
            font-size: 14px;
        }

        .tool-body {
            padding: 25px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-outline {
            background: transparent;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn-outline:hover {
            background: #667eea;
            color: white;
        }

        .btn-success {
            background: #2ecc71;
            color: white;
        }

        .btn-block {
            width: 100%;
        }

        .result-box {
            background: #f8f9fa;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            position: relative;
        }

        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            font-size: 12px;
        }

        .qr-code {
            text-align: center;
            padding: 20px;
        }

        .qr-code canvas {
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        .stats-row {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
        }

        .material-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .material-item {
            background: #f8f9fa;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .material-item:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .material-icon {
            font-size: 32px;
            margin-bottom: 10px;
        }

        .material-name {
            font-weight: 500;
            margin-bottom: 5px;
        }

        .material-size {
            font-size: 12px;
            color: #666;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 10px 15px;
            border-radius: 5px;
            margin-top: 10px;
            display: none;
        }

        @media (max-width: 768px) {
            .tools-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">GVD代理商后台</div>
            <nav class="nav-menu">
                <a href="/agent/dashboard.html">仪表板</a>
                <a href="/agent/team.html">团队管理</a>
                <a href="/agent/commission.html">佣金明细</a>
                <a href="/agent/tools.html">推广工具</a>
            </nav>
            <button class="btn btn-outline" onclick="logout()">退出</button>
        </div>
    </div>

    <div class="container">
        <h1 class="page-title">推广工具</h1>
        
        <div class="tools-grid">
            <!-- 邀请链接生成器 -->
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">🔗</div>
                    <div class="tool-title">邀请链接</div>
                    <div class="tool-description">生成专属邀请链接，追踪推广效果</div>
                </div>
                <div class="tool-body">
                    <div class="form-group">
                        <label for="linkType">链接类型</label>
                        <select class="form-control" id="linkType">
                            <option value="register">注册链接</option>
                            <option value="trade">交易链接</option>
                            <option value="ido">IDO链接</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="linkRemark">备注名称</label>
                        <input type="text" class="form-control" id="linkRemark" placeholder="为这个链接添加备注">
                    </div>
                    <button class="btn btn-primary btn-block" onclick="generateInviteLink()">生成邀请链接</button>
                    
                    <div class="result-box" id="linkResult" style="display: none;">
                        <button class="copy-btn btn btn-outline" onclick="copyToClipboard('generatedLink')">复制</button>
                        <div id="generatedLink"></div>
                    </div>
                    
                    <div class="stats-row">
                        <div class="stat-item">
                            <div class="stat-value" id="linkClicks">0</div>
                            <div class="stat-label">点击次数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="linkRegisters">0</div>
                            <div class="stat-label">注册转化</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="linkConversion">0%</div>
                            <div class="stat-label">转化率</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 二维码生成器 -->
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">📱</div>
                    <div class="tool-title">二维码生成</div>
                    <div class="tool-description">生成推广二维码，方便移动端分享</div>
                </div>
                <div class="tool-body">
                    <div class="form-group">
                        <label for="qrContent">二维码内容</label>
                        <input type="text" class="form-control" id="qrContent" placeholder="输入要生成二维码的链接">
                    </div>
                    <div class="form-group">
                        <label for="qrSize">二维码尺寸</label>
                        <select class="form-control" id="qrSize">
                            <option value="200">200x200</option>
                            <option value="300">300x300</option>
                            <option value="400">400x400</option>
                        </select>
                    </div>
                    <button class="btn btn-primary btn-block" onclick="generateQRCode()">生成二维码</button>
                    
                    <div class="qr-code" id="qrCodeResult" style="display: none;">
                        <canvas id="qrCanvas"></canvas>
                        <div style="margin-top: 15px;">
                            <button class="btn btn-success" onclick="downloadQRCode()">下载二维码</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 邀请码管理 -->
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">🎫</div>
                    <div class="tool-title">邀请码管理</div>
                    <div class="tool-description">创建和管理专属邀请码</div>
                </div>
                <div class="tool-body">
                    <div class="form-group">
                        <label for="inviteCodeType">邀请码类型</label>
                        <select class="form-control" id="inviteCodeType">
                            <option value="normal">普通邀请码</option>
                            <option value="premium">高级邀请码</option>
                            <option value="vip">VIP邀请码</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="codeLimit">使用次数限制</label>
                        <input type="number" class="form-control" id="codeLimit" placeholder="0表示无限制" min="0">
                    </div>
                    <button class="btn btn-primary btn-block" onclick="generateInviteCode()">生成邀请码</button>
                    
                    <div class="result-box" id="codeResult" style="display: none;">
                        <button class="copy-btn btn btn-outline" onclick="copyToClipboard('generatedCode')">复制</button>
                        <div id="generatedCode"></div>
                    </div>
                    
                    <div class="success-message" id="codeSuccess">
                        邀请码生成成功！
                    </div>
                </div>
            </div>

            <!-- 推广素材 -->
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">🎨</div>
                    <div class="tool-title">推广素材</div>
                    <div class="tool-description">下载官方推广图片和文案</div>
                </div>
                <div class="tool-body">
                    <div class="material-list">
                        <div class="material-item" onclick="downloadMaterial('banner1')">
                            <div class="material-icon">🖼️</div>
                            <div class="material-name">横幅广告</div>
                            <div class="material-size">1200x300</div>
                        </div>
                        <div class="material-item" onclick="downloadMaterial('poster1')">
                            <div class="material-icon">📄</div>
                            <div class="material-name">宣传海报</div>
                            <div class="material-size">800x1200</div>
                        </div>
                        <div class="material-item" onclick="downloadMaterial('logo')">
                            <div class="material-icon">🏷️</div>
                            <div class="material-name">品牌Logo</div>
                            <div class="material-size">512x512</div>
                        </div>
                        <div class="material-item" onclick="downloadMaterial('social')">
                            <div class="material-icon">📱</div>
                            <div class="material-name">社交媒体</div>
                            <div class="material-size">1080x1080</div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 20px;">
                        <button class="btn btn-outline btn-block" onclick="downloadAllMaterials()">下载全部素材</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script>
        class PromotionTools {
            constructor() {
                this.token = localStorage.getItem('agent_token');
                this.agentInfo = JSON.parse(localStorage.getItem('agent_info') || '{}');
                
                if (!this.token) {
                    window.location.href = '/agent/login.html';
                    return;
                }
                
                this.init();
            }
            
            async init() {
                await this.loadLinkStats();
            }
            
            async loadLinkStats() {
                try {
                    const response = await this.apiRequest('/api/agent/link-stats');
                    if (response.code === 1) {
                        document.getElementById('linkClicks').textContent = response.data.clicks || 0;
                        document.getElementById('linkRegisters').textContent = response.data.registers || 0;
                        document.getElementById('linkConversion').textContent = (response.data.conversion || 0) + '%';
                    }
                } catch (error) {
                    console.error('加载链接统计失败:', error);
                }
            }
            
            async apiRequest(url, options = {}) {
                const defaultOptions = {
                    headers: {
                        'Authorization': `Bearer ${this.token}`,
                        'Content-Type': 'application/json'
                    }
                };
                
                const response = await fetch(url, { ...defaultOptions, ...options });
                const result = await response.json();
                
                if (response.status === 401) {
                    localStorage.removeItem('agent_token');
                    localStorage.removeItem('agent_info');
                    window.location.href = '/agent/login.html';
                    return;
                }
                
                return result;
            }
        }
        
        async function generateInviteLink() {
            const linkType = document.getElementById('linkType').value;
            const remark = document.getElementById('linkRemark').value;
            
            try {
                const response = await promotionTools.apiRequest('/api/agent/generate-link', {
                    method: 'POST',
                    body: JSON.stringify({
                        type: linkType,
                        remark: remark
                    })
                });
                
                if (response.code === 1) {
                    document.getElementById('generatedLink').textContent = response.data.link;
                    document.getElementById('linkResult').style.display = 'block';
                } else {
                    alert(response.msg || '生成失败');
                }
            } catch (error) {
                alert('网络错误，请稍后重试');
            }
        }
        
        function generateQRCode() {
            const content = document.getElementById('qrContent').value;
            const size = parseInt(document.getElementById('qrSize').value);
            
            if (!content) {
                alert('请输入二维码内容');
                return;
            }
            
            const canvas = document.getElementById('qrCanvas');
            QRCode.toCanvas(canvas, content, {
                width: size,
                height: size,
                margin: 2
            }, function (error) {
                if (error) {
                    alert('生成二维码失败');
                } else {
                    document.getElementById('qrCodeResult').style.display = 'block';
                }
            });
        }
        
        async function generateInviteCode() {
            const type = document.getElementById('inviteCodeType').value;
            const limit = parseInt(document.getElementById('codeLimit').value) || 0;
            
            try {
                const response = await promotionTools.apiRequest('/api/agent/generate-code', {
                    method: 'POST',
                    body: JSON.stringify({
                        type: type,
                        limit: limit
                    })
                });
                
                if (response.code === 1) {
                    document.getElementById('generatedCode').textContent = response.data.code;
                    document.getElementById('codeResult').style.display = 'block';
                    document.getElementById('codeSuccess').style.display = 'block';
                    
                    setTimeout(() => {
                        document.getElementById('codeSuccess').style.display = 'none';
                    }, 3000);
                } else {
                    alert(response.msg || '生成失败');
                }
            } catch (error) {
                alert('网络错误，请稍后重试');
            }
        }
        
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            
            navigator.clipboard.writeText(text).then(() => {
                alert('已复制到剪贴板');
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('已复制到剪贴板');
            });
        }
        
        function downloadQRCode() {
            const canvas = document.getElementById('qrCanvas');
            const link = document.createElement('a');
            link.download = 'qrcode.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        function downloadMaterial(type) {
            // 模拟下载推广素材
            alert(`正在下载${type}素材...`);
        }
        
        function downloadAllMaterials() {
            alert('正在打包下载所有推广素材...');
        }
        
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('agent_token');
                localStorage.removeItem('agent_info');
                window.location.href = '/agent/login.html';
            }
        }
        
        let promotionTools;
        document.addEventListener('DOMContentLoaded', () => {
            promotionTools = new PromotionTools();
        });
    </script>
</body>
</html>
