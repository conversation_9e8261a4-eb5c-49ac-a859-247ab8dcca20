<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 机构账户模型
 */
class InstitutionalAccount extends Model
{
    protected $table = 'ce_institutional_accounts';
    protected $pk = 'id';

    // 设置字段信息
    protected $schema = [
        'id'                        => 'int',
        'account_id'               => 'string',
        'company_name'             => 'string',
        'company_type'             => 'string',
        'registration_number'      => 'string',
        'registration_country'     => 'string',
        'business_license'         => 'string',
        'contact_person'           => 'string',
        'contact_email'            => 'string',
        'contact_phone'            => 'string',
        'address'                  => 'string',
        'aum'                      => 'float',
        'trading_volume_requirement' => 'float',
        'fee_tier'                 => 'string',
        'api_key'                  => 'string',
        'api_secret'               => 'string',
        'status'                   => 'int',
        'reviewer_id'              => 'int',
        'review_notes'             => 'string',
        'reject_reason'            => 'string',
        'approved_at'              => 'datetime',
        'rejected_at'              => 'datetime',
        'created_at'               => 'datetime',
        'updated_at'               => 'datetime',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    /**
     * 关联OTC订单
     */
    public function otcOrders()
    {
        return $this->hasMany(OtcOrder::class, 'account_id', 'account_id');
    }

    /**
     * 关联托管账户
     */
    public function custodyAccounts()
    {
        return $this->hasMany(CustodyAccount::class, 'account_id', 'account_id');
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusTexts = [
            0 => '待审核',
            1 => '活跃',
            2 => '暂停',
            3 => '关闭'
        ];

        return $statusTexts[$data['status']] ?? '未知';
    }

    /**
     * 获取公司类型文本
     */
    public function getCompanyTypeTextAttr($value, $data)
    {
        $typeTexts = [
            'hedge_fund' => '对冲基金',
            'investment_bank' => '投资银行',
            'family_office' => '家族办公室',
            'pension_fund' => '养老基金',
            'insurance' => '保险公司',
            'sovereign_fund' => '主权基金'
        ];

        return $typeTexts[$data['company_type']] ?? $data['company_type'];
    }

    /**
     * 根据账户ID获取账户
     */
    public static function getByAccountId(string $accountId)
    {
        return self::where('account_id', $accountId)->find();
    }

    /**
     * 获取待审核的账户
     */
    public static function getPendingAccounts()
    {
        return self::where('status', 0)->order('created_at', 'asc')->select();
    }

    /**
     * 获取活跃账户
     */
    public static function getActiveAccounts()
    {
        return self::where('status', 1)->select();
    }
}
