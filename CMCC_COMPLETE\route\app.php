<?php
// +----------------------------------------------------------------------
// | 应用路由配置
// +----------------------------------------------------------------------

use think\facade\Route;

// API路由组
Route::group('api', function () {
    
    // 认证相关（无需登录）
    Route::group('auth', function () {
        Route::post('register', 'Auth/register');
        Route::post('login', 'Auth/login');
        Route::post('logout', 'Auth/logout');
        Route::post('send-sms', 'Auth/sendSms');
        Route::post('send-email', 'Auth/sendEmail');
        Route::get('captcha', 'Auth/captcha');
        Route::post('refresh-token', 'Auth/refreshToken');
        Route::post('forgot-password', 'Auth/forgotPassword');
        Route::post('reset-password', 'Auth/resetPassword');
    });

    // 需要登录的API
    Route::group('', function () {
        
        // 用户相关
        Route::group('user', function () {
            Route::get('info', 'User/info');
            Route::post('update', 'User/update');
            Route::post('change-password', 'User/changePassword');
            Route::post('set-pay-password', 'User/setPayPassword');
            Route::post('upload-avatar', 'User/uploadAvatar');
            Route::get('invite-info', 'User/getInviteInfo');
            Route::get('team-info', 'User/getTeamInfo');
            Route::get('login-logs', 'User/getLoginLogs');
        });

        // 钱包相关
        Route::group('wallet', function () {
            Route::get('assets', 'Wallet/getAssets');
            Route::get('balance/:coin', 'Wallet/getBalance');
            Route::post('transfer', 'Wallet/transfer');
            Route::get('transfer-records', 'Wallet/getTransferRecords');
            Route::get('deposit-address/:coin', 'Wallet/getDepositAddress');
            Route::post('withdraw', 'Wallet/withdraw');
            Route::get('withdraw-records', 'Wallet/getWithdrawRecords');
            Route::get('deposit-records', 'Wallet/getDepositRecords');
        });

        // 杠杆交易
        Route::group('leverage', function () {
            Route::get('config', 'Leverage/getConfig');
            Route::post('order', 'Leverage/createOrder');
            Route::post('close-order', 'Leverage/closeOrder');
            Route::get('orders', 'Leverage/getOrders');
            Route::get('order/:id', 'Leverage/getOrder');
            Route::get('positions', 'Leverage/getPositions');
            Route::post('add-margin', 'Leverage/addMargin');
        });

        // 期货交易
        Route::group('futures', function () {
            Route::get('contracts', 'Futures/getContracts');
            Route::get('contract/:symbol', 'Futures/getContract');
            Route::post('order', 'Futures/createOrder');
            Route::post('cancel-order', 'Futures/cancelOrder');
            Route::get('orders', 'Futures/getOrders');
            Route::get('positions', 'Futures/getPositions');
            Route::get('trades', 'Futures/getTrades');
        });

        // 市场数据
        Route::group('market', function () {
            Route::get('ticker/:symbol', 'Market/getTicker');
            Route::get('tickers', 'Market/getTickers');
            Route::get('depth/:symbol', 'Market/getDepth');
            Route::get('trades/:symbol', 'Market/getTrades');
            Route::get('kline/:symbol', 'Market/getKline');
        });

        // K线数据API
        Route::group('kline', function () {
            Route::get('data', 'Kline/data');
            Route::get('ticker24hr', 'Kline/ticker24hr');
            Route::get('realtime', 'Kline/realtime');
            Route::get('depth', 'Kline/depth');
            Route::get('intervals', 'Kline/intervals');
            Route::get('config', 'Kline/config');
            Route::post('simulate', 'Kline/simulate');
            Route::post('init', 'Kline/init');
        });

        // 现货交易API
        Route::group('spot', function () {
            Route::post('order', 'SpotTrading/createOrder');
            Route::post('cancel', 'SpotTrading/cancelOrder');
            Route::get('orders', 'SpotTrading/getUserOrders');
            Route::get('orderbook', 'SpotTrading/getOrderBook');
            Route::get('trades', 'SpotTrading/getLatestTrades');
            Route::get('symbols', 'SpotTrading/getSymbols');
            Route::get('symbol-info', 'SpotTrading/getSymbolInfo');
            Route::get('user-trades', 'SpotTrading/getUserTrades');
            Route::get('stats', 'SpotTrading/getTradeStats');
        });

        // 用户认证API
        Route::group('auth', function () {
            Route::post('register', 'Auth/register');
            Route::post('login', 'Auth/login');
            Route::post('logout', 'Auth/logout');
            Route::get('profile', 'Auth/profile');
            Route::post('update-profile', 'Auth/updateProfile');
            Route::post('change-password', 'Auth/changePassword');
            Route::post('set-pay-password', 'Auth/setPayPassword');
            Route::post('send-code', 'Auth/sendCode');
            Route::get('stats', 'Auth/getStats');
            Route::post('verify-token', 'Auth/verifyToken');
            Route::post('refresh-token', 'Auth/refreshToken');
        });

        // 资产管理API
        Route::group('asset', function () {
            Route::get('list', 'Asset/index');
            Route::get('detail', 'Asset/detail');
            Route::get('deposit-address', 'Asset/getDepositAddress');
            Route::post('deposit', 'Asset/deposit');
            Route::post('withdraw', 'Asset/withdraw');
            Route::post('transfer', 'Asset/transfer');
            Route::get('records', 'Asset/records');
            Route::get('coins', 'Asset/getSupportedCoins');
            Route::post('freeze', 'Asset/freeze');
            Route::post('unfreeze', 'Asset/unfreeze');
        });

        // IDO认购API
        Route::group('ido', function () {
            Route::get('projects', 'Ido/getProjects');
            Route::get('project-detail', 'Ido/getProjectDetail');
            Route::post('purchase', 'Ido/purchase');
            Route::get('my-orders', 'Ido/getMyOrders');
            Route::get('project-orders', 'Ido/getProjectOrders');
            Route::get('hot-projects', 'Ido/getHotProjects');
            Route::get('stats', 'Ido/getStats');
            Route::get('my-stats', 'Ido/getMyStats');
            Route::get('check-project', 'Ido/checkProject');
        });

        // 客服系统API
        Route::group('customer-service', function () {
            Route::post('init', 'CustomerService/init');
            Route::post('send', 'CustomerService/sendMessage');
            Route::get('messages', 'CustomerService/getMessages');
            Route::get('unread-count', 'CustomerService/getUnreadCount');
            Route::post('upload-image', 'CustomerService/uploadImage');
            Route::post('upload-file', 'CustomerService/uploadFile');
        });
    });

// 管理后台路由组
Route::group('admin', function () {
    // 管理员认证
    Route::group('auth', function () {
        Route::post('login', 'admin.Auth/login');
        Route::post('logout', 'admin.Auth/logout');
        Route::get('profile', 'admin.Auth/profile');
        Route::post('change-password', 'admin.Auth/changePassword');
        Route::post('verify-token', 'admin.Auth/verifyToken');
    });

    // 用户管理
    Route::group('user', function () {
        Route::get('list', 'admin.User/index');
        Route::get('detail', 'admin.User/detail');
        Route::post('freeze', 'admin.User/freeze');
        Route::post('reset-password', 'admin.User/resetPassword');
        Route::post('adjust-asset', 'admin.User/adjustAsset');
        Route::get('login-logs', 'admin.User/getLoginLogs');
    });

    // 系统管理
    Route::group('system', function () {
        Route::get('stats', 'admin.System/getStats');
        Route::get('config', 'admin.System/getConfig');
        Route::post('config', 'admin.System/updateConfig');
        Route::get('logs', 'admin.System/getLogs');
    });

        // 客服相关
        Route::group('customer', function () {
            Route::post('session/create', 'Customer/createSession');
            Route::post('message/send', 'Customer/sendMessage');
            Route::get('messages', 'Customer/getMessages');
            Route::get('sessions', 'Customer/getSessions');
            Route::post('message/read', 'Customer/markAsRead');
            Route::post('image/upload', 'Customer/uploadImage');
            Route::get('emojis', 'Customer/getEmojis');
            Route::get('online/status', 'Customer/getOnlineStatus');
        });

    })->middleware('api_auth');

})->prefix('api/')->middleware(['cors', 'rate_limit']);

// 管理后台路由组
Route::group('admin', function () {
    
    // 登录相关（无需认证）
    Route::get('login', 'Login/index');
    Route::post('login', 'Login/login');
    Route::get('captcha', 'Login/captcha');
    
    // 需要认证的管理功能
    Route::group('', function () {
        
        Route::post('logout', 'Login/logout');
        
        // 首页
        Route::get('index', 'Index/index');
        Route::get('dashboard', 'Index/dashboard');
        Route::get('statistics', 'Index/getStatistics');
        
        // 用户管理
        Route::group('user', function () {
            Route::get('index', 'User/index');
            Route::get('list', 'User/getUserList');
            Route::post('create', 'User/create');
            Route::post('update', 'User/update');
            Route::post('delete', 'User/delete');
            Route::post('adjust-balance', 'User/adjustBalance');
            Route::get('detail/:id', 'User/detail');
            Route::post('set-status', 'User/setStatus');
        });
        
        // 交易管理
        Route::group('trading', function () {
            Route::get('orders', 'Trading/getOrders');
            Route::get('order/:id', 'Trading/getOrder');
            Route::post('control-order', 'Trading/controlOrder');
            Route::post('settle-order', 'Trading/settleOrder');
            Route::get('statistics', 'Trading/getStatistics');
        });
        
        // 财务管理
        Route::group('finance', function () {
            Route::get('deposits', 'Finance/getDeposits');
            Route::get('withdraws', 'Finance/getWithdraws');
            Route::post('audit-withdraw', 'Finance/auditWithdraw');
            Route::get('financial-records', 'Finance/getFinancialRecords');
            Route::get('statistics', 'Finance/getStatistics');
        });
        
        // 系统管理
        Route::group('system', function () {
            Route::get('config', 'System/getConfig');
            Route::post('config', 'System/updateConfig');
            Route::get('admins', 'System/getAdmins');
            Route::post('admin', 'System/createAdmin');
            Route::post('admin/:id', 'System/updateAdmin');
            Route::delete('admin/:id', 'System/deleteAdmin');
            Route::get('logs', 'System/getLogs');
            Route::post('backup', 'System/backup');
            Route::post('clear-cache', 'System/clearCache');
        });

        // 客服管理
        Route::group('customer', function () {
            Route::get('sessions', 'Customer/getSessions');
            Route::post('message/send', 'Customer/sendMessage');
            Route::get('messages', 'Customer/getMessages');
            Route::post('message/read', 'Customer/markAsRead');
            Route::post('session/close', 'Customer/closeSession');
            Route::post('session/transfer', 'Customer/transferSession');
            Route::get('quick-replies', 'Customer/getQuickReplies');
            Route::post('quick-reply', 'Customer/addQuickReply');
            Route::put('quick-reply/:id', 'Customer/updateQuickReply');
            Route::delete('quick-reply/:id', 'Customer/deleteQuickReply');
            Route::get('statistics', 'Customer/getStatistics');
        });
        
    })->middleware('admin_auth');
    
})->prefix('admin/');

// 代理后台路由组
Route::group('agent', function () {
    
    // 登录相关（无需认证）
    Route::get('login', 'Login/index');
    Route::post('login', 'Login/login');
    Route::get('captcha', 'Login/captcha');
    
    // 需要认证的代理功能
    Route::group('', function () {
        
        Route::post('logout', 'Login/logout');
        
        // 首页
        Route::get('index', 'Index/index');
        Route::get('dashboard', 'Index/dashboard');
        Route::get('statistics', 'Index/getStatistics');
        
        // 团队管理
        Route::group('team', function () {
            Route::get('users', 'Team/getUsers');
            Route::post('create-user', 'Team/createUser');
            Route::post('adjust-balance', 'Team/adjustBalance');
            Route::get('user/:id', 'Team/getUserDetail');
            Route::get('statistics', 'Team/getStatistics');
        });
        
        // 订单管理
        Route::group('order', function () {
            Route::get('list', 'Order/getOrders');
            Route::post('control', 'Order/controlOrder');
            Route::get('statistics', 'Order/getStatistics');
        });
        
        // 财务管理
        Route::group('finance', function () {
            Route::get('commission', 'Finance/getCommission');
            Route::get('team-profit', 'Finance/getTeamProfit');
            Route::get('statistics', 'Finance/getStatistics');
        });

        // 客服管理
        Route::group('customer', function () {
            Route::get('sessions', 'Customer/getSessions');
            Route::post('message/send', 'Customer/sendMessage');
            Route::get('messages', 'Customer/getMessages');
            Route::post('message/read', 'Customer/markAsRead');
            Route::get('quick-replies', 'Customer/getQuickReplies');
            Route::get('customers', 'Customer/getMyCustomers');
            Route::post('contact', 'Customer/contactCustomer');
            Route::get('statistics', 'Customer/getWorkStatistics');
            Route::post('online/status', 'Customer/updateOnlineStatus');
        });
        
    })->middleware('agent_auth');
    
})->prefix('agent/');

// 前端页面路由
Route::get('/', 'index/Index/index');
Route::get('/trade', 'index/Index/trade');
Route::get('/wallet', 'index/Index/wallet');
Route::get('/user', 'index/Index/user');

return [];
