<?php
/**
 * 市场数据WebSocket服务器
 * 用于实时推送K线、深度、成交等数据
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Workerman\Worker;
use Workerman\Timer;
use Workerman\Connection\TcpConnection;

class MarketDataServer
{
    private $worker;
    private $connections = [];
    private $subscriptions = [];
    private $klineData = [];
    private $tickerData = [];
    private $depthData = [];
    
    public function __construct()
    {
        $this->worker = new Worker('websocket://0.0.0.0:9501');
        $this->worker->count = 1;
        $this->worker->name = 'MarketDataServer';
        
        $this->worker->onWorkerStart = [$this, 'onWorkerStart'];
        $this->worker->onConnect = [$this, 'onConnect'];
        $this->worker->onMessage = [$this, 'onMessage'];
        $this->worker->onClose = [$this, 'onClose'];
        $this->worker->onError = [$this, 'onError'];
    }
    
    public function onWorkerStart($worker)
    {
        echo "MarketDataServer started on ws://0.0.0.0:9501\n";
        $this->initializeData();
        $this->setupTimers();
    }
    
    public function onConnect(TcpConnection $connection)
    {
        $connection->id = uniqid();
        $this->connections[$connection->id] = $connection;
        
        echo "Client {$connection->id} connected\n";
        
        $this->sendMessage($connection, [
            'event' => 'connected',
            'data' => [
                'server' => 'GVD Market Data Server',
                'version' => '1.0.0',
                'time' => time() * 1000
            ]
        ]);
    }
    
    public function onMessage(TcpConnection $connection, $data)
    {
        try {
            $message = json_decode($data, true);
            if (!$message) {
                $this->sendError($connection, 'Invalid JSON format');
                return;
            }
            
            $this->handleMessage($connection, $message);
            
        } catch (\Exception $e) {
            $this->sendError($connection, 'Message processing error: ' . $e->getMessage());
        }
    }
    
    public function onClose(TcpConnection $connection)
    {
        $this->unsubscribeAll($connection);
        unset($this->connections[$connection->id]);
        echo "Client {$connection->id} disconnected\n";
    }
    
    public function onError(TcpConnection $connection, $code, $msg)
    {
        echo "Connection error: $code $msg\n";
    }
    
    private function handleMessage(TcpConnection $connection, array $message)
    {
        $method = $message['method'] ?? '';
        $params = $message['params'] ?? [];
        $id = $message['id'] ?? null;
        
        switch ($method) {
            case 'SUBSCRIBE':
                $this->handleSubscribe($connection, $params, $id);
                break;
                
            case 'UNSUBSCRIBE':
                $this->handleUnsubscribe($connection, $params, $id);
                break;
                
            case 'GET_TICKER':
                $this->handleGetTicker($connection, $params, $id);
                break;
                
            case 'GET_DEPTH':
                $this->handleGetDepth($connection, $params, $id);
                break;
                
            case 'GET_KLINE':
                $this->handleGetKline($connection, $params, $id);
                break;
                
            case 'PING':
                $this->sendMessage($connection, [
                    'id' => $id,
                    'result' => 'pong',
                    'time' => time() * 1000
                ]);
                break;
                
            default:
                $this->sendError($connection, 'Unknown method: ' . $method, $id);
        }
    }
    
    private function handleSubscribe(TcpConnection $connection, array $params, $id)
    {
        foreach ($params as $stream) {
            if (!isset($this->subscriptions[$stream])) {
                $this->subscriptions[$stream] = [];
            }
            
            $this->subscriptions[$stream][$connection->id] = $connection;
            $this->sendCurrentData($connection, $stream);
        }
        
        $this->sendMessage($connection, [
            'id' => $id,
            'result' => null
        ]);
    }
    
    private function handleUnsubscribe(TcpConnection $connection, array $params, $id)
    {
        foreach ($params as $stream) {
            if (isset($this->subscriptions[$stream][$connection->id])) {
                unset($this->subscriptions[$stream][$connection->id]);
            }
        }
        
        $this->sendMessage($connection, [
            'id' => $id,
            'result' => null
        ]);
    }
    
    private function handleGetTicker(TcpConnection $connection, array $params, $id)
    {
        $symbol = $params['symbol'] ?? 'BTCUSDT';
        $ticker = $this->tickerData[$symbol] ?? $this->generateTicker($symbol);
        
        $this->sendMessage($connection, [
            'id' => $id,
            'result' => $ticker
        ]);
    }
    
    private function handleGetDepth(TcpConnection $connection, array $params, $id)
    {
        $symbol = $params['symbol'] ?? 'BTCUSDT';
        $limit = $params['limit'] ?? 20;
        
        $depth = $this->depthData[$symbol] ?? $this->generateDepth($symbol, $limit);
        
        $this->sendMessage($connection, [
            'id' => $id,
            'result' => $depth
        ]);
    }
    
    private function handleGetKline(TcpConnection $connection, array $params, $id)
    {
        $symbol = $params['symbol'] ?? 'BTCUSDT';
        $interval = $params['interval'] ?? '1m';
        
        $kline = $this->klineData[$symbol][$interval] ?? $this->generateKline($symbol, $interval);
        
        $this->sendMessage($connection, [
            'id' => $id,
            'result' => $kline
        ]);
    }
    
    private function sendCurrentData(TcpConnection $connection, string $stream)
    {
        $parts = explode('@', $stream);
        if (count($parts) !== 2) {
            return;
        }
        
        $symbol = strtoupper($parts[0]);
        $type = $parts[1];
        
        switch ($type) {
            case 'ticker':
                $data = $this->tickerData[$symbol] ?? $this->generateTicker($symbol);
                break;
                
            case 'depth':
                $data = $this->depthData[$symbol] ?? $this->generateDepth($symbol);
                break;
                
            default:
                if (strpos($type, 'kline_') === 0) {
                    $interval = substr($type, 6);
                    $data = $this->klineData[$symbol][$interval] ?? $this->generateKline($symbol, $interval);
                } else {
                    return;
                }
        }
        
        $this->sendMessage($connection, [
            'stream' => $stream,
            'data' => $data
        ]);
    }
    
    private function initializeData()
    {
        $symbols = ['BTCUSDT', 'ETHUSDT', 'LTCUSDT', 'EOSUSDT', 'XRPUSDT'];
        $intervals = ['1m', '5m', '15m', '1h', '4h', '1d'];
        
        foreach ($symbols as $symbol) {
            $this->tickerData[$symbol] = $this->generateTicker($symbol);
            $this->depthData[$symbol] = $this->generateDepth($symbol);
            
            foreach ($intervals as $interval) {
                $this->klineData[$symbol][$interval] = $this->generateKline($symbol, $interval);
            }
        }
    }
    
    private function setupTimers()
    {
        Timer::add(1, function() {
            $this->updateTickers();
            $this->broadcastTickers();
        });
        
        Timer::add(2, function() {
            $this->updateDepth();
            $this->broadcastDepth();
        });
        
        Timer::add(60, function() {
            $this->updateKlines();
            $this->broadcastKlines();
        });
    }
    
    private function updateTickers()
    {
        foreach ($this->tickerData as $symbol => &$ticker) {
            $change = (mt_rand(-100, 100) / 10000);
            $newPrice = $ticker['c'] * (1 + $change);
            
            $ticker['c'] = round($newPrice, 2);
            $ticker['h'] = max($ticker['h'], $newPrice);
            $ticker['l'] = min($ticker['l'], $newPrice);
            $ticker['P'] = round((($newPrice - $ticker['o']) / $ticker['o']) * 100, 2);
            $ticker['E'] = time() * 1000;
        }
    }
    
    private function broadcastTickers()
    {
        foreach ($this->tickerData as $symbol => $ticker) {
            $stream = strtolower($symbol) . '@ticker';
            $this->broadcast($stream, $ticker);
        }
    }
    
    private function updateDepth()
    {
        foreach ($this->depthData as $symbol => &$depth) {
            $updateCount = mt_rand(1, 3);
            
            for ($i = 0; $i < $updateCount; $i++) {
                if (!empty($depth['bids'])) {
                    $index = mt_rand(0, count($depth['bids']) - 1);
                    $depth['bids'][$index][1] = mt_rand(1, 100) / 10;
                }
                
                if (!empty($depth['asks'])) {
                    $index = mt_rand(0, count($depth['asks']) - 1);
                    $depth['asks'][$index][1] = mt_rand(1, 100) / 10;
                }
            }
            
            $depth['lastUpdateId'] = time() * 1000;
        }
    }
    
    private function broadcastDepth()
    {
        foreach ($this->depthData as $symbol => $depth) {
            $stream = strtolower($symbol) . '@depth';
            $this->broadcast($stream, $depth);
        }
    }
    
    private function updateKlines()
    {
        foreach ($this->klineData as $symbol => &$intervals) {
            foreach ($intervals as $interval => &$kline) {
                $change = (mt_rand(-50, 50) / 10000);
                $newPrice = $kline['k']['c'] * (1 + $change);
                
                $kline['k']['c'] = round($newPrice, 2);
                $kline['k']['h'] = max($kline['k']['h'], $newPrice);
                $kline['k']['l'] = min($kline['k']['l'], $newPrice);
                $kline['k']['v'] += mt_rand(1, 10) / 10;
                $kline['E'] = time() * 1000;
            }
        }
    }
    
    private function broadcastKlines()
    {
        foreach ($this->klineData as $symbol => $intervals) {
            foreach ($intervals as $interval => $kline) {
                $stream = strtolower($symbol) . '@kline_' . $interval;
                $this->broadcast($stream, $kline);
            }
        }
    }
    
    private function broadcast(string $stream, array $data)
    {
        if (!isset($this->subscriptions[$stream])) {
            return;
        }
        
        $message = [
            'stream' => $stream,
            'data' => $data
        ];
        
        foreach ($this->subscriptions[$stream] as $connectionId => $connection) {
            if ($connection->getStatus() === TcpConnection::STATUS_ESTABLISHED) {
                $this->sendMessage($connection, $message);
            } else {
                unset($this->subscriptions[$stream][$connectionId]);
            }
        }
    }
    
    private function generateTicker(string $symbol): array
    {
        $basePrices = [
            'BTCUSDT' => 45000,
            'ETHUSDT' => 3000,
            'LTCUSDT' => 150,
            'EOSUSDT' => 5,
            'XRPUSDT' => 0.6
        ];
        
        $basePrice = $basePrices[$symbol] ?? 100;
        $open = $basePrice;
        $close = $open * (1 + (mt_rand(-500, 500) / 10000));
        
        return [
            'e' => '24hrTicker',
            'E' => time() * 1000,
            's' => $symbol,
            'p' => round($close - $open, 2),
            'P' => round((($close - $open) / $open) * 100, 2),
            'w' => round(($open + $close) / 2, 2),
            'x' => $open,
            'c' => round($close, 2),
            'Q' => mt_rand(1, 100) / 10,
            'b' => round($close * 0.999, 2),
            'B' => mt_rand(1, 100) / 10,
            'a' => round($close * 1.001, 2),
            'A' => mt_rand(1, 100) / 10,
            'o' => $open,
            'h' => round($close * 1.02, 2),
            'l' => round($close * 0.98, 2),
            'v' => mt_rand(1000, 10000),
            'q' => mt_rand(1000000, 10000000),
            'O' => (time() - 86400) * 1000,
            'C' => time() * 1000,
            'F' => 1,
            'L' => 1000,
            'n' => 1000
        ];
    }
    
    private function generateDepth(string $symbol, int $limit = 20): array
    {
        $basePrices = [
            'BTCUSDT' => 45000,
            'ETHUSDT' => 3000,
            'LTCUSDT' => 150,
            'EOSUSDT' => 5,
            'XRPUSDT' => 0.6
        ];
        
        $basePrice = $basePrices[$symbol] ?? 100;
        $bids = [];
        $asks = [];
        
        for ($i = 1; $i <= $limit; $i++) {
            $price = round($basePrice - ($i * 0.01 * $basePrice), 2);
            $quantity = mt_rand(1, 100) / 10;
            $bids[] = [strval($price), strval($quantity)];
        }
        
        for ($i = 1; $i <= $limit; $i++) {
            $price = round($basePrice + ($i * 0.01 * $basePrice), 2);
            $quantity = mt_rand(1, 100) / 10;
            $asks[] = [strval($price), strval($quantity)];
        }
        
        return [
            'lastUpdateId' => time() * 1000,
            'bids' => $bids,
            'asks' => $asks
        ];
    }
    
    private function generateKline(string $symbol, string $interval): array
    {
        $basePrices = [
            'BTCUSDT' => 45000,
            'ETHUSDT' => 3000,
            'LTCUSDT' => 150,
            'EOSUSDT' => 5,
            'XRPUSDT' => 0.6
        ];
        
        $basePrice = $basePrices[$symbol] ?? 100;
        $open = $basePrice;
        $close = $open * (1 + (mt_rand(-200, 200) / 10000));
        $high = max($open, $close) * (1 + mt_rand(0, 100) / 10000);
        $low = min($open, $close) * (1 - mt_rand(0, 100) / 10000);
        
        $now = time() * 1000;
        
        return [
            'e' => 'kline',
            'E' => $now,
            's' => $symbol,
            'k' => [
                't' => $now - 60000,
                'T' => $now,
                's' => $symbol,
                'i' => $interval,
                'f' => 100,
                'L' => 200,
                'o' => strval(round($open, 2)),
                'c' => strval(round($close, 2)),
                'h' => strval(round($high, 2)),
                'l' => strval(round($low, 2)),
                'v' => strval(mt_rand(100, 1000)),
                'n' => 100,
                'x' => false,
                'q' => strval(mt_rand(10000, 100000)),
                'V' => strval(mt_rand(50, 500)),
                'Q' => strval(mt_rand(5000, 50000))
            ]
        ];
    }
    
    private function sendMessage(TcpConnection $connection, array $message)
    {
        $connection->send(json_encode($message));
    }
    
    private function sendError(TcpConnection $connection, string $message, $id = null)
    {
        $this->sendMessage($connection, [
            'id' => $id,
            'error' => [
                'code' => -1,
                'msg' => $message
            ]
        ]);
    }
    
    private function unsubscribeAll(TcpConnection $connection)
    {
        foreach ($this->subscriptions as $stream => &$connections) {
            unset($connections[$connection->id]);
        }
    }
    
    public function start()
    {
        Worker::runAll();
    }
}

if (php_sapi_name() === 'cli') {
    $server = new MarketDataServer();
    $server->start();
}
