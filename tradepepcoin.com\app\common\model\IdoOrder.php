<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * IDO订单模型
 */
class IdoOrder extends Model
{
    protected $table = 'ce_ido_orders';
    
    // 设置字段信息
    protected $schema = [
        'id'             => 'int',
        'order_id'       => 'string',
        'user_id'        => 'int',
        'project_id'     => 'int',
        'amount'         => 'decimal',
        'price'          => 'decimal',
        'total_payment'  => 'decimal',
        'payment_coin'   => 'string',
        'released_amount'=> 'decimal',
        'status'         => 'int',
        'created_at'     => 'datetime',
        'updated_at'     => 'datetime',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 类型转换
    protected $type = [
        'user_id'         => 'integer',
        'project_id'      => 'integer',
        'amount'          => 'decimal:8',
        'price'           => 'decimal:8',
        'total_payment'   => 'decimal:8',
        'released_amount' => 'decimal:8',
        'status'          => 'integer',
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联项目
     */
    public function project()
    {
        return $this->belongsTo(IdoProject::class, 'project_id');
    }

    /**
     * 状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            1 => '已支付',
            2 => '已取消'
        ];
        return $status[$data['status']] ?? '未知';
    }

    /**
     * 状态颜色
     */
    public function getStatusColorAttr($value, $data)
    {
        $colors = [
            1 => 'success',
            2 => 'secondary'
        ];
        return $colors[$data['status']] ?? 'secondary';
    }

    /**
     * 格式化认购数量
     */
    public function getAmountFormatAttr($value, $data)
    {
        return number_format($data['amount'], 8, '.', '');
    }

    /**
     * 格式化价格
     */
    public function getPriceFormatAttr($value, $data)
    {
        return number_format($data['price'], 8, '.', '');
    }

    /**
     * 格式化支付金额
     */
    public function getTotalPaymentFormatAttr($value, $data)
    {
        return number_format($data['total_payment'], 8, '.', '');
    }

    /**
     * 格式化已释放数量
     */
    public function getReleasedAmountFormatAttr($value, $data)
    {
        return number_format($data['released_amount'], 8, '.', '');
    }

    /**
     * 剩余锁仓数量
     */
    public function getLockedAmountAttr($value, $data)
    {
        return $data['amount'] - $data['released_amount'];
    }

    /**
     * 格式化剩余锁仓数量
     */
    public function getLockedAmountFormatAttr($value, $data)
    {
        $lockedAmount = $this->locked_amount;
        return number_format($lockedAmount, 8, '.', '');
    }

    /**
     * 释放进度百分比
     */
    public function getReleaseProgressAttr($value, $data)
    {
        if ($data['amount'] <= 0) {
            return 0;
        }
        return round($data['released_amount'] / $data['amount'] * 100, 2);
    }

    /**
     * 是否完全释放
     */
    public function getIsFullyReleasedAttr($value, $data)
    {
        return $data['released_amount'] >= $data['amount'];
    }

    /**
     * 获取可释放数量
     */
    public function getReleasableAmountAttr($value, $data)
    {
        if ($data['status'] != 1) {
            return 0;
        }

        $project = $this->project;
        if (!$project) {
            return 0;
        }

        // 计算从认购完成到现在的天数
        $purchaseDate = strtotime($data['created_at']);
        $lockEndDate = $purchaseDate + ($project->lock_period * 86400);
        $now = time();

        // 如果还在锁仓期内
        if ($now < $lockEndDate) {
            return 0;
        }

        // 计算已过的释放天数
        $releaseDays = floor(($now - $lockEndDate) / 86400);
        
        // 计算应该释放的总数量
        $shouldReleased = min(
            $data['amount'],
            $data['amount'] * $project->daily_release_rate * $releaseDays
        );

        // 返回可释放数量（应该释放 - 已释放）
        return max(0, $shouldReleased - $data['released_amount']);
    }

    /**
     * 格式化可释放数量
     */
    public function getReleasableAmountFormatAttr($value, $data)
    {
        $releasableAmount = $this->releasable_amount;
        return number_format($releasableAmount, 8, '.', '');
    }

    /**
     * 下次释放时间
     */
    public function getNextReleaseTimeAttr($value, $data)
    {
        if ($data['status'] != 1 || $this->is_fully_released) {
            return null;
        }

        $project = $this->project;
        if (!$project) {
            return null;
        }

        $purchaseDate = strtotime($data['created_at']);
        $lockEndDate = $purchaseDate + ($project->lock_period * 86400);
        $now = time();

        if ($now < $lockEndDate) {
            // 还在锁仓期，返回锁仓结束时间
            return date('Y-m-d H:i:s', $lockEndDate);
        } else {
            // 已过锁仓期，返回明天的释放时间
            return date('Y-m-d 00:00:00', strtotime('+1 day'));
        }
    }

    /**
     * 获取用户统计
     */
    public static function getUserStats(int $userId): array
    {
        $where = ['user_id' => $userId];

        // 总认购订单数
        $totalOrders = self::where($where)->count();

        // 总认购金额
        $totalAmount = self::where($where)->where('status', 1)->sum('total_payment');

        // 总认购数量
        $totalTokens = self::where($where)->where('status', 1)->sum('amount');

        // 总已释放数量
        $totalReleased = self::where($where)->where('status', 1)->sum('released_amount');

        // 总锁仓数量
        $totalLocked = $totalTokens - $totalReleased;

        // 参与项目数
        $projectCount = self::where($where)->where('status', 1)->count('DISTINCT project_id');

        return [
            'total_orders' => $totalOrders,
            'total_amount' => $totalAmount,
            'total_tokens' => $totalTokens,
            'total_released' => $totalReleased,
            'total_locked' => $totalLocked,
            'project_count' => $projectCount
        ];
    }

    /**
     * 获取项目统计
     */
    public static function getProjectStats(int $projectId): array
    {
        $where = ['project_id' => $projectId, 'status' => 1];

        // 参与人数
        $participants = self::where($where)->count('DISTINCT user_id');

        // 已售数量
        $soldAmount = self::where($where)->sum('amount');

        // 筹集金额
        $raisedAmount = self::where($where)->sum('total_payment');

        // 总订单数
        $totalOrders = self::where($where)->count();

        return [
            'participants' => $participants,
            'sold_amount' => $soldAmount,
            'raised_amount' => $raisedAmount,
            'total_orders' => $totalOrders
        ];
    }

    /**
     * 获取释放记录
     */
    public function getReleaseRecords(): array
    {
        // 这里应该关联释放记录表，暂时返回模拟数据
        return [
            [
                'date' => '2024-08-01',
                'amount' => 100.00000000,
                'type' => 'daily_release',
                'description' => '每日释放'
            ],
            [
                'date' => '2024-08-02',
                'amount' => 100.00000000,
                'type' => 'daily_release',
                'description' => '每日释放'
            ]
        ];
    }

    /**
     * 执行释放
     */
    public function release(float $amount): bool
    {
        if ($amount <= 0 || $amount > $this->releasable_amount) {
            return false;
        }

        $this->released_amount += $amount;
        return $this->save();
    }
}
