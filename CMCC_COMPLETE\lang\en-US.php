<?php
/**
 * English Language Pack
 */
return [
    // Common
    'common' => [
        'submit' => 'Submit',
        'cancel' => 'Cancel',
        'confirm' => 'Confirm',
        'delete' => 'Delete',
        'edit' => 'Edit',
        'save' => 'Save',
        'back' => 'Back',
        'next' => 'Next',
        'previous' => 'Previous',
        'loading' => 'Loading...',
        'success' => 'Success',
        'error' => 'Error',
        'warning' => 'Warning',
        'info' => 'Info',
        'search' => 'Search',
        'reset' => 'Reset',
        'refresh' => 'Refresh',
        'close' => 'Close',
        'open' => 'Open',
        'view' => 'View',
        'download' => 'Download',
        'upload' => 'Upload',
        'copy' => 'Copy',
        'share' => 'Share',
        'more' => 'More',
        'all' => 'All',
        'none' => 'None',
        'yes' => 'Yes',
        'no' => 'No',
        'enable' => 'Enable',
        'disable' => 'Disable',
        'active' => 'Active',
        'inactive' => 'Inactive',
        'online' => 'Online',
        'offline' => 'Offline',
        'total' => 'Total',
        'amount' => 'Amount',
        'quantity' => 'Quantity',
        'price' => 'Price',
        'time' => 'Time',
        'date' => 'Date',
        'status' => 'Status',
        'type' => 'Type',
        'name' => 'Name',
        'description' => 'Description',
        'remark' => 'Remark',
        'operation' => 'Operation'
    ],

    // Authentication
    'auth' => [
        'login' => 'Login',
        'register' => 'Register',
        'logout' => 'Logout',
        'username' => 'Username',
        'email' => 'Email',
        'password' => 'Password',
        'confirm_password' => 'Confirm Password',
        'phone' => 'Phone',
        'verification_code' => 'Verification Code',
        'invite_code' => 'Invite Code',
        'remember_me' => 'Remember Me',
        'forgot_password' => 'Forgot Password',
        'reset_password' => 'Reset Password',
        'change_password' => 'Change Password',
        'old_password' => 'Old Password',
        'new_password' => 'New Password',
        'login_success' => 'Login successful',
        'login_failed' => 'Login failed',
        'register_success' => 'Registration successful',
        'register_failed' => 'Registration failed',
        'logout_success' => 'Logout successful',
        'password_changed' => 'Password changed successfully',
        'invalid_credentials' => 'Invalid username or password',
        'account_locked' => 'Account is locked',
        'account_not_verified' => 'Account not verified',
        'verification_code_sent' => 'Verification code sent',
        'verification_code_invalid' => 'Invalid verification code',
        'email_exists' => 'Email already exists',
        'username_exists' => 'Username already exists',
        'phone_exists' => 'Phone number already exists',
        'invite_code_invalid' => 'Invalid invite code',
        'password_too_weak' => 'Password is too weak',
        'passwords_not_match' => 'Passwords do not match'
    ],

    // Trading
    'trading' => [
        'spot_trading' => 'Spot Trading',
        'leverage_trading' => 'Leverage Trading',
        'futures_trading' => 'Futures Trading',
        'contract_trading' => 'Contract Trading',
        'buy' => 'Buy',
        'sell' => 'Sell',
        'long' => 'Long',
        'short' => 'Short',
        'market_order' => 'Market Order',
        'limit_order' => 'Limit Order',
        'stop_order' => 'Stop Order',
        'order_book' => 'Order Book',
        'trade_history' => 'Trade History',
        'open_orders' => 'Open Orders',
        'order_history' => 'Order History',
        'balance' => 'Balance',
        'available' => 'Available',
        'frozen' => 'Frozen',
        'total' => 'Total',
        'symbol' => 'Symbol',
        'side' => 'Side',
        'order_type' => 'Order Type',
        'quantity' => 'Quantity',
        'price' => 'Price',
        'amount' => 'Amount',
        'filled' => 'Filled',
        'status' => 'Status',
        'time' => 'Time',
        'fee' => 'Fee',
        'profit_loss' => 'P&L',
        'leverage' => 'Leverage',
        'margin' => 'Margin',
        'position' => 'Position',
        'close_position' => 'Close Position',
        'stop_loss' => 'Stop Loss',
        'take_profit' => 'Take Profit',
        'liquidation_price' => 'Liquidation Price',
        'mark_price' => 'Mark Price',
        'funding_rate' => 'Funding Rate',
        'order_placed' => 'Order placed',
        'order_cancelled' => 'Order cancelled',
        'order_filled' => 'Order filled',
        'insufficient_balance' => 'Insufficient balance',
        'invalid_price' => 'Invalid price',
        'invalid_quantity' => 'Invalid quantity',
        'market_closed' => 'Market closed',
        'trading_suspended' => 'Trading suspended'
    ],

    // Assets
    'asset' => [
        'assets' => 'Assets',
        'wallet' => 'Wallet',
        'deposit' => 'Deposit',
        'withdraw' => 'Withdraw',
        'transfer' => 'Transfer',
        'transaction_history' => 'Transaction History',
        'deposit_history' => 'Deposit History',
        'withdraw_history' => 'Withdraw History',
        'coin' => 'Coin',
        'network' => 'Network',
        'address' => 'Address',
        'tag' => 'Tag',
        'min_deposit' => 'Min Deposit',
        'min_withdraw' => 'Min Withdraw',
        'withdraw_fee' => 'Withdraw Fee',
        'deposit_address' => 'Deposit Address',
        'withdraw_address' => 'Withdraw Address',
        'deposit_amount' => 'Deposit Amount',
        'withdraw_amount' => 'Withdraw Amount',
        'deposit_success' => 'Deposit successful',
        'withdraw_success' => 'Withdraw successful',
        'deposit_pending' => 'Deposit pending',
        'withdraw_pending' => 'Withdraw pending',
        'deposit_failed' => 'Deposit failed',
        'withdraw_failed' => 'Withdraw failed',
        'invalid_address' => 'Invalid address',
        'insufficient_funds' => 'Insufficient funds',
        'withdraw_limit_exceeded' => 'Withdraw limit exceeded',
        'daily_limit_exceeded' => 'Daily limit exceeded',
        'address_whitelist_required' => 'Address whitelist required',
        'two_factor_required' => 'Two-factor authentication required'
    ],

    // IDO
    'ido' => [
        'ido' => 'IDO',
        'initial_dex_offering' => 'Initial DEX Offering',
        'project' => 'Project',
        'token' => 'Token',
        'participate' => 'Participate',
        'purchase' => 'Purchase',
        'allocation' => 'Allocation',
        'progress' => 'Progress',
        'start_time' => 'Start Time',
        'end_time' => 'End Time',
        'token_price' => 'Token Price',
        'target_amount' => 'Target Amount',
        'raised_amount' => 'Raised Amount',
        'min_purchase' => 'Min Purchase',
        'max_purchase' => 'Max Purchase',
        'your_allocation' => 'Your Allocation',
        'purchase_amount' => 'Purchase Amount',
        'token_amount' => 'Token Amount',
        'purchase_success' => 'Purchase successful',
        'purchase_failed' => 'Purchase failed',
        'project_not_started' => 'Project not started',
        'project_ended' => 'Project ended',
        'sold_out' => 'Sold out',
        'insufficient_allocation' => 'Insufficient allocation',
        'purchase_limit_exceeded' => 'Purchase limit exceeded',
        'whitelist_required' => 'Whitelist required',
        'kyc_required' => 'KYC verification required'
    ],

    // Agent
    'agent' => [
        'agent' => 'Agent',
        'referral' => 'Referral',
        'commission' => 'Commission',
        'team' => 'Team',
        'invite' => 'Invite',
        'promotion' => 'Promotion',
        'agent_center' => 'Agent Center',
        'referral_link' => 'Referral Link',
        'invite_code' => 'Invite Code',
        'qr_code' => 'QR Code',
        'team_members' => 'Team Members',
        'direct_referrals' => 'Direct Referrals',
        'total_referrals' => 'Total Referrals',
        'active_referrals' => 'Active Referrals',
        'commission_rate' => 'Commission Rate',
        'total_commission' => 'Total Commission',
        'available_commission' => 'Available Commission',
        'commission_history' => 'Commission History',
        'withdraw_commission' => 'Withdraw Commission',
        'promotion_materials' => 'Promotion Materials',
        'referral_stats' => 'Referral Stats',
        'level' => 'Level',
        'upgrade_requirements' => 'Upgrade Requirements',
        'benefits' => 'Benefits',
        'invite_friends' => 'Invite Friends',
        'share_link' => 'Share Link',
        'copy_link' => 'Copy Link',
        'generate_qr' => 'Generate QR Code',
        'download_materials' => 'Download Materials'
    ],

    // Customer Service
    'customer_service' => [
        'customer_service' => 'Customer Service',
        'online_support' => 'Online Support',
        'help_center' => 'Help Center',
        'contact_us' => 'Contact Us',
        'chat' => 'Chat',
        'message' => 'Message',
        'send_message' => 'Send Message',
        'type_message' => 'Type a message...',
        'attach_file' => 'Attach File',
        'send_image' => 'Send Image',
        'chat_history' => 'Chat History',
        'online' => 'Online',
        'offline' => 'Offline',
        'busy' => 'Busy',
        'away' => 'Away',
        'response_time' => 'Response Time',
        'satisfaction' => 'Satisfaction',
        'rate_service' => 'Rate Service',
        'submit_ticket' => 'Submit Ticket',
        'ticket_number' => 'Ticket Number',
        'ticket_status' => 'Ticket Status',
        'priority' => 'Priority',
        'category' => 'Category',
        'subject' => 'Subject',
        'description' => 'Description',
        'attachment' => 'Attachment',
        'reply' => 'Reply',
        'close_ticket' => 'Close Ticket',
        'reopen_ticket' => 'Reopen Ticket'
    ],

    // System
    'system' => [
        'system' => 'System',
        'settings' => 'Settings',
        'profile' => 'Profile',
        'security' => 'Security',
        'notifications' => 'Notifications',
        'preferences' => 'Preferences',
        'language' => 'Language',
        'timezone' => 'Timezone',
        'theme' => 'Theme',
        'dark_mode' => 'Dark Mode',
        'light_mode' => 'Light Mode',
        'auto_mode' => 'Auto Mode',
        'email_notifications' => 'Email Notifications',
        'sms_notifications' => 'SMS Notifications',
        'push_notifications' => 'Push Notifications',
        'trading_notifications' => 'Trading Notifications',
        'security_notifications' => 'Security Notifications',
        'marketing_notifications' => 'Marketing Notifications',
        'two_factor_auth' => 'Two-Factor Authentication',
        'enable_2fa' => 'Enable 2FA',
        'disable_2fa' => 'Disable 2FA',
        'backup_codes' => 'Backup Codes',
        'api_keys' => 'API Keys',
        'create_api_key' => 'Create API Key',
        'delete_api_key' => 'Delete API Key',
        'permissions' => 'Permissions',
        'ip_whitelist' => 'IP Whitelist',
        'login_history' => 'Login History',
        'device_management' => 'Device Management',
        'session_management' => 'Session Management',
        'privacy_policy' => 'Privacy Policy',
        'terms_of_service' => 'Terms of Service',
        'about' => 'About',
        'version' => 'Version',
        'support' => 'Support',
        'feedback' => 'Feedback',
        'bug_report' => 'Bug Report'
    ],

    // Error Messages
    'errors' => [
        'page_not_found' => 'Page not found',
        'access_denied' => 'Access denied',
        'server_error' => 'Server error',
        'network_error' => 'Network error',
        'timeout' => 'Request timeout',
        'invalid_request' => 'Invalid request',
        'invalid_parameter' => 'Invalid parameter',
        'missing_parameter' => 'Missing parameter',
        'database_error' => 'Database error',
        'file_not_found' => 'File not found',
        'permission_denied' => 'Permission denied',
        'rate_limit_exceeded' => 'Rate limit exceeded',
        'maintenance_mode' => 'System under maintenance',
        'service_unavailable' => 'Service unavailable',
        'unknown_error' => 'Unknown error'
    ],

    // Success Messages
    'success' => [
        'operation_successful' => 'Operation successful',
        'data_saved' => 'Data saved',
        'settings_updated' => 'Settings updated',
        'profile_updated' => 'Profile updated',
        'password_changed' => 'Password changed',
        'email_sent' => 'Email sent',
        'verification_successful' => 'Verification successful',
        'upload_successful' => 'Upload successful',
        'download_successful' => 'Download successful',
        'copy_successful' => 'Copy successful',
        'share_successful' => 'Share successful'
    ],

    // Time Related
    'time' => [
        'now' => 'Now',
        'today' => 'Today',
        'yesterday' => 'Yesterday',
        'tomorrow' => 'Tomorrow',
        'this_week' => 'This Week',
        'last_week' => 'Last Week',
        'next_week' => 'Next Week',
        'this_month' => 'This Month',
        'last_month' => 'Last Month',
        'next_month' => 'Next Month',
        'this_year' => 'This Year',
        'last_year' => 'Last Year',
        'next_year' => 'Next Year',
        'seconds_ago' => '{count} seconds ago',
        'minutes_ago' => '{count} minutes ago',
        'hours_ago' => '{count} hours ago',
        'days_ago' => '{count} days ago',
        'weeks_ago' => '{count} weeks ago',
        'months_ago' => '{count} months ago',
        'years_ago' => '{count} years ago',
        'in_seconds' => 'in {count} seconds',
        'in_minutes' => 'in {count} minutes',
        'in_hours' => 'in {count} hours',
        'in_days' => 'in {count} days',
        'in_weeks' => 'in {count} weeks',
        'in_months' => 'in {count} months',
        'in_years' => 'in {count} years'
    ]
];
