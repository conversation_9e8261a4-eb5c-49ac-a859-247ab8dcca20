<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\common\service\InstitutionalService;
use think\Request;
use think\Response;

/**
 * 机构服务API控制器
 */
class Institutional
{
    protected $institutionalService;

    public function __construct()
    {
        $this->institutionalService = new InstitutionalService();
    }

    /**
     * 创建机构账户
     */
    public function createAccount(Request $request): Response
    {
        $data = $request->post();

        $result = $this->institutionalService->createInstitutionalAccount($data);
        
        return json($result);
    }

    /**
     * 审核机构账户
     */
    public function reviewAccount(Request $request): Response
    {
        $accountId = $request->param('account_id');
        $reviewData = $request->post();

        $result = $this->institutionalService->reviewInstitutionalAccount($accountId, $reviewData);
        
        return json($result);
    }

    /**
     * 创建OTC订单
     */
    public function createOtcOrder(Request $request): Response
    {
        $accountId = $request->param('account_id');
        $data = $request->post();

        $result = $this->institutionalService->createOtcOrder($accountId, $data);
        
        return json($result);
    }

    /**
     * 匹配OTC订单
     */
    public function matchOtcOrder(Request $request): Response
    {
        $orderId = $request->param('order_id');
        $matchData = $request->post();

        $result = $this->institutionalService->matchOtcOrder($orderId, $matchData);
        
        return json($result);
    }

    /**
     * 创建托管账户
     */
    public function createCustodyAccount(Request $request): Response
    {
        $accountId = $request->param('account_id');
        $data = $request->post();

        $result = $this->institutionalService->createCustodyAccount($accountId, $data);
        
        return json($result);
    }

    /**
     * 获取机构账户列表
     */
    public function getAccounts(Request $request): Response
    {
        $filters = $request->get();

        $result = $this->institutionalService->getInstitutionalAccounts($filters);
        
        return json($result);
    }

    /**
     * 获取OTC订单列表
     */
    public function getOtcOrders(Request $request): Response
    {
        $accountId = $request->param('account_id', '');
        $filters = $request->get();

        $result = $this->institutionalService->getOtcOrders($accountId, $filters);
        
        return json($result);
    }

    /**
     * 获取机构交易统计
     */
    public function getStats(Request $request): Response
    {
        $accountId = $request->param('account_id');

        $result = $this->institutionalService->getInstitutionalStats($accountId);
        
        return json($result);
    }
}
