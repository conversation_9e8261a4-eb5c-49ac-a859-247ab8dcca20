<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\common\service\AgentService;
use app\common\service\UserService;
use think\Request;

/**
 * 代理端控制器
 */
class Agent extends BaseController
{
    protected $agentService;
    protected $userService;

    public function __construct()
    {
        parent::__construct();
        $this->agentService = new AgentService();
        $this->userService = new UserService();
    }

    /**
     * 获取代理下级用户列表
     */
    public function getUsers(Request $request)
    {
        $agentId = $this->getUserId();
        $filters = [
            'username' => $request->get('username', ''),
            'email' => $request->get('email', ''),
            'user_type' => $request->get('user_type', ''),
            'status' => $request->get('status', ''),
            'kyc_level' => $request->get('kyc_level', ''),
            'start_date' => $request->get('start_date', ''),
            'end_date' => $request->get('end_date', ''),
            'page' => (int)$request->get('page', 1),
            'limit' => (int)$request->get('limit', 20)
        ];

        $result = $this->agentService->getAgentUsers($agentId, $filters);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取用户详情
     */
    public function getUserDetail(Request $request)
    {
        $userId = (int)$request->param('user_id');
        $agentId = $this->getUserId();
        
        if (!$userId) {
            return $this->error('用户ID不能为空');
        }

        $result = $this->agentService->getAgentUserDetail($agentId, $userId);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取代理统计数据
     */
    public function getStats(Request $request)
    {
        $agentId = $this->getUserId();
        
        $result = $this->agentService->getAgentStats($agentId);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 创建下级用户
     */
    public function createUser(Request $request)
    {
        $data = $request->post();
        $agentId = $this->getUserId();
        
        // 验证必填字段
        $validate = [
            'username' => 'require|max:50',
            'password' => 'require|min:6',
            'email' => 'email'
        ];

        $result = $this->validate($data, $validate);
        if ($result !== true) {
            return $this->error($result);
        }

        $result = $this->agentService->createUser($agentId, $data);
        
        if ($result['code']) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 转换用户类型（仅管理员可用）
     */
    public function changeUserType(Request $request)
    {
        $userId = (int)$request->post('user_id');
        $userType = (int)$request->post('user_type');
        $agentId = $this->getUserId();
        
        if (!$userId || !in_array($userType, [1, 2])) {
            return $this->error('参数错误');
        }

        // 验证是否为管理员
        $agent = \app\common\model\User::find($agentId);
        if (!$agent || $agent->role !== 'admin') {
            return $this->error('只有管理员可以转换用户类型');
        }

        $result = $this->userService->changeUserType($userId, $userType, $agentId);
        
        if ($result['code']) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取代理佣金统计
     */
    public function getCommissionStats(Request $request)
    {
        $agentId = $this->getUserId();
        $startDate = $request->get('start_date', date('Y-m-01'));
        $endDate = $request->get('end_date', date('Y-m-d'));
        
        $result = $this->agentService->getCommissionStats($agentId, $startDate, $endDate);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取代理推广链接
     */
    public function getInviteLink(Request $request)
    {
        $agentId = $this->getUserId();
        
        $inviteCode = base64_encode($agentId . '_' . time());
        $inviteLink = request()->domain() . '/register?invite=' . $inviteCode;
        
        return $this->success([
            'invite_code' => $inviteCode,
            'invite_link' => $inviteLink
        ]);
    }

    /**
     * 获取下级代理列表
     */
    public function getSubAgents(Request $request)
    {
        $agentId = $this->getUserId();
        
        $subAgents = \app\common\model\User::where('agent_id', $agentId)
                                          ->where('role', 'agent')
                                          ->field('id,username,email,created_at,status')
                                          ->select();
        
        return $this->success($subAgents->toArray());
    }

    /**
     * 获取团队业绩
     */
    public function getTeamPerformance(Request $request)
    {
        $agentId = $this->getUserId();
        $period = $request->get('period', 'month'); // today, week, month
        
        $result = $this->agentService->getTeamPerformance($agentId, $period);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取用户交易统计
     */
    public function getUserTradeStats(Request $request)
    {
        $userId = (int)$request->get('user_id');
        $agentId = $this->getUserId();
        
        if (!$userId) {
            return $this->error('用户ID不能为空');
        }

        // 验证权限
        if (!$this->agentService->checkAgentPermission($agentId, $userId)) {
            return $this->error('无权限查看该用户信息');
        }

        $result = $this->agentService->getUserTradeStats($userId);
        
        return $this->success($result);
    }

    /**
     * 获取用户资产统计
     */
    public function getUserAssetStats(Request $request)
    {
        $userId = (int)$request->get('user_id');
        $agentId = $this->getUserId();
        
        if (!$userId) {
            return $this->error('用户ID不能为空');
        }

        // 验证权限
        if (!$this->agentService->checkAgentPermission($agentId, $userId)) {
            return $this->error('无权限查看该用户信息');
        }

        $result = $this->agentService->getUserAssetStats($userId);
        
        return $this->success($result);
    }

    /**
     * 重置用户密码
     */
    public function resetUserPassword(Request $request)
    {
        $userId = (int)$request->post('user_id');
        $newPassword = $request->post('new_password');
        $agentId = $this->getUserId();
        
        if (!$userId || empty($newPassword)) {
            return $this->error('参数不完整');
        }

        // 验证权限
        if (!$this->agentService->checkAgentPermission($agentId, $userId)) {
            return $this->error('无权限操作该用户');
        }

        $result = $this->userService->resetPassword($userId, $newPassword, $agentId);
        
        if ($result['code']) {
            return $this->success([], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 更新用户状态
     */
    public function updateUserStatus(Request $request)
    {
        $userId = (int)$request->post('user_id');
        $status = (int)$request->post('status');
        $agentId = $this->getUserId();
        
        if (!$userId || !in_array($status, [0, 1])) {
            return $this->error('参数错误');
        }

        // 验证权限
        if (!$this->agentService->checkAgentPermission($agentId, $userId)) {
            return $this->error('无权限操作该用户');
        }

        $result = $this->userService->updateUserStatus($userId, $status, $agentId);
        
        if ($result['code']) {
            return $this->success([], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取代理等级信息
     */
    public function getAgentLevel(Request $request)
    {
        $agentId = $this->getUserId();
        
        $agent = \app\common\model\User::find($agentId);
        if (!$agent) {
            return $this->error('代理信息不存在');
        }

        $levelInfo = [
            'level' => $agent->agent_level ?? 1,
            'commission_rate' => $agent->commission_rate ?? 0.1,
            'max_sub_agents' => $agent->max_sub_agents ?? 10,
            'permissions' => [
                'create_user' => true,
                'view_user_detail' => true,
                'reset_password' => true,
                'update_status' => true,
                'view_trade_stats' => true,
                'view_asset_stats' => true
            ]
        ];

        return $this->success($levelInfo);
    }
}
