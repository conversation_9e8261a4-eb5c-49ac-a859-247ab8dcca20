<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提现 - GVD交易平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }

        .header {
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }

        .nav-menu {
            display: flex;
            gap: 30px;
        }

        .nav-menu a {
            text-decoration: none;
            color: #666;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-menu a:hover {
            color: #667eea;
        }

        .back-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
        }

        .container {
            max-width: 800px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 30px;
            color: #333;
        }

        .withdraw-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
        }

        .form-section {
            margin-bottom: 30px;
        }

        .form-section h3 {
            margin-bottom: 20px;
            color: #333;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-group select,
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .balance-info {
            background: #f8f9ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .balance-label {
            color: #666;
            font-size: 14px;
        }

        .balance-amount {
            font-weight: bold;
            color: #667eea;
            font-size: 18px;
        }

        .amount-buttons {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .amount-btn {
            flex: 1;
            padding: 8px;
            background: transparent;
            border: 1px solid #667eea;
            color: #667eea;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .amount-btn:hover {
            background: #667eea;
            color: white;
        }

        .fee-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .fee-info h5 {
            color: #856404;
            margin-bottom: 10px;
        }

        .fee-details {
            color: #856404;
            font-size: 14px;
        }

        .fee-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .withdraw-btn {
            width: 100%;
            padding: 15px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .withdraw-btn:hover {
            background: #5a6fd8;
        }

        .withdraw-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .warning-box {
            background: #fee;
            border: 1px solid #fcc;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .warning-box h5 {
            color: #c33;
            margin-bottom: 10px;
        }

        .warning-box ul {
            color: #c33;
            padding-left: 20px;
        }

        .error-message {
            background: #fee;
            color: #c33;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }

        .success-message {
            background: #efe;
            color: #3c3;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        @media (max-width: 768px) {
            .amount-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">GVD交易平台</div>
            <nav class="nav-menu">
                <a href="/trade/index.html">交易</a>
                <a href="/user/dashboard.html">资产</a>
                <a href="/user/orders.html">订单</a>
            </nav>
            <a href="/user/dashboard.html" class="back-btn">返回资产</a>
        </div>
    </div>

    <div class="container">
        <h1 class="page-title">提现</h1>
        
        <div class="withdraw-container">
            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>
            
            <form id="withdrawForm">
                <div class="form-section">
                    <h3>选择提现币种</h3>
                    <div class="form-group">
                        <label for="coinSelect">币种</label>
                        <select id="coinSelect" required>
                            <option value="">请选择币种</option>
                        </select>
                    </div>
                    
                    <div class="balance-info" id="balanceInfo" style="display: none;">
                        <div class="balance-label">可用余额</div>
                        <div class="balance-amount" id="availableBalance">0.00</div>
                    </div>
                </div>
                
                <div class="form-section">
                    <h3>提现信息</h3>
                    <div class="form-group">
                        <label for="withdrawAddress">提现地址</label>
                        <input type="text" id="withdrawAddress" placeholder="请输入提现地址" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="withdrawAmount">提现数量</label>
                        <input type="number" id="withdrawAmount" placeholder="请输入提现数量" step="0.00000001" required>
                        <div class="amount-buttons">
                            <button type="button" class="amount-btn" onclick="setAmount(25)">25%</button>
                            <button type="button" class="amount-btn" onclick="setAmount(50)">50%</button>
                            <button type="button" class="amount-btn" onclick="setAmount(75)">75%</button>
                            <button type="button" class="amount-btn" onclick="setAmount(100)">全部</button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="payPassword">支付密码</label>
                        <input type="password" id="payPassword" placeholder="请输入6位数字支付密码" required>
                    </div>
                </div>
                
                <div class="fee-info" id="feeInfo" style="display: none;">
                    <h5>手续费信息</h5>
                    <div class="fee-details">
                        <div class="fee-row">
                            <span>提现数量:</span>
                            <span id="withdrawAmountDisplay">0.00</span>
                        </div>
                        <div class="fee-row">
                            <span>网络手续费:</span>
                            <span id="networkFee">0.00</span>
                        </div>
                        <div class="fee-row">
                            <span>实际到账:</span>
                            <span id="actualAmount">0.00</span>
                        </div>
                    </div>
                </div>
                
                <div class="warning-box">
                    <h5>⚠️ 重要提示</h5>
                    <ul>
                        <li>请确保提现地址正确，错误地址将导致资产丢失</li>
                        <li>提现需要网络确认，到账时间可能需要几分钟到几小时</li>
                        <li>最小提现金额：<span id="minWithdraw">10 USDT</span></li>
                        <li>提现申请提交后无法撤销，请仔细核对信息</li>
                    </ul>
                </div>
                
                <button type="submit" class="withdraw-btn" id="withdrawBtn">提交提现申请</button>
            </form>
        </div>
    </div>

    <script>
        class WithdrawPage {
            constructor() {
                this.token = localStorage.getItem('token');
                this.userAssets = [];
                this.selectedCoin = null;
                
                if (!this.token) {
                    window.location.href = '/auth/login.html';
                    return;
                }
                
                this.init();
            }
            
            async init() {
                await this.loadUserAssets();
                this.bindEvents();
            }
            
            bindEvents() {
                document.getElementById('withdrawForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleWithdraw();
                });
                
                document.getElementById('coinSelect').addEventListener('change', (e) => {
                    this.selectCoin(e.target.value);
                });
                
                document.getElementById('withdrawAmount').addEventListener('input', () => {
                    this.calculateFee();
                });
            }
            
            async loadUserAssets() {
                try {
                    const response = await this.apiRequest('/api/asset/list');
                    if (response.code === 1) {
                        this.userAssets = response.data.assets;
                        this.renderCoinOptions();
                    }
                } catch (error) {
                    console.error('加载用户资产失败:', error);
                    this.showError('加载资产失败');
                }
            }
            
            renderCoinOptions() {
                const coinSelect = document.getElementById('coinSelect');
                coinSelect.innerHTML = '<option value="">请选择币种</option>';
                
                this.userAssets.forEach(asset => {
                    if (parseFloat(asset.available) > 0) {
                        const option = document.createElement('option');
                        option.value = asset.coin_symbol;
                        option.textContent = `${asset.coin_symbol} (${asset.coin_name})`;
                        coinSelect.appendChild(option);
                    }
                });
            }
            
            selectCoin(coinSymbol) {
                if (!coinSymbol) {
                    document.getElementById('balanceInfo').style.display = 'none';
                    document.getElementById('feeInfo').style.display = 'none';
                    this.selectedCoin = null;
                    return;
                }
                
                this.selectedCoin = this.userAssets.find(asset => asset.coin_symbol === coinSymbol);
                if (this.selectedCoin) {
                    document.getElementById('availableBalance').textContent = 
                        `${this.selectedCoin.available} ${this.selectedCoin.coin_symbol}`;
                    document.getElementById('balanceInfo').style.display = 'flex';
                    
                    // 更新最小提现金额
                    document.getElementById('minWithdraw').textContent = `10 ${coinSymbol}`;
                    
                    this.calculateFee();
                }
            }
            
            calculateFee() {
                if (!this.selectedCoin) return;
                
                const amount = parseFloat(document.getElementById('withdrawAmount').value) || 0;
                const networkFee = this.getNetworkFee(this.selectedCoin.coin_symbol);
                const actualAmount = Math.max(0, amount - networkFee);
                
                document.getElementById('withdrawAmountDisplay').textContent = 
                    `${amount.toFixed(8)} ${this.selectedCoin.coin_symbol}`;
                document.getElementById('networkFee').textContent = 
                    `${networkFee.toFixed(8)} ${this.selectedCoin.coin_symbol}`;
                document.getElementById('actualAmount').textContent = 
                    `${actualAmount.toFixed(8)} ${this.selectedCoin.coin_symbol}`;
                
                document.getElementById('feeInfo').style.display = amount > 0 ? 'block' : 'none';
            }
            
            getNetworkFee(coinSymbol) {
                const fees = {
                    'USDT': 1,
                    'BTC': 0.0005,
                    'ETH': 0.005,
                    'LTC': 0.001,
                    'EOS': 0.1,
                    'XRP': 0.25
                };
                return fees[coinSymbol] || 0;
            }
            
            async handleWithdraw() {
                const coinSymbol = document.getElementById('coinSelect').value;
                const address = document.getElementById('withdrawAddress').value;
                const amount = parseFloat(document.getElementById('withdrawAmount').value);
                const payPassword = document.getElementById('payPassword').value;
                
                if (!this.validateForm(coinSymbol, address, amount, payPassword)) {
                    return;
                }
                
                this.setLoading(true);
                
                try {
                    const response = await this.apiRequest('/api/asset/withdraw', {
                        method: 'POST',
                        body: JSON.stringify({
                            coin_symbol: coinSymbol,
                            address: address,
                            amount: amount,
                            pay_password: payPassword
                        })
                    });
                    
                    if (response.code === 1) {
                        this.showSuccess('提现申请提交成功，请等待处理');
                        this.clearForm();
                        await this.loadUserAssets();
                    } else {
                        this.showError(response.msg || '提现申请失败');
                    }
                } catch (error) {
                    console.error('提现申请失败:', error);
                    this.showError('网络错误，请稍后重试');
                } finally {
                    this.setLoading(false);
                }
            }
            
            validateForm(coinSymbol, address, amount, payPassword) {
                if (!coinSymbol) {
                    this.showError('请选择提现币种');
                    return false;
                }
                
                if (!address) {
                    this.showError('请输入提现地址');
                    return false;
                }
                
                if (!amount || amount <= 0) {
                    this.showError('请输入有效的提现数量');
                    return false;
                }
                
                if (amount > parseFloat(this.selectedCoin.available)) {
                    this.showError('提现数量超过可用余额');
                    return false;
                }
                
                if (amount < 10) {
                    this.showError('提现数量不能少于最小提现金额');
                    return false;
                }
                
                if (!payPassword || payPassword.length !== 6) {
                    this.showError('请输入6位数字支付密码');
                    return false;
                }
                
                return true;
            }
            
            clearForm() {
                document.getElementById('withdrawForm').reset();
                document.getElementById('balanceInfo').style.display = 'none';
                document.getElementById('feeInfo').style.display = 'none';
                this.selectedCoin = null;
            }
            
            setLoading(loading) {
                const btn = document.getElementById('withdrawBtn');
                btn.disabled = loading;
                btn.textContent = loading ? '提交中...' : '提交提现申请';
            }
            
            showError(message) {
                const errorElement = document.getElementById('errorMessage');
                errorElement.textContent = message;
                errorElement.style.display = 'block';
                document.getElementById('successMessage').style.display = 'none';
            }
            
            showSuccess(message) {
                const successElement = document.getElementById('successMessage');
                successElement.textContent = message;
                successElement.style.display = 'block';
                document.getElementById('errorMessage').style.display = 'none';
            }
            
            async apiRequest(url, options = {}) {
                const defaultOptions = {
                    headers: {
                        'Authorization': `Bearer ${this.token}`,
                        'Content-Type': 'application/json'
                    }
                };
                
                const response = await fetch(url, { ...defaultOptions, ...options });
                const result = await response.json();
                
                if (response.status === 401) {
                    localStorage.removeItem('token');
                    localStorage.removeItem('user_info');
                    window.location.href = '/auth/login.html';
                    return;
                }
                
                return result;
            }
        }
        
        function setAmount(percentage) {
            if (!withdrawPage.selectedCoin) return;
            
            const available = parseFloat(withdrawPage.selectedCoin.available);
            const amount = (available * percentage / 100).toFixed(8);
            document.getElementById('withdrawAmount').value = amount;
            withdrawPage.calculateFee();
        }
        
        let withdrawPage;
        document.addEventListener('DOMContentLoaded', () => {
            withdrawPage = new WithdrawPage();
        });
    </script>
</body>
</html>
