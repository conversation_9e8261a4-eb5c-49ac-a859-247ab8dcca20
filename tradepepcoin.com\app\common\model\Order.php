<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 订单模型
 */
class Order extends Model
{
    protected $table = 'ce_orders';
    
    // 设置字段信息 - 兼容老系统字段
    protected $schema = [
        'id'            => 'int',
        'order_id'      => 'string',
        'user_id'       => 'int',
        'username'      => 'string',
        'symbol'        => 'string',
        'coin'          => 'string',
        'type'          => 'int',
        'order_type'    => 'int',
        'price'         => 'decimal',
        'amount'        => 'decimal',
        'total'         => 'decimal',
        'filled_amount' => 'decimal',
        'filled_total'  => 'decimal',
        'avg_price'     => 'decimal',
        'fee'           => 'decimal',
        'fee_rate'      => 'decimal',
        'fee_coin'      => 'string',
        'status'        => 'int',
        'trade_time'    => 'datetime',
        'created_at'    => 'datetime',
        'updated_at'    => 'datetime',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 类型转换
    protected $type = [
        'user_id'       => 'integer',
        'type'          => 'integer',
        'order_type'    => 'integer',
        'price'         => 'decimal:8',
        'amount'        => 'decimal:8',
        'total'         => 'decimal:8',
        'filled_amount' => 'decimal:8',
        'filled_total'  => 'decimal:8',
        'avg_price'     => 'decimal:8',
        'fee'           => 'decimal:8',
        'fee_rate'      => 'decimal:4',
        'status'        => 'integer',
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联交易对
     */
    public function tradingPair()
    {
        return $this->belongsTo(TradingPair::class, 'symbol', 'symbol');
    }

    /**
     * 关联成交记录
     */
    public function trades()
    {
        return $this->hasMany(Trade::class, 'order_id', 'order_id');
    }

    /**
     * 订单类型文本 - 兼容老系统
     */
    public function getTypeTextAttr($value, $data)
    {
        $types = [1 => '买入', 2 => '卖出'];
        return $types[$data['type']] ?? '未知';
    }

    /**
     * 订单类型颜色 - 兼容老系统
     */
    public function getTypeColorAttr($value, $data)
    {
        return $data['type'] == 1 ? 'success' : 'danger';
    }

    /**
     * 订单方式文本 - 兼容老系统
     */
    public function getOrderTypeTextAttr($value, $data)
    {
        $types = [1 => '限价', 2 => '市价'];
        return $types[$data['order_type']] ?? '未知';
    }

    /**
     * 订单状态文本 - 兼容老系统
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            1 => '委托中',
            2 => '已成交',
            3 => '已撤销',
            4 => '部分成交',
            5 => '失败'
        ];
        return $status[$data['status']] ?? '未知';
    }

    /**
     * 状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            0 => '待成交',
            1 => '部分成交',
            2 => '完全成交',
            3 => '已取消',
            4 => '已拒绝'
        ];
        return $status[$data['status']] ?? '未知';
    }

    /**
     * 状态颜色
     */
    public function getStatusColorAttr($value, $data)
    {
        $colors = [
            0 => 'warning',
            1 => 'info',
            2 => 'success',
            3 => 'secondary',
            4 => 'danger'
        ];
        return $colors[$data['status']] ?? 'secondary';
    }

    /**
     * 格式化价格
     */
    public function getPriceFormatAttr($value, $data)
    {
        return number_format($data['price'], 8, '.', '');
    }

    /**
     * 格式化数量
     */
    public function getAmountFormatAttr($value, $data)
    {
        return number_format($data['amount'], 8, '.', '');
    }

    /**
     * 格式化总额
     */
    public function getTotalFormatAttr($value, $data)
    {
        return number_format($data['total'], 8, '.', '');
    }

    /**
     * 格式化已成交数量
     */
    public function getFilledAmountFormatAttr($value, $data)
    {
        return number_format($data['filled_amount'], 8, '.', '');
    }

    /**
     * 格式化已成交总额
     */
    public function getFilledTotalFormatAttr($value, $data)
    {
        return number_format($data['filled_total'], 8, '.', '');
    }

    /**
     * 格式化平均价格
     */
    public function getAvgPriceFormatAttr($value, $data)
    {
        return number_format($data['avg_price'], 8, '.', '');
    }

    /**
     * 格式化手续费
     */
    public function getFeeFormatAttr($value, $data)
    {
        return number_format($data['fee'], 8, '.', '');
    }

    /**
     * 成交进度百分比
     */
    public function getProgressAttr($value, $data)
    {
        if ($data['amount'] <= 0) {
            return 0;
        }
        return round($data['filled_amount'] / $data['amount'] * 100, 2);
    }

    /**
     * 剩余数量
     */
    public function getRemainingAmountAttr($value, $data)
    {
        return $data['amount'] - $data['filled_amount'];
    }

    /**
     * 剩余数量格式化
     */
    public function getRemainingAmountFormatAttr($value, $data)
    {
        $remaining = $data['amount'] - $data['filled_amount'];
        return number_format($remaining, 8, '.', '');
    }

    /**
     * 是否可以取消
     */
    public function getCanCancelAttr($value, $data)
    {
        return in_array($data['status'], [0, 1]); // 待成交或部分成交
    }

    /**
     * 获取用户订单统计
     */
    public static function getUserStats(int $userId, string $symbol = ''): array
    {
        $where = ['user_id' => $userId];
        if (!empty($symbol)) {
            $where['symbol'] = $symbol;
        }

        // 总订单数
        $totalOrders = self::where($where)->count();

        // 成交订单数
        $filledOrders = self::where($where)->where('status', 2)->count();

        // 取消订单数
        $cancelledOrders = self::where($where)->where('status', 3)->count();

        // 总交易量
        $totalVolume = self::where($where)->where('status', 2)->sum('filled_total');

        // 总手续费
        $totalFee = self::where($where)->where('status', 2)->sum('fee');

        // 成交率
        $fillRate = $totalOrders > 0 ? round($filledOrders / $totalOrders * 100, 2) : 0;

        return [
            'total_orders' => $totalOrders,
            'filled_orders' => $filledOrders,
            'cancelled_orders' => $cancelledOrders,
            'total_volume' => $totalVolume,
            'total_fee' => $totalFee,
            'fill_rate' => $fillRate
        ];
    }

    /**
     * 获取交易对订单统计
     */
    public static function getSymbolStats(string $symbol): array
    {
        $where = ['symbol' => $symbol];

        // 24小时订单数
        $orders24h = self::where($where)
                        ->whereTime('created_at', '>=', date('Y-m-d H:i:s', time() - 86400))
                        ->count();

        // 24小时成交量
        $volume24h = self::where($where)
                        ->where('status', 2)
                        ->whereTime('created_at', '>=', date('Y-m-d H:i:s', time() - 86400))
                        ->sum('filled_total');

        // 24小时成交笔数
        $trades24h = self::where($where)
                        ->where('status', 2)
                        ->whereTime('created_at', '>=', date('Y-m-d H:i:s', time() - 86400))
                        ->count();

        return [
            'orders_24h' => $orders24h,
            'volume_24h' => $volume24h,
            'trades_24h' => $trades24h
        ];
    }
}
