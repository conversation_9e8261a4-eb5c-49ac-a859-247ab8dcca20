/**
 * 国际化多语言支持 JavaScript 库
 */

class I18n {
    constructor() {
        this.currentLanguage = this.detectLanguage();
        this.translations = {};
        this.supportedLanguages = {
            'zh-CN': '简体中文',
            'zh-TW': '繁體中文',
            'en-US': 'English',
            'ja-JP': '日本語',
            'ko-KR': '한국어',
            'es-ES': 'Español',
            'fr-FR': 'Français',
            'de-DE': 'Deutsch',
            'ru-RU': 'Русский',
            'ar-SA': 'العربية'
        };
        this.rtlLanguages = ['ar-SA', 'he-IL', 'fa-IR'];
        
        this.init();
    }

    /**
     * 初始化
     */
    async init() {
        await this.loadTranslations(this.currentLanguage);
        this.applyLanguage();
        this.bindEvents();
    }

    /**
     * 检测用户语言
     */
    detectLanguage() {
        // 1. 从localStorage获取
        const savedLanguage = localStorage.getItem('user_language');
        if (savedLanguage && this.supportedLanguages[savedLanguage]) {
            return savedLanguage;
        }

        // 2. 从浏览器语言获取
        const browserLanguage = navigator.language || navigator.userLanguage;
        
        // 精确匹配
        if (this.supportedLanguages[browserLanguage]) {
            return browserLanguage;
        }

        // 模糊匹配
        const languageCode = browserLanguage.split('-')[0];
        for (const lang in this.supportedLanguages) {
            if (lang.startsWith(languageCode)) {
                return lang;
            }
        }

        // 默认语言
        return 'zh-CN';
    }

    /**
     * 加载翻译文件
     */
    async loadTranslations(language) {
        try {
            const response = await fetch(`/api/i18n/translations/${language}`);
            if (response.ok) {
                this.translations = await response.json();
            } else {
                console.warn(`Failed to load translations for ${language}`);
                // 加载默认语言
                if (language !== 'zh-CN') {
                    await this.loadTranslations('zh-CN');
                }
            }
        } catch (error) {
            console.error('Error loading translations:', error);
        }
    }

    /**
     * 翻译文本
     */
    t(key, params = {}) {
        const keys = key.split('.');
        let value = this.translations;

        for (const k of keys) {
            if (value && typeof value === 'object' && value[k] !== undefined) {
                value = value[k];
            } else {
                return key; // 返回原键名
            }
        }

        if (typeof value !== 'string') {
            return key;
        }

        // 替换参数
        let result = value;
        for (const [param, replacement] of Object.entries(params)) {
            result = result.replace(new RegExp(`{${param}}`, 'g'), replacement);
        }

        return result;
    }

    /**
     * 切换语言
     */
    async changeLanguage(language) {
        if (!this.supportedLanguages[language]) {
            console.error(`Unsupported language: ${language}`);
            return false;
        }

        this.currentLanguage = language;
        localStorage.setItem('user_language', language);
        
        await this.loadTranslations(language);
        this.applyLanguage();
        
        // 通知服务器
        this.notifyServerLanguageChange(language);
        
        // 触发语言变更事件
        window.dispatchEvent(new CustomEvent('languageChanged', {
            detail: { language: language }
        }));

        return true;
    }

    /**
     * 应用语言设置
     */
    applyLanguage() {
        // 设置HTML lang属性
        document.documentElement.lang = this.currentLanguage;
        
        // 设置RTL方向
        if (this.isRTL(this.currentLanguage)) {
            document.documentElement.dir = 'rtl';
            document.body.classList.add('rtl');
        } else {
            document.documentElement.dir = 'ltr';
            document.body.classList.remove('rtl');
        }

        // 翻译页面元素
        this.translatePage();
        
        // 更新语言选择器
        this.updateLanguageSelector();
    }

    /**
     * 翻译页面元素
     */
    translatePage() {
        // 翻译带有data-i18n属性的元素
        document.querySelectorAll('[data-i18n]').forEach(element => {
            const key = element.getAttribute('data-i18n');
            const translation = this.t(key);
            
            if (element.tagName === 'INPUT' && (element.type === 'text' || element.type === 'email' || element.type === 'password')) {
                element.placeholder = translation;
            } else {
                element.textContent = translation;
            }
        });

        // 翻译带有data-i18n-title属性的元素
        document.querySelectorAll('[data-i18n-title]').forEach(element => {
            const key = element.getAttribute('data-i18n-title');
            element.title = this.t(key);
        });

        // 翻译带有data-i18n-placeholder属性的元素
        document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
            const key = element.getAttribute('data-i18n-placeholder');
            element.placeholder = this.t(key);
        });
    }

    /**
     * 更新语言选择器
     */
    updateLanguageSelector() {
        const selectors = document.querySelectorAll('.language-selector');
        selectors.forEach(selector => {
            const currentOption = selector.querySelector(`[data-lang="${this.currentLanguage}"]`);
            if (currentOption) {
                // 更新显示的当前语言
                const display = selector.querySelector('.current-language');
                if (display) {
                    display.textContent = this.supportedLanguages[this.currentLanguage];
                }
                
                // 更新选中状态
                selector.querySelectorAll('[data-lang]').forEach(option => {
                    option.classList.remove('active');
                });
                currentOption.classList.add('active');
            }
        });
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 语言选择器点击事件
        document.addEventListener('click', (e) => {
            const langOption = e.target.closest('[data-lang]');
            if (langOption) {
                const language = langOption.getAttribute('data-lang');
                this.changeLanguage(language);
            }
        });

        // 监听语言变更事件
        window.addEventListener('languageChanged', (e) => {
            console.log('Language changed to:', e.detail.language);
        });
    }

    /**
     * 通知服务器语言变更
     */
    async notifyServerLanguageChange(language) {
        try {
            await fetch('/api/user/language', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({ language })
            });
        } catch (error) {
            console.error('Failed to notify server of language change:', error);
        }
    }

    /**
     * 检查是否为RTL语言
     */
    isRTL(language) {
        return this.rtlLanguages.includes(language);
    }

    /**
     * 格式化数字（本地化）
     */
    formatNumber(number, options = {}) {
        const defaults = {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        };
        
        const formatOptions = { ...defaults, ...options };
        
        try {
            return new Intl.NumberFormat(this.currentLanguage, formatOptions).format(number);
        } catch (error) {
            return number.toString();
        }
    }

    /**
     * 格式化货币（本地化）
     */
    formatCurrency(amount, currency = 'USD', options = {}) {
        const defaults = {
            style: 'currency',
            currency: currency
        };
        
        const formatOptions = { ...defaults, ...options };
        
        try {
            return new Intl.NumberFormat(this.currentLanguage, formatOptions).format(amount);
        } catch (error) {
            return `${currency} ${amount}`;
        }
    }

    /**
     * 格式化日期（本地化）
     */
    formatDate(date, options = {}) {
        const defaults = {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        };
        
        const formatOptions = { ...defaults, ...options };
        
        try {
            return new Intl.DateTimeFormat(this.currentLanguage, formatOptions).format(new Date(date));
        } catch (error) {
            return new Date(date).toLocaleDateString();
        }
    }

    /**
     * 格式化时间（本地化）
     */
    formatTime(date, options = {}) {
        const defaults = {
            hour: '2-digit',
            minute: '2-digit'
        };
        
        const formatOptions = { ...defaults, ...options };
        
        try {
            return new Intl.DateTimeFormat(this.currentLanguage, formatOptions).format(new Date(date));
        } catch (error) {
            return new Date(date).toLocaleTimeString();
        }
    }

    /**
     * 相对时间格式化
     */
    formatRelativeTime(date) {
        const now = new Date();
        const targetDate = new Date(date);
        const diffInSeconds = Math.floor((now - targetDate) / 1000);

        if (diffInSeconds < 60) {
            return this.t('time.now');
        } else if (diffInSeconds < 3600) {
            const minutes = Math.floor(diffInSeconds / 60);
            return this.t('time.minutes_ago', { count: minutes });
        } else if (diffInSeconds < 86400) {
            const hours = Math.floor(diffInSeconds / 3600);
            return this.t('time.hours_ago', { count: hours });
        } else if (diffInSeconds < 2592000) {
            const days = Math.floor(diffInSeconds / 86400);
            return this.t('time.days_ago', { count: days });
        } else {
            return this.formatDate(date);
        }
    }

    /**
     * 创建语言选择器HTML
     */
    createLanguageSelector(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const selectorHTML = `
            <div class="language-selector">
                <div class="language-dropdown">
                    <button class="language-toggle">
                        <span class="current-language">${this.supportedLanguages[this.currentLanguage]}</span>
                        <span class="dropdown-arrow">▼</span>
                    </button>
                    <div class="language-options">
                        ${Object.entries(this.supportedLanguages).map(([code, name]) => `
                            <div class="language-option ${code === this.currentLanguage ? 'active' : ''}" data-lang="${code}">
                                <span class="language-name">${name}</span>
                                <span class="language-code">${code}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = selectorHTML;

        // 添加下拉菜单交互
        const toggle = container.querySelector('.language-toggle');
        const options = container.querySelector('.language-options');

        toggle.addEventListener('click', () => {
            options.classList.toggle('show');
        });

        // 点击外部关闭下拉菜单
        document.addEventListener('click', (e) => {
            if (!container.contains(e.target)) {
                options.classList.remove('show');
            }
        });
    }

    /**
     * 获取当前语言
     */
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    /**
     * 获取支持的语言列表
     */
    getSupportedLanguages() {
        return this.supportedLanguages;
    }
}

// 创建全局实例
window.i18n = new I18n();

// 导出类供模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = I18n;
}
