<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Cache;
use think\facade\Log;
use think\facade\Db;

/**
 * 消息队列服务类
 * 支持Redis队列和数据库队列
 */
class QueueService
{
    // 队列类型
    const DRIVER_REDIS = 'redis';
    const DRIVER_DATABASE = 'database';

    // 队列名称
    const QUEUE_EMAIL = 'email';
    const QUEUE_SMS = 'sms';
    const QUEUE_TRADING = 'trading';
    const QUEUE_NOTIFICATION = 'notification';
    const QUEUE_BLOCKCHAIN = 'blockchain';
    const QUEUE_STATISTICS = 'statistics';
    const QUEUE_RISK_CONTROL = 'risk_control';

    // 任务状态
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_RETRY = 'retry';

    private $driver;
    private $config;

    public function __construct()
    {
        $this->config = config('queue');
        $this->driver = $this->config['default'] ?? self::DRIVER_REDIS;
    }

    /**
     * 推送任务到队列
     */
    public function push(string $queue, string $job, array $data = [], int $delay = 0): array
    {
        try {
            $jobId = $this->generateJobId();
            $jobData = [
                'id' => $jobId,
                'queue' => $queue,
                'job' => $job,
                'data' => $data,
                'attempts' => 0,
                'max_attempts' => $this->config['max_attempts'] ?? 3,
                'delay' => $delay,
                'created_at' => time(),
                'available_at' => time() + $delay,
                'status' => self::STATUS_PENDING
            ];

            $result = $this->pushToQueue($queue, $jobData);

            if ($result) {
                // 记录任务日志
                $this->logJob($jobData, 'pushed');
                
                return [
                    'code' => 1,
                    'msg' => '任务推送成功',
                    'data' => ['job_id' => $jobId]
                ];
            } else {
                return ['code' => 0, 'msg' => '任务推送失败'];
            }

        } catch (\Exception $e) {
            Log::error('队列推送失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '队列推送异常'];
        }
    }

    /**
     * 从队列中获取任务
     */
    public function pop(string $queue): ?array
    {
        try {
            return $this->popFromQueue($queue);
        } catch (\Exception $e) {
            Log::error('队列获取失败：' . $e->getMessage());
            return null;
        }
    }

    /**
     * 批量推送任务
     */
    public function pushBatch(string $queue, array $jobs): array
    {
        $results = [];
        $successCount = 0;
        $failCount = 0;

        foreach ($jobs as $job) {
            $result = $this->push($queue, $job['job'], $job['data'] ?? [], $job['delay'] ?? 0);
            $results[] = $result;
            
            if ($result['code'] === 1) {
                $successCount++;
            } else {
                $failCount++;
            }
        }

        return [
            'code' => $failCount === 0 ? 1 : 0,
            'msg' => "批量推送完成，成功：{$successCount}，失败：{$failCount}",
            'data' => [
                'total' => count($jobs),
                'success' => $successCount,
                'failed' => $failCount,
                'results' => $results
            ]
        ];
    }

    /**
     * 延迟任务
     */
    public function delay(string $queue, string $job, array $data, int $delay): array
    {
        return $this->push($queue, $job, $data, $delay);
    }

    /**
     * 定时任务
     */
    public function schedule(string $queue, string $job, array $data, string $cron): array
    {
        try {
            $nextRunTime = $this->calculateNextRunTime($cron);
            $delay = $nextRunTime - time();

            if ($delay < 0) {
                return ['code' => 0, 'msg' => 'Cron表达式无效'];
            }

            // 添加定时任务标记
            $data['_scheduled'] = true;
            $data['_cron'] = $cron;

            return $this->push($queue, $job, $data, $delay);

        } catch (\Exception $e) {
            Log::error('定时任务设置失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '定时任务设置失败'];
        }
    }

    /**
     * 处理任务
     */
    public function process(array $jobData): array
    {
        try {
            // 更新任务状态为处理中
            $this->updateJobStatus($jobData['id'], self::STATUS_PROCESSING);
            
            // 执行任务
            $result = $this->executeJob($jobData);

            if ($result['success']) {
                // 任务成功
                $this->updateJobStatus($jobData['id'], self::STATUS_COMPLETED);
                $this->logJob($jobData, 'completed', $result['message'] ?? '');
                
                // 如果是定时任务，重新安排下次执行
                if (isset($jobData['data']['_scheduled']) && $jobData['data']['_scheduled']) {
                    $this->rescheduleJob($jobData);
                }

                return ['code' => 1, 'msg' => '任务执行成功'];
            } else {
                // 任务失败，检查是否需要重试
                $jobData['attempts']++;
                
                if ($jobData['attempts'] < $jobData['max_attempts']) {
                    // 重试
                    $retryDelay = $this->calculateRetryDelay($jobData['attempts']);
                    $jobData['available_at'] = time() + $retryDelay;
                    $jobData['status'] = self::STATUS_RETRY;
                    
                    $this->pushToQueue($jobData['queue'], $jobData);
                    $this->logJob($jobData, 'retry', $result['error'] ?? '');
                    
                    return ['code' => 0, 'msg' => '任务失败，已安排重试'];
                } else {
                    // 超过最大重试次数
                    $this->updateJobStatus($jobData['id'], self::STATUS_FAILED);
                    $this->logJob($jobData, 'failed', $result['error'] ?? '');
                    
                    return ['code' => 0, 'msg' => '任务执行失败'];
                }
            }

        } catch (\Exception $e) {
            Log::error('任务处理异常：' . $e->getMessage());
            $this->updateJobStatus($jobData['id'], self::STATUS_FAILED);
            $this->logJob($jobData, 'error', $e->getMessage());
            
            return ['code' => 0, 'msg' => '任务处理异常'];
        }
    }

    /**
     * 获取队列统计信息
     */
    public function getQueueStats(string $queue = ''): array
    {
        try {
            if ($this->driver === self::DRIVER_REDIS) {
                return $this->getRedisQueueStats($queue);
            } else {
                return $this->getDatabaseQueueStats($queue);
            }
        } catch (\Exception $e) {
            Log::error('获取队列统计失败：' . $e->getMessage());
            return [];
        }
    }

    /**
     * 清空队列
     */
    public function clear(string $queue): array
    {
        try {
            if ($this->driver === self::DRIVER_REDIS) {
                $result = $this->clearRedisQueue($queue);
            } else {
                $result = $this->clearDatabaseQueue($queue);
            }

            if ($result) {
                return ['code' => 1, 'msg' => '队列清空成功'];
            } else {
                return ['code' => 0, 'msg' => '队列清空失败'];
            }

        } catch (\Exception $e) {
            Log::error('清空队列失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '清空队列异常'];
        }
    }

    /**
     * 推送到队列（根据驱动）
     */
    private function pushToQueue(string $queue, array $jobData): bool
    {
        if ($this->driver === self::DRIVER_REDIS) {
            return $this->pushToRedisQueue($queue, $jobData);
        } else {
            return $this->pushToDatabaseQueue($queue, $jobData);
        }
    }

    /**
     * 从队列获取（根据驱动）
     */
    private function popFromQueue(string $queue): ?array
    {
        if ($this->driver === self::DRIVER_REDIS) {
            return $this->popFromRedisQueue($queue);
        } else {
            return $this->popFromDatabaseQueue($queue);
        }
    }

    /**
     * Redis队列推送
     */
    private function pushToRedisQueue(string $queue, array $jobData): bool
    {
        $redis = Cache::store('redis')->handler();
        $queueKey = "queue:{$queue}";
        
        if ($jobData['delay'] > 0) {
            // 延迟任务推送到延迟队列
            $delayKey = "queue:delayed:{$queue}";
            return $redis->zadd($delayKey, $jobData['available_at'], json_encode($jobData)) > 0;
        } else {
            // 立即任务推送到普通队列
            return $redis->lpush($queueKey, json_encode($jobData)) > 0;
        }
    }

    /**
     * Redis队列获取
     */
    private function popFromRedisQueue(string $queue): ?array
    {
        $redis = Cache::store('redis')->handler();
        
        // 先检查延迟队列中是否有到期的任务
        $this->moveDelayedJobs($queue);
        
        // 从普通队列获取任务
        $queueKey = "queue:{$queue}";
        $jobJson = $redis->rpop($queueKey);
        
        if ($jobJson) {
            return json_decode($jobJson, true);
        }
        
        return null;
    }

    /**
     * 移动到期的延迟任务
     */
    private function moveDelayedJobs(string $queue): void
    {
        $redis = Cache::store('redis')->handler();
        $delayKey = "queue:delayed:{$queue}";
        $queueKey = "queue:{$queue}";
        $now = time();
        
        // 获取所有到期的任务
        $jobs = $redis->zrangebyscore($delayKey, 0, $now);
        
        foreach ($jobs as $jobJson) {
            // 移动到普通队列
            $redis->lpush($queueKey, $jobJson);
            // 从延迟队列删除
            $redis->zrem($delayKey, $jobJson);
        }
    }

    /**
     * 数据库队列推送
     */
    private function pushToDatabaseQueue(string $queue, array $jobData): bool
    {
        return Db::name('queue_jobs')->insert([
            'id' => $jobData['id'],
            'queue' => $queue,
            'job' => $jobData['job'],
            'data' => json_encode($jobData['data']),
            'attempts' => $jobData['attempts'],
            'max_attempts' => $jobData['max_attempts'],
            'status' => $jobData['status'],
            'available_at' => date('Y-m-d H:i:s', $jobData['available_at']),
            'created_at' => date('Y-m-d H:i:s', $jobData['created_at'])
        ]) > 0;
    }

    /**
     * 数据库队列获取
     */
    private function popFromDatabaseQueue(string $queue): ?array
    {
        $job = Db::name('queue_jobs')
            ->where('queue', $queue)
            ->where('status', self::STATUS_PENDING)
            ->where('available_at', '<=', date('Y-m-d H:i:s'))
            ->order('id asc')
            ->find();

        if ($job) {
            // 标记为处理中
            Db::name('queue_jobs')
                ->where('id', $job['id'])
                ->update(['status' => self::STATUS_PROCESSING]);

            return [
                'id' => $job['id'],
                'queue' => $job['queue'],
                'job' => $job['job'],
                'data' => json_decode($job['data'], true),
                'attempts' => $job['attempts'],
                'max_attempts' => $job['max_attempts']
            ];
        }

        return null;
    }

    /**
     * 执行任务
     */
    private function executeJob(array $jobData): array
    {
        try {
            $jobClass = $jobData['job'];
            
            if (!class_exists($jobClass)) {
                return ['success' => false, 'error' => "任务类不存在：{$jobClass}"];
            }

            $job = new $jobClass();
            
            if (!method_exists($job, 'handle')) {
                return ['success' => false, 'error' => "任务类缺少handle方法：{$jobClass}"];
            }

            $result = $job->handle($jobData['data']);
            
            if ($result === false) {
                return ['success' => false, 'error' => '任务执行返回false'];
            }

            return ['success' => true, 'message' => '任务执行成功'];

        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * 生成任务ID
     */
    private function generateJobId(): string
    {
        return uniqid('job_', true);
    }

    /**
     * 计算重试延迟
     */
    private function calculateRetryDelay(int $attempts): int
    {
        // 指数退避算法
        return min(pow(2, $attempts) * 60, 3600); // 最大1小时
    }

    /**
     * 计算下次运行时间（简化的Cron解析）
     */
    private function calculateNextRunTime(string $cron): int
    {
        // 这里简化实现，实际应该使用专业的Cron解析库
        // 支持简单的格式：分钟 小时 * * *
        $parts = explode(' ', $cron);
        if (count($parts) !== 5) {
            throw new \Exception('Cron表达式格式错误');
        }

        $minute = (int)$parts[0];
        $hour = (int)$parts[1];
        
        $nextRun = mktime($hour, $minute, 0);
        if ($nextRun <= time()) {
            $nextRun += 86400; // 明天同一时间
        }
        
        return $nextRun;
    }

    /**
     * 重新安排定时任务
     */
    private function rescheduleJob(array $jobData): void
    {
        if (isset($jobData['data']['_cron'])) {
            $nextRunTime = $this->calculateNextRunTime($jobData['data']['_cron']);
            $delay = $nextRunTime - time();
            
            $this->push($jobData['queue'], $jobData['job'], $jobData['data'], $delay);
        }
    }

    /**
     * 更新任务状态
     */
    private function updateJobStatus(string $jobId, string $status): void
    {
        if ($this->driver === self::DRIVER_DATABASE) {
            Db::name('queue_jobs')
                ->where('id', $jobId)
                ->update([
                    'status' => $status,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
        }
    }

    /**
     * 记录任务日志
     */
    private function logJob(array $jobData, string $action, string $message = ''): void
    {
        try {
            Db::name('queue_logs')->insert([
                'job_id' => $jobData['id'],
                'queue' => $jobData['queue'],
                'job' => $jobData['job'],
                'action' => $action,
                'message' => $message,
                'attempts' => $jobData['attempts'] ?? 0,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            Log::error('记录队列日志失败：' . $e->getMessage());
        }
    }

    /**
     * 获取Redis队列统计
     */
    private function getRedisQueueStats(string $queue): array
    {
        $redis = Cache::store('redis')->handler();
        
        if ($queue) {
            $queueKey = "queue:{$queue}";
            $delayKey = "queue:delayed:{$queue}";
            
            return [
                'queue' => $queue,
                'pending' => $redis->llen($queueKey),
                'delayed' => $redis->zcard($delayKey)
            ];
        } else {
            // 获取所有队列统计
            $queues = [
                self::QUEUE_EMAIL,
                self::QUEUE_SMS,
                self::QUEUE_TRADING,
                self::QUEUE_NOTIFICATION,
                self::QUEUE_BLOCKCHAIN,
                self::QUEUE_STATISTICS,
                self::QUEUE_RISK_CONTROL
            ];
            
            $stats = [];
            foreach ($queues as $q) {
                $stats[$q] = $this->getRedisQueueStats($q);
            }
            
            return $stats;
        }
    }

    /**
     * 获取数据库队列统计
     */
    private function getDatabaseQueueStats(string $queue): array
    {
        $query = Db::name('queue_jobs');
        
        if ($queue) {
            $query->where('queue', $queue);
        }
        
        $stats = $query->field('status, COUNT(*) as count')
            ->group('status')
            ->select()
            ->toArray();
            
        $result = [];
        foreach ($stats as $stat) {
            $result[$stat['status']] = $stat['count'];
        }
        
        return $result;
    }

    /**
     * 清空Redis队列
     */
    private function clearRedisQueue(string $queue): bool
    {
        $redis = Cache::store('redis')->handler();
        $queueKey = "queue:{$queue}";
        $delayKey = "queue:delayed:{$queue}";
        
        $redis->del($queueKey);
        $redis->del($delayKey);
        
        return true;
    }

    /**
     * 清空数据库队列
     */
    private function clearDatabaseQueue(string $queue): bool
    {
        return Db::name('queue_jobs')->where('queue', $queue)->delete() >= 0;
    }
}
