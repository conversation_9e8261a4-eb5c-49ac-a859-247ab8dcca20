/* GVD代理端样式 */

/* 代理端布局 */
.agent-layout {
    display: flex;
    min-height: 100vh;
    background: var(--primary-bg);
}

.agent-sidebar {
    width: 260px;
    background: var(--card-bg);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: 100;
}

.agent-main {
    flex: 1;
    margin-left: 260px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* 代理端头部 */
.agent-header {
    background: var(--card-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-title h1 {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.header-title p {
    font-size: 14px;
    color: var(--text-secondary);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

/* 在线状态 */
.online-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: var(--secondary-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--color-success);
    position: relative;
}

.status-indicator.online {
    background: var(--color-success);
    animation: pulse-green 2s infinite;
}

.status-indicator.busy {
    background: var(--color-warning);
    animation: pulse-yellow 2s infinite;
}

.status-indicator.offline {
    background: var(--text-muted);
}

@keyframes pulse-green {
    0% {
        box-shadow: 0 0 0 0 rgba(2, 192, 118, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(2, 192, 118, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(2, 192, 118, 0);
    }
}

@keyframes pulse-yellow {
    0% {
        box-shadow: 0 0 0 0 rgba(240, 185, 11, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(240, 185, 11, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(240, 185, 11, 0);
    }
}

#statusSelect {
    border: none;
    background: transparent;
    color: var(--text-primary);
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
}

/* 代理端内容 */
.agent-content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
}

/* 客户卡片 */
.customer-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    transition: var(--transition-normal);
    position: relative;
    cursor: pointer;
}

.customer-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--color-info);
}

.customer-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--gradient-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 18px;
    margin-bottom: 16px;
}

.customer-info {
    margin-bottom: 16px;
}

.customer-name {
    font-weight: 600;
    font-size: 16px;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.customer-email {
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.customer-balance {
    font-size: 14px;
    font-weight: 600;
    color: var(--color-success);
    margin-bottom: 8px;
}

.customer-status {
    font-size: 11px;
    padding: 2px 8px;
    border-radius: 10px;
    font-weight: 500;
    display: inline-block;
}

.customer-status.active {
    background: rgba(2, 192, 118, 0.1);
    color: var(--color-success);
}

.customer-status.inactive {
    background: rgba(132, 142, 156, 0.1);
    color: var(--text-muted);
}

.customer-actions {
    display: flex;
    gap: 8px;
}

.customer-badge {
    position: absolute;
    top: 16px;
    right: 16px;
    background: var(--color-danger);
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: 600;
    min-width: 18px;
    text-align: center;
}

/* 客户网格 */
.customers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
}

.customers-section {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    margin-top: 30px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.section-filters {
    display: flex;
    gap: 12px;
    align-items: center;
}

.section-filters input {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--secondary-bg);
    color: var(--text-primary);
    font-size: 14px;
    width: 200px;
}

.section-filters input:focus {
    outline: none;
    border-color: var(--color-info);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

/* 空状态 */
.empty-customers {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: var(--text-muted);
    text-align: center;
}

.empty-customers .empty-icon {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.3;
}

.empty-customers .empty-text {
    font-size: 16px;
    margin-bottom: 20px;
}

/* 客户信息面板 */
.customer-info-panel {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
}

.customer-profile {
    padding: 20px;
}

.profile-avatar {
    text-align: center;
    margin-bottom: 20px;
}

.avatar-placeholder {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--gradient-primary);
    color: white;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 24px;
}

.profile-info {
    space-y: 12px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
    border-bottom: none;
}

.info-item label {
    font-size: 12px;
    color: var(--text-muted);
    font-weight: 500;
}

.info-item span {
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 500;
}

.customer-actions {
    padding: 16px 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* 快捷操作 */
.quick-actions {
    padding: 16px 20px;
    border-top: 1px solid var(--border-color);
}

.quick-actions h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.action-btn-block {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    background: var(--secondary-bg);
    color: var(--text-primary);
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 8px;
    text-align: left;
}

.action-btn-block:hover {
    background: var(--color-info);
    color: white;
    border-color: var(--color-info);
}

.action-btn-block .icon {
    font-size: 14px;
}

/* 模态框客户列表 */
.modal-customer-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition-fast);
    border: 1px solid transparent;
}

.modal-customer-item:hover {
    background: var(--secondary-bg);
    border-color: var(--color-info);
}

.modal-customer-item .customer-avatar {
    width: 36px;
    height: 36px;
    font-size: 14px;
    margin-bottom: 0;
}

.modal-customer-item .customer-info {
    margin-bottom: 0;
}

.modal-customer-item .customer-name {
    font-size: 14px;
    margin-bottom: 2px;
}

.modal-customer-item .customer-email {
    font-size: 11px;
    margin-bottom: 0;
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 16px 20px;
    max-width: 300px;
    z-index: 10000;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    animation: notificationSlideIn 0.3s ease;
    box-shadow: var(--shadow-lg);
}

.notification.success {
    border-left: 4px solid var(--color-success);
}

.notification.error {
    border-left: 4px solid var(--color-danger);
}

.notification.warning {
    border-left: 4px solid var(--color-warning);
}

.notification.info {
    border-left: 4px solid var(--color-info);
}

.notification-content {
    flex: 1;
}

.notification-message {
    font-size: 14px;
    color: var(--text-primary);
    line-height: 1.4;
}

.notification-close {
    width: 20px;
    height: 20px;
    border: none;
    background: transparent;
    color: var(--text-muted);
    cursor: pointer;
    font-size: 16px;
    line-height: 1;
    flex-shrink: 0;
}

.notification-close:hover {
    color: var(--color-danger);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .customer-interface {
        grid-template-columns: 280px 1fr 240px;
    }
    
    .customers-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .agent-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .agent-sidebar.open {
        transform: translateX(0);
    }
    
    .agent-main {
        margin-left: 0;
    }
    
    .agent-header {
        padding: 16px 20px;
    }
    
    .agent-content {
        padding: 20px;
    }
    
    .customer-interface {
        grid-template-columns: 1fr;
        height: auto;
    }
    
    .sessions-panel,
    .chat-panel,
    .customer-info-panel {
        height: 400px;
    }
    
    .customers-grid {
        grid-template-columns: 1fr;
    }
    
    .section-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .section-filters input {
        width: 100%;
    }
}
