<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\common\service\QuantTradingService;
use think\Request;
use think\Response;

/**
 * 量化交易API控制器
 */
class Quant
{
    protected $quantService;

    public function __construct()
    {
        $this->quantService = new QuantTradingService();
    }

    /**
     * 创建量化策略
     */
    public function createStrategy(Request $request): Response
    {
        $userId = $request->user_id ?? 0;
        $data = $request->post();

        $result = $this->quantService->createStrategy($userId, $data);
        
        return json($result);
    }

    /**
     * 策略回测
     */
    public function backtest(Request $request): Response
    {
        $strategyId = $request->param('strategy_id');
        $config = $request->post();

        $result = $this->quantService->backtest($strategyId, $config);
        
        return json($result);
    }

    /**
     * 启动策略
     */
    public function startStrategy(Request $request): Response
    {
        $strategyId = $request->param('strategy_id');

        $result = $this->quantService->startStrategy($strategyId);
        
        return json($result);
    }

    /**
     * 停止策略
     */
    public function stopStrategy(Request $request): Response
    {
        $strategyId = $request->param('strategy_id');

        $result = $this->quantService->stopStrategy($strategyId);
        
        return json($result);
    }

    /**
     * 获取策略列表
     */
    public function getStrategies(Request $request): Response
    {
        $userId = $request->user_id ?? 0;
        $filters = $request->get();

        $result = $this->quantService->getStrategies($userId, $filters);
        
        return json($result);
    }

    /**
     * 获取策略详情
     */
    public function getStrategyDetail(Request $request): Response
    {
        $strategyId = $request->param('strategy_id');

        $result = $this->quantService->getStrategyDetail($strategyId);
        
        return json($result);
    }

    /**
     * 生成交易信号
     */
    public function generateSignals(Request $request): Response
    {
        $strategyId = $request->param('strategy_id');

        $result = $this->quantService->generateSignals($strategyId);
        
        return json($result);
    }

    /**
     * 执行策略交易
     */
    public function executeStrategy(Request $request): Response
    {
        $strategyId = $request->param('strategy_id');

        $result = $this->quantService->executeStrategy($strategyId);
        
        return json($result);
    }
}
