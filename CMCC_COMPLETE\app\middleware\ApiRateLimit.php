<?php
declare (strict_types = 1);

namespace app\middleware;

use Closure;
use think\Request;
use think\Response;
use think\facade\Cache;
use think\facade\Config;

/**
 * API限流中间件
 */
class ApiRateLimit
{
    /**
     * 处理请求
     *
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 获取客户端IP
        $clientIp = $request->ip();
        
        // 获取限流配置
        $maxRequests = Config::get('app.api_rate_limit', 1000);
        $timeWindow = Config::get('app.api_rate_window', 3600);
        
        // 缓存键
        $cacheKey = 'rate_limit:' . $clientIp;
        
        // 获取当前请求次数
        $currentRequests = Cache::get($cacheKey, 0);
        
        if ($currentRequests >= $maxRequests) {
            return json([
                'code' => 429,
                'msg'  => '请求过于频繁，请稍后再试',
                'data' => [
                    'retry_after' => $timeWindow
                ]
            ])->code(429);
        }
        
        // 增加请求次数
        Cache::set($cacheKey, $currentRequests + 1, $timeWindow);
        
        $response = $next($request);
        
        // 添加限流头信息
        $response->header([
            'X-RateLimit-Limit' => $maxRequests,
            'X-RateLimit-Remaining' => max(0, $maxRequests - $currentRequests - 1),
            'X-RateLimit-Reset' => time() + $timeWindow
        ]);
        
        return $response;
    }
}
