<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Cache;
use think\facade\Log;
use think\facade\Db;

/**
 * 高频交易服务类
 * 优化交易性能，支持毫秒级响应
 */
class HighFrequencyTradingService
{
    // 订单类型
    const ORDER_TYPE_MARKET = 'market';
    const ORDER_TYPE_LIMIT = 'limit';
    const ORDER_TYPE_STOP = 'stop';
    const ORDER_TYPE_IOC = 'ioc'; // Immediate or Cancel
    const ORDER_TYPE_FOK = 'fok'; // Fill or Kill

    // 缓存键前缀
    const CACHE_PREFIX_ORDERBOOK = 'orderbook:';
    const CACHE_PREFIX_TICKER = 'ticker:';
    const CACHE_PREFIX_TRADES = 'trades:';
    const CACHE_PREFIX_USER_ORDERS = 'user_orders:';

    private $redis;
    private $matchingEngine;

    public function __construct()
    {
        $this->redis = Cache::store('redis')->handler();
        $this->matchingEngine = new TradingMatchingEngine();
    }

    /**
     * 高频下单（毫秒级响应）
     */
    public function placeOrderFast(array $orderData): array
    {
        $startTime = microtime(true);
        
        try {
            // 1. 快速验证（内存中验证）
            $validation = $this->fastValidateOrder($orderData);
            if (!$validation['valid']) {
                return [
                    'code' => 0,
                    'msg' => $validation['error'],
                    'latency' => round((microtime(true) - $startTime) * 1000, 2)
                ];
            }

            // 2. 生成订单ID
            $orderId = $this->generateOrderId();
            
            // 3. 预处理订单数据
            $order = $this->prepareOrder($orderId, $orderData);

            // 4. 快速资产检查和冻结
            $assetResult = $this->fastAssetOperation($order);
            if (!$assetResult['success']) {
                return [
                    'code' => 0,
                    'msg' => $assetResult['error'],
                    'latency' => round((microtime(true) - $startTime) * 1000, 2)
                ];
            }

            // 5. 推送到高频撮合队列
            $this->pushToMatchingQueue($order);

            // 6. 缓存订单信息
            $this->cacheOrder($order);

            // 7. 异步处理（数据库写入等）
            $this->asyncProcessOrder($order);

            $latency = round((microtime(true) - $startTime) * 1000, 2);

            return [
                'code' => 1,
                'msg' => '订单提交成功',
                'data' => [
                    'order_id' => $orderId,
                    'symbol' => $order['symbol'],
                    'side' => $order['side'],
                    'type' => $order['type'],
                    'quantity' => $order['quantity'],
                    'price' => $order['price'],
                    'status' => 'pending',
                    'created_at' => $order['created_at']
                ],
                'latency' => $latency
            ];

        } catch (\Exception $e) {
            Log::error('高频下单异常：' . $e->getMessage());
            return [
                'code' => 0,
                'msg' => '系统异常',
                'latency' => round((microtime(true) - $startTime) * 1000, 2)
            ];
        }
    }

    /**
     * 批量下单（高频场景）
     */
    public function batchPlaceOrders(array $orders): array
    {
        $startTime = microtime(true);
        $results = [];
        $successCount = 0;
        $failCount = 0;

        try {
            // 批量验证
            $validatedOrders = [];
            foreach ($orders as $orderData) {
                $validation = $this->fastValidateOrder($orderData);
                if ($validation['valid']) {
                    $orderId = $this->generateOrderId();
                    $validatedOrders[] = $this->prepareOrder($orderId, $orderData);
                } else {
                    $results[] = ['code' => 0, 'msg' => $validation['error']];
                    $failCount++;
                }
            }

            // 批量资产操作
            $assetResults = $this->batchAssetOperation($validatedOrders);

            // 批量推送到撮合队列
            foreach ($validatedOrders as $index => $order) {
                if ($assetResults[$index]['success']) {
                    $this->pushToMatchingQueue($order);
                    $this->cacheOrder($order);
                    $this->asyncProcessOrder($order);
                    
                    $results[] = [
                        'code' => 1,
                        'msg' => '订单提交成功',
                        'data' => ['order_id' => $order['order_id']]
                    ];
                    $successCount++;
                } else {
                    $results[] = [
                        'code' => 0,
                        'msg' => $assetResults[$index]['error']
                    ];
                    $failCount++;
                }
            }

            $latency = round((microtime(true) - $startTime) * 1000, 2);

            return [
                'code' => $failCount === 0 ? 1 : 0,
                'msg' => "批量下单完成，成功：{$successCount}，失败：{$failCount}",
                'data' => [
                    'total' => count($orders),
                    'success' => $successCount,
                    'failed' => $failCount,
                    'results' => $results
                ],
                'latency' => $latency
            ];

        } catch (\Exception $e) {
            Log::error('批量下单异常：' . $e->getMessage());
            return [
                'code' => 0,
                'msg' => '批量下单异常',
                'latency' => round((microtime(true) - $startTime) * 1000, 2)
            ];
        }
    }

    /**
     * 快速撤单
     */
    public function cancelOrderFast(string $orderId, int $userId): array
    {
        $startTime = microtime(true);

        try {
            // 1. 从缓存获取订单信息
            $order = $this->getOrderFromCache($orderId);
            if (!$order) {
                return [
                    'code' => 0,
                    'msg' => '订单不存在',
                    'latency' => round((microtime(true) - $startTime) * 1000, 2)
                ];
            }

            // 2. 验证订单所有权
            if ($order['user_id'] !== $userId) {
                return [
                    'code' => 0,
                    'msg' => '无权限撤销此订单',
                    'latency' => round((microtime(true) - $startTime) * 1000, 2)
                ];
            }

            // 3. 检查订单状态
            if (!in_array($order['status'], ['pending', 'partial'])) {
                return [
                    'code' => 0,
                    'msg' => '订单状态不允许撤销',
                    'latency' => round((microtime(true) - $startTime) * 1000, 2)
                ];
            }

            // 4. 推送撤单请求到撮合队列
            $this->pushCancelToMatchingQueue($orderId, $userId);

            // 5. 更新缓存状态
            $this->updateOrderCacheStatus($orderId, 'cancelling');

            // 6. 异步处理撤单
            $this->asyncProcessCancel($orderId, $userId);

            $latency = round((microtime(true) - $startTime) * 1000, 2);

            return [
                'code' => 1,
                'msg' => '撤单请求提交成功',
                'data' => [
                    'order_id' => $orderId,
                    'status' => 'cancelling'
                ],
                'latency' => $latency
            ];

        } catch (\Exception $e) {
            Log::error('快速撤单异常：' . $e->getMessage());
            return [
                'code' => 0,
                'msg' => '撤单异常',
                'latency' => round((microtime(true) - $startTime) * 1000, 2)
            ];
        }
    }

    /**
     * 获取实时订单簿（高频优化）
     */
    public function getOrderBookFast(string $symbol, int $depth = 20): array
    {
        $startTime = microtime(true);

        try {
            $cacheKey = self::CACHE_PREFIX_ORDERBOOK . $symbol;
            $orderBook = $this->redis->get($cacheKey);

            if ($orderBook) {
                $data = json_decode($orderBook, true);
                
                // 限制深度
                $data['bids'] = array_slice($data['bids'], 0, $depth);
                $data['asks'] = array_slice($data['asks'], 0, $depth);
                
                return [
                    'code' => 1,
                    'data' => $data,
                    'latency' => round((microtime(true) - $startTime) * 1000, 2),
                    'from_cache' => true
                ];
            }

            // 缓存未命中，从撮合引擎获取
            $orderBook = $this->matchingEngine->getOrderBook($symbol, $depth);
            
            // 缓存结果（短时间缓存）
            $this->redis->setex($cacheKey, 1, json_encode($orderBook));

            return [
                'code' => 1,
                'data' => $orderBook,
                'latency' => round((microtime(true) - $startTime) * 1000, 2),
                'from_cache' => false
            ];

        } catch (\Exception $e) {
            Log::error('获取订单簿异常：' . $e->getMessage());
            return [
                'code' => 0,
                'msg' => '获取订单簿失败',
                'latency' => round((microtime(true) - $startTime) * 1000, 2)
            ];
        }
    }

    /**
     * 获取实时行情（高频优化）
     */
    public function getTickerFast(string $symbol): array
    {
        $startTime = microtime(true);

        try {
            $cacheKey = self::CACHE_PREFIX_TICKER . $symbol;
            $ticker = $this->redis->get($cacheKey);

            if ($ticker) {
                return [
                    'code' => 1,
                    'data' => json_decode($ticker, true),
                    'latency' => round((microtime(true) - $startTime) * 1000, 2),
                    'from_cache' => true
                ];
            }

            // 缓存未命中，计算行情
            $ticker = $this->calculateTicker($symbol);
            
            // 缓存结果
            $this->redis->setex($cacheKey, 5, json_encode($ticker));

            return [
                'code' => 1,
                'data' => $ticker,
                'latency' => round((microtime(true) - $startTime) * 1000, 2),
                'from_cache' => false
            ];

        } catch (\Exception $e) {
            Log::error('获取行情异常：' . $e->getMessage());
            return [
                'code' => 0,
                'msg' => '获取行情失败',
                'latency' => round((microtime(true) - $startTime) * 1000, 2)
            ];
        }
    }

    /**
     * 快速验证订单
     */
    private function fastValidateOrder(array $orderData): array
    {
        // 基础字段验证
        $requiredFields = ['user_id', 'symbol', 'side', 'type', 'quantity'];
        foreach ($requiredFields as $field) {
            if (!isset($orderData[$field]) || empty($orderData[$field])) {
                return ['valid' => false, 'error' => "缺少必要字段：{$field}"];
            }
        }

        // 交易对验证（从缓存）
        $symbolInfo = $this->getSymbolInfoFromCache($orderData['symbol']);
        if (!$symbolInfo) {
            return ['valid' => false, 'error' => '交易对不存在或已停用'];
        }

        // 数量验证
        if ($orderData['quantity'] <= 0) {
            return ['valid' => false, 'error' => '数量必须大于0'];
        }

        if ($orderData['quantity'] < $symbolInfo['min_quantity']) {
            return ['valid' => false, 'error' => '数量低于最小限制'];
        }

        // 价格验证（限价单）
        if ($orderData['type'] === self::ORDER_TYPE_LIMIT) {
            if (!isset($orderData['price']) || $orderData['price'] <= 0) {
                return ['valid' => false, 'error' => '限价单必须指定有效价格'];
            }

            if ($orderData['price'] < $symbolInfo['min_price']) {
                return ['valid' => false, 'error' => '价格低于最小限制'];
            }
        }

        return ['valid' => true, 'error' => ''];
    }

    /**
     * 准备订单数据
     */
    private function prepareOrder(string $orderId, array $orderData): array
    {
        $now = microtime(true);
        
        return [
            'order_id' => $orderId,
            'user_id' => $orderData['user_id'],
            'symbol' => $orderData['symbol'],
            'side' => $orderData['side'],
            'type' => $orderData['type'],
            'quantity' => $orderData['quantity'],
            'price' => $orderData['price'] ?? 0,
            'filled_quantity' => 0,
            'status' => 'pending',
            'created_at' => $now,
            'updated_at' => $now,
            'client_order_id' => $orderData['client_order_id'] ?? '',
            'time_in_force' => $orderData['time_in_force'] ?? 'GTC'
        ];
    }

    /**
     * 快速资产操作
     */
    private function fastAssetOperation(array $order): array
    {
        try {
            $userId = $order['user_id'];
            $symbol = $order['symbol'];
            $side = $order['side'];
            $quantity = $order['quantity'];
            $price = $order['price'];

            // 计算需要冻结的资产
            if ($side === 'buy') {
                // 买单冻结计价货币
                $quoteCurrency = $this->getQuoteCurrency($symbol);
                $amount = $order['type'] === self::ORDER_TYPE_MARKET ? 
                    $quantity * $this->getMarketPrice($symbol) : 
                    $quantity * $price;
                
                return $this->freezeAssetFast($userId, $quoteCurrency, $amount);
            } else {
                // 卖单冻结基础货币
                $baseCurrency = $this->getBaseCurrency($symbol);
                return $this->freezeAssetFast($userId, $baseCurrency, $quantity);
            }

        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * 快速冻结资产
     */
    private function freezeAssetFast(int $userId, string $currency, float $amount): array
    {
        $cacheKey = "user_asset:{$userId}:{$currency}";
        $asset = $this->redis->hgetall($cacheKey);

        if (!$asset || !isset($asset['available'])) {
            // 从数据库加载资产信息到缓存
            $asset = $this->loadUserAssetToCache($userId, $currency);
        }

        $available = floatval($asset['available']);
        if ($available < $amount) {
            return ['success' => false, 'error' => '余额不足'];
        }

        // 更新缓存中的资产
        $newAvailable = $available - $amount;
        $newFrozen = floatval($asset['frozen']) + $amount;

        $this->redis->hmset($cacheKey, [
            'available' => $newAvailable,
            'frozen' => $newFrozen,
            'updated_at' => time()
        ]);

        // 异步更新数据库
        $this->asyncUpdateAsset($userId, $currency, $newAvailable, $newFrozen);

        return ['success' => true, 'error' => ''];
    }

    /**
     * 生成订单ID
     */
    private function generateOrderId(): string
    {
        return 'HFT' . date('YmdHis') . substr(microtime(), 2, 6) . mt_rand(100, 999);
    }

    /**
     * 推送到撮合队列
     */
    private function pushToMatchingQueue(array $order): void
    {
        $queueKey = "matching_queue:{$order['symbol']}";
        $this->redis->lpush($queueKey, json_encode($order));
    }

    /**
     * 缓存订单信息
     */
    private function cacheOrder(array $order): void
    {
        $cacheKey = "order:{$order['order_id']}";
        $this->redis->setex($cacheKey, 3600, json_encode($order));

        // 用户订单索引
        $userOrdersKey = self::CACHE_PREFIX_USER_ORDERS . $order['user_id'];
        $this->redis->sadd($userOrdersKey, $order['order_id']);
        $this->redis->expire($userOrdersKey, 3600);
    }

    /**
     * 异步处理订单
     */
    private function asyncProcessOrder(array $order): void
    {
        // 推送到异步队列进行数据库写入等操作
        $queueService = new QueueService();
        $queueService->push(QueueService::QUEUE_TRADING, 'ProcessOrderJob', $order);
    }

    /**
     * 从缓存获取订单
     */
    private function getOrderFromCache(string $orderId): ?array
    {
        $cacheKey = "order:{$orderId}";
        $orderJson = $this->redis->get($cacheKey);
        
        return $orderJson ? json_decode($orderJson, true) : null;
    }

    /**
     * 获取交易对信息（从缓存）
     */
    private function getSymbolInfoFromCache(string $symbol): ?array
    {
        $cacheKey = "symbol_info:{$symbol}";
        $symbolInfo = $this->redis->get($cacheKey);
        
        if (!$symbolInfo) {
            // 从数据库加载到缓存
            $symbolInfo = $this->loadSymbolInfoToCache($symbol);
        }
        
        return $symbolInfo ? json_decode($symbolInfo, true) : null;
    }

    /**
     * 加载交易对信息到缓存
     */
    private function loadSymbolInfoToCache(string $symbol): ?string
    {
        $symbolInfo = Db::name('trading_pairs')
            ->where('symbol', $symbol)
            ->where('status', 1)
            ->find();

        if ($symbolInfo) {
            $cacheKey = "symbol_info:{$symbol}";
            $this->redis->setex($cacheKey, 3600, json_encode($symbolInfo));
            return json_encode($symbolInfo);
        }

        return null;
    }

    /**
     * 加载用户资产到缓存
     */
    private function loadUserAssetToCache(int $userId, string $currency): array
    {
        $asset = Db::name('user_assets')
            ->where('user_id', $userId)
            ->where('coin_symbol', $currency)
            ->find();

        if (!$asset) {
            $asset = [
                'available' => 0,
                'frozen' => 0,
                'total' => 0
            ];
        }

        $cacheKey = "user_asset:{$userId}:{$currency}";
        $this->redis->hmset($cacheKey, $asset);
        $this->redis->expire($cacheKey, 3600);

        return $asset;
    }

    /**
     * 异步更新资产
     */
    private function asyncUpdateAsset(int $userId, string $currency, float $available, float $frozen): void
    {
        $queueService = new QueueService();
        $queueService->push(QueueService::QUEUE_TRADING, 'UpdateAssetJob', [
            'user_id' => $userId,
            'currency' => $currency,
            'available' => $available,
            'frozen' => $frozen
        ]);
    }
}
