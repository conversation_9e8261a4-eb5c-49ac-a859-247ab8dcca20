/* 合约交易页面样式 - 现代化设计 */

/* ==================== 页面头部 ==================== */
.page-header {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: 20px 0;
  margin-bottom: 20px;
}

.page-title h1 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.page-title p {
  margin: 4px 0 0 0;
  font-size: 14px;
}

/* ==================== 价格显示 ==================== */
.price-display {
  background: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: 16px;
  margin-bottom: 20px;
  text-align: center;
}

.current-price {
  margin-bottom: 8px;
}

.price-label {
  display: block;
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.price-value {
  font-size: 24px;
  font-weight: 600;
  font-family: 'Monaco', 'Menlo', monospace;
}

.price-change {
  display: flex;
  justify-content: center;
  align-items: center;
}

.change-value {
  font-size: 14px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 4px;
}

.change-value.positive {
  color: var(--color-success);
  background: rgba(14, 203, 129, 0.1);
}

.change-value.negative {
  color: var(--color-danger);
  background: rgba(246, 70, 93, 0.1);
}

/* ==================== 金额按钮 ==================== */
.amount-buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  margin-top: 8px;
}

.amount-btn {
  padding: 8px;
  border: 1px solid var(--border-color);
  background: transparent;
  color: var(--text-secondary);
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.amount-btn:hover,
.amount-btn.active {
  border-color: var(--color-primary);
  color: var(--color-primary);
  background: rgba(240, 185, 11, 0.1);
}

/* ==================== 时长选择器 ==================== */
.duration-selector {
  display: flex;
  gap: 8px;
}

.duration-btn {
  flex: 1;
  padding: 10px;
  border: 1px solid var(--border-color);
  background: transparent;
  color: var(--text-secondary);
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.duration-btn:hover,
.duration-btn.active {
  border-color: var(--color-primary);
  color: var(--color-primary);
  background: rgba(240, 185, 11, 0.1);
}

/* ==================== 盈利信息 ==================== */
.profit-info {
  background: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: 16px;
  margin-bottom: 20px;
}

.profit-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.profit-row:last-child {
  margin-bottom: 0;
}

.profit-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.profit-value {
  font-size: 14px;
  font-weight: 600;
  font-family: 'Monaco', 'Menlo', monospace;
}

/* ==================== 交易按钮 ==================== */
.trade-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 20px;
}

.trade-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 16px;
  border: none;
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.trade-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  transition: left 0.5s ease;
}

.trade-btn:hover::before {
  left: 100%;
}

.buy-btn {
  background: var(--color-success);
  color: #fff;
}

.buy-btn:hover {
  background: #0BB574;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(14, 203, 129, 0.3);
}

.sell-btn {
  background: var(--color-danger);
  color: #fff;
}

.sell-btn:hover {
  background: #E03D54;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(246, 70, 93, 0.3);
}

.btn-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.btn-text {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.btn-desc {
  font-size: 12px;
  opacity: 0.8;
}

/* ==================== 余额信息 ==================== */
.balance-info {
  background: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: 16px;
}

.balance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.balance-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.balance-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  font-family: 'Monaco', 'Menlo', monospace;
}

/* ==================== 图表容器 ==================== */
.chart-container {
  height: 300px;
  position: relative;
}

.chart-controls {
  display: flex;
  gap: 4px;
}

/* ==================== 合约标签 ==================== */
.contract-tabs {
  display: flex;
  width: 100%;
}

.contract-tab {
  flex: 1;
  padding: 12px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
}

.contract-tab.active {
  color: var(--text-primary);
}

.contract-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--color-primary);
}

/* ==================== 合约列表 ==================== */
.contract-list {
  display: none;
  max-height: 400px;
  overflow-y: auto;
}

.contract-list.active {
  display: block;
}

.contract-item {
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s ease;
}

.contract-item:hover {
  background: var(--bg-hover);
}

.contract-item:last-child {
  border-bottom: none;
}

.contract-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.contract-symbol {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.contract-direction {
  padding: 2px 6px;
  border-radius: 2px;
  font-size: 11px;
  font-weight: 500;
}

.contract-direction.buy {
  background: rgba(14, 203, 129, 0.1);
  color: var(--color-success);
}

.contract-direction.sell {
  background: rgba(246, 70, 93, 0.1);
  color: var(--color-danger);
}

.contract-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  font-size: 12px;
}

.contract-detail {
  display: flex;
  justify-content: space-between;
}

.detail-label {
  color: var(--text-secondary);
}

.detail-value {
  color: var(--text-primary);
  font-family: 'Monaco', 'Menlo', monospace;
}

.contract-progress {
  margin-top: 8px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: var(--border-color);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--color-primary);
  transition: width 0.3s ease;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  margin-top: 4px;
  font-size: 11px;
  color: var(--text-secondary);
}

/* ==================== 空状态 ==================== */
.contract-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  color: var(--text-secondary);
  font-size: 14px;
}

/* ==================== 统计信息 ==================== */
.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.stat-item:last-child {
  margin-bottom: 0;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  font-family: 'Monaco', 'Menlo', monospace;
}

/* ==================== 最新成交 ==================== */
.trades-list {
  max-height: 200px;
  overflow-y: auto;
}

.trade-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid var(--border-color);
  font-size: 12px;
}

.trade-item:last-child {
  border-bottom: none;
}

.trade-price {
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: 600;
}

.trade-amount {
  color: var(--text-secondary);
  font-family: 'Monaco', 'Menlo', monospace;
}

.trade-time {
  color: var(--text-tertiary);
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 992px) {
  .trade-buttons {
    grid-template-columns: 1fr;
  }
  
  .amount-buttons {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 16px 0;
  }
  
  .page-title h1 {
    font-size: 20px;
  }
  
  .price-value {
    font-size: 20px;
  }
  
  .trade-btn {
    padding: 16px 12px;
  }
  
  .btn-icon {
    font-size: 20px;
  }
  
  .btn-text {
    font-size: 14px;
  }
}
