<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的钱包 - GVD</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #1a1a1a; color: #fff; }
        
        .header { background: #2d2d2d; padding: 15px 0; border-bottom: 1px solid #333; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        .nav { display: flex; justify-content: space-between; align-items: center; }
        .logo { font-size: 24px; font-weight: bold; color: #007bff; }
        .nav-links a { color: #fff; text-decoration: none; margin: 0 15px; }
        
        .main { padding: 30px 0; }
        .wallet-overview { background: #2d2d2d; padding: 30px; border-radius: 10px; margin-bottom: 30px; }
        .balance-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 20px; }
        .balance-item { background: #333; padding: 20px; border-radius: 8px; text-align: center; }
        .balance-amount { font-size: 24px; font-weight: bold; color: #28a745; margin-bottom: 10px; }
        .balance-label { color: #999; font-size: 14px; }
        
        .wallet-actions { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .action-card { background: #2d2d2d; padding: 30px; border-radius: 10px; text-align: center; }
        .action-icon { font-size: 48px; margin-bottom: 20px; }
        .action-title { font-size: 20px; margin-bottom: 15px; }
        .action-desc { color: #999; margin-bottom: 20px; }
        .btn { padding: 12px 24px; background: #007bff; color: #fff; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #000; }
        
        .recent-transactions { background: #2d2d2d; padding: 30px; border-radius: 10px; }
        .transaction-list { margin-top: 20px; }
        .transaction-item { display: flex; justify-content: space-between; align-items: center; padding: 15px 0; border-bottom: 1px solid #333; }
        .transaction-info { flex: 1; }
        .transaction-type { font-weight: bold; margin-bottom: 5px; }
        .transaction-time { color: #999; font-size: 12px; }
        .transaction-amount { font-weight: bold; }
        .amount-positive { color: #28a745; }
        .amount-negative { color: #dc3545; }
        
        .tabs { display: flex; margin-bottom: 20px; }
        .tab { padding: 10px 20px; background: #333; color: #999; border: none; cursor: pointer; margin-right: 10px; border-radius: 5px; }
        .tab.active { background: #007bff; color: #fff; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="nav">
                <div class="logo">💰 我的钱包</div>
                <div class="nav-links">
                    <a href="/">首页</a>
                    <a href="/contract/">交易</a>
                    <a href="/auth/login.html">退出</a>
                </div>
            </div>
        </div>
    </div>

    <div class="main">
        <div class="container">
            <!-- 钱包总览 -->
            <div class="wallet-overview">
                <h2>资产总览</h2>
                <div class="balance-grid">
                    <div class="balance-item">
                        <div class="balance-amount" id="totalBalance">1,000.00</div>
                        <div class="balance-label">总资产 (USDT)</div>
                    </div>
                    <div class="balance-item">
                        <div class="balance-amount" id="availableBalance">800.00</div>
                        <div class="balance-label">可用余额 (USDT)</div>
                    </div>
                    <div class="balance-item">
                        <div class="balance-amount" id="frozenBalance">200.00</div>
                        <div class="balance-label">冻结余额 (USDT)</div>
                    </div>
                </div>
            </div>

            <!-- 钱包操作 -->
            <div class="wallet-actions">
                <div class="action-card">
                    <div class="action-icon">💳</div>
                    <div class="action-title">充值</div>
                    <div class="action-desc">向您的账户充值USDT</div>
                    <a href="/wallet/deposit.html" class="btn btn-success">立即充值</a>
                </div>
                <div class="action-card">
                    <div class="action-icon">💸</div>
                    <div class="action-title">提现</div>
                    <div class="action-desc">将资金提取到外部钱包</div>
                    <a href="/wallet/withdraw.html" class="btn btn-warning">立即提现</a>
                </div>
                <div class="action-card">
                    <div class="action-icon">🔐</div>
                    <div class="action-title">实名认证</div>
                    <div class="action-desc">完成实名认证，提升安全等级</div>
                    <a href="/wallet/kyc.html" class="btn">立即认证</a>
                </div>
            </div>

            <!-- 交易记录 -->
            <div class="recent-transactions">
                <h2>交易记录</h2>
                <div class="tabs">
                    <button class="tab active" onclick="switchTab('all')">全部</button>
                    <button class="tab" onclick="switchTab('deposit')">充值</button>
                    <button class="tab" onclick="switchTab('withdraw')">提现</button>
                    <button class="tab" onclick="switchTab('trade')">交易</button>
                </div>
                <div class="transaction-list" id="transactionList">
                    <div class="transaction-item">
                        <div class="transaction-info">
                            <div class="transaction-type">USDT充值</div>
                            <div class="transaction-time">2024-01-15 14:30:25</div>
                        </div>
                        <div class="transaction-amount amount-positive">+500.00 USDT</div>
                    </div>
                    <div class="transaction-item">
                        <div class="transaction-info">
                            <div class="transaction-type">秒合约交易</div>
                            <div class="transaction-time">2024-01-15 13:25:10</div>
                        </div>
                        <div class="transaction-amount amount-positive">+75.00 USDT</div>
                    </div>
                    <div class="transaction-item">
                        <div class="transaction-info">
                            <div class="transaction-type">秒合约交易</div>
                            <div class="transaction-time">2024-01-15 12:15:45</div>
                        </div>
                        <div class="transaction-amount amount-negative">-100.00 USDT</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 检查登录状态
        function checkLogin() {
            const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
            if (!isLoggedIn) {
                alert('请先登录');
                window.location.href = '/auth/login.html';
                return false;
            }
            return true;
        }

        // 切换标签
        function switchTab(type) {
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
            
            // 这里可以根据type加载不同的交易记录
            console.log('切换到:', type);
        }

        // 页面加载时检查登录
        document.addEventListener('DOMContentLoaded', function() {
            if (!checkLogin()) return;
            
            // 加载用户资产数据
            loadUserAssets();
        });

        // 加载用户资产
        function loadUserAssets() {
            // 这里应该调用API获取用户真实资产数据
            console.log('加载用户资产数据...');
        }
    </script>
</body>
</html>
