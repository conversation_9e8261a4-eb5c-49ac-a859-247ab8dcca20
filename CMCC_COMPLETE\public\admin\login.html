<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - GVD交易平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            width: 450px;
            max-width: 90%;
        }

        .login-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .login-header h1 {
            font-size: 32px;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .login-header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .admin-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
        }

        .login-form {
            padding: 50px 40px;
        }

        .form-group {
            margin-bottom: 30px;
        }

        .form-group label {
            display: block;
            margin-bottom: 10px;
            color: #2c3e50;
            font-weight: 600;
            font-size: 14px;
        }

        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s;
            background: #f8f9fa;
        }

        .form-group input:focus {
            outline: none;
            border-color: #3498db;
            background: white;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .login-btn {
            width: 100%;
            padding: 18px;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(52, 152, 219, 0.3);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .error-message {
            background: #e74c3c;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 25px;
            display: none;
            text-align: center;
        }

        .success-message {
            background: #27ae60;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 25px;
            display: none;
            text-align: center;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 15px;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 24px;
            height: 24px;
            border: 3px solid #3498db;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .security-notice {
            background: #f39c12;
            color: white;
            padding: 15px;
            text-align: center;
            font-size: 14px;
            margin-top: 20px;
            border-radius: 8px;
        }

        .footer {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
            font-size: 14px;
            border-top: 1px solid #ecf0f1;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="admin-icon">👨‍💼</div>
            <h1>管理后台</h1>
            <p>GVD交易平台管理系统</p>
        </div>
        
        <div class="login-form">
            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>
            
            <form id="loginForm">
                <div class="form-group">
                    <label for="username">管理员账号</label>
                    <input type="text" id="username" name="username" required autocomplete="username">
                </div>
                
                <div class="form-group">
                    <label for="password">登录密码</label>
                    <input type="password" id="password" name="password" required autocomplete="current-password">
                </div>
                
                <button type="submit" class="login-btn" id="loginBtn">登录管理后台</button>
                <div class="loading" id="loading"></div>
            </form>
            
            <div class="security-notice">
                ⚠️ 管理后台仅限授权人员访问，所有操作将被记录
            </div>
        </div>
        
        <div class="footer">
            © 2024 GVD交易平台 - 管理系统 v1.0
        </div>
    </div>

    <script>
        class AdminLogin {
            constructor() {
                this.form = document.getElementById('loginForm');
                this.loginBtn = document.getElementById('loginBtn');
                this.loading = document.getElementById('loading');
                this.errorMessage = document.getElementById('errorMessage');
                this.successMessage = document.getElementById('successMessage');
                
                this.bindEvents();
                this.checkLoginStatus();
            }
            
            bindEvents() {
                this.form.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleLogin();
                });
                
                // 回车键登录
                document.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !this.loginBtn.disabled) {
                        this.handleLogin();
                    }
                });
            }
            
            async handleLogin() {
                const formData = new FormData(this.form);
                const data = {
                    username: formData.get('username'),
                    password: formData.get('password')
                };
                
                if (!this.validateForm(data)) {
                    return;
                }
                
                this.setLoading(true);
                
                try {
                    const response = await fetch('/admin/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(data)
                    });
                    
                    const result = await response.json();
                    
                    if (result.code === 1) {
                        this.showSuccess('登录成功，正在跳转...');
                        
                        // 保存管理员token
                        localStorage.setItem('admin_token', result.data.token);
                        localStorage.setItem('admin_info', JSON.stringify(result.data));
                        
                        // 跳转到管理后台首页
                        setTimeout(() => {
                            window.location.href = '/admin/dashboard.html';
                        }, 1000);
                    } else {
                        this.showError(result.msg || '登录失败');
                    }
                } catch (error) {
                    console.error('登录请求失败:', error);
                    this.showError('网络错误，请检查网络连接');
                } finally {
                    this.setLoading(false);
                }
            }
            
            validateForm(data) {
                if (!data.username) {
                    this.showError('请输入管理员账号');
                    return false;
                }
                
                if (!data.password) {
                    this.showError('请输入登录密码');
                    return false;
                }
                
                if (data.password.length < 6) {
                    this.showError('密码长度不能少于6位');
                    return false;
                }
                
                return true;
            }
            
            setLoading(loading) {
                this.loginBtn.disabled = loading;
                this.loading.style.display = loading ? 'block' : 'none';
                this.loginBtn.textContent = loading ? '登录中...' : '登录管理后台';
            }
            
            showError(message) {
                this.errorMessage.textContent = message;
                this.errorMessage.style.display = 'block';
                this.successMessage.style.display = 'none';
                
                // 3秒后自动隐藏
                setTimeout(() => {
                    this.errorMessage.style.display = 'none';
                }, 3000);
            }
            
            showSuccess(message) {
                this.successMessage.textContent = message;
                this.successMessage.style.display = 'block';
                this.errorMessage.style.display = 'none';
            }
            
            checkLoginStatus() {
                const token = localStorage.getItem('admin_token');
                if (token) {
                    // 验证token是否有效
                    fetch('/admin/auth/verify-token', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    }).then(response => response.json())
                      .then(result => {
                          if (result.code === 1) {
                              // token有效，跳转到管理后台
                              window.location.href = '/admin/dashboard.html';
                          }
                      }).catch(() => {
                          // token验证失败，清除本地存储
                          localStorage.removeItem('admin_token');
                          localStorage.removeItem('admin_info');
                      });
                }
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            new AdminLogin();
        });
    </script>
</body>
</html>
