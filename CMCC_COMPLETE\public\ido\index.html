<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IDO认购 - GVD交易平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }

        .header {
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }

        .nav-menu {
            display: flex;
            gap: 30px;
        }

        .nav-menu a {
            text-decoration: none;
            color: #666;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-menu a:hover,
        .nav-menu a.active {
            color: #667eea;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .page-title {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .page-subtitle {
            font-size: 18px;
            color: #666;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .filter-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            justify-content: center;
        }

        .filter-tab {
            padding: 10px 20px;
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }

        .filter-tab:hover,
        .filter-tab.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .project-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.3s;
        }

        .project-card:hover {
            transform: translateY(-5px);
        }

        .project-header {
            padding: 25px;
            border-bottom: 1px solid #eee;
        }

        .project-info {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .project-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #667eea;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
        }

        .project-details h3 {
            font-size: 20px;
            margin-bottom: 5px;
            color: #333;
        }

        .project-symbol {
            color: #666;
            font-size: 14px;
        }

        .project-status {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-upcoming {
            background: #fff3cd;
            color: #856404;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-completed {
            background: #d1ecf1;
            color: #0c5460;
        }

        .project-description {
            color: #666;
            line-height: 1.6;
        }

        .project-body {
            padding: 25px;
        }

        .progress-section {
            margin-bottom: 20px;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e1e5e9;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s;
        }

        .project-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-item .value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .stat-item .label {
            font-size: 12px;
            color: #666;
        }

        .project-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-outline {
            background: transparent;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn:hover {
            opacity: 0.8;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .loading {
            text-align: center;
            padding: 60px;
            color: #666;
        }

        .empty-state {
            text-align: center;
            padding: 60px;
            color: #666;
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .projects-grid {
                grid-template-columns: 1fr;
            }
            
            .filter-tabs {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">GVD交易平台</div>
            <nav class="nav-menu">
                <a href="/trade/index.html">交易</a>
                <a href="/ido/index.html" class="active">IDO</a>
                <a href="/user/dashboard.html">资产</a>
                <a href="/user/orders.html">订单</a>
            </nav>
            <div class="user-info">
                <span id="username">用户</span>
                <button class="btn btn-outline" onclick="logout()">退出</button>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="page-header">
            <h1 class="page-title">IDO认购</h1>
            <p class="page-subtitle">参与优质项目的首次代币发行，抢占投资先机</p>
        </div>

        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <div class="stat-value" id="totalProjects">0</div>
                <div class="stat-label">总项目数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="activeProjects">0</div>
                <div class="stat-label">进行中</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="completedProjects">0</div>
                <div class="stat-label">已完成</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalRaised">$0</div>
                <div class="stat-label">总募集金额</div>
            </div>
        </div>

        <div class="filter-tabs">
            <div class="filter-tab active" data-status="" onclick="filterProjects('')">全部</div>
            <div class="filter-tab" data-status="1" onclick="filterProjects('1')">即将开始</div>
            <div class="filter-tab" data-status="2" onclick="filterProjects('2')">进行中</div>
            <div class="filter-tab" data-status="3" onclick="filterProjects('3')">已完成</div>
        </div>

        <div class="projects-grid" id="projectsGrid">
            <div class="loading">加载中...</div>
        </div>
    </div>

    <script>
        class IdoPage {
            constructor() {
                this.token = localStorage.getItem('token');
                this.userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
                this.currentFilter = '';
                
                if (!this.token) {
                    window.location.href = '/auth/login.html';
                    return;
                }
                
                this.init();
            }
            
            async init() {
                this.updateUserInfo();
                await this.loadStats();
                await this.loadProjects();
            }
            
            updateUserInfo() {
                if (this.userInfo.username) {
                    document.getElementById('username').textContent = this.userInfo.username;
                }
            }
            
            async loadStats() {
                try {
                    const response = await this.apiRequest('/api/ido/stats');
                    if (response.code === 1) {
                        const stats = response.data;
                        document.getElementById('totalProjects').textContent = stats.total_projects || 0;
                        document.getElementById('activeProjects').textContent = stats.active_projects || 0;
                        document.getElementById('completedProjects').textContent = stats.completed_projects || 0;
                        document.getElementById('totalRaised').textContent = `$${this.formatNumber(stats.total_raised || 0)}`;
                    }
                } catch (error) {
                    console.error('加载统计失败:', error);
                }
            }
            
            async loadProjects() {
                try {
                    const params = new URLSearchParams();
                    if (this.currentFilter) {
                        params.append('status', this.currentFilter);
                    }
                    
                    const response = await this.apiRequest(`/api/ido/projects?${params.toString()}`);
                    if (response.code === 1) {
                        this.renderProjects(response.data.list);
                    }
                } catch (error) {
                    console.error('加载项目失败:', error);
                    document.getElementById('projectsGrid').innerHTML = '<div class="empty-state">加载失败</div>';
                }
            }
            
            renderProjects(projects) {
                const projectsGrid = document.getElementById('projectsGrid');
                
                if (projects.length === 0) {
                    projectsGrid.innerHTML = '<div class="empty-state">暂无项目</div>';
                    return;
                }
                
                projectsGrid.innerHTML = '';
                projects.forEach(project => {
                    const projectCard = this.createProjectCard(project);
                    projectsGrid.appendChild(projectCard);
                });
            }
            
            createProjectCard(project) {
                const card = document.createElement('div');
                card.className = 'project-card';
                
                const statusClass = this.getStatusClass(project.status);
                const canPurchase = project.can_purchase;
                
                card.innerHTML = `
                    <div class="project-header">
                        <div class="project-info">
                            <div class="project-icon">${project.symbol.charAt(0)}</div>
                            <div class="project-details">
                                <h3>${project.name}</h3>
                                <div class="project-symbol">${project.symbol}</div>
                            </div>
                            <div class="project-status ${statusClass}">${project.status_text}</div>
                        </div>
                        <div class="project-description">${project.description || '暂无描述'}</div>
                    </div>
                    <div class="project-body">
                        <div class="progress-section">
                            <div class="progress-info">
                                <span>进度</span>
                                <span>${project.progress.toFixed(1)}%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${project.progress}%"></div>
                            </div>
                        </div>
                        <div class="project-stats">
                            <div class="stat-item">
                                <div class="value">$${this.formatNumber(project.raised_amount)}</div>
                                <div class="label">已募集</div>
                            </div>
                            <div class="stat-item">
                                <div class="value">$${this.formatNumber(project.target_amount)}</div>
                                <div class="label">目标金额</div>
                            </div>
                            <div class="stat-item">
                                <div class="value">$${project.token_price}</div>
                                <div class="label">代币价格</div>
                            </div>
                            <div class="stat-item">
                                <div class="value">${this.formatTime(project.remaining_time)}</div>
                                <div class="label">剩余时间</div>
                            </div>
                        </div>
                        <div class="project-actions">
                            <button class="btn btn-outline" onclick="viewProject(${project.id})">查看详情</button>
                            <button class="btn btn-primary" ${!canPurchase ? 'disabled' : ''} 
                                    onclick="purchaseProject(${project.id})">${canPurchase ? '立即认购' : '不可认购'}</button>
                        </div>
                    </div>
                `;
                
                return card;
            }
            
            getStatusClass(status) {
                const statusMap = {
                    1: 'status-upcoming',
                    2: 'status-active',
                    3: 'status-completed',
                    4: 'status-cancelled'
                };
                return statusMap[status] || 'status-upcoming';
            }
            
            formatNumber(num) {
                if (num >= 1000000) {
                    return (num / 1000000).toFixed(1) + 'M';
                } else if (num >= 1000) {
                    return (num / 1000).toFixed(1) + 'K';
                }
                return num.toFixed(0);
            }
            
            formatTime(seconds) {
                if (seconds <= 0) return '已结束';
                
                const days = Math.floor(seconds / 86400);
                const hours = Math.floor((seconds % 86400) / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                
                if (days > 0) return `${days}天`;
                if (hours > 0) return `${hours}小时`;
                return `${minutes}分钟`;
            }
            
            async apiRequest(url, options = {}) {
                const defaultOptions = {
                    headers: {
                        'Authorization': `Bearer ${this.token}`,
                        'Content-Type': 'application/json'
                    }
                };
                
                const response = await fetch(url, { ...defaultOptions, ...options });
                const result = await response.json();
                
                if (response.status === 401) {
                    localStorage.removeItem('token');
                    localStorage.removeItem('user_info');
                    window.location.href = '/auth/login.html';
                    return;
                }
                
                return result;
            }
        }
        
        function filterProjects(status) {
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            idoPage.currentFilter = status;
            idoPage.loadProjects();
        }
        
        function viewProject(projectId) {
            window.location.href = `/ido/project.html?id=${projectId}`;
        }
        
        function purchaseProject(projectId) {
            window.location.href = `/ido/purchase.html?id=${projectId}`;
        }
        
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('token');
                localStorage.removeItem('user_info');
                window.location.href = '/auth/login.html';
            }
        }
        
        let idoPage;
        document.addEventListener('DOMContentLoaded', () => {
            idoPage = new IdoPage();
        });
    </script>
</body>
</html>
