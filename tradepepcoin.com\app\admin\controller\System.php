<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\BaseController;
use think\facade\View;
use think\facade\Session;
use think\facade\Request;
use think\facade\Db;
use think\facade\Cache;
use app\common\service\SystemConfigService;

/**
 * 系统配置控制器
 */
class System extends BaseController
{
    public function initialize()
    {
        parent::initialize();
        
        // 检查管理员登录状态
        if (!Session::has('admin_id')) {
            $this->redirect('/admin/login');
        }
    }

    /**
     * 合约配置
     */
    public function contractConfig()
    {
        if (Request::isPost()) {
            return $this->saveContractConfig();
        }
        
        // 获取当前配置
        $config = Db::name('contract_config')->find() ?: [];
        
        View::assign([
            'config' => $config,
            'title' => '合约配置 - GVD管理后台'
        ]);
        
        return View::fetch('admin/system/contract_config');
    }

    /**
     * 保存合约配置
     */
    private function saveContractConfig()
    {
        $data = Request::post();
        
        try {
            // 处理赔率配置
            $profitRates = [];
            if (isset($data['profit_rates'])) {
                foreach ($data['profit_rates'] as $time => $rate) {
                    $profitRates[$time] = floatval($rate);
                }
            }
            
            // 处理快捷金额
            $quickAmounts = [];
            if (isset($data['quick_amounts'])) {
                $quickAmounts = array_map('intval', explode(',', $data['quick_amounts']));
            }
            
            $configData = [
                'min_amount' => floatval($data['min_amount'] ?? 10),
                'max_amount' => floatval($data['max_amount'] ?? 10000),
                'win_rate' => floatval($data['win_rate'] ?? 0.85),
                'available_times' => json_encode($data['available_times'] ?? [60, 180, 300, 600]),
                'available_coins' => json_encode($data['available_coins'] ?? ['BTC', 'ETH', 'LTC']),
                'quick_amounts' => json_encode($quickAmounts),
                'profit_rates' => json_encode($profitRates),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            // 检查是否存在配置
            $exists = Db::name('contract_config')->find();
            
            if ($exists) {
                Db::name('contract_config')->update($configData);
            } else {
                $configData['created_at'] = date('Y-m-d H:i:s');
                Db::name('contract_config')->insert($configData);
            }
            
            // 清除缓存
            Cache::delete('contract_config');
            
            return json(['code' => 1, 'msg' => '保存成功']);
            
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 图片管理
     */
    public function imageConfig()
    {
        if (Request::isPost()) {
            return $this->saveImageConfig();
        }
        
        // 获取当前图片配置
        $images = Db::name('system_images')->select();
        
        View::assign([
            'images' => $images,
            'title' => '图片管理 - GVD管理后台'
        ]);
        
        return View::fetch('admin/system/image_config');
    }

    /**
     * 保存图片配置
     */
    private function saveImageConfig()
    {
        $data = Request::post();
        
        try {
            foreach ($data['images'] as $key => $url) {
                Db::name('system_images')->where('key', $key)->delete();
                Db::name('system_images')->insert([
                    'key' => $key,
                    'url' => $url,
                    'description' => $data['descriptions'][$key] ?? '',
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }
            
            return json(['code' => 1, 'msg' => '保存成功']);
            
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 万能验证码设置
     */
    public function universalCode()
    {
        if (Request::isPost()) {
            return $this->saveUniversalCode();
        }
        
        // 获取当前万能验证码
        $config = Db::name('system_config')->where('key', 'universal_verification_code')->find();
        
        View::assign([
            'current_code' => $config['value'] ?? '888888',
            'title' => '万能验证码设置 - GVD管理后台'
        ]);
        
        return View::fetch('admin/system/universal_code');
    }

    /**
     * 保存万能验证码
     */
    private function saveUniversalCode()
    {
        $code = Request::post('code');
        
        if (!$code || strlen($code) != 6 || !is_numeric($code)) {
            return json(['code' => 0, 'msg' => '验证码必须是6位数字']);
        }
        
        try {
            Db::name('system_config')->where('key', 'universal_verification_code')->delete();
            Db::name('system_config')->insert([
                'key' => 'universal_verification_code',
                'value' => $code,
                'description' => '万能验证码',
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            return json(['code' => 1, 'msg' => '设置成功']);
            
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '设置失败：' . $e->getMessage()]);
        }
    }

    /**
     * 财务报表
     */
    public function financeReport()
    {
        $startDate = Request::get('start_date', date('Y-m-01'));
        $endDate = Request::get('end_date', date('Y-m-d'));
        
        // 充值统计
        $depositStats = Db::name('deposit_records')
                         ->where('status', 'completed')
                         ->whereBetweenTime('created_at', $startDate, $endDate)
                         ->field('coin_symbol, sum(amount) as total_amount, count(*) as total_count')
                         ->group('coin_symbol')
                         ->select();
        
        // 提现统计
        $withdrawStats = Db::name('withdraw_records')
                          ->where('status', 'completed')
                          ->whereBetweenTime('created_at', $startDate, $endDate)
                          ->field('coin_symbol, sum(amount) as total_amount, count(*) as total_count')
                          ->group('coin_symbol')
                          ->select();
        
        // 合约交易统计
        $contractStats = Db::name('contract_orders')
                          ->whereBetweenTime('created_at', $startDate, $endDate)
                          ->field('
                              count(*) as total_orders,
                              sum(amount) as total_amount,
                              sum(case when is_win = 1 then profit_loss else 0 end) as total_profit,
                              sum(case when is_win = 0 then amount else 0 end) as total_loss
                          ')
                          ->find();
        
        View::assign([
            'deposit_stats' => $depositStats,
            'withdraw_stats' => $withdrawStats,
            'contract_stats' => $contractStats,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'title' => '财务报表 - GVD管理后台'
        ]);
        
        return View::fetch('admin/system/finance_report');
    }

    /**
     * 客服配置
     */
    public function customerService()
    {
        if (Request::isPost()) {
            return $this->saveCustomerService();
        }
        
        $config = Db::name('system_config')->where('key', 'customer_service')->find();
        $serviceConfig = $config ? json_decode($config['value'], true) : [];
        
        View::assign([
            'config' => $serviceConfig,
            'title' => '客服配置 - GVD管理后台'
        ]);
        
        return View::fetch('admin/system/customer_service');
    }

    /**
     * 保存客服配置
     */
    private function saveCustomerService()
    {
        $data = Request::post();
        
        try {
            $config = [
                'qq' => $data['qq'] ?? '',
                'wechat' => $data['wechat'] ?? '',
                'telegram' => $data['telegram'] ?? '',
                'email' => $data['email'] ?? '',
                'phone' => $data['phone'] ?? '',
                'work_time' => $data['work_time'] ?? '9:00-18:00'
            ];
            
            Db::name('system_config')->where('key', 'customer_service')->delete();
            Db::name('system_config')->insert([
                'key' => 'customer_service',
                'value' => json_encode($config),
                'description' => '客服配置',
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            return json(['code' => 1, 'msg' => '保存成功']);
            
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }
}
