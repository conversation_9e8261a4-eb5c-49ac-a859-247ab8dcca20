<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 交易对模型
 */
class TradingPair extends Model
{
    protected $table = 'gvd_trading_pairs';
    
    // 设置字段信息
    protected $schema = [
        'id'               => 'int',
        'symbol'           => 'string',
        'base_coin'        => 'string',
        'quote_coin'       => 'string',
        'price_precision'  => 'int',
        'amount_precision' => 'int',
        'min_amount'       => 'decimal',
        'max_amount'       => 'decimal',
        'min_total'        => 'decimal',
        'maker_fee'        => 'decimal',
        'taker_fee'        => 'decimal',
        'current_price'    => 'decimal',
        'change_24h'       => 'decimal',
        'volume_24h'       => 'decimal',
        'high_24h'         => 'decimal',
        'low_24h'          => 'decimal',
        'sort'             => 'int',
        'status'           => 'int',
        'created_at'       => 'datetime',
        'updated_at'       => 'datetime',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 类型转换
    protected $type = [
        'price_precision'  => 'integer',
        'amount_precision' => 'integer',
        'min_amount'       => 'decimal:8',
        'max_amount'       => 'decimal:8',
        'min_total'        => 'decimal:8',
        'maker_fee'        => 'decimal:6',
        'taker_fee'        => 'decimal:6',
        'current_price'    => 'decimal:8',
        'change_24h'       => 'decimal:4',
        'volume_24h'       => 'decimal:8',
        'high_24h'         => 'decimal:8',
        'low_24h'          => 'decimal:8',
        'sort'             => 'integer',
        'status'           => 'integer',
    ];

    /**
     * 关联基础币种
     */
    public function baseCoin()
    {
        return $this->belongsTo(Coin::class, 'base_coin', 'symbol');
    }

    /**
     * 关联计价币种
     */
    public function quoteCoin()
    {
        return $this->belongsTo(Coin::class, 'quote_coin', 'symbol');
    }

    /**
     * 获取启用的交易对
     */
    public static function getEnabled()
    {
        return self::where('status', 1)->order('sort', 'asc')->select();
    }

    /**
     * 根据符号获取交易对
     */
    public static function getBySymbol(string $symbol)
    {
        return self::where('symbol', $symbol)->find();
    }

    /**
     * 获取热门交易对
     */
    public static function getHot(int $limit = 10)
    {
        return self::where('status', 1)
                  ->order('volume_24h', 'desc')
                  ->limit($limit)
                  ->select();
    }

    /**
     * 状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [0 => '禁用', 1 => '启用'];
        return $status[$data['status']] ?? '未知';
    }

    /**
     * 24小时涨跌幅文本
     */
    public function getChange24hTextAttr($value, $data)
    {
        $change = $data['change_24h'];
        $prefix = $change >= 0 ? '+' : '';
        return $prefix . number_format($change, 2) . '%';
    }

    /**
     * 24小时涨跌幅颜色
     */
    public function getChange24hColorAttr($value, $data)
    {
        return $data['change_24h'] >= 0 ? 'success' : 'danger';
    }

    /**
     * 格式化当前价格
     */
    public function getCurrentPriceFormatAttr($value, $data)
    {
        return number_format($data['current_price'], $data['price_precision'], '.', '');
    }

    /**
     * 格式化24小时成交量
     */
    public function getVolume24hFormatAttr($value, $data)
    {
        return number_format($data['volume_24h'], $data['amount_precision'], '.', '');
    }

    /**
     * 格式化24小时最高价
     */
    public function getHigh24hFormatAttr($value, $data)
    {
        return number_format($data['high_24h'], $data['price_precision'], '.', '');
    }

    /**
     * 格式化24小时最低价
     */
    public function getLow24hFormatAttr($value, $data)
    {
        return number_format($data['low_24h'], $data['price_precision'], '.', '');
    }

    /**
     * 格式化最小交易数量
     */
    public function getMinAmountFormatAttr($value, $data)
    {
        return number_format($data['min_amount'], $data['amount_precision'], '.', '');
    }

    /**
     * 格式化最大交易数量
     */
    public function getMaxAmountFormatAttr($value, $data)
    {
        return number_format($data['max_amount'], $data['amount_precision'], '.', '');
    }

    /**
     * 格式化最小交易额
     */
    public function getMinTotalFormatAttr($value, $data)
    {
        return number_format($data['min_total'], $data['price_precision'], '.', '');
    }

    /**
     * Maker手续费百分比
     */
    public function getMakerFeePercentAttr($value, $data)
    {
        return number_format($data['maker_fee'] * 100, 3) . '%';
    }

    /**
     * Taker手续费百分比
     */
    public function getTakerFeePercentAttr($value, $data)
    {
        return number_format($data['taker_fee'] * 100, 3) . '%';
    }

    /**
     * 更新市场数据
     */
    public function updateMarketData(array $data): bool
    {
        $updateData = [];
        
        if (isset($data['price'])) {
            $updateData['current_price'] = $data['price'];
        }
        
        if (isset($data['change_24h'])) {
            $updateData['change_24h'] = $data['change_24h'];
        }
        
        if (isset($data['volume_24h'])) {
            $updateData['volume_24h'] = $data['volume_24h'];
        }
        
        if (isset($data['high_24h'])) {
            $updateData['high_24h'] = $data['high_24h'];
        }
        
        if (isset($data['low_24h'])) {
            $updateData['low_24h'] = $data['low_24h'];
        }
        
        if (empty($updateData)) {
            return false;
        }
        
        return $this->save($updateData);
    }
}
