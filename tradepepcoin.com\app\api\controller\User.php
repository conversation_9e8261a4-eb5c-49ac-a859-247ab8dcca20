<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\BaseController;
use app\common\model\User as UserModel;
use app\common\model\UserAsset;
use app\common\model\DepositRecord;
use app\common\model\WithdrawRecord;
use app\common\model\Order;
use app\common\model\Commission;
use think\facade\Session;

/**
 * 用户API控制器
 */
class User extends BaseController
{
    public function initialize()
    {
        parent::initialize();
        $this->checkAuth();
    }

    /**
     * 获取用户信息
     */
    public function info()
    {
        $userId = $this->getUserId();
        $user = UserModel::find($userId);
        
        if (!$user) {
            return $this->error('用户不存在');
        }

        return $this->success([
            'user_id' => $user->id,
            'username' => $user->username,
            'email' => $user->email,
            'nickname' => $user->nickname,
            'avatar' => $user->avatar_url,
            'phone' => $user->phone,
            'real_name' => $user->real_name,
            'user_type' => $user->user_type,
            'status' => $user->status,
            'invite_code' => $user->invite_code,
            'created_at' => $user->created_at,
            'last_login_time' => $user->last_login_time
        ]);
    }

    /**
     * 获取用户资产
     */
    public function assets()
    {
        $userId = $this->getUserId();
        $coinSymbol = input('coin_symbol', '');
        
        $where = ['user_id' => $userId];
        if ($coinSymbol) {
            $where['coin_symbol'] = $coinSymbol;
        }

        $assets = UserAsset::where($where)
                          ->with('coin')
                          ->select();

        $data = [];
        foreach ($assets as $asset) {
            $data[] = [
                'coin_symbol' => $asset->coin_symbol,
                'coin_name' => $asset->coin->name ?? '',
                'available' => $asset->available,
                'frozen' => $asset->frozen,
                'total' => $asset->total,
                'updated_at' => $asset->updated_at
            ];
        }

        return $this->success($data);
    }

    /**
     * 获取充值记录
     */
    public function deposits()
    {
        $userId = $this->getUserId();
        $coinSymbol = input('coin_symbol', '');
        $status = input('status', '');
        $page = input('page/d', 1);
        $limit = input('limit/d', 50);

        $where = ['user_id' => $userId];
        
        if ($coinSymbol) {
            $where['coin_symbol'] = $coinSymbol;
        }
        
        if ($status !== '') {
            $where['status'] = $status;
        }

        $deposits = DepositRecord::where($where)
                                ->order('created_at', 'desc')
                                ->paginate([
                                    'list_rows' => $limit,
                                    'page' => $page
                                ]);

        $data = [];
        foreach ($deposits->items() as $deposit) {
            $data[] = [
                'id' => $deposit->id,
                'coin_symbol' => $deposit->coin_symbol,
                'amount' => $deposit->amount,
                'address' => $deposit->address,
                'txid' => $deposit->txid,
                'confirmations' => $deposit->confirmations,
                'required_confirmations' => $deposit->required_confirmations,
                'status' => $deposit->status,
                'status_text' => $deposit->status_text,
                'created_at' => $deposit->created_at,
                'confirmed_at' => $deposit->confirmed_at
            ];
        }

        return $this->success([
            'data' => $data,
            'total' => $deposits->total(),
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * 获取提现记录
     */
    public function withdrawals()
    {
        $userId = $this->getUserId();
        $coinSymbol = input('coin_symbol', '');
        $status = input('status', '');
        $page = input('page/d', 1);
        $limit = input('limit/d', 50);

        $where = ['user_id' => $userId];
        
        if ($coinSymbol) {
            $where['coin_symbol'] = $coinSymbol;
        }
        
        if ($status !== '') {
            $where['status'] = $status;
        }

        $withdrawals = WithdrawRecord::where($where)
                                    ->order('created_at', 'desc')
                                    ->paginate([
                                        'list_rows' => $limit,
                                        'page' => $page
                                    ]);

        $data = [];
        foreach ($withdrawals->items() as $withdrawal) {
            $data[] = [
                'id' => $withdrawal->id,
                'coin_symbol' => $withdrawal->coin_symbol,
                'amount' => $withdrawal->amount,
                'fee' => $withdrawal->fee,
                'actual_amount' => $withdrawal->actual_amount,
                'address' => $withdrawal->address,
                'memo' => $withdrawal->memo,
                'txid' => $withdrawal->txid,
                'status' => $withdrawal->status,
                'status_text' => $withdrawal->status_text,
                'reject_reason' => $withdrawal->reject_reason,
                'created_at' => $withdrawal->created_at,
                'processed_at' => $withdrawal->processed_at
            ];
        }

        return $this->success([
            'data' => $data,
            'total' => $withdrawals->total(),
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * 获取订单历史
     */
    public function orders()
    {
        $userId = $this->getUserId();
        $symbol = input('symbol', '');
        $status = input('status', '');
        $page = input('page/d', 1);
        $limit = input('limit/d', 50);

        $where = ['user_id' => $userId];
        
        if ($symbol) {
            $where['symbol'] = $symbol;
        }
        
        if ($status !== '') {
            $where['status'] = $status;
        }

        $orders = Order::where($where)
                      ->order('created_at', 'desc')
                      ->paginate([
                          'list_rows' => $limit,
                          'page' => $page
                      ]);

        $data = [];
        foreach ($orders->items() as $order) {
            $data[] = [
                'order_id' => $order->order_id,
                'symbol' => $order->symbol,
                'type' => $order->type,
                'order_type' => $order->order_type,
                'amount' => $order->amount,
                'price' => $order->price,
                'filled_amount' => $order->filled_amount,
                'filled_total' => $order->filled_total,
                'avg_price' => $order->avg_price,
                'fee' => $order->fee,
                'status' => $order->status,
                'status_text' => $order->status_text,
                'created_at' => $order->created_at,
                'updated_at' => $order->updated_at
            ];
        }

        return $this->success([
            'data' => $data,
            'total' => $orders->total(),
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * 获取佣金记录
     */
    public function commissions()
    {
        $userId = $this->getUserId();
        $type = input('type', '');
        $level = input('level', '');
        $page = input('page/d', 1);
        $limit = input('limit/d', 50);

        $where = ['user_id' => $userId];
        
        if ($type) {
            $where['type'] = $type;
        }
        
        if ($level !== '') {
            $where['level'] = $level;
        }

        $commissions = Commission::where($where)
                                ->with('fromUser')
                                ->order('created_at', 'desc')
                                ->paginate([
                                    'list_rows' => $limit,
                                    'page' => $page
                                ]);

        $data = [];
        foreach ($commissions->items() as $commission) {
            $data[] = [
                'id' => $commission->id,
                'from_user_id' => $commission->from_user_id,
                'from_username' => $commission->fromUser->username ?? '',
                'type' => $commission->type,
                'type_text' => $commission->type_text,
                'level' => $commission->level,
                'level_text' => $commission->level_text,
                'rate' => $commission->rate,
                'amount' => $commission->amount,
                'coin_symbol' => $commission->coin_symbol,
                'status' => $commission->status,
                'status_text' => $commission->status_text,
                'created_at' => $commission->created_at
            ];
        }

        return $this->success([
            'data' => $data,
            'total' => $commissions->total(),
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * 获取用户统计
     */
    public function statistics()
    {
        $userId = $this->getUserId();

        // 资产统计
        $assets = UserAsset::where('user_id', $userId)->select();
        $totalValue = 0;
        $assetCount = 0;
        
        foreach ($assets as $asset) {
            if ($asset->total > 0) {
                $assetCount++;
                // 这里应该根据实时汇率计算总价值
                $totalValue += $asset->total;
            }
        }

        // 交易统计
        $totalOrders = Order::where('user_id', $userId)->count();
        $completedOrders = Order::where('user_id', $userId)->where('status', 2)->count();
        
        // 佣金统计
        $totalCommission = Commission::where('user_id', $userId)->where('status', 1)->sum('amount');
        
        // 充值提现统计
        $totalDeposit = DepositRecord::where('user_id', $userId)->where('status', 1)->sum('amount');
        $totalWithdraw = WithdrawRecord::where('user_id', $userId)->where('status', 2)->sum('amount');

        return $this->success([
            'asset_stats' => [
                'total_value' => $totalValue,
                'asset_count' => $assetCount
            ],
            'trade_stats' => [
                'total_orders' => $totalOrders,
                'completed_orders' => $completedOrders,
                'success_rate' => $totalOrders > 0 ? round($completedOrders / $totalOrders * 100, 2) : 0
            ],
            'commission_stats' => [
                'total_commission' => $totalCommission
            ],
            'finance_stats' => [
                'total_deposit' => $totalDeposit,
                'total_withdraw' => $totalWithdraw
            ]
        ]);
    }

    /**
     * 更新用户信息
     */
    public function updateInfo()
    {
        if (!$this->request->isPut() && !$this->request->isPost()) {
            return $this->error('请求方式错误');
        }

        $userId = $this->getUserId();
        $data = $this->request->post();

        $user = UserModel::find($userId);
        if (!$user) {
            return $this->error('用户不存在');
        }

        // 允许更新的字段
        $allowedFields = ['nickname', 'avatar', 'phone'];
        $updateData = [];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = $data[$field];
            }
        }

        if (empty($updateData)) {
            return $this->error('没有可更新的数据');
        }

        if ($user->save($updateData)) {
            return $this->success('更新成功');
        } else {
            return $this->error('更新失败');
        }
    }

    /**
     * 检查认证
     */
    private function checkAuth()
    {
        if (!Session::has('user_id')) {
            $this->error('未授权访问', 401);
        }
    }

    /**
     * 获取用户ID
     */
    private function getUserId()
    {
        return Session::get('user_id');
    }
}
