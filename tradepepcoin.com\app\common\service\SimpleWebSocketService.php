<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Log;
use think\facade\Cache;

/**
 * 简化WebSocket服务类 - 只包含基础实时推送功能
 */
class SimpleWebSocketService
{
    // 连接存储
    private static $connections = [];
    
    // 订阅存储
    private static $subscriptions = [];

    /**
     * 添加连接
     */
    public static function addConnection(string $connectionId, $connection): void
    {
        self::$connections[$connectionId] = $connection;
        Log::info("WebSocket连接建立", ['connection_id' => $connectionId]);
    }

    /**
     * 移除连接
     */
    public static function removeConnection(string $connectionId): void
    {
        // 移除连接
        unset(self::$connections[$connectionId]);
        
        // 移除相关订阅
        foreach (self::$subscriptions as $channel => $subscribers) {
            if (isset($subscribers[$connectionId])) {
                unset(self::$subscriptions[$channel][$connectionId]);
            }
        }
        
        Log::info("WebSocket连接断开", ['connection_id' => $connectionId]);
    }

    /**
     * 订阅频道
     */
    public static function subscribe(string $connectionId, string $channel): array
    {
        if (!isset(self::$connections[$connectionId])) {
            return ['code' => 0, 'msg' => '连接不存在'];
        }

        // 验证频道格式
        if (!self::isValidChannel($channel)) {
            return ['code' => 0, 'msg' => '无效的频道名称'];
        }

        // 添加订阅
        if (!isset(self::$subscriptions[$channel])) {
            self::$subscriptions[$channel] = [];
        }
        
        self::$subscriptions[$channel][$connectionId] = true;

        Log::info("WebSocket订阅频道", [
            'connection_id' => $connectionId,
            'channel' => $channel
        ]);

        return [
            'code' => 1,
            'msg' => '订阅成功',
            'data' => ['channel' => $channel]
        ];
    }

    /**
     * 取消订阅
     */
    public static function unsubscribe(string $connectionId, string $channel): array
    {
        if (isset(self::$subscriptions[$channel][$connectionId])) {
            unset(self::$subscriptions[$channel][$connectionId]);
            
            Log::info("WebSocket取消订阅", [
                'connection_id' => $connectionId,
                'channel' => $channel
            ]);
        }

        return [
            'code' => 1,
            'msg' => '取消订阅成功',
            'data' => ['channel' => $channel]
        ];
    }

    /**
     * 推送消息到频道
     */
    public static function pushToChannel(string $channel, array $data): int
    {
        if (!isset(self::$subscriptions[$channel])) {
            return 0;
        }

        $message = json_encode([
            'channel' => $channel,
            'data' => $data,
            'timestamp' => time()
        ]);

        $sentCount = 0;
        foreach (self::$subscriptions[$channel] as $connectionId => $subscribed) {
            if (isset(self::$connections[$connectionId])) {
                try {
                    self::$connections[$connectionId]->send($message);
                    $sentCount++;
                } catch (\Exception $e) {
                    Log::error("WebSocket推送失败", [
                        'connection_id' => $connectionId,
                        'error' => $e->getMessage()
                    ]);
                    // 移除失效连接
                    self::removeConnection($connectionId);
                }
            }
        }

        return $sentCount;
    }

    /**
     * 推送市场数据
     */
    public static function pushMarketData(string $symbol, array $data): int
    {
        $channels = [
            "ticker.{$symbol}",
            "depth.{$symbol}",
            "trades.{$symbol}"
        ];

        $totalSent = 0;
        foreach ($channels as $channel) {
            $totalSent += self::pushToChannel($channel, $data);
        }

        return $totalSent;
    }

    /**
     * 推送用户数据
     */
    public static function pushUserData(int $userId, string $type, array $data): int
    {
        $channel = "user.{$userId}.{$type}";
        return self::pushToChannel($channel, $data);
    }

    /**
     * 推送订单更新
     */
    public static function pushOrderUpdate(int $userId, array $orderData): int
    {
        return self::pushUserData($userId, 'orders', [
            'type' => 'order_update',
            'data' => $orderData
        ]);
    }

    /**
     * 推送资产更新
     */
    public static function pushAssetUpdate(int $userId, array $assetData): int
    {
        return self::pushUserData($userId, 'assets', [
            'type' => 'asset_update',
            'data' => $assetData
        ]);
    }

    /**
     * 推送交易记录
     */
    public static function pushTradeRecord(int $userId, array $tradeData): int
    {
        return self::pushUserData($userId, 'trades', [
            'type' => 'trade_record',
            'data' => $tradeData
        ]);
    }

    /**
     * 获取连接统计
     */
    public static function getConnectionStats(): array
    {
        $totalConnections = count(self::$connections);
        $totalSubscriptions = 0;
        $channelStats = [];

        foreach (self::$subscriptions as $channel => $subscribers) {
            $subscriberCount = count($subscribers);
            $totalSubscriptions += $subscriberCount;
            $channelStats[$channel] = $subscriberCount;
        }

        return [
            'total_connections' => $totalConnections,
            'total_subscriptions' => $totalSubscriptions,
            'channel_stats' => $channelStats
        ];
    }

    /**
     * 心跳检测
     */
    public static function heartbeat(): void
    {
        $heartbeatData = [
            'type' => 'heartbeat',
            'timestamp' => time()
        ];

        $message = json_encode($heartbeatData);
        $activeConnections = [];

        foreach (self::$connections as $connectionId => $connection) {
            try {
                $connection->send($message);
                $activeConnections[$connectionId] = $connection;
            } catch (\Exception $e) {
                Log::warning("心跳检测发现失效连接", [
                    'connection_id' => $connectionId,
                    'error' => $e->getMessage()
                ]);
                // 移除失效连接
                self::removeConnection($connectionId);
            }
        }

        self::$connections = $activeConnections;
    }

    /**
     * 验证频道名称
     */
    private static function isValidChannel(string $channel): bool
    {
        // 允许的频道格式：
        // ticker.BTCUSDT
        // depth.BTCUSDT
        // trades.BTCUSDT
        // user.123.orders
        // user.123.assets
        // user.123.trades
        
        $patterns = [
            '/^ticker\.[A-Z0-9]+$/',
            '/^depth\.[A-Z0-9]+$/',
            '/^trades\.[A-Z0-9]+$/',
            '/^user\.\d+\.(orders|assets|trades)$/'
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $channel)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 清理过期连接
     */
    public static function cleanup(): void
    {
        $cleanedConnections = 0;
        $cleanedSubscriptions = 0;

        // 清理无效连接
        foreach (self::$connections as $connectionId => $connection) {
            if (!$connection || $connection->getState() !== 'open') {
                self::removeConnection($connectionId);
                $cleanedConnections++;
            }
        }

        // 清理空频道
        foreach (self::$subscriptions as $channel => $subscribers) {
            if (empty($subscribers)) {
                unset(self::$subscriptions[$channel]);
                $cleanedSubscriptions++;
            }
        }

        if ($cleanedConnections > 0 || $cleanedSubscriptions > 0) {
            Log::info("WebSocket清理完成", [
                'cleaned_connections' => $cleanedConnections,
                'cleaned_subscriptions' => $cleanedSubscriptions
            ]);
        }
    }

    /**
     * 广播系统消息
     */
    public static function broadcast(array $data): int
    {
        $message = json_encode([
            'type' => 'broadcast',
            'data' => $data,
            'timestamp' => time()
        ]);

        $sentCount = 0;
        foreach (self::$connections as $connectionId => $connection) {
            try {
                $connection->send($message);
                $sentCount++;
            } catch (\Exception $e) {
                Log::error("广播消息失败", [
                    'connection_id' => $connectionId,
                    'error' => $e->getMessage()
                ]);
                self::removeConnection($connectionId);
            }
        }

        return $sentCount;
    }

    /**
     * 获取用户连接
     */
    public static function getUserConnections(int $userId): array
    {
        $userConnections = [];
        $userChannelPrefix = "user.{$userId}.";

        foreach (self::$subscriptions as $channel => $subscribers) {
            if (strpos($channel, $userChannelPrefix) === 0) {
                foreach ($subscribers as $connectionId => $subscribed) {
                    if (!in_array($connectionId, $userConnections)) {
                        $userConnections[] = $connectionId;
                    }
                }
            }
        }

        return $userConnections;
    }

    /**
     * 断开用户所有连接
     */
    public static function disconnectUser(int $userId): int
    {
        $userConnections = self::getUserConnections($userId);
        $disconnectedCount = 0;

        foreach ($userConnections as $connectionId) {
            if (isset(self::$connections[$connectionId])) {
                try {
                    self::$connections[$connectionId]->close();
                    self::removeConnection($connectionId);
                    $disconnectedCount++;
                } catch (\Exception $e) {
                    Log::error("断开用户连接失败", [
                        'user_id' => $userId,
                        'connection_id' => $connectionId,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }

        return $disconnectedCount;
    }
}
