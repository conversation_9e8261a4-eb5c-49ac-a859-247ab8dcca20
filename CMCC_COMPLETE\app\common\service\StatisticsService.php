<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Db;
use think\facade\Cache;
use think\facade\Log;
use app\common\service\UserService;

/**
 * 统计服务类
 */
class StatisticsService
{
    /**
     * 获取平台总览统计
     */
    public function getPlatformOverview(): array
    {
        $cacheKey = 'platform_overview';
        $stats = Cache::get($cacheKey);
        
        if (!$stats) {
            // 用户统计（只统计正式用户）
            $userStats = [
                'total_users' => Db::name('gvd_users')->where('user_type', UserService::USER_TYPE_FORMAL)->count(),
                'active_users' => Db::name('gvd_users')->where('user_type', UserService::USER_TYPE_FORMAL)->where('status', 1)->count(),
                'today_register' => Db::name('gvd_users')
                    ->where('user_type', UserService::USER_TYPE_FORMAL)
                    ->whereTime('created_at', 'today')
                    ->count(),
                'month_register' => Db::name('gvd_users')
                    ->where('user_type', UserService::USER_TYPE_FORMAL)
                    ->whereTime('created_at', 'month')
                    ->count()
            ];

            // 测试用户统计（单独统计，不在主要统计中显示）
            $testUserStats = [
                'total_test_users' => Db::name('gvd_users')->where('user_type', UserService::USER_TYPE_TEST)->count(),
                'active_test_users' => Db::name('gvd_users')->where('user_type', UserService::USER_TYPE_TEST)->where('status', 1)->count()
            ];
            
            // 交易统计
            $tradeStats = [
                'total_orders' => Db::name('gvd_spot_orders')->count(),
                'today_orders' => Db::name('gvd_spot_orders')->whereTime('created_at', 'today')->count(),
                'total_volume' => Db::name('gvd_spot_orders')->where('status', 2)->sum('total'),
                'today_volume' => Db::name('gvd_spot_orders')->where('status', 2)->whereTime('created_at', 'today')->sum('total')
            ];
            
            // 合约统计
            $contractStats = [
                'total_contracts' => Db::name('gvd_futures_orders')->count(),
                'today_contracts' => Db::name('gvd_futures_orders')->whereTime('created_at', 'today')->count(),
                'total_contract_volume' => Db::name('gvd_futures_orders')->sum('amount'),
                'today_contract_volume' => Db::name('gvd_futures_orders')->whereTime('created_at', 'today')->sum('amount')
            ];
            
            // 资产统计
            $assetStats = [
                'total_deposits' => Db::name('deposit_records')->where('status', 1)->sum('amount'),
                'today_deposits' => Db::name('deposit_records')->where('status', 1)->whereTime('created_at', 'today')->sum('amount'),
                'total_withdraws' => Db::name('withdraw_records')->where('status', 1)->sum('amount'),
                'today_withdraws' => Db::name('withdraw_records')->where('status', 1)->whereTime('created_at', 'today')->sum('amount')
            ];
            
            $stats = [
                'user_stats' => $userStats,
                'test_user_stats' => $testUserStats, // 测试用户单独统计
                'trade_stats' => $tradeStats,
                'contract_stats' => $contractStats,
                'asset_stats' => $assetStats
            ];
            
            Cache::set($cacheKey, $stats, 300); // 缓存5分钟
        }
        
        return $stats;
    }
    
    /**
     * 获取用户统计（只统计正式用户）
     */
    public function getUserStats(string $period = 'month'): array
    {
        $cacheKey = "user_stats_{$period}";
        $stats = Cache::get($cacheKey);
        
        if (!$stats) {
            $dateFormat = $this->getDateFormat($period);
            $days = $this->getPeriodDays($period);
            
            // 用户注册趋势（只统计正式用户）
            $registerTrend = Db::table('gvd_users')
                ->field("DATE_FORMAT(created_at, '{$dateFormat}') as date, COUNT(*) as count")
                ->where('user_type', UserService::USER_TYPE_FORMAL)
                ->where('created_at', '>=', date('Y-m-d H:i:s', time() - $days * 86400))
                ->group('date')
                ->order('date')
                ->select()
                ->toArray();
            
            // 用户活跃度（只统计正式用户）
            $activeTrend = Db::table('user_login_log')
                ->alias('ull')
                ->join('users u', 'ull.user_id = u.id')
                ->field("DATE_FORMAT(ull.created_at, '{$dateFormat}') as date, COUNT(DISTINCT ull.user_id) as count")
                ->where('u.user_type', UserService::USER_TYPE_FORMAL)
                ->where('ull.created_at', '>=', date('Y-m-d H:i:s', time() - $days * 86400))
                ->group('date')
                ->order('date')
                ->select()
                ->toArray();
            
            // 用户地区分布（只统计正式用户）
            $regionDistribution = Db::table('gvd_users')
                ->field('region, COUNT(*) as count')
                ->where('user_type', UserService::USER_TYPE_FORMAL)
                ->where('region', '<>', '')
                ->group('region')
                ->order('count', 'desc')
                ->limit(10)
                ->select()
                ->toArray();

            // 用户等级分布（只统计正式用户）
            $levelDistribution = Db::table('gvd_users')
                ->field('level, COUNT(*) as count')
                ->where('user_type', UserService::USER_TYPE_FORMAL)
                ->group('level')
                ->order('level')
                ->select()
                ->toArray();
            
            $stats = [
                'register_trend' => $registerTrend,
                'active_trend' => $activeTrend,
                'region_distribution' => $regionDistribution,
                'level_distribution' => $levelDistribution
            ];
            
            Cache::set($cacheKey, $stats, 3600); // 缓存1小时
        }
        
        return $stats;
    }
    
    /**
     * 获取代理统计（按代理分组，只统计正式用户）
     */
    public function getAgentStats(int $agentId = 0): array
    {
        $cacheKey = "agent_stats_{$agentId}";
        $stats = Cache::get($cacheKey);
        
        if (!$stats) {
            $query = Db::table('gvd_users')->where('user_type', UserService::USER_TYPE_FORMAL);
            
            if ($agentId > 0) {
                $query->where('agent_id', $agentId);
            }
            
            // 代理下的用户统计
            $userStats = [
                'total_users' => $query->count(),
                'active_users' => $query->where('status', 1)->count(),
                'today_register' => $query->whereTime('created_at', 'today')->count(),
                'month_register' => $query->whereTime('created_at', 'month')->count()
            ];
            
            // 代理下用户的交易统计
            $userIds = $query->column('id');
            
            $tradeStats = [];
            if (!empty($userIds)) {
                $tradeStats = [
                    'total_orders' => Db::name('gvd_spot_orders')->whereIn('user_id', $userIds)->count(),
                    'today_orders' => Db::name('gvd_spot_orders')->whereIn('user_id', $userIds)->whereTime('created_at', 'today')->count(),
                    'total_volume' => Db::name('gvd_spot_orders')->whereIn('user_id', $userIds)->where('status', 2)->sum('total'),
                    'today_volume' => Db::name('gvd_spot_orders')->whereIn('user_id', $userIds)->where('status', 2)->whereTime('created_at', 'today')->sum('total')
                ];
            }
            
            // 佣金统计
            $commissionStats = [
                'total_commission' => Db::name('commissions')->where('user_id', $agentId)->sum('amount'),
                'today_commission' => Db::name('commissions')->where('user_id', $agentId)->whereTime('created_at', 'today')->sum('amount'),
                'month_commission' => Db::name('commissions')->where('user_id', $agentId)->whereTime('created_at', 'month')->sum('amount')
            ];
            
            $stats = [
                'user_stats' => $userStats,
                'trade_stats' => $tradeStats,
                'commission_stats' => $commissionStats
            ];
            
            Cache::set($cacheKey, $stats, 1800); // 缓存30分钟
        }
        
        return $stats;
    }
    
    /**
     * 获取财务统计
     */
    public function getFinanceStats(string $period = 'month'): array
    {
        $cacheKey = "finance_stats_{$period}";
        $stats = Cache::get($cacheKey);
        
        if (!$stats) {
            $dateFormat = $this->getDateFormat($period);
            $days = $this->getPeriodDays($period);
            
            // 充值统计
            $depositTrend = Db::table('deposit_records')
                ->field("DATE_FORMAT(created_at, '{$dateFormat}') as date, SUM(amount) as amount, COUNT(*) as count")
                ->where('status', 1)
                ->where('created_at', '>=', date('Y-m-d H:i:s', time() - $days * 86400))
                ->group('date')
                ->order('date')
                ->select()
                ->toArray();
            
            // 提现统计
            $withdrawTrend = Db::table('withdraw_records')
                ->field("DATE_FORMAT(created_at, '{$dateFormat}') as date, SUM(amount) as amount, COUNT(*) as count")
                ->where('status', 1)
                ->where('created_at', '>=', date('Y-m-d H:i:s', time() - $days * 86400))
                ->group('date')
                ->order('date')
                ->select()
                ->toArray();
            
            // 手续费统计
            $feeStats = [
                'total_trade_fee' => Db::name('gvd_spot_orders')->where('status', 2)->sum('fee'),
                'total_withdraw_fee' => 0, // 暂时设为0，因为没有withdraw_records表
                'today_trade_fee' => Db::name('gvd_spot_orders')->where('status', 2)->whereTime('created_at', 'today')->sum('fee'),
                'today_withdraw_fee' => 0 // 暂时设为0，因为没有withdraw_records表
            ];
            
            $stats = [
                'deposit_trend' => $depositTrend,
                'withdraw_trend' => $withdrawTrend,
                'fee_stats' => $feeStats
            ];
            
            Cache::set($cacheKey, $stats, 3600); // 缓存1小时
        }
        
        return $stats;
    }
    
    /**
     * 获取日期格式
     */
    private function getDateFormat(string $period): string
    {
        switch ($period) {
            case 'day':
                return '%Y-%m-%d %H:00:00';
            case 'week':
            case 'month':
                return '%Y-%m-%d';
            case 'year':
                return '%Y-%m';
            default:
                return '%Y-%m-%d';
        }
    }
    
    /**
     * 获取周期天数
     */
    private function getPeriodDays(string $period): int
    {
        switch ($period) {
            case 'day':
                return 1;
            case 'week':
                return 7;
            case 'month':
                return 30;
            case 'year':
                return 365;
            default:
                return 30;
        }
    }
    
    /**
     * 清除统计缓存
     */
    public function clearStatsCache(): void
    {
        $cacheKeys = [
            'platform_overview',
            'user_stats_day',
            'user_stats_week', 
            'user_stats_month',
            'user_stats_year',
            'finance_stats_day',
            'finance_stats_week',
            'finance_stats_month',
            'finance_stats_year'
        ];
        
        foreach ($cacheKeys as $key) {
            Cache::delete($key);
        }
        
        // 清除代理统计缓存
        $agentIds = Db::name('users')->where('role', 'agent')->column('id');
        foreach ($agentIds as $agentId) {
            Cache::delete("agent_stats_{$agentId}");
        }
    }
}
