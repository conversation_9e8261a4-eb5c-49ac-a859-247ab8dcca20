<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\common\service\WalletService;
use think\response\Json;

/**
 * 钱包控制器
 * 兼容ThinkPHP 6.1和PHP 8.1
 */
class Wallet extends BaseController
{
    protected $walletService;

    protected function initialize(): void
    {
        parent::initialize();
        $this->walletService = new WalletService();
    }

    /**
     * 获取用户余额
     */
    public function getBalance(): Json
    {
        try {
            $coinSymbol = $this->request->param('coin_symbol', 'USDT');
            $walletType = $this->request->param('wallet_type', 'spot');

            $result = $this->walletService->getUserBalance($this->userId, $coinSymbol, $walletType);

            return json($result);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取余额失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取充值地址（简化版）
     */
    public function getDepositAddress(): Json
    {
        try {
            $coinSymbol = $this->request->param('coin_symbol', 'USDT');

            // 简化实现，返回固定地址
            $addresses = [
                'USDT' => '******************************************',
                'BTC' => '**********************************',
                'ETH' => '******************************************'
            ];

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'platform_address' => $addresses[$coinSymbol] ?? '',
                    'coin_symbol' => $coinSymbol
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取充值地址失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取充值记录（使用现有表）
     */
    public function getDepositRecords(): Json
    {
        try {
            $page = (int)$this->request->param('page', 1);
            $limit = (int)$this->request->param('limit', 20);
            $coinSymbol = $this->request->param('coin_symbol');

            // 使用现有的资产变动记录表
            $query = db('gvd_asset_records')
                ->where('user_id', $this->userId)
                ->where('type', 'deposit')
                ->order('id desc');

            if ($coinSymbol) {
                $query->where('coin_symbol', $coinSymbol);
            }

            $total = $query->count();
            $records = $query->page($page, $limit)->select()->toArray();

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'list' => $records,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取充值记录失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取交易记录
     */
    public function getTransactions(): Json
    {
        try {
            $page = (int)$this->request->param('page', 1);
            $limit = (int)$this->request->param('limit', 20);
            $coinSymbol = $this->request->param('coin_symbol');
            $type = $this->request->param('type');

            $query = db('gvd_asset_records')
                ->where('user_id', $this->userId)
                ->order('id desc');

            if ($coinSymbol) {
                $query->where('coin_symbol', $coinSymbol);
            }

            if ($type) {
                $query->where('type', $type);
            }

            $total = $query->count();
            $transactions = $query->page($page, $limit)->select()->toArray();

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'list' => $transactions,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取交易记录失败：' . $e->getMessage()]);
        }
    }

    /**
     * 余额转账（简化版）
     */
    public function transfer(): Json
    {
        try {
            $coinSymbol = $this->request->param('coin_symbol', 'USDT');
            $amount = (float)$this->request->param('amount');
            $type = $this->request->param('type', 'internal'); // 内部转账

            if (empty($coinSymbol) || $amount <= 0) {
                return json(['code' => 0, 'msg' => '参数错误']);
            }

            // 检查余额
            $balance = $this->walletService->getUserBalance($this->userId, $coinSymbol);
            if ($balance['code'] !== 1 || $balance['data']['available'] < $amount) {
                return json(['code' => 0, 'msg' => '余额不足']);
            }

            // 执行转账（简化实现）
            $result = $this->walletService->addBalance(
                $this->userId,
                $coinSymbol,
                0, // 不改变余额，只记录流水
                'transfer',
                "内部转账 {$amount} {$coinSymbol}"
            );

            return json([
                'code' => 1,
                'msg' => '转账成功',
                'data' => [
                    'amount' => $amount,
                    'coin_symbol' => $coinSymbol,
                    'type' => $type
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '转账失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取钱包统计信息（简化版）
     */
    public function getWalletStats(): Json
    {
        try {
            // 获取USDT余额
            $balance = $this->walletService->getUserBalance($this->userId, 'USDT');
            $totalBalance = $balance['code'] === 1 ? $balance['data']['total'] : 0;

            // 获取今日盈亏
            $todayStart = date('Y-m-d 00:00:00');
            $todayPnl = db('gvd_asset_records')
                ->where('user_id', $this->userId)
                ->where('type', 'trade')
                ->where('created_at', '>=', $todayStart)
                ->sum('amount');

            // 获取总盈亏
            $totalPnl = db('gvd_asset_records')
                ->where('user_id', $this->userId)
                ->where('type', 'trade')
                ->sum('amount');

            $stats = [
                'total_balance' => $totalBalance,
                'available_balance' => $balance['code'] === 1 ? $balance['data']['available'] : 0,
                'frozen_balance' => $balance['code'] === 1 ? $balance['data']['frozen'] : 0,
                'today_pnl' => $todayPnl ?: 0,
                'total_pnl' => $totalPnl ?: 0
            ];

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取统计信息失败：' . $e->getMessage()]);
        }
    }
}
