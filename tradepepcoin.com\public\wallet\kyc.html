<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实名认证 - GVD</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #1a1a1a; color: #fff; }
        
        .header { background: #2d2d2d; padding: 15px 0; border-bottom: 1px solid #333; }
        .container { max-width: 800px; margin: 0 auto; padding: 0 20px; }
        .nav { display: flex; justify-content: space-between; align-items: center; }
        .logo { font-size: 24px; font-weight: bold; color: #007bff; }
        .back-link { color: #999; text-decoration: none; }
        
        .main { padding: 30px 0; }
        .kyc-form { background: #2d2d2d; padding: 40px; border-radius: 10px; }
        .form-group { margin-bottom: 25px; }
        .form-group label { display: block; margin-bottom: 8px; color: #ccc; font-weight: bold; }
        .form-group input, .form-group select { 
            width: 100%; padding: 12px; background: #1a1a1a; border: 1px solid #333; 
            color: #fff; border-radius: 5px; font-size: 16px; 
        }
        .form-group input:focus, .form-group select:focus { outline: none; border-color: #007bff; }
        
        .upload-area { 
            border: 2px dashed #333; padding: 40px; text-align: center; border-radius: 8px; 
            cursor: pointer; transition: border-color 0.3s; 
        }
        .upload-area:hover { border-color: #007bff; }
        .upload-area.dragover { border-color: #007bff; background: rgba(0,123,255,0.1); }
        
        .btn { 
            width: 100%; padding: 15px; background: #007bff; color: #fff; border: none; 
            border-radius: 5px; font-size: 16px; font-weight: bold; cursor: pointer; 
        }
        .btn:hover { background: #0056b3; }
        
        .status-badge { 
            display: inline-block; padding: 5px 15px; border-radius: 15px; font-size: 12px; 
            font-weight: bold; 
        }
        .status-pending { background: #ffc107; color: #000; }
        .status-approved { background: #28a745; color: #fff; }
        .status-rejected { background: #dc3545; color: #fff; }
        
        .preview-image { max-width: 200px; max-height: 200px; border-radius: 8px; margin-top: 10px; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="nav">
                <div class="logo">🔐 实名认证</div>
                <a href="/wallet/" class="back-link">← 返回钱包</a>
            </div>
        </div>
    </div>

    <div class="main">
        <div class="container">
            <div class="kyc-form">
                <h2 style="margin-bottom: 30px;">身份验证</h2>
                <p style="color: #999; margin-bottom: 30px;">
                    为了保障您的资金安全，请完成身份验证。认证后可享受更高的提现额度。
                </p>

                <form id="kycForm">
                    <div class="form-group">
                        <label>真实姓名</label>
                        <input type="text" id="realName" placeholder="请输入真实姓名" required>
                    </div>

                    <div class="form-group">
                        <label>证件类型</label>
                        <select id="idType" required>
                            <option value="">请选择证件类型</option>
                            <option value="1">护照</option>
                            <option value="2">驾驶证</option>
                            <option value="3">SSN</option>
                            <option value="4">身份ID</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>证件号码</label>
                        <input type="text" id="idNumber" placeholder="请输入证件号码" required>
                    </div>

                    <div class="form-group">
                        <label>证件正面照片</label>
                        <div class="upload-area" onclick="document.getElementById('frontImage').click()">
                            <p>点击或拖拽上传证件正面照片</p>
                            <p style="color: #999; font-size: 12px;">支持 JPG、PNG 格式，文件大小不超过 5MB</p>
                        </div>
                        <input type="file" id="frontImage" accept="image/*" style="display: none;">
                        <img id="frontPreview" class="preview-image" style="display: none;">
                    </div>

                    <div class="form-group">
                        <label>证件背面照片</label>
                        <div class="upload-area" onclick="document.getElementById('backImage').click()">
                            <p>点击或拖拽上传证件背面照片</p>
                            <p style="color: #999; font-size: 12px;">支持 JPG、PNG 格式，文件大小不超过 5MB</p>
                        </div>
                        <input type="file" id="backImage" accept="image/*" style="display: none;">
                        <img id="backPreview" class="preview-image" style="display: none;">
                    </div>

                    <button type="submit" class="btn">提交认证申请</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 检查登录状态
        function checkLogin() {
            const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
            if (!isLoggedIn) {
                alert('请先登录');
                window.location.href = '/auth/login.html';
                return false;
            }
            return true;
        }

        // 图片预览
        function setupImagePreview(inputId, previewId) {
            document.getElementById(inputId).addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const preview = document.getElementById(previewId);
                        preview.src = e.target.result;
                        preview.style.display = 'block';
                    };
                    reader.readAsDataURL(file);
                }
            });
        }

        // 表单提交
        document.getElementById('kycForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                realName: document.getElementById('realName').value,
                idType: document.getElementById('idType').value,
                idNumber: document.getElementById('idNumber').value,
                frontImage: document.getElementById('frontImage').files[0],
                backImage: document.getElementById('backImage').files[0]
            };

            // 验证
            if (!formData.realName || !formData.idType || !formData.idNumber) {
                alert('请填写完整信息');
                return;
            }

            if (!formData.frontImage || !formData.backImage) {
                alert('请上传证件照片');
                return;
            }

            // 这里应该调用API提交认证申请
            console.log('提交认证申请:', formData);
            alert('认证申请已提交，请等待审核');
        });

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            if (!checkLogin()) return;
            
            setupImagePreview('frontImage', 'frontPreview');
            setupImagePreview('backImage', 'backPreview');
        });
    </script>
</body>
</html>
