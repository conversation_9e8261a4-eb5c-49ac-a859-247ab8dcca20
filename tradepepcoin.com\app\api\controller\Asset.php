<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\common\service\WalletService;
use app\common\service\UserService;
use think\Request;

/**
 * 资产管理控制器
 */
class Asset extends BaseController
{
    protected $walletService;
    protected $userService;

    public function __construct()
    {
        parent::__construct();
        $this->walletService = new WalletService();
        $this->userService = new UserService();
    }

    /**
     * 获取不需要验证的方法
     */
    protected function getNoAuthMethods(): array
    {
        return [
            'processAutoDeposit'
        ];
    }

    /**
     * 获取用户资产列表
     */
    public function getBalance(Request $request)
    {
        $userId = $this->getUserId();
        $coinSymbol = $request->get('coin_symbol', '');
        
        $result = $this->walletService->getUserAssets($userId, $coinSymbol);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取充值地址（取消大额公共地址逻辑）
     */
    public function getDepositAddress(Request $request)
    {
        $userId = $this->getUserId();
        $coinSymbol = $request->get('coin_symbol');
        
        if (empty($coinSymbol)) {
            return $this->error('币种不能为空');
        }

        // 为每个用户生成独立充值地址，不再使用公共地址
        $address = $this->walletService->getDepositAddress($userId, $coinSymbol);
        
        if ($address) {
            return $this->success([
                'coin_symbol' => $coinSymbol,
                'address' => $address,
                'qr_code' => $this->generateQRCode($address)
            ]);
        } else {
            return $this->error('获取充值地址失败');
        }
    }

    /**
     * 创建充值记录
     */
    public function createDeposit(Request $request)
    {
        $userId = $this->getUserId();
        $data = $request->post();
        
        // 验证必填字段
        $validate = [
            'coin_symbol' => 'require',
            'amount' => 'require|float|gt:0',
            'txid' => 'require'
        ];

        $result = $this->validate($data, $validate);
        if ($result !== true) {
            return $this->error($result);
        }

        $result = $this->walletService->createDepositRecord($userId, $data);
        
        if ($result['code']) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取充值记录
     */
    public function getDepositHistory(Request $request)
    {
        $userId = $this->getUserId();
        $filters = [
            'coin_symbol' => $request->get('coin_symbol', ''),
            'status' => $request->get('status', ''),
            'start_date' => $request->get('start_date', ''),
            'end_date' => $request->get('end_date', ''),
            'page' => (int)$request->get('page', 1),
            'limit' => (int)$request->get('limit', 20)
        ];

        $result = $this->walletService->getDepositHistory($userId, $filters);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 创建提币申请
     */
    public function createWithdraw(Request $request)
    {
        $userId = $this->getUserId();
        $data = $request->post();
        
        // 验证必填字段
        $validate = [
            'coin_symbol' => 'require',
            'amount' => 'require|float|gt:0',
            'address' => 'require',
            'password' => 'require'
        ];

        $result = $this->validate($data, $validate);
        if ($result !== true) {
            return $this->error($result);
        }

        $result = $this->walletService->createWithdrawRecord($userId, $data);
        
        if ($result['code']) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取提币记录
     */
    public function getWithdrawHistory(Request $request)
    {
        $userId = $this->getUserId();
        $filters = [
            'coin_symbol' => $request->get('coin_symbol', ''),
            'status' => $request->get('status', ''),
            'start_date' => $request->get('start_date', ''),
            'end_date' => $request->get('end_date', ''),
            'page' => (int)$request->get('page', 1),
            'limit' => (int)$request->get('limit', 20)
        ];

        $result = $this->walletService->getWithdrawHistory($userId, $filters);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 取消提币申请
     */
    public function cancelWithdraw(Request $request)
    {
        $userId = $this->getUserId();
        $withdrawId = (int)$request->param('withdraw_id');
        
        if (!$withdrawId) {
            return $this->error('提币ID不能为空');
        }

        $result = $this->walletService->cancelWithdraw($userId, $withdrawId);
        
        if ($result['code']) {
            return $this->success([], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取支持的币种列表
     */
    public function getSupportedCoins(Request $request)
    {
        $type = $request->get('type', 'all'); // all, deposit, withdraw
        
        $result = $this->walletService->getSupportedCoins($type);
        
        return $this->success($result);
    }

    /**
     * 获取币种信息
     */
    public function getCoinInfo(Request $request)
    {
        $coinSymbol = $request->get('coin_symbol');
        
        if (empty($coinSymbol)) {
            return $this->error('币种不能为空');
        }

        $result = $this->walletService->getCoinInfo($coinSymbol);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取资产统计
     */
    public function getAssetStats(Request $request)
    {
        $userId = $this->getUserId();
        
        $result = $this->walletService->getAssetStats($userId);
        
        return $this->success($result);
    }

    /**
     * 获取财务流水
     */
    public function getFinancialRecords(Request $request)
    {
        $userId = $this->getUserId();
        $filters = [
            'coin_symbol' => $request->get('coin_symbol', ''),
            'type' => $request->get('type', ''),
            'start_date' => $request->get('start_date', ''),
            'end_date' => $request->get('end_date', ''),
            'page' => (int)$request->get('page', 1),
            'limit' => (int)$request->get('limit', 20)
        ];

        $result = $this->walletService->getFinancialRecords($userId, $filters);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 内部转账
     */
    public function internalTransfer(Request $request)
    {
        $userId = $this->getUserId();
        $data = $request->post();
        
        // 验证必填字段
        $validate = [
            'to_username' => 'require',
            'coin_symbol' => 'require',
            'amount' => 'require|float|gt:0',
            'password' => 'require'
        ];

        $result = $this->validate($data, $validate);
        if ($result !== true) {
            return $this->error($result);
        }

        $result = $this->walletService->internalTransfer($userId, $data);
        
        if ($result['code']) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 生成二维码
     */
    private function generateQRCode(string $content): string
    {
        // 这里应该集成二维码生成库
        // 暂时返回base64编码的内容
        return 'data:image/png;base64,' . base64_encode($content);
    }

    /**
     * 处理自动充值到账（Webhook）
     */
    public function processAutoDeposit(Request $request)
    {
        $data = $request->post();
        
        // 验证webhook签名
        $signature = $request->header('X-Signature');
        if (!$this->verifyWebhookSignature($data, $signature)) {
            return $this->error('签名验证失败', 401);
        }

        // 验证必填字段
        $validate = [
            'txid' => 'require',
            'address' => 'require',
            'amount' => 'require|float|gt:0',
            'confirmations' => 'integer'
        ];

        $result = $this->validate($data, $validate);
        if ($result !== true) {
            return $this->error($result);
        }

        $result = $this->walletService->processAutoDeposit($data);
        
        if ($result['code']) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 验证Webhook签名
     */
    private function verifyWebhookSignature(array $data, string $signature): bool
    {
        $secret = config('wallet.webhook_secret', '');
        if (empty($secret)) {
            return true; // 如果没有配置密钥，跳过验证
        }

        $payload = json_encode($data);
        $expectedSignature = hash_hmac('sha256', $payload, $secret);
        
        return hash_equals($expectedSignature, $signature);
    }
}
