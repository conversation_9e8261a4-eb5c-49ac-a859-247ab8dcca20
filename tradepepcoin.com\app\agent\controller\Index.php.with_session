<?php
declare(strict_types=1);

namespace app\agent\controller;

use app\BaseController;
use think\facade\Session;

class Index extends BaseController
{
    public function initialize()
    {
        parent::initialize();
        // 检查代理商登录状态
        if (!Session::has('agent_id')) {
            $this->redirect('/agent/login');
        }
    }
    
    public function index()
    {
        return view('agent/index');
    }
    
    public function users()
    {
        return view('agent/users');
    }
    
    public function orders()
    {
        return view('agent/orders');
    }
    
    public function commissions()
    {
        return view('agent/commissions');
    }

    public function dashboard()
    {
        // 直接调用Dashboard控制器的index方法
        $dashboard = new \app\agent\controller\Dashboard();
        // 跳过Dashboard的initialize，因为我们已经检查了Session
        return $dashboard->index();
    }

    public function customerService()
    {
        // 直接调用Dashboard控制器的customerService方法
        $dashboard = new \app\agent\controller\Dashboard();
        // 跳过Dashboard的initialize，因为我们已经检查了Session
        return $dashboard->customerService();
    }

    public function test()
    {
        return json(['status' => 'success', 'message' => 'Index控制器工作正常', 'agent_id' => Session::get('agent_id')]);
    }
}
