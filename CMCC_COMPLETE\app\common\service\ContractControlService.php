<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Db;
use think\facade\Log;

/**
 * 合约控制服务 - 适配现有数据库
 */
class ContractControlService
{
    const CONTROL_NONE = 'none';
    const CONTROL_WIN = 'win';
    const CONTROL_LOSE = 'lose';
    const CONTROL_RANDOM = 'random';

    /**
     * 设置用户控制策略
     */
    public function setUserControl(int $userId, string $controlType, array $options = [], int $operatorId = 0): array
    {
        try {
            $user = Db::name('gvd_users')->where('id', $userId)->find();
            if (!$user) {
                return ['code' => 0, 'msg' => '用户不存在'];
            }

            $validTypes = [self::CONTROL_NONE, self::CONTROL_WIN, self::CONTROL_LOSE, self::CONTROL_RANDOM];
            if (!in_array($controlType, $validTypes)) {
                return ['code' => 0, 'msg' => '无效的控制类型'];
            }

            $controlConfig = [
                'control_type' => $controlType,
                'win_rate' => $options['win_rate'] ?? 50,
                'operator_id' => $operatorId,
                'created_at' => date('Y-m-d H:i:s')
            ];

            $configKey = "user_control_{$userId}";
            $existingConfig = Db::name('gvd_system_config')
                ->where('type', 'user_control')
                ->where('key', $configKey)
                ->find();

            if ($existingConfig) {
                Db::name('gvd_system_config')
                    ->where('id', $existingConfig['id'])
                    ->update([
                        'value' => json_encode($controlConfig, JSON_UNESCAPED_UNICODE),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            } else {
                Db::name('gvd_system_config')->insert([
                    'type' => 'user_control',
                    'key' => $configKey,
                    'value' => json_encode($controlConfig, JSON_UNESCAPED_UNICODE),
                    'description' => "用户{$userId}的合约控制策略",
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }

            return [
                'code' => 1,
                'msg' => '控制策略设置成功',
                'data' => $controlConfig
            ];

        } catch (\Exception $e) {
            Log::error('设置用户控制策略失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '设置失败'];
        }
    }

    /**
     * 获取用户控制策略
     */
    public function getUserControl(int $userId): array
    {
        try {
            $configKey = "user_control_{$userId}";
            $config = Db::name('gvd_system_config')
                ->where('type', 'user_control')
                ->where('key', $configKey)
                ->find();

            if (!$config) {
                return [
                    'code' => 1,
                    'msg' => '获取成功',
                    'data' => [
                        'control_type' => self::CONTROL_NONE,
                        'win_rate' => 50
                    ]
                ];
            }

            $controlData = json_decode($config['value'], true) ?: [];

            return [
                'code' => 1,
                'msg' => '获取成功',
                'data' => $controlData
            ];

        } catch (\Exception $e) {
            Log::error('获取用户控制策略失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 批量设置用户控制
     */
    public function batchSetUserControl(array $userIds, string $controlType, array $options = [], int $operatorId = 0): array
    {
        try {
            $successCount = 0;
            $failedUsers = [];

            foreach ($userIds as $userId) {
                $result = $this->setUserControl($userId, $controlType, $options, $operatorId);
                if ($result['code'] === 1) {
                    $successCount++;
                } else {
                    $failedUsers[] = $userId;
                }
            }

            return [
                'code' => 1,
                'msg' => '批量设置完成',
                'data' => [
                    'total' => count($userIds),
                    'success' => $successCount,
                    'failed' => count($failedUsers)
                ]
            ];

        } catch (\Exception $e) {
            Log::error('批量设置用户控制失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '批量设置失败'];
        }
    }

    /**
     * 处理期货订单结算
     */
    public function processFuturesSettlement(int $orderId): array
    {
        try {
            $order = Db::name('gvd_futures_orders')->where('id', $orderId)->find();
            if (!$order || $order['status'] !== 'open') {
                return ['code' => 0, 'msg' => '订单不存在或已结算'];
            }

            $controlResult = $this->getUserControl($order['user_id']);
            $control = $controlResult['data'] ?? [];

            $marketPrice = $this->getCurrentMarketPrice($order['symbol']);
            $settlementPrice = $this->calculateSettlementPrice($order, $control, $marketPrice);
            $pnl = $this->calculatePnL($order, $settlementPrice);

            Db::startTrans();
            try {
                Db::name('gvd_futures_orders')
                    ->where('id', $orderId)
                    ->update([
                        'close_price' => $settlementPrice,
                        'pnl' => $pnl,
                        'status' => 'closed',
                        'close_time' => time(),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);

                if ($pnl != 0) {
                    $walletService = new WalletService();
                    $walletService->addBalance(
                        $order['user_id'],
                        'USDT',
                        $pnl,
                        WalletService::TRANSACTION_TRADE,
                        "期货交易结算 订单#{$orderId}"
                    );
                }

                Db::commit();

                return [
                    'code' => 1,
                    'msg' => '结算成功',
                    'data' => [
                        'order_id' => $orderId,
                        'settlement_price' => $settlementPrice,
                        'pnl' => $pnl
                    ]
                ];

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('处理期货订单结算失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '结算失败'];
        }
    }

    private function getCurrentMarketPrice(string $symbol): float
    {
        return 50000.0; // 简化实现
    }

    private function calculateSettlementPrice(array $order, array $control, float $marketPrice): float
    {
        $controlType = $control['control_type'] ?? self::CONTROL_NONE;

        if ($controlType === self::CONTROL_NONE) {
            return $marketPrice;
        }

        $openPrice = (float)$order['open_price'];
        $side = $order['side'];
        $volatility = mt_rand(100, 500) / 10000;
        $priceRange = $openPrice * $volatility;

        switch ($controlType) {
            case self::CONTROL_WIN:
                return $side === 'buy' ? $openPrice + $priceRange : $openPrice - $priceRange;
            case self::CONTROL_LOSE:
                return $side === 'buy' ? $openPrice - $priceRange : $openPrice + $priceRange;
            case self::CONTROL_RANDOM:
                $winRate = $control['win_rate'] ?? 50;
                $shouldWin = mt_rand(1, 100) <= $winRate;
                if ($shouldWin) {
                    return $side === 'buy' ? $openPrice + $priceRange : $openPrice - $priceRange;
                } else {
                    return $side === 'buy' ? $openPrice - $priceRange : $openPrice + $priceRange;
                }
            default:
                return $marketPrice;
        }
    }

    private function calculatePnL(array $order, float $settlementPrice): float
    {
        $openPrice = (float)$order['open_price'];
        $quantity = (float)$order['quantity'];
        $side = $order['side'];

        if ($side === 'buy') {
            return ($settlementPrice - $openPrice) * $quantity;
        } else {
            return ($openPrice - $settlementPrice) * $quantity;
        }
    }

    public function getControlStats(array $params = []): array
    {
        try {
            $controlledUsers = Db::name('gvd_system_config')
                ->where('type', 'user_control')
                ->count();

            $todayOrders = Db::name('gvd_futures_orders')
                ->where('created_at', '>=', date('Y-m-d 00:00:00'))
                ->count();

            return [
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'controlled_users' => $controlledUsers,
                    'today_orders' => $todayOrders,
                    'platform_win_rate' => 65.8
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取控制统计失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }
}
