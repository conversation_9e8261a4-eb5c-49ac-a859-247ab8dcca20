<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\User;
use app\common\model\Deposit;
use app\common\model\Withdrawal;
use app\common\model\UserAsset;
use think\facade\Db;
use think\facade\Log;

/**
 * 充值提现服务类 - 兼容老系统逻辑
 */
class DepositWithdrawService
{
    protected $assetService;
    
    public function __construct()
    {
        $this->assetService = new AssetService();
    }
    
    /**
     * 创建充值订单 - 兼容老系统
     */
    public function createDeposit(int $userId, array $data): array
    {
        try {
            $user = User::find($userId);
            if (!$user) {
                return ['code' => 0, 'msg' => '用户不存在'];
            }
            
            $amount = floatval($data['amount']);
            $coinSymbol = strtoupper($data['coin_symbol']);
            $address = $data['address'] ?? '';
            $txHash = $data['tx_hash'] ?? '';
            
            if ($amount <= 0) {
                return ['code' => 0, 'msg' => '充值金额必须大于0'];
            }
            
            // 创建充值记录
            $deposit = Deposit::create([
                'user_id' => $userId,
                'username' => $user->username,
                'coin_symbol' => $coinSymbol,
                'amount' => $amount,
                'address' => $address,
                'tx_hash' => $txHash,
                'status' => 1, // 待确认
                'created_at' => date('Y-m-d H:i:s'),
            ]);
            
            return [
                'code' => 1,
                'msg' => '充值申请已提交',
                'data' => ['deposit_id' => $deposit->id]
            ];
            
        } catch (\Exception $e) {
            Log::error('创建充值订单失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '充值申请失败'];
        }
    }
    
    /**
     * 确认充值 - 管理员操作
     */
    public function confirmDeposit(int $depositId, int $adminId): array
    {
        try {
            $deposit = Deposit::find($depositId);
            if (!$deposit) {
                return ['code' => 0, 'msg' => '充值记录不存在'];
            }
            
            if ($deposit->status != 1) {
                return ['code' => 0, 'msg' => '充值状态不允许确认'];
            }
            
            Db::startTrans();
            
            // 增加用户资产
            $result = $this->assetService->addAsset(
                $deposit->user_id,
                $deposit->coin_symbol,
                $deposit->amount,
                '充值到账',
                9
            );
            
            if ($result['code'] != 1) {
                Db::rollback();
                return $result;
            }
            
            // 更新充值状态
            $deposit->status = 2; // 已确认
            $deposit->confirmed_at = date('Y-m-d H:i:s');
            $deposit->confirmed_by = $adminId;
            $deposit->save();
            
            Db::commit();
            
            return ['code' => 1, 'msg' => '充值确认成功'];
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('确认充值失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '确认失败'];
        }
    }
    
    /**
     * 创建提现申请 - 兼容老系统
     */
    public function createWithdrawal(int $userId, array $data): array
    {
        try {
            $user = User::find($userId);
            if (!$user) {
                return ['code' => 0, 'msg' => '用户不存在'];
            }
            
            if (!$user->canWithdraw()) {
                return ['code' => 0, 'msg' => '您的账户暂时无法提现'];
            }
            
            $amount = floatval($data['amount']);
            $coinSymbol = strtoupper($data['coin_symbol']);
            $address = $data['address'];
            $payPassword = $data['pay_password'] ?? '';
            
            if ($amount <= 0) {
                return ['code' => 0, 'msg' => '提现金额必须大于0'];
            }
            
            if (empty($address)) {
                return ['code' => 0, 'msg' => '提现地址不能为空'];
            }
            
            // 验证交易密码
            if (!$user->verifyPayPassword($payPassword)) {
                return ['code' => 0, 'msg' => '交易密码错误'];
            }
            
            // 检查余额
            $asset = UserAsset::getOrCreate($userId, $coinSymbol);
            if ($asset->available < $amount) {
                return ['code' => 0, 'msg' => $coinSymbol . '余额不足'];
            }
            
            // 计算手续费
            $fee = $this->getWithdrawFee($coinSymbol, $amount);
            $actualAmount = $amount - $fee;
            
            if ($actualAmount <= 0) {
                return ['code' => 0, 'msg' => '提现金额过小'];
            }
            
            Db::startTrans();
            
            // 冻结资产
            $result = $this->assetService->freezeAsset($userId, $coinSymbol, $amount, '提现申请冻结');
            if ($result['code'] != 1) {
                Db::rollback();
                return $result;
            }
            
            // 创建提现记录
            $withdrawal = Withdrawal::create([
                'user_id' => $userId,
                'username' => $user->username,
                'coin_symbol' => $coinSymbol,
                'amount' => $amount,
                'fee' => $fee,
                'actual_amount' => $actualAmount,
                'address' => $address,
                'status' => 1, // 待审核
                'created_at' => date('Y-m-d H:i:s'),
            ]);
            
            Db::commit();
            
            return [
                'code' => 1,
                'msg' => '提现申请已提交',
                'data' => [
                    'withdrawal_id' => $withdrawal->id,
                    'fee' => $fee,
                    'actual_amount' => $actualAmount
                ]
            ];
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('创建提现申请失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '提现申请失败'];
        }
    }
    
    /**
     * 审核提现申请 - 管理员操作
     */
    public function reviewWithdrawal(int $withdrawalId, int $status, int $adminId, string $remark = ''): array
    {
        try {
            $withdrawal = Withdrawal::find($withdrawalId);
            if (!$withdrawal) {
                return ['code' => 0, 'msg' => '提现记录不存在'];
            }
            
            if ($withdrawal->status != 1) {
                return ['code' => 0, 'msg' => '提现状态不允许审核'];
            }
            
            Db::startTrans();
            
            if ($status == 2) {
                // 审核通过 - 扣除冻结资产
                $result = $this->assetService->subFrozenAsset(
                    $withdrawal->user_id,
                    $withdrawal->coin_symbol,
                    $withdrawal->amount,
                    '提现审核通过'
                );
                
                if ($result['code'] != 1) {
                    Db::rollback();
                    return $result;
                }
                
                // 扣除手续费
                if ($withdrawal->fee > 0) {
                    $this->assetService->addAsset(
                        $withdrawal->user_id,
                        $withdrawal->coin_symbol,
                        -$withdrawal->fee,
                        '提现手续费',
                        13
                    );
                }
                
            } elseif ($status == 3) {
                // 审核拒绝 - 解冻资产
                $result = $this->assetService->unfreezeAsset(
                    $withdrawal->user_id,
                    $withdrawal->coin_symbol,
                    $withdrawal->amount,
                    '提现申请被拒绝'
                );
                
                if ($result['code'] != 1) {
                    Db::rollback();
                    return $result;
                }
            }
            
            // 更新提现状态
            $withdrawal->status = $status;
            $withdrawal->reviewed_at = date('Y-m-d H:i:s');
            $withdrawal->reviewed_by = $adminId;
            $withdrawal->remark = $remark;
            $withdrawal->save();
            
            Db::commit();
            
            $statusText = $status == 2 ? '通过' : '拒绝';
            return ['code' => 1, 'msg' => '提现审核' . $statusText . '成功'];
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('审核提现申请失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '审核失败'];
        }
    }
    
    /**
     * 获取用户充值记录
     */
    public function getUserDeposits(int $userId, array $params = []): array
    {
        $where = ['user_id' => $userId];
        
        if (!empty($params['coin_symbol'])) {
            $where['coin_symbol'] = $params['coin_symbol'];
        }
        
        if (!empty($params['status'])) {
            $where['status'] = $params['status'];
        }
        
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 20;
        
        $deposits = Deposit::where($where)
                          ->order('created_at', 'desc')
                          ->paginate([
                              'list_rows' => $limit,
                              'page' => $page
                          ]);
        
        return [
            'code' => 1,
            'data' => $deposits->items(),
            'total' => $deposits->total()
        ];
    }
    
    /**
     * 获取用户提现记录
     */
    public function getUserWithdrawals(int $userId, array $params = []): array
    {
        $where = ['user_id' => $userId];
        
        if (!empty($params['coin_symbol'])) {
            $where['coin_symbol'] = $params['coin_symbol'];
        }
        
        if (!empty($params['status'])) {
            $where['status'] = $params['status'];
        }
        
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 20;
        
        $withdrawals = Withdrawal::where($where)
                                ->order('created_at', 'desc')
                                ->paginate([
                                    'list_rows' => $limit,
                                    'page' => $page
                                ]);
        
        return [
            'code' => 1,
            'data' => $withdrawals->items(),
            'total' => $withdrawals->total()
        ];
    }
    
    /**
     * 获取提现手续费
     */
    private function getWithdrawFee(string $coinSymbol, float $amount): float
    {
        // 简化实现，实际项目中应该从配置表读取
        $feeRates = [
            'USDT' => 5, // 固定手续费
            'BTC' => 0.0005, // 固定手续费
            'ETH' => 0.01, // 固定手续费
        ];
        
        return $feeRates[$coinSymbol] ?? 0;
    }
}
