/**
 * 交易平台前端核心功能
 */

class TradingPlatform {
    constructor() {
        this.apiBase = '/api/v1';
        this.token = localStorage.getItem('token');
        this.ws = null;
        this.currentSymbol = 'BTCUSDT';
        this.init();
    }

    init() {
        this.setupAxios();
        this.setupWebSocket();
        this.bindEvents();
        this.loadMarketData();
        this.loadUserData();
    }

    // 设置Axios
    setupAxios() {
        axios.defaults.baseURL = this.apiBase;
        if (this.token) {
            axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;
        }
        
        // 响应拦截器
        axios.interceptors.response.use(
            response => response,
            error => {
                if (error.response?.status === 401) {
                    this.logout();
                }
                return Promise.reject(error);
            }
        );
    }

    // 设置WebSocket
    setupWebSocket() {
        if (!this.token) return;

        const wsUrl = `ws://${window.location.host}/ws?token=${this.token}`;
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
            console.log('WebSocket连接成功');
            this.subscribeChannels();
        };

        this.ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleWebSocketMessage(data);
        };

        this.ws.onclose = () => {
            console.log('WebSocket连接断开');
            setTimeout(() => this.setupWebSocket(), 5000);
        };
    }

    // 订阅频道
    subscribeChannels() {
        // 订阅市场数据
        this.ws.send(JSON.stringify({
            action: 'subscribe',
            channel: `ticker.${this.currentSymbol}`
        }));

        this.ws.send(JSON.stringify({
            action: 'subscribe',
            channel: `depth.${this.currentSymbol}`
        }));

        // 订阅用户数据
        this.ws.send(JSON.stringify({
            action: 'subscribe',
            channel: 'user.orders'
        }));

        this.ws.send(JSON.stringify({
            action: 'subscribe',
            channel: 'user.assets'
        }));
    }

    // 处理WebSocket消息
    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'ticker':
                this.updateTicker(data.data);
                break;
            case 'depth':
                this.updateDepth(data.data);
                break;
            case 'trades':
                this.updateTrades(data.data);
                break;
            case 'order_update':
                this.updateUserOrders();
                break;
            case 'asset_update':
                this.updateUserAssets();
                break;
        }
    }

    // 绑定事件
    bindEvents() {
        // 交易表单提交
        $('#buyForm').on('submit', (e) => {
            e.preventDefault();
            this.createOrder('buy');
        });

        $('#sellForm').on('submit', (e) => {
            e.preventDefault();
            this.createOrder('sell');
        });

        // 交易对切换
        $('.symbol-item').on('click', (e) => {
            const symbol = $(e.target).data('symbol');
            this.switchSymbol(symbol);
        });

        // 订单类型切换
        $('input[name="orderType"]').on('change', (e) => {
            const orderType = $(e.target).val();
            this.toggleOrderType(orderType);
        });

        // 取消订单
        $(document).on('click', '.cancel-order', (e) => {
            const orderId = $(e.target).data('order-id');
            this.cancelOrder(orderId);
        });

        // 量化策略相关
        $('#createStrategyBtn').on('click', () => {
            $('#strategyModal').modal('show');
        });

        $('#strategyForm').on('submit', (e) => {
            e.preventDefault();
            this.createQuantStrategy();
        });

        // 社交交易相关
        $('.follow-trader').on('click', (e) => {
            const traderId = $(e.target).data('trader-id');
            this.followTrader(traderId);
        });

        $('.subscribe-strategy').on('click', (e) => {
            const strategyId = $(e.target).data('strategy-id');
            this.subscribeStrategy(strategyId);
        });
    }

    // 创建订单
    async createOrder(type) {
        const formId = type === 'buy' ? '#buyForm' : '#sellForm';
        const orderType = $(`${formId} input[name="orderType"]:checked`).val();
        
        const formData = {
            symbol: this.currentSymbol,
            type: type,
            order_type: orderType,
            amount: parseFloat($(`${formId} input[name="amount"]`).val()),
            price: orderType === 'limit' ? parseFloat($(`${formId} input[name="price"]`).val()) : 0
        };

        try {
            const response = await axios.post('/trade/order', formData);
            if (response.data.code === 1) {
                this.showSuccess('订单创建成功');
                this.clearForm(formId);
                this.updateUserOrders();
                this.updateUserAssets();
            } else {
                this.showError(response.data.msg);
            }
        } catch (error) {
            this.showError('订单创建失败');
        }
    }

    // 取消订单
    async cancelOrder(orderId) {
        try {
            const response = await axios.delete(`/trade/order/${orderId}`);
            if (response.data.code === 1) {
                this.showSuccess('订单取消成功');
                this.updateUserOrders();
            } else {
                this.showError(response.data.msg);
            }
        } catch (error) {
            this.showError('订单取消失败');
        }
    }

    // 创建量化策略
    async createQuantStrategy() {
        const formData = {
            name: $('#strategyName').val(),
            description: $('#strategyDesc').val(),
            type: $('#strategyType').val(),
            symbols: [this.currentSymbol],
            risk_level: parseInt($('#riskLevel').val()),
            investment_amount: parseFloat($('#investmentAmount').val()),
            parameters: {
                stop_loss: parseFloat($('#stopLoss').val()) || 0,
                take_profit: parseFloat($('#takeProfit').val()) || 0
            }
        };

        try {
            const response = await axios.post('/quant/strategy', formData);
            if (response.data.code === 1) {
                this.showSuccess('量化策略创建成功');
                $('#strategyModal').modal('hide');
                this.loadQuantStrategies();
            } else {
                this.showError(response.data.msg);
            }
        } catch (error) {
            this.showError('量化策略创建失败');
        }
    }

    // 关注交易员
    async followTrader(traderId) {
        try {
            const response = await axios.post(`/social/follow/${traderId}`);
            if (response.data.code === 1) {
                this.showSuccess('关注成功');
                this.loadTraderRanking();
            } else {
                this.showError(response.data.msg);
            }
        } catch (error) {
            this.showError('关注失败');
        }
    }

    // 订阅策略
    async subscribeStrategy(strategyId) {
        const amount = prompt('请输入投资金额:');
        if (!amount || isNaN(amount)) return;

        try {
            const response = await axios.post(`/social/strategy/${strategyId}/subscribe`, {
                investment_amount: parseFloat(amount),
                copy_mode: 'fixed_amount'
            });
            
            if (response.data.code === 1) {
                this.showSuccess('策略订阅成功');
                this.loadSocialStrategies();
            } else {
                this.showError(response.data.msg);
            }
        } catch (error) {
            this.showError('策略订阅失败');
        }
    }

    // 切换交易对
    switchSymbol(symbol) {
        this.currentSymbol = symbol;
        $('.symbol-item').removeClass('active');
        $(`.symbol-item[data-symbol="${symbol}"]`).addClass('active');
        
        // 重新订阅WebSocket频道
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.subscribeChannels();
        }
        
        // 加载新交易对数据
        this.loadMarketData();
    }

    // 切换订单类型
    toggleOrderType(orderType) {
        if (orderType === 'market') {
            $('.price-input').hide();
        } else {
            $('.price-input').show();
        }
    }

    // 加载市场数据
    async loadMarketData() {
        try {
            // 加载行情数据
            const tickerResponse = await axios.get(`/market/ticker/${this.currentSymbol}`);
            if (tickerResponse.data.code === 1) {
                this.updateTicker(tickerResponse.data.data);
            }

            // 加载深度数据
            const depthResponse = await axios.get(`/market/depth/${this.currentSymbol}`);
            if (depthResponse.data.code === 1) {
                this.updateDepth(depthResponse.data.data);
            }

            // 加载成交记录
            const tradesResponse = await axios.get(`/market/trades/${this.currentSymbol}`);
            if (tradesResponse.data.code === 1) {
                this.updateTrades(tradesResponse.data.data);
            }
        } catch (error) {
            console.error('加载市场数据失败:', error);
        }
    }

    // 加载用户数据
    async loadUserData() {
        if (!this.token) return;

        try {
            // 加载用户资产
            this.updateUserAssets();
            
            // 加载用户订单
            this.updateUserOrders();
            
            // 加载量化策略
            this.loadQuantStrategies();
            
            // 加载社交策略
            this.loadSocialStrategies();
        } catch (error) {
            console.error('加载用户数据失败:', error);
        }
    }

    // 更新行情数据
    updateTicker(data) {
        $('#currentPrice').text(data.price);
        $('#priceChange').text(data.change);
        $('#volume24h').text(data.volume);
        
        // 更新价格颜色
        const changeClass = parseFloat(data.change) >= 0 ? 'text-success' : 'text-danger';
        $('#priceChange').removeClass('text-success text-danger').addClass(changeClass);
    }

    // 更新深度数据
    updateDepth(data) {
        // 更新买盘
        const buysList = $('#buyOrders');
        buysList.empty();
        data.bids.slice(0, 10).forEach(bid => {
            buysList.append(`
                <tr>
                    <td class="text-success">${bid[0]}</td>
                    <td>${bid[1]}</td>
                </tr>
            `);
        });

        // 更新卖盘
        const sellsList = $('#sellOrders');
        sellsList.empty();
        data.asks.slice(0, 10).forEach(ask => {
            sellsList.append(`
                <tr>
                    <td class="text-danger">${ask[0]}</td>
                    <td>${ask[1]}</td>
                </tr>
            `);
        });
    }

    // 更新成交记录
    updateTrades(data) {
        const tradesList = $('#recentTrades');
        tradesList.empty();
        
        data.slice(0, 20).forEach(trade => {
            const typeClass = trade.type === 'buy' ? 'text-success' : 'text-danger';
            tradesList.append(`
                <tr>
                    <td class="${typeClass}">${trade.price}</td>
                    <td>${trade.amount}</td>
                    <td>${trade.time}</td>
                </tr>
            `);
        });
    }

    // 更新用户资产
    async updateUserAssets() {
        try {
            const response = await axios.get('/asset/balance');
            if (response.data.code === 1) {
                const assets = response.data.data;
                const assetsList = $('#userAssets');
                assetsList.empty();
                
                assets.forEach(asset => {
                    assetsList.append(`
                        <tr>
                            <td>${asset.coin_symbol}</td>
                            <td>${asset.available}</td>
                            <td>${asset.frozen}</td>
                            <td>${asset.total}</td>
                        </tr>
                    `);
                });
            }
        } catch (error) {
            console.error('更新用户资产失败:', error);
        }
    }

    // 更新用户订单
    async updateUserOrders() {
        try {
            const response = await axios.get('/trade/orders');
            if (response.data.code === 1) {
                const orders = response.data.data;
                const ordersList = $('#userOrders');
                ordersList.empty();
                
                orders.forEach(order => {
                    const statusText = this.getOrderStatusText(order.status);
                    const typeClass = order.type === 'buy' ? 'text-success' : 'text-danger';
                    
                    ordersList.append(`
                        <tr>
                            <td>${order.symbol}</td>
                            <td class="${typeClass}">${order.type.toUpperCase()}</td>
                            <td>${order.amount}</td>
                            <td>${order.price}</td>
                            <td>${statusText}</td>
                            <td>
                                ${order.status === 0 ? `<button class="btn btn-sm btn-outline-danger cancel-order" data-order-id="${order.order_id}">取消</button>` : ''}
                            </td>
                        </tr>
                    `);
                });
            }
        } catch (error) {
            console.error('更新用户订单失败:', error);
        }
    }

    // 工具方法
    getOrderStatusText(status) {
        const statuses = {
            0: '待成交',
            1: '已成交', 
            2: '部分成交',
            3: '已取消'
        };
        return statuses[status] || '未知';
    }

    clearForm(formId) {
        $(`${formId} input[type="number"]`).val('');
    }

    showSuccess(message) {
        // 这里可以集成toast或其他通知组件
        console.log('Success:', message);
        alert(message);
    }

    showError(message) {
        // 这里可以集成toast或其他通知组件
        console.error('Error:', message);
        alert(message);
    }

    logout() {
        localStorage.removeItem('token');
        this.token = null;
        if (this.ws) {
            this.ws.close();
        }
        window.location.href = '/login';
    }
}

// 初始化交易平台
$(document).ready(() => {
    window.tradingPlatform = new TradingPlatform();
});
