<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 网格策略模型
 */
class GridStrategy extends Model
{
    protected $table = 'grid_strategies';

    // 设置字段信息
    protected $schema = [
        'id' => 'int',
        'user_id' => 'int',
        'strategy_id' => 'string',
        'symbol' => 'string',
        'name' => 'string',
        'type' => 'string',
        'min_price' => 'decimal',
        'max_price' => 'decimal',
        'grid_count' => 'int',
        'grid_spacing' => 'decimal',
        'investment_amount' => 'decimal',
        'profit_per_grid' => 'decimal',
        'stop_loss_price' => 'decimal',
        'take_profit_price' => 'decimal',
        'status' => 'int',
        'total_profit' => 'decimal',
        'total_trades' => 'int',
        'current_grids' => 'json',
        'settings' => 'json',
        'started_at' => 'datetime',
        'stopped_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // JSON字段
    protected $json = ['current_grids', 'settings'];

    // 策略类型
    const TYPE_ARITHMETIC = 'arithmetic';   // 等差网格
    const TYPE_GEOMETRIC = 'geometric';     // 等比网格
    const TYPE_FIBONACCI = 'fibonacci';     // 斐波那契网格

    // 策略状态
    const STATUS_DRAFT = 0;      // 草稿
    const STATUS_RUNNING = 1;    // 运行中
    const STATUS_PAUSED = 2;     // 已暂停
    const STATUS_STOPPED = 3;    // 已停止
    const STATUS_COMPLETED = 4;  // 已完成

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联交易对
     */
    public function tradingPair()
    {
        return $this->belongsTo(TradingPair::class, 'symbol', 'symbol');
    }

    /**
     * 关联网格订单
     */
    public function gridOrders()
    {
        return $this->hasMany(GridOrder::class, 'strategy_id', 'strategy_id');
    }

    /**
     * 创建网格策略
     */
    public static function createStrategy(array $data): array
    {
        try {
            $strategy = new self();
            $strategy->user_id = $data['user_id'];
            $strategy->strategy_id = self::generateStrategyId();
            $strategy->symbol = $data['symbol'];
            $strategy->name = $data['name'];
            $strategy->type = $data['type'];
            $strategy->min_price = $data['min_price'];
            $strategy->max_price = $data['max_price'];
            $strategy->grid_count = $data['grid_count'];
            $strategy->investment_amount = $data['investment_amount'];
            $strategy->stop_loss_price = $data['stop_loss_price'] ?? 0;
            $strategy->take_profit_price = $data['take_profit_price'] ?? 0;
            $strategy->settings = $data['settings'] ?? [];
            $strategy->status = self::STATUS_DRAFT;

            // 计算网格参数
            $strategy->calculateGridParameters();

            if ($strategy->save()) {
                return ['code' => 1, 'msg' => '网格策略创建成功', 'data' => $strategy];
            } else {
                return ['code' => 0, 'msg' => '网格策略创建失败'];
            }
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '创建失败：' . $e->getMessage()];
        }
    }

    /**
     * 启动策略
     */
    public function start(): bool
    {
        if ($this->status !== self::STATUS_DRAFT && $this->status !== self::STATUS_PAUSED) {
            return false;
        }

        // 检查用户余额
        if (!$this->checkUserBalance()) {
            return false;
        }

        // 创建初始网格订单
        if (!$this->createInitialGrids()) {
            return false;
        }

        $this->status = self::STATUS_RUNNING;
        $this->started_at = date('Y-m-d H:i:s');

        return $this->save();
    }

    /**
     * 暂停策略
     */
    public function pause(): bool
    {
        if ($this->status !== self::STATUS_RUNNING) {
            return false;
        }

        // 取消所有未成交的网格订单
        $this->cancelPendingGridOrders();

        $this->status = self::STATUS_PAUSED;
        return $this->save();
    }

    /**
     * 停止策略
     */
    public function stop(): bool
    {
        if (!in_array($this->status, [self::STATUS_RUNNING, self::STATUS_PAUSED])) {
            return false;
        }

        // 取消所有未成交的网格订单
        $this->cancelPendingGridOrders();

        $this->status = self::STATUS_STOPPED;
        $this->stopped_at = date('Y-m-d H:i:s');

        return $this->save();
    }

    /**
     * 处理网格订单成交
     */
    public function handleGridOrderFilled(GridOrder $gridOrder): bool
    {
        if ($this->status !== self::STATUS_RUNNING) {
            return false;
        }

        // 更新统计信息
        $this->total_trades++;
        $this->total_profit += $gridOrder->profit;

        // 创建对应的反向订单
        $this->createCounterOrder($gridOrder);

        // 检查止盈止损条件
        $this->checkStopConditions();

        return $this->save();
    }

    /**
     * 计算网格参数
     */
    private function calculateGridParameters(): void
    {
        $priceRange = $this->max_price - $this->min_price;

        switch ($this->type) {
            case self::TYPE_ARITHMETIC:
                $this->grid_spacing = $priceRange / $this->grid_count;
                $this->profit_per_grid = $this->grid_spacing * 0.01; // 1%利润
                break;

            case self::TYPE_GEOMETRIC:
                $ratio = pow($this->max_price / $this->min_price, 1 / $this->grid_count);
                $this->grid_spacing = $ratio - 1;
                $this->profit_per_grid = $this->grid_spacing * 100; // 百分比利润
                break;

            case self::TYPE_FIBONACCI:
                $this->calculateFibonacciGrids();
                break;
        }
    }

    /**
     * 计算斐波那契网格
     */
    private function calculateFibonacciGrids(): void
    {
        $fibonacci = [1, 1];
        for ($i = 2; $i < $this->grid_count; $i++) {
            $fibonacci[$i] = $fibonacci[$i-1] + $fibonacci[$i-2];
        }

        $totalRatio = array_sum($fibonacci);
        $priceRange = $this->max_price - $this->min_price;
        
        $grids = [];
        $currentPrice = $this->min_price;
        
        foreach ($fibonacci as $ratio) {
            $spacing = ($priceRange * $ratio) / $totalRatio;
            $grids[] = $currentPrice + $spacing;
            $currentPrice += $spacing;
        }

        $this->current_grids = $grids;
    }

    /**
     * 检查用户余额
     */
    private function checkUserBalance(): bool
    {
        $userAsset = UserAsset::where('user_id', $this->user_id)
                             ->where('coin_symbol', 'USDT')
                             ->find();

        return $userAsset && $userAsset->available >= $this->investment_amount;
    }

    /**
     * 创建初始网格订单
     */
    private function createInitialGrids(): bool
    {
        $currentPrice = $this->getCurrentPrice();
        $gridPrices = $this->generateGridPrices();
        
        foreach ($gridPrices as $price) {
            $side = $price < $currentPrice ? 'buy' : 'sell';
            $amount = $this->calculateGridAmount($price);

            $gridOrder = GridOrder::createGridOrder([
                'strategy_id' => $this->strategy_id,
                'user_id' => $this->user_id,
                'symbol' => $this->symbol,
                'side' => $side,
                'price' => $price,
                'amount' => $amount,
                'grid_level' => $this->getGridLevel($price)
            ]);

            if (!$gridOrder['code']) {
                return false;
            }
        }

        return true;
    }

    /**
     * 生成网格价格
     */
    private function generateGridPrices(): array
    {
        $prices = [];
        
        switch ($this->type) {
            case self::TYPE_ARITHMETIC:
                for ($i = 0; $i <= $this->grid_count; $i++) {
                    $prices[] = $this->min_price + ($i * $this->grid_spacing);
                }
                break;

            case self::TYPE_GEOMETRIC:
                $price = $this->min_price;
                for ($i = 0; $i <= $this->grid_count; $i++) {
                    $prices[] = $price;
                    $price *= (1 + $this->grid_spacing);
                }
                break;

            case self::TYPE_FIBONACCI:
                $prices = $this->current_grids;
                break;
        }

        return $prices;
    }

    /**
     * 计算网格数量
     */
    private function calculateGridAmount(float $price): float
    {
        $amountPerGrid = $this->investment_amount / $this->grid_count;
        return $amountPerGrid / $price;
    }

    /**
     * 获取网格等级
     */
    private function getGridLevel(float $price): int
    {
        $priceRange = $this->max_price - $this->min_price;
        $position = ($price - $this->min_price) / $priceRange;
        
        return (int)($position * $this->grid_count);
    }

    /**
     * 获取当前价格
     */
    private function getCurrentPrice(): float
    {
        $tradingPair = TradingPair::getBySymbol($this->symbol);
        return $tradingPair ? $tradingPair->current_price : 0;
    }

    /**
     * 创建反向订单
     */
    private function createCounterOrder(GridOrder $filledOrder): void
    {
        $counterSide = $filledOrder->side === 'buy' ? 'sell' : 'buy';
        $counterPrice = $filledOrder->side === 'buy' ? 
            $filledOrder->price + $this->grid_spacing :
            $filledOrder->price - $this->grid_spacing;

        GridOrder::createGridOrder([
            'strategy_id' => $this->strategy_id,
            'user_id' => $this->user_id,
            'symbol' => $this->symbol,
            'side' => $counterSide,
            'price' => $counterPrice,
            'amount' => $filledOrder->amount,
            'grid_level' => $filledOrder->grid_level + ($counterSide === 'sell' ? 1 : -1)
        ]);
    }

    /**
     * 取消待成交的网格订单
     */
    private function cancelPendingGridOrders(): void
    {
        $pendingOrders = GridOrder::where('strategy_id', $this->strategy_id)
                                 ->where('status', GridOrder::STATUS_PENDING)
                                 ->select();

        foreach ($pendingOrders as $order) {
            $order->cancel();
        }
    }

    /**
     * 检查止盈止损条件
     */
    private function checkStopConditions(): void
    {
        $currentPrice = $this->getCurrentPrice();

        // 检查止损
        if ($this->stop_loss_price > 0 && $currentPrice <= $this->stop_loss_price) {
            $this->stop();
            return;
        }

        // 检查止盈
        if ($this->take_profit_price > 0 && $currentPrice >= $this->take_profit_price) {
            $this->stop();
            return;
        }

        // 检查总利润止盈
        $profitThreshold = $this->settings['profit_threshold'] ?? 0;
        if ($profitThreshold > 0 && $this->total_profit >= $profitThreshold) {
            $this->stop();
        }
    }

    /**
     * 生成策略ID
     */
    private static function generateStrategyId(): string
    {
        return 'GRID' . date('YmdHis') . mt_rand(1000, 9999);
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr(): string
    {
        $statusTexts = [
            self::STATUS_DRAFT => '草稿',
            self::STATUS_RUNNING => '运行中',
            self::STATUS_PAUSED => '已暂停',
            self::STATUS_STOPPED => '已停止',
            self::STATUS_COMPLETED => '已完成'
        ];

        return $statusTexts[$this->status] ?? '未知状态';
    }

    /**
     * 获取类型文本
     */
    public function getTypeTextAttr(): string
    {
        $typeTexts = [
            self::TYPE_ARITHMETIC => '等差网格',
            self::TYPE_GEOMETRIC => '等比网格',
            self::TYPE_FIBONACCI => '斐波那契网格'
        ];

        return $typeTexts[$this->type] ?? '未知类型';
    }

    /**
     * 获取收益率
     */
    public function getProfitRateAttr(): float
    {
        return $this->investment_amount > 0 ? 
            ($this->total_profit / $this->investment_amount) * 100 : 0;
    }
}
