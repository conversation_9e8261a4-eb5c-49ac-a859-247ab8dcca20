<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GVD</title>
    <link href="https://cdn.jsdelivr.net/npm/element-ui@2.15.9/lib/theme-chalk/index.css" rel="stylesheet">
    <style>
        body { margin: 0; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; background: #f8f9fa; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px 0; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        .hero { text-align: center; padding: 60px 0; }
        .hero h1 { font-size: 48px; margin-bottom: 20px; }
        .hero p { font-size: 20px; opacity: 0.9; margin-bottom: 40px; }
        .features { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; padding: 60px 0; }
        .feature-card { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; }
        .nav-buttons { display: flex; gap: 20px; justify-content: center; flex-wrap: wrap; }
        .btn { padding: 15px 30px; border-radius: 25px; text-decoration: none; font-weight: bold; transition: all 0.3s; }
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="hero">
                <h1>GVD</h1>
                <p>专业、安全、高效的数字货币交易体验</p>
                <div class="nav-buttons">
                    <a href="/auth/register" class="btn btn-primary">立即注册</a>
                    <a href="/auth/login" class="btn btn-secondary">用户登录</a>
                    <a href="/contract/" class="btn btn-secondary" onclick="return checkLogin()">开始交易</a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="features">
            <div class="feature-card">
                <h3>🔒 安全可靠</h3>
                <p>银行级安全防护，多重加密保障资金安全</p>
            </div>
            <div class="feature-card">
                <h3>⚡ 高速交易</h3>
                <p>毫秒级撮合引擎，极速交易体验</p>
            </div>
            <div class="feature-card">
                <h3>💰 低手续费</h3>
                <p>行业最低手续费，让您的收益最大化</p>
            </div>
            <div class="feature-card">
                <h3>📱 多端支持</h3>
                <p>支持Web、APP、API多种交易方式</p>
            </div>
            <div class="feature-card">
                <h3>🎯 合约交易</h3>
                <p>支持多种合约产品，灵活投资策略</p>
            </div>
            <div class="feature-card">
                <h3>🌟 专业服务</h3>
                <p>7×24小时专业客服，随时为您服务</p>
            </div>
        </div>

        <!-- APP下载区域 -->
        <div style="background: #2d2d2d; color: white; padding: 40px 0; text-align: center; margin-top: 60px;">
            <h3 style="margin-bottom: 30px;">下载GVD APP</h3>
            <div style="display: flex; justify-content: center; gap: 30px; flex-wrap: wrap;">
                <a href="/download/gvd-android.apk" class="btn" style="background: #28a745; color: white; display: flex; align-items: center; gap: 10px; padding: 15px 25px;">
                    <i class="fab fa-android" style="font-size: 24px;"></i>
                    <div>
                        <div style="font-size: 12px; opacity: 0.8;">下载</div>
                        <div style="font-weight: bold;">Android版</div>
                    </div>
                </a>
                <a href="/download/gvd-ios.ipa" class="btn" style="background: #007bff; color: white; display: flex; align-items: center; gap: 10px; padding: 15px 25px;">
                    <i class="fab fa-apple" style="font-size: 24px;"></i>
                    <div>
                        <div style="font-size: 12px; opacity: 0.8;">下载</div>
                        <div style="font-weight: bold;">iOS版</div>
                    </div>
                </a>
            </div>
            <p style="margin-top: 20px; opacity: 0.7; font-size: 14px;">
                支持Android 5.0+ 和 iOS 10.0+ 系统
            </p>
        </div>
    </div>

    <script>
        function checkLogin() {
            // 检查是否已登录
            if (!sessionStorage.getItem('user_id')) {
                alert('请先登录或注册');
                window.location.href = '/auth/login';
                return false;
            }
            return true;
        }
    </script>
</body>
</html>
