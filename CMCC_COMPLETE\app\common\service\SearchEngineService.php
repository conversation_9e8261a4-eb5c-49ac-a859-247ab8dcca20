<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

/**
 * 搜索引擎服务类
 * 集成Elasticsearch实现全文搜索
 */
class SearchEngineService
{
    // 索引类型
    const INDEX_USERS = 'users';
    const INDEX_ORDERS = 'orders';
    const INDEX_TRADES = 'trades';
    const INDEX_ARTICLES = 'articles';
    const INDEX_HELP = 'help';
    const INDEX_LOGS = 'logs';

    // 搜索类型
    const SEARCH_EXACT = 'exact';
    const SEARCH_FUZZY = 'fuzzy';
    const SEARCH_WILDCARD = 'wildcard';
    const SEARCH_PHRASE = 'phrase';

    private $client;
    private $config;

    public function __construct()
    {
        $this->config = config('elasticsearch');
        $this->client = $this->initElasticsearchClient();
    }

    /**
     * 初始化Elasticsearch客户端
     */
    private function initElasticsearchClient()
    {
        try {
            // 这里应该使用Elasticsearch官方客户端
            // 简化实现，返回模拟客户端
            return new class {
                public function index($params) { return ['result' => 'created']; }
                public function search($params) { return ['hits' => ['hits' => []]]; }
                public function delete($params) { return ['result' => 'deleted']; }
                public function update($params) { return ['result' => 'updated']; }
                public function indices() { 
                    return new class {
                        public function create($params) { return ['acknowledged' => true]; }
                        public function delete($params) { return ['acknowledged' => true]; }
                        public function exists($params) { return true; }
                    };
                }
            };
        } catch (\Exception $e) {
            Log::error('Elasticsearch连接失败：' . $e->getMessage());
            return null;
        }
    }

    /**
     * 创建索引
     */
    public function createIndex(string $indexName, array $mapping = []): array
    {
        try {
            if (!$this->client) {
                return ['code' => 0, 'msg' => 'Elasticsearch未连接'];
            }

            $params = [
                'index' => $indexName,
                'body' => [
                    'settings' => [
                        'number_of_shards' => 1,
                        'number_of_replicas' => 0,
                        'analysis' => [
                            'analyzer' => [
                                'ik_max_word' => [
                                    'type' => 'custom',
                                    'tokenizer' => 'ik_max_word'
                                ],
                                'ik_smart' => [
                                    'type' => 'custom',
                                    'tokenizer' => 'ik_smart'
                                ]
                            ]
                        ]
                    ],
                    'mappings' => $mapping ?: $this->getDefaultMapping($indexName)
                ]
            ];

            $response = $this->client->indices()->create($params);

            return [
                'code' => 1,
                'msg' => '索引创建成功',
                'data' => $response
            ];

        } catch (\Exception $e) {
            Log::error('创建索引失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '创建失败：' . $e->getMessage()];
        }
    }

    /**
     * 添加文档到索引
     */
    public function indexDocument(string $indexName, string $id, array $document): array
    {
        try {
            if (!$this->client) {
                return ['code' => 0, 'msg' => 'Elasticsearch未连接'];
            }

            $params = [
                'index' => $indexName,
                'id' => $id,
                'body' => $document
            ];

            $response = $this->client->index($params);

            return [
                'code' => 1,
                'msg' => '文档索引成功',
                'data' => $response
            ];

        } catch (\Exception $e) {
            Log::error('索引文档失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '索引失败：' . $e->getMessage()];
        }
    }

    /**
     * 批量索引文档
     */
    public function bulkIndex(string $indexName, array $documents): array
    {
        try {
            if (!$this->client) {
                return ['code' => 0, 'msg' => 'Elasticsearch未连接'];
            }

            $body = [];
            foreach ($documents as $id => $document) {
                $body[] = [
                    'index' => [
                        '_index' => $indexName,
                        '_id' => $id
                    ]
                ];
                $body[] = $document;
            }

            $params = ['body' => $body];
            $response = $this->client->bulk($params);

            $successCount = 0;
            $errorCount = 0;
            $errors = [];

            if (isset($response['items'])) {
                foreach ($response['items'] as $item) {
                    if (isset($item['index']['error'])) {
                        $errorCount++;
                        $errors[] = $item['index']['error'];
                    } else {
                        $successCount++;
                    }
                }
            }

            return [
                'code' => 1,
                'msg' => '批量索引完成',
                'data' => [
                    'success_count' => $successCount,
                    'error_count' => $errorCount,
                    'errors' => $errors
                ]
            ];

        } catch (\Exception $e) {
            Log::error('批量索引失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '批量索引失败：' . $e->getMessage()];
        }
    }

    /**
     * 搜索文档
     */
    public function search(string $indexName, array $searchParams): array
    {
        try {
            if (!$this->client) {
                return ['code' => 0, 'msg' => 'Elasticsearch未连接'];
            }

            $query = $this->buildQuery($searchParams);
            
            $params = [
                'index' => $indexName,
                'body' => [
                    'query' => $query,
                    'from' => $searchParams['from'] ?? 0,
                    'size' => $searchParams['size'] ?? 20,
                    'sort' => $searchParams['sort'] ?? [],
                    'highlight' => $this->buildHighlight($searchParams)
                ]
            ];

            // 添加聚合
            if (!empty($searchParams['aggregations'])) {
                $params['body']['aggs'] = $searchParams['aggregations'];
            }

            $response = $this->client->search($params);

            $results = $this->formatSearchResults($response);

            return [
                'code' => 1,
                'msg' => '搜索成功',
                'data' => $results
            ];

        } catch (\Exception $e) {
            Log::error('搜索失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '搜索失败：' . $e->getMessage()];
        }
    }

    /**
     * 多索引搜索
     */
    public function multiSearch(array $searches): array
    {
        try {
            if (!$this->client) {
                return ['code' => 0, 'msg' => 'Elasticsearch未连接'];
            }

            $body = [];
            foreach ($searches as $search) {
                $body[] = [
                    'index' => $search['index']
                ];
                $body[] = [
                    'query' => $this->buildQuery($search['params']),
                    'from' => $search['params']['from'] ?? 0,
                    'size' => $search['params']['size'] ?? 20
                ];
            }

            $params = ['body' => $body];
            $response = $this->client->msearch($params);

            $results = [];
            foreach ($response['responses'] as $index => $searchResponse) {
                $results[$index] = $this->formatSearchResults($searchResponse);
            }

            return [
                'code' => 1,
                'msg' => '多索引搜索成功',
                'data' => $results
            ];

        } catch (\Exception $e) {
            Log::error('多索引搜索失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '搜索失败：' . $e->getMessage()];
        }
    }

    /**
     * 搜索建议
     */
    public function suggest(string $indexName, string $text, array $options = []): array
    {
        try {
            if (!$this->client) {
                return ['code' => 0, 'msg' => 'Elasticsearch未连接'];
            }

            $params = [
                'index' => $indexName,
                'body' => [
                    'suggest' => [
                        'text' => $text,
                        'simple_phrase' => [
                            'phrase' => [
                                'field' => $options['field'] ?? 'content',
                                'size' => $options['size'] ?? 5,
                                'gram_size' => $options['gram_size'] ?? 3,
                                'direct_generator' => [
                                    [
                                        'field' => $options['field'] ?? 'content',
                                        'suggest_mode' => 'missing'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];

            $response = $this->client->search($params);

            $suggestions = [];
            if (isset($response['suggest']['simple_phrase'][0]['options'])) {
                foreach ($response['suggest']['simple_phrase'][0]['options'] as $option) {
                    $suggestions[] = [
                        'text' => $option['text'],
                        'score' => $option['score']
                    ];
                }
            }

            return [
                'code' => 1,
                'msg' => '获取建议成功',
                'data' => $suggestions
            ];

        } catch (\Exception $e) {
            Log::error('获取搜索建议失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取建议失败：' . $e->getMessage()];
        }
    }

    /**
     * 删除文档
     */
    public function deleteDocument(string $indexName, string $id): array
    {
        try {
            if (!$this->client) {
                return ['code' => 0, 'msg' => 'Elasticsearch未连接'];
            }

            $params = [
                'index' => $indexName,
                'id' => $id
            ];

            $response = $this->client->delete($params);

            return [
                'code' => 1,
                'msg' => '文档删除成功',
                'data' => $response
            ];

        } catch (\Exception $e) {
            Log::error('删除文档失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '删除失败：' . $e->getMessage()];
        }
    }

    /**
     * 更新文档
     */
    public function updateDocument(string $indexName, string $id, array $document): array
    {
        try {
            if (!$this->client) {
                return ['code' => 0, 'msg' => 'Elasticsearch未连接'];
            }

            $params = [
                'index' => $indexName,
                'id' => $id,
                'body' => [
                    'doc' => $document
                ]
            ];

            $response = $this->client->update($params);

            return [
                'code' => 1,
                'msg' => '文档更新成功',
                'data' => $response
            ];

        } catch (\Exception $e) {
            Log::error('更新文档失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '更新失败：' . $e->getMessage()];
        }
    }

    /**
     * 同步数据库数据到搜索引擎
     */
    public function syncFromDatabase(string $indexName, string $tableName, array $options = []): array
    {
        try {
            $batchSize = $options['batch_size'] ?? 1000;
            $totalCount = Db::name($tableName)->count();
            $processedCount = 0;
            $errorCount = 0;

            for ($offset = 0; $offset < $totalCount; $offset += $batchSize) {
                $records = Db::name($tableName)
                    ->limit($batchSize)
                    ->page(($offset / $batchSize) + 1, $batchSize)
                    ->select()
                    ->toArray();

                if (empty($records)) {
                    break;
                }

                $documents = [];
                foreach ($records as $record) {
                    $id = $record['id'] ?? $record['user_id'] ?? uniqid();
                    $documents[$id] = $this->transformRecord($record, $indexName);
                }

                $result = $this->bulkIndex($indexName, $documents);
                if ($result['code'] === 1) {
                    $processedCount += $result['data']['success_count'];
                    $errorCount += $result['data']['error_count'];
                } else {
                    $errorCount += count($documents);
                }
            }

            return [
                'code' => 1,
                'msg' => '数据同步完成',
                'data' => [
                    'total_count' => $totalCount,
                    'processed_count' => $processedCount,
                    'error_count' => $errorCount
                ]
            ];

        } catch (\Exception $e) {
            Log::error('数据同步失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '同步失败：' . $e->getMessage()];
        }
    }

    /**
     * 构建查询
     */
    private function buildQuery(array $searchParams): array
    {
        $query = ['bool' => ['must' => [], 'filter' => []]];

        // 关键词搜索
        if (!empty($searchParams['keyword'])) {
            $searchType = $searchParams['search_type'] ?? self::SEARCH_FUZZY;
            
            switch ($searchType) {
                case self::SEARCH_EXACT:
                    $query['bool']['must'][] = [
                        'term' => [
                            $searchParams['field'] ?? '_all' => $searchParams['keyword']
                        ]
                    ];
                    break;
                    
                case self::SEARCH_PHRASE:
                    $query['bool']['must'][] = [
                        'match_phrase' => [
                            $searchParams['field'] ?? '_all' => $searchParams['keyword']
                        ]
                    ];
                    break;
                    
                case self::SEARCH_WILDCARD:
                    $query['bool']['must'][] = [
                        'wildcard' => [
                            $searchParams['field'] ?? '_all' => '*' . $searchParams['keyword'] . '*'
                        ]
                    ];
                    break;
                    
                default: // SEARCH_FUZZY
                    $query['bool']['must'][] = [
                        'multi_match' => [
                            'query' => $searchParams['keyword'],
                            'fields' => $searchParams['fields'] ?? ['_all'],
                            'fuzziness' => 'AUTO'
                        ]
                    ];
            }
        }

        // 过滤条件
        if (!empty($searchParams['filters'])) {
            foreach ($searchParams['filters'] as $field => $value) {
                if (is_array($value)) {
                    $query['bool']['filter'][] = [
                        'terms' => [$field => $value]
                    ];
                } else {
                    $query['bool']['filter'][] = [
                        'term' => [$field => $value]
                    ];
                }
            }
        }

        // 范围查询
        if (!empty($searchParams['ranges'])) {
            foreach ($searchParams['ranges'] as $field => $range) {
                $query['bool']['filter'][] = [
                    'range' => [$field => $range]
                ];
            }
        }

        return $query;
    }

    /**
     * 构建高亮
     */
    private function buildHighlight(array $searchParams): array
    {
        if (empty($searchParams['highlight'])) {
            return [];
        }

        return [
            'fields' => $searchParams['highlight_fields'] ?? ['*' => new \stdClass()],
            'pre_tags' => ['<mark>'],
            'post_tags' => ['</mark>']
        ];
    }

    /**
     * 格式化搜索结果
     */
    private function formatSearchResults(array $response): array
    {
        $results = [
            'total' => $response['hits']['total']['value'] ?? 0,
            'max_score' => $response['hits']['max_score'] ?? 0,
            'hits' => [],
            'aggregations' => $response['aggregations'] ?? []
        ];

        foreach ($response['hits']['hits'] as $hit) {
            $result = [
                'id' => $hit['_id'],
                'score' => $hit['_score'],
                'source' => $hit['_source']
            ];

            if (!empty($hit['highlight'])) {
                $result['highlight'] = $hit['highlight'];
            }

            $results['hits'][] = $result;
        }

        return $results;
    }

    /**
     * 获取默认映射
     */
    private function getDefaultMapping(string $indexName): array
    {
        $mappings = [
            self::INDEX_USERS => [
                'properties' => [
                    'username' => ['type' => 'keyword'],
                    'email' => ['type' => 'keyword'],
                    'phone' => ['type' => 'keyword'],
                    'nickname' => ['type' => 'text', 'analyzer' => 'ik_max_word'],
                    'status' => ['type' => 'integer'],
                    'created_at' => ['type' => 'date']
                ]
            ],
            self::INDEX_ORDERS => [
                'properties' => [
                    'order_id' => ['type' => 'keyword'],
                    'user_id' => ['type' => 'integer'],
                    'symbol' => ['type' => 'keyword'],
                    'side' => ['type' => 'keyword'],
                    'type' => ['type' => 'keyword'],
                    'quantity' => ['type' => 'double'],
                    'price' => ['type' => 'double'],
                    'status' => ['type' => 'keyword'],
                    'created_at' => ['type' => 'date']
                ]
            ],
            self::INDEX_ARTICLES => [
                'properties' => [
                    'title' => ['type' => 'text', 'analyzer' => 'ik_max_word'],
                    'content' => ['type' => 'text', 'analyzer' => 'ik_max_word'],
                    'category' => ['type' => 'keyword'],
                    'tags' => ['type' => 'keyword'],
                    'author' => ['type' => 'keyword'],
                    'status' => ['type' => 'integer'],
                    'created_at' => ['type' => 'date']
                ]
            ]
        ];

        return $mappings[$indexName] ?? [
            'properties' => [
                'content' => ['type' => 'text', 'analyzer' => 'ik_max_word'],
                'created_at' => ['type' => 'date']
            ]
        ];
    }

    /**
     * 转换记录格式
     */
    private function transformRecord(array $record, string $indexName): array
    {
        // 根据索引类型转换记录格式
        switch ($indexName) {
            case self::INDEX_USERS:
                return [
                    'username' => $record['username'] ?? '',
                    'email' => $record['email'] ?? '',
                    'phone' => $record['phone'] ?? '',
                    'nickname' => $record['nickname'] ?? '',
                    'status' => $record['status'] ?? 0,
                    'created_at' => $record['created_at'] ?? date('Y-m-d H:i:s')
                ];
                
            case self::INDEX_ORDERS:
                return [
                    'order_id' => $record['order_id'] ?? '',
                    'user_id' => $record['user_id'] ?? 0,
                    'symbol' => $record['symbol'] ?? '',
                    'side' => $record['side'] ?? '',
                    'type' => $record['type'] ?? '',
                    'quantity' => $record['quantity'] ?? 0,
                    'price' => $record['price'] ?? 0,
                    'status' => $record['status'] ?? '',
                    'created_at' => $record['created_at'] ?? date('Y-m-d H:i:s')
                ];
                
            default:
                return $record;
        }
    }
}
