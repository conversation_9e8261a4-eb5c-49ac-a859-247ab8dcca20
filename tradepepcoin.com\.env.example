# 应用配置
APP_DEBUG = true
APP_TRACE = false

[APP]
DEFAULT_TIMEZONE = Asia/Shanghai

[DATABASE]
TYPE = mysql
HOSTNAME = 127.0.0.1
DATABASE = crypto_exchange
USERNAME = root
PASSWORD = 
HOSTPORT = 3306
CHARSET = utf8mb4
DEBUG = true
PREFIX = ce_

[LANG]
default_lang = zh-cn

[CACHE]
DRIVER = file
CACHE_PREFIX = crypto_
CACHE_TAG_PREFIX = crypto_tag_

[SESSION]
TYPE = file
AUTO_START = true
PREFIX = crypto_session_
EXPIRE = 3600

[COOKIE]
PREFIX = crypto_
EXPIRE = 0
PATH = /
DOMAIN = 
SECURE = false
HTTPONLY = true
SAMESITE = 

# 邮件配置
[EMAIL]
SMTP_HOST = smtp.qq.com
SMTP_PORT = 587
SMTP_USERNAME = 
SMTP_PASSWORD = 
SMTP_SECURE = tls
FROM_EMAIL = 
FROM_NAME = 数字货币交易平台

# 系统配置
[SYSTEM]
SITE_NAME = 数字货币交易平台
SITE_URL = http://localhost
SITE_LOGO = 
UNIVERSAL_CODE = 888888
ADMIN_PATH = admin

# 安全配置
[SECURITY]
AUTH_KEY = your-secret-key-here
JWT_SECRET = your-jwt-secret-here
ENCRYPT_KEY = your-encrypt-key-here

# 文件上传配置
[UPLOAD]
MAX_SIZE = 10485760
ALLOWED_EXT = jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx
UPLOAD_PATH = uploads/

# Redis配置（可选）
[REDIS]
HOST = 127.0.0.1
PORT = 6379
PASSWORD = 
SELECT = 0
TIMEOUT = 0
EXPIRE = 3600
PERSISTENT = false
PREFIX = crypto_

# 队列配置
[QUEUE]
CONNECTOR = sync
EXPIRE = 60
RETRY_AFTER = 90
BLOCK_FOR = null
MEMORY = 128

# 日志配置
[LOG]
CHANNEL = file
LEVEL = error
FILE_SIZE = 2097152
PATH = 

# API配置
[API]
RATE_LIMIT = 1000
RATE_LIMIT_TIME = 3600
TOKEN_EXPIRE = 7200

# 交易配置
[TRADE]
DEFAULT_FEE = 0.001
MIN_TRADE_AMOUNT = 0.001
MAX_TRADE_AMOUNT = 1000000

# 合约配置
[CONTRACT]
DEFAULT_PERIODS = 60,180,300,1800
DEFAULT_RATES = 75,77,80,85
DEFAULT_AMOUNTS = 50,100,500,1000
MIN_AMOUNT = 10
MAX_AMOUNT = 10000
FEE_RATE = 0.02

# 充值提现配置
[FINANCE]
MIN_DEPOSIT = 10
MIN_WITHDRAW = 10
WITHDRAW_FEE = 5
AUTO_CONFIRM_DEPOSIT = true
AUTO_CONFIRM_WITHDRAW = false

# 推荐佣金配置
[COMMISSION]
LEVEL1_RATE = 0.5
LEVEL2_RATE = 0.3
LEVEL3_RATE = 0.2
ENABLE_COMMISSION = true

# 短信配置（可选）
[SMS]
PROVIDER = aliyun
ACCESS_KEY = 
SECRET_KEY = 
SIGN_NAME = 
TEMPLATE_CODE = 

# 第三方API配置
[THIRD_PARTY]
COINMARKETCAP_API_KEY = 
BINANCE_API_KEY = 
BINANCE_SECRET_KEY = 

# 监控配置
[MONITOR]
ENABLE_MONITOR = false
ALERT_EMAIL = 
ALERT_WEBHOOK = 

# 备份配置
[BACKUP]
AUTO_BACKUP = false
BACKUP_PATH = backups/
BACKUP_KEEP_DAYS = 30
