<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 成交记录模型
 */
class Trade extends Model
{
    protected $table = 'gvd_trades';
    
    // 设置字段信息
    protected $schema = [
        'id'         => 'int',
        'trade_id'   => 'string',
        'order_id'   => 'string',
        'user_id'    => 'int',
        'symbol'     => 'string',
        'type'       => 'string',
        'price'      => 'decimal',
        'amount'     => 'decimal',
        'total'      => 'decimal',
        'fee'        => 'decimal',
        'fee_coin'   => 'string',
        'created_at' => 'datetime',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = 'datetime';
    protected $updateTime = false;

    // 类型转换
    protected $type = [
        'user_id' => 'integer',
        'price'   => 'decimal:8',
        'amount'  => 'decimal:8',
        'total'   => 'decimal:8',
        'fee'     => 'decimal:8',
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联订单
     */
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id', 'order_id');
    }

    /**
     * 关联交易对
     */
    public function tradingPair()
    {
        return $this->belongsTo(TradingPair::class, 'symbol', 'symbol');
    }

    /**
     * 交易类型文本
     */
    public function getTypeTextAttr($value, $data)
    {
        $types = ['buy' => '买入', 'sell' => '卖出'];
        return $types[$data['type']] ?? '未知';
    }

    /**
     * 交易类型颜色
     */
    public function getTypeColorAttr($value, $data)
    {
        return $data['type'] == 'buy' ? 'success' : 'danger';
    }

    /**
     * 格式化价格
     */
    public function getPriceFormatAttr($value, $data)
    {
        return number_format($data['price'], 8, '.', '');
    }

    /**
     * 格式化数量
     */
    public function getAmountFormatAttr($value, $data)
    {
        return number_format($data['amount'], 8, '.', '');
    }

    /**
     * 格式化总额
     */
    public function getTotalFormatAttr($value, $data)
    {
        return number_format($data['total'], 8, '.', '');
    }

    /**
     * 格式化手续费
     */
    public function getFeeFormatAttr($value, $data)
    {
        return number_format($data['fee'], 8, '.', '');
    }

    /**
     * 获取用户成交统计
     */
    public static function getUserStats(int $userId, string $symbol = ''): array
    {
        $where = ['user_id' => $userId];
        if (!empty($symbol)) {
            $where['symbol'] = $symbol;
        }

        // 总成交笔数
        $totalTrades = self::where($where)->count();

        // 总成交量
        $totalVolume = self::where($where)->sum('total');

        // 总手续费
        $totalFee = self::where($where)->sum('fee');

        // 买入笔数
        $buyTrades = self::where($where)->where('type', 'buy')->count();

        // 卖出笔数
        $sellTrades = self::where($where)->where('type', 'sell')->count();

        // 买入成交量
        $buyVolume = self::where($where)->where('type', 'buy')->sum('total');

        // 卖出成交量
        $sellVolume = self::where($where)->where('type', 'sell')->sum('total');

        // 今日成交笔数
        $todayTrades = self::where($where)
                          ->whereTime('created_at', 'today')
                          ->count();

        // 今日成交量
        $todayVolume = self::where($where)
                          ->whereTime('created_at', 'today')
                          ->sum('total');

        return [
            'total_trades' => $totalTrades,
            'total_volume' => $totalVolume,
            'total_fee' => $totalFee,
            'buy_trades' => $buyTrades,
            'sell_trades' => $sellTrades,
            'buy_volume' => $buyVolume,
            'sell_volume' => $sellVolume,
            'today_trades' => $todayTrades,
            'today_volume' => $todayVolume
        ];
    }

    /**
     * 获取交易对成交统计
     */
    public static function getSymbolStats(string $symbol): array
    {
        $where = ['symbol' => $symbol];

        // 24小时成交笔数
        $trades24h = self::where($where)
                        ->whereTime('created_at', '>=', date('Y-m-d H:i:s', time() - 86400))
                        ->count();

        // 24小时成交量
        $volume24h = self::where($where)
                        ->whereTime('created_at', '>=', date('Y-m-d H:i:s', time() - 86400))
                        ->sum('total');

        // 24小时最高价
        $high24h = self::where($where)
                      ->whereTime('created_at', '>=', date('Y-m-d H:i:s', time() - 86400))
                      ->max('price');

        // 24小时最低价
        $low24h = self::where($where)
                     ->whereTime('created_at', '>=', date('Y-m-d H:i:s', time() - 86400))
                     ->min('price');

        // 最新价格
        $latestPrice = self::where($where)
                          ->order('created_at', 'desc')
                          ->value('price');

        // 24小时前价格
        $price24hAgo = self::where($where)
                          ->whereTime('created_at', '<=', date('Y-m-d H:i:s', time() - 86400))
                          ->order('created_at', 'desc')
                          ->value('price');

        // 计算24小时涨跌幅
        $change24h = 0;
        if ($price24hAgo > 0 && $latestPrice > 0) {
            $change24h = ($latestPrice - $price24hAgo) / $price24hAgo * 100;
        }

        return [
            'trades_24h' => $trades24h,
            'volume_24h' => $volume24h,
            'high_24h' => $high24h,
            'low_24h' => $low24h,
            'latest_price' => $latestPrice,
            'change_24h' => $change24h
        ];
    }

    /**
     * 获取最新成交记录
     */
    public static function getRecentTrades(string $symbol, int $limit = 50): array
    {
        return self::where('symbol', $symbol)
                  ->order('created_at', 'desc')
                  ->limit($limit)
                  ->select()
                  ->toArray();
    }

    /**
     * 获取成交历史
     */
    public static function getTradeHistory(int $userId, array $params = []): array
    {
        $where = ['user_id' => $userId];

        if (!empty($params['symbol'])) {
            $where['symbol'] = $params['symbol'];
        }

        if (!empty($params['type'])) {
            $where['type'] = $params['type'];
        }

        if (!empty($params['start_time'])) {
            $where[] = ['created_at', '>=', $params['start_time']];
        }

        if (!empty($params['end_time'])) {
            $where[] = ['created_at', '<=', $params['end_time']];
        }

        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 20;

        return self::where($where)
                  ->order('created_at', 'desc')
                  ->paginate([
                      'list_rows' => $limit,
                      'page' => $page
                  ])
                  ->toArray();
    }
}
