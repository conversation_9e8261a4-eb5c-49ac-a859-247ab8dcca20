<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 杠杆订单模型
 */
class LeverageOrder extends Model
{
    protected $name = 'gvd_leverage_orders';
    
    protected $type = [
        'price' => 'float',
        'amount' => 'float',
        'total' => 'float',
        'margin' => 'float',
        'leverage' => 'float',
        'filled_amount' => 'float',
        'filled_total' => 'float',
        'avg_price' => 'float',
        'fee' => 'float',
        'pnl' => 'float',
        'liquidation_price' => 'float'
    ];

    // 订单类型
    const TYPE_BUY = 1;   // 做多
    const TYPE_SELL = 2;  // 做空

    // 订单方式
    const ORDER_TYPE_LIMIT = 1;  // 限价单
    const ORDER_TYPE_MARKET = 2; // 市价单

    // 订单状态
    const STATUS_PENDING = 1;    // 待成交
    const STATUS_PARTIAL = 2;    // 部分成交
    const STATUS_FILLED = 3;     // 完全成交
    const STATUS_CANCELLED = 4;  // 已取消
    const STATUS_LIQUIDATED = 5; // 已强平

    /**
     * 生成订单号
     */
    public static function generateOrderId(): string
    {
        return 'LV' . date('YmdHis') . mt_rand(1000, 9999);
    }

    /**
     * 获取订单类型文本
     */
    public function getTypeTextAttr($value, $data): string
    {
        $types = [
            self::TYPE_BUY => '做多',
            self::TYPE_SELL => '做空'
        ];
        return $types[$data['type']] ?? '未知';
    }

    /**
     * 获取订单状态文本
     */
    public function getStatusTextAttr($value, $data): string
    {
        $statuses = [
            self::STATUS_PENDING => '待成交',
            self::STATUS_PARTIAL => '部分成交',
            self::STATUS_FILLED => '完全成交',
            self::STATUS_CANCELLED => '已取消',
            self::STATUS_LIQUIDATED => '已强平'
        ];
        return $statuses[$data['status']] ?? '未知';
    }

    /**
     * 计算强平价格
     */
    public function calculateLiquidationPrice(): float
    {
        if ($this->margin <= 0 || $this->amount <= 0) {
            return 0;
        }

        $maintenanceMarginRate = 0.005; // 维持保证金率 0.5%
        
        if ($this->type == self::TYPE_BUY) {
            // 做多强平价格 = 开仓价格 - (保证金 - 维持保证金) / 数量
            $liquidationPrice = $this->avg_price - (($this->margin * (1 - $maintenanceMarginRate)) / $this->amount);
        } else {
            // 做空强平价格 = 开仓价格 + (保证金 - 维持保证金) / 数量
            $liquidationPrice = $this->avg_price + (($this->margin * (1 - $maintenanceMarginRate)) / $this->amount);
        }

        return max(0, $liquidationPrice);
    }

    /**
     * 计算未实现盈亏
     */
    public function calculateUnrealizedPnl(float $currentPrice): float
    {
        if ($this->status != self::STATUS_FILLED || $this->filled_amount <= 0) {
            return 0;
        }

        if ($this->type == self::TYPE_BUY) {
            // 做多盈亏 = (当前价格 - 开仓价格) * 数量
            return ($currentPrice - $this->avg_price) * $this->filled_amount;
        } else {
            // 做空盈亏 = (开仓价格 - 当前价格) * 数量
            return ($this->avg_price - $currentPrice) * $this->filled_amount;
        }
    }

    /**
     * 计算保证金率
     */
    public function calculateMarginRatio(float $currentPrice): float
    {
        $unrealizedPnl = $this->calculateUnrealizedPnl($currentPrice);
        $equity = $this->margin + $unrealizedPnl;
        $notionalValue = $this->filled_amount * $currentPrice;

        if ($notionalValue <= 0) {
            return 0;
        }

        return ($equity / $notionalValue) * 100;
    }

    /**
     * 检查是否需要强平
     */
    public function shouldLiquidate(float $currentPrice): bool
    {
        $marginRatio = $this->calculateMarginRatio($currentPrice);
        $maintenanceMarginRate = 0.5; // 维持保证金率 0.5%

        return $marginRatio <= $maintenanceMarginRate;
    }

    /**
     * 执行强平
     */
    public function liquidate(float $liquidationPrice): bool
    {
        if ($this->status != self::STATUS_FILLED) {
            return false;
        }

        // 计算强平后的盈亏
        $liquidationPnl = $this->calculateUnrealizedPnl($liquidationPrice);
        
        $this->status = self::STATUS_LIQUIDATED;
        $this->pnl = $liquidationPnl;
        $this->liquidation_price = $liquidationPrice;
        $this->updated_at = date('Y-m-d H:i:s');

        return $this->save();
    }

    /**
     * 获取用户的杠杆持仓
     */
    public static function getUserPositions(int $userId, string $symbol = ''): array
    {
        $query = self::where('user_id', $userId)
                    ->where('status', self::STATUS_FILLED);
        
        if (!empty($symbol)) {
            $query->where('symbol', $symbol);
        }
        
        return $query->order('created_at', 'desc')
                    ->select()
                    ->toArray();
    }

    /**
     * 获取用户的杠杆订单历史
     */
    public static function getUserOrderHistory(int $userId, string $symbol = '', int $page = 1, int $limit = 20): array
    {
        $query = self::where('user_id', $userId);
        
        if (!empty($symbol)) {
            $query->where('symbol', $symbol);
        }
        
        $orders = $query->order('created_at', 'desc')
                       ->paginate([
                           'list_rows' => $limit,
                           'page' => $page
                       ]);
        
        return [
            'data' => $orders->items(),
            'total' => $orders->total(),
            'page' => $page,
            'limit' => $limit
        ];
    }

    /**
     * 获取需要强平的订单
     */
    public static function getLiquidationOrders(string $symbol, float $currentPrice): array
    {
        $orders = self::where('symbol', $symbol)
                     ->where('status', self::STATUS_FILLED)
                     ->select();

        $liquidationOrders = [];
        foreach ($orders as $order) {
            if ($order->shouldLiquidate($currentPrice)) {
                $liquidationOrders[] = $order;
            }
        }

        return $liquidationOrders;
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联交易对
     */
    public function tradingPair()
    {
        return $this->belongsTo(TradingPair::class, 'symbol', 'symbol');
    }

    /**
     * 平仓
     */
    public function closePosition(float $closePrice, float $closeAmount = 0): array
    {
        try {
            if ($this->status != self::STATUS_FILLED) {
                return ['code' => 0, 'msg' => '订单状态不允许平仓'];
            }

            $closeAmount = $closeAmount ?: $this->filled_amount;
            if ($closeAmount > $this->filled_amount) {
                return ['code' => 0, 'msg' => '平仓数量超过持仓数量'];
            }

            // 计算平仓盈亏
            $closePnl = $this->calculateUnrealizedPnl($closePrice) * ($closeAmount / $this->filled_amount);
            
            // 更新订单状态
            $this->filled_amount -= $closeAmount;
            $this->pnl += $closePnl;
            
            if ($this->filled_amount <= 0) {
                $this->status = self::STATUS_CANCELLED; // 完全平仓
            }
            
            $this->updated_at = date('Y-m-d H:i:s');
            $this->save();

            return [
                'code' => 1,
                'msg' => '平仓成功',
                'data' => [
                    'close_amount' => $closeAmount,
                    'close_price' => $closePrice,
                    'pnl' => $closePnl,
                    'remaining_amount' => $this->filled_amount
                ]
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '平仓失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取杠杆交易统计
     */
    public static function getLeverageStats(int $userId): array
    {
        $stats = self::where('user_id', $userId)
                    ->where('status', 'in', [self::STATUS_FILLED, self::STATUS_CANCELLED, self::STATUS_LIQUIDATED])
                    ->field([
                        'COUNT(*) as total_orders',
                        'SUM(CASE WHEN pnl > 0 THEN 1 ELSE 0 END) as win_orders',
                        'SUM(pnl) as total_pnl',
                        'SUM(margin) as total_margin',
                        'MAX(leverage) as max_leverage'
                    ])
                    ->find();

        if (!$stats) {
            return [
                'total_orders' => 0,
                'win_orders' => 0,
                'win_rate' => 0,
                'total_pnl' => 0,
                'total_margin' => 0,
                'max_leverage' => 0,
                'roi' => 0
            ];
        }

        $winRate = $stats['total_orders'] > 0 ? ($stats['win_orders'] / $stats['total_orders']) * 100 : 0;
        $roi = $stats['total_margin'] > 0 ? ($stats['total_pnl'] / $stats['total_margin']) * 100 : 0;

        return [
            'total_orders' => $stats['total_orders'],
            'win_orders' => $stats['win_orders'],
            'win_rate' => round($winRate, 2),
            'total_pnl' => $stats['total_pnl'],
            'total_margin' => $stats['total_margin'],
            'max_leverage' => $stats['max_leverage'],
            'roi' => round($roi, 2)
        ];
    }
}
