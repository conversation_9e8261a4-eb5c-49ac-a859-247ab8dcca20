{extend name="mobile/layout" /}

{block name="header-title"}币币交易{/block}

{block name="header-right"}
<button class="btn-header" onclick="showSymbolSelector()">
    <i class="fas fa-search"></i>
</button>
{/block}

{block name="css"}
<style>
.symbol-header {
    background: white;
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
}

.symbol-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.symbol-name {
    font-size: 18px;
    font-weight: 600;
    color: var(--dark-color);
}

.symbol-price {
    font-size: 18px;
    font-weight: 600;
}

.symbol-stats {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: var(--text-muted);
}

.chart-container {
    background: white;
    height: 300px;
    position: relative;
}

.chart-tabs {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
    display: flex;
    gap: 4px;
}

.chart-tab {
    padding: 4px 8px;
    background: rgba(0,0,0,0.1);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
}

.chart-tab.active {
    background: var(--primary-color);
}

.trading-section {
    background: white;
    margin-top: 8px;
}

.trade-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
}

.trade-tab {
    flex: 1;
    padding: 12px;
    text-align: center;
    background: none;
    border: none;
    font-size: 16px;
    font-weight: 500;
    color: var(--text-muted);
    border-bottom: 2px solid transparent;
}

.trade-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.trade-tab.buy.active {
    color: var(--success-color);
    border-bottom-color: var(--success-color);
}

.trade-tab.sell.active {
    color: var(--danger-color);
    border-bottom-color: var(--danger-color);
}

.trade-form {
    padding: 16px;
    display: none;
}

.trade-form.active {
    display: block;
}

.balance-info {
    background: var(--light-color);
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 16px;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--dark-color);
}

.input-group {
    position: relative;
}

.input-group .form-control {
    padding-right: 60px;
}

.input-group .input-group-text {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 14px;
    z-index: 5;
}

.percentage-buttons {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.percentage-buttons .btn {
    flex: 1;
    padding: 8px;
    font-size: 14px;
    border: 1px solid var(--border-color);
    background: white;
    color: var(--text-muted);
}

.percentage-buttons .btn:active {
    background: var(--light-color);
}

.btn-trade {
    width: 100%;
    padding: 16px;
    font-size: 16px;
    font-weight: 600;
    border: none;
    border-radius: 8px;
}

.btn-buy {
    background: var(--success-color);
    color: white;
}

.btn-sell {
    background: var(--danger-color);
    color: white;
}

.market-data {
    background: white;
    margin-top: 8px;
}

.market-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
}

.market-tab {
    flex: 1;
    padding: 12px;
    text-align: center;
    background: none;
    border: none;
    font-size: 14px;
    color: var(--text-muted);
    border-bottom: 2px solid transparent;
}

.market-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.market-content {
    display: none;
    max-height: 300px;
    overflow-y: auto;
}

.market-content.active {
    display: block;
}

.orderbook-item,
.trade-item,
.order-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 16px;
    font-size: 14px;
    border-bottom: 1px solid var(--light-color);
}

.orderbook-item:last-child,
.trade-item:last-child,
.order-item:last-child {
    border-bottom: none;
}

.orderbook-spread {
    text-align: center;
    padding: 8px;
    background: var(--light-color);
    font-size: 12px;
    color: var(--text-muted);
}

.symbol-selector-modal .modal-dialog {
    margin: 0;
    max-width: 100%;
    height: 100%;
}

.symbol-selector-modal .modal-content {
    height: 100%;
    border-radius: 0;
}

.symbol-search {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
}

.symbol-list {
    flex: 1;
    overflow-y: auto;
}

.symbol-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid var(--light-color);
    cursor: pointer;
}

.symbol-item:active {
    background: var(--light-color);
}

.symbol-item-left {
    flex: 1;
}

.symbol-item-name {
    font-weight: 600;
    margin-bottom: 4px;
}

.symbol-item-volume {
    font-size: 12px;
    color: var(--text-muted);
}

.symbol-item-right {
    text-align: right;
}

.symbol-item-price {
    font-weight: 600;
    margin-bottom: 4px;
}

.symbol-item-change {
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 4px;
}
</style>
{/block}

{block name="content"}
<!-- 交易对信息 -->
<div class="symbol-header">
    <div class="symbol-info">
        <div class="symbol-name" id="currentSymbol">BTC/USDT</div>
        <div class="symbol-price price-up" id="currentPrice">$45,230.50</div>
    </div>
    <div class="symbol-stats">
        <span>24h涨跌: <span class="price-up" id="priceChange">+2.34%</span></span>
        <span>24h高: <span id="high24h">$46,500</span></span>
        <span>24h低: <span id="low24h">$44,100</span></span>
        <span>24h量: <span id="volume24h">1,234.56</span></span>
    </div>
</div>

<!-- K线图 -->
<div class="chart-container">
    <div class="chart-tabs">
        <button class="chart-tab active" data-interval="1m">1分</button>
        <button class="chart-tab" data-interval="5m">5分</button>
        <button class="chart-tab" data-interval="15m">15分</button>
        <button class="chart-tab" data-interval="1h">1时</button>
        <button class="chart-tab" data-interval="1d">1天</button>
    </div>
    <div id="mobileChart" style="width: 100%; height: 100%;"></div>
</div>

<!-- 交易区域 -->
<div class="trading-section">
    <div class="trade-tabs">
        <button class="trade-tab buy active" data-type="buy">买入</button>
        <button class="trade-tab sell" data-type="sell">卖出</button>
    </div>

    <!-- 买入表单 -->
    <div class="trade-form active" id="buyForm">
        <div class="balance-info">
            <span>可用余额</span>
            <span id="usdtBalance">1,000.00 USDT</span>
        </div>
        
        <div class="form-group">
            <label>价格</label>
            <div class="input-group">
                <input type="number" class="form-control" id="buyPrice" placeholder="市价">
                <span class="input-group-text">USDT</span>
            </div>
        </div>
        
        <div class="form-group">
            <label>数量</label>
            <div class="input-group">
                <input type="number" class="form-control" id="buyAmount" placeholder="0.00">
                <span class="input-group-text">BTC</span>
            </div>
        </div>
        
        <div class="percentage-buttons">
            <button class="btn" data-percent="25">25%</button>
            <button class="btn" data-percent="50">50%</button>
            <button class="btn" data-percent="75">75%</button>
            <button class="btn" data-percent="100">100%</button>
        </div>
        
        <div class="form-group">
            <label>总额</label>
            <div class="input-group">
                <input type="number" class="form-control" id="buyTotal" placeholder="0.00">
                <span class="input-group-text">USDT</span>
            </div>
        </div>
        
        <button class="btn-trade btn-buy" id="buyBtn">买入 BTC</button>
    </div>

    <!-- 卖出表单 -->
    <div class="trade-form" id="sellForm">
        <div class="balance-info">
            <span>可用余额</span>
            <span id="btcBalance">0.12345 BTC</span>
        </div>
        
        <div class="form-group">
            <label>价格</label>
            <div class="input-group">
                <input type="number" class="form-control" id="sellPrice" placeholder="市价">
                <span class="input-group-text">USDT</span>
            </div>
        </div>
        
        <div class="form-group">
            <label>数量</label>
            <div class="input-group">
                <input type="number" class="form-control" id="sellAmount" placeholder="0.00">
                <span class="input-group-text">BTC</span>
            </div>
        </div>
        
        <div class="percentage-buttons">
            <button class="btn" data-percent="25">25%</button>
            <button class="btn" data-percent="50">50%</button>
            <button class="btn" data-percent="75">75%</button>
            <button class="btn" data-percent="100">100%</button>
        </div>
        
        <div class="form-group">
            <label>总额</label>
            <div class="input-group">
                <input type="number" class="form-control" id="sellTotal" placeholder="0.00">
                <span class="input-group-text">USDT</span>
            </div>
        </div>
        
        <button class="btn-trade btn-sell" id="sellBtn">卖出 BTC</button>
    </div>
</div>

<!-- 市场数据 -->
<div class="market-data">
    <div class="market-tabs">
        <button class="market-tab active" data-content="orderbook">深度</button>
        <button class="market-tab" data-content="trades">成交</button>
        <button class="market-tab" data-content="orders">订单</button>
    </div>

    <!-- 订单簿 -->
    <div class="market-content active" id="orderbook">
        <div id="orderbookAsks"></div>
        <div class="orderbook-spread">价差: <span id="spread">0.50</span></div>
        <div id="orderbookBids"></div>
    </div>

    <!-- 最新成交 -->
    <div class="market-content" id="trades">
        <div id="recentTrades"></div>
    </div>

    <!-- 我的订单 -->
    <div class="market-content" id="orders">
        <div id="userOrders"></div>
    </div>
</div>

<!-- 交易对选择模态框 -->
<div class="modal fade symbol-selector-modal" id="symbolSelectorModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content d-flex flex-column">
            <div class="modal-header">
                <h5 class="modal-title">选择交易对</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            
            <div class="symbol-search">
                <input type="text" class="form-control" id="symbolSearchInput" placeholder="搜索交易对">
            </div>
            
            <div class="symbol-list" id="symbolList">
                <!-- 交易对列表将在这里动态加载 -->
            </div>
        </div>
    </div>
</div>
{/block}

{block name="js"}
<script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>

<script>
$(document).ready(function() {
    let currentSymbol = 'BTCUSDT';
    let currentInterval = '1m';
    let chart = null;
    let candlestickSeries = null;
    let ws = null;

    // 初始化
    init();

    function init() {
        initChart();
        initWebSocket();
        loadInitialData();
        bindEvents();
    }

    // 初始化图表
    function initChart() {
        const chartContainer = document.getElementById('mobileChart');
        
        chart = LightweightCharts.createChart(chartContainer, {
            width: chartContainer.clientWidth,
            height: 300,
            layout: {
                backgroundColor: '#ffffff',
                textColor: '#333',
            },
            grid: {
                vertLines: { color: '#f0f0f0' },
                horzLines: { color: '#f0f0f0' },
            },
            crosshair: {
                mode: LightweightCharts.CrosshairMode.Normal,
            },
            rightPriceScale: {
                borderColor: '#cccccc',
                scaleMargins: { top: 0.1, bottom: 0.1 },
            },
            timeScale: {
                borderColor: '#cccccc',
                timeVisible: true,
                secondsVisible: false,
            },
        });

        candlestickSeries = chart.addCandlestickSeries({
            upColor: '#26a69a',
            downColor: '#ef5350',
            borderVisible: false,
            wickUpColor: '#26a69a',
            wickDownColor: '#ef5350',
        });

        // 响应式调整
        const resizeObserver = new ResizeObserver(entries => {
            chart.applyOptions({ width: chartContainer.clientWidth });
        });
        resizeObserver.observe(chartContainer);
    }

    // 初始化WebSocket
    function initWebSocket() {
        if (ws) {
            ws.close();
        }

        ws = new WebSocket('ws://localhost:9501');
        
        ws.onopen = function() {
            console.log('WebSocket连接已建立');
            subscribeToStreams();
        };

        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            handleWebSocketMessage(data);
        };

        ws.onclose = function() {
            console.log('WebSocket连接已关闭');
            setTimeout(initWebSocket, 5000);
        };
    }

    // 订阅数据流
    function subscribeToStreams() {
        const subscribeMessage = {
            method: 'SUBSCRIBE',
            params: [
                `${currentSymbol.toLowerCase()}@ticker`,
                `${currentSymbol.toLowerCase()}@depth`,
                `${currentSymbol.toLowerCase()}@trade`,
                `${currentSymbol.toLowerCase()}@kline_${currentInterval}`
            ],
            id: 1
        };

        ws.send(JSON.stringify(subscribeMessage));
    }

    // 处理WebSocket消息
    function handleWebSocketMessage(data) {
        if (data.stream) {
            const stream = data.stream;
            
            if (stream.includes('@ticker')) {
                updateTicker(data.data);
            } else if (stream.includes('@depth')) {
                updateOrderbook(data.data);
            } else if (stream.includes('@trade')) {
                updateRecentTrades(data.data);
            } else if (stream.includes('@kline')) {
                updateChart(data.data);
            }
        }
    }

    // 更新价格信息
    function updateTicker(data) {
        $('#currentPrice').text('$' + parseFloat(data.c).toFixed(2));
        
        const change = parseFloat(data.P);
        const changeElement = $('#priceChange');
        
        changeElement.text((change >= 0 ? '+' : '') + change.toFixed(2) + '%');
        changeElement.removeClass('price-up price-down');
        changeElement.addClass(change >= 0 ? 'price-up' : 'price-down');
        
        $('#high24h').text('$' + parseFloat(data.h).toFixed(2));
        $('#low24h').text('$' + parseFloat(data.l).toFixed(2));
        $('#volume24h').text(parseFloat(data.v).toFixed(2));
    }

    // 更新订单簿
    function updateOrderbook(data) {
        const asksHtml = data.asks.slice(0, 5).reverse().map(ask => `
            <div class="orderbook-item">
                <span class="price-down">${parseFloat(ask[0]).toFixed(2)}</span>
                <span>${parseFloat(ask[1]).toFixed(6)}</span>
            </div>
        `).join('');

        const bidsHtml = data.bids.slice(0, 5).map(bid => `
            <div class="orderbook-item">
                <span class="price-up">${parseFloat(bid[0]).toFixed(2)}</span>
                <span>${parseFloat(bid[1]).toFixed(6)}</span>
            </div>
        `).join('');

        $('#orderbookAsks').html(asksHtml);
        $('#orderbookBids').html(bidsHtml);

        if (data.asks.length > 0 && data.bids.length > 0) {
            const spread = parseFloat(data.asks[0][0]) - parseFloat(data.bids[0][0]);
            $('#spread').text(spread.toFixed(2));
        }
    }

    // 更新最新成交
    function updateRecentTrades(data) {
        const time = new Date(data.T).toLocaleTimeString();
        const price = parseFloat(data.p).toFixed(2);
        const amount = parseFloat(data.q).toFixed(6);
        const isBuy = !data.m;

        const tradeHtml = `
            <div class="trade-item">
                <span>${time}</span>
                <span class="${isBuy ? 'price-up' : 'price-down'}">${price}</span>
                <span>${amount}</span>
            </div>
        `;

        const tradesContainer = $('#recentTrades');
        tradesContainer.prepend(tradeHtml);
        
        if (tradesContainer.children().length > 20) {
            tradesContainer.children().last().remove();
        }
    }

    // 更新图表
    function updateChart(data) {
        const kline = data.k;
        const candleData = {
            time: kline.t / 1000,
            open: parseFloat(kline.o),
            high: parseFloat(kline.h),
            low: parseFloat(kline.l),
            close: parseFloat(kline.c)
        };

        candlestickSeries.update(candleData);
    }

    // 加载初始数据
    function loadInitialData() {
        loadKlineData();
        loadUserBalance();
        loadUserOrders();
        loadSymbolList();
    }

    // 加载K线数据
    function loadKlineData() {
        $.get('/api/market/klines', {
            symbol: currentSymbol,
            interval: currentInterval,
            limit: 100
        }).done(function(response) {
            if (response.code === 1) {
                const klineData = response.data.map(item => ({
                    time: item[0] / 1000,
                    open: parseFloat(item[1]),
                    high: parseFloat(item[2]),
                    low: parseFloat(item[3]),
                    close: parseFloat(item[4])
                }));

                candlestickSeries.setData(klineData);
            }
        });
    }

    // 加载用户余额
    function loadUserBalance() {
        $.get('/api/user/assets').done(function(response) {
            if (response.code === 1) {
                response.data.forEach(asset => {
                    if (asset.coin_symbol === 'USDT') {
                        $('#usdtBalance').text(asset.available + ' USDT');
                    } else if (asset.coin_symbol === 'BTC') {
                        $('#btcBalance').text(asset.available + ' BTC');
                    }
                });
            }
        });
    }

    // 加载用户订单
    function loadUserOrders() {
        $.get('/api/trade/openOrders', {
            symbol: currentSymbol
        }).done(function(response) {
            if (response.code === 1) {
                updateOrdersDisplay(response.data);
            }
        });
    }

    // 更新订单显示
    function updateOrdersDisplay(orders) {
        const ordersHtml = orders.map(order => `
            <div class="order-item">
                <div>
                    <div class="${order.side.toLowerCase() === 'buy' ? 'price-up' : 'price-down'}">${order.side} ${parseFloat(order.orig_qty).toFixed(6)}</div>
                    <div style="font-size: 12px; color: var(--text-muted);">${parseFloat(order.price).toFixed(2)}</div>
                </div>
                <button class="btn btn-sm btn-outline-danger" onclick="cancelOrder('${order.order_id}')">取消</button>
            </div>
        `).join('');

        $('#userOrders').html(ordersHtml || '<div class="empty-state"><i class="fas fa-inbox"></i><div>暂无订单</div></div>');
    }

    // 加载交易对列表
    function loadSymbolList() {
        $.get('/api/market/symbols').done(function(response) {
            if (response.code === 1) {
                updateSymbolList(response.data);
            }
        });
    }

    // 更新交易对列表
    function updateSymbolList(symbols) {
        const symbolsHtml = symbols.map(symbol => `
            <div class="symbol-item" onclick="selectSymbol('${symbol.symbol}')">
                <div class="symbol-item-left">
                    <div class="symbol-item-name">${symbol.symbol}</div>
                    <div class="symbol-item-volume">24h量: ${symbol.volume || '0'}</div>
                </div>
                <div class="symbol-item-right">
                    <div class="symbol-item-price">$${symbol.price || '0.00'}</div>
                    <div class="symbol-item-change price-up">+0.00%</div>
                </div>
            </div>
        `).join('');

        $('#symbolList').html(symbolsHtml);
    }

    // 绑定事件
    function bindEvents() {
        // 交易类型切换
        $('.trade-tab').click(function() {
            const type = $(this).data('type');
            
            $('.trade-tab').removeClass('active');
            $(this).addClass('active');
            
            $('.trade-form').removeClass('active');
            $(`#${type}Form`).addClass('active');
        });

        // 市场数据切换
        $('.market-tab').click(function() {
            const content = $(this).data('content');
            
            $('.market-tab').removeClass('active');
            $(this).addClass('active');
            
            $('.market-content').removeClass('active');
            $(`#${content}`).addClass('active');
        });

        // 时间周期切换
        $('.chart-tab').click(function() {
            const interval = $(this).data('interval');
            
            $('.chart-tab').removeClass('active');
            $(this).addClass('active');
            
            currentInterval = interval;
            loadKlineData();
            
            if (ws && ws.readyState === WebSocket.OPEN) {
                subscribeToStreams();
            }
        });

        // 百分比按钮
        $('.percentage-buttons .btn').click(function() {
            const percent = $(this).data('percent');
            const isBuy = $(this).closest('#buyForm').length > 0;
            
            if (isBuy) {
                const balance = parseFloat($('#usdtBalance').text());
                const price = parseFloat($('#buyPrice').val()) || parseFloat($('#currentPrice').text().replace('$', ''));
                const amount = (balance * percent / 100) / price;
                
                $('#buyAmount').val(amount.toFixed(6));
                $('#buyTotal').val((balance * percent / 100).toFixed(2));
            } else {
                const balance = parseFloat($('#btcBalance').text());
                const amount = balance * percent / 100;
                const price = parseFloat($('#sellPrice').val()) || parseFloat($('#currentPrice').text().replace('$', ''));
                
                $('#sellAmount').val(amount.toFixed(6));
                $('#sellTotal').val((amount * price).toFixed(2));
            }
        });

        // 价格/数量/总额联动
        $('#buyPrice, #buyAmount').on('input', function() {
            const price = parseFloat($('#buyPrice').val()) || 0;
            const amount = parseFloat($('#buyAmount').val()) || 0;
            $('#buyTotal').val((price * amount).toFixed(2));
        });

        $('#buyTotal').on('input', function() {
            const total = parseFloat($('#buyTotal').val()) || 0;
            const price = parseFloat($('#buyPrice').val()) || parseFloat($('#currentPrice').text().replace('$', ''));
            $('#buyAmount').val((total / price).toFixed(6));
        });

        $('#sellPrice, #sellAmount').on('input', function() {
            const price = parseFloat($('#sellPrice').val()) || 0;
            const amount = parseFloat($('#sellAmount').val()) || 0;
            $('#sellTotal').val((price * amount).toFixed(2));
        });

        $('#sellTotal').on('input', function() {
            const total = parseFloat($('#sellTotal').val()) || 0;
            const price = parseFloat($('#sellPrice').val()) || parseFloat($('#currentPrice').text().replace('$', ''));
            $('#sellAmount').val((total / price).toFixed(6));
        });

        // 买入卖出按钮
        $('#buyBtn').click(() => placeOrder('BUY'));
        $('#sellBtn').click(() => placeOrder('SELL'));

        // 交易对搜索
        $('#symbolSearchInput').on('input', function() {
            const keyword = $(this).val().toLowerCase();
            $('.symbol-item').each(function() {
                const symbolName = $(this).find('.symbol-item-name').text().toLowerCase();
                $(this).toggle(symbolName.includes(keyword));
            });
        });
    }

    // 显示交易对选择器
    window.showSymbolSelector = function() {
        const modal = new bootstrap.Modal(document.getElementById('symbolSelectorModal'));
        modal.show();
    };

    // 选择交易对
    window.selectSymbol = function(symbol) {
        currentSymbol = symbol.replace('/', '');
        $('#currentSymbol').text(symbol);
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('symbolSelectorModal'));
        modal.hide();
        
        // 重新加载数据
        loadKlineData();
        loadUserOrders();
        
        if (ws && ws.readyState === WebSocket.OPEN) {
            subscribeToStreams();
        }
    };

    // 下单
    function placeOrder(side) {
        const isBuy = side === 'BUY';
        const price = isBy ? $('#buyPrice').val() : $('#sellPrice').val();
        const amount = isBy ? $('#buyAmount').val() : $('#sellAmount').val();
        
        if (!amount || parseFloat(amount) <= 0) {
            showToast('请输入有效的数量', 'warning');
            return;
        }

        showLoading();

        const orderData = {
            symbol: currentSymbol,
            side: side,
            type: price ? 'LIMIT' : 'MARKET',
            quantity: amount
        };

        if (price) {
            orderData.price = price;
        }

        $.post('/api/trade/order', orderData)
            .done(function(response) {
                if (response.code === 1) {
                    showToast('订单提交成功', 'success');
                    
                    // 清空表单
                    if (isBy) {
                        $('#buyAmount, #buyTotal').val('');
                    } else {
                        $('#sellAmount, #sellTotal').val('');
                    }
                    
                    // 刷新数据
                    loadUserBalance();
                    loadUserOrders();
                } else {
                    showToast(response.msg || '订单提交失败', 'danger');
                }
            })
            .fail(function() {
                showToast('网络错误，请稍后重试', 'danger');
            })
            .always(function() {
                hideLoading();
            });
    }

    // 取消订单
    window.cancelOrder = function(orderId) {
        if (!confirm('确定要取消这个订单吗？')) {
            return;
        }

        showLoading();

        $.ajax({
            url: '/api/trade/cancelOrder',
            method: 'DELETE',
            data: {
                symbol: currentSymbol,
                order_id: orderId
            }
        }).done(function(response) {
            if (response.code === 1) {
                showToast('订单取消成功', 'success');
                loadUserOrders();
            } else {
                showToast(response.msg || '订单取消失败', 'danger');
            }
        }).always(function() {
            hideLoading();
        });
    };

    // 刷新数据函数
    window.refreshData = function() {
        loadUserBalance();
        loadUserOrders();
        loadKlineData();
    };
});
</script>
{/block}
