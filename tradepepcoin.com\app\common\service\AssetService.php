<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\User;
use app\common\model\UserAsset;
use app\common\model\FinancialRecord;
use app\common\model\Coin;
use think\facade\Db;
use think\facade\Log;

/**
 * 资产服务类 - 兼容老系统资产管理逻辑
 */
class AssetService
{
    /**
     * 获取用户所有资产 - 兼容老系统格式
     */
    public function getUserAssets(int $userId): array
    {
        try {
            $user = User::find($userId);
            if (!$user) {
                return ['code' => 0, 'msg' => '用户不存在'];
            }
            
            // 获取所有币种
            $coins = Coin::where('status', 1)->select();
            $assets = [];
            
            foreach ($coins as $coin) {
                $asset = UserAsset::getOrCreate($userId, $coin->symbol);
                
                // 兼容老系统格式
                $assets[strtolower($coin->symbol)] = [
                    'available' => $asset->available,
                    'frozen' => $asset->frozen,
                    'total' => $asset->total,
                    'coin_name' => $coin->name,
                    'coin_symbol' => $coin->symbol,
                ];
            }
            
            // 添加体验金
            $assets['money'] = [
                'available' => $user->money,
                'frozen' => 0,
                'total' => $user->money,
                'coin_name' => '体验金',
                'coin_symbol' => 'MONEY',
            ];
            
            return ['code' => 1, 'data' => $assets];
            
        } catch (\Exception $e) {
            Log::error('获取用户资产失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取资产失败'];
        }
    }
    
    /**
     * 增加用户资产 - 兼容老系统逻辑
     */
    public function addAsset(int $userId, string $coinSymbol, float $amount, string $remark = '', int $type = 1): array
    {
        if ($amount <= 0) {
            return ['code' => 0, 'msg' => '金额必须大于0'];
        }
        
        try {
            Db::startTrans();
            
            $user = User::find($userId);
            if (!$user) {
                Db::rollback();
                return ['code' => 0, 'msg' => '用户不存在'];
            }
            
            // 特殊处理体验金
            if (strtolower($coinSymbol) == 'money') {
                $beforeAmount = $user->money;
                $user->money += $amount;
                $user->save();
                $afterAmount = $user->money;
            } else {
                // 处理普通币种
                $asset = UserAsset::getOrCreate($userId, strtoupper($coinSymbol));
                $beforeAmount = $asset->available;
                
                if (!$asset->addAvailable($amount)) {
                    Db::rollback();
                    return ['code' => 0, 'msg' => '资产增加失败'];
                }
                
                $afterAmount = $asset->available;
            }
            
            // 记录账单 - 兼容老系统格式
            $this->addBill([
                'uid' => $userId,
                'username' => $user->username,
                'coinname' => strtoupper($coinSymbol),
                'num' => $amount,
                'afternum' => $afterAmount,
                'type' => $type,
                'st' => 1, // 收入
                'remark' => $remark ?: '资产增加',
            ]);
            
            Db::commit();
            return ['code' => 1, 'msg' => '资产增加成功'];
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('增加用户资产失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '操作失败'];
        }
    }
    
    /**
     * 减少用户资产 - 兼容老系统逻辑
     */
    public function subAsset(int $userId, string $coinSymbol, float $amount, string $remark = '', int $type = 2): array
    {
        if ($amount <= 0) {
            return ['code' => 0, 'msg' => '金额必须大于0'];
        }
        
        try {
            Db::startTrans();
            
            $user = User::find($userId);
            if (!$user) {
                Db::rollback();
                return ['code' => 0, 'msg' => '用户不存在'];
            }
            
            // 特殊处理体验金
            if (strtolower($coinSymbol) == 'money') {
                if ($user->money < $amount) {
                    Db::rollback();
                    return ['code' => 0, 'msg' => '体验金余额不足'];
                }
                
                $beforeAmount = $user->money;
                $user->money -= $amount;
                $user->save();
                $afterAmount = $user->money;
            } else {
                // 处理普通币种
                $asset = UserAsset::getOrCreate($userId, strtoupper($coinSymbol));
                $beforeAmount = $asset->available;
                
                if (!$asset->subAvailable($amount)) {
                    Db::rollback();
                    return ['code' => 0, 'msg' => '余额不足'];
                }
                
                $afterAmount = $asset->available;
            }
            
            // 记录账单 - 兼容老系统格式
            $this->addBill([
                'uid' => $userId,
                'username' => $user->username,
                'coinname' => strtoupper($coinSymbol),
                'num' => $amount,
                'afternum' => $afterAmount,
                'type' => $type,
                'st' => 2, // 支出
                'remark' => $remark ?: '资产减少',
            ]);
            
            Db::commit();
            return ['code' => 1, 'msg' => '资产减少成功'];
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('减少用户资产失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '操作失败'];
        }
    }
    
    /**
     * 冻结用户资产 - 兼容老系统逻辑
     */
    public function freezeAsset(int $userId, string $coinSymbol, float $amount, string $remark = ''): array
    {
        if ($amount <= 0) {
            return ['code' => 0, 'msg' => '金额必须大于0'];
        }
        
        try {
            Db::startTrans();
            
            $user = User::find($userId);
            if (!$user) {
                Db::rollback();
                return ['code' => 0, 'msg' => '用户不存在'];
            }
            
            $asset = UserAsset::getOrCreate($userId, strtoupper($coinSymbol));
            
            if (!$asset->freeze($amount)) {
                Db::rollback();
                return ['code' => 0, 'msg' => '可用余额不足'];
            }
            
            // 记录账单
            $this->addBill([
                'uid' => $userId,
                'username' => $user->username,
                'coinname' => strtoupper($coinSymbol),
                'num' => $amount,
                'afternum' => $asset->available,
                'type' => 3,
                'st' => 3, // 冻结
                'remark' => $remark ?: '资产冻结',
            ]);
            
            Db::commit();
            return ['code' => 1, 'msg' => '资产冻结成功'];
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('冻结用户资产失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '操作失败'];
        }
    }
    
    /**
     * 解冻用户资产 - 兼容老系统逻辑
     */
    public function unfreezeAsset(int $userId, string $coinSymbol, float $amount, string $remark = ''): array
    {
        if ($amount <= 0) {
            return ['code' => 0, 'msg' => '金额必须大于0'];
        }
        
        try {
            Db::startTrans();
            
            $user = User::find($userId);
            if (!$user) {
                Db::rollback();
                return ['code' => 0, 'msg' => '用户不存在'];
            }
            
            $asset = UserAsset::getOrCreate($userId, strtoupper($coinSymbol));
            
            if (!$asset->unfreeze($amount)) {
                Db::rollback();
                return ['code' => 0, 'msg' => '冻结余额不足'];
            }
            
            // 记录账单
            $this->addBill([
                'uid' => $userId,
                'username' => $user->username,
                'coinname' => strtoupper($coinSymbol),
                'num' => $amount,
                'afternum' => $asset->available,
                'type' => 4,
                'st' => 4, // 解冻
                'remark' => $remark ?: '资产解冻',
            ]);
            
            Db::commit();
            return ['code' => 1, 'msg' => '资产解冻成功'];
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('解冻用户资产失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '操作失败'];
        }
    }
    
    /**
     * 扣除冻结资产 - 用于交易完成等场景
     */
    public function subFrozenAsset(int $userId, string $coinSymbol, float $amount, string $remark = ''): array
    {
        if ($amount <= 0) {
            return ['code' => 0, 'msg' => '金额必须大于0'];
        }
        
        try {
            Db::startTrans();
            
            $user = User::find($userId);
            if (!$user) {
                Db::rollback();
                return ['code' => 0, 'msg' => '用户不存在'];
            }
            
            $asset = UserAsset::getOrCreate($userId, strtoupper($coinSymbol));
            
            if (!$asset->subFrozen($amount)) {
                Db::rollback();
                return ['code' => 0, 'msg' => '冻结余额不足'];
            }
            
            // 记录账单
            $this->addBill([
                'uid' => $userId,
                'username' => $user->username,
                'coinname' => strtoupper($coinSymbol),
                'num' => $amount,
                'afternum' => $asset->frozen,
                'type' => 5,
                'st' => 2, // 支出
                'remark' => $remark ?: '扣除冻结资产',
            ]);
            
            Db::commit();
            return ['code' => 1, 'msg' => '操作成功'];
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('扣除冻结资产失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '操作失败'];
        }
    }
    
    /**
     * 添加账单记录 - 兼容老系统格式
     */
    private function addBill(array $data): bool
    {
        try {
            FinancialRecord::create([
                'user_id' => $data['uid'],
                'username' => $data['username'],
                'coin_symbol' => $data['coinname'],
                'amount' => $data['num'],
                'balance_after' => $data['afternum'],
                'type' => $data['type'],
                'direction' => $data['st'], // 1收入 2支出 3冻结 4解冻
                'remark' => $data['remark'],
                'created_at' => date('Y-m-d H:i:s'),
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('添加账单记录失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取用户财务统计 - 兼容老系统
     */
    public function getUserFinancialStats(int $userId): array
    {
        try {
            // 总收入
            $totalIncome = FinancialRecord::where('user_id', $userId)
                                         ->where('direction', 1)
                                         ->sum('amount');

            // 总支出
            $totalExpense = FinancialRecord::where('user_id', $userId)
                                          ->where('direction', 2)
                                          ->sum('amount');

            // 今日收入
            $todayIncome = FinancialRecord::where('user_id', $userId)
                                         ->where('direction', 1)
                                         ->whereTime('created_at', 'today')
                                         ->sum('amount');

            // 今日支出
            $todayExpense = FinancialRecord::where('user_id', $userId)
                                          ->where('direction', 2)
                                          ->whereTime('created_at', 'today')
                                          ->sum('amount');

            // 本月收入
            $monthIncome = FinancialRecord::where('user_id', $userId)
                                         ->where('direction', 1)
                                         ->whereTime('created_at', 'month')
                                         ->sum('amount');

            // 本月支出
            $monthExpense = FinancialRecord::where('user_id', $userId)
                                          ->where('direction', 2)
                                          ->whereTime('created_at', 'month')
                                          ->sum('amount');

            return [
                'total_income' => $totalIncome,
                'total_expense' => $totalExpense,
                'total_profit' => $totalIncome - $totalExpense,
                'today_income' => $todayIncome,
                'today_expense' => $todayExpense,
                'today_profit' => $todayIncome - $todayExpense,
                'month_income' => $monthIncome,
                'month_expense' => $monthExpense,
                'month_profit' => $monthIncome - $monthExpense,
            ];
        } catch (\Exception $e) {
            Log::error('获取用户财务统计失败：' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取用户账单记录 - 兼容老系统
     */
    public function getUserBills(int $userId, array $params = []): array
    {
        try {
            $where = ['user_id' => $userId];

            if (!empty($params['coin_symbol'])) {
                $where['coin_symbol'] = $params['coin_symbol'];
            }

            if (!empty($params['type'])) {
                $where['type'] = $params['type'];
            }

            if (!empty($params['direction'])) {
                $where['direction'] = $params['direction'];
            }

            if (!empty($params['start_date'])) {
                $where[] = ['created_at', '>=', $params['start_date']];
            }

            if (!empty($params['end_date'])) {
                $where[] = ['created_at', '<=', $params['end_date']];
            }

            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 20;

            $bills = FinancialRecord::where($where)
                                   ->order('created_at', 'desc')
                                   ->paginate([
                                       'list_rows' => $limit,
                                       'page' => $page
                                   ]);

            return [
                'code' => 1,
                'data' => $bills->items(),
                'total' => $bills->total(),
                'page' => $page,
                'limit' => $limit
            ];

        } catch (\Exception $e) {
            Log::error('获取用户账单记录失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }
}
