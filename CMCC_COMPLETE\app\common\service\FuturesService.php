<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

/**
 * 期货交易服务类
 */
class FuturesService
{
    // 合约类型
    const TYPE_PERPETUAL = 'perpetual';  // 永续合约
    const TYPE_QUARTERLY = 'quarterly';  // 季度合约
    const TYPE_WEEKLY = 'weekly';        // 周合约

    // 订单状态
    const STATUS_PENDING = 'pending';     // 待成交
    const STATUS_FILLED = 'filled';      // 已成交
    const STATUS_CANCELLED = 'cancelled'; // 已取消
    const STATUS_EXPIRED = 'expired';     // 已过期

    // 持仓方向
    const SIDE_LONG = 'long';   // 多头
    const SIDE_SHORT = 'short'; // 空头

    /**
     * 获取期货合约列表（增强版）
     */
    public function getContracts(): array
    {
        $contracts = Db::name('futures_contracts')
            ->where('status', 1)
            ->order('sort asc, id asc')
            ->select()
            ->toArray();

        // 添加实时价格信息
        foreach ($contracts as &$contract) {
            $ticker = $this->getContractTicker($contract['symbol']);
            $contract['current_price'] = $ticker['price'];
            $contract['change_24h'] = $ticker['change_24h'];
            $contract['volume_24h'] = $ticker['volume_24h'];
            $contract['open_interest'] = $this->getOpenInterest($contract['symbol']);
            $contract['funding_rate'] = $this->getFundingRate($contract['symbol']);
            $contract['next_funding_time'] = $this->getNextFundingTime($contract['symbol']);
        }

        return [
            'code' => 1,
            'msg' => '获取成功',
            'data' => $contracts
        ];
    }

    /**
     * 自动撮合引擎
     */
    public function autoMatchEngine(): void
    {
        try {
            // 获取所有活跃合约
            $contracts = Db::name('futures_contracts')
                ->where('status', 1)
                ->column('symbol');

            foreach ($contracts as $symbol) {
                $this->matchOrdersBySymbol($symbol);
            }

        } catch (\Exception $e) {
            Log::error('自动撮合引擎执行失败：' . $e->getMessage());
        }
    }

    /**
     * 按合约撮合订单
     */
    private function matchOrdersBySymbol(string $symbol): void
    {
        try {
            // 获取买单（按价格降序）
            $buyOrders = Db::name('futures_orders')
                ->where('symbol', $symbol)
                ->where('side', 'long')
                ->where('status', self::STATUS_PENDING)
                ->where('order_type', 'limit')
                ->order('price desc, created_at asc')
                ->select()
                ->toArray();

            // 获取卖单（按价格升序）
            $sellOrders = Db::name('futures_orders')
                ->where('symbol', $symbol)
                ->where('side', 'short')
                ->where('status', self::STATUS_PENDING)
                ->where('order_type', 'limit')
                ->order('price asc, created_at asc')
                ->select()
                ->toArray();

            // 撮合逻辑
            foreach ($buyOrders as $buyOrder) {
                foreach ($sellOrders as $sellOrder) {
                    if ($buyOrder['price'] >= $sellOrder['price']) {
                        $this->executeTrade($buyOrder, $sellOrder);
                        break; // 买单已完全成交，处理下一个买单
                    }
                }
            }

        } catch (\Exception $e) {
            Log::error("合约 {$symbol} 撮合失败：" . $e->getMessage());
        }
    }

    /**
     * 执行交易撮合
     */
    private function executeTrade(array $buyOrder, array $sellOrder): void
    {
        try {
            Db::startTrans();

            // 确定成交价格（取卖单价格）
            $tradePrice = $sellOrder['price'];

            // 确定成交数量（取较小值）
            $tradeQuantity = min($buyOrder['quantity'], $sellOrder['quantity']);

            // 更新买单
            $this->updateOrderAfterTrade($buyOrder, $tradeQuantity, $tradePrice);

            // 更新卖单
            $this->updateOrderAfterTrade($sellOrder, $tradeQuantity, $tradePrice);

            // 创建成交记录
            $this->createTradeRecord($buyOrder, $sellOrder, $tradePrice, $tradeQuantity);

            // 更新持仓
            $this->updatePositionAfterTrade($buyOrder, $tradePrice, $tradeQuantity, 'long');
            $this->updatePositionAfterTrade($sellOrder, $tradePrice, $tradeQuantity, 'short');

            Db::commit();

            Log::info('撮合成交', [
                'symbol' => $buyOrder['symbol'],
                'price' => $tradePrice,
                'quantity' => $tradeQuantity,
                'buy_order' => $buyOrder['order_id'],
                'sell_order' => $sellOrder['order_id']
            ]);

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('执行交易撮合失败：' . $e->getMessage());
        }
    }

    /**
     * 永续合约资金费率计算
     */
    public function calculateFundingRate(string $symbol): array
    {
        try {
            // 获取合约信息
            $contract = Db::name('futures_contracts')
                ->where('symbol', $symbol)
                ->where('type', self::TYPE_PERPETUAL)
                ->find();

            if (!$contract) {
                throw new \Exception('永续合约不存在');
            }

            // 获取标记价格和指数价格
            $markPrice = $this->getMarkPrice($symbol);
            $indexPrice = $this->getIndexPrice($symbol);

            // 计算溢价率
            $premiumRate = ($markPrice - $indexPrice) / $indexPrice;

            // 计算资金费率（简化公式）
            $fundingRate = $premiumRate * 0.01; // 1%的系数

            // 限制资金费率范围 -0.75% 到 +0.75%
            $fundingRate = max(-0.0075, min(0.0075, $fundingRate));

            // 保存资金费率
            Db::name('funding_rates')->insert([
                'symbol' => $symbol,
                'funding_rate' => $fundingRate,
                'mark_price' => $markPrice,
                'index_price' => $indexPrice,
                'premium_rate' => $premiumRate,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            return [
                'code' => 1,
                'msg' => '计算成功',
                'data' => [
                    'symbol' => $symbol,
                    'funding_rate' => $fundingRate,
                    'mark_price' => $markPrice,
                    'index_price' => $indexPrice,
                    'next_funding_time' => $this->getNextFundingTime($symbol)
                ]
            ];

        } catch (\Exception $e) {
            Log::error('计算资金费率失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 执行资金费用结算
     */
    public function settleFundingFee(): void
    {
        try {
            // 获取所有永续合约
            $perpetualContracts = Db::name('futures_contracts')
                ->where('type', self::TYPE_PERPETUAL)
                ->where('status', 1)
                ->column('symbol');

            foreach ($perpetualContracts as $symbol) {
                $this->settleFundingFeeBySymbol($symbol);
            }

        } catch (\Exception $e) {
            Log::error('资金费用结算失败：' . $e->getMessage());
        }
    }

    /**
     * 按合约结算资金费用
     */
    private function settleFundingFeeBySymbol(string $symbol): void
    {
        try {
            // 获取最新资金费率
            $fundingRate = $this->getCurrentFundingRate($symbol);

            if ($fundingRate == 0) {
                return;
            }

            // 获取所有持仓
            $positions = Db::name('futures_positions')
                ->where('symbol', $symbol)
                ->where('status', 'open')
                ->select()
                ->toArray();

            foreach ($positions as $position) {
                // 计算资金费用
                $fundingFee = $position['notional_value'] * $fundingRate;

                // 多头支付，空头收取
                if ($position['side'] === 'long') {
                    $fundingFee = -abs($fundingFee); // 多头支付（负数）
                } else {
                    $fundingFee = abs($fundingFee);  // 空头收取（正数）
                }

                // 更新用户资产
                if ($fundingFee > 0) {
                    $this->addAsset($position['user_id'], 'USDT', $fundingFee);
                } else {
                    $this->subAsset($position['user_id'], 'USDT', abs($fundingFee));
                }

                // 记录资金费用
                $this->recordFinancialLog(
                    $position['user_id'],
                    'USDT',
                    $fundingFee,
                    'funding_fee',
                    '资金费用',
                    $position['id']
                );
            }

            Log::info("合约 {$symbol} 资金费用结算完成", [
                'funding_rate' => $fundingRate,
                'positions_count' => count($positions)
            ]);

        } catch (\Exception $e) {
            Log::error("合约 {$symbol} 资金费用结算失败：" . $e->getMessage());
        }
    }

    /**
     * 创建期货订单
     */
    public function createOrder(array $data): array
    {
        try {
            Db::startTrans();

            // 获取合约信息
            $contract = $this->getContract($data['contract_symbol']);
            if (!$contract) {
                throw new \Exception('合约不存在');
            }

            // 验证订单参数
            $this->validateOrderParams($data, $contract);

            // 计算保证金
            $margin = $this->calculateMargin($data, $contract);

            // 验证用户资产
            $userAsset = $this->getUserAsset($data['user_id'], $contract['quote_coin']);
            if ($userAsset['available'] < $margin) {
                throw new \Exception('保证金不足');
            }

            // 创建订单
            $orderData = [
                'user_id' => $data['user_id'],
                'contract_symbol' => $data['contract_symbol'],
                'order_type' => $data['order_type'], // limit/market
                'side' => $data['side'], // buy/sell
                'quantity' => $data['quantity'],
                'price' => $data['price'] ?? 0,
                'margin' => $margin,
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $orderId = Db::name('futures_orders')->insertGetId($orderData);

            // 冻结保证金
            $this->freezeAsset($data['user_id'], $contract['quote_coin'], $margin);

            // 如果是市价单，立即撮合
            if ($data['order_type'] === 'market') {
                $this->matchMarketOrder($orderId);
            }

            Db::commit();

            return [
                'code' => 1,
                'msg' => '订单创建成功',
                'data' => [
                    'order_id' => $orderId,
                    'margin' => $margin
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('创建期货订单失败: ' . $e->getMessage());
            return [
                'code' => 0,
                'msg' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 取消订单
     */
    public function cancelOrder(int $orderId, int $userId): array
    {
        try {
            Db::startTrans();

            $order = Db::name('futures_orders')
                ->where('id', $orderId)
                ->where('user_id', $userId)
                ->where('status', 'pending')
                ->find();

            if (!$order) {
                throw new \Exception('订单不存在或无法取消');
            }

            // 更新订单状态
            Db::name('futures_orders')->where('id', $orderId)->update([
                'status' => 'cancelled',
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // 解冻保证金
            $contract = $this->getContract($order['contract_symbol']);
            $this->unfreezeAsset($userId, $contract['quote_coin'], $order['margin']);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '订单取消成功',
                'data' => null
            ];

        } catch (\Exception $e) {
            Db::rollback();
            return [
                'code' => 0,
                'msg' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 获取用户订单列表
     */
    public function getUserOrders(int $userId, array $params = []): array
    {
        $query = Db::name('futures_orders')->where('user_id', $userId);

        // 状态筛选
        if (!empty($params['status'])) {
            $query->where('status', $params['status']);
        }

        // 合约筛选
        if (!empty($params['contract_symbol'])) {
            $query->where('contract_symbol', $params['contract_symbol']);
        }

        // 分页
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 20;
        $offset = ($page - 1) * $limit;

        $total = $query->count();
        $orders = $query->order('id desc')
            ->limit($offset, $limit)
            ->select()
            ->toArray();

        return [
            'code' => 1,
            'msg' => '获取成功',
            'data' => [
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'orders' => $orders
            ]
        ];
    }

    /**
     * 获取用户持仓
     */
    public function getUserPositions(int $userId): array
    {
        $positions = Db::name('futures_positions')
            ->where('user_id', $userId)
            ->where('quantity', '>', 0)
            ->select()
            ->toArray();

        // 计算实时盈亏
        foreach ($positions as &$position) {
            $currentPrice = $this->getCurrentPrice($position['contract_symbol']);
            $position['current_price'] = $currentPrice;
            $position['unrealized_pnl'] = $this->calculatePositionPnl($position, $currentPrice);
        }

        return [
            'code' => 1,
            'msg' => '获取成功',
            'data' => $positions
        ];
    }

    /**
     * 订单撮合引擎
     */
    public function matchOrders(): void
    {
        // 获取所有活跃合约
        $contracts = Db::name('futures_contracts')
            ->where('status', 1)
            ->column('symbol');

        foreach ($contracts as $symbol) {
            $this->matchContractOrders($symbol);
        }
    }

    /**
     * 自动交割
     */
    public function autoSettle(): void
    {
        // 获取今日到期的交割合约
        $contracts = Db::name('futures_contracts')
            ->where('contract_type', 'delivery')
            ->where('delivery_date', date('Y-m-d'))
            ->where('status', 1)
            ->select();

        foreach ($contracts as $contract) {
            $this->settleContract($contract);
        }
    }

    /**
     * 获取合约信息
     */
    private function getContract(string $symbol): ?array
    {
        return Db::name('futures_contracts')
            ->where('symbol', $symbol)
            ->where('status', 1)
            ->find();
    }

    /**
     * 验证订单参数
     */
    private function validateOrderParams(array $data, array $contract): void
    {
        // 验证数量
        if ($data['quantity'] < $contract['min_quantity'] || $data['quantity'] > $contract['max_quantity']) {
            throw new \Exception('交易数量超出限制');
        }

        // 验证价格精度
        if ($data['order_type'] === 'limit') {
            $priceDecimals = strlen(substr(strrchr($data['price'], '.'), 1));
            if ($priceDecimals > $contract['price_precision']) {
                throw new \Exception('价格精度超出限制');
            }
        }

        // 验证数量精度
        $quantityDecimals = strlen(substr(strrchr($data['quantity'], '.'), 1));
        if ($quantityDecimals > $contract['quantity_precision']) {
            throw new \Exception('数量精度超出限制');
        }
    }

    /**
     * 计算保证金
     */
    private function calculateMargin(array $data, array $contract): float
    {
        $price = $data['price'] ?? $this->getCurrentPrice($contract['symbol']);
        $notional = $data['quantity'] * $price;
        return $notional * $contract['margin_rate'];
    }

    /**
     * 市价单撮合
     */
    private function matchMarketOrder(int $orderId): void
    {
        $order = Db::name('futures_orders')->where('id', $orderId)->find();
        if (!$order) return;

        $currentPrice = $this->getCurrentPrice($order['contract_symbol']);
        $contract = $this->getContract($order['contract_symbol']);

        // 计算手续费
        $fee = $order['quantity'] * $currentPrice * $contract['taker_fee'];

        // 更新订单状态
        Db::name('futures_orders')->where('id', $orderId)->update([
            'filled_quantity' => $order['quantity'],
            'avg_price' => $currentPrice,
            'fee' => $fee,
            'status' => 'filled',
            'filled_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        // 更新或创建持仓
        $this->updatePosition($order, $currentPrice);

        // 扣除手续费
        $this->subAsset($order['user_id'], $contract['quote_coin'], $fee);

        // 记录交易
        $this->recordTrade($order, $currentPrice, $fee);
    }

    /**
     * 合约订单撮合
     */
    private function matchContractOrders(string $symbol): void
    {
        // 获取买单和卖单
        $buyOrders = Db::name('futures_orders')
            ->where('contract_symbol', $symbol)
            ->where('side', 'buy')
            ->where('status', 'pending')
            ->where('order_type', 'limit')
            ->order('price desc, id asc')
            ->select();

        $sellOrders = Db::name('futures_orders')
            ->where('contract_symbol', $symbol)
            ->where('side', 'sell')
            ->where('status', 'pending')
            ->where('order_type', 'limit')
            ->order('price asc, id asc')
            ->select();

        // 撮合逻辑
        foreach ($buyOrders as $buyOrder) {
            foreach ($sellOrders as $sellOrder) {
                if ($buyOrder['price'] >= $sellOrder['price']) {
                    $this->executeTrade($buyOrder, $sellOrder);
                }
            }
        }
    }

    /**
     * 执行交易
     */
    private function executeTrade(array $buyOrder, array $sellOrder): void
    {
        $tradePrice = $sellOrder['price']; // 以卖单价格成交
        $tradeQuantity = min($buyOrder['quantity'] - $buyOrder['filled_quantity'], 
                           $sellOrder['quantity'] - $sellOrder['filled_quantity']);

        if ($tradeQuantity <= 0) return;

        try {
            Db::startTrans();

            // 更新买单
            $this->updateOrderFilled($buyOrder['id'], $tradeQuantity, $tradePrice);
            
            // 更新卖单
            $this->updateOrderFilled($sellOrder['id'], $tradeQuantity, $tradePrice);

            // 更新持仓
            $this->updatePosition($buyOrder, $tradePrice, $tradeQuantity);
            $this->updatePosition($sellOrder, $tradePrice, $tradeQuantity);

            Db::commit();

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('执行交易失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新订单成交信息
     */
    private function updateOrderFilled(int $orderId, float $quantity, float $price): void
    {
        $order = Db::name('futures_orders')->where('id', $orderId)->find();
        $newFilledQuantity = $order['filled_quantity'] + $quantity;
        $newAvgPrice = (($order['avg_price'] * $order['filled_quantity']) + ($price * $quantity)) / $newFilledQuantity;

        $updateData = [
            'filled_quantity' => $newFilledQuantity,
            'avg_price' => $newAvgPrice,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        if ($newFilledQuantity >= $order['quantity']) {
            $updateData['status'] = 'filled';
            $updateData['filled_at'] = date('Y-m-d H:i:s');
        } else {
            $updateData['status'] = 'partial';
        }

        Db::name('futures_orders')->where('id', $orderId)->update($updateData);
    }

    /**
     * 更新持仓
     */
    private function updatePosition(array $order, float $price, float $quantity = null): void
    {
        $quantity = $quantity ?? $order['filled_quantity'];
        
        $position = Db::name('futures_positions')
            ->where('user_id', $order['user_id'])
            ->where('contract_symbol', $order['contract_symbol'])
            ->find();

        if ($position) {
            // 更新现有持仓
            $newQuantity = $position['quantity'] + ($order['side'] === 'buy' ? $quantity : -$quantity);
            $newAvgPrice = (($position['avg_price'] * $position['quantity']) + ($price * $quantity)) / $newQuantity;

            Db::name('futures_positions')->where('id', $position['id'])->update([
                'quantity' => $newQuantity,
                'avg_price' => $newAvgPrice,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        } else {
            // 创建新持仓
            Db::name('futures_positions')->insert([
                'user_id' => $order['user_id'],
                'contract_symbol' => $order['contract_symbol'],
                'quantity' => $order['side'] === 'buy' ? $quantity : -$quantity,
                'avg_price' => $price,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }
    }

    /**
     * 记录交易
     */
    private function recordTrade(array $order, float $price, float $fee): void
    {
        Db::name('futures_trades')->insert([
            'user_id' => $order['user_id'],
            'order_id' => $order['id'],
            'contract_symbol' => $order['contract_symbol'],
            'side' => $order['side'],
            'quantity' => $order['filled_quantity'],
            'price' => $price,
            'fee' => $fee,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 合约交割
     */
    private function settleContract(array $contract): void
    {
        Log::info('开始交割合约: ' . $contract['symbol']);

        // 获取交割价格
        $settlementPrice = $this->getCurrentPrice($contract['symbol']);

        // 获取所有持仓
        $positions = Db::name('futures_positions')
            ->where('contract_symbol', $contract['symbol'])
            ->where('quantity', '<>', 0)
            ->select();

        foreach ($positions as $position) {
            $this->settlePosition($position, $settlementPrice);
        }

        // 禁用合约
        Db::name('futures_contracts')->where('id', $contract['id'])->update([
            'status' => 0,
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        Log::info('合约交割完成: ' . $contract['symbol']);
    }

    /**
     * 持仓交割
     */
    private function settlePosition(array $position, float $settlementPrice): void
    {
        $pnl = $this->calculatePositionPnl($position, $settlementPrice);
        
        // 结算盈亏
        if ($pnl > 0) {
            $this->addAsset($position['user_id'], 'USDT', $pnl);
        } else {
            $this->subAsset($position['user_id'], 'USDT', abs($pnl));
        }

        // 清空持仓
        Db::name('futures_positions')->where('id', $position['id'])->update([
            'quantity' => 0,
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        // 记录交割日志
        Db::name('financial_records')->insert([
            'user_id' => $position['user_id'],
            'coin_symbol' => 'USDT',
            'amount' => $pnl,
            'type' => 'futures_settlement',
            'remark' => '期货合约交割',
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 计算持仓盈亏
     */
    private function calculatePositionPnl(array $position, float $currentPrice): float
    {
        $priceDiff = $currentPrice - $position['avg_price'];
        return $priceDiff * $position['quantity'];
    }

    /**
     * 获取合约行情
     */
    private function getContractTicker(string $symbol): array
    {
        $cacheKey = 'ticker:' . $symbol;
        $ticker = Cache::get($cacheKey);
        
        if (!$ticker) {
            // 模拟行情数据
            $ticker = [
                'price' => $this->getCurrentPrice($symbol),
                'change_24h' => rand(-10, 10) / 100,
                'volume_24h' => rand(1000000, 10000000)
            ];
            Cache::set($cacheKey, $ticker, 60);
        }
        
        return $ticker;
    }

    /**
     * 获取当前价格
     */
    private function getCurrentPrice(string $symbol): float
    {
        $cacheKey = 'price:' . $symbol;
        $price = Cache::get($cacheKey);
        
        if (!$price) {
            // 从数据库或API获取价格
            $priceData = Db::name('market_prices')
                ->where('symbol', $symbol)
                ->order('id desc')
                ->find();
            
            $price = $priceData ? $priceData['price'] : 50000;
            Cache::set($cacheKey, $price, 10);
        }
        
        return (float)$price;
    }

    /**
     * 获取用户资产
     */
    private function getUserAsset(int $userId, string $coinSymbol): array
    {
        return Db::name('user_assets')
            ->where('user_id', $userId)
            ->where('coin_symbol', $coinSymbol)
            ->find() ?: ['available' => 0, 'frozen' => 0];
    }

    /**
     * 冻结资产
     */
    private function freezeAsset(int $userId, string $coinSymbol, float $amount): void
    {
        Db::name('user_assets')
            ->where('user_id', $userId)
            ->where('coin_symbol', $coinSymbol)
            ->dec('available', $amount)
            ->inc('frozen', $amount);
    }

    /**
     * 解冻资产
     */
    private function unfreezeAsset(int $userId, string $coinSymbol, float $amount): void
    {
        Db::name('user_assets')
            ->where('user_id', $userId)
            ->where('coin_symbol', $coinSymbol)
            ->dec('frozen', $amount)
            ->inc('available', $amount);
    }

    /**
     * 增加资产
     */
    private function addAsset(int $userId, string $coinSymbol, float $amount): void
    {
        Db::name('user_assets')
            ->where('user_id', $userId)
            ->where('coin_symbol', $coinSymbol)
            ->inc('available', $amount);
    }

    /**
     * 减少资产
     */
    private function subAsset(int $userId, string $coinSymbol, float $amount): void
    {
        Db::name('user_assets')
            ->where('user_id', $userId)
            ->where('coin_symbol', $coinSymbol)
            ->dec('available', $amount);
    }
}
