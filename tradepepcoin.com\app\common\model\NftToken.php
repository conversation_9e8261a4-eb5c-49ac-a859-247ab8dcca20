<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * NFT代币模型
 */
class NftToken extends Model
{
    protected $table = 'nft_tokens';

    // 设置字段信息
    protected $schema = [
        'id' => 'int',
        'token_id' => 'string',
        'collection_id' => 'string',
        'owner_id' => 'int',
        'creator_id' => 'int',
        'name' => 'string',
        'description' => 'text',
        'image' => 'string',
        'animation_url' => 'string',
        'external_url' => 'string',
        'attributes' => 'json',
        'metadata' => 'json',
        'token_uri' => 'string',
        'blockchain' => 'string',
        'contract_address' => 'string',
        'token_standard' => 'string',
        'listed_price' => 'decimal',
        'listed_currency' => 'string',
        'last_sale_price' => 'decimal',
        'last_sale_currency' => 'string',
        'view_count' => 'int',
        'like_count' => 'int',
        'status' => 'int',
        'is_featured' => 'int',
        'rarity_rank' => 'int',
        'rarity_score' => 'decimal',
        'listed_at' => 'datetime',
        'last_sale_at' => 'datetime',
        'minted_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // JSON字段
    protected $json = ['attributes', 'metadata'];

    // NFT状态
    const STATUS_DRAFT = 0;      // 草稿
    const STATUS_MINTED = 1;     // 已铸造
    const STATUS_LISTED = 2;     // 已上架
    const STATUS_SOLD = 3;       // 已售出
    const STATUS_BURNED = 4;     // 已销毁
    const STATUS_TRANSFERRED = 5; // 已转移

    // 区块链网络
    const BLOCKCHAIN_ETHEREUM = 'ETH';
    const BLOCKCHAIN_BSC = 'BSC';
    const BLOCKCHAIN_POLYGON = 'MATIC';
    const BLOCKCHAIN_SOLANA = 'SOL';

    // 代币标准
    const STANDARD_ERC721 = 'ERC-721';
    const STANDARD_ERC1155 = 'ERC-1155';
    const STANDARD_SPL = 'SPL';

    /**
     * 关联集合
     */
    public function collection()
    {
        return $this->belongsTo(NftCollection::class, 'collection_id', 'collection_id');
    }

    /**
     * 关联所有者
     */
    public function owner()
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    /**
     * 关联创建者
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    /**
     * 关联订单
     */
    public function orders()
    {
        return $this->hasMany(NftOrder::class, 'token_id', 'token_id');
    }

    /**
     * 关联拍卖
     */
    public function auctions()
    {
        return $this->hasMany(NftAuction::class, 'token_id', 'token_id');
    }

    /**
     * 关联交易记录
     */
    public function trades()
    {
        return $this->hasMany(NftTrade::class, 'token_id', 'token_id');
    }

    /**
     * 关联收藏记录
     */
    public function favorites()
    {
        return $this->hasMany(NftFavorite::class, 'token_id', 'token_id');
    }

    /**
     * 转移所有权
     */
    public function transfer(int $newOwnerId, string $reason = 'transfer'): bool
    {
        $oldOwnerId = $this->owner_id;
        
        $this->owner_id = $newOwnerId;
        $this->status = self::STATUS_TRANSFERRED;
        
        if ($this->save()) {
            // 记录转移历史
            $this->recordTransfer($oldOwnerId, $newOwnerId, $reason);
            return true;
        }
        
        return false;
    }

    /**
     * 上架NFT
     */
    public function list(float $price, string $currency = 'ETH'): bool
    {
        if ($this->status !== self::STATUS_MINTED) {
            return false;
        }

        $this->status = self::STATUS_LISTED;
        $this->listed_price = $price;
        $this->listed_currency = $currency;
        $this->listed_at = date('Y-m-d H:i:s');

        return $this->save();
    }

    /**
     * 下架NFT
     */
    public function unlist(): bool
    {
        if ($this->status !== self::STATUS_LISTED) {
            return false;
        }

        $this->status = self::STATUS_MINTED;
        $this->listed_price = null;
        $this->listed_currency = null;
        $this->listed_at = null;

        return $this->save();
    }

    /**
     * 标记为已售出
     */
    public function markAsSold(float $price, string $currency): bool
    {
        $this->status = self::STATUS_SOLD;
        $this->last_sale_price = $price;
        $this->last_sale_currency = $currency;
        $this->last_sale_at = date('Y-m-d H:i:s');
        $this->listed_price = null;
        $this->listed_currency = null;

        return $this->save();
    }

    /**
     * 销毁NFT
     */
    public function burn(): bool
    {
        $this->status = self::STATUS_BURNED;
        return $this->save();
    }

    /**
     * 增加浏览量
     */
    public function incrementViewCount(): bool
    {
        $this->view_count++;
        return $this->save();
    }

    /**
     * 增加点赞数
     */
    public function incrementLikeCount(): bool
    {
        $this->like_count++;
        return $this->save();
    }

    /**
     * 减少点赞数
     */
    public function decrementLikeCount(): bool
    {
        $this->like_count = max(0, $this->like_count - 1);
        return $this->save();
    }

    /**
     * 计算稀有度分数
     */
    public function calculateRarityScore(): float
    {
        if (empty($this->attributes)) {
            return 0;
        }

        $collection = $this->collection;
        if (!$collection) {
            return 0;
        }

        $totalSupply = $collection->total_supply;
        if ($totalSupply <= 0) {
            return 0;
        }

        $rarityScore = 0;
        
        foreach ($this->attributes as $attribute) {
            $traitType = $attribute['trait_type'] ?? '';
            $value = $attribute['value'] ?? '';
            
            if (empty($traitType) || empty($value)) {
                continue;
            }

            // 计算该属性值在集合中的稀有度
            $count = $this->getAttributeCount($collection->collection_id, $traitType, $value);
            if ($count > 0) {
                $rarity = $totalSupply / $count;
                $rarityScore += $rarity;
            }
        }

        return round($rarityScore, 2);
    }

    /**
     * 更新稀有度排名
     */
    public function updateRarityRank(): bool
    {
        $this->rarity_score = $this->calculateRarityScore();
        
        // 计算在集合中的排名
        $rank = self::where('collection_id', $this->collection_id)
                   ->where('rarity_score', '>', $this->rarity_score)
                   ->count() + 1;
        
        $this->rarity_rank = $rank;
        
        return $this->save();
    }

    /**
     * 获取属性统计
     */
    private function getAttributeCount(string $collectionId, string $traitType, string $value): int
    {
        return self::where('collection_id', $collectionId)
                  ->whereRaw("JSON_CONTAINS(attributes, JSON_OBJECT('trait_type', ?, 'value', ?))", [$traitType, $value])
                  ->count();
    }

    /**
     * 记录转移历史
     */
    private function recordTransfer(int $fromUserId, int $toUserId, string $reason): void
    {
        // 这里应该创建转移记录
        // 简化处理，只记录日志
        \think\facade\Log::info("NFT转移", [
            'token_id' => $this->token_id,
            'from_user_id' => $fromUserId,
            'to_user_id' => $toUserId,
            'reason' => $reason
        ]);
    }

    /**
     * 获取完整的元数据
     */
    public function getFullMetadata(): array
    {
        return [
            'name' => $this->name,
            'description' => $this->description,
            'image' => $this->image,
            'animation_url' => $this->animation_url,
            'external_url' => $this->external_url,
            'attributes' => $this->attributes ?: [],
            'properties' => [
                'collection' => $this->collection->name ?? '',
                'creator' => $this->creator->username ?? '',
                'blockchain' => $this->blockchain,
                'token_standard' => $this->token_standard,
                'rarity_rank' => $this->rarity_rank,
                'rarity_score' => $this->rarity_score
            ]
        ];
    }

    /**
     * 获取价格历史
     */
    public function getPriceHistory(): array
    {
        // 从交易记录中获取价格历史
        $trades = $this->trades()
                      ->order('created_at', 'asc')
                      ->select();

        $priceHistory = [];
        foreach ($trades as $trade) {
            $priceHistory[] = [
                'price' => $trade->price,
                'currency' => $trade->currency,
                'date' => $trade->created_at,
                'type' => $trade->type
            ];
        }

        return $priceHistory;
    }

    /**
     * 检查是否被用户收藏
     */
    public function isFavoritedBy(int $userId): bool
    {
        return $this->favorites()
                   ->where('user_id', $userId)
                   ->where('status', 1)
                   ->exists();
    }

    /**
     * 获取当前活跃订单
     */
    public function getActiveOrder(): ?NftOrder
    {
        return $this->orders()
                   ->where('status', NftOrder::STATUS_PENDING)
                   ->order('created_at', 'desc')
                   ->find();
    }

    /**
     * 获取当前活跃拍卖
     */
    public function getActiveAuction(): ?NftAuction
    {
        return $this->auctions()
                   ->where('status', NftAuction::STATUS_ACTIVE)
                   ->order('created_at', 'desc')
                   ->find();
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr(): string
    {
        $statusTexts = [
            self::STATUS_DRAFT => '草稿',
            self::STATUS_MINTED => '已铸造',
            self::STATUS_LISTED => '已上架',
            self::STATUS_SOLD => '已售出',
            self::STATUS_BURNED => '已销毁',
            self::STATUS_TRANSFERRED => '已转移'
        ];

        return $statusTexts[$this->status] ?? '未知状态';
    }

    /**
     * 获取区块链文本
     */
    public function getBlockchainTextAttr(): string
    {
        $blockchainTexts = [
            self::BLOCKCHAIN_ETHEREUM => 'Ethereum',
            self::BLOCKCHAIN_BSC => 'BSC',
            self::BLOCKCHAIN_POLYGON => 'Polygon',
            self::BLOCKCHAIN_SOLANA => 'Solana'
        ];

        return $blockchainTexts[$this->blockchain] ?? $this->blockchain;
    }

    /**
     * 获取稀有度等级
     */
    public function getRarityLevelAttr(): string
    {
        if ($this->rarity_rank <= 0) {
            return 'Unknown';
        }

        $collection = $this->collection;
        if (!$collection || $collection->total_supply <= 0) {
            return 'Unknown';
        }

        $percentage = ($this->rarity_rank / $collection->total_supply) * 100;

        if ($percentage <= 1) {
            return 'Legendary';
        } elseif ($percentage <= 5) {
            return 'Epic';
        } elseif ($percentage <= 15) {
            return 'Rare';
        } elseif ($percentage <= 40) {
            return 'Uncommon';
        } else {
            return 'Common';
        }
    }

    /**
     * 获取格式化价格
     */
    public function getFormattedPriceAttr(): string
    {
        if (!$this->listed_price) {
            return 'Not for sale';
        }

        return number_format($this->listed_price, 4) . ' ' . $this->listed_currency;
    }

    /**
     * 搜索器：按集合
     */
    public function searchCollectionIdAttr($query, $value)
    {
        if ($value) {
            $query->where('collection_id', $value);
        }
    }

    /**
     * 搜索器：按所有者
     */
    public function searchOwnerIdAttr($query, $value)
    {
        if ($value) {
            $query->where('owner_id', $value);
        }
    }

    /**
     * 搜索器：按创建者
     */
    public function searchCreatorIdAttr($query, $value)
    {
        if ($value) {
            $query->where('creator_id', $value);
        }
    }

    /**
     * 搜索器：按状态
     */
    public function searchStatusAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('status', $value);
        }
    }

    /**
     * 搜索器：按价格范围
     */
    public function searchPriceRangeAttr($query, $value)
    {
        if (is_array($value) && count($value) === 2) {
            $query->whereBetween('listed_price', $value);
        }
    }

    /**
     * 搜索器：按区块链
     */
    public function searchBlockchainAttr($query, $value)
    {
        if ($value) {
            $query->where('blockchain', $value);
        }
    }

    /**
     * 搜索器：按关键词
     */
    public function searchKeywordAttr($query, $value)
    {
        if ($value) {
            $query->where('name|description', 'like', "%{$value}%");
        }
    }

    /**
     * 搜索器：按属性
     */
    public function searchAttributeAttr($query, $value)
    {
        if (is_array($value)) {
            foreach ($value as $traitType => $traitValue) {
                $query->whereRaw("JSON_CONTAINS(attributes, JSON_OBJECT('trait_type', ?, 'value', ?))", [$traitType, $traitValue]);
            }
        }
    }
}
