# GVD交易平台 Nginx 配置文件
# 适用于 Nginx 1.20+

server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # 重定向到HTTPS（生产环境推荐）
    # return 301 https://$server_name$request_uri;
    
    # 网站根目录
    root /path/to/CMCC_COMPLETE/public;
    index index.php index.html;
    
    # 字符集
    charset utf-8;
    
    # 访问日志
    access_log /var/log/nginx/gvd_access.log;
    error_log /var/log/nginx/gvd_error.log;
    
    # 安全头部
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 隐藏Nginx版本
    server_tokens off;
    
    # 主要路由规则
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # PHP处理
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;  # 或者 unix:/var/run/php/php8.1-fpm.sock
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # PHP-FPM优化
        fastcgi_connect_timeout 60s;
        fastcgi_send_timeout 60s;
        fastcgi_read_timeout 60s;
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
        fastcgi_temp_file_write_size 256k;
    }
    
    # 静态文件处理
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # API接口优化
    location /api/ {
        try_files $uri $uri/ /index.php?$query_string;
        
        # CORS支持
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
        add_header Access-Control-Expose-Headers "Content-Length,Content-Range" always;
        
        # 处理预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization";
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type "text/plain; charset=utf-8";
            add_header Content-Length 0;
            return 204;
        }
        
        # API限流
        limit_req zone=api burst=20 nodelay;
    }
    
    # 管理后台
    location /admin/ {
        try_files $uri $uri/ /index.php?$query_string;
        
        # IP白名单（可选）
        # allow ***********/24;
        # deny all;
    }
    
    # 代理后台
    location /agent/ {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # WebSocket代理（如果使用）
    location /ws/ {
        proxy_pass http://127.0.0.1:2346;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ /(vendor|runtime|config|database)/ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(env|log|sql|md)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 禁止访问安装文件（生产环境）
    location ~ ^/(install\.php|deploy\.sh)$ {
        # deny all;  # 生产环境取消注释
        access_log off;
    }
    
    # 文件上传大小限制
    client_max_body_size 100M;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
}

# HTTPS配置（生产环境推荐）
server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL证书配置
    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;
    
    # SSL优化
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 其他配置与HTTP相同
    root /path/to/CMCC_COMPLETE/public;
    index index.php index.html;
    
    # 包含HTTP配置的其他部分...
    include /etc/nginx/snippets/gvd-common.conf;
}

# 全局配置
http {
    # 限流配置
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    
    # 日志格式
    log_format gvd_format '$remote_addr - $remote_user [$time_local] '
                         '"$request" $status $body_bytes_sent '
                         '"$http_referer" "$http_user_agent" '
                         '$request_time $upstream_response_time';
    
    # 缓存配置
    proxy_cache_path /var/cache/nginx/gvd levels=1:2 keys_zone=gvd_cache:10m max_size=1g inactive=60m use_temp_path=off;
    
    # 上游服务器配置（负载均衡）
    upstream php_backend {
        server 127.0.0.1:9000 weight=1 max_fails=3 fail_timeout=30s;
        # server 127.0.0.1:9001 weight=1 max_fails=3 fail_timeout=30s;  # 多个PHP-FPM实例
    }
    
    # WebSocket上游
    upstream websocket_backend {
        server 127.0.0.1:2346;
        # server 127.0.0.1:2347;  # 多个WebSocket实例
    }
}

# 性能优化建议
# 1. 启用HTTP/2
# 2. 配置SSL证书
# 3. 启用Gzip压缩
# 4. 设置适当的缓存策略
# 5. 配置负载均衡
# 6. 监控访问日志
# 7. 定期更新SSL证书

# 安全建议
# 1. 隐藏Nginx版本信息
# 2. 配置防火墙规则
# 3. 限制管理后台访问IP
# 4. 启用访问日志监控
# 5. 定期备份配置文件
# 6. 使用fail2ban防止暴力破解
