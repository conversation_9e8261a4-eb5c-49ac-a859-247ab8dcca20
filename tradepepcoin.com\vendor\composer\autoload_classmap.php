<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Firebase\\JWT\\BeforeValidException' => $vendorDir . '/firebase/php-jwt/src/BeforeValidException.php',
    'Firebase\\JWT\\CachedKeySet' => $vendorDir . '/firebase/php-jwt/src/CachedKeySet.php',
    'Firebase\\JWT\\ExpiredException' => $vendorDir . '/firebase/php-jwt/src/ExpiredException.php',
    'Firebase\\JWT\\JWK' => $vendorDir . '/firebase/php-jwt/src/JWK.php',
    'Firebase\\JWT\\JWT' => $vendorDir . '/firebase/php-jwt/src/JWT.php',
    'Firebase\\JWT\\JWTExceptionWithPayloadInterface' => $vendorDir . '/firebase/php-jwt/src/JWTExceptionWithPayloadInterface.php',
    'Firebase\\JWT\\Key' => $vendorDir . '/firebase/php-jwt/src/Key.php',
    'Firebase\\JWT\\SignatureInvalidException' => $vendorDir . '/firebase/php-jwt/src/SignatureInvalidException.php',
    'GuzzleHttp\\BodySummarizer' => $vendorDir . '/guzzlehttp/guzzle/src/BodySummarizer.php',
    'GuzzleHttp\\BodySummarizerInterface' => $vendorDir . '/guzzlehttp/guzzle/src/BodySummarizerInterface.php',
    'GuzzleHttp\\Client' => $vendorDir . '/guzzlehttp/guzzle/src/Client.php',
    'GuzzleHttp\\ClientInterface' => $vendorDir . '/guzzlehttp/guzzle/src/ClientInterface.php',
    'GuzzleHttp\\ClientTrait' => $vendorDir . '/guzzlehttp/guzzle/src/ClientTrait.php',
    'GuzzleHttp\\Cookie\\CookieJar' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/CookieJar.php',
    'GuzzleHttp\\Cookie\\CookieJarInterface' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/CookieJarInterface.php',
    'GuzzleHttp\\Cookie\\FileCookieJar' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/FileCookieJar.php',
    'GuzzleHttp\\Cookie\\SessionCookieJar' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/SessionCookieJar.php',
    'GuzzleHttp\\Cookie\\SetCookie' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/SetCookie.php',
    'GuzzleHttp\\Exception\\BadResponseException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/BadResponseException.php',
    'GuzzleHttp\\Exception\\ClientException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/ClientException.php',
    'GuzzleHttp\\Exception\\ConnectException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/ConnectException.php',
    'GuzzleHttp\\Exception\\GuzzleException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/GuzzleException.php',
    'GuzzleHttp\\Exception\\InvalidArgumentException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/InvalidArgumentException.php',
    'GuzzleHttp\\Exception\\RequestException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/RequestException.php',
    'GuzzleHttp\\Exception\\ServerException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/ServerException.php',
    'GuzzleHttp\\Exception\\TooManyRedirectsException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/TooManyRedirectsException.php',
    'GuzzleHttp\\Exception\\TransferException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/TransferException.php',
    'GuzzleHttp\\HandlerStack' => $vendorDir . '/guzzlehttp/guzzle/src/HandlerStack.php',
    'GuzzleHttp\\Handler\\CurlFactory' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlFactory.php',
    'GuzzleHttp\\Handler\\CurlFactoryInterface' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlFactoryInterface.php',
    'GuzzleHttp\\Handler\\CurlHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlHandler.php',
    'GuzzleHttp\\Handler\\CurlMultiHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlMultiHandler.php',
    'GuzzleHttp\\Handler\\EasyHandle' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/EasyHandle.php',
    'GuzzleHttp\\Handler\\HeaderProcessor' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/HeaderProcessor.php',
    'GuzzleHttp\\Handler\\MockHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/MockHandler.php',
    'GuzzleHttp\\Handler\\Proxy' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/Proxy.php',
    'GuzzleHttp\\Handler\\StreamHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/StreamHandler.php',
    'GuzzleHttp\\MessageFormatter' => $vendorDir . '/guzzlehttp/guzzle/src/MessageFormatter.php',
    'GuzzleHttp\\MessageFormatterInterface' => $vendorDir . '/guzzlehttp/guzzle/src/MessageFormatterInterface.php',
    'GuzzleHttp\\Middleware' => $vendorDir . '/guzzlehttp/guzzle/src/Middleware.php',
    'GuzzleHttp\\Pool' => $vendorDir . '/guzzlehttp/guzzle/src/Pool.php',
    'GuzzleHttp\\PrepareBodyMiddleware' => $vendorDir . '/guzzlehttp/guzzle/src/PrepareBodyMiddleware.php',
    'GuzzleHttp\\Promise\\AggregateException' => $vendorDir . '/guzzlehttp/promises/src/AggregateException.php',
    'GuzzleHttp\\Promise\\CancellationException' => $vendorDir . '/guzzlehttp/promises/src/CancellationException.php',
    'GuzzleHttp\\Promise\\Coroutine' => $vendorDir . '/guzzlehttp/promises/src/Coroutine.php',
    'GuzzleHttp\\Promise\\Create' => $vendorDir . '/guzzlehttp/promises/src/Create.php',
    'GuzzleHttp\\Promise\\Each' => $vendorDir . '/guzzlehttp/promises/src/Each.php',
    'GuzzleHttp\\Promise\\EachPromise' => $vendorDir . '/guzzlehttp/promises/src/EachPromise.php',
    'GuzzleHttp\\Promise\\FulfilledPromise' => $vendorDir . '/guzzlehttp/promises/src/FulfilledPromise.php',
    'GuzzleHttp\\Promise\\Is' => $vendorDir . '/guzzlehttp/promises/src/Is.php',
    'GuzzleHttp\\Promise\\Promise' => $vendorDir . '/guzzlehttp/promises/src/Promise.php',
    'GuzzleHttp\\Promise\\PromiseInterface' => $vendorDir . '/guzzlehttp/promises/src/PromiseInterface.php',
    'GuzzleHttp\\Promise\\PromisorInterface' => $vendorDir . '/guzzlehttp/promises/src/PromisorInterface.php',
    'GuzzleHttp\\Promise\\RejectedPromise' => $vendorDir . '/guzzlehttp/promises/src/RejectedPromise.php',
    'GuzzleHttp\\Promise\\RejectionException' => $vendorDir . '/guzzlehttp/promises/src/RejectionException.php',
    'GuzzleHttp\\Promise\\TaskQueue' => $vendorDir . '/guzzlehttp/promises/src/TaskQueue.php',
    'GuzzleHttp\\Promise\\TaskQueueInterface' => $vendorDir . '/guzzlehttp/promises/src/TaskQueueInterface.php',
    'GuzzleHttp\\Promise\\Utils' => $vendorDir . '/guzzlehttp/promises/src/Utils.php',
    'GuzzleHttp\\Psr7\\AppendStream' => $vendorDir . '/guzzlehttp/psr7/src/AppendStream.php',
    'GuzzleHttp\\Psr7\\BufferStream' => $vendorDir . '/guzzlehttp/psr7/src/BufferStream.php',
    'GuzzleHttp\\Psr7\\CachingStream' => $vendorDir . '/guzzlehttp/psr7/src/CachingStream.php',
    'GuzzleHttp\\Psr7\\DroppingStream' => $vendorDir . '/guzzlehttp/psr7/src/DroppingStream.php',
    'GuzzleHttp\\Psr7\\Exception\\MalformedUriException' => $vendorDir . '/guzzlehttp/psr7/src/Exception/MalformedUriException.php',
    'GuzzleHttp\\Psr7\\FnStream' => $vendorDir . '/guzzlehttp/psr7/src/FnStream.php',
    'GuzzleHttp\\Psr7\\Header' => $vendorDir . '/guzzlehttp/psr7/src/Header.php',
    'GuzzleHttp\\Psr7\\HttpFactory' => $vendorDir . '/guzzlehttp/psr7/src/HttpFactory.php',
    'GuzzleHttp\\Psr7\\InflateStream' => $vendorDir . '/guzzlehttp/psr7/src/InflateStream.php',
    'GuzzleHttp\\Psr7\\LazyOpenStream' => $vendorDir . '/guzzlehttp/psr7/src/LazyOpenStream.php',
    'GuzzleHttp\\Psr7\\LimitStream' => $vendorDir . '/guzzlehttp/psr7/src/LimitStream.php',
    'GuzzleHttp\\Psr7\\Message' => $vendorDir . '/guzzlehttp/psr7/src/Message.php',
    'GuzzleHttp\\Psr7\\MessageTrait' => $vendorDir . '/guzzlehttp/psr7/src/MessageTrait.php',
    'GuzzleHttp\\Psr7\\MimeType' => $vendorDir . '/guzzlehttp/psr7/src/MimeType.php',
    'GuzzleHttp\\Psr7\\MultipartStream' => $vendorDir . '/guzzlehttp/psr7/src/MultipartStream.php',
    'GuzzleHttp\\Psr7\\NoSeekStream' => $vendorDir . '/guzzlehttp/psr7/src/NoSeekStream.php',
    'GuzzleHttp\\Psr7\\PumpStream' => $vendorDir . '/guzzlehttp/psr7/src/PumpStream.php',
    'GuzzleHttp\\Psr7\\Query' => $vendorDir . '/guzzlehttp/psr7/src/Query.php',
    'GuzzleHttp\\Psr7\\Request' => $vendorDir . '/guzzlehttp/psr7/src/Request.php',
    'GuzzleHttp\\Psr7\\Response' => $vendorDir . '/guzzlehttp/psr7/src/Response.php',
    'GuzzleHttp\\Psr7\\Rfc7230' => $vendorDir . '/guzzlehttp/psr7/src/Rfc7230.php',
    'GuzzleHttp\\Psr7\\ServerRequest' => $vendorDir . '/guzzlehttp/psr7/src/ServerRequest.php',
    'GuzzleHttp\\Psr7\\Stream' => $vendorDir . '/guzzlehttp/psr7/src/Stream.php',
    'GuzzleHttp\\Psr7\\StreamDecoratorTrait' => $vendorDir . '/guzzlehttp/psr7/src/StreamDecoratorTrait.php',
    'GuzzleHttp\\Psr7\\StreamWrapper' => $vendorDir . '/guzzlehttp/psr7/src/StreamWrapper.php',
    'GuzzleHttp\\Psr7\\UploadedFile' => $vendorDir . '/guzzlehttp/psr7/src/UploadedFile.php',
    'GuzzleHttp\\Psr7\\Uri' => $vendorDir . '/guzzlehttp/psr7/src/Uri.php',
    'GuzzleHttp\\Psr7\\UriComparator' => $vendorDir . '/guzzlehttp/psr7/src/UriComparator.php',
    'GuzzleHttp\\Psr7\\UriNormalizer' => $vendorDir . '/guzzlehttp/psr7/src/UriNormalizer.php',
    'GuzzleHttp\\Psr7\\UriResolver' => $vendorDir . '/guzzlehttp/psr7/src/UriResolver.php',
    'GuzzleHttp\\Psr7\\Utils' => $vendorDir . '/guzzlehttp/psr7/src/Utils.php',
    'GuzzleHttp\\RedirectMiddleware' => $vendorDir . '/guzzlehttp/guzzle/src/RedirectMiddleware.php',
    'GuzzleHttp\\RequestOptions' => $vendorDir . '/guzzlehttp/guzzle/src/RequestOptions.php',
    'GuzzleHttp\\RetryMiddleware' => $vendorDir . '/guzzlehttp/guzzle/src/RetryMiddleware.php',
    'GuzzleHttp\\TransferStats' => $vendorDir . '/guzzlehttp/guzzle/src/TransferStats.php',
    'GuzzleHttp\\Utils' => $vendorDir . '/guzzlehttp/guzzle/src/Utils.php',
    'Monolog\\Attribute\\AsMonologProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Attribute/AsMonologProcessor.php',
    'Monolog\\DateTimeImmutable' => $vendorDir . '/monolog/monolog/src/Monolog/DateTimeImmutable.php',
    'Monolog\\ErrorHandler' => $vendorDir . '/monolog/monolog/src/Monolog/ErrorHandler.php',
    'Monolog\\Formatter\\ChromePHPFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/ChromePHPFormatter.php',
    'Monolog\\Formatter\\ElasticaFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/ElasticaFormatter.php',
    'Monolog\\Formatter\\ElasticsearchFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/ElasticsearchFormatter.php',
    'Monolog\\Formatter\\FlowdockFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/FlowdockFormatter.php',
    'Monolog\\Formatter\\FluentdFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/FluentdFormatter.php',
    'Monolog\\Formatter\\FormatterInterface' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/FormatterInterface.php',
    'Monolog\\Formatter\\GelfMessageFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/GelfMessageFormatter.php',
    'Monolog\\Formatter\\GoogleCloudLoggingFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/GoogleCloudLoggingFormatter.php',
    'Monolog\\Formatter\\HtmlFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/HtmlFormatter.php',
    'Monolog\\Formatter\\JsonFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/JsonFormatter.php',
    'Monolog\\Formatter\\LineFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/LineFormatter.php',
    'Monolog\\Formatter\\LogglyFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/LogglyFormatter.php',
    'Monolog\\Formatter\\LogmaticFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/LogmaticFormatter.php',
    'Monolog\\Formatter\\LogstashFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/LogstashFormatter.php',
    'Monolog\\Formatter\\MongoDBFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/MongoDBFormatter.php',
    'Monolog\\Formatter\\NormalizerFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/NormalizerFormatter.php',
    'Monolog\\Formatter\\ScalarFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/ScalarFormatter.php',
    'Monolog\\Formatter\\WildfireFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/WildfireFormatter.php',
    'Monolog\\Handler\\AbstractHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/AbstractHandler.php',
    'Monolog\\Handler\\AbstractProcessingHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/AbstractProcessingHandler.php',
    'Monolog\\Handler\\AbstractSyslogHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/AbstractSyslogHandler.php',
    'Monolog\\Handler\\AmqpHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/AmqpHandler.php',
    'Monolog\\Handler\\BrowserConsoleHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/BrowserConsoleHandler.php',
    'Monolog\\Handler\\BufferHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/BufferHandler.php',
    'Monolog\\Handler\\ChromePHPHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ChromePHPHandler.php',
    'Monolog\\Handler\\CouchDBHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/CouchDBHandler.php',
    'Monolog\\Handler\\CubeHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/CubeHandler.php',
    'Monolog\\Handler\\Curl\\Util' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/Curl/Util.php',
    'Monolog\\Handler\\DeduplicationHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/DeduplicationHandler.php',
    'Monolog\\Handler\\DoctrineCouchDBHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/DoctrineCouchDBHandler.php',
    'Monolog\\Handler\\DynamoDbHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/DynamoDbHandler.php',
    'Monolog\\Handler\\ElasticaHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ElasticaHandler.php',
    'Monolog\\Handler\\ElasticsearchHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ElasticsearchHandler.php',
    'Monolog\\Handler\\ErrorLogHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ErrorLogHandler.php',
    'Monolog\\Handler\\FallbackGroupHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FallbackGroupHandler.php',
    'Monolog\\Handler\\FilterHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FilterHandler.php',
    'Monolog\\Handler\\FingersCrossedHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FingersCrossedHandler.php',
    'Monolog\\Handler\\FingersCrossed\\ActivationStrategyInterface' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FingersCrossed/ActivationStrategyInterface.php',
    'Monolog\\Handler\\FingersCrossed\\ChannelLevelActivationStrategy' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FingersCrossed/ChannelLevelActivationStrategy.php',
    'Monolog\\Handler\\FingersCrossed\\ErrorLevelActivationStrategy' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FingersCrossed/ErrorLevelActivationStrategy.php',
    'Monolog\\Handler\\FirePHPHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FirePHPHandler.php',
    'Monolog\\Handler\\FleepHookHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FleepHookHandler.php',
    'Monolog\\Handler\\FlowdockHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FlowdockHandler.php',
    'Monolog\\Handler\\FormattableHandlerInterface' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FormattableHandlerInterface.php',
    'Monolog\\Handler\\FormattableHandlerTrait' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FormattableHandlerTrait.php',
    'Monolog\\Handler\\GelfHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/GelfHandler.php',
    'Monolog\\Handler\\GroupHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/GroupHandler.php',
    'Monolog\\Handler\\Handler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/Handler.php',
    'Monolog\\Handler\\HandlerInterface' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/HandlerInterface.php',
    'Monolog\\Handler\\HandlerWrapper' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/HandlerWrapper.php',
    'Monolog\\Handler\\IFTTTHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/IFTTTHandler.php',
    'Monolog\\Handler\\InsightOpsHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/InsightOpsHandler.php',
    'Monolog\\Handler\\LogEntriesHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/LogEntriesHandler.php',
    'Monolog\\Handler\\LogglyHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/LogglyHandler.php',
    'Monolog\\Handler\\LogmaticHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/LogmaticHandler.php',
    'Monolog\\Handler\\MailHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/MailHandler.php',
    'Monolog\\Handler\\MandrillHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/MandrillHandler.php',
    'Monolog\\Handler\\MissingExtensionException' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/MissingExtensionException.php',
    'Monolog\\Handler\\MongoDBHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/MongoDBHandler.php',
    'Monolog\\Handler\\NativeMailerHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/NativeMailerHandler.php',
    'Monolog\\Handler\\NewRelicHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/NewRelicHandler.php',
    'Monolog\\Handler\\NoopHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/NoopHandler.php',
    'Monolog\\Handler\\NullHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/NullHandler.php',
    'Monolog\\Handler\\OverflowHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/OverflowHandler.php',
    'Monolog\\Handler\\PHPConsoleHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/PHPConsoleHandler.php',
    'Monolog\\Handler\\ProcessHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ProcessHandler.php',
    'Monolog\\Handler\\ProcessableHandlerInterface' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ProcessableHandlerInterface.php',
    'Monolog\\Handler\\ProcessableHandlerTrait' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ProcessableHandlerTrait.php',
    'Monolog\\Handler\\PsrHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/PsrHandler.php',
    'Monolog\\Handler\\PushoverHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/PushoverHandler.php',
    'Monolog\\Handler\\RedisHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/RedisHandler.php',
    'Monolog\\Handler\\RedisPubSubHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/RedisPubSubHandler.php',
    'Monolog\\Handler\\RollbarHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/RollbarHandler.php',
    'Monolog\\Handler\\RotatingFileHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/RotatingFileHandler.php',
    'Monolog\\Handler\\SamplingHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SamplingHandler.php',
    'Monolog\\Handler\\SendGridHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SendGridHandler.php',
    'Monolog\\Handler\\SlackHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SlackHandler.php',
    'Monolog\\Handler\\SlackWebhookHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SlackWebhookHandler.php',
    'Monolog\\Handler\\Slack\\SlackRecord' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/Slack/SlackRecord.php',
    'Monolog\\Handler\\SocketHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SocketHandler.php',
    'Monolog\\Handler\\SqsHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SqsHandler.php',
    'Monolog\\Handler\\StreamHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/StreamHandler.php',
    'Monolog\\Handler\\SwiftMailerHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SwiftMailerHandler.php',
    'Monolog\\Handler\\SymfonyMailerHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SymfonyMailerHandler.php',
    'Monolog\\Handler\\SyslogHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SyslogHandler.php',
    'Monolog\\Handler\\SyslogUdpHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SyslogUdpHandler.php',
    'Monolog\\Handler\\SyslogUdp\\UdpSocket' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SyslogUdp/UdpSocket.php',
    'Monolog\\Handler\\TelegramBotHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/TelegramBotHandler.php',
    'Monolog\\Handler\\TestHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/TestHandler.php',
    'Monolog\\Handler\\WebRequestRecognizerTrait' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/WebRequestRecognizerTrait.php',
    'Monolog\\Handler\\WhatFailureGroupHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/WhatFailureGroupHandler.php',
    'Monolog\\Handler\\ZendMonitorHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ZendMonitorHandler.php',
    'Monolog\\LogRecord' => $vendorDir . '/monolog/monolog/src/Monolog/LogRecord.php',
    'Monolog\\Logger' => $vendorDir . '/monolog/monolog/src/Monolog/Logger.php',
    'Monolog\\Processor\\GitProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/GitProcessor.php',
    'Monolog\\Processor\\HostnameProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/HostnameProcessor.php',
    'Monolog\\Processor\\IntrospectionProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/IntrospectionProcessor.php',
    'Monolog\\Processor\\MemoryPeakUsageProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/MemoryPeakUsageProcessor.php',
    'Monolog\\Processor\\MemoryProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/MemoryProcessor.php',
    'Monolog\\Processor\\MemoryUsageProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/MemoryUsageProcessor.php',
    'Monolog\\Processor\\MercurialProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/MercurialProcessor.php',
    'Monolog\\Processor\\ProcessIdProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/ProcessIdProcessor.php',
    'Monolog\\Processor\\ProcessorInterface' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/ProcessorInterface.php',
    'Monolog\\Processor\\PsrLogMessageProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/PsrLogMessageProcessor.php',
    'Monolog\\Processor\\TagProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/TagProcessor.php',
    'Monolog\\Processor\\UidProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/UidProcessor.php',
    'Monolog\\Processor\\WebProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/WebProcessor.php',
    'Monolog\\Registry' => $vendorDir . '/monolog/monolog/src/Monolog/Registry.php',
    'Monolog\\ResettableInterface' => $vendorDir . '/monolog/monolog/src/Monolog/ResettableInterface.php',
    'Monolog\\SignalHandler' => $vendorDir . '/monolog/monolog/src/Monolog/SignalHandler.php',
    'Monolog\\Test\\TestCase' => $vendorDir . '/monolog/monolog/src/Monolog/Test/TestCase.php',
    'Monolog\\Utils' => $vendorDir . '/monolog/monolog/src/Monolog/Utils.php',
    'PHPMailer\\PHPMailer\\DSNConfigurator' => $vendorDir . '/phpmailer/phpmailer/src/DSNConfigurator.php',
    'PHPMailer\\PHPMailer\\Exception' => $vendorDir . '/phpmailer/phpmailer/src/Exception.php',
    'PHPMailer\\PHPMailer\\OAuth' => $vendorDir . '/phpmailer/phpmailer/src/OAuth.php',
    'PHPMailer\\PHPMailer\\OAuthTokenProvider' => $vendorDir . '/phpmailer/phpmailer/src/OAuthTokenProvider.php',
    'PHPMailer\\PHPMailer\\PHPMailer' => $vendorDir . '/phpmailer/phpmailer/src/PHPMailer.php',
    'PHPMailer\\PHPMailer\\POP3' => $vendorDir . '/phpmailer/phpmailer/src/POP3.php',
    'PHPMailer\\PHPMailer\\SMTP' => $vendorDir . '/phpmailer/phpmailer/src/SMTP.php',
    'Psr\\Container\\ContainerExceptionInterface' => $vendorDir . '/psr/container/src/ContainerExceptionInterface.php',
    'Psr\\Container\\ContainerInterface' => $vendorDir . '/psr/container/src/ContainerInterface.php',
    'Psr\\Container\\NotFoundExceptionInterface' => $vendorDir . '/psr/container/src/NotFoundExceptionInterface.php',
    'Psr\\Http\\Client\\ClientExceptionInterface' => $vendorDir . '/psr/http-client/src/ClientExceptionInterface.php',
    'Psr\\Http\\Client\\ClientInterface' => $vendorDir . '/psr/http-client/src/ClientInterface.php',
    'Psr\\Http\\Client\\NetworkExceptionInterface' => $vendorDir . '/psr/http-client/src/NetworkExceptionInterface.php',
    'Psr\\Http\\Client\\RequestExceptionInterface' => $vendorDir . '/psr/http-client/src/RequestExceptionInterface.php',
    'Psr\\Http\\Message\\MessageInterface' => $vendorDir . '/psr/http-message/src/MessageInterface.php',
    'Psr\\Http\\Message\\RequestFactoryInterface' => $vendorDir . '/psr/http-factory/src/RequestFactoryInterface.php',
    'Psr\\Http\\Message\\RequestInterface' => $vendorDir . '/psr/http-message/src/RequestInterface.php',
    'Psr\\Http\\Message\\ResponseFactoryInterface' => $vendorDir . '/psr/http-factory/src/ResponseFactoryInterface.php',
    'Psr\\Http\\Message\\ResponseInterface' => $vendorDir . '/psr/http-message/src/ResponseInterface.php',
    'Psr\\Http\\Message\\ServerRequestFactoryInterface' => $vendorDir . '/psr/http-factory/src/ServerRequestFactoryInterface.php',
    'Psr\\Http\\Message\\ServerRequestInterface' => $vendorDir . '/psr/http-message/src/ServerRequestInterface.php',
    'Psr\\Http\\Message\\StreamFactoryInterface' => $vendorDir . '/psr/http-factory/src/StreamFactoryInterface.php',
    'Psr\\Http\\Message\\StreamInterface' => $vendorDir . '/psr/http-message/src/StreamInterface.php',
    'Psr\\Http\\Message\\UploadedFileFactoryInterface' => $vendorDir . '/psr/http-factory/src/UploadedFileFactoryInterface.php',
    'Psr\\Http\\Message\\UploadedFileInterface' => $vendorDir . '/psr/http-message/src/UploadedFileInterface.php',
    'Psr\\Http\\Message\\UriFactoryInterface' => $vendorDir . '/psr/http-factory/src/UriFactoryInterface.php',
    'Psr\\Http\\Message\\UriInterface' => $vendorDir . '/psr/http-message/src/UriInterface.php',
    'Psr\\Log\\AbstractLogger' => $vendorDir . '/psr/log/Psr/Log/AbstractLogger.php',
    'Psr\\Log\\InvalidArgumentException' => $vendorDir . '/psr/log/Psr/Log/InvalidArgumentException.php',
    'Psr\\Log\\LogLevel' => $vendorDir . '/psr/log/Psr/Log/LogLevel.php',
    'Psr\\Log\\LoggerAwareInterface' => $vendorDir . '/psr/log/Psr/Log/LoggerAwareInterface.php',
    'Psr\\Log\\LoggerAwareTrait' => $vendorDir . '/psr/log/Psr/Log/LoggerAwareTrait.php',
    'Psr\\Log\\LoggerInterface' => $vendorDir . '/psr/log/Psr/Log/LoggerInterface.php',
    'Psr\\Log\\LoggerTrait' => $vendorDir . '/psr/log/Psr/Log/LoggerTrait.php',
    'Psr\\Log\\NullLogger' => $vendorDir . '/psr/log/Psr/Log/NullLogger.php',
    'Psr\\Log\\Test\\DummyTest' => $vendorDir . '/psr/log/Psr/Log/Test/DummyTest.php',
    'Psr\\Log\\Test\\LoggerInterfaceTest' => $vendorDir . '/psr/log/Psr/Log/Test/LoggerInterfaceTest.php',
    'Psr\\Log\\Test\\TestLogger' => $vendorDir . '/psr/log/Psr/Log/Test/TestLogger.php',
    'Psr\\SimpleCache\\CacheException' => $vendorDir . '/psr/simple-cache/src/CacheException.php',
    'Psr\\SimpleCache\\CacheInterface' => $vendorDir . '/psr/simple-cache/src/CacheInterface.php',
    'Psr\\SimpleCache\\InvalidArgumentException' => $vendorDir . '/psr/simple-cache/src/InvalidArgumentException.php',
    'app\\BaseController' => $baseDir . '/app/BaseController.php',
    'app\\admin\\controller\\Coin' => $baseDir . '/app/admin/controller/Coin.php',
    'app\\admin\\controller\\CustomerService' => $baseDir . '/app/admin/controller/CustomerService.php',
    'app\\admin\\controller\\Index' => $baseDir . '/app/admin/controller/Index.php',
    'app\\admin\\controller\\Statistics' => $baseDir . '/app/admin/controller/Statistics.php',
    'app\\admin\\controller\\System' => $baseDir . '/app/admin/controller/System.php',
    'app\\admin\\controller\\TradingPair' => $baseDir . '/app/admin/controller/TradingPair.php',
    'app\\admin\\controller\\User' => $baseDir . '/app/admin/controller/User.php',
    'app\\agent\\controller\\Auth' => $baseDir . '/app/agent/controller/Auth.php',
    'app\\agent\\controller\\Dashboard' => $baseDir . '/app/agent/controller/Dashboard.php',
    'app\\agent\\controller\\Index' => $baseDir . '/app/agent/controller/Index.php',
    'app\\api\\controller\\Admin' => $baseDir . '/app/api/controller/Admin.php',
    'app\\api\\controller\\Agent' => $baseDir . '/app/api/controller/Agent.php',
    'app\\api\\controller\\Ai' => $baseDir . '/app/api/controller/Ai.php',
    'app\\api\\controller\\Asset' => $baseDir . '/app/api/controller/Asset.php',
    'app\\api\\controller\\Auth' => $baseDir . '/app/api/controller/Auth.php',
    'app\\api\\controller\\BaseController' => $baseDir . '/app/api/controller/BaseController.php',
    'app\\api\\controller\\Config' => $baseDir . '/app/api/controller/Config.php',
    'app\\api\\controller\\Contract' => $baseDir . '/app/api/controller/Contract.php',
    'app\\api\\controller\\CustomerService' => $baseDir . '/app/api/controller/CustomerService.php',
    'app\\api\\controller\\Institutional' => $baseDir . '/app/api/controller/Institutional.php',
    'app\\api\\controller\\Market' => $baseDir . '/app/api/controller/Market.php',
    'app\\api\\controller\\Quant' => $baseDir . '/app/api/controller/Quant.php',
    'app\\api\\controller\\Social' => $baseDir . '/app/api/controller/Social.php',
    'app\\api\\controller\\Trade' => $baseDir . '/app/api/controller/Trade.php',
    'app\\api\\controller\\User' => $baseDir . '/app/api/controller/User.php',
    'app\\command\\ContractSettle' => $baseDir . '/app/command/ContractSettle.php',
    'app\\command\\MarketDataSync' => $baseDir . '/app/command/MarketDataSync.php',
    'app\\command\\QuantRunner' => $baseDir . '/app/command/QuantRunner.php',
    'app\\command\\TradeMatch' => $baseDir . '/app/command/TradeMatch.php',
    'app\\common\\model\\AdvancedOrder' => $baseDir . '/app/common/model/AdvancedOrder.php',
    'app\\common\\model\\AiStrategy' => $baseDir . '/app/common/model/AiStrategy.php',
    'app\\common\\model\\Coin' => $baseDir . '/app/common/model/Coin.php',
    'app\\common\\model\\Commission' => $baseDir . '/app/common/model/Commission.php',
    'app\\common\\model\\ContractConfig' => $baseDir . '/app/common/model/ContractConfig.php',
    'app\\common\\model\\ContractOrder' => $baseDir . '/app/common/model/ContractOrder.php',
    'app\\common\\model\\CrossChainBridge' => $baseDir . '/app/common/model/CrossChainBridge.php',
    'app\\common\\model\\CustomerService' => $baseDir . '/app/common/model/CustomerService.php',
    'app\\common\\model\\CustomerServiceMessage' => $baseDir . '/app/common/model/CustomerServiceMessage.php',
    'app\\common\\model\\CustomerServiceTicket' => $baseDir . '/app/common/model/CustomerServiceTicket.php',
    'app\\common\\model\\DepositAddress' => $baseDir . '/app/common/model/DepositAddress.php',
    'app\\common\\model\\DepositRecord' => $baseDir . '/app/common/model/DepositRecord.php',
    'app\\common\\model\\FinancialRecord' => $baseDir . '/app/common/model/FinancialRecord.php',
    'app\\common\\model\\GridStrategy' => $baseDir . '/app/common/model/GridStrategy.php',
    'app\\common\\model\\IdoOrder' => $baseDir . '/app/common/model/IdoOrder.php',
    'app\\common\\model\\IdoProject' => $baseDir . '/app/common/model/IdoProject.php',
    'app\\common\\model\\InstitutionalAccount' => $baseDir . '/app/common/model/InstitutionalAccount.php',
    'app\\common\\model\\LiquidityPool' => $baseDir . '/app/common/model/LiquidityPool.php',
    'app\\common\\model\\NftToken' => $baseDir . '/app/common/model/NftToken.php',
    'app\\common\\model\\Notification' => $baseDir . '/app/common/model/Notification.php',
    'app\\common\\model\\Order' => $baseDir . '/app/common/model/Order.php',
    'app\\common\\model\\QuantStrategy' => $baseDir . '/app/common/model/QuantStrategy.php',
    'app\\common\\model\\SystemConfig' => $baseDir . '/app/common/model/SystemConfig.php',
    'app\\common\\model\\Trade' => $baseDir . '/app/common/model/Trade.php',
    'app\\common\\model\\TradingPair' => $baseDir . '/app/common/model/TradingPair.php',
    'app\\common\\model\\TradingStrategy' => $baseDir . '/app/common/model/TradingStrategy.php',
    'app\\common\\model\\User' => $baseDir . '/app/common/model/User.php',
    'app\\common\\model\\UserAsset' => $baseDir . '/app/common/model/UserAsset.php',
    'app\\common\\model\\WithdrawRecord' => $baseDir . '/app/common/model/WithdrawRecord.php',
    'app\\common\\model\\Withdrawal' => $baseDir . '/app/common/model/Withdrawal.php',
    'app\\common\\service\\AdvancedTradeService' => $baseDir . '/app/common/service/AdvancedTradeService.php',
    'app\\common\\service\\AgentService' => $baseDir . '/app/common/service/AgentService.php',
    'app\\common\\service\\AiTradingService' => $baseDir . '/app/common/service/AiTradingService.php',
    'app\\common\\service\\AssetService' => $baseDir . '/app/common/service/AssetService.php',
    'app\\common\\service\\ContractService' => $baseDir . '/app/common/service/ContractService.php',
    'app\\common\\service\\CrossChainService' => $baseDir . '/app/common/service/CrossChainService.php',
    'app\\common\\service\\CustomerServiceService' => $baseDir . '/app/common/service/CustomerServiceService.php',
    'app\\common\\service\\DeFiService' => $baseDir . '/app/common/service/DeFiService.php',
    'app\\common\\service\\DepositWithdrawService' => $baseDir . '/app/common/service/DepositWithdrawService.php',
    'app\\common\\service\\EmailService' => $baseDir . '/app/common/service/EmailService.php',
    'app\\common\\service\\FinancialReportService' => $baseDir . '/app/common/service/FinancialReportService.php',
    'app\\common\\service\\IdoService' => $baseDir . '/app/common/service/IdoService.php',
    'app\\common\\service\\InstitutionalService' => $baseDir . '/app/common/service/InstitutionalService.php',
    'app\\common\\service\\KycService' => $baseDir . '/app/common/service/KycService.php',
    'app\\common\\service\\LanguageService' => $baseDir . '/app/common/service/LanguageService.php',
    'app\\common\\service\\NftService' => $baseDir . '/app/common/service/NftService.php',
    'app\\common\\service\\NotificationService' => $baseDir . '/app/common/service/NotificationService.php',
    'app\\common\\service\\PaymentService' => $baseDir . '/app/common/service/PaymentService.php',
    'app\\common\\service\\PerformanceService' => $baseDir . '/app/common/service/PerformanceService.php',
    'app\\common\\service\\QuantTradingService' => $baseDir . '/app/common/service/QuantTradingService.php',
    'app\\common\\service\\SecurityService' => $baseDir . '/app/common/service/SecurityService.php',
    'app\\common\\service\\SimpleKycService' => $baseDir . '/app/common/service/SimpleKycService.php',
    'app\\common\\service\\SimpleTradeService' => $baseDir . '/app/common/service/SimpleTradeService.php',
    'app\\common\\service\\SimpleWebSocketService' => $baseDir . '/app/common/service/SimpleWebSocketService.php',
    'app\\common\\service\\SocialTradingService' => $baseDir . '/app/common/service/SocialTradingService.php',
    'app\\common\\service\\StatisticsService' => $baseDir . '/app/common/service/StatisticsService.php',
    'app\\common\\service\\SystemConfigService' => $baseDir . '/app/common/service/SystemConfigService.php',
    'app\\common\\service\\TradeService' => $baseDir . '/app/common/service/TradeService.php',
    'app\\common\\service\\UserService' => $baseDir . '/app/common/service/UserService.php',
    'app\\common\\service\\WalletService' => $baseDir . '/app/common/service/WalletService.php',
    'app\\index\\controller\\Auth' => $baseDir . '/app/index/controller/Auth.php',
    'app\\index\\controller\\Contract' => $baseDir . '/app/index/controller/Contract.php',
    'app\\index\\controller\\CustomerService' => $baseDir . '/app/index/controller/CustomerService.php',
    'app\\index\\controller\\Ido' => $baseDir . '/app/index/controller/Ido.php',
    'app\\index\\controller\\Index' => $baseDir . '/app/index/controller/Index.php',
    'app\\index\\controller\\Language' => $baseDir . '/app/index/controller/Language.php',
    'app\\index\\controller\\Notification' => $baseDir . '/app/index/controller/Notification.php',
    'app\\index\\controller\\Trade' => $baseDir . '/app/index/controller/Trade.php',
    'app\\index\\controller\\User' => $baseDir . '/app/index/controller/User.php',
    'app\\index\\controller\\Wallet' => $baseDir . '/app/index/controller/Wallet.php',
    'app\\job\\EmailJob' => $baseDir . '/app/job/EmailJob.php',
    'app\\job\\QuantExecuteJob' => $baseDir . '/app/job/QuantExecuteJob.php',
    'app\\middleware\\ApiAuth' => $baseDir . '/app/middleware/ApiAuth.php',
    'app\\middleware\\ApiThrottle' => $baseDir . '/app/middleware/ApiThrottle.php',
    'app\\middleware\\LanguageMiddleware' => $baseDir . '/app/middleware/LanguageMiddleware.php',
    'app\\middleware\\SecurityMiddleware' => $baseDir . '/app/middleware/SecurityMiddleware.php',
    'app\\websocket\\controller\\Market' => $baseDir . '/app/websocket/controller/Market.php',
    'app\\websocket\\controller\\User' => $baseDir . '/app/websocket/controller/User.php',
    'think\\App' => $vendorDir . '/topthink/framework/src/think/App.php',
    'think\\Cache' => $vendorDir . '/topthink/framework/src/think/Cache.php',
    'think\\Collection' => $vendorDir . '/topthink/think-helper/src/Collection.php',
    'think\\Config' => $vendorDir . '/topthink/framework/src/think/Config.php',
    'think\\Console' => $vendorDir . '/topthink/framework/src/think/Console.php',
    'think\\Container' => $vendorDir . '/topthink/framework/src/think/Container.php',
    'think\\Cookie' => $vendorDir . '/topthink/framework/src/think/Cookie.php',
    'think\\Db' => $vendorDir . '/topthink/framework/src/think/Db.php',
    'think\\DbManager' => $vendorDir . '/topthink/think-orm/src/DbManager.php',
    'think\\Env' => $vendorDir . '/topthink/framework/src/think/Env.php',
    'think\\Event' => $vendorDir . '/topthink/framework/src/think/Event.php',
    'think\\Exception' => $vendorDir . '/topthink/framework/src/think/Exception.php',
    'think\\Facade' => $vendorDir . '/topthink/framework/src/think/Facade.php',
    'think\\File' => $vendorDir . '/topthink/framework/src/think/File.php',
    'think\\Http' => $vendorDir . '/topthink/framework/src/think/Http.php',
    'think\\Lang' => $vendorDir . '/topthink/framework/src/think/Lang.php',
    'think\\Log' => $vendorDir . '/topthink/framework/src/think/Log.php',
    'think\\Manager' => $vendorDir . '/topthink/framework/src/think/Manager.php',
    'think\\Middleware' => $vendorDir . '/topthink/framework/src/think/Middleware.php',
    'think\\Model' => $vendorDir . '/topthink/think-orm/src/Model.php',
    'think\\Paginator' => $vendorDir . '/topthink/think-orm/src/Paginator.php',
    'think\\Pipeline' => $vendorDir . '/topthink/framework/src/think/Pipeline.php',
    'think\\Request' => $vendorDir . '/topthink/framework/src/think/Request.php',
    'think\\Response' => $vendorDir . '/topthink/framework/src/think/Response.php',
    'think\\Route' => $vendorDir . '/topthink/framework/src/think/Route.php',
    'think\\Service' => $vendorDir . '/topthink/framework/src/think/Service.php',
    'think\\Session' => $vendorDir . '/topthink/framework/src/think/Session.php',
    'think\\Template' => $vendorDir . '/topthink/think-template/src/Template.php',
    'think\\Validate' => $vendorDir . '/topthink/framework/src/think/Validate.php',
    'think\\View' => $vendorDir . '/topthink/framework/src/think/View.php',
    'think\\app\\MultiApp' => $vendorDir . '/topthink/think-multi-app/src/MultiApp.php',
    'think\\app\\Service' => $vendorDir . '/topthink/think-multi-app/src/Service.php',
    'think\\app\\Url' => $vendorDir . '/topthink/think-multi-app/src/Url.php',
    'think\\app\\command\\Build' => $vendorDir . '/topthink/think-multi-app/src/command/Build.php',
    'think\\app\\command\\Clear' => $vendorDir . '/topthink/think-multi-app/src/command/Clear.php',
    'think\\cache\\Driver' => $vendorDir . '/topthink/framework/src/think/cache/Driver.php',
    'think\\cache\\TagSet' => $vendorDir . '/topthink/framework/src/think/cache/TagSet.php',
    'think\\cache\\driver\\File' => $vendorDir . '/topthink/framework/src/think/cache/driver/File.php',
    'think\\cache\\driver\\Memcache' => $vendorDir . '/topthink/framework/src/think/cache/driver/Memcache.php',
    'think\\cache\\driver\\Memcached' => $vendorDir . '/topthink/framework/src/think/cache/driver/Memcached.php',
    'think\\cache\\driver\\Redis' => $vendorDir . '/topthink/framework/src/think/cache/driver/Redis.php',
    'think\\cache\\driver\\Wincache' => $vendorDir . '/topthink/framework/src/think/cache/driver/Wincache.php',
    'think\\captcha\\Captcha' => $vendorDir . '/topthink/think-captcha/src/Captcha.php',
    'think\\captcha\\CaptchaController' => $vendorDir . '/topthink/think-captcha/src/CaptchaController.php',
    'think\\captcha\\CaptchaService' => $vendorDir . '/topthink/think-captcha/src/CaptchaService.php',
    'think\\captcha\\facade\\Captcha' => $vendorDir . '/topthink/think-captcha/src/facade/Captcha.php',
    'think\\console\\Command' => $vendorDir . '/topthink/framework/src/think/console/Command.php',
    'think\\console\\Input' => $vendorDir . '/topthink/framework/src/think/console/Input.php',
    'think\\console\\Output' => $vendorDir . '/topthink/framework/src/think/console/Output.php',
    'think\\console\\Table' => $vendorDir . '/topthink/framework/src/think/console/Table.php',
    'think\\console\\command\\Clear' => $vendorDir . '/topthink/framework/src/think/console/command/Clear.php',
    'think\\console\\command\\Help' => $vendorDir . '/topthink/framework/src/think/console/command/Help.php',
    'think\\console\\command\\Lists' => $vendorDir . '/topthink/framework/src/think/console/command/Lists.php',
    'think\\console\\command\\Make' => $vendorDir . '/topthink/framework/src/think/console/command/Make.php',
    'think\\console\\command\\RouteList' => $vendorDir . '/topthink/framework/src/think/console/command/RouteList.php',
    'think\\console\\command\\RunServer' => $vendorDir . '/topthink/framework/src/think/console/command/RunServer.php',
    'think\\console\\command\\ServiceDiscover' => $vendorDir . '/topthink/framework/src/think/console/command/ServiceDiscover.php',
    'think\\console\\command\\VendorPublish' => $vendorDir . '/topthink/framework/src/think/console/command/VendorPublish.php',
    'think\\console\\command\\Version' => $vendorDir . '/topthink/framework/src/think/console/command/Version.php',
    'think\\console\\command\\make\\Command' => $vendorDir . '/topthink/framework/src/think/console/command/make/Command.php',
    'think\\console\\command\\make\\Controller' => $vendorDir . '/topthink/framework/src/think/console/command/make/Controller.php',
    'think\\console\\command\\make\\Event' => $vendorDir . '/topthink/framework/src/think/console/command/make/Event.php',
    'think\\console\\command\\make\\Listener' => $vendorDir . '/topthink/framework/src/think/console/command/make/Listener.php',
    'think\\console\\command\\make\\Middleware' => $vendorDir . '/topthink/framework/src/think/console/command/make/Middleware.php',
    'think\\console\\command\\make\\Model' => $vendorDir . '/topthink/framework/src/think/console/command/make/Model.php',
    'think\\console\\command\\make\\Service' => $vendorDir . '/topthink/framework/src/think/console/command/make/Service.php',
    'think\\console\\command\\make\\Subscribe' => $vendorDir . '/topthink/framework/src/think/console/command/make/Subscribe.php',
    'think\\console\\command\\make\\Validate' => $vendorDir . '/topthink/framework/src/think/console/command/make/Validate.php',
    'think\\console\\command\\optimize\\Route' => $vendorDir . '/topthink/framework/src/think/console/command/optimize/Route.php',
    'think\\console\\command\\optimize\\Schema' => $vendorDir . '/topthink/framework/src/think/console/command/optimize/Schema.php',
    'think\\console\\input\\Argument' => $vendorDir . '/topthink/framework/src/think/console/input/Argument.php',
    'think\\console\\input\\Definition' => $vendorDir . '/topthink/framework/src/think/console/input/Definition.php',
    'think\\console\\input\\Option' => $vendorDir . '/topthink/framework/src/think/console/input/Option.php',
    'think\\console\\output\\Ask' => $vendorDir . '/topthink/framework/src/think/console/output/Ask.php',
    'think\\console\\output\\Descriptor' => $vendorDir . '/topthink/framework/src/think/console/output/Descriptor.php',
    'think\\console\\output\\Formatter' => $vendorDir . '/topthink/framework/src/think/console/output/Formatter.php',
    'think\\console\\output\\Question' => $vendorDir . '/topthink/framework/src/think/console/output/Question.php',
    'think\\console\\output\\descriptor\\Console' => $vendorDir . '/topthink/framework/src/think/console/output/descriptor/Console.php',
    'think\\console\\output\\driver\\Buffer' => $vendorDir . '/topthink/framework/src/think/console/output/driver/Buffer.php',
    'think\\console\\output\\driver\\Console' => $vendorDir . '/topthink/framework/src/think/console/output/driver/Console.php',
    'think\\console\\output\\driver\\Nothing' => $vendorDir . '/topthink/framework/src/think/console/output/driver/Nothing.php',
    'think\\console\\output\\formatter\\Stack' => $vendorDir . '/topthink/framework/src/think/console/output/formatter/Stack.php',
    'think\\console\\output\\formatter\\Style' => $vendorDir . '/topthink/framework/src/think/console/output/formatter/Style.php',
    'think\\console\\output\\question\\Choice' => $vendorDir . '/topthink/framework/src/think/console/output/question/Choice.php',
    'think\\console\\output\\question\\Confirmation' => $vendorDir . '/topthink/framework/src/think/console/output/question/Confirmation.php',
    'think\\contract\\Arrayable' => $vendorDir . '/topthink/think-helper/src/contract/Arrayable.php',
    'think\\contract\\CacheHandlerInterface' => $vendorDir . '/topthink/framework/src/think/contract/CacheHandlerInterface.php',
    'think\\contract\\Jsonable' => $vendorDir . '/topthink/think-helper/src/contract/Jsonable.php',
    'think\\contract\\LogHandlerInterface' => $vendorDir . '/topthink/framework/src/think/contract/LogHandlerInterface.php',
    'think\\contract\\ModelRelationInterface' => $vendorDir . '/topthink/framework/src/think/contract/ModelRelationInterface.php',
    'think\\contract\\SessionHandlerInterface' => $vendorDir . '/topthink/framework/src/think/contract/SessionHandlerInterface.php',
    'think\\contract\\TemplateHandlerInterface' => $vendorDir . '/topthink/framework/src/think/contract/TemplateHandlerInterface.php',
    'think\\db\\BaseQuery' => $vendorDir . '/topthink/think-orm/src/db/BaseQuery.php',
    'think\\db\\Builder' => $vendorDir . '/topthink/think-orm/src/db/Builder.php',
    'think\\db\\CacheItem' => $vendorDir . '/topthink/think-orm/src/db/CacheItem.php',
    'think\\db\\Connection' => $vendorDir . '/topthink/think-orm/src/db/Connection.php',
    'think\\db\\ConnectionInterface' => $vendorDir . '/topthink/think-orm/src/db/ConnectionInterface.php',
    'think\\db\\Fetch' => $vendorDir . '/topthink/think-orm/src/db/Fetch.php',
    'think\\db\\Mongo' => $vendorDir . '/topthink/think-orm/src/db/Mongo.php',
    'think\\db\\PDOConnection' => $vendorDir . '/topthink/think-orm/src/db/PDOConnection.php',
    'think\\db\\Query' => $vendorDir . '/topthink/think-orm/src/db/Query.php',
    'think\\db\\Raw' => $vendorDir . '/topthink/think-orm/src/db/Raw.php',
    'think\\db\\Where' => $vendorDir . '/topthink/think-orm/src/db/Where.php',
    'think\\db\\builder\\Mongo' => $vendorDir . '/topthink/think-orm/src/db/builder/Mongo.php',
    'think\\db\\builder\\Mysql' => $vendorDir . '/topthink/think-orm/src/db/builder/Mysql.php',
    'think\\db\\builder\\Oracle' => $vendorDir . '/topthink/think-orm/src/db/builder/Oracle.php',
    'think\\db\\builder\\Pgsql' => $vendorDir . '/topthink/think-orm/src/db/builder/Pgsql.php',
    'think\\db\\builder\\Sqlite' => $vendorDir . '/topthink/think-orm/src/db/builder/Sqlite.php',
    'think\\db\\builder\\Sqlsrv' => $vendorDir . '/topthink/think-orm/src/db/builder/Sqlsrv.php',
    'think\\db\\concern\\AggregateQuery' => $vendorDir . '/topthink/think-orm/src/db/concern/AggregateQuery.php',
    'think\\db\\concern\\JoinAndViewQuery' => $vendorDir . '/topthink/think-orm/src/db/concern/JoinAndViewQuery.php',
    'think\\db\\concern\\ModelRelationQuery' => $vendorDir . '/topthink/think-orm/src/db/concern/ModelRelationQuery.php',
    'think\\db\\concern\\ParamsBind' => $vendorDir . '/topthink/think-orm/src/db/concern/ParamsBind.php',
    'think\\db\\concern\\ResultOperation' => $vendorDir . '/topthink/think-orm/src/db/concern/ResultOperation.php',
    'think\\db\\concern\\TableFieldInfo' => $vendorDir . '/topthink/think-orm/src/db/concern/TableFieldInfo.php',
    'think\\db\\concern\\TimeFieldQuery' => $vendorDir . '/topthink/think-orm/src/db/concern/TimeFieldQuery.php',
    'think\\db\\concern\\Transaction' => $vendorDir . '/topthink/think-orm/src/db/concern/Transaction.php',
    'think\\db\\concern\\WhereQuery' => $vendorDir . '/topthink/think-orm/src/db/concern/WhereQuery.php',
    'think\\db\\connector\\Mongo' => $vendorDir . '/topthink/think-orm/src/db/connector/Mongo.php',
    'think\\db\\connector\\Mysql' => $vendorDir . '/topthink/think-orm/src/db/connector/Mysql.php',
    'think\\db\\connector\\Oracle' => $vendorDir . '/topthink/think-orm/src/db/connector/Oracle.php',
    'think\\db\\connector\\Pgsql' => $vendorDir . '/topthink/think-orm/src/db/connector/Pgsql.php',
    'think\\db\\connector\\Sqlite' => $vendorDir . '/topthink/think-orm/src/db/connector/Sqlite.php',
    'think\\db\\connector\\Sqlsrv' => $vendorDir . '/topthink/think-orm/src/db/connector/Sqlsrv.php',
    'think\\db\\exception\\BindParamException' => $vendorDir . '/topthink/think-orm/src/db/exception/BindParamException.php',
    'think\\db\\exception\\DataNotFoundException' => $vendorDir . '/topthink/think-orm/src/db/exception/DataNotFoundException.php',
    'think\\db\\exception\\DbEventException' => $vendorDir . '/topthink/think-orm/src/db/exception/DbEventException.php',
    'think\\db\\exception\\DbException' => $vendorDir . '/topthink/think-orm/src/db/exception/DbException.php',
    'think\\db\\exception\\InvalidArgumentException' => $vendorDir . '/topthink/think-orm/src/db/exception/InvalidArgumentException.php',
    'think\\db\\exception\\ModelEventException' => $vendorDir . '/topthink/think-orm/src/db/exception/ModelEventException.php',
    'think\\db\\exception\\ModelNotFoundException' => $vendorDir . '/topthink/think-orm/src/db/exception/ModelNotFoundException.php',
    'think\\db\\exception\\PDOException' => $vendorDir . '/topthink/think-orm/src/db/exception/PDOException.php',
    'think\\event\\AppInit' => $vendorDir . '/topthink/framework/src/think/event/AppInit.php',
    'think\\event\\HttpEnd' => $vendorDir . '/topthink/framework/src/think/event/HttpEnd.php',
    'think\\event\\HttpRun' => $vendorDir . '/topthink/framework/src/think/event/HttpRun.php',
    'think\\event\\LogRecord' => $vendorDir . '/topthink/framework/src/think/event/LogRecord.php',
    'think\\event\\LogWrite' => $vendorDir . '/topthink/framework/src/think/event/LogWrite.php',
    'think\\event\\RouteLoaded' => $vendorDir . '/topthink/framework/src/think/event/RouteLoaded.php',
    'think\\exception\\ClassNotFoundException' => $vendorDir . '/topthink/framework/src/think/exception/ClassNotFoundException.php',
    'think\\exception\\ErrorException' => $vendorDir . '/topthink/framework/src/think/exception/ErrorException.php',
    'think\\exception\\FileException' => $vendorDir . '/topthink/framework/src/think/exception/FileException.php',
    'think\\exception\\FuncNotFoundException' => $vendorDir . '/topthink/framework/src/think/exception/FuncNotFoundException.php',
    'think\\exception\\Handle' => $vendorDir . '/topthink/framework/src/think/exception/Handle.php',
    'think\\exception\\HttpException' => $vendorDir . '/topthink/framework/src/think/exception/HttpException.php',
    'think\\exception\\HttpResponseException' => $vendorDir . '/topthink/framework/src/think/exception/HttpResponseException.php',
    'think\\exception\\InvalidArgumentException' => $vendorDir . '/topthink/framework/src/think/exception/InvalidArgumentException.php',
    'think\\exception\\RouteNotFoundException' => $vendorDir . '/topthink/framework/src/think/exception/RouteNotFoundException.php',
    'think\\exception\\ValidateException' => $vendorDir . '/topthink/framework/src/think/exception/ValidateException.php',
    'think\\facade\\App' => $vendorDir . '/topthink/framework/src/think/facade/App.php',
    'think\\facade\\Cache' => $vendorDir . '/topthink/framework/src/think/facade/Cache.php',
    'think\\facade\\Config' => $vendorDir . '/topthink/framework/src/think/facade/Config.php',
    'think\\facade\\Console' => $vendorDir . '/topthink/framework/src/think/facade/Console.php',
    'think\\facade\\Cookie' => $vendorDir . '/topthink/framework/src/think/facade/Cookie.php',
    'think\\facade\\Db' => $vendorDir . '/topthink/think-orm/src/facade/Db.php',
    'think\\facade\\Env' => $vendorDir . '/topthink/framework/src/think/facade/Env.php',
    'think\\facade\\Event' => $vendorDir . '/topthink/framework/src/think/facade/Event.php',
    'think\\facade\\Lang' => $vendorDir . '/topthink/framework/src/think/facade/Lang.php',
    'think\\facade\\Log' => $vendorDir . '/topthink/framework/src/think/facade/Log.php',
    'think\\facade\\Middleware' => $vendorDir . '/topthink/framework/src/think/facade/Middleware.php',
    'think\\facade\\Request' => $vendorDir . '/topthink/framework/src/think/facade/Request.php',
    'think\\facade\\Route' => $vendorDir . '/topthink/framework/src/think/facade/Route.php',
    'think\\facade\\Session' => $vendorDir . '/topthink/framework/src/think/facade/Session.php',
    'think\\facade\\Template' => $vendorDir . '/topthink/think-template/src/facade/Template.php',
    'think\\facade\\Validate' => $vendorDir . '/topthink/framework/src/think/facade/Validate.php',
    'think\\facade\\View' => $vendorDir . '/topthink/framework/src/think/facade/View.php',
    'think\\file\\UploadedFile' => $vendorDir . '/topthink/framework/src/think/file/UploadedFile.php',
    'think\\helper\\Arr' => $vendorDir . '/topthink/think-helper/src/helper/Arr.php',
    'think\\helper\\Macroable' => $vendorDir . '/topthink/think-helper/src/helper/Macroable.php',
    'think\\helper\\Str' => $vendorDir . '/topthink/think-helper/src/helper/Str.php',
    'think\\initializer\\BootService' => $vendorDir . '/topthink/framework/src/think/initializer/BootService.php',
    'think\\initializer\\Error' => $vendorDir . '/topthink/framework/src/think/initializer/Error.php',
    'think\\initializer\\RegisterService' => $vendorDir . '/topthink/framework/src/think/initializer/RegisterService.php',
    'think\\log\\Channel' => $vendorDir . '/topthink/framework/src/think/log/Channel.php',
    'think\\log\\ChannelSet' => $vendorDir . '/topthink/framework/src/think/log/ChannelSet.php',
    'think\\log\\driver\\File' => $vendorDir . '/topthink/framework/src/think/log/driver/File.php',
    'think\\log\\driver\\Socket' => $vendorDir . '/topthink/framework/src/think/log/driver/Socket.php',
    'think\\middleware\\AllowCrossDomain' => $vendorDir . '/topthink/framework/src/think/middleware/AllowCrossDomain.php',
    'think\\middleware\\CheckRequestCache' => $vendorDir . '/topthink/framework/src/think/middleware/CheckRequestCache.php',
    'think\\middleware\\FormTokenCheck' => $vendorDir . '/topthink/framework/src/think/middleware/FormTokenCheck.php',
    'think\\middleware\\LoadLangPack' => $vendorDir . '/topthink/framework/src/think/middleware/LoadLangPack.php',
    'think\\middleware\\SessionInit' => $vendorDir . '/topthink/framework/src/think/middleware/SessionInit.php',
    'think\\model\\Collection' => $vendorDir . '/topthink/think-orm/src/model/Collection.php',
    'think\\model\\Pivot' => $vendorDir . '/topthink/think-orm/src/model/Pivot.php',
    'think\\model\\Relation' => $vendorDir . '/topthink/think-orm/src/model/Relation.php',
    'think\\model\\concern\\Attribute' => $vendorDir . '/topthink/think-orm/src/model/concern/Attribute.php',
    'think\\model\\concern\\Conversion' => $vendorDir . '/topthink/think-orm/src/model/concern/Conversion.php',
    'think\\model\\concern\\ModelEvent' => $vendorDir . '/topthink/think-orm/src/model/concern/ModelEvent.php',
    'think\\model\\concern\\OptimLock' => $vendorDir . '/topthink/think-orm/src/model/concern/OptimLock.php',
    'think\\model\\concern\\RelationShip' => $vendorDir . '/topthink/think-orm/src/model/concern/RelationShip.php',
    'think\\model\\concern\\SoftDelete' => $vendorDir . '/topthink/think-orm/src/model/concern/SoftDelete.php',
    'think\\model\\concern\\TimeStamp' => $vendorDir . '/topthink/think-orm/src/model/concern/TimeStamp.php',
    'think\\model\\concern\\Virtual' => $vendorDir . '/topthink/think-orm/src/model/concern/Virtual.php',
    'think\\model\\relation\\BelongsTo' => $vendorDir . '/topthink/think-orm/src/model/relation/BelongsTo.php',
    'think\\model\\relation\\BelongsToMany' => $vendorDir . '/topthink/think-orm/src/model/relation/BelongsToMany.php',
    'think\\model\\relation\\HasMany' => $vendorDir . '/topthink/think-orm/src/model/relation/HasMany.php',
    'think\\model\\relation\\HasManyThrough' => $vendorDir . '/topthink/think-orm/src/model/relation/HasManyThrough.php',
    'think\\model\\relation\\HasOne' => $vendorDir . '/topthink/think-orm/src/model/relation/HasOne.php',
    'think\\model\\relation\\HasOneThrough' => $vendorDir . '/topthink/think-orm/src/model/relation/HasOneThrough.php',
    'think\\model\\relation\\MorphMany' => $vendorDir . '/topthink/think-orm/src/model/relation/MorphMany.php',
    'think\\model\\relation\\MorphOne' => $vendorDir . '/topthink/think-orm/src/model/relation/MorphOne.php',
    'think\\model\\relation\\MorphTo' => $vendorDir . '/topthink/think-orm/src/model/relation/MorphTo.php',
    'think\\model\\relation\\MorphToMany' => $vendorDir . '/topthink/think-orm/src/model/relation/MorphToMany.php',
    'think\\model\\relation\\OneToOne' => $vendorDir . '/topthink/think-orm/src/model/relation/OneToOne.php',
    'think\\paginator\\driver\\Bootstrap' => $vendorDir . '/topthink/think-orm/src/paginator/driver/Bootstrap.php',
    'think\\response\\File' => $vendorDir . '/topthink/framework/src/think/response/File.php',
    'think\\response\\Html' => $vendorDir . '/topthink/framework/src/think/response/Html.php',
    'think\\response\\Json' => $vendorDir . '/topthink/framework/src/think/response/Json.php',
    'think\\response\\Jsonp' => $vendorDir . '/topthink/framework/src/think/response/Jsonp.php',
    'think\\response\\Redirect' => $vendorDir . '/topthink/framework/src/think/response/Redirect.php',
    'think\\response\\View' => $vendorDir . '/topthink/framework/src/think/response/View.php',
    'think\\response\\Xml' => $vendorDir . '/topthink/framework/src/think/response/Xml.php',
    'think\\route\\Dispatch' => $vendorDir . '/topthink/framework/src/think/route/Dispatch.php',
    'think\\route\\Domain' => $vendorDir . '/topthink/framework/src/think/route/Domain.php',
    'think\\route\\Resource' => $vendorDir . '/topthink/framework/src/think/route/Resource.php',
    'think\\route\\ResourceRegister' => $vendorDir . '/topthink/framework/src/think/route/ResourceRegister.php',
    'think\\route\\Rule' => $vendorDir . '/topthink/framework/src/think/route/Rule.php',
    'think\\route\\RuleGroup' => $vendorDir . '/topthink/framework/src/think/route/RuleGroup.php',
    'think\\route\\RuleItem' => $vendorDir . '/topthink/framework/src/think/route/RuleItem.php',
    'think\\route\\RuleName' => $vendorDir . '/topthink/framework/src/think/route/RuleName.php',
    'think\\route\\Url' => $vendorDir . '/topthink/framework/src/think/route/Url.php',
    'think\\route\\dispatch\\Callback' => $vendorDir . '/topthink/framework/src/think/route/dispatch/Callback.php',
    'think\\route\\dispatch\\Controller' => $vendorDir . '/topthink/framework/src/think/route/dispatch/Controller.php',
    'think\\route\\dispatch\\Url' => $vendorDir . '/topthink/framework/src/think/route/dispatch/Url.php',
    'think\\service\\ModelService' => $vendorDir . '/topthink/framework/src/think/service/ModelService.php',
    'think\\service\\PaginatorService' => $vendorDir . '/topthink/framework/src/think/service/PaginatorService.php',
    'think\\service\\ValidateService' => $vendorDir . '/topthink/framework/src/think/service/ValidateService.php',
    'think\\session\\Store' => $vendorDir . '/topthink/framework/src/think/session/Store.php',
    'think\\session\\driver\\Cache' => $vendorDir . '/topthink/framework/src/think/session/driver/Cache.php',
    'think\\session\\driver\\File' => $vendorDir . '/topthink/framework/src/think/session/driver/File.php',
    'think\\template\\TagLib' => $vendorDir . '/topthink/think-template/src/template/TagLib.php',
    'think\\template\\driver\\File' => $vendorDir . '/topthink/think-template/src/template/driver/File.php',
    'think\\template\\exception\\TemplateNotFoundException' => $vendorDir . '/topthink/think-template/src/template/exception/TemplateNotFoundException.php',
    'think\\template\\taglib\\Cx' => $vendorDir . '/topthink/think-template/src/template/taglib/Cx.php',
    'think\\validate\\ValidateRule' => $vendorDir . '/topthink/framework/src/think/validate/ValidateRule.php',
    'think\\view\\driver\\Php' => $vendorDir . '/topthink/framework/src/think/view/driver/Php.php',
    'think\\view\\driver\\Think' => $vendorDir . '/topthink/think-view/src/Think.php',
);
