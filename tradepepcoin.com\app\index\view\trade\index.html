{extend name="index/layout" /}

{block name="css"}
<style>
.trading-container {
    padding-top: 70px;
    background: #f8f9fa;
    min-height: 100vh;
}

.trading-header {
    background: white;
    border-bottom: 1px solid #dee2e6;
    padding: 15px 0;
    margin-bottom: 20px;
}

.symbol-selector {
    display: flex;
    align-items: center;
    gap: 15px;
}

.symbol-info h3 {
    margin: 0;
    font-size: 24px;
    font-weight: bold;
}

.price-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.current-price {
    font-size: 20px;
    font-weight: bold;
}

.price-change {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
}

.price-up {
    color: #28a745;
    background: rgba(40, 167, 69, 0.1);
}

.price-down {
    color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
}

.trading-main {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 20px;
    margin-bottom: 20px;
}

.chart-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.chart-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-tabs {
    display: flex;
    gap: 10px;
}

.chart-tab {
    padding: 8px 16px;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.chart-tab.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.chart-area {
    height: 400px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    position: relative;
}

.trading-panel {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    height: fit-content;
}

.trade-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid #dee2e6;
}

.trade-tab {
    flex: 1;
    padding: 12px;
    text-align: center;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s;
}

.trade-tab.active {
    color: #007bff;
    border-bottom-color: #007bff;
}

.trade-tab.buy.active {
    color: #28a745;
    border-bottom-color: #28a745;
}

.trade-tab.sell.active {
    color: #dc3545;
    border-bottom-color: #dc3545;
}

.trade-form {
    display: none;
}

.trade-form.active {
    display: block;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 14px;
}

.btn-trade {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-buy {
    background: #28a745;
    color: white;
}

.btn-buy:hover {
    background: #218838;
}

.btn-sell {
    background: #dc3545;
    color: white;
}

.btn-sell:hover {
    background: #c82333;
}

.balance-info {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 15px;
    font-size: 14px;
}

.trading-bottom {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
}

.orderbook-container,
.trades-container,
.orders-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.section-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dee2e6;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.orderbook-table,
.trades-table,
.orders-table {
    width: 100%;
    font-size: 12px;
}

.orderbook-table th,
.trades-table th,
.orders-table th {
    padding: 8px 4px;
    text-align: right;
    font-weight: 500;
    color: #6c757d;
    border-bottom: 1px solid #dee2e6;
}

.orderbook-table td,
.trades-table td,
.orders-table td {
    padding: 4px;
    text-align: right;
    border-bottom: 1px solid #f8f9fa;
}

.bid-price {
    color: #28a745;
}

.ask-price {
    color: #dc3545;
}

.buy-trade {
    color: #28a745;
}

.sell-trade {
    color: #dc3545;
}

@media (max-width: 768px) {
    .trading-main {
        grid-template-columns: 1fr;
    }
    
    .trading-bottom {
        grid-template-columns: 1fr;
    }
    
    .price-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}
</style>
{/block}

{block name="content"}
<div class="trading-container">
    <!-- 交易对信息 -->
    <div class="trading-header">
        <div class="container">
            <div class="symbol-selector">
                <div class="symbol-info">
                    <h3 id="currentSymbol">BTC/USDT</h3>
                </div>
                <div class="price-info">
                    <span class="current-price" id="currentPrice">$45,230.50</span>
                    <span class="price-change price-up" id="priceChange">+2.34%</span>
                    <div class="stats">
                        <small class="text-muted">24h高: <span id="high24h">$46,500.00</span></small>
                        <small class="text-muted">24h低: <span id="low24h">$44,100.00</span></small>
                        <small class="text-muted">24h量: <span id="volume24h">1,234.56 BTC</span></small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 主要交易区域 -->
        <div class="trading-main">
            <!-- K线图区域 -->
            <div class="chart-container">
                <div class="chart-header">
                    <div class="chart-tabs">
                        <button class="chart-tab active" data-interval="1m">1分</button>
                        <button class="chart-tab" data-interval="5m">5分</button>
                        <button class="chart-tab" data-interval="15m">15分</button>
                        <button class="chart-tab" data-interval="1h">1小时</button>
                        <button class="chart-tab" data-interval="4h">4小时</button>
                        <button class="chart-tab" data-interval="1d">1天</button>
                    </div>
                    <div class="chart-tools">
                        <button class="btn btn-sm btn-outline-secondary" id="fullscreenBtn">
                            <i class="fas fa-expand"></i>
                        </button>
                    </div>
                </div>
                <div class="chart-area" id="tradingChart">
                    <!-- TradingView图表将在这里渲染 -->
                    <div class="d-flex align-items-center justify-content-center h-100">
                        <div class="text-center">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">K线图表</h5>
                            <p class="text-muted">实时价格走势</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 交易面板 -->
            <div class="trading-panel">
                <div class="trade-tabs">
                    <div class="trade-tab buy active" data-type="buy">买入</div>
                    <div class="trade-tab sell" data-type="sell">卖出</div>
                </div>

                <!-- 买入表单 -->
                <div class="trade-form active" id="buyForm">
                    <div class="balance-info">
                        可用余额: <span id="usdtBalance">1,000.00 USDT</span>
                    </div>
                    
                    <div class="form-group">
                        <label>价格 (USDT)</label>
                        <input type="number" class="form-control" id="buyPrice" placeholder="市价">
                    </div>
                    
                    <div class="form-group">
                        <label>数量 (BTC)</label>
                        <input type="number" class="form-control" id="buyAmount" placeholder="0.00">
                    </div>
                    
                    <div class="form-group">
                        <label>总额 (USDT)</label>
                        <input type="number" class="form-control" id="buyTotal" placeholder="0.00">
                    </div>
                    
                    <div class="percentage-buttons mb-3">
                        <button class="btn btn-sm btn-outline-secondary me-1" data-percent="25">25%</button>
                        <button class="btn btn-sm btn-outline-secondary me-1" data-percent="50">50%</button>
                        <button class="btn btn-sm btn-outline-secondary me-1" data-percent="75">75%</button>
                        <button class="btn btn-sm btn-outline-secondary" data-percent="100">100%</button>
                    </div>
                    
                    <button class="btn-trade btn-buy" id="buyBtn">买入 BTC</button>
                </div>

                <!-- 卖出表单 -->
                <div class="trade-form" id="sellForm">
                    <div class="balance-info">
                        可用余额: <span id="btcBalance">0.12345 BTC</span>
                    </div>
                    
                    <div class="form-group">
                        <label>价格 (USDT)</label>
                        <input type="number" class="form-control" id="sellPrice" placeholder="市价">
                    </div>
                    
                    <div class="form-group">
                        <label>数量 (BTC)</label>
                        <input type="number" class="form-control" id="sellAmount" placeholder="0.00">
                    </div>
                    
                    <div class="form-group">
                        <label>总额 (USDT)</label>
                        <input type="number" class="form-control" id="sellTotal" placeholder="0.00">
                    </div>
                    
                    <div class="percentage-buttons mb-3">
                        <button class="btn btn-sm btn-outline-secondary me-1" data-percent="25">25%</button>
                        <button class="btn btn-sm btn-outline-secondary me-1" data-percent="50">50%</button>
                        <button class="btn btn-sm btn-outline-secondary me-1" data-percent="75">75%</button>
                        <button class="btn btn-sm btn-outline-secondary" data-percent="100">100%</button>
                    </div>
                    
                    <button class="btn-trade btn-sell" id="sellBtn">卖出 BTC</button>
                </div>
            </div>
        </div>

        <!-- 底部信息区域 -->
        <div class="trading-bottom">
            <!-- 订单簿 -->
            <div class="orderbook-container">
                <div class="section-header">
                    <h6 class="section-title">订单簿</h6>
                    <small class="text-muted">深度</small>
                </div>
                <table class="orderbook-table">
                    <thead>
                        <tr>
                            <th>价格(USDT)</th>
                            <th>数量(BTC)</th>
                            <th>累计</th>
                        </tr>
                    </thead>
                    <tbody id="orderbookAsks">
                        <!-- 卖单数据 -->
                    </tbody>
                </table>
                <div class="spread-info text-center py-2">
                    <small class="text-muted">价差: <span id="spread">0.50</span></small>
                </div>
                <table class="orderbook-table">
                    <tbody id="orderbookBids">
                        <!-- 买单数据 -->
                    </tbody>
                </table>
            </div>

            <!-- 最新成交 -->
            <div class="trades-container">
                <div class="section-header">
                    <h6 class="section-title">最新成交</h6>
                    <small class="text-muted">实时</small>
                </div>
                <table class="trades-table">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>价格(USDT)</th>
                            <th>数量(BTC)</th>
                        </tr>
                    </thead>
                    <tbody id="recentTrades">
                        <!-- 成交数据 -->
                    </tbody>
                </table>
            </div>

            <!-- 我的订单 -->
            <div class="orders-container">
                <div class="section-header">
                    <h6 class="section-title">我的订单</h6>
                    <div class="order-tabs">
                        <small class="order-tab active" data-status="open">当前</small>
                        <small class="order-tab" data-status="history">历史</small>
                    </div>
                </div>
                <table class="orders-table">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>类型</th>
                            <th>价格</th>
                            <th>数量</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="userOrders">
                        <!-- 订单数据 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="js"}
<!-- TradingView图表库 -->
<script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>

<script>
$(document).ready(function() {
    let currentSymbol = 'BTCUSDT';
    let currentInterval = '1m';
    let chart = null;
    let candlestickSeries = null;
    let ws = null;

    // 初始化
    init();

    function init() {
        initChart();
        initWebSocket();
        loadInitialData();
        bindEvents();
    }

    // 初始化图表
    function initChart() {
        const chartContainer = document.getElementById('tradingChart');
        chartContainer.innerHTML = '';
        
        chart = LightweightCharts.createChart(chartContainer, {
            width: chartContainer.clientWidth,
            height: 400,
            layout: {
                backgroundColor: '#ffffff',
                textColor: '#333',
            },
            grid: {
                vertLines: {
                    color: '#f0f0f0',
                },
                horzLines: {
                    color: '#f0f0f0',
                },
            },
            crosshair: {
                mode: LightweightCharts.CrosshairMode.Normal,
            },
            rightPriceScale: {
                borderColor: '#cccccc',
            },
            timeScale: {
                borderColor: '#cccccc',
                timeVisible: true,
                secondsVisible: false,
            },
        });

        candlestickSeries = chart.addCandlestickSeries({
            upColor: '#26a69a',
            downColor: '#ef5350',
            borderVisible: false,
            wickUpColor: '#26a69a',
            wickDownColor: '#ef5350',
        });

        // 响应式调整
        window.addEventListener('resize', () => {
            chart.applyOptions({ width: chartContainer.clientWidth });
        });
    }

    // 初始化WebSocket
    function initWebSocket() {
        if (ws) {
            ws.close();
        }

        ws = new WebSocket('ws://localhost:9501');
        
        ws.onopen = function() {
            console.log('WebSocket连接已建立');
            subscribeToStreams();
        };

        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            handleWebSocketMessage(data);
        };

        ws.onclose = function() {
            console.log('WebSocket连接已关闭');
            // 重连逻辑
            setTimeout(initWebSocket, 5000);
        };

        ws.onerror = function(error) {
            console.error('WebSocket错误:', error);
        };
    }

    // 订阅数据流
    function subscribeToStreams() {
        const subscribeMessage = {
            method: 'SUBSCRIBE',
            params: [
                `${currentSymbol.toLowerCase()}@ticker`,
                `${currentSymbol.toLowerCase()}@depth`,
                `${currentSymbol.toLowerCase()}@trade`,
                `${currentSymbol.toLowerCase()}@kline_${currentInterval}`
            ],
            id: 1
        };

        ws.send(JSON.stringify(subscribeMessage));
    }

    // 处理WebSocket消息
    function handleWebSocketMessage(data) {
        if (data.stream) {
            const stream = data.stream;
            
            if (stream.includes('@ticker')) {
                updateTicker(data.data);
            } else if (stream.includes('@depth')) {
                updateOrderbook(data.data);
            } else if (stream.includes('@trade')) {
                updateRecentTrades(data.data);
            } else if (stream.includes('@kline')) {
                updateChart(data.data);
            }
        }
    }

    // 更新价格信息
    function updateTicker(data) {
        $('#currentPrice').text('$' + parseFloat(data.c).toFixed(2));
        
        const change = parseFloat(data.P);
        const changeElement = $('#priceChange');
        
        changeElement.text((change >= 0 ? '+' : '') + change.toFixed(2) + '%');
        changeElement.removeClass('price-up price-down');
        changeElement.addClass(change >= 0 ? 'price-up' : 'price-down');
        
        $('#high24h').text('$' + parseFloat(data.h).toFixed(2));
        $('#low24h').text('$' + parseFloat(data.l).toFixed(2));
        $('#volume24h').text(parseFloat(data.v).toFixed(2) + ' BTC');
    }

    // 更新订单簿
    function updateOrderbook(data) {
        const asksHtml = data.asks.slice(0, 10).reverse().map(ask => `
            <tr>
                <td class="ask-price">${parseFloat(ask[0]).toFixed(2)}</td>
                <td>${parseFloat(ask[1]).toFixed(6)}</td>
                <td>${(parseFloat(ask[0]) * parseFloat(ask[1])).toFixed(2)}</td>
            </tr>
        `).join('');

        const bidsHtml = data.bids.slice(0, 10).map(bid => `
            <tr>
                <td class="bid-price">${parseFloat(bid[0]).toFixed(2)}</td>
                <td>${parseFloat(bid[1]).toFixed(6)}</td>
                <td>${(parseFloat(bid[0]) * parseFloat(bid[1])).toFixed(2)}</td>
            </tr>
        `).join('');

        $('#orderbookAsks').html(asksHtml);
        $('#orderbookBids').html(bidsHtml);

        // 计算价差
        if (data.asks.length > 0 && data.bids.length > 0) {
            const spread = parseFloat(data.asks[0][0]) - parseFloat(data.bids[0][0]);
            $('#spread').text(spread.toFixed(2));
        }
    }

    // 更新最新成交
    function updateRecentTrades(data) {
        const time = new Date(data.T).toLocaleTimeString();
        const price = parseFloat(data.p).toFixed(2);
        const amount = parseFloat(data.q).toFixed(6);
        const isBuy = !data.m;

        const tradeHtml = `
            <tr>
                <td>${time}</td>
                <td class="${isBuy ? 'buy-trade' : 'sell-trade'}">${price}</td>
                <td>${amount}</td>
            </tr>
        `;

        const tradesTable = $('#recentTrades');
        tradesTable.prepend(tradeHtml);
        
        // 保持最多50条记录
        if (tradesTable.children().length > 50) {
            tradesTable.children().last().remove();
        }
    }

    // 更新图表
    function updateChart(data) {
        const kline = data.k;
        const candleData = {
            time: kline.t / 1000,
            open: parseFloat(kline.o),
            high: parseFloat(kline.h),
            low: parseFloat(kline.l),
            close: parseFloat(kline.c)
        };

        candlestickSeries.update(candleData);
    }

    // 加载初始数据
    function loadInitialData() {
        // 加载K线数据
        loadKlineData();
        
        // 加载用户余额
        loadUserBalance();
        
        // 加载用户订单
        loadUserOrders();
    }

    // 加载K线数据
    function loadKlineData() {
        $.get('/api/market/klines', {
            symbol: currentSymbol,
            interval: currentInterval,
            limit: 500
        }).done(function(response) {
            if (response.code === 1) {
                const klineData = response.data.map(item => ({
                    time: item[0] / 1000,
                    open: parseFloat(item[1]),
                    high: parseFloat(item[2]),
                    low: parseFloat(item[3]),
                    close: parseFloat(item[4])
                }));

                candlestickSeries.setData(klineData);
            }
        });
    }

    // 加载用户余额
    function loadUserBalance() {
        $.get('/api/user/assets').done(function(response) {
            if (response.code === 1) {
                response.data.forEach(asset => {
                    if (asset.coin_symbol === 'USDT') {
                        $('#usdtBalance').text(asset.available + ' USDT');
                    } else if (asset.coin_symbol === 'BTC') {
                        $('#btcBalance').text(asset.available + ' BTC');
                    }
                });
            }
        });
    }

    // 加载用户订单
    function loadUserOrders() {
        $.get('/api/trade/openOrders', {
            symbol: currentSymbol
        }).done(function(response) {
            if (response.code === 1) {
                updateOrdersTable(response.data);
            }
        });
    }

    // 更新订单表格
    function updateOrdersTable(orders) {
        const ordersHtml = orders.map(order => `
            <tr>
                <td>${new Date(order.time).toLocaleTimeString()}</td>
                <td class="${order.side.toLowerCase() === 'buy' ? 'buy-trade' : 'sell-trade'}">${order.side}</td>
                <td>${parseFloat(order.price).toFixed(2)}</td>
                <td>${parseFloat(order.orig_qty).toFixed(6)}</td>
                <td>${getOrderStatusText(order.status)}</td>
                <td>
                    ${order.status === 'NEW' ? `<button class="btn btn-sm btn-outline-danger" onclick="cancelOrder('${order.order_id}')">取消</button>` : '-'}
                </td>
            </tr>
        `).join('');

        $('#userOrders').html(ordersHtml);
    }

    // 绑定事件
    function bindEvents() {
        // 交易类型切换
        $('.trade-tab').click(function() {
            const type = $(this).data('type');
            
            $('.trade-tab').removeClass('active');
            $(this).addClass('active');
            
            $('.trade-form').removeClass('active');
            $(`#${type}Form`).addClass('active');
        });

        // 时间周期切换
        $('.chart-tab').click(function() {
            const interval = $(this).data('interval');
            
            $('.chart-tab').removeClass('active');
            $(this).addClass('active');
            
            currentInterval = interval;
            loadKlineData();
            
            // 重新订阅WebSocket
            if (ws && ws.readyState === WebSocket.OPEN) {
                subscribeToStreams();
            }
        });

        // 百分比按钮
        $('.percentage-buttons button').click(function() {
            const percent = $(this).data('percent');
            const isActive = $(this).closest('.trade-form').hasClass('active');
            const isBuy = $(this).closest('#buyForm').length > 0;
            
            if (isBuy) {
                const balance = parseFloat($('#usdtBalance').text());
                const price = parseFloat($('#buyPrice').val()) || parseFloat($('#currentPrice').text().replace('$', ''));
                const amount = (balance * percent / 100) / price;
                
                $('#buyAmount').val(amount.toFixed(6));
                $('#buyTotal').val((balance * percent / 100).toFixed(2));
            } else {
                const balance = parseFloat($('#btcBalance').text());
                const amount = balance * percent / 100;
                const price = parseFloat($('#sellPrice').val()) || parseFloat($('#currentPrice').text().replace('$', ''));
                
                $('#sellAmount').val(amount.toFixed(6));
                $('#sellTotal').val((amount * price).toFixed(2));
            }
        });

        // 价格/数量/总额联动
        $('#buyPrice, #buyAmount').on('input', function() {
            const price = parseFloat($('#buyPrice').val()) || 0;
            const amount = parseFloat($('#buyAmount').val()) || 0;
            $('#buyTotal').val((price * amount).toFixed(2));
        });

        $('#buyTotal').on('input', function() {
            const total = parseFloat($('#buyTotal').val()) || 0;
            const price = parseFloat($('#buyPrice').val()) || parseFloat($('#currentPrice').text().replace('$', ''));
            $('#buyAmount').val((total / price).toFixed(6));
        });

        $('#sellPrice, #sellAmount').on('input', function() {
            const price = parseFloat($('#sellPrice').val()) || 0;
            const amount = parseFloat($('#sellAmount').val()) || 0;
            $('#sellTotal').val((price * amount).toFixed(2));
        });

        $('#sellTotal').on('input', function() {
            const total = parseFloat($('#sellTotal').val()) || 0;
            const price = parseFloat($('#sellPrice').val()) || parseFloat($('#currentPrice').text().replace('$', ''));
            $('#sellAmount').val((total / price).toFixed(6));
        });

        // 买入按钮
        $('#buyBtn').click(function() {
            const price = $('#buyPrice').val();
            const amount = $('#buyAmount').val();
            
            if (!amount || parseFloat(amount) <= 0) {
                showToast('请输入有效的数量', 'warning');
                return;
            }

            placeOrder('BUY', price, amount);
        });

        // 卖出按钮
        $('#sellBtn').click(function() {
            const price = $('#sellPrice').val();
            const amount = $('#sellAmount').val();
            
            if (!amount || parseFloat(amount) <= 0) {
                showToast('请输入有效的数量', 'warning');
                return;
            }

            placeOrder('SELL', price, amount);
        });

        // 全屏按钮
        $('#fullscreenBtn').click(function() {
            const chartContainer = document.getElementById('tradingChart');
            if (chartContainer.requestFullscreen) {
                chartContainer.requestFullscreen();
            }
        });
    }

    // 下单
    function placeOrder(side, price, quantity) {
        const orderData = {
            symbol: currentSymbol,
            side: side,
            type: price ? 'LIMIT' : 'MARKET',
            quantity: quantity
        };

        if (price) {
            orderData.price = price;
        }

        $.post('/api/trade/order', orderData).done(function(response) {
            if (response.code === 1) {
                showToast('订单提交成功', 'success');
                
                // 清空表单
                if (side === 'BUY') {
                    $('#buyAmount, #buyTotal').val('');
                } else {
                    $('#sellAmount, #sellTotal').val('');
                }
                
                // 刷新数据
                loadUserBalance();
                loadUserOrders();
            } else {
                showToast(response.msg || '订单提交失败', 'error');
            }
        }).fail(function() {
            showToast('网络错误，请稍后重试', 'error');
        });
    }

    // 取消订单
    window.cancelOrder = function(orderId) {
        if (!confirm('确定要取消这个订单吗？')) {
            return;
        }

        $.ajax({
            url: '/api/trade/cancelOrder',
            method: 'DELETE',
            data: {
                symbol: currentSymbol,
                order_id: orderId
            }
        }).done(function(response) {
            if (response.code === 1) {
                showToast('订单取消成功', 'success');
                loadUserOrders();
            } else {
                showToast(response.msg || '订单取消失败', 'error');
            }
        });
    };

    // 获取订单状态文本
    function getOrderStatusText(status) {
        const statusMap = {
            'NEW': '新订单',
            'PARTIALLY_FILLED': '部分成交',
            'FILLED': '完全成交',
            'CANCELED': '已取消',
            'REJECTED': '已拒绝'
        };
        
        return statusMap[status] || status;
    }

    // 设置当前价格到表单
    $(document).on('click', '.bid-price, .ask-price', function() {
        const price = $(this).text();
        $('#buyPrice, #sellPrice').val(price);
    });
});
</script>
{/block}
