{extend name="layout/base" /}

{block name="css"}
<style>
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 120px 0 80px;
    color: white;
}

.market-ticker {
    background: #f8f9fa;
    padding: 15px 0;
    border-bottom: 1px solid #dee2e6;
}

.ticker-item {
    display: inline-block;
    margin-right: 30px;
    white-space: nowrap;
}

.price-up { color: #28a745; }
.price-down { color: #dc3545; }

.trading-card {
    transition: transform 0.2s;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.trading-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.stats-card {
    background: white;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 24px;
}
</style>
{/block}

{block name="content"}
<!-- 市场行情滚动条 -->
<div class="market-ticker">
    <div class="container">
        <div class="ticker-scroll">
            {volist name="marketData" id="market" key="symbol"}
            <span class="ticker-item">
                <strong>{$symbol}</strong>
                <span class="ms-2">${$market.price}</span>
                <span class="ms-1 {$market.trend == 'up' ? 'price-up' : 'price-down'}">
                    {$market.change}
                </span>
            </span>
            {/volist}
        </div>
    </div>
</div>

<!-- 英雄区域 -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">
                    专业的数字货币交易平台
                </h1>
                <p class="lead mb-4">
                    安全、稳定、高效的数字资产交易服务，支持比特币、以太坊等主流数字货币交易
                </p>
                <div class="d-flex gap-3">
                    <a href="/auth/register" class="btn btn-light btn-lg px-4">
                        <i class="fas fa-user-plus me-2"></i>立即注册
                    </a>
                    <a href="/trade" class="btn btn-outline-light btn-lg px-4">
                        <i class="fas fa-chart-line me-2"></i>开始交易
                    </a>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="row g-3">
                    <div class="col-6">
                        <div class="stats-card">
                            <h3 class="text-primary mb-2">100+</h3>
                            <p class="text-muted mb-0">交易对</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stats-card">
                            <h3 class="text-primary mb-2">1M+</h3>
                            <p class="text-muted mb-0">注册用户</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stats-card">
                            <h3 class="text-primary mb-2">$10B+</h3>
                            <p class="text-muted mb-0">交易量</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stats-card">
                            <h3 class="text-primary mb-2">24/7</h3>
                            <p class="text-muted mb-0">客服支持</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 热门交易对 -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="text-center mb-5">热门交易对</h2>
            </div>
        </div>
        <div class="row g-4">
            {volist name="tradingPairs" id="pair"}
            <div class="col-lg-3 col-md-6">
                <div class="card trading-card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="card-title mb-0">{$pair.symbol}</h5>
                            <span class="badge bg-primary">热门</span>
                        </div>
                        <div class="mb-2">
                            <span class="text-muted">当前价格</span>
                            <h4 class="text-primary mb-0">${$pair.current_price|number_format=2}</h4>
                        </div>
                        <div class="d-flex justify-content-between text-sm">
                            <span class="text-muted">24h涨跌</span>
                            <span class="{$pair.change_24h >= 0 ? 'price-up' : 'price-down'}">
                                {$pair.change_24h >= 0 ? '+' : ''}{$pair.change_24h}%
                            </span>
                        </div>
                        <div class="d-flex justify-content-between text-sm">
                            <span class="text-muted">24h成交量</span>
                            <span>{$pair.volume_24h|number_format=2}</span>
                        </div>
                        <div class="mt-3">
                            <a href="/trade?symbol={$pair.symbol}" class="btn btn-primary btn-sm w-100">
                                立即交易
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {/volist}
        </div>
    </div>
</section>

<!-- 平台特色 -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="text-center mb-5">平台特色</h2>
            </div>
        </div>
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="text-center">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h5>安全可靠</h5>
                    <p class="text-muted">多重安全防护，冷热钱包分离，资产安全有保障</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="text-center">
                    <div class="feature-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h5>极速交易</h5>
                    <p class="text-muted">高性能撮合引擎，毫秒级交易执行</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="text-center">
                    <div class="feature-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <h5>丰富币种</h5>
                    <p class="text-muted">支持主流数字货币，交易对丰富</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="text-center">
                    <div class="feature-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h5>专业服务</h5>
                    <p class="text-muted">7×24小时客服支持，专业团队服务</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 最新公告 -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <h3 class="mb-4">最新公告</h3>
                <div class="list-group">
                    <a href="/notice/detail/1" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">平台升级维护公告</h6>
                            <small class="text-muted">2024-08-01</small>
                        </div>
                        <p class="mb-1">为了给用户提供更好的服务体验，平台将于...</p>
                        <small class="text-danger">重要</small>
                    </a>
                    <a href="/notice/detail/2" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">新币种上线通知</h6>
                            <small class="text-muted">2024-07-30</small>
                        </div>
                        <p class="mb-1">平台即将上线新的数字货币交易对...</p>
                    </a>
                    <a href="/notice/detail/3" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">交易手续费调整公告</h6>
                            <small class="text-muted">2024-07-28</small>
                        </div>
                        <p class="mb-1">为了提升平台竞争力，现对部分交易对手续费进行调整...</p>
                    </a>
                </div>
                <div class="text-center mt-3">
                    <a href="/notice" class="btn btn-outline-primary">查看更多公告</a>
                </div>
            </div>
            <div class="col-lg-4">
                <h3 class="mb-4">快速入门</h3>
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">新手指南</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <a href="/help/register" class="text-decoration-none">
                                    <i class="fas fa-user-plus me-2 text-primary"></i>如何注册账户
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="/help/deposit" class="text-decoration-none">
                                    <i class="fas fa-wallet me-2 text-primary"></i>如何充值
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="/help/trade" class="text-decoration-none">
                                    <i class="fas fa-chart-line me-2 text-primary"></i>如何交易
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="/help/withdraw" class="text-decoration-none">
                                    <i class="fas fa-money-bill-wave me-2 text-primary"></i>如何提现
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{/block}

{block name="js"}
<script>
// 市场数据实时更新
function updateMarketData() {
    $.get('/api/market-data', function(data) {
        // 更新市场数据显示
        console.log('Market data updated:', data);
    });
}

// 每30秒更新一次市场数据
setInterval(updateMarketData, 30000);

// 页面加载完成后初始化
$(document).ready(function() {
    updateMarketData();
});
</script>
{/block}
