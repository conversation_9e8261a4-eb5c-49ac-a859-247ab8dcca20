<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\common\service\SocialTradingService;
use think\Request;
use think\Response;

/**
 * 社交交易API控制器
 */
class Social
{
    protected $socialService;

    public function __construct()
    {
        $this->socialService = new SocialTradingService();
    }

    /**
     * 创建交易策略
     */
    public function createStrategy(Request $request): Response
    {
        $userId = $request->user_id ?? 0;
        $data = $request->post();

        $result = $this->socialService->createTradingStrategy($userId, $data);
        
        return json($result);
    }

    /**
     * 发布策略
     */
    public function publishStrategy(Request $request): Response
    {
        $strategyId = $request->param('strategy_id');
        $userId = $request->user_id ?? 0;

        $result = $this->socialService->publishStrategy($strategyId, $userId);
        
        return json($result);
    }

    /**
     * 订阅策略
     */
    public function subscribeStrategy(Request $request): Response
    {
        $userId = $request->user_id ?? 0;
        $strategyId = $request->param('strategy_id');
        $subscriptionData = $request->post();

        $result = $this->socialService->subscribeStrategy($userId, $strategyId, $subscriptionData);
        
        return json($result);
    }

    /**
     * 跟单交易
     */
    public function copyTrade(Request $request): Response
    {
        $originalOrderId = $request->param('original_order_id');
        $tradeData = $request->post();

        $result = $this->socialService->copyTrade($originalOrderId, $tradeData);
        
        return json($result);
    }

    /**
     * 关注交易员
     */
    public function followTrader(Request $request): Response
    {
        $userId = $request->user_id ?? 0;
        $traderId = $request->param('trader_id');

        $result = $this->socialService->followTrader($userId, $traderId);
        
        return json($result);
    }

    /**
     * 发布社交帖子
     */
    public function createPost(Request $request): Response
    {
        $userId = $request->user_id ?? 0;
        $data = $request->post();

        $result = $this->socialService->createSocialPost($userId, $data);
        
        return json($result);
    }

    /**
     * 获取交易员排行榜
     */
    public function getTraderRanking(Request $request): Response
    {
        $filters = $request->get();

        $result = $this->socialService->getTraderRanking($filters);
        
        return json($result);
    }

    /**
     * 获取策略列表
     */
    public function getStrategies(Request $request): Response
    {
        $filters = $request->get();

        $result = $this->socialService->getStrategies($filters);
        
        return json($result);
    }

    /**
     * 获取社交动态
     */
    public function getSocialFeed(Request $request): Response
    {
        $userId = $request->user_id ?? 0;
        $filters = $request->get();

        $result = $this->socialService->getSocialFeed($userId, $filters);
        
        return json($result);
    }
}
