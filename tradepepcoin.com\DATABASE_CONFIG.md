# GVD数据库配置说明

## 📋 数据库信息位置

数据库配置信息存储在以下文件中：

### 1. 主配置文件：`.env`
```bash
# 数据库配置
DATABASE_TYPE=mysql
DATABASE_HOSTNAME=127.0.0.1
DATABASE_DATABASE=crypto_exchange
DATABASE_USERNAME=crypto_exchange
DATABASE_PASSWORD=AKFLe62CsmkGPW75
DATABASE_PORT=3306
DATABASE_PREFIX=ce_
DATABASE_CHARSET=utf8mb4
```

### 2. 框架配置文件：`config/database.php`
```php
'mysql' => [
    'type'            => env('DATABASE_TYPE', 'mysql'),
    'hostname'        => env('DATABASE_HOSTNAME', '127.0.0.1'),
    'database'        => env('DATABASE_DATABASE', 'crypto_exchange'),
    'username'        => env('DATABASE_USERNAME', 'crypto_exchange'),
    'password'        => env('DATABASE_PASSWORD', 'AKFLe62CsmkGPW75'),
    'hostport'        => env('DATABASE_PORT', '3306'),
    'charset'         => env('DATABASE_CHARSET', 'utf8mb4'),
    'prefix'          => env('DATABASE_PREFIX', 'ce_'),
]
```

## 🔧 如何修改数据库配置

### 方法1：修改.env文件（推荐）
```bash
# 编辑.env文件
nano .env

# 修改以下配置项
DATABASE_HOSTNAME=你的数据库主机
DATABASE_DATABASE=你的数据库名
DATABASE_USERNAME=你的数据库用户名
DATABASE_PASSWORD=你的数据库密码
DATABASE_PORT=你的数据库端口
```

### 方法2：直接修改config/database.php
```php
// 不推荐直接修改，建议使用.env文件
'hostname' => '你的数据库主机',
'database' => '你的数据库名',
'username' => '你的数据库用户名',
'password' => '你的数据库密码',
```

## 🗄️ 数据库结构

### 核心表结构
- `ce_users` - 用户表
- `ce_contract_orders` - 合约订单表
- `ce_deposit_records` - 充值记录表
- `ce_withdraw_records` - 提现记录表
- `ce_user_assets` - 用户资产表
- `ce_trading_pairs` - 交易对表
- `ce_deposit_addresses` - 充值地址表
- `ce_financial_records` - 财务记录表

### 新增表结构
- `ce_contract_config` - 合约配置表
- `ce_system_config` - 系统配置表
- `ce_system_images` - 系统图片表
- `ce_customer_messages` - 客服消息表
- `ce_admins` - 管理员表
- `ce_agents` - 代理表

## 🚀 部署步骤

### 1. 检查数据库配置
```bash
php check_database_config.php
```

### 2. 创建数据库（如果不存在）
```sql
CREATE DATABASE crypto_exchange CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. 创建数据库用户（如果不存在）
```sql
CREATE USER 'crypto_exchange'@'localhost' IDENTIFIED BY 'AKFLe62CsmkGPW75';
GRANT ALL PRIVILEGES ON crypto_exchange.* TO 'crypto_exchange'@'localhost';
FLUSH PRIVILEGES;
```

### 4. 导入数据库结构
```bash
mysql -u crypto_exchange -p crypto_exchange < database_structure.sql
```

### 5. 验证配置
```bash
php check_database_config.php
```

## ⚠️ 注意事项

1. **安全性**：
   - 生产环境请修改默认密码
   - 使用强密码
   - 限制数据库用户权限

2. **备份**：
   - 定期备份数据库
   - 备份.env配置文件

3. **权限**：
   - 确保.env文件权限为600
   - 数据库用户只授予必要权限

4. **字符集**：
   - 使用utf8mb4字符集
   - 支持emoji和特殊字符

## 🔍 故障排除

### 连接失败
1. 检查数据库服务是否启动
2. 验证主机地址和端口
3. 确认用户名和密码正确
4. 检查防火墙设置

### 表不存在
1. 执行database_structure.sql
2. 检查表前缀配置
3. 验证数据库权限

### 字符集问题
1. 确保数据库字符集为utf8mb4
2. 检查连接字符集配置
3. 验证表字符集设置

## 📞 技术支持

如果遇到数据库配置问题，请：
1. 运行检查脚本：`php check_database_config.php`
2. 查看错误日志：`runtime/log/`
3. 检查数据库连接日志

---

**当前配置**：
- 数据库名：crypto_exchange
- 用户名：crypto_exchange
- 表前缀：ce_
- 字符集：utf8mb4
