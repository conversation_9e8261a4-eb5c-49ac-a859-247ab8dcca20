<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>充值 - 数字货币交易平台</title>
    <style>
        body { font-family: Arial, sans-serif; background: #1a1a1a; color: #fff; margin: 0; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .back-link { display: inline-block; margin-bottom: 20px; color: #007bff; text-decoration: none; }
        .deposit-methods { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; }
        .method-card { background: #2d2d2d; padding: 20px; border-radius: 10px; border: 1px solid #333; text-align: center; }
        .method-title { font-size: 20px; margin-bottom: 15px; color: #007bff; }
        .qr-code { width: 150px; height: 150px; background: #fff; margin: 15px auto; border-radius: 5px; }
        .address { background: #1a1a1a; padding: 10px; border-radius: 5px; word-break: break-all; font-family: monospace; }
        .btn { padding: 10px 20px; background: #007bff; color: #fff; border: none; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="container">
        <a href="/" class="back-link">← 返回首页</a>
        <h1>💰 充值中心</h1>
        <div class="deposit-methods">
            <div class="method-card">
                <div class="method-title">USDT (TRC20)</div>
                <div class="qr-code"></div>
                <div class="address">TXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXx</div>
                <button class="btn" onclick="copyAddress(this)">复制地址</button>
            </div>
            <div class="method-card">
                <div class="method-title">BTC</div>
                <div class="qr-code"></div>
                <div class="address">**********************************</div>
                <button class="btn" onclick="copyAddress(this)">复制地址</button>
            </div>
        </div>
    </div>
    <script>
        function copyAddress(btn) {
            const address = btn.previousElementSibling.textContent;
            navigator.clipboard.writeText(address);
            btn.textContent = '已复制';
            setTimeout(() => btn.textContent = '复制地址', 2000);
        }
    </script>
</body>
</html>
