<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\IdoProject;
use app\common\model\IdoOrder;
use app\common\model\UserAsset;
use app\common\model\FinancialRecord;
use app\common\model\Commission;
use app\common\model\User;
use think\facade\Db;

/**
 * IDO服务类
 */
class IdoService
{
    /**
     * 创建认购订单
     */
    public function createOrder(int $userId, array $data): array
    {
        // 获取项目信息
        $project = IdoProject::find($data['project_id']);
        if (!$project || $project->status != 1) {
            return ['code' => 0, 'msg' => '项目不存在或已下架'];
        }

        // 检查项目状态
        if (!$project->can_purchase) {
            return ['code' => 0, 'msg' => '项目不在认购期内'];
        }

        // 验证认购数量
        $validation = $project->validatePurchaseAmount($data['amount']);
        if (!$validation['valid']) {
            return ['code' => 0, 'msg' => $validation['msg']];
        }

        // 检查用户是否已经认购过
        $existingOrder = IdoOrder::where('user_id', $userId)
                                ->where('project_id', $data['project_id'])
                                ->where('status', 1)
                                ->find();

        if ($existingOrder) {
            // 检查是否超过最大认购限制
            $totalAmount = $existingOrder->amount + $data['amount'];
            if ($project->max_purchase > 0 && $totalAmount > $project->max_purchase) {
                return ['code' => 0, 'msg' => '超过最大认购限制'];
            }
        }

        // 计算支付金额
        $totalPayment = $project->calculatePayment($data['amount']);

        // 检查用户支付币种余额
        $paymentAsset = UserAsset::getOrCreate($userId, $project->payment_coin);
        if ($paymentAsset->available < $totalPayment) {
            return ['code' => 0, 'msg' => $project->payment_coin . '余额不足'];
        }

        // 开启事务
        Db::startTrans();
        try {
            // 扣除用户资产
            $paymentAsset->subAvailable($totalPayment);

            // 创建订单
            $order = new IdoOrder();
            $order->order_id = $this->generateOrderId();
            $order->user_id = $userId;
            $order->project_id = $data['project_id'];
            $order->amount = $data['amount'];
            $order->price = $project->price;
            $order->total_payment = $totalPayment;
            $order->payment_coin = $project->payment_coin;
            $order->released_amount = 0;
            $order->status = 1; // 已支付
            $order->save();

            // 记录财务流水
            FinancialRecord::create([
                'user_id' => $userId,
                'coin_symbol' => $project->payment_coin,
                'type' => 'ido',
                'amount' => -$totalPayment,
                'balance_before' => $paymentAsset->available + $totalPayment,
                'balance_after' => $paymentAsset->available,
                'related_id' => $order->order_id,
                'description' => "认购 {$project->name}"
            ]);

            // 处理推荐佣金
            $this->processCommissions($userId, $project, $totalPayment);

            Db::commit();

            return ['code' => 1, 'msg' => '认购成功', 'data' => ['order_id' => $order->order_id]];
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 0, 'msg' => '认购失败：' . $e->getMessage()];
        }
    }

    /**
     * 处理推荐佣金
     */
    private function processCommissions(int $userId, IdoProject $project, float $amount): void
    {
        $user = User::find($userId);
        if (!$user) {
            return;
        }

        // 一级推荐人
        if ($user->inviter_level1 && $project->commission_level1 > 0) {
            $this->createCommission(
                $user->inviter_level1,
                $userId,
                'ido',
                1,
                $project->commission_level1,
                $amount * $project->commission_level1,
                $project->payment_coin,
                $project->name
            );
        }

        // 二级推荐人
        if ($user->inviter_level2 && $project->commission_level2 > 0) {
            $this->createCommission(
                $user->inviter_level2,
                $userId,
                'ido',
                2,
                $project->commission_level2,
                $amount * $project->commission_level2,
                $project->payment_coin,
                $project->name
            );
        }

        // 三级推荐人
        if ($user->inviter_level3 && $project->commission_level3 > 0) {
            $this->createCommission(
                $user->inviter_level3,
                $userId,
                'ido',
                3,
                $project->commission_level3,
                $amount * $project->commission_level3,
                $project->payment_coin,
                $project->name
            );
        }
    }

    /**
     * 创建佣金记录
     */
    private function createCommission(int $userId, int $fromUserId, string $type, int $level, float $rate, float $amount, string $coinSymbol, string $description): void
    {
        // 创建佣金记录
        Commission::create([
            'user_id' => $userId,
            'from_user_id' => $fromUserId,
            'type' => $type,
            'level' => $level,
            'rate' => $rate,
            'amount' => $amount,
            'coin_symbol' => $coinSymbol,
            'related_id' => '',
            'status' => 1 // 已发放
        ]);

        // 增加用户资产
        $asset = UserAsset::getOrCreate($userId, $coinSymbol);
        $asset->addAvailable($amount);

        // 记录财务流水
        FinancialRecord::create([
            'user_id' => $userId,
            'coin_symbol' => $coinSymbol,
            'type' => 'commission',
            'amount' => $amount,
            'balance_before' => $asset->available - $amount,
            'balance_after' => $asset->available,
            'related_id' => '',
            'description' => "推荐佣金 {$description}"
        ]);
    }

    /**
     * 释放代币
     */
    public function releaseTokens(int $userId, string $orderId): array
    {
        $order = IdoOrder::where('order_id', $orderId)
                        ->where('user_id', $userId)
                        ->find();

        if (!$order) {
            return ['code' => 0, 'msg' => '订单不存在'];
        }

        if ($order->status != 1) {
            return ['code' => 0, 'msg' => '订单状态不允许释放'];
        }

        $releasableAmount = $order->releasable_amount;
        if ($releasableAmount <= 0) {
            return ['code' => 0, 'msg' => '暂无可释放数量'];
        }

        // 开启事务
        Db::startTrans();
        try {
            // 更新订单释放数量
            $order->release($releasableAmount);

            // 获取项目信息
            $project = $order->project;
            if (!$project) {
                throw new \Exception('项目不存在');
            }

            // 增加用户代币资产
            $tokenAsset = UserAsset::getOrCreate($userId, $project->symbol);
            $tokenAsset->addAvailable($releasableAmount);

            // 记录财务流水
            FinancialRecord::create([
                'user_id' => $userId,
                'coin_symbol' => $project->symbol,
                'type' => 'ido',
                'amount' => $releasableAmount,
                'balance_before' => $tokenAsset->available - $releasableAmount,
                'balance_after' => $tokenAsset->available,
                'related_id' => $order->order_id,
                'description' => "代币释放 {$project->name}"
            ]);

            Db::commit();

            return ['code' => 1, 'msg' => '释放成功', 'data' => [
                'released_amount' => $releasableAmount,
                'total_released' => $order->released_amount
            ]];
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 0, 'msg' => '释放失败：' . $e->getMessage()];
        }
    }

    /**
     * 批量释放到期代币
     */
    public function batchReleaseTokens(): array
    {
        // 获取所有需要释放的订单
        $orders = IdoOrder::where('status', 1)
                         ->where('released_amount', '<', 'amount')
                         ->select();

        $released = 0;
        $failed = 0;

        foreach ($orders as $order) {
            $releasableAmount = $order->releasable_amount;
            if ($releasableAmount > 0) {
                $result = $this->releaseTokens($order->user_id, $order->order_id);
                if ($result['code']) {
                    $released++;
                } else {
                    $failed++;
                }
            }
        }

        return [
            'code' => 1,
            'msg' => "批量释放完成，成功：{$released}，失败：{$failed}",
            'data' => [
                'released' => $released,
                'failed' => $failed
            ]
        ];
    }

    /**
     * 获取项目统计
     */
    public function getProjectStats(int $projectId): array
    {
        return IdoOrder::getProjectStats($projectId);
    }

    /**
     * 获取用户统计
     */
    public function getUserStats(int $userId): array
    {
        return IdoOrder::getUserStats($userId);
    }

    /**
     * 获取释放记录
     */
    public function getReleaseRecords(int $userId, string $orderId): array
    {
        $order = IdoOrder::where('order_id', $orderId)
                        ->where('user_id', $userId)
                        ->find();

        if (!$order) {
            return [];
        }

        return $order->getReleaseRecords();
    }

    /**
     * 生成订单ID
     */
    private function generateOrderId(): string
    {
        return 'IDO' . date('YmdHis') . mt_rand(1000, 9999);
    }
}
