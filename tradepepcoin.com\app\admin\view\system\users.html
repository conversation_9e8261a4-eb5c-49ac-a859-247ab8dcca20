<!DOCTYPE html>
<html lang="zh-CN" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 管理端</title>
    <link rel="stylesheet" href="/static/css/modern-theme.css">
    <link rel="stylesheet" href="/static/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <div class="admin-sidebar">
            <div class="sidebar-header">
                <h2>系统管理</h2>
                <p>用户管理</p>
            </div>
            
            <nav class="admin-nav">
                <a href="/admin/system/index" class="nav-item">
                    <span class="nav-icon">📊</span>
                    <span class="nav-text">系统首页</span>
                </a>
                <a href="/admin/system/users" class="nav-item active">
                    <span class="nav-icon">👥</span>
                    <span class="nav-text">用户管理</span>
                </a>
                <a href="/admin/system/deposits" class="nav-item">
                    <span class="nav-icon">💳</span>
                    <span class="nav-text">充值管理</span>
                </a>
                <a href="/admin/system/withdrawals" class="nav-item">
                    <span class="nav-icon">💸</span>
                    <span class="nav-text">提现管理</span>
                </a>
                <a href="/admin/system/financialRecords" class="nav-item">
                    <span class="nav-icon">📄</span>
                    <span class="nav-text">财务记录</span>
                </a>
            </nav>
        </div>

        <!-- 主要内容 -->
        <div class="admin-main">
            <!-- 页面头部 -->
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1>{$title}</h1>
                        <p class="text-secondary">管理系统用户账户</p>
                    </div>
                    <div class="header-actions">
                        <div class="btn-group">
                            <a href="/admin/system/users?user_type=1" class="btn btn-{$user_type == 1 ? 'primary' : 'outline'}">
                                正式用户
                            </a>
                            <a href="/admin/system/users?user_type=2" class="btn btn-{$user_type == 2 ? 'primary' : 'outline'}">
                                测试用户
                            </a>
                        </div>
                        {if $user_type == 2}
                        <button class="btn btn-primary" onclick="showCreateModal()">
                            <span>➕</span> 创建测试用户
                        </button>
                        {/if}
                    </div>
                </div>
            </div>

            <!-- 搜索筛选 -->
            <div class="card mb-3">
                <div class="card-body">
                    <form method="GET" class="search-form">
                        <input type="hidden" name="user_type" value="{$user_type}">
                        <div class="row">
                            <div class="col-3">
                                <input type="text" class="form-control" name="username" value="{$username}" placeholder="搜索用户名">
                            </div>
                            <div class="col-3">
                                <select class="form-control" name="status">
                                    <option value="">全部状态</option>
                                    <option value="1" {$status == '1' ? 'selected' : ''}>正常</option>
                                    <option value="0" {$status == '0' ? 'selected' : ''}>禁用</option>
                                </select>
                            </div>
                            <div class="col-3">
                                <button type="submit" class="btn btn-primary">搜索</button>
                                <a href="/admin/system/users?user_type={$user_type}" class="btn btn-outline">重置</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 用户列表 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        {$user_type == 1 ? '正式用户列表' : '测试用户列表'}
                        <span class="badge badge-primary">{$users->total()}</span>
                    </h3>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>用户名</th>
                                    <th>邮箱</th>
                                    <th>用户类型</th>
                                    <th>USDT余额</th>
                                    <th>注册时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {volist name="users" id="user"}
                                <tr>
                                    <td>{$user.id}</td>
                                    <td>
                                        {if $user.user_type == 2}
                                        <span class="badge badge-warning">TEST</span>
                                        {/if}
                                        {$user.username}
                                        {if $user.is_agent}
                                        <span class="badge badge-info">代理</span>
                                        {/if}
                                    </td>
                                    <td>{$user.email}</td>
                                    <td>
                                        <span class="badge badge-{$user.user_type == 1 ? 'success' : 'warning'}">
                                            {$user.user_type == 1 ? '正式用户' : '测试用户'}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="balance-amount" data-user-id="{$user.id}">
                                            加载中...
                                        </span>
                                    </td>
                                    <td>{$user.created_at}</td>
                                    <td>
                                        <span class="badge badge-{$user.status == 1 ? 'success' : 'danger'}">
                                            {$user.status == 1 ? '正常' : '禁用'}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline" onclick="showBalanceModal({$user.id}, '{$user.username}', {$user.user_type})">
                                                调整余额
                                            </button>
                                            {if $user.user_type == 2}
                                            <button class="btn btn-sm btn-danger" onclick="deleteTestUser({$user.id}, '{$user.username}')">
                                                删除
                                            </button>
                                            {/if}
                                        </div>
                                    </td>
                                </tr>
                                {/volist}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    {$users->render()}
                </div>
            </div>
        </div>
    </div>

    <!-- 创建测试用户模态框 -->
    <div class="modal" id="createModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>创建测试用户</h3>
                <button class="modal-close" onclick="hideCreateModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="createForm">
                    <div class="form-group">
                        <label class="form-label">用户名 *</label>
                        <input type="text" class="form-control" name="username" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">密码 *</label>
                        <input type="password" class="form-control" name="password" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">邮箱</label>
                        <input type="email" class="form-control" name="email">
                    </div>
                    <div class="form-group">
                        <label class="form-label">所属代理商</label>
                        <select class="form-control" name="agent_id">
                            <option value="0">无代理商</option>
                            <!-- 这里可以动态加载代理商列表 -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">初始余额 (USDT)</label>
                        <input type="number" class="form-control" name="initial_balance" value="1000" min="0" step="0.01">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="hideCreateModal()">取消</button>
                <button class="btn btn-primary" onclick="createTestUser()">创建</button>
            </div>
        </div>
    </div>

    <!-- 调整余额模态框 -->
    <div class="modal" id="balanceModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>调整用户余额</h3>
                <button class="modal-close" onclick="hideBalanceModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="balanceForm">
                    <input type="hidden" name="user_id" id="balanceUserId">
                    <div class="form-group">
                        <label class="form-label">用户名</label>
                        <input type="text" class="form-control" id="balanceUsername" readonly>
                    </div>
                    <div class="form-group">
                        <label class="form-label">用户类型</label>
                        <input type="text" class="form-control" id="balanceUserType" readonly>
                    </div>
                    <div class="form-group">
                        <label class="form-label">币种</label>
                        <select class="form-control" name="coin_symbol">
                            <option value="USDT">USDT</option>
                            <option value="BTC">BTC</option>
                            <option value="ETH">ETH</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">调整金额</label>
                        <input type="number" class="form-control" name="amount" step="0.00000001" required>
                        <small class="form-text">正数为增加，负数为减少</small>
                    </div>
                    <div class="form-group">
                        <label class="form-label">备注</label>
                        <input type="text" class="form-control" name="remark" value="管理员调整用户余额">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="hideBalanceModal()">取消</button>
                <button class="btn btn-primary" onclick="adjustBalance()">确认调整</button>
            </div>
        </div>
    </div>

    <script src="/static/js/admin.js"></script>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initTheme();
            loadUserBalances();
        });

        // 显示创建模态框
        function showCreateModal() {
            document.getElementById('createModal').style.display = 'flex';
        }

        // 隐藏创建模态框
        function hideCreateModal() {
            document.getElementById('createModal').style.display = 'none';
            document.getElementById('createForm').reset();
        }

        // 创建测试用户
        function createTestUser() {
            const form = document.getElementById('createForm');
            const formData = new FormData(form);

            fetch('/admin/system/createTestUser', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    showNotification('测试用户创建成功', 'success');
                    hideCreateModal();
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification(data.msg || '创建失败', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('网络错误，请重试', 'error');
            });
        }

        // 显示余额调整模态框
        function showBalanceModal(userId, username, userType) {
            document.getElementById('balanceUserId').value = userId;
            document.getElementById('balanceUsername').value = username;
            document.getElementById('balanceUserType').value = userType == 1 ? '正式用户' : '测试用户';
            document.getElementById('balanceModal').style.display = 'flex';
        }

        // 隐藏余额调整模态框
        function hideBalanceModal() {
            document.getElementById('balanceModal').style.display = 'none';
            document.getElementById('balanceForm').reset();
        }

        // 调整余额
        function adjustBalance() {
            const form = document.getElementById('balanceForm');
            const formData = new FormData(form);

            fetch('/admin/system/adjustUserBalance', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    showNotification('余额调整成功', 'success');
                    hideBalanceModal();
                    loadUserBalances();
                } else {
                    showNotification(data.msg || '调整失败', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('网络错误，请重试', 'error');
            });
        }

        // 删除测试用户
        function deleteTestUser(userId, username) {
            if (!confirm(`确定要删除测试用户 "${username}" 吗？此操作不可恢复！`)) {
                return;
            }

            const formData = new FormData();
            formData.append('user_id', userId);

            fetch('/admin/system/deleteTestUser', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    showNotification('测试用户删除成功', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification(data.msg || '删除失败', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('网络错误，请重试', 'error');
            });
        }

        // 加载用户余额
        function loadUserBalances() {
            const balanceElements = document.querySelectorAll('.balance-amount');
            
            balanceElements.forEach(element => {
                const userId = element.getAttribute('data-user-id');
                
                fetch(`/user/assets/api?user_id=${userId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1 && data.data.USDT) {
                        const balance = parseFloat(data.data.USDT.available || 0);
                        element.textContent = balance.toFixed(2) + ' USDT';
                    } else {
                        element.textContent = '0.00 USDT';
                    }
                })
                .catch(error => {
                    element.textContent = '加载失败';
                });
            });
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <span>${message}</span>
                <button onclick="this.parentElement.remove()">×</button>
            `;
            
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 16px;
                border-radius: 4px;
                color: white;
                z-index: 10000;
                display: flex;
                align-items: center;
                gap: 8px;
                background: ${type === 'success' ? '#0ECB81' : type === 'error' ? '#F6465D' : '#1890FF'};
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 3000);
        }

        // 主题切换
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
        }

        // 初始化主题
        function initTheme() {
            const savedTheme = localStorage.getItem('theme') || 'dark';
            document.documentElement.setAttribute('data-theme', savedTheme);
        }
    </script>
</body>
</html>
