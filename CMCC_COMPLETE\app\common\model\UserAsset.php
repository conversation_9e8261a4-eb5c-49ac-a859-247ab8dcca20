<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 用户资产模型
 */
class UserAsset extends Model
{
    protected $name = 'gvd_user_assets';
    
    protected $type = [
        'available' => 'float',
        'frozen' => 'float',
        'total' => 'float'
    ];

    /**
     * 初始化用户资产
     */
    public static function initUserAssets(int $userId): void
    {
        $defaultCoins = [
            'USDT' => ['name' => 'Tether USD', 'decimals' => 6],
            'BTC' => ['name' => 'Bitcoin', 'decimals' => 8],
            'ETH' => ['name' => 'Ethereum', 'decimals' => 18],
            'LTC' => ['name' => 'Litecoin', 'decimals' => 8],
            'EOS' => ['name' => 'EOS', 'decimals' => 4],
            'XRP' => ['name' => 'Ripple', 'decimals' => 6]
        ];

        foreach ($defaultCoins as $symbol => $info) {
            $exists = self::where('user_id', $userId)
                         ->where('coin_symbol', $symbol)
                         ->find();
            
            if (!$exists) {
                self::create([
                    'user_id' => $userId,
                    'coin_symbol' => $symbol,
                    'coin_name' => $info['name'],
                    'available' => 0,
                    'frozen' => 0,
                    'total' => 0,
                    'decimals' => $info['decimals'],
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }
        }
    }

    /**
     * 获取用户资产列表
     */
    public static function getUserAssets(int $userId): array
    {
        return self::where('user_id', $userId)
                  ->order('coin_symbol', 'asc')
                  ->select()
                  ->toArray();
    }

    /**
     * 获取用户指定币种资产
     */
    public static function getUserAsset(int $userId, string $coinSymbol): ?UserAsset
    {
        return self::where('user_id', $userId)
                  ->where('coin_symbol', $coinSymbol)
                  ->find();
    }

    /**
     * 增加资产
     */
    public function addBalance(float $amount, string $type = 'available'): bool
    {
        try {
            if ($type === 'available') {
                $this->available += $amount;
            } elseif ($type === 'frozen') {
                $this->frozen += $amount;
            }
            
            $this->total = $this->available + $this->frozen;
            $this->updated_at = date('Y-m-d H:i:s');
            
            return $this->save();
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 减少资产
     */
    public function subBalance(float $amount, string $type = 'available'): bool
    {
        try {
            if ($type === 'available') {
                if ($this->available < $amount) {
                    return false;
                }
                $this->available -= $amount;
            } elseif ($type === 'frozen') {
                if ($this->frozen < $amount) {
                    return false;
                }
                $this->frozen -= $amount;
            }
            
            $this->total = $this->available + $this->frozen;
            $this->updated_at = date('Y-m-d H:i:s');
            
            return $this->save();
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 冻结资产
     */
    public function freezeBalance(float $amount): bool
    {
        try {
            if ($this->available < $amount) {
                return false;
            }
            
            $this->available -= $amount;
            $this->frozen += $amount;
            $this->updated_at = date('Y-m-d H:i:s');
            
            return $this->save();
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 解冻资产
     */
    public function unfreezeBalance(float $amount): bool
    {
        try {
            if ($this->frozen < $amount) {
                return false;
            }
            
            $this->frozen -= $amount;
            $this->available += $amount;
            $this->updated_at = date('Y-m-d H:i:s');
            
            return $this->save();
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 转账（内部转账）
     */
    public static function transfer(int $fromUserId, int $toUserId, string $coinSymbol, float $amount): array
    {
        try {
            \think\facade\Db::startTrans();

            $fromAsset = self::getUserAsset($fromUserId, $coinSymbol);
            $toAsset = self::getUserAsset($toUserId, $coinSymbol);

            if (!$fromAsset || !$toAsset) {
                throw new \Exception('用户资产不存在');
            }

            if ($fromAsset->available < $amount) {
                throw new \Exception('余额不足');
            }

            // 扣除发送方资产
            $fromAsset->subBalance($amount, 'available');
            
            // 增加接收方资产
            $toAsset->addBalance($amount, 'available');

            \think\facade\Db::commit();

            return ['code' => 1, 'msg' => '转账成功'];

        } catch (\Exception $e) {
            \think\facade\Db::rollback();
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 获取用户资产总值（以USDT计算）
     */
    public static function getUserTotalValue(int $userId): float
    {
        $assets = self::getUserAssets($userId);
        $totalValue = 0;

        foreach ($assets as $asset) {
            if ($asset['coin_symbol'] === 'USDT') {
                $totalValue += $asset['total'];
            } else {
                // 这里应该获取实时汇率，暂时使用固定汇率
                $rate = self::getCoinRate($asset['coin_symbol']);
                $totalValue += $asset['total'] * $rate;
            }
        }

        return $totalValue;
    }

    /**
     * 获取币种汇率（相对USDT）
     */
    private static function getCoinRate(string $coinSymbol): float
    {
        // 这里应该从交易对获取实时价格
        $rates = [
            'BTC' => 45000,
            'ETH' => 3000,
            'LTC' => 150,
            'EOS' => 5,
            'XRP' => 0.6
        ];

        return $rates[$coinSymbol] ?? 1;
    }

    /**
     * 格式化金额
     */
    public function formatAmount(float $amount): string
    {
        return number_format($amount, $this->decimals, '.', '');
    }

    /**
     * 获取资产信息（用于前端显示）
     */
    public function getAssetInfo(): array
    {
        $rate = self::getCoinRate($this->coin_symbol);
        $usdtValue = $this->total * $rate;

        return [
            'coin_symbol' => $this->coin_symbol,
            'coin_name' => $this->coin_name,
            'available' => $this->formatAmount($this->available),
            'frozen' => $this->formatAmount($this->frozen),
            'total' => $this->formatAmount($this->total),
            'usdt_value' => number_format($usdtValue, 2, '.', ''),
            'rate' => $rate,
            'decimals' => $this->decimals
        ];
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 获取资产变动记录
     */
    public static function getAssetRecords(int $userId, string $coinSymbol = '', int $page = 1, int $limit = 20): array
    {
        // 这里应该从资产变动记录表获取数据
        // 暂时返回空数组
        return [
            'data' => [],
            'total' => 0,
            'page' => $page,
            'limit' => $limit
        ];
    }

    /**
     * 检查资产是否足够
     */
    public static function checkBalance(int $userId, string $coinSymbol, float $amount, string $type = 'available'): bool
    {
        $asset = self::getUserAsset($userId, $coinSymbol);
        if (!$asset) {
            return false;
        }

        if ($type === 'available') {
            return $asset->available >= $amount;
        } elseif ($type === 'frozen') {
            return $asset->frozen >= $amount;
        } elseif ($type === 'total') {
            return $asset->total >= $amount;
        }

        return false;
    }

    /**
     * 批量更新用户资产
     */
    public static function batchUpdateAssets(array $updates): bool
    {
        try {
            \think\facade\Db::startTrans();

            foreach ($updates as $update) {
                $asset = self::getUserAsset($update['user_id'], $update['coin_symbol']);
                if (!$asset) {
                    continue;
                }

                if (isset($update['available'])) {
                    $asset->available = $update['available'];
                }
                if (isset($update['frozen'])) {
                    $asset->frozen = $update['frozen'];
                }
                
                $asset->total = $asset->available + $asset->frozen;
                $asset->updated_at = date('Y-m-d H:i:s');
                $asset->save();
            }

            \think\facade\Db::commit();
            return true;

        } catch (\Exception $e) {
            \think\facade\Db::rollback();
            return false;
        }
    }
}
