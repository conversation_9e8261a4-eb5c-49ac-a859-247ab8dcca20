<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\BaseController;
use think\facade\View;
use think\facade\Session;
use think\facade\Request;
use think\facade\Validate;
use app\common\service\UserService;
use app\common\service\StatisticsService;
use app\common\service\AssetService;
use app\common\model\User as UserModel;
use app\common\model\Admin;

/**
 * 管理端用户控制器
 */
class User extends BaseController
{
    protected $userService;
    protected $statisticsService;

    public function initialize()
    {
        parent::initialize();
        
        // 检查管理员登录状态
        if (!Session::has('admin_id')) {
            $this->redirect('/admin/login');
        }
        
        $this->userService = new UserService();
        $this->statisticsService = new StatisticsService();
    }

    /**
     * 用户列表
     */
    public function index()
    {
        $userType = Request::get('user_type', '');
        $keyword = Request::get('keyword', '');
        $page = Request::get('page', 1);
        $limit = 20;
        
        // 构建查询条件
        $where = [];
        if ($userType !== '') {
            $where['user_type'] = $userType;
        }
        if ($keyword) {
            $where[] = ['username|email', 'like', "%{$keyword}%"];
        }
        
        // 获取用户列表
        $users = UserModel::where($where)
                         ->order('id desc')
                         ->paginate([
                             'list_rows' => $limit,
                             'page' => $page
                         ]);
        
        // 统计数据（分别统计正式用户和测试用户）
        $stats = [
            'total_formal' => UserModel::where('user_type', UserModel::USER_TYPE_FORMAL)->count(),
            'total_test' => UserModel::where('user_type', UserModel::USER_TYPE_TEST)->count(),
            'today_register_formal' => UserModel::where('user_type', UserModel::USER_TYPE_FORMAL)
                                              ->whereTime('created_at', 'today')
                                              ->count(),
            'today_register_test' => UserModel::where('user_type', UserModel::USER_TYPE_TEST)
                                            ->whereTime('created_at', 'today')
                                            ->count()
        ];
        
        if (Request::isAjax()) {
            return json([
                'code' => 1,
                'data' => $users->items(),
                'total' => $users->total(),
                'stats' => $stats
            ]);
        }
        
        View::assign([
            'users' => $users,
            'stats' => $stats,
            'user_type' => $userType,
            'keyword' => $keyword,
            'title' => '用户管理 - GVD管理后台'
        ]);
        
        return View::fetch('admin/user/index');
    }

    /**
     * 添加测试用户
     */
    public function add()
    {
        if (Request::isPost()) {
            return $this->doAdd();
        }
        
        View::assign([
            'title' => '添加测试用户 - GVD管理后台'
        ]);
        
        return View::fetch('admin/user/add');
    }

    /**
     * 执行添加用户
     */
    private function doAdd()
    {
        $data = Request::post();
        $adminId = Session::get('admin_id');
        
        // 验证数据
        $validate = Validate::rule([
            'username' => 'require|alphaDash|length:3,20|unique:users',
            'email' => 'require|email|unique:users',
            'password' => 'require|min:6'
        ])->message([
            'username.require' => '请输入用户名',
            'username.unique' => '用户名已存在',
            'email.require' => '请输入邮箱',
            'email.unique' => '邮箱已被注册',
            'password.require' => '请输入密码'
        ]);

        if (!$validate->check($data)) {
            return json(['code' => 0, 'msg' => $validate->getError()]);
        }
        
        // 创建测试用户
        $result = $this->userService->createTestUser($data, $adminId);
        
        return json($result);
    }

    /**
     * 编辑用户
     */
    public function edit()
    {
        $id = Request::get('id');
        
        if (Request::isPost()) {
            return $this->doEdit($id);
        }
        
        $user = UserModel::find($id);
        if (!$user) {
            $this->error('用户不存在');
        }
        
        View::assign([
            'user' => $user,
            'title' => '编辑用户 - GVD管理后台'
        ]);
        
        return View::fetch('admin/user/edit');
    }

    /**
     * 执行编辑用户
     */
    private function doEdit($id)
    {
        $data = Request::post();
        $adminId = Session::get('admin_id');
        
        $user = UserModel::find($id);
        if (!$user) {
            return json(['code' => 0, 'msg' => '用户不存在']);
        }
        
        // 验证数据
        $validate = Validate::rule([
            'username' => "require|alphaDash|length:3,20|unique:users,username,{$id}",
            'email' => "require|email|unique:users,email,{$id}"
        ])->message([
            'username.require' => '请输入用户名',
            'username.unique' => '用户名已存在',
            'email.require' => '请输入邮箱',
            'email.unique' => '邮箱已被注册'
        ]);

        if (!$validate->check($data)) {
            return json(['code' => 0, 'msg' => $validate->getError()]);
        }
        
        try {
            // 更新用户信息
            $updateData = [
                'username' => $data['username'],
                'email' => $data['email'],
                'status' => $data['status'],
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            // 如果有密码，则更新密码
            if (!empty($data['password'])) {
                $updateData['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
            }
            
            $user->save($updateData);
            
            // 记录操作日志
            \think\facade\Db::name('admin_logs')->insert([
                'admin_id' => $adminId,
                'action' => 'edit_user',
                'content' => "编辑用户 {$user->username}",
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            return json(['code' => 1, 'msg' => '用户信息更新成功']);
            
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 修改用户类型
     */
    public function changeUserType()
    {
        $userId = Request::post('user_id');
        $userType = Request::post('user_type');
        $adminId = Session::get('admin_id');
        
        if (!$userId || !in_array($userType, [UserService::USER_TYPE_FORMAL, UserService::USER_TYPE_TEST])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }
        
        $result = $this->userService->changeUserType($userId, $userType, $adminId);
        
        return json($result);
    }

    /**
     * 禁用/启用用户
     */
    public function toggleStatus()
    {
        $id = Request::post('id');
        $adminId = Session::get('admin_id');
        
        $user = UserModel::find($id);
        if (!$user) {
            return json(['code' => 0, 'msg' => '用户不存在']);
        }
        
        try {
            $newStatus = $user->status == 1 ? 0 : 1;
            $user->status = $newStatus;
            $user->save();
            
            // 记录操作日志
            \think\facade\Db::name('admin_logs')->insert([
                'admin_id' => $adminId,
                'action' => 'toggle_user_status',
                'content' => ($newStatus ? '启用' : '禁用') . "用户 {$user->username}",
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            return json(['code' => 1, 'msg' => '状态更新成功']);
            
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '操作失败：' . $e->getMessage()]);
        }
    }

    /**
     * 删除用户
     */
    public function delete()
    {
        $id = Request::post('id');
        $adminId = Session::get('admin_id');
        
        $user = UserModel::find($id);
        if (!$user) {
            return json(['code' => 0, 'msg' => '用户不存在']);
        }

        // 只能删除测试用户
        if ($user->user_type == UserModel::USER_TYPE_FORMAL) {
            return json(['code' => 0, 'msg' => '不能删除正式用户']);
        }
        
        try {
            $user->delete();
            
            // 记录操作日志
            \think\facade\Db::name('admin_logs')->insert([
                'admin_id' => $adminId,
                'action' => 'delete_user',
                'content' => "删除测试用户 {$user->username}",
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            return json(['code' => 1, 'msg' => '删除成功']);
            
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 用户详情
     */
    public function detail()
    {
        $id = Request::get('id');
        
        $user = UserModel::find($id);
        if (!$user) {
            $this->error('用户不存在');
        }
        
        // 获取用户资产
        $assets = \app\common\model\UserAsset::where('user_id', $id)->select();

        // 获取用户订单统计
        $orderStats = [
            'total_orders' => \app\common\model\Order::where('user_id', $id)->count(),
            'total_volume' => \app\common\model\Order::where('user_id', $id)->where('status', 2)->sum('total'),
            'total_contracts' => \app\common\model\ContractOrder::where('user_id', $id)->count(),
            'contract_volume' => \app\common\model\ContractOrder::where('user_id', $id)->sum('amount')
        ];
        
        // 获取登录日志
        $loginLogs = \think\facade\Db::name('user_login_log')
                                   ->where('user_id', $id)
                                   ->order('created_at desc')
                                   ->limit(10)
                                   ->select();
        
        View::assign([
            'user' => $user,
            'assets' => $assets,
            'order_stats' => $orderStats,
            'login_logs' => $loginLogs,
            'title' => '用户详情 - GVD管理后台'
        ]);
        
        return View::fetch('admin/user/detail');
    }

    /**
     * 用户统计
     */
    public function statistics()
    {
        $period = Request::get('period', 'month');
        
        // 获取用户统计（只统计正式用户）
        $userStats = $this->statisticsService->getUserStats($period);
        
        // 获取平台总览
        $overview = $this->statisticsService->getPlatformOverview();
        
        if (Request::isAjax()) {
            return json([
                'code' => 1,
                'data' => [
                    'user_stats' => $userStats,
                    'overview' => $overview
                ]
            ]);
        }
        
        View::assign([
            'user_stats' => $userStats,
            'overview' => $overview,
            'period' => $period,
            'title' => '用户统计 - GVD管理后台'
        ]);
        
        return View::fetch('admin/user/statistics');
    }
}
