<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\BaseController;
use app\common\service\SpotTradingService;
use app\common\model\TradingPair;
use app\common\model\Trade;
use think\facade\Request;
use think\facade\Validate;

/**
 * 现货交易API控制器
 */
class SpotTrading extends BaseController
{
    protected $spotTradingService;

    public function initialize()
    {
        parent::initialize();
        $this->spotTradingService = new SpotTradingService();
    }

    /**
     * 创建订单
     */
    public function createOrder()
    {
        if (!Request::isPost()) {
            return $this->error('请求方式错误');
        }

        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $data = Request::post();
        $data['user_id'] = $userId;

        $validate = Validate::rule([
            'symbol' => 'require|alphaNum',
            'type' => 'require|in:1,2',
            'order_type' => 'require|in:1,2',
            'amount' => 'require|float|gt:0',
            'price' => 'requireIf:order_type,1|float|gt:0'
        ])->message([
            'symbol.require' => '交易对不能为空',
            'type.require' => '订单类型不能为空',
            'type.in' => '订单类型无效',
            'order_type.require' => '订单方式不能为空',
            'order_type.in' => '订单方式无效',
            'amount.require' => '交易数量不能为空',
            'amount.gt' => '交易数量必须大于0',
            'price.requireIf' => '限价单必须设置价格',
            'price.gt' => '价格必须大于0'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        $result = $this->spotTradingService->createOrder($data);
        return json($result);
    }

    /**
     * 取消订单
     */
    public function cancelOrder()
    {
        if (!Request::isPost()) {
            return $this->error('请求方式错误');
        }

        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $orderId = Request::post('order_id');
        if (empty($orderId)) {
            return $this->error('订单ID不能为空');
        }

        $result = $this->spotTradingService->cancelOrder($userId, $orderId);
        return json($result);
    }

    /**
     * 获取用户订单
     */
    public function getUserOrders()
    {
        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $symbol = Request::get('symbol', '');
        $status = Request::get('status', 'active');
        $page = Request::get('page/d', 1);
        $limit = Request::get('limit/d', 20);

        $result = $this->spotTradingService->getUserOrders($userId, $symbol, $status, $page, $limit);
        return json($result);
    }

    /**
     * 获取订单簿
     */
    public function getOrderBook()
    {
        $symbol = Request::get('symbol', 'BTCUSDT');
        $depth = Request::get('depth/d', 20);

        $result = $this->spotTradingService->getOrderBook($symbol, $depth);
        return json($result);
    }

    /**
     * 获取最新成交记录
     */
    public function getLatestTrades()
    {
        $symbol = Request::get('symbol', 'BTCUSDT');
        $limit = Request::get('limit/d', 50);

        $trades = Trade::getTradesForDisplay($symbol, $limit);
        return $this->success('获取成功', $trades);
    }

    /**
     * 获取交易对列表
     */
    public function getSymbols()
    {
        $symbols = TradingPair::getTradingPairsList();
        return $this->success('获取成功', $symbols);
    }

    /**
     * 获取交易对配置
     */
    public function getSymbolInfo()
    {
        $symbol = Request::get('symbol', 'BTCUSDT');

        $tradingPair = TradingPair::getBySymbol($symbol);
        if (!$tradingPair) {
            return $this->error('交易对不存在');
        }

        $config = $tradingPair->getConfig();
        $stats = $tradingPair->get24hStats();

        return $this->success('获取成功', [
            'config' => $config,
            'stats' => $stats
        ]);
    }

    /**
     * 获取用户成交记录
     */
    public function getUserTrades()
    {
        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $symbol = Request::get('symbol', '');
        $page = Request::get('page/d', 1);
        $limit = Request::get('limit/d', 20);

        $result = Trade::getUserTrades($userId, $symbol, $page, $limit);
        return $this->success('获取成功', $result);
    }

    /**
     * 获取交易统计
     */
    public function getTradeStats()
    {
        $symbol = Request::get('symbol', 'BTCUSDT');
        $stats = Trade::get24hTradeStats($symbol);
        return $this->success('获取成功', $stats);
    }

    /**
     * 获取当前用户ID
     */
    private function getUserId(): int
    {
        return Request::header('user-id', 0) ?: 1;
    }
}
