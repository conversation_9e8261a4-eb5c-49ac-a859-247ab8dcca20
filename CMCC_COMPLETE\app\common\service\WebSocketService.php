<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Log;
use think\facade\Cache;
use Workerman\Worker;
use Workerman\Connection\TcpConnection;

/**
 * WebSocket服务类
 * 提供实时数据推送功能
 */
class WebSocketService
{
    // 频道类型
    const CHANNEL_ORDERBOOK = 'orderbook';
    const CHANNEL_TRADES = 'trades';
    const CHANNEL_TICKER = 'ticker';
    const CHANNEL_KLINE = 'kline';
    const CHANNEL_USER_ORDERS = 'user_orders';
    const CHANNEL_USER_TRADES = 'user_trades';
    const CHANNEL_USER_ASSETS = 'user_assets';

    // 消息类型
    const MSG_TYPE_SUBSCRIBE = 'subscribe';
    const MSG_TYPE_UNSUBSCRIBE = 'unsubscribe';
    const MSG_TYPE_PING = 'ping';
    const MSG_TYPE_PONG = 'pong';

    private $worker;
    private $connections = [];
    private $subscriptions = [];
    private $redis;

    public function __construct()
    {
        $this->redis = Cache::store('redis')->handler();
    }

    /**
     * 启动WebSocket服务
     */
    public function start(string $host = '0.0.0.0', int $port = 8080): void
    {
        $this->worker = new Worker("websocket://{$host}:{$port}");
        $this->worker->count = 4; // 4个进程
        $this->worker->name = 'GVD-WebSocket';

        // 连接建立时的回调
        $this->worker->onConnect = [$this, 'onConnect'];
        
        // 收到消息时的回调
        $this->worker->onMessage = [$this, 'onMessage'];
        
        // 连接关闭时的回调
        $this->worker->onClose = [$this, 'onClose'];

        // 启动定时器
        $this->startTimers();

        Log::info("WebSocket服务启动在 {$host}:{$port}");
        Worker::runAll();
    }

    /**
     * 连接建立回调
     */
    public function onConnect(TcpConnection $connection): void
    {
        $connectionId = $connection->id;
        $this->connections[$connectionId] = $connection;
        
        Log::info("WebSocket连接建立: {$connectionId}");
        
        // 发送欢迎消息
        $this->sendMessage($connection, [
            'type' => 'welcome',
            'data' => [
                'connection_id' => $connectionId,
                'server_time' => time(),
                'supported_channels' => [
                    self::CHANNEL_ORDERBOOK,
                    self::CHANNEL_TRADES,
                    self::CHANNEL_TICKER,
                    self::CHANNEL_KLINE,
                    self::CHANNEL_USER_ORDERS,
                    self::CHANNEL_USER_TRADES,
                    self::CHANNEL_USER_ASSETS
                ]
            ]
        ]);
    }

    /**
     * 收到消息回调
     */
    public function onMessage(TcpConnection $connection, string $data): void
    {
        try {
            $message = json_decode($data, true);
            if (!$message) {
                $this->sendError($connection, 'Invalid JSON format');
                return;
            }

            $type = $message['type'] ?? '';
            
            switch ($type) {
                case self::MSG_TYPE_SUBSCRIBE:
                    $this->handleSubscribe($connection, $message);
                    break;
                    
                case self::MSG_TYPE_UNSUBSCRIBE:
                    $this->handleUnsubscribe($connection, $message);
                    break;
                    
                case self::MSG_TYPE_PING:
                    $this->handlePing($connection, $message);
                    break;
                    
                default:
                    $this->sendError($connection, 'Unknown message type');
            }

        } catch (\Exception $e) {
            Log::error('WebSocket消息处理异常：' . $e->getMessage());
            $this->sendError($connection, 'Message processing error');
        }
    }

    /**
     * 连接关闭回调
     */
    public function onClose(TcpConnection $connection): void
    {
        $connectionId = $connection->id;
        
        // 清理订阅
        $this->cleanupSubscriptions($connectionId);
        
        // 移除连接
        unset($this->connections[$connectionId]);
        
        Log::info("WebSocket连接关闭: {$connectionId}");
    }

    /**
     * 处理订阅请求
     */
    private function handleSubscribe(TcpConnection $connection, array $message): void
    {
        $channel = $message['channel'] ?? '';
        $symbol = $message['symbol'] ?? '';
        $token = $message['token'] ?? '';

        if (empty($channel)) {
            $this->sendError($connection, 'Channel is required');
            return;
        }

        // 验证频道
        if (!$this->isValidChannel($channel)) {
            $this->sendError($connection, 'Invalid channel');
            return;
        }

        // 用户频道需要认证
        if ($this->isUserChannel($channel)) {
            $userId = $this->authenticateToken($token);
            if (!$userId) {
                $this->sendError($connection, 'Authentication required');
                return;
            }
            $connection->userId = $userId;
        }

        // 公共频道需要交易对
        if ($this->isPublicChannel($channel) && empty($symbol)) {
            $this->sendError($connection, 'Symbol is required for public channels');
            return;
        }

        // 添加订阅
        $subscriptionKey = $this->getSubscriptionKey($channel, $symbol, $connection->userId ?? 0);
        $this->subscriptions[$subscriptionKey][$connection->id] = $connection;

        // 发送订阅确认
        $this->sendMessage($connection, [
            'type' => 'subscribed',
            'data' => [
                'channel' => $channel,
                'symbol' => $symbol,
                'subscription_key' => $subscriptionKey
            ]
        ]);

        // 发送初始数据
        $this->sendInitialData($connection, $channel, $symbol);

        Log::info("WebSocket订阅: {$connection->id} -> {$subscriptionKey}");
    }

    /**
     * 处理取消订阅
     */
    private function handleUnsubscribe(TcpConnection $connection, array $message): void
    {
        $channel = $message['channel'] ?? '';
        $symbol = $message['symbol'] ?? '';

        $subscriptionKey = $this->getSubscriptionKey($channel, $symbol, $connection->userId ?? 0);
        
        if (isset($this->subscriptions[$subscriptionKey][$connection->id])) {
            unset($this->subscriptions[$subscriptionKey][$connection->id]);
            
            // 如果没有订阅者了，清理订阅键
            if (empty($this->subscriptions[$subscriptionKey])) {
                unset($this->subscriptions[$subscriptionKey]);
            }
        }

        $this->sendMessage($connection, [
            'type' => 'unsubscribed',
            'data' => [
                'channel' => $channel,
                'symbol' => $symbol
            ]
        ]);

        Log::info("WebSocket取消订阅: {$connection->id} -> {$subscriptionKey}");
    }

    /**
     * 处理心跳
     */
    private function handlePing(TcpConnection $connection, array $message): void
    {
        $this->sendMessage($connection, [
            'type' => self::MSG_TYPE_PONG,
            'data' => [
                'timestamp' => time()
            ]
        ]);
    }

    /**
     * 广播订单簿更新
     */
    public function broadcastOrderBook(string $symbol, array $orderBook): void
    {
        $subscriptionKey = $this->getSubscriptionKey(self::CHANNEL_ORDERBOOK, $symbol);
        $this->broadcast($subscriptionKey, [
            'type' => 'orderbook_update',
            'data' => [
                'symbol' => $symbol,
                'bids' => $orderBook['bids'],
                'asks' => $orderBook['asks'],
                'timestamp' => microtime(true)
            ]
        ]);
    }

    /**
     * 广播交易更新
     */
    public function broadcastTrade(string $symbol, array $trade): void
    {
        $subscriptionKey = $this->getSubscriptionKey(self::CHANNEL_TRADES, $symbol);
        $this->broadcast($subscriptionKey, [
            'type' => 'trade_update',
            'data' => [
                'symbol' => $symbol,
                'trade' => $trade,
                'timestamp' => microtime(true)
            ]
        ]);
    }

    /**
     * 广播行情更新
     */
    public function broadcastTicker(string $symbol, array $ticker): void
    {
        $subscriptionKey = $this->getSubscriptionKey(self::CHANNEL_TICKER, $symbol);
        $this->broadcast($subscriptionKey, [
            'type' => 'ticker_update',
            'data' => [
                'symbol' => $symbol,
                'ticker' => $ticker,
                'timestamp' => microtime(true)
            ]
        ]);
    }

    /**
     * 广播K线更新
     */
    public function broadcastKline(string $symbol, string $interval, array $kline): void
    {
        $subscriptionKey = $this->getSubscriptionKey(self::CHANNEL_KLINE, $symbol . ':' . $interval);
        $this->broadcast($subscriptionKey, [
            'type' => 'kline_update',
            'data' => [
                'symbol' => $symbol,
                'interval' => $interval,
                'kline' => $kline,
                'timestamp' => microtime(true)
            ]
        ]);
    }

    /**
     * 广播用户订单更新
     */
    public function broadcastUserOrder(int $userId, array $order): void
    {
        $subscriptionKey = $this->getSubscriptionKey(self::CHANNEL_USER_ORDERS, '', $userId);
        $this->broadcast($subscriptionKey, [
            'type' => 'user_order_update',
            'data' => [
                'order' => $order,
                'timestamp' => microtime(true)
            ]
        ]);
    }

    /**
     * 广播用户资产更新
     */
    public function broadcastUserAsset(int $userId, array $asset): void
    {
        $subscriptionKey = $this->getSubscriptionKey(self::CHANNEL_USER_ASSETS, '', $userId);
        $this->broadcast($subscriptionKey, [
            'type' => 'user_asset_update',
            'data' => [
                'asset' => $asset,
                'timestamp' => microtime(true)
            ]
        ]);
    }

    /**
     * 广播消息到订阅者
     */
    private function broadcast(string $subscriptionKey, array $message): void
    {
        if (!isset($this->subscriptions[$subscriptionKey])) {
            return;
        }

        $messageJson = json_encode($message);
        $deadConnections = [];

        foreach ($this->subscriptions[$subscriptionKey] as $connectionId => $connection) {
            try {
                $connection->send($messageJson);
            } catch (\Exception $e) {
                // 连接已断开，标记为死连接
                $deadConnections[] = $connectionId;
                Log::warning("WebSocket发送失败，连接已断开: {$connectionId}");
            }
        }

        // 清理死连接
        foreach ($deadConnections as $connectionId) {
            unset($this->subscriptions[$subscriptionKey][$connectionId]);
        }

        // 如果没有订阅者了，清理订阅键
        if (empty($this->subscriptions[$subscriptionKey])) {
            unset($this->subscriptions[$subscriptionKey]);
        }
    }

    /**
     * 发送消息给单个连接
     */
    private function sendMessage(TcpConnection $connection, array $message): void
    {
        try {
            $connection->send(json_encode($message));
        } catch (\Exception $e) {
            Log::error("WebSocket发送消息失败: {$e->getMessage()}");
        }
    }

    /**
     * 发送错误消息
     */
    private function sendError(TcpConnection $connection, string $error): void
    {
        $this->sendMessage($connection, [
            'type' => 'error',
            'data' => [
                'message' => $error,
                'timestamp' => time()
            ]
        ]);
    }

    /**
     * 发送初始数据
     */
    private function sendInitialData(TcpConnection $connection, string $channel, string $symbol): void
    {
        try {
            switch ($channel) {
                case self::CHANNEL_ORDERBOOK:
                    $orderBook = $this->getOrderBookFromCache($symbol);
                    if ($orderBook) {
                        $this->sendMessage($connection, [
                            'type' => 'orderbook_snapshot',
                            'data' => [
                                'symbol' => $symbol,
                                'bids' => $orderBook['bids'],
                                'asks' => $orderBook['asks'],
                                'timestamp' => microtime(true)
                            ]
                        ]);
                    }
                    break;

                case self::CHANNEL_TICKER:
                    $ticker = $this->getTickerFromCache($symbol);
                    if ($ticker) {
                        $this->sendMessage($connection, [
                            'type' => 'ticker_snapshot',
                            'data' => [
                                'symbol' => $symbol,
                                'ticker' => $ticker,
                                'timestamp' => microtime(true)
                            ]
                        ]);
                    }
                    break;

                case self::CHANNEL_TRADES:
                    $trades = $this->getRecentTradesFromCache($symbol);
                    if ($trades) {
                        $this->sendMessage($connection, [
                            'type' => 'trades_snapshot',
                            'data' => [
                                'symbol' => $symbol,
                                'trades' => $trades,
                                'timestamp' => microtime(true)
                            ]
                        ]);
                    }
                    break;
            }
        } catch (\Exception $e) {
            Log::error("发送初始数据失败: {$e->getMessage()}");
        }
    }

    /**
     * 启动定时器
     */
    private function startTimers(): void
    {
        // 心跳检测定时器
        $this->worker->onWorkerStart = function() {
            // 每30秒检查一次连接状态
            \Workerman\Lib\Timer::add(30, function() {
                $this->checkConnections();
            });

            // 每5秒推送行情更新
            \Workerman\Lib\Timer::add(5, function() {
                $this->pushMarketUpdates();
            });
        };
    }

    /**
     * 检查连接状态
     */
    private function checkConnections(): void
    {
        $deadConnections = [];
        
        foreach ($this->connections as $connectionId => $connection) {
            if ($connection->getStatus() !== TcpConnection::STATUS_ESTABLISHED) {
                $deadConnections[] = $connectionId;
            }
        }

        foreach ($deadConnections as $connectionId) {
            $this->cleanupSubscriptions($connectionId);
            unset($this->connections[$connectionId]);
        }

        if (!empty($deadConnections)) {
            Log::info("清理死连接: " . count($deadConnections) . "个");
        }
    }

    /**
     * 推送市场更新
     */
    private function pushMarketUpdates(): void
    {
        // 获取活跃的交易对
        $activeSymbols = $this->getActiveSymbols();
        
        foreach ($activeSymbols as $symbol) {
            // 推送行情更新
            $ticker = $this->getTickerFromCache($symbol);
            if ($ticker) {
                $this->broadcastTicker($symbol, $ticker);
            }
        }
    }

    /**
     * 清理订阅
     */
    private function cleanupSubscriptions(string $connectionId): void
    {
        foreach ($this->subscriptions as $subscriptionKey => $connections) {
            if (isset($connections[$connectionId])) {
                unset($this->subscriptions[$subscriptionKey][$connectionId]);
                
                if (empty($this->subscriptions[$subscriptionKey])) {
                    unset($this->subscriptions[$subscriptionKey]);
                }
            }
        }
    }

    /**
     * 获取订阅键
     */
    private function getSubscriptionKey(string $channel, string $symbol = '', int $userId = 0): string
    {
        if ($userId > 0) {
            return "{$channel}:user:{$userId}";
        } else {
            return "{$channel}:{$symbol}";
        }
    }

    /**
     * 验证频道
     */
    private function isValidChannel(string $channel): bool
    {
        return in_array($channel, [
            self::CHANNEL_ORDERBOOK,
            self::CHANNEL_TRADES,
            self::CHANNEL_TICKER,
            self::CHANNEL_KLINE,
            self::CHANNEL_USER_ORDERS,
            self::CHANNEL_USER_TRADES,
            self::CHANNEL_USER_ASSETS
        ]);
    }

    /**
     * 是否为用户频道
     */
    private function isUserChannel(string $channel): bool
    {
        return in_array($channel, [
            self::CHANNEL_USER_ORDERS,
            self::CHANNEL_USER_TRADES,
            self::CHANNEL_USER_ASSETS
        ]);
    }

    /**
     * 是否为公共频道
     */
    private function isPublicChannel(string $channel): bool
    {
        return in_array($channel, [
            self::CHANNEL_ORDERBOOK,
            self::CHANNEL_TRADES,
            self::CHANNEL_TICKER,
            self::CHANNEL_KLINE
        ]);
    }

    /**
     * 认证Token
     */
    private function authenticateToken(string $token): ?int
    {
        try {
            // 这里应该验证JWT token
            // 简化实现，从缓存获取用户ID
            $userId = Cache::get("ws_token:{$token}");
            return $userId ? (int)$userId : null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 从缓存获取订单簿
     */
    private function getOrderBookFromCache(string $symbol): ?array
    {
        $cacheKey = "orderbook:{$symbol}";
        $data = $this->redis->get($cacheKey);
        return $data ? json_decode($data, true) : null;
    }

    /**
     * 从缓存获取行情
     */
    private function getTickerFromCache(string $symbol): ?array
    {
        $cacheKey = "ticker:{$symbol}";
        $data = $this->redis->get($cacheKey);
        return $data ? json_decode($data, true) : null;
    }

    /**
     * 从缓存获取最近交易
     */
    private function getRecentTradesFromCache(string $symbol): ?array
    {
        $cacheKey = "recent_trades:{$symbol}";
        $data = $this->redis->lrange($cacheKey, 0, 49); // 最近50笔交易
        return array_map(function($item) {
            return json_decode($item, true);
        }, $data);
    }

    /**
     * 获取活跃交易对
     */
    private function getActiveSymbols(): array
    {
        $cacheKey = "active_symbols";
        $symbols = $this->redis->smembers($cacheKey);
        return $symbols ?: ['BTCUSDT', 'ETHUSDT']; // 默认交易对
    }
}
