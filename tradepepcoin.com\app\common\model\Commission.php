<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 佣金记录模型
 */
class Commission extends Model
{
    protected $table = 'ce_commissions';
    
    // 设置字段信息
    protected $schema = [
        'id'           => 'int',
        'user_id'      => 'int',
        'from_user_id' => 'int',
        'type'         => 'string',
        'level'        => 'int',
        'rate'         => 'decimal',
        'amount'       => 'decimal',
        'coin_symbol'  => 'string',
        'related_id'   => 'string',
        'status'       => 'int',
        'created_at'   => 'datetime',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = 'datetime';
    protected $updateTime = false;

    // 类型转换
    protected $type = [
        'user_id'      => 'integer',
        'from_user_id' => 'integer',
        'level'        => 'integer',
        'rate'         => 'decimal:6',
        'amount'       => 'decimal:8',
        'status'       => 'integer',
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联来源用户
     */
    public function fromUser()
    {
        return $this->belongsTo(User::class, 'from_user_id');
    }

    /**
     * 关联币种
     */
    public function coin()
    {
        return $this->belongsTo(Coin::class, 'coin_symbol', 'symbol');
    }

    /**
     * 佣金类型文本
     */
    public function getTypeTextAttr($value, $data)
    {
        $types = [
            'trade' => '交易佣金',
            'contract' => '合约佣金',
            'ido' => '认购佣金'
        ];
        return $types[$data['type']] ?? '未知';
    }

    /**
     * 级别文本
     */
    public function getLevelTextAttr($value, $data)
    {
        return $data['level'] . '级';
    }

    /**
     * 状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [0 => '待发放', 1 => '已发放'];
        return $status[$data['status']] ?? '未知';
    }

    /**
     * 状态颜色
     */
    public function getStatusColorAttr($value, $data)
    {
        $colors = [0 => 'warning', 1 => 'success'];
        return $colors[$data['status']] ?? 'secondary';
    }

    /**
     * 格式化比例
     */
    public function getRateFormatAttr($value, $data)
    {
        return number_format($data['rate'] * 100, 2) . '%';
    }

    /**
     * 格式化金额
     */
    public function getAmountFormatAttr($value, $data)
    {
        return number_format($data['amount'], 8, '.', '');
    }

    /**
     * 获取用户佣金统计
     */
    public static function getUserStats(int $userId, string $type = ''): array
    {
        $where = ['user_id' => $userId, 'status' => 1];
        if (!empty($type)) {
            $where['type'] = $type;
        }

        // 总佣金金额
        $totalAmount = self::where($where)->sum('amount');

        // 总佣金笔数
        $totalCount = self::where($where)->count();

        // 今日佣金
        $todayAmount = self::where($where)
                          ->whereTime('created_at', 'today')
                          ->sum('amount');

        // 今日佣金笔数
        $todayCount = self::where($where)
                         ->whereTime('created_at', 'today')
                         ->count();

        // 本月佣金
        $monthAmount = self::where($where)
                          ->whereTime('created_at', 'month')
                          ->sum('amount');

        // 各级别佣金统计
        $level1Amount = self::where($where)->where('level', 1)->sum('amount');
        $level2Amount = self::where($where)->where('level', 2)->sum('amount');
        $level3Amount = self::where($where)->where('level', 3)->sum('amount');

        // 各类型佣金统计
        $tradeAmount = self::where('user_id', $userId)->where('status', 1)->where('type', 'trade')->sum('amount');
        $contractAmount = self::where('user_id', $userId)->where('status', 1)->where('type', 'contract')->sum('amount');
        $idoAmount = self::where('user_id', $userId)->where('status', 1)->where('type', 'ido')->sum('amount');

        return [
            'total_amount' => $totalAmount,
            'total_count' => $totalCount,
            'today_amount' => $todayAmount,
            'today_count' => $todayCount,
            'month_amount' => $monthAmount,
            'level1_amount' => $level1Amount,
            'level2_amount' => $level2Amount,
            'level3_amount' => $level3Amount,
            'trade_amount' => $tradeAmount,
            'contract_amount' => $contractAmount,
            'ido_amount' => $idoAmount
        ];
    }

    /**
     * 获取用户下级佣金统计
     */
    public static function getDownlineStats(int $userId): array
    {
        // 一级下级佣金
        $level1Stats = self::where('from_user_id', 'in', function($query) use ($userId) {
            $query->table('ce_users')->where('inviter_level1', $userId)->field('id');
        })->where('user_id', $userId)->where('level', 1)->where('status', 1);

        // 二级下级佣金
        $level2Stats = self::where('from_user_id', 'in', function($query) use ($userId) {
            $query->table('ce_users')->where('inviter_level2', $userId)->field('id');
        })->where('user_id', $userId)->where('level', 2)->where('status', 1);

        // 三级下级佣金
        $level3Stats = self::where('from_user_id', 'in', function($query) use ($userId) {
            $query->table('ce_users')->where('inviter_level3', $userId)->field('id');
        })->where('user_id', $userId)->where('level', 3)->where('status', 1);

        return [
            'level1' => [
                'count' => $level1Stats->count(),
                'amount' => $level1Stats->sum('amount')
            ],
            'level2' => [
                'count' => $level2Stats->count(),
                'amount' => $level2Stats->sum('amount')
            ],
            'level3' => [
                'count' => $level3Stats->count(),
                'amount' => $level3Stats->sum('amount')
            ]
        ];
    }

    /**
     * 获取佣金排行榜
     */
    public static function getRanking(string $period = 'month', int $limit = 10): array
    {
        $where = ['status' => 1];

        // 时间范围
        switch ($period) {
            case 'today':
                $where[] = ['created_at', '>=', date('Y-m-d 00:00:00')];
                break;
            case 'week':
                $where[] = ['created_at', '>=', date('Y-m-d 00:00:00', strtotime('-7 days'))];
                break;
            case 'month':
                $where[] = ['created_at', '>=', date('Y-m-d 00:00:00', strtotime('-30 days'))];
                break;
        }

        $ranking = self::where($where)
                      ->field('user_id, SUM(amount) as total_amount, COUNT(*) as total_count')
                      ->group('user_id')
                      ->order('total_amount', 'desc')
                      ->limit($limit)
                      ->select()
                      ->toArray();

        // 补充用户信息
        foreach ($ranking as &$item) {
            $user = User::find($item['user_id']);
            $item['username'] = $user ? $user->username : '未知用户';
        }

        return $ranking;
    }

    /**
     * 获取佣金明细
     */
    public static function getCommissionDetails(int $userId, array $params = []): array
    {
        $where = ['user_id' => $userId];

        if (!empty($params['type'])) {
            $where['type'] = $params['type'];
        }

        if (!empty($params['level'])) {
            $where['level'] = $params['level'];
        }

        if (!empty($params['coin_symbol'])) {
            $where['coin_symbol'] = $params['coin_symbol'];
        }

        if (!empty($params['status'])) {
            $where['status'] = $params['status'];
        }

        if (!empty($params['start_time'])) {
            $where[] = ['created_at', '>=', $params['start_time']];
        }

        if (!empty($params['end_time'])) {
            $where[] = ['created_at', '<=', $params['end_time']];
        }

        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 20;

        return self::where($where)
                  ->with(['fromUser'])
                  ->order('created_at', 'desc')
                  ->paginate([
                      'list_rows' => $limit,
                      'page' => $page
                  ])
                  ->toArray();
    }
}
