<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\User;
use app\common\model\Order;
use app\common\model\Trade;
use app\common\model\TradingPair;
use app\common\model\UserAsset;
use app\common\model\SystemConfig;
use think\facade\Db;
use think\facade\Log;

/**
 * 交易服务类 - 兼容老系统交易逻辑
 */
class TradeService
{
    protected $assetService;
    
    public function __construct()
    {
        $this->assetService = new AssetService();
    }
    
    /**
     * 创建买入订单 - 兼容老系统逻辑
     */
    public function createBuyOrder(int $userId, array $orderData): array
    {
        try {
            // 验证用户
            $user = User::find($userId);
            if (!$user) {
                return ['code' => 0, 'msg' => '用户不存在'];
            }
            
            if (!$user->canTrade()) {
                return ['code' => 0, 'msg' => '您的账户暂时无法交易'];
            }
            
            // 验证交易对
            $tradingPair = TradingPair::where('symbol', $orderData['symbol'])->find();
            if (!$tradingPair || !$tradingPair->is_active) {
                return ['code' => 0, 'msg' => '交易对不存在或已停用'];
            }
            
            // 检查开市时间
            if (!$this->isMarketOpen()) {
                return ['code' => 0, 'msg' => '未开市'];
            }
            
            // 获取手续费率
            $feeRate = $this->getFeeRate($userId, $orderData['symbol']);
            
            $symbol = $orderData['symbol'];
            $price = floatval($orderData['price']);
            $amount = floatval($orderData['amount']);
            $orderType = intval($orderData['order_type']); // 1限价 2市价
            
            // 计算所需USDT
            if ($orderType == 1) {
                // 限价单
                $totalUsdt = $price * $amount;
            } else {
                // 市价单 - 使用当前市价
                $currentPrice = $this->getCurrentPrice($symbol);
                $totalUsdt = $currentPrice * $amount;
                $price = $currentPrice;
            }
            
            // 检查余额
            $usdtAsset = UserAsset::getOrCreate($userId, 'USDT');
            if ($usdtAsset->available < $totalUsdt) {
                return ['code' => 0, 'msg' => 'USDT余额不足'];
            }
            
            Db::startTrans();
            
            // 冻结USDT
            $freezeResult = $this->assetService->freezeAsset($userId, 'USDT', $totalUsdt, '买入委托冻结');
            if ($freezeResult['code'] != 1) {
                Db::rollback();
                return $freezeResult;
            }
            
            // 创建订单
            $orderId = $this->generateOrderId();
            $coinSymbol = str_replace('USDT', '', $symbol);
            
            $order = Order::create([
                'order_id' => $orderId,
                'user_id' => $userId,
                'username' => $user->username,
                'symbol' => $symbol,
                'coin' => $coinSymbol,
                'type' => 1, // 买入
                'order_type' => $orderType,
                'price' => $price,
                'amount' => $amount,
                'total' => $totalUsdt,
                'filled_amount' => 0,
                'filled_total' => 0,
                'avg_price' => 0,
                'fee' => 0,
                'fee_rate' => $feeRate,
                'fee_coin' => $coinSymbol,
                'status' => 1, // 委托中
            ]);
            
            // 如果是市价单，立即执行
            if ($orderType == 2) {
                $this->executeMarketOrder($order);
            }
            
            Db::commit();
            
            return ['code' => 1, 'msg' => '订单委托成功', 'data' => ['order_id' => $orderId]];
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('创建买入订单失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '订单创建失败'];
        }
    }
    
    /**
     * 创建卖出订单 - 兼容老系统逻辑
     */
    public function createSellOrder(int $userId, array $orderData): array
    {
        try {
            // 验证用户
            $user = User::find($userId);
            if (!$user) {
                return ['code' => 0, 'msg' => '用户不存在'];
            }
            
            if (!$user->canTrade()) {
                return ['code' => 0, 'msg' => '您的账户暂时无法交易'];
            }
            
            // 验证交易对
            $tradingPair = TradingPair::where('symbol', $orderData['symbol'])->find();
            if (!$tradingPair || !$tradingPair->is_active) {
                return ['code' => 0, 'msg' => '交易对不存在或已停用'];
            }
            
            // 检查开市时间
            if (!$this->isMarketOpen()) {
                return ['code' => 0, 'msg' => '未开市'];
            }
            
            // 获取手续费率
            $feeRate = $this->getFeeRate($userId, $orderData['symbol']);
            
            $symbol = $orderData['symbol'];
            $price = floatval($orderData['price']);
            $amount = floatval($orderData['amount']);
            $orderType = intval($orderData['order_type']); // 1限价 2市价
            
            $coinSymbol = str_replace('USDT', '', $symbol);
            
            // 检查币种余额
            $coinAsset = UserAsset::getOrCreate($userId, $coinSymbol);
            if ($coinAsset->available < $amount) {
                return ['code' => 0, 'msg' => $coinSymbol . '余额不足'];
            }
            
            Db::startTrans();
            
            // 冻结币种
            $freezeResult = $this->assetService->freezeAsset($userId, $coinSymbol, $amount, '卖出委托冻结');
            if ($freezeResult['code'] != 1) {
                Db::rollback();
                return $freezeResult;
            }
            
            // 如果是市价单，使用当前市价
            if ($orderType == 2) {
                $price = $this->getCurrentPrice($symbol);
            }
            
            $totalUsdt = $price * $amount;
            
            // 创建订单
            $orderId = $this->generateOrderId();
            
            $order = Order::create([
                'order_id' => $orderId,
                'user_id' => $userId,
                'username' => $user->username,
                'symbol' => $symbol,
                'coin' => $coinSymbol,
                'type' => 2, // 卖出
                'order_type' => $orderType,
                'price' => $price,
                'amount' => $amount,
                'total' => $totalUsdt,
                'filled_amount' => 0,
                'filled_total' => 0,
                'avg_price' => 0,
                'fee' => 0,
                'fee_rate' => $feeRate,
                'fee_coin' => 'USDT',
                'status' => 1, // 委托中
            ]);
            
            // 如果是市价单，立即执行
            if ($orderType == 2) {
                $this->executeMarketOrder($order);
            }
            
            Db::commit();
            
            return ['code' => 1, 'msg' => '订单委托成功', 'data' => ['order_id' => $orderId]];
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('创建卖出订单失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '订单创建失败'];
        }
    }
    
    /**
     * 撤销订单 - 兼容老系统逻辑
     */
    public function cancelOrder(int $userId, string $orderId): array
    {
        try {
            $order = Order::where('order_id', $orderId)
                          ->where('user_id', $userId)
                          ->find();
            
            if (!$order) {
                return ['code' => 0, 'msg' => '订单不存在'];
            }
            
            if ($order->status != 1) {
                return ['code' => 0, 'msg' => '订单状态不允许撤销'];
            }
            
            Db::startTrans();
            
            // 解冻资产
            if ($order->type == 1) {
                // 买入订单，解冻USDT
                $unfreezeAmount = $order->total - $order->filled_total;
                $this->assetService->unfreezeAsset($userId, 'USDT', $unfreezeAmount, '撤销买入订单');
            } else {
                // 卖出订单，解冻币种
                $unfreezeAmount = $order->amount - $order->filled_amount;
                $this->assetService->unfreezeAsset($userId, $order->coin, $unfreezeAmount, '撤销卖出订单');
            }
            
            // 更新订单状态
            $order->status = 3; // 已撤销
            $order->save();
            
            Db::commit();
            
            return ['code' => 1, 'msg' => '订单已撤销'];
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('撤销订单失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '撤销失败'];
        }
    }
    
    /**
     * 执行市价订单
     */
    private function executeMarketOrder(Order $order): bool
    {
        try {
            // 计算手续费
            if ($order->type == 1) {
                // 买入：手续费从获得的币种中扣除
                $fee = $order->amount * $order->fee_rate / 100;
                $actualAmount = $order->amount - $fee;
                
                // 扣除冻结的USDT
                $this->assetService->subFrozenAsset($order->user_id, 'USDT', $order->total, '市价买入成交');
                
                // 增加币种
                $this->assetService->addAsset($order->user_id, $order->coin, $actualAmount, '市价买入获得', 5);
                
            } else {
                // 卖出：手续费从获得的USDT中扣除
                $totalUsdt = $order->amount * $order->price;
                $fee = $totalUsdt * $order->fee_rate / 100;
                $actualUsdt = $totalUsdt - $fee;
                
                // 扣除冻结的币种
                $this->assetService->subFrozenAsset($order->user_id, $order->coin, $order->amount, '市价卖出成交');
                
                // 增加USDT
                $this->assetService->addAsset($order->user_id, 'USDT', $actualUsdt, '市价卖出获得', 6);
            }
            
            // 更新订单状态
            $order->filled_amount = $order->amount;
            $order->filled_total = $order->total;
            $order->avg_price = $order->price;
            $order->fee = $fee ?? 0;
            $order->status = 2; // 已成交
            $order->trade_time = date('Y-m-d H:i:s');
            $order->save();
            
            // 创建成交记录
            Trade::create([
                'trade_id' => $this->generateTradeId(),
                'order_id' => $order->order_id,
                'user_id' => $order->user_id,
                'symbol' => $order->symbol,
                'type' => $order->type == 1 ? 'buy' : 'sell',
                'price' => $order->price,
                'amount' => $order->amount,
                'total' => $order->total,
                'fee' => $order->fee,
                'fee_coin' => $order->fee_coin,
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            Log::error('执行市价订单失败：' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 检查是否开市
     */
    private function isMarketOpen(): bool
    {
        // 简化实现，实际项目中应该从配置表读取
        $currentTime = date('H:i');
        return $currentTime >= '09:00' && $currentTime <= '23:59';
    }
    
    /**
     * 获取手续费率
     */
    private function getFeeRate(int $userId, string $symbol): float
    {
        // 简化实现，实际项目中应该根据用户等级和交易对配置
        return 0.1; // 0.1%
    }
    
    /**
     * 获取当前价格
     */
    private function getCurrentPrice(string $symbol): float
    {
        // 简化实现，实际项目中应该从行情API获取
        $prices = [
            'BTCUSDT' => 45000,
            'ETHUSDT' => 2800,
            'BNBUSDT' => 300,
        ];
        
        return $prices[$symbol] ?? 1;
    }
    
    /**
     * 生成订单ID
     */
    private function generateOrderId(): string
    {
        return 'ORD' . date('YmdHis') . mt_rand(1000, 9999);
    }
    
    /**
     * 生成成交ID
     */
    private function generateTradeId(): string
    {
        return 'TRD' . date('YmdHis') . mt_rand(1000, 9999);
    }

    /**
     * 获取用户订单列表 - 兼容老系统
     */
    public function getUserOrders(int $userId, array $params = []): array
    {
        $where = ['user_id' => $userId];

        if (!empty($params['symbol'])) {
            $where['symbol'] = $params['symbol'];
        }

        if (!empty($params['type'])) {
            $where['type'] = $params['type'];
        }

        if (!empty($params['status'])) {
            $where['status'] = $params['status'];
        }

        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 20;

        $orders = Order::where($where)
                      ->order('created_at', 'desc')
                      ->paginate([
                          'list_rows' => $limit,
                          'page' => $page
                      ]);

        return [
            'code' => 1,
            'data' => $orders->items(),
            'total' => $orders->total(),
            'page' => $page,
            'limit' => $limit
        ];
    }

    /**
     * 获取用户交易统计 - 兼容老系统
     */
    public function getUserTradeStats(int $userId): array
    {
        // 总交易次数
        $totalTrades = Order::where('user_id', $userId)
                           ->where('status', 2)
                           ->count();

        // 总交易量
        $totalVolume = Order::where('user_id', $userId)
                           ->where('status', 2)
                           ->sum('filled_total');

        // 总手续费
        $totalFee = Order::where('user_id', $userId)
                        ->where('status', 2)
                        ->sum('fee');

        // 今日交易次数
        $todayTrades = Order::where('user_id', $userId)
                           ->where('status', 2)
                           ->whereTime('created_at', 'today')
                           ->count();

        // 今日交易量
        $todayVolume = Order::where('user_id', $userId)
                           ->where('status', 2)
                           ->whereTime('created_at', 'today')
                           ->sum('filled_total');

        // 本月交易次数
        $monthTrades = Order::where('user_id', $userId)
                           ->where('status', 2)
                           ->whereTime('created_at', 'month')
                           ->count();

        return [
            'total_trades' => $totalTrades,
            'total_volume' => $totalVolume,
            'total_fee' => $totalFee,
            'today_trades' => $todayTrades,
            'today_volume' => $todayVolume,
            'month_trades' => $monthTrades,
        ];
    }

    /**
     * 限价订单撮合 - 简化版本
     */
    public function matchLimitOrders(string $symbol): int
    {
        $matchedCount = 0;

        try {
            // 获取买入订单（按价格降序）
            $buyOrders = Order::where([
                'symbol' => $symbol,
                'type' => 1,
                'order_type' => 1,
                'status' => 1
            ])->order('price', 'desc')->limit(10)->select();

            // 获取卖出订单（按价格升序）
            $sellOrders = Order::where([
                'symbol' => $symbol,
                'type' => 2,
                'order_type' => 1,
                'status' => 1
            ])->order('price', 'asc')->limit(10)->select();

            foreach ($buyOrders as $buyOrder) {
                foreach ($sellOrders as $sellOrder) {
                    // 价格匹配
                    if ($buyOrder->price >= $sellOrder->price) {
                        $this->executeTrade($buyOrder, $sellOrder);
                        $matchedCount++;
                    }
                }
            }

        } catch (\Exception $e) {
            Log::error('订单撮合失败：' . $e->getMessage());
        }

        return $matchedCount;
    }

    /**
     * 执行交易撮合
     */
    private function executeTrade(Order $buyOrder, Order $sellOrder): bool
    {
        try {
            Db::startTrans();

            // 计算成交数量（取较小值）
            $tradeAmount = min(
                $buyOrder->amount - $buyOrder->filled_amount,
                $sellOrder->amount - $sellOrder->filled_amount
            );

            // 成交价格（取卖方价格）
            $tradePrice = $sellOrder->price;
            $tradeTotal = $tradeAmount * $tradePrice;

            // 计算手续费
            $buyFee = $tradeAmount * $buyOrder->fee_rate / 100;
            $sellFee = $tradeTotal * $sellOrder->fee_rate / 100;

            // 买方：扣除冻结USDT，增加币种
            $this->assetService->subFrozenAsset($buyOrder->user_id, 'USDT', $tradeTotal, '限价买入成交');
            $this->assetService->addAsset($buyOrder->user_id, $buyOrder->coin, $tradeAmount - $buyFee, '限价买入获得', 5);

            // 卖方：扣除冻结币种，增加USDT
            $this->assetService->subFrozenAsset($sellOrder->user_id, $sellOrder->coin, $tradeAmount, '限价卖出成交');
            $this->assetService->addAsset($sellOrder->user_id, 'USDT', $tradeTotal - $sellFee, '限价卖出获得', 6);

            // 更新买入订单
            $buyOrder->filled_amount += $tradeAmount;
            $buyOrder->filled_total += $tradeTotal;
            $buyOrder->fee += $buyFee;
            if ($buyOrder->filled_amount >= $buyOrder->amount) {
                $buyOrder->status = 2; // 完全成交
                $buyOrder->trade_time = date('Y-m-d H:i:s');
            } else {
                $buyOrder->status = 4; // 部分成交
            }
            $buyOrder->save();

            // 更新卖出订单
            $sellOrder->filled_amount += $tradeAmount;
            $sellOrder->filled_total += $tradeTotal;
            $sellOrder->fee += $sellFee;
            if ($sellOrder->filled_amount >= $sellOrder->amount) {
                $sellOrder->status = 2; // 完全成交
                $sellOrder->trade_time = date('Y-m-d H:i:s');
            } else {
                $sellOrder->status = 4; // 部分成交
            }
            $sellOrder->save();

            // 创建成交记录
            Trade::create([
                'trade_id' => $this->generateTradeId(),
                'order_id' => $buyOrder->order_id,
                'user_id' => $buyOrder->user_id,
                'symbol' => $buyOrder->symbol,
                'type' => 1,
                'price' => $tradePrice,
                'amount' => $tradeAmount,
                'total' => $tradeTotal,
                'fee' => $buyFee,
                'fee_coin' => $buyOrder->coin,
            ]);

            Trade::create([
                'trade_id' => $this->generateTradeId(),
                'order_id' => $sellOrder->order_id,
                'user_id' => $sellOrder->user_id,
                'symbol' => $sellOrder->symbol,
                'type' => 2,
                'price' => $tradePrice,
                'amount' => $tradeAmount,
                'total' => $tradeTotal,
                'fee' => $sellFee,
                'fee_coin' => 'USDT',
            ]);

            Db::commit();
            return true;

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('执行交易撮合失败：' . $e->getMessage());
            return false;
        }
    }
}
