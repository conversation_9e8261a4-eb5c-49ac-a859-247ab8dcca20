<?php
declare (strict_types = 1);

namespace app\index\controller;

use app\BaseController;
use app\common\model\User;
use app\common\service\UserService;
use app\common\service\VerificationService;
use think\facade\View;
use think\facade\Session;
use think\facade\Request;
use think\facade\Validate;
use think\captcha\facade\Captcha;

/**
 * 用户认证控制器
 */
class Auth extends BaseController
{
    protected $userService;
    protected $verificationService;

    public function initialize()
    {
        parent::initialize();
        $this->userService = new UserService();
        $this->verificationService = new VerificationService();
    }

    /**
     * 登录页面
     */
    public function login()
    {
        if (Request::isPost()) {
            return $this->doLogin();
        }

        // 如果已登录，跳转到首页
        if (Session::has('user_id')) {
            $this->redirect('/');
        }

        View::assign([
            'title' => '用户登录 - GVD数字货币交易平台'
        ]);

        return View::fetch('auth/login');
    }

    /**
     * 执行登录
     */
    private function doLogin()
    {
        $data = Request::post();

        // 验证数据
        $validate = Validate::rule([
            'username' => 'require',
            'password' => 'require|min:6',
            'captcha' => 'require|captcha'
        ])->message([
            'username.require' => '请输入用户名/邮箱/手机号',
            'password.require' => '请输入密码',
            'password.min' => '密码至少6位',
            'captcha.require' => '请输入验证码',
            'captcha.captcha' => '验证码错误'
        ]);

        if (!$validate->check($data)) {
            return json(['code' => 0, 'msg' => $validate->getError()]);
        }

        // 执行登录
        $result = $this->userService->login($data['username'], $data['password']);

        if ($result['code'] == 1) {
            // 登录成功，设置session
            Session::set('user_id', $result['data']['id']);
            Session::set('username', $result['data']['username']);
            Session::set('user_info', $result['data']);
        }

        return json($result);
    }

    /**
     * 注册页面
     */
    public function register()
    {
        if (Request::isPost()) {
            return $this->doRegister();
        }

        // 如果已登录，跳转到首页
        if (Session::has('user_id')) {
            $this->redirect('/');
        }

        View::assign([
            'title' => '用户注册 - GVD数字货币交易平台'
        ]);

        return View::fetch('auth/register');
    }

    /**
     * 执行注册
     */
    private function doRegister()
    {
        $data = Request::post();

        // 判断注册方式（邮箱或手机）
        $registerType = !empty($data['email']) ? 'email' : 'phone';

        if ($registerType === 'email') {
            // 邮箱注册验证
            $validate = Validate::rule([
                'username' => 'require|alphaDash|length:3,20|unique:ce_users',
                'email' => 'require|email|unique:ce_users',
                'password' => 'require|min:6',
                'email_code' => 'require|length:6'
            ])->message([
                'username.require' => '请输入用户名',
                'username.unique' => '用户名已存在',
                'email.require' => '请输入邮箱',
                'email.unique' => '邮箱已被注册',
                'password.require' => '请输入密码',
                'email_code.require' => '请输入验证码'
            ]);

            if (!$validate->check($data)) {
                return json(['code' => 0, 'msg' => $validate->getError()]);
            }

            // 验证邮箱验证码
            if (!$this->verificationService->verifyCode('email', $data['email'], $data['email_code'])) {
                return json(['code' => 0, 'msg' => '验证码错误或已过期']);
            }
        } else {
            // 手机注册验证
            $validate = Validate::rule([
                'username' => 'require|alphaDash|length:3,20|unique:ce_users',
                'phone' => 'require',
                'password' => 'require|min:6',
                'sms_code' => 'require|length:6'
            ])->message([
                'username.require' => '请输入用户名',
                'username.unique' => '用户名已存在',
                'phone.require' => '请输入手机号',
                'password.require' => '请输入密码',
                'sms_code.require' => '请输入验证码'
            ]);

            if (!$validate->check($data)) {
                return json(['code' => 0, 'msg' => $validate->getError()]);
            }

            // 组合完整手机号
            $fullPhone = ($data['country_code'] ?? '+86') . $data['phone'];
            $data['phone'] = $fullPhone;

            // 检查手机号是否已注册
            if (User::where('phone', $fullPhone)->find()) {
                return json(['code' => 0, 'msg' => '手机号已被注册']);
            }

            // 验证短信验证码
            if (!$this->verificationService->verifyCode('sms', $fullPhone, $data['sms_code'])) {
                return json(['code' => 0, 'msg' => '验证码错误或已过期']);
            }
        }

        // 设置用户类型为正式用户（前端注册）
        $data['user_type'] = User::USER_TYPE_FORMAL;

        // 执行注册
        $result = $this->userService->register($data);

        return json($result);
    }

    /**
     * 退出登录
     */
    public function logout()
    {
        Session::clear();
        $this->redirect('/');
    }

    /**
     * 忘记密码
     */
    public function forgotPassword()
    {
        if (Request::isPost()) {
            return $this->doForgotPassword();
        }

        View::assign([
            'title' => '忘记密码 - GVD数字货币交易平台'
        ]);

        return View::fetch('auth/forgot-password');
    }

    /**
     * 执行忘记密码
     */
    private function doForgotPassword()
    {
        $data = Request::post();

        // 验证数据
        $validate = Validate::rule([
            'email' => 'require|email',
            'captcha' => 'require|captcha'
        ])->message([
            'email.require' => '请输入邮箱',
            'email.email' => '邮箱格式错误',
            'captcha.require' => '请输入验证码',
            'captcha.captcha' => '验证码错误'
        ]);

        if (!$validate->check($data)) {
            return json(['code' => 0, 'msg' => $validate->getError()]);
        }

        // 发送重置密码邮件
        $result = $this->userService->sendResetPasswordEmail($data['email']);

        return json($result);
    }

    /**
     * 发送邮箱验证码
     */
    public function sendEmailCode()
    {
        if (!Request::isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $email = Request::post('email');

        if (!$email || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return json(['code' => 0, 'msg' => '邮箱格式错误']);
        }

        // 检查邮箱是否已注册
        if (User::where('email', $email)->find()) {
            return json(['code' => 0, 'msg' => '该邮箱已被注册']);
        }

        $result = $this->verificationService->sendEmailCode($email);

        return json($result);
    }

    /**
     * 发送短信验证码
     */
    public function sendSmsCode()
    {
        if (!Request::isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $phone = Request::post('phone');

        if (!$phone) {
            return json(['code' => 0, 'msg' => '请输入手机号码']);
        }

        // 检查手机号是否已注册
        if (User::where('phone', $phone)->find()) {
            return json(['code' => 0, 'msg' => '该手机号已被注册']);
        }

        // 发送验证码
        $result = $this->verificationService->sendSmsCode($phone);

        return json($result);
    }

    /**
     * 获取验证码
     */
    public function captcha()
    {
        return Captcha::create();
    }
}
