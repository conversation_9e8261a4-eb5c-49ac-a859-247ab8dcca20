/**
 * GVD交易平台 - 主要样式文件
 * 包含全局样式、组件样式、响应式设计
 */

/* ===== 全局样式 ===== */
:root {
    --primary-color: #667eea;
    --primary-dark: #5a6fd8;
    --secondary-color: #764ba2;
    --success-color: #2ecc71;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #3498db;
    --light-color: #f8f9fa;
    --dark-color: #2c3e50;
    --border-color: #e1e5e9;
    --text-color: #333;
    --text-muted: #666;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #f5f7fa;
}

/* ===== 通用组件 ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    text-align: center;
    transition: var(--transition);
    line-height: 1;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* ===== 表单组件 ===== */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 16px;
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control:disabled {
    background: var(--light-color);
    color: var(--text-muted);
}

/* ===== 卡片组件 ===== */
.card {
    background: white;
    border-radius: 10px;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.card-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background: var(--light-color);
}

.card-body {
    padding: 20px;
}

.card-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    background: var(--light-color);
}

/* ===== 表格组件 ===== */
.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.table th,
.table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background: var(--light-color);
    font-weight: 600;
    color: var(--text-color);
}

.table tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
}

/* ===== 状态标签 ===== */
.badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.badge-success {
    background: rgba(46, 204, 113, 0.2);
    color: var(--success-color);
}

.badge-danger {
    background: rgba(231, 76, 60, 0.2);
    color: var(--danger-color);
}

.badge-warning {
    background: rgba(243, 156, 18, 0.2);
    color: var(--warning-color);
}

.badge-info {
    background: rgba(52, 152, 219, 0.2);
    color: var(--info-color);
}

/* ===== 消息提示 ===== */
.alert {
    padding: 15px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
}

.alert-success {
    background: rgba(46, 204, 113, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(46, 204, 113, 0.3);
}

.alert-danger {
    background: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.alert-warning {
    background: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(243, 156, 18, 0.3);
}

.alert-info {
    background: rgba(52, 152, 219, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(52, 152, 219, 0.3);
}

/* ===== 加载动画 ===== */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* ===== 工具类 ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.text-muted { color: var(--text-muted); }
.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-danger { color: var(--danger-color); }
.text-warning { color: var(--warning-color); }

.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.align-items-center { align-items: center; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 14px;
    }
    
    .form-control {
        padding: 10px 12px;
        font-size: 14px;
    }
    
    .table th,
    .table td {
        padding: 8px 10px;
        font-size: 14px;
    }
    
    .card-body {
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }
    
    .btn {
        padding: 8px 16px;
        font-size: 13px;
    }
    
    .form-control {
        padding: 8px 10px;
        font-size: 13px;
    }
}
