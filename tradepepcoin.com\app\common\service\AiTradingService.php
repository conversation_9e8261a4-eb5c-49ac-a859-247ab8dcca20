<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\TradingPair;
use app\common\model\User;
use app\common\model\AiStrategy;
use app\common\model\MarketData;
use think\facade\Log;
use think\facade\Cache;

/**
 * AI智能交易服务
 */
class AiTradingService
{
    // AI策略类型
    const STRATEGY_TREND_FOLLOWING = 'trend_following';     // 趋势跟踪
    const STRATEGY_MEAN_REVERSION = 'mean_reversion';       // 均值回归
    const STRATEGY_MOMENTUM = 'momentum';                   // 动量策略
    const STRATEGY_ARBITRAGE = 'arbitrage';                 // 套利策略
    const STRATEGY_SENTIMENT = 'sentiment';                 // 情绪分析
    const STRATEGY_NEURAL_NETWORK = 'neural_network';       // 神经网络

    // 风险等级
    const RISK_CONSERVATIVE = 1;    // 保守型
    const RISK_MODERATE = 2;        // 稳健型
    const RISK_AGGRESSIVE = 3;      // 激进型

    /**
     * 创建AI交易策略
     */
    public function createAiStrategy(int $userId, array $data): array
    {
        try {
            // 验证用户风险承受能力
            $riskAssessment = $this->assessUserRiskTolerance($userId);
            if (!$riskAssessment['code']) {
                return $riskAssessment;
            }

            // 验证策略参数
            $validation = $this->validateStrategyParameters($data);
            if (!$validation['code']) {
                return $validation;
            }

            // 创建AI策略
            $strategyData = [
                'user_id' => $userId,
                'strategy_id' => $this->generateStrategyId(),
                'name' => $data['name'],
                'type' => $data['type'],
                'symbols' => $data['symbols'] ?? [],
                'risk_level' => $data['risk_level'],
                'investment_amount' => $data['investment_amount'],
                'parameters' => $this->optimizeStrategyParameters($data['type'], $data['parameters'] ?? []),
                'ml_model' => $this->selectOptimalModel($data['type']),
                'status' => AiStrategy::STATUS_TRAINING
            ];

            $strategy = AiStrategy::create($strategyData);

            if ($strategy) {
                // 启动模型训练
                $this->startModelTraining($strategy);

                return [
                    'code' => 1,
                    'msg' => 'AI策略创建成功',
                    'data' => [
                        'strategy_id' => $strategy->strategy_id,
                        'estimated_training_time' => $this->estimateTrainingTime($data['type'])
                    ]
                ];
            } else {
                return ['code' => 0, 'msg' => 'AI策略创建失败'];
            }
        } catch (\Exception $e) {
            Log::error('AI策略创建失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'AI策略创建失败'];
        }
    }

    /**
     * 市场预测分析
     */
    public function predictMarketTrend(string $symbol, string $timeframe = '1h', int $periods = 24): array
    {
        try {
            // 获取历史数据
            $historicalData = $this->getHistoricalData($symbol, $timeframe, 1000);
            
            // 技术指标计算
            $technicalIndicators = $this->calculateTechnicalIndicators($historicalData);
            
            // 情绪分析
            $sentimentData = $this->analyzeSentiment($symbol);
            
            // 机器学习预测
            $mlPrediction = $this->performMachineLearningPrediction($symbol, $historicalData, $technicalIndicators, $sentimentData);
            
            // 综合分析
            $prediction = $this->generateComprehensivePrediction($mlPrediction, $technicalIndicators, $sentimentData);

            return [
                'code' => 1,
                'data' => [
                    'symbol' => $symbol,
                    'timeframe' => $timeframe,
                    'prediction' => $prediction,
                    'confidence' => $prediction['confidence'],
                    'technical_analysis' => $technicalIndicators,
                    'sentiment_analysis' => $sentimentData,
                    'price_targets' => $prediction['price_targets'],
                    'risk_assessment' => $prediction['risk_assessment']
                ]
            ];
        } catch (\Exception $e) {
            Log::error('市场预测失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '市场预测失败'];
        }
    }

    /**
     * 智能投顾建议
     */
    public function getInvestmentAdvice(int $userId, array $preferences = []): array
    {
        try {
            // 获取用户画像
            $userProfile = $this->buildUserProfile($userId);
            
            // 市场分析
            $marketAnalysis = $this->performMarketAnalysis();
            
            // 资产配置建议
            $assetAllocation = $this->generateAssetAllocation($userProfile, $marketAnalysis, $preferences);
            
            // 投资策略推荐
            $strategyRecommendations = $this->recommendInvestmentStrategies($userProfile, $marketAnalysis);
            
            // 风险提示
            $riskWarnings = $this->generateRiskWarnings($userProfile, $assetAllocation);

            return [
                'code' => 1,
                'data' => [
                    'user_profile' => $userProfile,
                    'market_outlook' => $marketAnalysis['outlook'],
                    'asset_allocation' => $assetAllocation,
                    'strategy_recommendations' => $strategyRecommendations,
                    'risk_warnings' => $riskWarnings,
                    'next_review_date' => date('Y-m-d', strtotime('+1 week'))
                ]
            ];
        } catch (\Exception $e) {
            Log::error('智能投顾建议生成失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '智能投顾建议生成失败'];
        }
    }

    /**
     * 风险预警系统
     */
    public function performRiskAssessment(int $userId, string $symbol = ''): array
    {
        try {
            $riskFactors = [];

            // 市场风险评估
            $marketRisk = $this->assessMarketRisk($symbol);
            $riskFactors['market_risk'] = $marketRisk;

            // 用户持仓风险
            $portfolioRisk = $this->assessPortfolioRisk($userId);
            $riskFactors['portfolio_risk'] = $portfolioRisk;

            // 流动性风险
            $liquidityRisk = $this->assessLiquidityRisk($userId, $symbol);
            $riskFactors['liquidity_risk'] = $liquidityRisk;

            // 技术风险
            $technicalRisk = $this->assessTechnicalRisk($symbol);
            $riskFactors['technical_risk'] = $technicalRisk;

            // 综合风险评分
            $overallRisk = $this->calculateOverallRisk($riskFactors);

            // 生成预警建议
            $warnings = $this->generateRiskWarnings($riskFactors, $overallRisk);

            return [
                'code' => 1,
                'data' => [
                    'overall_risk_score' => $overallRisk['score'],
                    'risk_level' => $overallRisk['level'],
                    'risk_factors' => $riskFactors,
                    'warnings' => $warnings,
                    'recommendations' => $this->generateRiskMitigationRecommendations($riskFactors)
                ]
            ];
        } catch (\Exception $e) {
            Log::error('风险评估失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '风险评估失败'];
        }
    }

    /**
     * 智能订单执行
     */
    public function executeSmartOrder(int $userId, array $orderData): array
    {
        try {
            // 市场时机分析
            $timingAnalysis = $this->analyzeMarketTiming($orderData['symbol'], $orderData['side']);
            
            // 最优执行策略
            $executionStrategy = $this->determineOptimalExecutionStrategy($orderData, $timingAnalysis);
            
            // 价格预测
            $priceForcast = $this->predictOptimalPrice($orderData['symbol'], $orderData['side']);
            
            // 执行建议
            $executionAdvice = [
                'recommended_action' => $executionStrategy['action'],
                'optimal_price' => $priceForcast['optimal_price'],
                'execution_timing' => $executionStrategy['timing'],
                'split_strategy' => $executionStrategy['split_strategy'],
                'confidence' => $executionStrategy['confidence']
            ];

            // 如果建议立即执行
            if ($executionStrategy['action'] === 'execute_now') {
                $tradeService = new TradeService();
                $result = $tradeService->createOrder($userId, $orderData);
                
                if ($result['code']) {
                    $executionAdvice['order_id'] = $result['data']['order_id'];
                    $executionAdvice['execution_status'] = 'executed';
                }
            }

            return [
                'code' => 1,
                'data' => $executionAdvice
            ];
        } catch (\Exception $e) {
            Log::error('智能订单执行失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '智能订单执行失败'];
        }
    }

    /**
     * 情绪分析
     */
    private function analyzeSentiment(string $symbol): array
    {
        try {
            // 社交媒体情绪分析
            $socialSentiment = $this->analyzeSocialMediaSentiment($symbol);
            
            // 新闻情绪分析
            $newsSentiment = $this->analyzeNewsSentiment($symbol);
            
            // 交易量情绪分析
            $volumeSentiment = $this->analyzeVolumeSentiment($symbol);
            
            // 恐慌贪婪指数
            $fearGreedIndex = $this->calculateFearGreedIndex($symbol);

            return [
                'social_sentiment' => $socialSentiment,
                'news_sentiment' => $newsSentiment,
                'volume_sentiment' => $volumeSentiment,
                'fear_greed_index' => $fearGreedIndex,
                'overall_sentiment' => $this->calculateOverallSentiment([
                    $socialSentiment, $newsSentiment, $volumeSentiment, $fearGreedIndex
                ])
            ];
        } catch (\Exception $e) {
            Log::error('情绪分析失败: ' . $e->getMessage());
            return ['error' => '情绪分析失败'];
        }
    }

    /**
     * 技术指标计算
     */
    private function calculateTechnicalIndicators(array $data): array
    {
        $indicators = [];

        // 移动平均线
        $indicators['sma_20'] = $this->calculateSMA($data, 20);
        $indicators['sma_50'] = $this->calculateSMA($data, 50);
        $indicators['ema_12'] = $this->calculateEMA($data, 12);
        $indicators['ema_26'] = $this->calculateEMA($data, 26);

        // MACD
        $indicators['macd'] = $this->calculateMACD($data);

        // RSI
        $indicators['rsi'] = $this->calculateRSI($data, 14);

        // 布林带
        $indicators['bollinger_bands'] = $this->calculateBollingerBands($data, 20, 2);

        // KDJ
        $indicators['kdj'] = $this->calculateKDJ($data, 9, 3, 3);

        // 成交量指标
        $indicators['volume_sma'] = $this->calculateVolumeSMA($data, 20);

        // 支撑阻力位
        $indicators['support_resistance'] = $this->calculateSupportResistance($data);

        return $indicators;
    }

    /**
     * 机器学习预测
     */
    private function performMachineLearningPrediction(string $symbol, array $historicalData, array $technicalIndicators, array $sentimentData): array
    {
        try {
            // 特征工程
            $features = $this->extractFeatures($historicalData, $technicalIndicators, $sentimentData);
            
            // 模型预测
            $models = [
                'lstm' => $this->predictWithLSTM($features),
                'random_forest' => $this->predictWithRandomForest($features),
                'svm' => $this->predictWithSVM($features),
                'xgboost' => $this->predictWithXGBoost($features)
            ];

            // 集成学习
            $ensemblePrediction = $this->ensemblePredictions($models);

            return [
                'individual_models' => $models,
                'ensemble_prediction' => $ensemblePrediction,
                'confidence' => $ensemblePrediction['confidence'],
                'prediction_horizon' => '24h'
            ];
        } catch (\Exception $e) {
            Log::error('机器学习预测失败: ' . $e->getMessage());
            return ['error' => '机器学习预测失败'];
        }
    }

    /**
     * 用户风险承受能力评估
     */
    private function assessUserRiskTolerance(int $userId): array
    {
        try {
            $user = User::find($userId);
            if (!$user) {
                return ['code' => 0, 'msg' => '用户不存在'];
            }

            // 获取用户交易历史
            $tradingHistory = $this->getUserTradingHistory($userId);
            
            // 计算风险指标
            $riskMetrics = [
                'max_drawdown' => $this->calculateMaxDrawdown($tradingHistory),
                'volatility' => $this->calculateVolatility($tradingHistory),
                'sharpe_ratio' => $this->calculateSharpeRatio($tradingHistory),
                'win_rate' => $this->calculateWinRate($tradingHistory)
            ];

            // 风险承受能力评分
            $riskScore = $this->calculateRiskToleranceScore($riskMetrics);

            return [
                'code' => 1,
                'data' => [
                    'risk_score' => $riskScore,
                    'risk_level' => $this->getRiskLevel($riskScore),
                    'risk_metrics' => $riskMetrics,
                    'recommendations' => $this->getRiskRecommendations($riskScore)
                ]
            ];
        } catch (\Exception $e) {
            Log::error('风险承受能力评估失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '风险评估失败'];
        }
    }

    /**
     * 简单移动平均线
     */
    private function calculateSMA(array $data, int $period): array
    {
        $sma = [];
        $prices = array_column($data, 'close');
        
        for ($i = $period - 1; $i < count($prices); $i++) {
            $sum = array_sum(array_slice($prices, $i - $period + 1, $period));
            $sma[] = $sum / $period;
        }
        
        return $sma;
    }

    /**
     * 指数移动平均线
     */
    private function calculateEMA(array $data, int $period): array
    {
        $ema = [];
        $prices = array_column($data, 'close');
        $multiplier = 2 / ($period + 1);
        
        // 第一个EMA值使用SMA
        $ema[0] = array_sum(array_slice($prices, 0, $period)) / $period;
        
        for ($i = 1; $i < count($prices) - $period + 1; $i++) {
            $ema[$i] = ($prices[$i + $period - 1] * $multiplier) + ($ema[$i - 1] * (1 - $multiplier));
        }
        
        return $ema;
    }

    /**
     * RSI计算
     */
    private function calculateRSI(array $data, int $period): array
    {
        $prices = array_column($data, 'close');
        $gains = [];
        $losses = [];
        
        for ($i = 1; $i < count($prices); $i++) {
            $change = $prices[$i] - $prices[$i - 1];
            $gains[] = $change > 0 ? $change : 0;
            $losses[] = $change < 0 ? abs($change) : 0;
        }
        
        $rsi = [];
        for ($i = $period - 1; $i < count($gains); $i++) {
            $avgGain = array_sum(array_slice($gains, $i - $period + 1, $period)) / $period;
            $avgLoss = array_sum(array_slice($losses, $i - $period + 1, $period)) / $period;
            
            if ($avgLoss == 0) {
                $rsi[] = 100;
            } else {
                $rs = $avgGain / $avgLoss;
                $rsi[] = 100 - (100 / (1 + $rs));
            }
        }
        
        return $rsi;
    }

    /**
     * MACD计算
     */
    private function calculateMACD(array $data): array
    {
        $ema12 = $this->calculateEMA($data, 12);
        $ema26 = $this->calculateEMA($data, 26);
        
        $macdLine = [];
        $minLength = min(count($ema12), count($ema26));
        
        for ($i = 0; $i < $minLength; $i++) {
            $macdLine[] = $ema12[$i] - $ema26[$i];
        }
        
        // 信号线（MACD的9日EMA）
        $signalLine = $this->calculateEMAFromArray($macdLine, 9);
        
        // 柱状图
        $histogram = [];
        $minSignalLength = min(count($macdLine), count($signalLine));
        
        for ($i = 0; $i < $minSignalLength; $i++) {
            $histogram[] = $macdLine[$i] - $signalLine[$i];
        }
        
        return [
            'macd_line' => $macdLine,
            'signal_line' => $signalLine,
            'histogram' => $histogram
        ];
    }

    /**
     * 从数组计算EMA
     */
    private function calculateEMAFromArray(array $values, int $period): array
    {
        $ema = [];
        $multiplier = 2 / ($period + 1);
        
        $ema[0] = array_sum(array_slice($values, 0, $period)) / $period;
        
        for ($i = 1; $i < count($values) - $period + 1; $i++) {
            $ema[$i] = ($values[$i + $period - 1] * $multiplier) + ($ema[$i - 1] * (1 - $multiplier));
        }
        
        return $ema;
    }

    /**
     * 布林带计算
     */
    private function calculateBollingerBands(array $data, int $period, float $stdDev): array
    {
        $sma = $this->calculateSMA($data, $period);
        $prices = array_column($data, 'close');
        
        $upperBand = [];
        $lowerBand = [];
        
        for ($i = $period - 1; $i < count($prices); $i++) {
            $slice = array_slice($prices, $i - $period + 1, $period);
            $mean = array_sum($slice) / $period;
            
            $variance = 0;
            foreach ($slice as $price) {
                $variance += pow($price - $mean, 2);
            }
            $standardDeviation = sqrt($variance / $period);
            
            $upperBand[] = $sma[$i - $period + 1] + ($stdDev * $standardDeviation);
            $lowerBand[] = $sma[$i - $period + 1] - ($stdDev * $standardDeviation);
        }
        
        return [
            'middle_band' => $sma,
            'upper_band' => $upperBand,
            'lower_band' => $lowerBand
        ];
    }

    /**
     * 生成策略ID
     */
    private function generateStrategyId(): string
    {
        return 'AI' . date('YmdHis') . mt_rand(1000, 9999);
    }

    /**
     * 模拟方法 - 实际应用中需要连接真实的ML服务
     */
    private function predictWithLSTM(array $features): array
    {
        return [
            'prediction' => rand(95, 105) / 100,
            'confidence' => rand(70, 90) / 100,
            'model' => 'LSTM'
        ];
    }

    private function predictWithRandomForest(array $features): array
    {
        return [
            'prediction' => rand(96, 104) / 100,
            'confidence' => rand(75, 85) / 100,
            'model' => 'Random Forest'
        ];
    }

    private function predictWithSVM(array $features): array
    {
        return [
            'prediction' => rand(97, 103) / 100,
            'confidence' => rand(65, 80) / 100,
            'model' => 'SVM'
        ];
    }

    private function predictWithXGBoost(array $features): array
    {
        return [
            'prediction' => rand(98, 102) / 100,
            'confidence' => rand(80, 95) / 100,
            'model' => 'XGBoost'
        ];
    }

    /**
     * 集成预测结果
     */
    private function ensemblePredictions(array $models): array
    {
        $totalWeight = 0;
        $weightedPrediction = 0;
        
        foreach ($models as $model) {
            $weight = $model['confidence'];
            $weightedPrediction += $model['prediction'] * $weight;
            $totalWeight += $weight;
        }
        
        $finalPrediction = $weightedPrediction / $totalWeight;
        $avgConfidence = $totalWeight / count($models);
        
        return [
            'prediction' => $finalPrediction,
            'confidence' => $avgConfidence,
            'direction' => $finalPrediction > 1 ? 'up' : 'down',
            'strength' => abs($finalPrediction - 1) * 100
        ];
    }

    /**
     * 其他模拟方法
     */
    private function getHistoricalData(string $symbol, string $timeframe, int $limit): array
    {
        // 模拟历史数据
        $data = [];
        $basePrice = 45000;
        
        for ($i = 0; $i < $limit; $i++) {
            $change = (rand(-500, 500) / 100);
            $basePrice += $change;
            
            $data[] = [
                'timestamp' => time() - ($limit - $i) * 3600,
                'open' => $basePrice,
                'high' => $basePrice + rand(0, 200),
                'low' => $basePrice - rand(0, 200),
                'close' => $basePrice + rand(-100, 100),
                'volume' => rand(100, 1000)
            ];
        }
        
        return $data;
    }

    private function extractFeatures(array $historicalData, array $technicalIndicators, array $sentimentData): array
    {
        return [
            'price_features' => array_slice(array_column($historicalData, 'close'), -50),
            'volume_features' => array_slice(array_column($historicalData, 'volume'), -50),
            'technical_features' => $technicalIndicators,
            'sentiment_features' => $sentimentData
        ];
    }

    private function validateStrategyParameters(array $data): array
    {
        if (empty($data['name'])) {
            return ['code' => 0, 'msg' => '策略名称不能为空'];
        }
        
        if (empty($data['type']) || !in_array($data['type'], [
            self::STRATEGY_TREND_FOLLOWING,
            self::STRATEGY_MEAN_REVERSION,
            self::STRATEGY_MOMENTUM,
            self::STRATEGY_ARBITRAGE,
            self::STRATEGY_SENTIMENT,
            self::STRATEGY_NEURAL_NETWORK
        ])) {
            return ['code' => 0, 'msg' => '策略类型无效'];
        }
        
        return ['code' => 1, 'msg' => '验证通过'];
    }

    private function optimizeStrategyParameters(string $type, array $parameters): array
    {
        // 根据策略类型优化参数
        $optimized = $parameters;
        
        switch ($type) {
            case self::STRATEGY_TREND_FOLLOWING:
                $optimized['lookback_period'] = $optimized['lookback_period'] ?? 20;
                $optimized['threshold'] = $optimized['threshold'] ?? 0.02;
                break;
            case self::STRATEGY_MEAN_REVERSION:
                $optimized['mean_period'] = $optimized['mean_period'] ?? 50;
                $optimized['deviation_threshold'] = $optimized['deviation_threshold'] ?? 2.0;
                break;
        }
        
        return $optimized;
    }

    private function selectOptimalModel(string $strategyType): string
    {
        $modelMap = [
            self::STRATEGY_TREND_FOLLOWING => 'LSTM',
            self::STRATEGY_MEAN_REVERSION => 'Random Forest',
            self::STRATEGY_MOMENTUM => 'XGBoost',
            self::STRATEGY_ARBITRAGE => 'SVM',
            self::STRATEGY_SENTIMENT => 'Neural Network',
            self::STRATEGY_NEURAL_NETWORK => 'Deep Neural Network'
        ];
        
        return $modelMap[$strategyType] ?? 'Random Forest';
    }

    private function startModelTraining($strategy): void
    {
        // 启动异步模型训练任务
        Log::info("开始训练AI模型", [
            'strategy_id' => $strategy->strategy_id,
            'type' => $strategy->type
        ]);
    }

    private function estimateTrainingTime(string $type): string
    {
        $timeMap = [
            self::STRATEGY_NEURAL_NETWORK => '2-4小时',
            self::STRATEGY_SENTIMENT => '1-2小时',
            default => '30-60分钟'
        ];
        
        return $timeMap[$type] ?? $timeMap['default'];
    }
}
