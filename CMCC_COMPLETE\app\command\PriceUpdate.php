<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Cache;
use think\facade\Log;
use think\facade\Db;

/**
 * 价格更新定时任务
 */
class PriceUpdate extends Command
{
    protected function configure()
    {
        $this->setName('trading:price-update')
            ->setDescription('更新市场价格数据');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始更新市场价格...');
        
        try {
            // 1. 更新主要交易对价格
            $this->updateMainPairs($output);
            
            // 2. 更新期货合约价格
            $this->updateFuturesPrices($output);
            
            // 3. 生成K线数据
            $this->generateKlineData($output);
            
            // 4. 更新市场统计
            $this->updateMarketStats($output);
            
            $output->writeln('市场价格更新完成');
            Log::info('市场价格更新成功');
            
        } catch (\Exception $e) {
            $output->writeln('价格更新失败: ' . $e->getMessage());
            Log::error('价格更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新主要交易对价格
     */
    private function updateMainPairs(Output $output): void
    {
        $output->writeln('更新主要交易对价格...');
        
        $pairs = [
            'BTCUSDT' => $this->generatePrice(45000, 55000),
            'ETHUSDT' => $this->generatePrice(2800, 3200),
            'BNBUSDT' => $this->generatePrice(280, 320),
            'ADAUSDT' => $this->generatePrice(0.8, 1.2),
            'DOTUSDT' => $this->generatePrice(25, 35),
            'LINKUSDT' => $this->generatePrice(20, 30),
            'LTCUSDT' => $this->generatePrice(180, 220),
            'BCHUSDT' => $this->generatePrice(450, 550),
            'XLMUSDT' => $this->generatePrice(0.3, 0.5),
            'EOSUSDT' => $this->generatePrice(3, 5)
        ];

        foreach ($pairs as $symbol => $price) {
            // 更新缓存
            Cache::set('price:' . $symbol, $price, 3600);
            
            // 保存到数据库
            $this->savePriceToDatabase($symbol, $price);
            
            $output->writeln("更新 {$symbol}: {$price}");
        }
    }

    /**
     * 更新期货合约价格
     */
    private function updateFuturesPrices(Output $output): void
    {
        $output->writeln('更新期货合约价格...');
        
        $contracts = Db::name('futures_contracts')
            ->where('status', 1)
            ->column('symbol');

        foreach ($contracts as $symbol) {
            $basePrice = Cache::get('price:' . str_replace('-', '', $symbol));
            if ($basePrice) {
                // 期货价格通常与现货价格有小幅差异
                $futuresPrice = $basePrice * (1 + (rand(-100, 100) / 10000));
                Cache::set('futures_price:' . $symbol, $futuresPrice, 3600);
                
                $output->writeln("更新期货 {$symbol}: {$futuresPrice}");
            }
        }
    }

    /**
     * 生成K线数据
     */
    private function generateKlineData(Output $output): void
    {
        $output->writeln('生成K线数据...');
        
        $symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT'];
        $intervals = ['1m', '5m', '15m', '1h', '4h', '1d'];
        
        foreach ($symbols as $symbol) {
            $currentPrice = Cache::get('price:' . $symbol);
            if (!$currentPrice) continue;
            
            foreach ($intervals as $interval) {
                $this->generateKlineForInterval($symbol, $interval, $currentPrice);
            }
        }
    }

    /**
     * 为特定时间间隔生成K线数据
     */
    private function generateKlineForInterval(string $symbol, string $interval, float $currentPrice): void
    {
        $timestamp = $this->getIntervalTimestamp($interval);
        
        // 检查是否已存在该时间段的K线
        $exists = Db::name('kline_data')
            ->where('symbol', $symbol)
            ->where('interval', $interval)
            ->where('timestamp', $timestamp)
            ->find();
            
        if ($exists) {
            // 更新收盘价和最高最低价
            $high = max($exists['high'], $currentPrice);
            $low = min($exists['low'], $currentPrice);
            
            Db::name('kline_data')
                ->where('id', $exists['id'])
                ->update([
                    'close' => $currentPrice,
                    'high' => $high,
                    'low' => $low,
                    'volume' => $exists['volume'] + rand(100, 1000),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
        } else {
            // 创建新的K线数据
            $open = $currentPrice * (1 + (rand(-200, 200) / 10000));
            $high = max($open, $currentPrice) * (1 + (rand(0, 100) / 10000));
            $low = min($open, $currentPrice) * (1 - (rand(0, 100) / 10000));
            
            Db::name('kline_data')->insert([
                'symbol' => $symbol,
                'interval' => $interval,
                'timestamp' => $timestamp,
                'open' => $open,
                'high' => $high,
                'low' => $low,
                'close' => $currentPrice,
                'volume' => rand(1000, 10000),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }
    }

    /**
     * 更新市场统计
     */
    private function updateMarketStats(Output $output): void
    {
        $output->writeln('更新市场统计...');
        
        try {
            // 计算24小时交易量
            $volume24h = Db::name('futures_trades')
                ->where('created_at', '>=', date('Y-m-d H:i:s', time() - 86400))
                ->sum('quantity * price');
            
            // 计算活跃用户数
            $activeUsers = Db::name('leverage_orders')
                ->where('created_at', '>=', date('Y-m-d H:i:s', time() - 86400))
                ->count('DISTINCT user_id');
            
            // 保存统计数据
            Cache::set('market_stats', [
                'volume_24h' => $volume24h,
                'active_users' => $activeUsers,
                'updated_at' => time()
            ], 3600);
            
            $output->writeln("24小时交易量: {$volume24h}");
            $output->writeln("活跃用户数: {$activeUsers}");
            
        } catch (\Exception $e) {
            $output->writeln('市场统计更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成随机价格
     */
    private function generatePrice(float $min, float $max): float
    {
        $price = $min + (($max - $min) * (rand(0, 10000) / 10000));
        
        // 添加一些随机波动
        $volatility = 0.02; // 2%波动
        $change = 1 + (rand(-100, 100) / 10000) * $volatility;
        
        return round($price * $change, 2);
    }

    /**
     * 保存价格到数据库
     */
    private function savePriceToDatabase(string $symbol, float $price): void
    {
        try {
            // 检查是否存在价格记录表
            $tableExists = Db::query("SHOW TABLES LIKE 'gvd_market_prices'");
            
            if (!$tableExists) {
                // 创建价格记录表
                Db::execute("
                    CREATE TABLE `gvd_market_prices` (
                        `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                        `symbol` varchar(20) NOT NULL,
                        `price` decimal(20,8) NOT NULL,
                        `change_24h` decimal(8,4) DEFAULT '0.0000',
                        `volume_24h` decimal(20,8) DEFAULT '0.00000000',
                        `created_at` datetime NOT NULL,
                        PRIMARY KEY (`id`),
                        KEY `symbol` (`symbol`),
                        KEY `created_at` (`created_at`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='市场价格记录表'
                ");
            }
            
            // 获取上一次价格计算涨跌幅
            $lastPrice = Db::name('market_prices')
                ->where('symbol', $symbol)
                ->order('id desc')
                ->value('price');
            
            $change24h = 0;
            if ($lastPrice) {
                $change24h = (($price - $lastPrice) / $lastPrice) * 100;
            }
            
            // 插入新价格记录
            Db::name('market_prices')->insert([
                'symbol' => $symbol,
                'price' => $price,
                'change_24h' => $change24h,
                'volume_24h' => rand(1000000, 10000000),
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
        } catch (\Exception $e) {
            Log::error('保存价格到数据库失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取时间间隔对应的时间戳
     */
    private function getIntervalTimestamp(string $interval): int
    {
        $now = time();
        
        switch ($interval) {
            case '1m':
                return $now - ($now % 60);
            case '5m':
                return $now - ($now % 300);
            case '15m':
                return $now - ($now % 900);
            case '1h':
                return $now - ($now % 3600);
            case '4h':
                return $now - ($now % 14400);
            case '1d':
                return strtotime(date('Y-m-d 00:00:00'));
            default:
                return $now;
        }
    }
}
