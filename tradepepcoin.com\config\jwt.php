<?php

return [
    // JWT密钥
    'secret' => env('jwt.secret', 'your-jwt-secret-key'),
    
    // JWT过期时间（秒）
    'expire' => env('jwt.expire', 7200),
    
    // 刷新token过期时间（秒）
    'refresh_expire' => env('jwt.refresh_expire', 604800),
    
    // 签发者
    'issuer' => env('jwt.issuer', 'crypto-exchange'),
    
    // 接收者
    'audience' => env('jwt.audience', 'crypto-exchange-users'),
    
    // 算法
    'algorithm' => 'HS256',
    
    // 黑名单缓存前缀
    'blacklist_prefix' => 'jwt_blacklist:',
    
    // 是否允许刷新token
    'allow_refresh' => true,
    
    // 刷新token宽限期（秒）
    'refresh_grace_period' => 300,
];
