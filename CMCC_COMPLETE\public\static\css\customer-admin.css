/* GVD管理端客服系统样式 */

/* 客服界面布局 */
.customer-interface {
    display: grid;
    grid-template-columns: 320px 1fr 280px;
    gap: 20px;
    height: 600px;
    margin-bottom: 40px;
}

/* 会话面板 */
.sessions-panel {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.panel-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background: var(--secondary-bg);
}

.panel-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
}

.panel-filters {
    display: flex;
    gap: 8px;
}

.panel-filters select {
    flex: 1;
    padding: 6px 8px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--card-bg);
    color: var(--text-primary);
    font-size: 12px;
}

/* 会话列表 */
.sessions-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
}

.session-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition-fast);
    position: relative;
    margin-bottom: 4px;
}

.session-item:hover {
    background: var(--secondary-bg);
}

.session-item.selected {
    background: var(--color-info);
    color: white;
}

.session-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--gradient-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    flex-shrink: 0;
}

.session-content {
    flex: 1;
    min-width: 0;
}

.session-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.session-name {
    font-weight: 600;
    font-size: 14px;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.session-item.selected .session-name {
    color: white;
}

.session-time {
    font-size: 11px;
    color: var(--text-muted);
    flex-shrink: 0;
}

.session-item.selected .session-time {
    color: rgba(255, 255, 255, 0.8);
}

.session-preview {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.session-status {
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 500;
    flex-shrink: 0;
}

.session-status.active {
    background: rgba(2, 192, 118, 0.1);
    color: var(--color-success);
}

.session-status.waiting {
    background: rgba(240, 185, 11, 0.1);
    color: var(--color-warning);
}

.session-status.closed {
    background: rgba(132, 142, 156, 0.1);
    color: var(--text-muted);
}

.session-message {
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.session-item.selected .session-message {
    color: rgba(255, 255, 255, 0.8);
}

.session-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: var(--color-danger);
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: 600;
    min-width: 18px;
    text-align: center;
}

/* 空状态 */
.empty-sessions {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--text-muted);
}

.empty-sessions .empty-icon {
    font-size: 48px;
    margin-bottom: 12px;
    opacity: 0.5;
}

.empty-sessions .empty-text {
    font-size: 14px;
}

/* 聊天面板 */
.chat-panel {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.chat-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    background: var(--secondary-bg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.chat-info .user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: var(--gradient-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
}

.user-details .user-name {
    font-weight: 600;
    font-size: 14px;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.user-details .user-status {
    font-size: 12px;
    color: var(--text-secondary);
}

.chat-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.action-btn:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

/* 聊天消息 */
.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: var(--primary-bg);
}

.empty-chat {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--text-muted);
    text-align: center;
}

.empty-chat .empty-icon {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.3;
}

.empty-chat h3 {
    font-size: 18px;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.empty-chat p {
    font-size: 14px;
}

/* 消息样式 */
.message {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.message.admin {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 12px;
    flex-shrink: 0;
}

.message.user .message-avatar {
    background: var(--color-info);
    color: white;
}

.message.agent .message-avatar {
    background: var(--color-success);
    color: white;
}

.message.admin .message-avatar {
    background: var(--color-warning);
    color: white;
}

.message-content {
    max-width: 70%;
}

.message.admin .message-content {
    text-align: right;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
    font-size: 11px;
    color: var(--text-muted);
}

.message.admin .message-header {
    flex-direction: row-reverse;
}

.message-sender {
    font-weight: 500;
}

.message-bubble {
    background: white;
    padding: 12px 16px;
    border-radius: 16px;
    box-shadow: var(--shadow-sm);
    word-wrap: break-word;
}

.message.admin .message-bubble {
    background: var(--color-info);
    color: white;
}

.message-text {
    font-size: 14px;
    line-height: 1.4;
}

.message-image img {
    max-width: 200px;
    max-height: 200px;
    border-radius: 8px;
}

.message-emoji {
    font-size: 24px;
}

/* 聊天输入 */
.chat-input {
    border-top: 1px solid var(--border-color);
    background: white;
}

.quick-replies {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.quick-reply-btn {
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    background: var(--card-bg);
    color: var(--text-primary);
    border-radius: 16px;
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition-fast);
}

.quick-reply-btn:hover {
    background: var(--color-info);
    color: white;
    border-color: var(--color-info);
}

.input-area {
    display: flex;
    align-items: flex-end;
    padding: 16px;
    gap: 12px;
}

.input-area textarea {
    flex: 1;
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 12px 16px;
    font-size: 14px;
    line-height: 1.4;
    resize: none;
    outline: none;
    max-height: 120px;
    min-height: 20px;
}

.input-area textarea:focus {
    border-color: var(--color-info);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.send-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: var(--color-info);
    color: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.send-btn:hover {
    background: var(--color-info);
    transform: scale(1.05);
}

/* 用户信息面板 */
.user-info-panel {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
}

.user-profile {
    padding: 20px;
}

.profile-avatar {
    text-align: center;
    margin-bottom: 20px;
}

.avatar-placeholder {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--gradient-primary);
    color: white;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 24px;
}

.profile-info {
    space-y: 12px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
    border-bottom: none;
}

.info-item label {
    font-size: 12px;
    color: var(--text-muted);
    font-weight: 500;
}

.info-item span {
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 500;
}

.user-actions {
    padding: 16px 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* 快捷回复管理 */
.quick-reply-section {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.quick-reply-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
}

.quick-reply-item {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 16px;
    transition: var(--transition-fast);
}

.quick-reply-item:hover {
    border-color: var(--color-info);
}

.reply-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.reply-title {
    font-weight: 600;
    font-size: 14px;
    color: var(--text-primary);
}

.reply-category {
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 500;
    background: var(--color-info);
    color: white;
}

.reply-content {
    font-size: 13px;
    color: var(--text-secondary);
    line-height: 1.4;
    margin-bottom: 12px;
}

.reply-actions {
    display: flex;
    gap: 8px;
}

.reply-actions .btn {
    padding: 4px 8px;
    font-size: 11px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .customer-interface {
        grid-template-columns: 280px 1fr 240px;
    }
}

@media (max-width: 768px) {
    .customer-interface {
        grid-template-columns: 1fr;
        height: auto;
    }
    
    .sessions-panel,
    .chat-panel,
    .user-info-panel {
        height: 400px;
    }
    
    .quick-reply-list {
        grid-template-columns: 1fr;
    }
}
