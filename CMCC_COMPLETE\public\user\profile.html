<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人设置 - GVD交易平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }

        .header {
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }

        .nav-menu {
            display: flex;
            gap: 30px;
        }

        .nav-menu a {
            text-decoration: none;
            color: #666;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-menu a:hover,
        .nav-menu a.active {
            color: #667eea;
        }

        .back-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
        }

        .container {
            max-width: 1000px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 30px;
            color: #333;
        }

        .profile-layout {
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: 30px;
        }

        .profile-sidebar {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            height: fit-content;
        }

        .profile-nav {
            list-style: none;
        }

        .profile-nav li {
            margin-bottom: 10px;
        }

        .profile-nav a {
            display: block;
            padding: 12px 15px;
            color: #666;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .profile-nav a:hover,
        .profile-nav a.active {
            background: #667eea;
            color: white;
        }

        .profile-content {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }

        .section {
            display: none;
        }

        .section.active {
            display: block;
        }

        .section-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-group input:disabled {
            background: #f8f9fa;
            color: #6c757d;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .info-card {
            background: #f8f9ff;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .info-card h4 {
            color: #667eea;
            margin-bottom: 10px;
        }

        .info-card p {
            color: #666;
            line-height: 1.6;
        }

        .security-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .security-item:last-child {
            border-bottom: none;
        }

        .security-info h4 {
            margin-bottom: 5px;
            color: #333;
        }

        .security-info p {
            color: #666;
            font-size: 14px;
        }

        .security-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-enabled {
            background: rgba(46, 204, 113, 0.2);
            color: #27ae60;
        }

        .status-disabled {
            background: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
        }

        .error-message {
            background: #fee;
            color: #c33;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }

        .success-message {
            background: #efe;
            color: #3c3;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }

        .avatar-section {
            text-align: center;
            margin-bottom: 30px;
        }

        .avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: #667eea;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 36px;
            font-weight: bold;
            margin: 0 auto 15px;
        }

        .avatar-upload {
            display: none;
        }

        .upload-btn {
            background: #f8f9fa;
            color: #667eea;
            border: 2px solid #667eea;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .profile-layout {
                grid-template-columns: 1fr;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">GVD交易平台</div>
            <nav class="nav-menu">
                <a href="/trade/index.html">交易</a>
                <a href="/ido/index.html">IDO</a>
                <a href="/user/dashboard.html">资产</a>
                <a href="/user/orders.html">订单</a>
            </nav>
            <a href="/user/dashboard.html" class="back-btn">返回资产</a>
        </div>
    </div>

    <div class="container">
        <h1 class="page-title">个人设置</h1>
        
        <div class="profile-layout">
            <div class="profile-sidebar">
                <ul class="profile-nav">
                    <li><a href="#" class="nav-link active" data-section="basic">基本信息</a></li>
                    <li><a href="#" class="nav-link" data-section="security">安全设置</a></li>
                    <li><a href="#" class="nav-link" data-section="password">修改密码</a></li>
                    <li><a href="#" class="nav-link" data-section="notification">通知设置</a></li>
                </ul>
            </div>

            <div class="profile-content">
                <!-- 基本信息 -->
                <div class="section active" id="basic">
                    <h2 class="section-title">基本信息</h2>
                    
                    <div class="avatar-section">
                        <div class="avatar" id="userAvatar">U</div>
                        <button class="upload-btn" onclick="selectAvatar()">更换头像</button>
                        <input type="file" class="avatar-upload" id="avatarUpload" accept="image/*" onchange="uploadAvatar()">
                    </div>
                    
                    <div class="error-message" id="basicError"></div>
                    <div class="success-message" id="basicSuccess"></div>
                    
                    <form id="basicForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="username">用户名</label>
                                <input type="text" id="username" disabled>
                            </div>
                            <div class="form-group">
                                <label for="email">邮箱地址</label>
                                <input type="email" id="email" disabled>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="phone">手机号码</label>
                                <input type="tel" id="phone" placeholder="请输入手机号码">
                            </div>
                            <div class="form-group">
                                <label for="realName">真实姓名</label>
                                <input type="text" id="realName" placeholder="请输入真实姓名">
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">保存修改</button>
                    </form>
                </div>

                <!-- 安全设置 -->
                <div class="section" id="security">
                    <h2 class="section-title">安全设置</h2>
                    
                    <div class="info-card">
                        <h4>安全提示</h4>
                        <p>为了保障您的账户安全，建议您开启所有安全功能。如果您发现账户异常，请立即联系客服。</p>
                    </div>
                    
                    <div class="security-item">
                        <div class="security-info">
                            <h4>登录密码</h4>
                            <p>用于登录账户的密码</p>
                        </div>
                        <div class="security-status">
                            <span class="status-badge status-enabled">已设置</span>
                            <button class="btn btn-primary" onclick="showSection('password')">修改</button>
                        </div>
                    </div>
                    
                    <div class="security-item">
                        <div class="security-info">
                            <h4>支付密码</h4>
                            <p>用于提现和交易的6位数字密码</p>
                        </div>
                        <div class="security-status">
                            <span class="status-badge" id="payPasswordStatus">未设置</span>
                            <button class="btn btn-primary" onclick="setPayPassword()">设置</button>
                        </div>
                    </div>
                    
                    <div class="security-item">
                        <div class="security-info">
                            <h4>邮箱验证</h4>
                            <p>邮箱已绑定并验证</p>
                        </div>
                        <div class="security-status">
                            <span class="status-badge status-enabled">已验证</span>
                        </div>
                    </div>
                    
                    <div class="security-item">
                        <div class="security-info">
                            <h4>手机验证</h4>
                            <p>绑定手机号码用于接收验证码</p>
                        </div>
                        <div class="security-status">
                            <span class="status-badge" id="phoneStatus">未绑定</span>
                            <button class="btn btn-primary" onclick="bindPhone()">绑定</button>
                        </div>
                    </div>
                </div>

                <!-- 修改密码 -->
                <div class="section" id="password">
                    <h2 class="section-title">修改密码</h2>
                    
                    <div class="error-message" id="passwordError"></div>
                    <div class="success-message" id="passwordSuccess"></div>
                    
                    <form id="passwordForm">
                        <div class="form-group">
                            <label for="oldPassword">当前密码</label>
                            <input type="password" id="oldPassword" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="newPassword">新密码</label>
                            <input type="password" id="newPassword" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirmPassword">确认新密码</label>
                            <input type="password" id="confirmPassword" required>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">修改密码</button>
                    </form>
                </div>

                <!-- 通知设置 -->
                <div class="section" id="notification">
                    <h2 class="section-title">通知设置</h2>
                    
                    <div class="info-card">
                        <h4>通知说明</h4>
                        <p>您可以选择接收哪些类型的通知，我们会通过邮箱或站内消息的方式发送给您。</p>
                    </div>
                    
                    <div class="error-message" id="notificationError"></div>
                    <div class="success-message" id="notificationSuccess"></div>
                    
                    <form id="notificationForm">
                        <div class="security-item">
                            <div class="security-info">
                                <h4>交易通知</h4>
                                <p>订单成交、取消等交易相关通知</p>
                            </div>
                            <div class="security-status">
                                <input type="checkbox" id="tradeNotification" checked>
                            </div>
                        </div>
                        
                        <div class="security-item">
                            <div class="security-info">
                                <h4>资产通知</h4>
                                <p>充值、提现等资产变动通知</p>
                            </div>
                            <div class="security-status">
                                <input type="checkbox" id="assetNotification" checked>
                            </div>
                        </div>
                        
                        <div class="security-item">
                            <div class="security-info">
                                <h4>IDO通知</h4>
                                <p>IDO项目开始、认购成功等通知</p>
                            </div>
                            <div class="security-status">
                                <input type="checkbox" id="idoNotification" checked>
                            </div>
                        </div>
                        
                        <div class="security-item">
                            <div class="security-info">
                                <h4>系统通知</h4>
                                <p>系统维护、重要公告等通知</p>
                            </div>
                            <div class="security-status">
                                <input type="checkbox" id="systemNotification" checked>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">保存设置</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        class ProfilePage {
            constructor() {
                this.token = localStorage.getItem('token');
                this.userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
                
                if (!this.token) {
                    window.location.href = '/auth/login.html';
                    return;
                }
                
                this.init();
            }
            
            async init() {
                await this.loadUserProfile();
                this.bindEvents();
                this.updateUserInfo();
            }
            
            bindEvents() {
                // 导航切换
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.showSection(e.target.dataset.section);
                    });
                });
                
                // 表单提交
                document.getElementById('basicForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.updateBasicInfo();
                });
                
                document.getElementById('passwordForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.changePassword();
                });
                
                document.getElementById('notificationForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.updateNotificationSettings();
                });
            }
            
            showSection(sectionId) {
                // 隐藏所有section
                document.querySelectorAll('.section').forEach(section => {
                    section.classList.remove('active');
                });
                
                // 显示目标section
                document.getElementById(sectionId).classList.add('active');
                
                // 更新导航状态
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.remove('active');
                });
                document.querySelector(`[data-section="${sectionId}"]`).classList.add('active');
            }
            
            async loadUserProfile() {
                try {
                    const response = await this.apiRequest('/api/auth/profile');
                    if (response.code === 1) {
                        this.userInfo = response.data;
                        this.updateUserInfo();
                    }
                } catch (error) {
                    console.error('加载用户信息失败:', error);
                }
            }
            
            updateUserInfo() {
                if (this.userInfo.username) {
                    document.getElementById('username').value = this.userInfo.username;
                    document.getElementById('userAvatar').textContent = this.userInfo.username.charAt(0).toUpperCase();
                }
                
                if (this.userInfo.email) {
                    document.getElementById('email').value = this.userInfo.email;
                }
                
                if (this.userInfo.phone) {
                    document.getElementById('phone').value = this.userInfo.phone;
                    document.getElementById('phoneStatus').textContent = '已绑定';
                    document.getElementById('phoneStatus').className = 'status-badge status-enabled';
                }
                
                if (this.userInfo.real_name) {
                    document.getElementById('realName').value = this.userInfo.real_name;
                }
                
                if (this.userInfo.pay_password) {
                    document.getElementById('payPasswordStatus').textContent = '已设置';
                    document.getElementById('payPasswordStatus').className = 'status-badge status-enabled';
                }
            }
            
            async updateBasicInfo() {
                const formData = new FormData(document.getElementById('basicForm'));
                const data = {
                    phone: formData.get('phone'),
                    real_name: formData.get('realName')
                };
                
                try {
                    const response = await this.apiRequest('/api/auth/update-profile', {
                        method: 'POST',
                        body: JSON.stringify(data)
                    });
                    
                    if (response.code === 1) {
                        this.showMessage('basicSuccess', '基本信息更新成功');
                        this.userInfo = { ...this.userInfo, ...data };
                        localStorage.setItem('user_info', JSON.stringify(this.userInfo));
                    } else {
                        this.showMessage('basicError', response.msg || '更新失败');
                    }
                } catch (error) {
                    console.error('更新基本信息失败:', error);
                    this.showMessage('basicError', '网络错误，请稍后重试');
                }
            }
            
            async changePassword() {
                const formData = new FormData(document.getElementById('passwordForm'));
                const data = {
                    old_password: formData.get('oldPassword'),
                    new_password: formData.get('newPassword'),
                    confirm_password: formData.get('confirmPassword')
                };
                
                if (data.new_password !== data.confirm_password) {
                    this.showMessage('passwordError', '两次输入的密码不一致');
                    return;
                }
                
                if (data.new_password.length < 6) {
                    this.showMessage('passwordError', '新密码长度不能少于6位');
                    return;
                }
                
                try {
                    const response = await this.apiRequest('/api/auth/change-password', {
                        method: 'POST',
                        body: JSON.stringify(data)
                    });
                    
                    if (response.code === 1) {
                        this.showMessage('passwordSuccess', '密码修改成功');
                        document.getElementById('passwordForm').reset();
                    } else {
                        this.showMessage('passwordError', response.msg || '修改失败');
                    }
                } catch (error) {
                    console.error('修改密码失败:', error);
                    this.showMessage('passwordError', '网络错误，请稍后重试');
                }
            }
            
            async updateNotificationSettings() {
                const data = {
                    trade_notification: document.getElementById('tradeNotification').checked,
                    asset_notification: document.getElementById('assetNotification').checked,
                    ido_notification: document.getElementById('idoNotification').checked,
                    system_notification: document.getElementById('systemNotification').checked
                };
                
                try {
                    // 这里应该调用更新通知设置的API
                    // 暂时模拟成功
                    this.showMessage('notificationSuccess', '通知设置保存成功');
                } catch (error) {
                    console.error('更新通知设置失败:', error);
                    this.showMessage('notificationError', '网络错误，请稍后重试');
                }
            }
            
            showMessage(elementId, message) {
                const element = document.getElementById(elementId);
                element.textContent = message;
                element.style.display = 'block';
                
                // 3秒后隐藏消息
                setTimeout(() => {
                    element.style.display = 'none';
                }, 3000);
            }
            
            async apiRequest(url, options = {}) {
                const defaultOptions = {
                    headers: {
                        'Authorization': `Bearer ${this.token}`,
                        'Content-Type': 'application/json'
                    }
                };
                
                const response = await fetch(url, { ...defaultOptions, ...options });
                const result = await response.json();
                
                if (response.status === 401) {
                    localStorage.removeItem('token');
                    localStorage.removeItem('user_info');
                    window.location.href = '/auth/login.html';
                    return;
                }
                
                return result;
            }
        }
        
        function showSection(sectionId) {
            profilePage.showSection(sectionId);
        }
        
        function selectAvatar() {
            document.getElementById('avatarUpload').click();
        }
        
        function uploadAvatar() {
            // 头像上传功能
            alert('头像上传功能待实现');
        }
        
        function setPayPassword() {
            const payPassword = prompt('请输入6位数字支付密码：');
            if (payPassword && payPassword.length === 6 && /^\d+$/.test(payPassword)) {
                // 这里应该调用设置支付密码的API
                alert('支付密码设置成功');
                document.getElementById('payPasswordStatus').textContent = '已设置';
                document.getElementById('payPasswordStatus').className = 'status-badge status-enabled';
            } else {
                alert('请输入6位数字密码');
            }
        }
        
        function bindPhone() {
            const phone = prompt('请输入手机号码：');
            if (phone && /^1[3-9]\d{9}$/.test(phone)) {
                // 这里应该调用绑定手机的API
                alert('手机号码绑定成功');
                document.getElementById('phone').value = phone;
                document.getElementById('phoneStatus').textContent = '已绑定';
                document.getElementById('phoneStatus').className = 'status-badge status-enabled';
            } else {
                alert('请输入正确的手机号码');
            }
        }
        
        let profilePage;
        document.addEventListener('DOMContentLoaded', () => {
            profilePage = new ProfilePage();
        });
    </script>
</body>
</html>
