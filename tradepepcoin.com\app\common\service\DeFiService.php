<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\LiquidityPool;
use app\common\model\StakingPool;
use app\common\model\DeFiPosition;
use app\common\model\UserAsset;
use app\common\model\User;
use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

/**
 * DeFi服务类
 */
class DeFiService
{
    // 池子类型
    const POOL_TYPE_LIQUIDITY = 'liquidity';    // 流动性挖矿
    const POOL_TYPE_STAKING = 'staking';        // 质押挖矿
    const POOL_TYPE_LENDING = 'lending';        // 借贷
    const POOL_TYPE_FARMING = 'farming';        // 收益农场

    // 位置状态
    const POSITION_ACTIVE = 1;      // 活跃
    const POSITION_WITHDRAWN = 2;   // 已提取
    const POSITION_EXPIRED = 3;     // 已过期

    /**
     * 创建流动性池
     */
    public function createLiquidityPool(array $data): array
    {
        try {
            // 验证参数
            $validation = $this->validatePoolData($data);
            if (!$validation['code']) {
                return $validation;
            }

            // 创建流动性池
            $poolData = [
                'pool_id' => $this->generatePoolId(),
                'name' => $data['name'],
                'type' => self::POOL_TYPE_LIQUIDITY,
                'token_a' => $data['token_a'],
                'token_b' => $data['token_b'],
                'reward_token' => $data['reward_token'],
                'apy' => $data['apy'],
                'min_deposit' => $data['min_deposit'] ?? 0,
                'max_deposit' => $data['max_deposit'] ?? 0,
                'lock_period' => $data['lock_period'] ?? 0,
                'total_liquidity' => 0,
                'total_rewards' => $data['total_rewards'] ?? 0,
                'start_time' => $data['start_time'],
                'end_time' => $data['end_time'],
                'status' => LiquidityPool::STATUS_ACTIVE
            ];

            $pool = LiquidityPool::create($poolData);

            if ($pool) {
                Log::info("流动性池创建成功", [
                    'pool_id' => $pool->pool_id,
                    'name' => $pool->name
                ]);

                return [
                    'code' => 1,
                    'msg' => '流动性池创建成功',
                    'data' => ['pool_id' => $pool->pool_id]
                ];
            } else {
                return ['code' => 0, 'msg' => '流动性池创建失败'];
            }
        } catch (\Exception $e) {
            Log::error('流动性池创建失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '流动性池创建失败'];
        }
    }

    /**
     * 添加流动性
     */
    public function addLiquidity(int $userId, string $poolId, array $amounts): array
    {
        try {
            // 获取流动性池
            $pool = LiquidityPool::where('pool_id', $poolId)
                                ->where('status', LiquidityPool::STATUS_ACTIVE)
                                ->find();

            if (!$pool) {
                return ['code' => 0, 'msg' => '流动性池不存在或已关闭'];
            }

            // 验证添加时间
            if (time() < strtotime($pool->start_time) || time() > strtotime($pool->end_time)) {
                return ['code' => 0, 'msg' => '不在流动性挖矿时间范围内'];
            }

            // 验证用户资产
            $assetCheck = $this->checkUserAssetsForLiquidity($userId, $pool, $amounts);
            if (!$assetCheck['code']) {
                return $assetCheck;
            }

            // 计算LP代币数量
            $lpTokens = $this->calculateLpTokens($pool, $amounts);

            // 开始事务
            Db::startTrans();
            try {
                // 扣除用户资产
                $this->deductUserAssets($userId, $pool, $amounts);

                // 创建DeFi位置
                $position = DeFiPosition::create([
                    'user_id' => $userId,
                    'pool_id' => $poolId,
                    'type' => self::POOL_TYPE_LIQUIDITY,
                    'token_a_amount' => $amounts['token_a'],
                    'token_b_amount' => $amounts['token_b'],
                    'lp_tokens' => $lpTokens,
                    'entry_price_a' => $this->getCurrentPrice($pool->token_a),
                    'entry_price_b' => $this->getCurrentPrice($pool->token_b),
                    'apy' => $pool->apy,
                    'lock_until' => $pool->lock_period > 0 ? date('Y-m-d H:i:s', time() + $pool->lock_period) : null,
                    'status' => self::POSITION_ACTIVE,
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                // 更新池子总流动性
                $pool->total_liquidity += $lpTokens;
                $pool->participants_count++;
                $pool->save();

                Db::commit();

                Log::info("添加流动性成功", [
                    'user_id' => $userId,
                    'pool_id' => $poolId,
                    'lp_tokens' => $lpTokens
                ]);

                return [
                    'code' => 1,
                    'msg' => '添加流动性成功',
                    'data' => [
                        'position_id' => $position->id,
                        'lp_tokens' => $lpTokens,
                        'estimated_daily_rewards' => $this->calculateDailyRewards($pool, $lpTokens)
                    ]
                ];
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('添加流动性失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '添加流动性失败'];
        }
    }

    /**
     * 移除流动性
     */
    public function removeLiquidity(int $userId, int $positionId, float $percentage = 100): array
    {
        try {
            // 获取用户位置
            $position = DeFiPosition::where('user_id', $userId)
                                   ->where('id', $positionId)
                                   ->where('status', self::POSITION_ACTIVE)
                                   ->find();

            if (!$position) {
                return ['code' => 0, 'msg' => '流动性位置不存在'];
            }

            // 检查锁定期
            if ($position->lock_until && time() < strtotime($position->lock_until)) {
                return ['code' => 0, 'msg' => '流动性仍在锁定期内'];
            }

            // 获取流动性池
            $pool = LiquidityPool::where('pool_id', $position->pool_id)->find();
            if (!$pool) {
                return ['code' => 0, 'msg' => '流动性池不存在'];
            }

            // 计算提取数量
            $withdrawAmount = $position->lp_tokens * ($percentage / 100);
            $tokenAAmount = $position->token_a_amount * ($percentage / 100);
            $tokenBAmount = $position->token_b_amount * ($percentage / 100);

            // 计算收益
            $rewards = $this->calculateAccruedRewards($position, $percentage);

            // 开始事务
            Db::startTrans();
            try {
                // 返还用户资产
                $this->returnUserAssets($userId, $pool, $tokenAAmount, $tokenBAmount, $rewards);

                // 更新位置
                if ($percentage >= 100) {
                    $position->status = self::POSITION_WITHDRAWN;
                    $position->withdrawn_at = date('Y-m-d H:i:s');
                } else {
                    $position->lp_tokens -= $withdrawAmount;
                    $position->token_a_amount -= $tokenAAmount;
                    $position->token_b_amount -= $tokenBAmount;
                }
                $position->save();

                // 更新池子总流动性
                $pool->total_liquidity -= $withdrawAmount;
                if ($percentage >= 100) {
                    $pool->participants_count--;
                }
                $pool->save();

                Db::commit();

                Log::info("移除流动性成功", [
                    'user_id' => $userId,
                    'position_id' => $positionId,
                    'percentage' => $percentage
                ]);

                return [
                    'code' => 1,
                    'msg' => '移除流动性成功',
                    'data' => [
                        'token_a_amount' => $tokenAAmount,
                        'token_b_amount' => $tokenBAmount,
                        'rewards' => $rewards,
                        'remaining_lp_tokens' => $percentage >= 100 ? 0 : $position->lp_tokens
                    ]
                ];
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('移除流动性失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '移除流动性失败'];
        }
    }

    /**
     * 创建质押池
     */
    public function createStakingPool(array $data): array
    {
        try {
            $poolData = [
                'pool_id' => $this->generatePoolId(),
                'name' => $data['name'],
                'stake_token' => $data['stake_token'],
                'reward_token' => $data['reward_token'],
                'apy' => $data['apy'],
                'min_stake' => $data['min_stake'] ?? 0,
                'max_stake' => $data['max_stake'] ?? 0,
                'lock_period' => $data['lock_period'] ?? 0,
                'total_staked' => 0,
                'total_rewards' => $data['total_rewards'] ?? 0,
                'start_time' => $data['start_time'],
                'end_time' => $data['end_time'],
                'status' => StakingPool::STATUS_ACTIVE
            ];

            $pool = StakingPool::create($poolData);

            if ($pool) {
                return [
                    'code' => 1,
                    'msg' => '质押池创建成功',
                    'data' => ['pool_id' => $pool->pool_id]
                ];
            } else {
                return ['code' => 0, 'msg' => '质押池创建失败'];
            }
        } catch (\Exception $e) {
            Log::error('质押池创建失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '质押池创建失败'];
        }
    }

    /**
     * 质押代币
     */
    public function stakeTokens(int $userId, string $poolId, float $amount): array
    {
        try {
            // 获取质押池
            $pool = StakingPool::where('pool_id', $poolId)
                              ->where('status', StakingPool::STATUS_ACTIVE)
                              ->find();

            if (!$pool) {
                return ['code' => 0, 'msg' => '质押池不存在或已关闭'];
            }

            // 验证质押金额
            if ($amount < $pool->min_stake || ($pool->max_stake > 0 && $amount > $pool->max_stake)) {
                return ['code' => 0, 'msg' => '质押金额不在允许范围内'];
            }

            // 验证用户资产
            $userAsset = UserAsset::getUserAsset($userId, $pool->stake_token);
            if (!$userAsset || $userAsset->available < $amount) {
                return ['code' => 0, 'msg' => '余额不足'];
            }

            // 开始事务
            Db::startTrans();
            try {
                // 扣除用户资产
                UserAsset::freezeAsset($userId, $pool->stake_token, $amount);

                // 创建质押位置
                $position = DeFiPosition::create([
                    'user_id' => $userId,
                    'pool_id' => $poolId,
                    'type' => self::POOL_TYPE_STAKING,
                    'stake_amount' => $amount,
                    'apy' => $pool->apy,
                    'lock_until' => $pool->lock_period > 0 ? date('Y-m-d H:i:s', time() + $pool->lock_period) : null,
                    'status' => self::POSITION_ACTIVE,
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                // 更新池子总质押量
                $pool->total_staked += $amount;
                $pool->participants_count++;
                $pool->save();

                Db::commit();

                return [
                    'code' => 1,
                    'msg' => '质押成功',
                    'data' => [
                        'position_id' => $position->id,
                        'estimated_daily_rewards' => $this->calculateStakingDailyRewards($pool, $amount)
                    ]
                ];
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('质押失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '质押失败'];
        }
    }

    /**
     * 解除质押
     */
    public function unstakeTokens(int $userId, int $positionId): array
    {
        try {
            // 获取质押位置
            $position = DeFiPosition::where('user_id', $userId)
                                   ->where('id', $positionId)
                                   ->where('type', self::POOL_TYPE_STAKING)
                                   ->where('status', self::POSITION_ACTIVE)
                                   ->find();

            if (!$position) {
                return ['code' => 0, 'msg' => '质押位置不存在'];
            }

            // 检查锁定期
            if ($position->lock_until && time() < strtotime($position->lock_until)) {
                return ['code' => 0, 'msg' => '质押仍在锁定期内'];
            }

            // 获取质押池
            $pool = StakingPool::where('pool_id', $position->pool_id)->find();
            if (!$pool) {
                return ['code' => 0, 'msg' => '质押池不存在'];
            }

            // 计算收益
            $rewards = $this->calculateStakingRewards($position);

            // 开始事务
            Db::startTrans();
            try {
                // 返还质押资产
                UserAsset::unfreezeAsset($userId, $pool->stake_token, $position->stake_amount);

                // 发放收益
                if ($rewards > 0) {
                    UserAsset::addAsset($userId, $pool->reward_token, $rewards);
                }

                // 更新位置状态
                $position->status = self::POSITION_WITHDRAWN;
                $position->withdrawn_at = date('Y-m-d H:i:s');
                $position->total_rewards = $rewards;
                $position->save();

                // 更新池子统计
                $pool->total_staked -= $position->stake_amount;
                $pool->participants_count--;
                $pool->save();

                Db::commit();

                return [
                    'code' => 1,
                    'msg' => '解除质押成功',
                    'data' => [
                        'stake_amount' => $position->stake_amount,
                        'rewards' => $rewards
                    ]
                ];
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('解除质押失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '解除质押失败'];
        }
    }

    /**
     * 收获收益
     */
    public function harvestRewards(int $userId, int $positionId): array
    {
        try {
            $position = DeFiPosition::where('user_id', $userId)
                                   ->where('id', $positionId)
                                   ->where('status', self::POSITION_ACTIVE)
                                   ->find();

            if (!$position) {
                return ['code' => 0, 'msg' => '位置不存在'];
            }

            // 计算可收获的收益
            $rewards = $this->calculateHarvestableRewards($position);

            if ($rewards <= 0) {
                return ['code' => 0, 'msg' => '暂无可收获的收益'];
            }

            // 获取池子信息
            $pool = $this->getPoolByPosition($position);
            if (!$pool) {
                return ['code' => 0, 'msg' => '池子不存在'];
            }

            // 开始事务
            Db::startTrans();
            try {
                // 发放收益
                $rewardToken = $position->type === self::POOL_TYPE_LIQUIDITY ? 
                    $pool->reward_token : $pool->reward_token;
                
                UserAsset::addAsset($userId, $rewardToken, $rewards);

                // 更新位置的收益记录
                $position->total_rewards += $rewards;
                $position->last_harvest_at = date('Y-m-d H:i:s');
                $position->save();

                Db::commit();

                return [
                    'code' => 1,
                    'msg' => '收益收获成功',
                    'data' => [
                        'rewards' => $rewards,
                        'reward_token' => $rewardToken
                    ]
                ];
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('收获收益失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '收获收益失败'];
        }
    }

    /**
     * 获取用户DeFi位置
     */
    public function getUserPositions(int $userId, string $type = ''): array
    {
        $query = DeFiPosition::where('user_id', $userId);
        
        if ($type) {
            $query->where('type', $type);
        }

        $positions = $query->with(['liquidityPool', 'stakingPool'])
                          ->order('created_at', 'desc')
                          ->select();

        $result = [];
        foreach ($positions as $position) {
            $positionData = $position->toArray();
            
            // 计算当前收益
            $positionData['current_rewards'] = $this->calculateCurrentRewards($position);
            $positionData['harvestable_rewards'] = $this->calculateHarvestableRewards($position);
            
            // 计算APY
            $positionData['current_apy'] = $this->calculateCurrentApy($position);
            
            $result[] = $positionData;
        }

        return [
            'code' => 1,
            'data' => $result
        ];
    }

    /**
     * 获取DeFi池子列表
     */
    public function getPools(string $type = '', array $filters = []): array
    {
        $pools = [];

        if (!$type || $type === self::POOL_TYPE_LIQUIDITY) {
            $liquidityPools = LiquidityPool::where('status', LiquidityPool::STATUS_ACTIVE)
                                         ->order('apy', 'desc')
                                         ->select();
            
            foreach ($liquidityPools as $pool) {
                $pools[] = array_merge($pool->toArray(), [
                    'type' => self::POOL_TYPE_LIQUIDITY,
                    'tvl' => $this->calculateTVL($pool),
                    'daily_volume' => $this->calculateDailyVolume($pool->pool_id)
                ]);
            }
        }

        if (!$type || $type === self::POOL_TYPE_STAKING) {
            $stakingPools = StakingPool::where('status', StakingPool::STATUS_ACTIVE)
                                     ->order('apy', 'desc')
                                     ->select();
            
            foreach ($stakingPools as $pool) {
                $pools[] = array_merge($pool->toArray(), [
                    'type' => self::POOL_TYPE_STAKING,
                    'tvl' => $pool->total_staked * $this->getCurrentPrice($pool->stake_token)
                ]);
            }
        }

        // 按APY排序
        usort($pools, function($a, $b) {
            return $b['apy'] <=> $a['apy'];
        });

        return [
            'code' => 1,
            'data' => $pools
        ];
    }

    /**
     * 验证池子数据
     */
    private function validatePoolData(array $data): array
    {
        if (empty($data['name'])) {
            return ['code' => 0, 'msg' => '池子名称不能为空'];
        }

        if (empty($data['token_a']) || empty($data['token_b'])) {
            return ['code' => 0, 'msg' => '代币对不能为空'];
        }

        if (empty($data['apy']) || $data['apy'] <= 0) {
            return ['code' => 0, 'msg' => 'APY必须大于0'];
        }

        return ['code' => 1, 'msg' => '验证通过'];
    }

    /**
     * 检查用户资产
     */
    private function checkUserAssetsForLiquidity(int $userId, LiquidityPool $pool, array $amounts): array
    {
        $assetA = UserAsset::getUserAsset($userId, $pool->token_a);
        $assetB = UserAsset::getUserAsset($userId, $pool->token_b);

        if (!$assetA || $assetA->available < $amounts['token_a']) {
            return ['code' => 0, 'msg' => $pool->token_a . '余额不足'];
        }

        if (!$assetB || $assetB->available < $amounts['token_b']) {
            return ['code' => 0, 'msg' => $pool->token_b . '余额不足'];
        }

        return ['code' => 1, 'msg' => '资产检查通过'];
    }

    /**
     * 计算LP代币数量
     */
    private function calculateLpTokens(LiquidityPool $pool, array $amounts): float
    {
        // 简化计算，实际应该根据AMM公式计算
        $priceA = $this->getCurrentPrice($pool->token_a);
        $priceB = $this->getCurrentPrice($pool->token_b);
        
        $valueA = $amounts['token_a'] * $priceA;
        $valueB = $amounts['token_b'] * $priceB;
        
        return sqrt($valueA * $valueB);
    }

    /**
     * 计算每日收益
     */
    private function calculateDailyRewards(LiquidityPool $pool, float $lpTokens): float
    {
        $dailyApy = $pool->apy / 365;
        $totalValue = $lpTokens * $this->getLpTokenPrice($pool);
        
        return $totalValue * ($dailyApy / 100);
    }

    /**
     * 计算质押每日收益
     */
    private function calculateStakingDailyRewards(StakingPool $pool, float $amount): float
    {
        $dailyApy = $pool->apy / 365;
        $totalValue = $amount * $this->getCurrentPrice($pool->stake_token);
        
        return $totalValue * ($dailyApy / 100);
    }

    /**
     * 其他辅助方法
     */
    private function generatePoolId(): string
    {
        return 'POOL' . date('YmdHis') . mt_rand(1000, 9999);
    }

    private function getCurrentPrice(string $token): float
    {
        // 模拟价格，实际应该从价格预言机获取
        $prices = [
            'BTC' => 45000,
            'ETH' => 3000,
            'USDT' => 1,
            'USDC' => 1,
            'BNB' => 300
        ];
        
        return $prices[$token] ?? 1;
    }

    private function getLpTokenPrice(LiquidityPool $pool): float
    {
        // 简化计算LP代币价格
        return 1;
    }

    private function calculateTVL($pool): float
    {
        if ($pool instanceof LiquidityPool) {
            return $pool->total_liquidity * $this->getLpTokenPrice($pool);
        } else {
            return $pool->total_staked * $this->getCurrentPrice($pool->stake_token);
        }
    }

    private function calculateDailyVolume(string $poolId): float
    {
        // 模拟24小时交易量
        return rand(100000, 1000000);
    }

    private function deductUserAssets(int $userId, LiquidityPool $pool, array $amounts): void
    {
        UserAsset::freezeAsset($userId, $pool->token_a, $amounts['token_a']);
        UserAsset::freezeAsset($userId, $pool->token_b, $amounts['token_b']);
    }

    private function returnUserAssets(int $userId, LiquidityPool $pool, float $tokenAAmount, float $tokenBAmount, float $rewards): void
    {
        UserAsset::unfreezeAsset($userId, $pool->token_a, $tokenAAmount);
        UserAsset::unfreezeAsset($userId, $pool->token_b, $tokenBAmount);
        
        if ($rewards > 0) {
            UserAsset::addAsset($userId, $pool->reward_token, $rewards);
        }
    }

    private function calculateAccruedRewards(DeFiPosition $position, float $percentage): float
    {
        $days = (time() - strtotime($position->created_at)) / 86400;
        $dailyRate = $position->apy / 365 / 100;
        $totalValue = $position->lp_tokens * $this->getLpTokenPrice($position->liquidityPool);
        
        return $totalValue * $dailyRate * $days * ($percentage / 100);
    }

    private function calculateStakingRewards(DeFiPosition $position): float
    {
        $days = (time() - strtotime($position->created_at)) / 86400;
        $dailyRate = $position->apy / 365 / 100;
        
        return $position->stake_amount * $dailyRate * $days;
    }

    private function calculateCurrentRewards(DeFiPosition $position): float
    {
        if ($position->type === self::POOL_TYPE_STAKING) {
            return $this->calculateStakingRewards($position);
        } else {
            return $this->calculateAccruedRewards($position, 100);
        }
    }

    private function calculateHarvestableRewards(DeFiPosition $position): float
    {
        $totalRewards = $this->calculateCurrentRewards($position);
        return max(0, $totalRewards - $position->total_rewards);
    }

    private function calculateCurrentApy(DeFiPosition $position): float
    {
        // 根据市场情况动态调整APY
        return $position->apy * (rand(90, 110) / 100);
    }

    private function getPoolByPosition(DeFiPosition $position)
    {
        if ($position->type === self::POOL_TYPE_LIQUIDITY) {
            return LiquidityPool::where('pool_id', $position->pool_id)->find();
        } else {
            return StakingPool::where('pool_id', $position->pool_id)->find();
        }
    }
}
