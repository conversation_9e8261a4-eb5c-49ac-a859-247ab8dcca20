<?php
declare(strict_types=1);

namespace app\agent\controller;

use app\BaseController;

class Index extends BaseController
{
    public function index()
    {
        return view('agent/index');
    }
    
    public function users()
    {
        return view('agent/users');
    }
    
    public function orders()
    {
        return view('agent/orders');
    }
    
    public function commissions()
    {
        return view('agent/commissions');
    }
}

    public function simple()
    {
        return "Hello from Index controller";
    }

