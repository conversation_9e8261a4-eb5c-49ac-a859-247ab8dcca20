<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

/**
 * 代理服务类 - 完整版
 * 管理代理系统的所有功能
 */
class AgentService
{
    // 代理类型
    const AGENT_TYPE_NORMAL = 1;    // 普通代理
    const AGENT_TYPE_SENIOR = 2;    // 高级代理
    const AGENT_TYPE_PARTNER = 3;   // 合作伙伴

    // 佣金类型
    const COMMISSION_TRADE = 'trade';       // 交易佣金
    const COMMISSION_DEPOSIT = 'deposit';   // 充值佣金
    const COMMISSION_WITHDRAW = 'withdraw'; // 提现佣金

    /**
     * 代理登录
     */
    public function login(string $username, string $password): array
    {
        try {
            // 查找代理用户
            $agent = Db::name('gvd_users')
                ->where('username', $username)
                ->where('user_type', 2) // 代理类型
                ->where('status', 1)
                ->find();

            if (!$agent) {
                return ['code' => 0, 'msg' => '代理账户不存在或已被禁用'];
            }

            // 验证密码
            if (!password_verify($password, $agent['password'])) {
                return ['code' => 0, 'msg' => '密码错误'];
            }

            // 更新登录信息
            Db::name('gvd_users')->where('id', $agent['id'])->update([
                'last_login_time' => date('Y-m-d H:i:s'),
                'last_login_ip' => request()->ip(),
                'login_count' => Db::raw('login_count + 1')
            ]);

            // 记录登录日志
            $this->logAgentAction($agent['id'], 'login', '代理登录', [
                'ip' => request()->ip(),
                'user_agent' => request()->header('user-agent')
            ]);

            // 生成token
            $token = $this->generateToken($agent['id']);

            return [
                'code' => 1,
                'msg' => '登录成功',
                'data' => [
                    'agent_info' => $agent,
                    'token' => $token
                ]
            ];

        } catch (\Exception $e) {
            Log::error('代理登录失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '登录失败'];
        }
    }

    /**
     * 获取代理信息
     */
    public function getAgentInfo(int $agentId): array
    {
        try {
            $cacheKey = 'agent_info:' . $agentId;
            $agent = Cache::get($cacheKey);
            
            if (!$agent) {
                $agent = Db::name('gvd_users')
                    ->where('id', $agentId)
                    ->where('user_type', 2)
                    ->find();
                
                if ($agent) {
                    // 获取代理配置
                    $configs = Db::name('gvd_agent_config')
                        ->where('agent_id', $agentId)
                        ->column('config_value', 'config_key');
                    
                    $agent['configs'] = $configs;
                    Cache::set($cacheKey, $agent, 300);
                }
            }

            return [
                'code' => 1,
                'msg' => '获取成功',
                'data' => $agent ?: []
            ];

        } catch (\Exception $e) {
            Log::error('获取代理信息失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 获取代理统计数据
     */
    public function getAgentStats(int $agentId): array
    {
        try {
            $cacheKey = 'agent_stats:' . $agentId;
            $stats = Cache::get($cacheKey);
            
            if (!$stats) {
                // 团队用户统计
                $totalUsers = Db::name('gvd_users')
                    ->where('inviter_id', $agentId)
                    ->count();
                
                $todayUsers = Db::name('gvd_users')
                    ->where('inviter_id', $agentId)
                    ->whereTime('created_at', 'today')
                    ->count();
                
                $activeUsers = Db::name('gvd_users')
                    ->where('inviter_id', $agentId)
                    ->where('last_login_time', '>=', date('Y-m-d H:i:s', time() - 86400))
                    ->count();

                // 团队交易统计
                $teamUserIds = Db::name('gvd_users')
                    ->where('inviter_id', $agentId)
                    ->column('id');

                $totalOrders = 0;
                $todayOrders = 0;
                $totalVolume = 0;
                $todayVolume = 0;

                if (!empty($teamUserIds)) {
                    // 期货订单统计
                    $totalOrders = Db::name('gvd_futures_orders')
                        ->whereIn('user_id', $teamUserIds)
                        ->count();
                    
                    $todayOrders = Db::name('gvd_futures_orders')
                        ->whereIn('user_id', $teamUserIds)
                        ->whereTime('created_at', 'today')
                        ->count();
                    
                    $totalVolume = Db::name('gvd_futures_orders')
                        ->whereIn('user_id', $teamUserIds)
                        ->sum('amount');
                    
                    $todayVolume = Db::name('gvd_futures_orders')
                        ->whereIn('user_id', $teamUserIds)
                        ->whereTime('created_at', 'today')
                        ->sum('amount');
                }

                // 佣金统计
                $totalCommission = Db::name('gvd_agent_commission')
                    ->where('agent_id', $agentId)
                    ->where('status', 1)
                    ->sum('amount');
                
                $todayCommission = Db::name('gvd_agent_commission')
                    ->where('agent_id', $agentId)
                    ->where('status', 1)
                    ->whereTime('created_at', 'today')
                    ->sum('amount');

                $stats = [
                    'team_stats' => [
                        'total_users' => $totalUsers,
                        'today_users' => $todayUsers,
                        'active_users' => $activeUsers
                    ],
                    'trade_stats' => [
                        'total_orders' => $totalOrders,
                        'today_orders' => $todayOrders,
                        'total_volume' => $totalVolume,
                        'today_volume' => $todayVolume
                    ],
                    'commission_stats' => [
                        'total_commission' => $totalCommission,
                        'today_commission' => $todayCommission
                    ]
                ];

                Cache::set($cacheKey, $stats, 300);
            }

            return [
                'code' => 1,
                'msg' => '获取成功',
                'data' => $stats
            ];

        } catch (\Exception $e) {
            Log::error('获取代理统计失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 获取团队用户列表
     */
    public function getTeamUsers(int $agentId, array $params = []): array
    {
        try {
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 20;
            
            $query = Db::name('gvd_users')
                ->where('inviter_id', $agentId);

            // 用户类型筛选
            if (!empty($params['user_type'])) {
                $query->where('user_type', $params['user_type']);
            }

            // 状态筛选
            if (isset($params['status'])) {
                $query->where('status', $params['status']);
            }

            // 注册时间筛选
            if (!empty($params['start_date'])) {
                $query->where('created_at', '>=', $params['start_date']);
            }
            if (!empty($params['end_date'])) {
                $query->where('created_at', '<=', $params['end_date']);
            }

            $total = $query->count();
            $users = $query->page($page, $limit)
                ->order('id desc')
                ->select()
                ->toArray();

            // 获取用户资产和交易统计
            foreach ($users as &$user) {
                // 资产信息
                $assets = Db::name('gvd_user_assets')
                    ->where('user_id', $user['id'])
                    ->where('coin_symbol', 'USDT')
                    ->find();
                
                $user['balance'] = $assets ? $assets['available'] : 0;
                $user['frozen'] = $assets ? $assets['frozen'] : 0;

                // 交易统计
                $orderStats = Db::name('gvd_futures_orders')
                    ->where('user_id', $user['id'])
                    ->field('COUNT(*) as total_orders, SUM(amount) as total_volume, SUM(pnl) as total_profit')
                    ->find();

                $user['total_orders'] = $orderStats['total_orders'] ?? 0;
                $user['total_volume'] = $orderStats['total_volume'] ?? 0;
                $user['total_profit'] = $orderStats['total_profit'] ?? 0;
            }

            return [
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'list' => $users,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取团队用户失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 创建团队用户
     */
    public function createTeamUser(int $agentId, array $data): array
    {
        try {
            Db::startTrans();

            // 检查用户名是否存在
            $existUser = Db::name('gvd_users')->where('username', $data['username'])->find();
            if ($existUser) {
                throw new \Exception('用户名已存在');
            }

            // 生成邀请码
            $inviteCode = $this->generateInviteCode();

            // 创建用户数据
            $userData = [
                'username' => $data['username'],
                'password' => password_hash($data['password'], PASSWORD_DEFAULT),
                'email' => $data['email'] ?? '',
                'phone' => $data['phone'] ?? '',
                'user_type' => $data['user_type'] ?? 1, // 默认普通用户
                'inviter_id' => $agentId,
                'invite_code' => $inviteCode,
                'status' => 1,
                'register_ip' => request()->ip(),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $userId = Db::name('gvd_users')->insertGetId($userData);

            // 初始化用户资产
            $this->initUserAssets($userId);

            // 如果设置了初始余额
            if (!empty($data['initial_balance']) && $data['initial_balance'] > 0) {
                Db::name('gvd_user_assets')
                    ->where('user_id', $userId)
                    ->where('coin_symbol', 'USDT')
                    ->update([
                        'available' => $data['initial_balance'],
                        'total' => $data['initial_balance']
                    ]);

                // 记录财务流水
                Db::name('gvd_financial_records')->insert([
                    'user_id' => $userId,
                    'coin_symbol' => 'USDT',
                    'amount' => $data['initial_balance'],
                    'type' => 'agent_gift',
                    'remark' => '代理赠送初始余额',
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }

            // 记录代理操作日志
            $this->logAgentAction($agentId, 'create_user', '创建团队用户', [
                'user_id' => $userId,
                'username' => $data['username'],
                'initial_balance' => $data['initial_balance'] ?? 0
            ]);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '用户创建成功',
                'data' => [
                    'user_id' => $userId,
                    'username' => $data['username'],
                    'invite_code' => $inviteCode
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('创建团队用户失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '创建失败：' . $e->getMessage()];
        }
    }

    /**
     * 控制用户订单
     */
    public function controlUserOrder(int $agentId, int $orderId, string $controlType, array $params = []): array
    {
        try {
            Db::startTrans();

            // 获取订单信息
            $order = Db::name('gvd_futures_orders')
                ->where('id', $orderId)
                ->where('status', 'open')
                ->find();

            if (!$order) {
                throw new \Exception('订单不存在或已平仓');
            }

            // 验证订单归属
            $user = Db::name('gvd_users')
                ->where('id', $order['user_id'])
                ->where('inviter_id', $agentId)
                ->find();

            if (!$user) {
                throw new \Exception('无权限操作此订单');
            }

            // 获取当前价格
            $currentPrice = $this->getCurrentPrice($order['symbol']);
            $profitLoss = 0;

            // 根据控制类型计算结果
            switch ($controlType) {
                case 'win':
                    $profitLoss = abs($order['amount'] * 0.1); // 盈利10%
                    break;
                case 'lose':
                    $profitLoss = -abs($order['amount'] * 0.1); // 亏损10%
                    break;
                case 'liquidate':
                    $profitLoss = -$order['margin']; // 爆仓
                    break;
                default:
                    throw new \Exception('无效的控制类型');
            }

            // 更新订单状态
            Db::name('gvd_futures_orders')->where('id', $orderId)->update([
                'close_price' => $currentPrice,
                'pnl' => $profitLoss,
                'status' => $controlType === 'liquidate' ? 'liquidated' : 'closed',
                'close_time' => time(),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // 结算资产
            $finalAmount = $order['margin'] + $profitLoss;

            // 解冻保证金
            Db::name('gvd_user_assets')
                ->where('user_id', $order['user_id'])
                ->where('coin_symbol', 'USDT')
                ->dec('frozen', $order['margin']);

            // 添加最终金额
            if ($finalAmount > 0) {
                Db::name('gvd_user_assets')
                    ->where('user_id', $order['user_id'])
                    ->where('coin_symbol', 'USDT')
                    ->inc('available', $finalAmount);
            }

            // 记录财务流水
            Db::name('gvd_financial_records')->insert([
                'user_id' => $order['user_id'],
                'coin_symbol' => 'USDT',
                'amount' => $profitLoss,
                'type' => 'agent_control',
                'remark' => '代理控制订单: ' . $controlType,
                'order_id' => $orderId,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            // 记录代理操作日志
            $this->logAgentAction($agentId, 'control_order', '控制用户订单', [
                'order_id' => $orderId,
                'user_id' => $order['user_id'],
                'control_type' => $controlType,
                'profit_loss' => $profitLoss
            ]);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '订单控制成功',
                'data' => [
                    'order_id' => $orderId,
                    'control_type' => $controlType,
                    'profit_loss' => $profitLoss,
                    'final_amount' => $finalAmount
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('控制用户订单失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '控制失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取佣金记录
     */
    public function getCommissionRecords(int $agentId, array $params = []): array
    {
        try {
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 20;

            $query = Db::name('gvd_agent_commission ac')
                ->join('gvd_users u', 'ac.user_id = u.id')
                ->field('ac.*, u.username')
                ->where('ac.agent_id', $agentId);

            // 佣金类型筛选
            if (!empty($params['commission_type'])) {
                $query->where('ac.commission_type', $params['commission_type']);
            }

            // 状态筛选
            if (isset($params['status'])) {
                $query->where('ac.status', $params['status']);
            }

            // 时间筛选
            if (!empty($params['start_date'])) {
                $query->where('ac.created_at', '>=', $params['start_date']);
            }
            if (!empty($params['end_date'])) {
                $query->where('ac.created_at', '<=', $params['end_date']);
            }

            $total = $query->count();
            $records = $query->page($page, $limit)
                ->order('ac.id desc')
                ->select()
                ->toArray();

            return [
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'list' => $records,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取佣金记录失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 申请提现
     */
    public function applyWithdraw(int $agentId, array $data): array
    {
        try {
            Db::startTrans();

            // 检查提现金额
            $amount = $data['amount'];
            $minAmount = $this->getAgentConfig($agentId, 'min_withdraw_amount', 100);

            if ($amount < $minAmount) {
                throw new \Exception('提现金额不能少于' . $minAmount);
            }

            // 检查可用佣金余额
            $availableCommission = Db::name('gvd_agent_commission')
                ->where('agent_id', $agentId)
                ->where('status', 1)
                ->sum('amount');

            if ($amount > $availableCommission) {
                throw new \Exception('可用佣金余额不足');
            }

            // 计算手续费
            $feeRate = $this->getAgentConfig($agentId, 'withdraw_fee_rate', 0.005);
            $fee = $amount * $feeRate;
            $actualAmount = $amount - $fee;

            // 创建提现记录
            $withdrawData = [
                'agent_id' => $agentId,
                'amount' => $amount,
                'coin_symbol' => $data['coin_symbol'] ?? 'USDT',
                'withdraw_address' => $data['withdraw_address'],
                'fee' => $fee,
                'actual_amount' => $actualAmount,
                'status' => 0, // 待审核
                'remark' => $data['remark'] ?? '',
                'created_at' => date('Y-m-d H:i:s')
            ];

            $withdrawId = Db::name('gvd_agent_withdraws')->insertGetId($withdrawData);

            // 记录代理操作日志
            $this->logAgentAction($agentId, 'apply_withdraw', '申请提现', [
                'withdraw_id' => $withdrawId,
                'amount' => $amount,
                'fee' => $fee,
                'actual_amount' => $actualAmount
            ]);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '提现申请提交成功',
                'data' => [
                    'withdraw_id' => $withdrawId,
                    'amount' => $amount,
                    'fee' => $fee,
                    'actual_amount' => $actualAmount
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('申请提现失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '申请失败：' . $e->getMessage()];
        }
    }
