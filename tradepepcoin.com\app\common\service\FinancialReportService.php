<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\User;
use app\common\model\UserAsset;
use app\common\model\Order;
use app\common\model\ContractOrder;
use app\common\model\DepositRecord;
use app\common\model\WithdrawRecord;
use app\common\model\FinancialRecord;
use think\facade\Db;
use think\facade\Log;

/**
 * 财务报表服务
 */
class FinancialReportService
{
    /**
     * 获取平台总览报表
     */
    public function getPlatformOverview(array $filters = []): array
    {
        try {
            $startDate = $filters['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
            $endDate = $filters['end_date'] ?? date('Y-m-d');

            $report = [];

            // 用户统计（只统计正式用户）
            $userQuery = User::where('user_type', UserService::USER_TYPE_FORMAL);
            $report['total_formal_users'] = $userQuery->count();
            $report['active_formal_users'] = $userQuery->where('status', 1)->count();
            $report['new_formal_users'] = $userQuery->where('created_at', '>=', $startDate)
                                                   ->where('created_at', '<=', $endDate . ' 23:59:59')
                                                   ->count();

            // 测试用户统计（单独统计，不在主要统计中显示）
            $testUserQuery = User::where('user_type', UserService::USER_TYPE_TEST);
            $report['test_users'] = [
                'total' => $testUserQuery->count(),
                'active' => $testUserQuery->where('status', 1)->count(),
                'new' => $testUserQuery->where('created_at', '>=', $startDate)
                                      ->where('created_at', '<=', $endDate . ' 23:59:59')
                                      ->count()
            ];

            // 资产统计
            $formalUserIds = $userQuery->column('id');
            if (!empty($formalUserIds)) {
                $report['total_assets'] = UserAsset::whereIn('user_id', $formalUserIds)->sum('total');
                $report['available_assets'] = UserAsset::whereIn('user_id', $formalUserIds)->sum('available');
                $report['frozen_assets'] = UserAsset::whereIn('user_id', $formalUserIds)->sum('frozen');
            } else {
                $report['total_assets'] = 0;
                $report['available_assets'] = 0;
                $report['frozen_assets'] = 0;
            }

            // 交易统计
            $tradeQuery = Order::where('created_at', '>=', $startDate)
                              ->where('created_at', '<=', $endDate . ' 23:59:59');
            
            if (!empty($formalUserIds)) {
                $tradeQuery->whereIn('user_id', $formalUserIds);
            }

            $report['total_trades'] = $tradeQuery->count();
            $report['trade_volume'] = $tradeQuery->sum('total');
            $report['trade_fee'] = $tradeQuery->sum('fee');

            // 合约统计
            $contractQuery = ContractOrder::where('created_at', '>=', $startDate)
                                         ->where('created_at', '<=', $endDate . ' 23:59:59');
            
            if (!empty($formalUserIds)) {
                $contractQuery->whereIn('user_id', $formalUserIds);
            }

            $report['total_contracts'] = $contractQuery->count();
            $report['contract_volume'] = $contractQuery->sum('amount');
            
            // 合约盈亏统计
            $winContracts = $contractQuery->where('status', ContractService::STATUS_WIN)->sum('profit_amount');
            $loseContracts = $contractQuery->where('status', ContractService::STATUS_LOSE)->sum('amount');
            $report['contract_profit'] = $loseContracts - $winContracts; // 平台盈利

            // 充值统计
            $depositQuery = DepositRecord::where('status', 1)
                                        ->where('created_at', '>=', $startDate)
                                        ->where('created_at', '<=', $endDate . ' 23:59:59');
            
            if (!empty($formalUserIds)) {
                $depositQuery->whereIn('user_id', $formalUserIds);
            }

            $report['total_deposits'] = $depositQuery->count();
            $report['deposit_amount'] = $depositQuery->sum('amount');

            // 提币统计
            $withdrawQuery = WithdrawRecord::where('status', 1)
                                          ->where('created_at', '>=', $startDate)
                                          ->where('created_at', '<=', $endDate . ' 23:59:59');
            
            if (!empty($formalUserIds)) {
                $withdrawQuery->whereIn('user_id', $formalUserIds);
            }

            $report['total_withdraws'] = $withdrawQuery->count();
            $report['withdraw_amount'] = $withdrawQuery->sum('amount');
            $report['withdraw_fee'] = $withdrawQuery->sum('fee');

            // 净流入
            $report['net_inflow'] = $report['deposit_amount'] - $report['withdraw_amount'];

            return [
                'code' => 1,
                'data' => $report
            ];

        } catch (\Exception $e) {
            Log::error('获取平台总览报表失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取报表失败'];
        }
    }

    /**
     * 获取用户资产报表
     */
    public function getUserAssetReport(array $filters = []): array
    {
        try {
            $query = UserAsset::alias('ua')
                             ->join('ce_users u', 'ua.user_id = u.id')
                             ->where('u.user_type', UserService::USER_TYPE_FORMAL); // 只统计正式用户

            // 筛选条件
            if (!empty($filters['coin_symbol'])) {
                $query->where('ua.coin_symbol', $filters['coin_symbol']);
            }

            if (!empty($filters['min_amount'])) {
                $query->where('ua.total', '>=', $filters['min_amount']);
            }

            if (!empty($filters['agent_id'])) {
                $query->where('u.agent_id', $filters['agent_id']);
            }

            $assets = $query->field('ua.*, u.username, u.email, u.agent_id')
                           ->order('ua.total', 'desc')
                           ->limit($filters['limit'] ?? 100)
                           ->select();

            // 统计汇总
            $summary = [
                'total_users' => $query->group('ua.user_id')->count(),
                'total_amount' => $query->sum('ua.total'),
                'available_amount' => $query->sum('ua.available'),
                'frozen_amount' => $query->sum('ua.frozen')
            ];

            return [
                'code' => 1,
                'data' => [
                    'list' => $assets->toArray(),
                    'summary' => $summary
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取用户资产报表失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取报表失败'];
        }
    }

    /**
     * 获取交易报表
     */
    public function getTradeReport(array $filters = []): array
    {
        try {
            $startDate = $filters['start_date'] ?? date('Y-m-d');
            $endDate = $filters['end_date'] ?? date('Y-m-d');

            // 按日期统计
            $dailyStats = Db::query("
                SELECT 
                    DATE(o.created_at) as date,
                    COUNT(*) as trade_count,
                    SUM(o.total) as trade_volume,
                    SUM(o.fee) as trade_fee
                FROM ce_orders o
                JOIN ce_users u ON o.user_id = u.id
                WHERE u.user_type = ? 
                AND o.created_at >= ? 
                AND o.created_at <= ?
                AND o.status = 1
                GROUP BY DATE(o.created_at)
                ORDER BY date DESC
            ", [UserService::USER_TYPE_FORMAL, $startDate, $endDate . ' 23:59:59']);

            // 按交易对统计
            $symbolStats = Db::query("
                SELECT 
                    o.symbol,
                    COUNT(*) as trade_count,
                    SUM(o.total) as trade_volume,
                    SUM(o.fee) as trade_fee
                FROM ce_orders o
                JOIN ce_users u ON o.user_id = u.id
                WHERE u.user_type = ? 
                AND o.created_at >= ? 
                AND o.created_at <= ?
                AND o.status = 1
                GROUP BY o.symbol
                ORDER BY trade_volume DESC
            ", [UserService::USER_TYPE_FORMAL, $startDate, $endDate . ' 23:59:59']);

            // 按用户统计（前50名）
            $userStats = Db::query("
                SELECT 
                    u.username,
                    u.email,
                    COUNT(*) as trade_count,
                    SUM(o.total) as trade_volume,
                    SUM(o.fee) as trade_fee
                FROM ce_orders o
                JOIN ce_users u ON o.user_id = u.id
                WHERE u.user_type = ? 
                AND o.created_at >= ? 
                AND o.created_at <= ?
                AND o.status = 1
                GROUP BY o.user_id
                ORDER BY trade_volume DESC
                LIMIT 50
            ", [UserService::USER_TYPE_FORMAL, $startDate, $endDate . ' 23:59:59']);

            return [
                'code' => 1,
                'data' => [
                    'daily_stats' => $dailyStats,
                    'symbol_stats' => $symbolStats,
                    'user_stats' => $userStats
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取交易报表失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取报表失败'];
        }
    }

    /**
     * 获取合约报表
     */
    public function getContractReport(array $filters = []): array
    {
        try {
            $startDate = $filters['start_date'] ?? date('Y-m-d');
            $endDate = $filters['end_date'] ?? date('Y-m-d');

            // 按日期统计
            $dailyStats = Db::query("
                SELECT 
                    DATE(co.created_at) as date,
                    COUNT(*) as contract_count,
                    SUM(co.amount) as contract_volume,
                    SUM(CASE WHEN co.status = ? THEN co.profit_amount ELSE 0 END) as user_profit,
                    SUM(CASE WHEN co.status = ? THEN co.amount ELSE 0 END) as user_loss,
                    (SUM(CASE WHEN co.status = ? THEN co.amount ELSE 0 END) - 
                     SUM(CASE WHEN co.status = ? THEN co.profit_amount ELSE 0 END)) as platform_profit
                FROM ce_contract_orders co
                JOIN ce_users u ON co.user_id = u.id
                WHERE u.user_type = ? 
                AND co.created_at >= ? 
                AND co.created_at <= ?
                GROUP BY DATE(co.created_at)
                ORDER BY date DESC
            ", [
                ContractService::STATUS_WIN, 
                ContractService::STATUS_LOSE,
                ContractService::STATUS_LOSE,
                ContractService::STATUS_WIN,
                UserService::USER_TYPE_FORMAL, 
                $startDate, 
                $endDate . ' 23:59:59'
            ]);

            // 按交易对统计
            $symbolStats = Db::query("
                SELECT 
                    co.symbol,
                    COUNT(*) as contract_count,
                    SUM(co.amount) as contract_volume,
                    COUNT(CASE WHEN co.status = ? THEN 1 END) as win_count,
                    COUNT(CASE WHEN co.status = ? THEN 1 END) as lose_count,
                    ROUND(COUNT(CASE WHEN co.status = ? THEN 1 END) * 100.0 / 
                          (COUNT(CASE WHEN co.status = ? THEN 1 END) + COUNT(CASE WHEN co.status = ? THEN 1 END)), 2) as user_win_rate
                FROM ce_contract_orders co
                JOIN ce_users u ON co.user_id = u.id
                WHERE u.user_type = ? 
                AND co.created_at >= ? 
                AND co.created_at <= ?
                AND co.status IN (?, ?)
                GROUP BY co.symbol
                ORDER BY contract_volume DESC
            ", [
                ContractService::STATUS_WIN,
                ContractService::STATUS_LOSE,
                ContractService::STATUS_WIN,
                ContractService::STATUS_WIN,
                ContractService::STATUS_LOSE,
                UserService::USER_TYPE_FORMAL,
                $startDate,
                $endDate . ' 23:59:59',
                ContractService::STATUS_WIN,
                ContractService::STATUS_LOSE
            ]);

            return [
                'code' => 1,
                'data' => [
                    'daily_stats' => $dailyStats,
                    'symbol_stats' => $symbolStats
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取合约报表失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取报表失败'];
        }
    }

    /**
     * 获取资金流水报表
     */
    public function getFinancialFlowReport(array $filters = []): array
    {
        try {
            $startDate = $filters['start_date'] ?? date('Y-m-d');
            $endDate = $filters['end_date'] ?? date('Y-m-d');

            $query = FinancialRecord::alias('fr')
                                   ->join('ce_users u', 'fr.user_id = u.id')
                                   ->where('u.user_type', UserService::USER_TYPE_FORMAL)
                                   ->where('fr.created_at', '>=', $startDate)
                                   ->where('fr.created_at', '<=', $endDate . ' 23:59:59');

            // 筛选条件
            if (!empty($filters['type'])) {
                $query->where('fr.type', $filters['type']);
            }

            if (!empty($filters['coin_symbol'])) {
                $query->where('fr.coin_symbol', $filters['coin_symbol']);
            }

            if (!empty($filters['user_id'])) {
                $query->where('fr.user_id', $filters['user_id']);
            }

            $records = $query->field('fr.*, u.username')
                            ->order('fr.created_at', 'desc')
                            ->limit($filters['limit'] ?? 100)
                            ->select();

            // 按类型统计
            $typeStats = $query->field('fr.type, COUNT(*) as count, SUM(fr.amount) as total_amount')
                              ->group('fr.type')
                              ->select();

            return [
                'code' => 1,
                'data' => [
                    'records' => $records->toArray(),
                    'type_stats' => $typeStats->toArray()
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取资金流水报表失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取报表失败'];
        }
    }
}
