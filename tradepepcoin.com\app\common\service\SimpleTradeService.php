<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\TradingPair;
use app\common\model\Order;
use app\common\model\Trade;
use app\common\model\UserAsset;
use app\common\model\FinancialRecord;
use think\facade\Db;
use think\facade\Cache;
use think\facade\Log;

/**
 * 简化交易服务类 - 只包含基础交易功能
 */
class SimpleTradeService
{
    /**
     * 创建订单
     */
    public function createOrder(int $userId, array $data): array
    {
        try {
            // 获取交易对信息
            $tradingPair = TradingPair::getBySymbol($data['symbol']);
            if (!$tradingPair || $tradingPair->status != 1) {
                return ['code' => 0, 'msg' => '交易对不存在或已禁用'];
            }

            // 验证交易数量
            if ($data['amount'] < $tradingPair->min_amount) {
                return ['code' => 0, 'msg' => '交易数量不能小于最小限制'];
            }

            if ($tradingPair->max_amount > 0 && $data['amount'] > $tradingPair->max_amount) {
                return ['code' => 0, 'msg' => '交易数量不能大于最大限制'];
            }

            // 计算价格和总额
            if ($data['order_type'] == 'market') {
                // 市价单使用当前价格
                $price = $tradingPair->current_price;
            } else {
                // 限价单使用用户指定价格
                $price = $data['price'];
            }

            $total = $data['amount'] * $price;

            // 验证最小交易额
            if ($total < $tradingPair->min_total) {
                return ['code' => 0, 'msg' => '交易金额不能小于最小限制'];
            }

            // 验证用户资产
            $assetCheck = $this->checkUserAssets($userId, $data, $tradingPair, $total);
            if (!$assetCheck['code']) {
                return $assetCheck;
            }

            // 开始事务
            Db::startTrans();
            try {
                // 创建订单
                $orderData = [
                    'order_id' => $this->generateOrderId(),
                    'user_id' => $userId,
                    'symbol' => $data['symbol'],
                    'type' => $data['type'], // buy/sell
                    'order_type' => $data['order_type'], // market/limit
                    'amount' => $data['amount'],
                    'price' => $price,
                    'total' => $total,
                    'filled_amount' => 0,
                    'filled_total' => 0,
                    'status' => 0, // 待成交
                    'created_at' => date('Y-m-d H:i:s')
                ];

                $order = Order::create($orderData);

                // 冻结用户资产
                $this->freezeUserAssets($userId, $data, $tradingPair, $total);

                // 如果是市价单，立即撮合
                if ($data['order_type'] == 'market') {
                    $this->matchOrder($order);
                }

                Db::commit();

                Log::info("订单创建成功", [
                    'order_id' => $order->order_id,
                    'user_id' => $userId,
                    'symbol' => $data['symbol'],
                    'type' => $data['type'],
                    'amount' => $data['amount']
                ]);

                return [
                    'code' => 1,
                    'msg' => '订单创建成功',
                    'data' => [
                        'order_id' => $order->order_id,
                        'status' => $order->status
                    ]
                ];
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('订单创建失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '订单创建失败'];
        }
    }

    /**
     * 取消订单
     */
    public function cancelOrder(int $userId, string $orderId): array
    {
        try {
            $order = Order::where('order_id', $orderId)
                         ->where('user_id', $userId)
                         ->find();

            if (!$order) {
                return ['code' => 0, 'msg' => '订单不存在'];
            }

            if ($order->status != 0) {
                return ['code' => 0, 'msg' => '订单状态不允许取消'];
            }

            // 开始事务
            Db::startTrans();
            try {
                // 更新订单状态
                $order->status = 3; // 已取消
                $order->cancelled_at = date('Y-m-d H:i:s');
                $order->save();

                // 解冻用户资产
                $this->unfreezeUserAssets($order);

                Db::commit();

                return ['code' => 1, 'msg' => '订单取消成功'];
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('订单取消失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '订单取消失败'];
        }
    }

    /**
     * 获取用户订单列表
     */
    public function getUserOrders(int $userId, array $params = []): array
    {
        $query = Order::where('user_id', $userId);

        // 按交易对筛选
        if (!empty($params['symbol'])) {
            $query->where('symbol', $params['symbol']);
        }

        // 按状态筛选
        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }

        // 按类型筛选
        if (!empty($params['type'])) {
            $query->where('type', $params['type']);
        }

        $orders = $query->order('created_at', 'desc')
                       ->limit($params['limit'] ?? 50)
                       ->select();

        return [
            'code' => 1,
            'data' => $orders->toArray()
        ];
    }

    /**
     * 获取订单详情
     */
    public function getOrderDetail(int $userId, string $orderId): array
    {
        $order = Order::where('order_id', $orderId)
                     ->where('user_id', $userId)
                     ->find();

        if (!$order) {
            return ['code' => 0, 'msg' => '订单不存在'];
        }

        return [
            'code' => 1,
            'data' => $order->toArray()
        ];
    }

    /**
     * 获取交易历史
     */
    public function getTradeHistory(int $userId, array $params = []): array
    {
        $query = Trade::where('buyer_id', $userId)
                     ->whereOr('seller_id', $userId);

        if (!empty($params['symbol'])) {
            $query->where('symbol', $params['symbol']);
        }

        $trades = $query->order('created_at', 'desc')
                       ->limit($params['limit'] ?? 50)
                       ->select();

        return [
            'code' => 1,
            'data' => $trades->toArray()
        ];
    }

    /**
     * 验证用户资产
     */
    private function checkUserAssets(int $userId, array $data, TradingPair $tradingPair, float $total): array
    {
        if ($data['type'] == 'buy') {
            // 买入需要计价币种
            $asset = UserAsset::getUserAsset($userId, $tradingPair->quote_coin);
            if (!$asset || $asset->available < $total) {
                return ['code' => 0, 'msg' => $tradingPair->quote_coin . '余额不足'];
            }
        } else {
            // 卖出需要基础币种
            $asset = UserAsset::getUserAsset($userId, $tradingPair->base_coin);
            if (!$asset || $asset->available < $data['amount']) {
                return ['code' => 0, 'msg' => $tradingPair->base_coin . '余额不足'];
            }
        }

        return ['code' => 1, 'msg' => '资产验证通过'];
    }

    /**
     * 冻结用户资产
     */
    private function freezeUserAssets(int $userId, array $data, TradingPair $tradingPair, float $total): void
    {
        if ($data['type'] == 'buy') {
            // 买入冻结计价币种
            UserAsset::freezeAsset($userId, $tradingPair->quote_coin, $total);
        } else {
            // 卖出冻结基础币种
            UserAsset::freezeAsset($userId, $tradingPair->base_coin, $data['amount']);
        }
    }

    /**
     * 解冻用户资产
     */
    private function unfreezeUserAssets(Order $order): void
    {
        $tradingPair = TradingPair::getBySymbol($order->symbol);

        if ($order->type == 'buy') {
            // 买入解冻计价币种
            $unfreezeAmount = $order->total - $order->filled_total;
            UserAsset::unfreezeAsset($order->user_id, $tradingPair->quote_coin, $unfreezeAmount);
        } else {
            // 卖出解冻基础币种
            $unfreezeAmount = $order->amount - $order->filled_amount;
            UserAsset::unfreezeAsset($order->user_id, $tradingPair->base_coin, $unfreezeAmount);
        }
    }

    /**
     * 订单撮合（简化版）
     */
    private function matchOrder(Order $order): void
    {
        // 简化的撮合逻辑，直接按当前价格成交
        $tradingPair = TradingPair::getBySymbol($order->symbol);
        $fillPrice = $tradingPair->current_price;
        $fillAmount = $order->amount;
        $fillTotal = $fillAmount * $fillPrice;

        // 计算手续费
        $feeRate = 0.001; // 0.1%
        $fee = $fillTotal * $feeRate;

        // 更新订单状态
        $order->filled_amount = $fillAmount;
        $order->filled_total = $fillTotal;
        $order->status = 1; // 已成交
        $order->filled_at = date('Y-m-d H:i:s');
        $order->save();

        // 创建交易记录
        Trade::create([
            'trade_id' => $this->generateTradeId(),
            'symbol' => $order->symbol,
            'buyer_id' => $order->type == 'buy' ? $order->user_id : 0,
            'seller_id' => $order->type == 'sell' ? $order->user_id : 0,
            'amount' => $fillAmount,
            'price' => $fillPrice,
            'total' => $fillTotal,
            'fee' => $fee,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        // 更新用户资产
        $this->updateUserAssets($order, $fillAmount, $fillTotal, $fee);

        // 记录财务流水
        $this->recordFinancialFlow($order, $fillAmount, $fillTotal, $fee);
    }

    /**
     * 更新用户资产
     */
    private function updateUserAssets(Order $order, float $fillAmount, float $fillTotal, float $fee): void
    {
        $tradingPair = TradingPair::getBySymbol($order->symbol);

        if ($order->type == 'buy') {
            // 买入：扣除冻结的计价币种，增加基础币种
            $quoteAsset = UserAsset::getOrCreate($order->user_id, $tradingPair->quote_coin);
            $baseAsset = UserAsset::getOrCreate($order->user_id, $tradingPair->base_coin);

            $quoteAsset->subFrozen($fillTotal);
            $baseAsset->addAvailable($fillAmount - $fee);
        } else {
            // 卖出：扣除冻结的基础币种，增加计价币种
            $baseAsset = UserAsset::getOrCreate($order->user_id, $tradingPair->base_coin);
            $quoteAsset = UserAsset::getOrCreate($order->user_id, $tradingPair->quote_coin);

            $baseAsset->subFrozen($fillAmount);
            $quoteAsset->addAvailable($fillTotal - $fee);
        }
    }

    /**
     * 记录财务流水
     */
    private function recordFinancialFlow(Order $order, float $fillAmount, float $fillTotal, float $fee): void
    {
        $tradingPair = TradingPair::getBySymbol($order->symbol);

        if ($order->type == 'buy') {
            // 买入记录
            FinancialRecord::create([
                'user_id' => $order->user_id,
                'coin_symbol' => $tradingPair->base_coin,
                'type' => 'trade',
                'amount' => $fillAmount - $fee,
                'related_id' => $order->order_id,
                'description' => "买入 {$order->symbol}"
            ]);
        } else {
            // 卖出记录
            FinancialRecord::create([
                'user_id' => $order->user_id,
                'coin_symbol' => $tradingPair->quote_coin,
                'type' => 'trade',
                'amount' => $fillTotal - $fee,
                'related_id' => $order->order_id,
                'description' => "卖出 {$order->symbol}"
            ]);
        }
    }

    /**
     * 生成订单ID
     */
    private function generateOrderId(): string
    {
        return date('YmdHis') . mt_rand(100000, 999999);
    }

    /**
     * 生成交易ID
     */
    private function generateTradeId(): string
    {
        return 'T' . date('YmdHis') . mt_rand(100000, 999999);
    }
}
