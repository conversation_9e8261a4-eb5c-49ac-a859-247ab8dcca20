{extend name="index/layout" /}

{block name="css"}
<style>
.auth-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    padding-top: 56px;
}

.auth-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    overflow: hidden;
    max-width: 900px;
    margin: 0 auto;
}

.auth-left {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 60px 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
}

.auth-right {
    padding: 60px 40px;
}

.form-floating {
    margin-bottom: 1rem;
}

.btn-auth {
    height: 50px;
    font-size: 16px;
    font-weight: 500;
}

.divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #dee2e6;
}

.divider span {
    background: white;
    padding: 0 15px;
    color: #6c757d;
}

.captcha-container {
    display: flex;
    gap: 10px;
    align-items: center;
}

.captcha-img {
    cursor: pointer;
    border: 1px solid #ced4da;
    border-radius: 5px;
}

@media (max-width: 768px) {
    .auth-left {
        display: none;
    }
    
    .auth-right {
        padding: 40px 20px;
    }
}
</style>
{/block}

{block name="content"}
<div class="auth-container">
    <div class="container">
        <div class="auth-card">
            <div class="row g-0">
                <div class="col-lg-6">
                    <div class="auth-left">
                        <div>
                            <h2 class="mb-4">欢迎回来！</h2>
                            <p class="mb-4">登录您的账户，继续您的数字货币交易之旅</p>
                            <div class="features">
                                <div class="feature-item mb-3">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    银行级安全保障
                                </div>
                                <div class="feature-item mb-3">
                                    <i class="fas fa-clock me-2"></i>
                                    7×24小时交易
                                </div>
                                <div class="feature-item mb-3">
                                    <i class="fas fa-users me-2"></i>
                                    专业客服支持
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="auth-right">
                        <div class="text-center mb-4">
                            <h3 class="fw-bold">用户登录</h3>
                            <p class="text-muted">请输入您的登录信息</p>
                        </div>

                        <form id="loginForm">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="username" name="username" placeholder="用户名或邮箱" required>
                                <label for="username">用户名或邮箱</label>
                            </div>

                            <div class="form-floating">
                                <input type="password" class="form-control" id="password" name="password" placeholder="密码" required>
                                <label for="password">密码</label>
                            </div>

                            <div class="form-floating">
                                <div class="captcha-container">
                                    <input type="text" class="form-control" id="captcha" name="captcha" placeholder="验证码" required>
                                    <img src="/auth/captcha" alt="验证码" class="captcha-img" id="captchaImg" width="120" height="40">
                                </div>
                                <label for="captcha">验证码</label>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                    <label class="form-check-label" for="remember">
                                        记住我
                                    </label>
                                </div>
                                <a href="/auth/forgot-password" class="text-decoration-none">忘记密码？</a>
                            </div>

                            <button type="submit" class="btn btn-primary w-100 btn-auth" id="loginBtn">
                                <span class="spinner-border spinner-border-sm me-2 d-none" id="loginSpinner"></span>
                                登录
                            </button>
                        </form>

                        <div class="divider">
                            <span>或</span>
                        </div>

                        <div class="text-center">
                            <p class="mb-0">还没有账户？ <a href="/auth/register" class="text-decoration-none fw-bold">立即注册</a></p>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="js"}
<script>
$(document).ready(function() {
    // 刷新验证码
    $('#captchaImg').click(function() {
        $(this).attr('src', '/auth/captcha?' + Math.random());
    });

    // 登录表单提交
    $('#loginForm').submit(function(e) {
        e.preventDefault();
        
        const $btn = $('#loginBtn');
        const $spinner = $('#loginSpinner');
        const formData = $(this).serialize();
        
        // 显示加载状态
        $btn.prop('disabled', true);
        $spinner.removeClass('d-none');
        
        $.post('/auth/login', formData)
            .done(function(response) {
                if (response.code === 1) {
                    showToast('登录成功', 'success');
                    
                    // 跳转到指定页面或首页
                    const redirect = new URLSearchParams(window.location.search).get('redirect') || '/';
                    setTimeout(() => {
                        window.location.href = redirect;
                    }, 1000);
                } else {
                    showToast(response.msg || '登录失败', 'error');
                    // 刷新验证码
                    $('#captchaImg').click();
                }
            })
            .fail(function() {
                showToast('网络错误，请稍后重试', 'error');
                $('#captchaImg').click();
            })
            .always(function() {
                // 恢复按钮状态
                $btn.prop('disabled', false);
                $spinner.addClass('d-none');
            });
    });

    // 回车键登录
    $(document).keypress(function(e) {
        if (e.which === 13) {
            $('#loginForm').submit();
        }
    });

    // 表单验证
    $('#email').on('blur', function() {
        const email = $(this).val();
        if (email && !isValidEmail(email)) {
            $(this).addClass('is-invalid');
            showToast('请输入有效的邮箱地址', 'warning');
        } else {
            $(this).removeClass('is-invalid');
        }
    });

    $('#password').on('input', function() {
        const password = $(this).val();
        if (password.length > 0 && password.length < 6) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
});

// 快速登录函数
function quickLogin(email, password) {
    $('#email').val(email);
    $('#password').val(password);
    $('#captcha').val('888888'); // 万能验证码
    $('#loginForm').submit();
}

// 邮箱验证函数
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// 自动填充上次登录的邮箱
$(document).ready(function() {
    const lastEmail = localStorage.getItem('lastLoginEmail');
    if (lastEmail) {
        $('#email').val(lastEmail);
        $('#password').focus();
    }
});

// 保存登录邮箱
$('#loginForm').on('submit', function() {
    const email = $('#email').val();
    if (email) {
        localStorage.setItem('lastLoginEmail', email);
    }
});
</script>
{/block}
