<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 充值记录模型
 */
class DepositRecord extends Model
{
    protected $name = 'ce_deposit_records';
    
    // 设置字段信息
    protected $schema = [
        'id'                    => 'int',
        'user_id'               => 'int',
        'coin_symbol'           => 'string',
        'address'               => 'string',
        'amount'                => 'decimal',
        'txid'                  => 'string',
        'confirmations'         => 'int',
        'required_confirmations' => 'int',
        'status'                => 'string',
        'created_at'            => 'datetime',
        'updated_at'            => 'datetime'
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 状态常量
    const STATUS_PENDING = 'pending';       // 待确认
    const STATUS_COMPLETED = 'completed';   // 已完成
    const STATUS_FAILED = 'failed';         // 失败
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            self::STATUS_PENDING => '待确认',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_FAILED => '失败'
        ];
        return $status[$data['status']] ?? '未知';
    }
    
    /**
     * 获取确认进度
     */
    public function getConfirmProgressAttr($value, $data)
    {
        if ($data['status'] === self::STATUS_COMPLETED) {
            return 100;
        }
        
        if ($data['required_confirmations'] <= 0) {
            return 0;
        }
        
        return min(100, round(($data['confirmations'] / $data['required_confirmations']) * 100, 2));
    }
    
    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    
    /**
     * 获取用户充值记录
     */
    public static function getUserDeposits(int $userId, string $coinSymbol = '', string $status = '', int $page = 1, int $limit = 20)
    {
        $query = self::where('user_id', $userId);
        
        if ($coinSymbol) {
            $query = $query->where('coin_symbol', $coinSymbol);
        }
        
        if ($status) {
            $query = $query->where('status', $status);
        }
        
        return $query->order('created_at desc')
                    ->paginate([
                        'list_rows' => $limit,
                        'page' => $page
                    ]);
    }
    
    /**
     * 获取充值统计
     */
    public static function getDepositStats(int $userId): array
    {
        $total = self::where('user_id', $userId)->count();
        $completed = self::where('user_id', $userId)->where('status', self::STATUS_COMPLETED)->count();
        $pending = self::where('user_id', $userId)->where('status', self::STATUS_PENDING)->count();
        
        $totalAmount = self::where('user_id', $userId)
                          ->where('status', self::STATUS_COMPLETED)
                          ->sum('amount');
        
        return [
            'total' => $total,
            'completed' => $completed,
            'pending' => $pending,
            'total_amount' => $totalAmount
        ];
    }
    
    /**
     * 检查是否需要更新确认数
     */
    public function needsConfirmationUpdate(): bool
    {
        return $this->status === self::STATUS_PENDING && 
               $this->confirmations < $this->required_confirmations;
    }
    
    /**
     * 更新确认数
     */
    public function updateConfirmations(int $confirmations): bool
    {
        $this->confirmations = $confirmations;
        
        // 如果确认数达到要求，更新状态为已完成
        if ($confirmations >= $this->required_confirmations) {
            $this->status = self::STATUS_COMPLETED;
        }
        
        return $this->save();
    }
}
