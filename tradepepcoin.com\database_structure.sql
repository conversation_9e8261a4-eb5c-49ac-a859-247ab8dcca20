-- GVD交易平台数据库结构

-- 用户表（添加用户类型和代理字段）
ALTER TABLE `ce_users` ADD COLUMN `user_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '用户类型：1正式用户 2测试用户';
ALTER TABLE `ce_users` ADD COLUMN `agent_id` int(11) NOT NULL DEFAULT 0 COMMENT '代理ID';

-- 合约配置表
CREATE TABLE IF NOT EXISTS `ce_contract_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `min_amount` decimal(10,2) NOT NULL DEFAULT 10.00 COMMENT '最小交易金额',
  `max_amount` decimal(10,2) NOT NULL DEFAULT 10000.00 COMMENT '最大交易金额',
  `win_rate` decimal(3,2) NOT NULL DEFAULT 0.85 COMMENT '胜率',
  `available_times` text COMMENT '可选时长(JSON)',
  `available_coins` text COMMENT '可交易币种(JSON)',
  `quick_amounts` text COMMENT '快捷金额(JSON)',
  `profit_rates` text COMMENT '赔率配置(JSON)',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合约配置表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS `ce_system_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(100) NOT NULL COMMENT '配置键',
  `value` text COMMENT '配置值',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 系统图片表
CREATE TABLE IF NOT EXISTS `ce_system_images` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(100) NOT NULL COMMENT '图片键',
  `url` varchar(500) NOT NULL COMMENT '图片URL',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统图片表';

-- 客服消息表
CREATE TABLE IF NOT EXISTS `ce_customer_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `content` text NOT NULL COMMENT '消息内容',
  `type` varchar(20) NOT NULL DEFAULT 'text' COMMENT '消息类型：text,image,file',
  `sender` varchar(20) NOT NULL COMMENT '发送者：user,service',
  `status` varchar(20) NOT NULL DEFAULT 'sent' COMMENT '状态：sent,read',
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服消息表';

-- 管理员表
CREATE TABLE IF NOT EXISTS `ce_admins` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `role` varchar(20) NOT NULL DEFAULT 'admin' COMMENT '角色',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0禁用 1启用',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- 代理表
CREATE TABLE IF NOT EXISTS `ce_agents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `commission_rate` decimal(5,4) NOT NULL DEFAULT 0.0000 COMMENT '佣金比例',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0禁用 1启用',
  `parent_id` int(11) NOT NULL DEFAULT 0 COMMENT '上级代理ID',
  `level` tinyint(2) NOT NULL DEFAULT 1 COMMENT '代理等级',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代理表';

-- 插入默认配置数据
INSERT INTO `ce_system_config` (`key`, `value`, `description`, `created_at`) VALUES
('universal_verification_code', '888888', '万能验证码', NOW()),
('customer_service', '{"qq":"","wechat":"","telegram":"","email":"<EMAIL>","phone":"","work_time":"9:00-18:00"}', '客服配置', NOW());

-- 插入默认合约配置
INSERT INTO `ce_contract_config` (`min_amount`, `max_amount`, `win_rate`, `available_times`, `available_coins`, `quick_amounts`, `profit_rates`, `created_at`) VALUES
(10.00, 10000.00, 0.85, '[60,180,300,600]', '["BTC","ETH","LTC","EOS","XRP"]', '[50,100,500,1000]', '{"60":0.80,"180":0.85,"300":0.90,"600":0.95}', NOW());

-- 插入默认管理员账户（密码：admin123）
INSERT INTO `ce_admins` (`username`, `password`, `real_name`, `email`, `role`, `status`, `created_at`) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', '<EMAIL>', 'admin', 1, NOW());

-- 插入默认系统图片配置
INSERT INTO `ce_system_images` (`key`, `url`, `description`, `created_at`) VALUES
('logo', '/static/images/logo.png', '网站Logo', NOW()),
('banner_home', '/static/images/banner_home.jpg', '首页横幅', NOW()),
('banner_trade', '/static/images/banner_trade.jpg', '交易页横幅', NOW()),
('icon_btc', '/static/images/coins/btc.png', 'BTC图标', NOW()),
('icon_eth', '/static/images/coins/eth.png', 'ETH图标', NOW()),
('icon_usdt', '/static/images/coins/usdt.png', 'USDT图标', NOW());

-- 创建索引优化
ALTER TABLE `ce_users` ADD INDEX `idx_user_type` (`user_type`);
ALTER TABLE `ce_users` ADD INDEX `idx_agent_id` (`agent_id`);
ALTER TABLE `ce_contract_orders` ADD INDEX `idx_user_status` (`user_id`, `status`);
ALTER TABLE `ce_deposit_records` ADD INDEX `idx_user_status` (`user_id`, `status`);
ALTER TABLE `ce_withdraw_records` ADD INDEX `idx_user_status` (`user_id`, `status`);
