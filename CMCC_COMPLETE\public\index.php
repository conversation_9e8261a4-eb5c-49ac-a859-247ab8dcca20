<?php
// +----------------------------------------------------------------------
// | GVD数字货币交易平台
// +----------------------------------------------------------------------
// | 版本: 2.0.0
// +----------------------------------------------------------------------
// | 作者: GVD Team
// +----------------------------------------------------------------------

namespace think;

// 定义项目路径
define('__ROOT__', dirname(__DIR__));

// 加载基础文件
require __ROOT__ . '/vendor/autoload.php';

// 执行HTTP应用并响应
$http = (new App())->http;

$response = $http->run();

$response->send();

$http->end($response);
