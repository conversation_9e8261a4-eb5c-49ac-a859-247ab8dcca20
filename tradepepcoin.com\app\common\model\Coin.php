<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 币种模型
 */
class Coin extends Model
{
    protected $table = 'ce_coins';
    
    // 设置字段信息
    protected $schema = [
        'id'               => 'int',
        'symbol'           => 'string',
        'name'             => 'string',
        'icon'             => 'string',
        'decimals'         => 'int',
        'is_deposit'       => 'int',
        'is_withdraw'      => 'int',
        'is_trade'         => 'int',
        'min_deposit'      => 'decimal',
        'min_withdraw'     => 'decimal',
        'withdraw_fee'     => 'decimal',
        'withdraw_fee_type'=> 'int',
        'deposit_address'  => 'string',
        'contract_address' => 'string',
        'sort'             => 'int',
        'status'           => 'int',
        'created_at'       => 'datetime',
        'updated_at'       => 'datetime',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 类型转换
    protected $type = [
        'decimals'          => 'integer',
        'is_deposit'        => 'integer',
        'is_withdraw'       => 'integer',
        'is_trade'          => 'integer',
        'min_deposit'       => 'decimal:8',
        'min_withdraw'      => 'decimal:8',
        'withdraw_fee'      => 'decimal:8',
        'withdraw_fee_type' => 'integer',
        'sort'              => 'integer',
        'status'            => 'integer',
    ];

    /**
     * 获取启用的币种
     */
    public static function getEnabled()
    {
        return self::where('status', 1)->order('sort', 'asc')->select();
    }

    /**
     * 获取支持充值的币种
     */
    public static function getDepositEnabled()
    {
        return self::where('status', 1)
                  ->where('is_deposit', 1)
                  ->order('sort', 'asc')
                  ->select();
    }

    /**
     * 获取支持提现的币种
     */
    public static function getWithdrawEnabled()
    {
        return self::where('status', 1)
                  ->where('is_withdraw', 1)
                  ->order('sort', 'asc')
                  ->select();
    }

    /**
     * 获取支持交易的币种
     */
    public static function getTradeEnabled()
    {
        return self::where('status', 1)
                  ->where('is_trade', 1)
                  ->order('sort', 'asc')
                  ->select();
    }

    /**
     * 根据符号获取币种
     */
    public static function getBySymbol(string $symbol)
    {
        return self::where('symbol', $symbol)->find();
    }

    /**
     * 状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [0 => '禁用', 1 => '启用'];
        return $status[$data['status']] ?? '未知';
    }

    /**
     * 是否支持充值文本
     */
    public function getIsDepositTextAttr($value, $data)
    {
        return $data['is_deposit'] ? '支持' : '不支持';
    }

    /**
     * 是否支持提现文本
     */
    public function getIsWithdrawTextAttr($value, $data)
    {
        return $data['is_withdraw'] ? '支持' : '不支持';
    }

    /**
     * 是否支持交易文本
     */
    public function getIsTradeTextAttr($value, $data)
    {
        return $data['is_trade'] ? '支持' : '不支持';
    }

    /**
     * 手续费类型文本
     */
    public function getWithdrawFeeTypeTextAttr($value, $data)
    {
        $types = [1 => '固定', 2 => '比例'];
        return $types[$data['withdraw_fee_type']] ?? '未知';
    }

    /**
     * 格式化最小充值金额
     */
    public function getMinDepositFormatAttr($value, $data)
    {
        return number_format($data['min_deposit'], $data['decimals'], '.', '');
    }

    /**
     * 格式化最小提现金额
     */
    public function getMinWithdrawFormatAttr($value, $data)
    {
        return number_format($data['min_withdraw'], $data['decimals'], '.', '');
    }

    /**
     * 格式化提现手续费
     */
    public function getWithdrawFeeFormatAttr($value, $data)
    {
        if ($data['withdraw_fee_type'] == 1) {
            // 固定手续费
            return number_format($data['withdraw_fee'], $data['decimals'], '.', '') . ' ' . $data['symbol'];
        } else {
            // 比例手续费
            return number_format($data['withdraw_fee'] * 100, 2, '.', '') . '%';
        }
    }

    /**
     * 图标URL
     */
    public function getIconUrlAttr($value, $data)
    {
        if (empty($data['icon'])) {
            return '/static/images/coins/default.png';
        }
        
        if (strpos($data['icon'], 'http') === 0) {
            return $data['icon'];
        }
        
        return '/uploads/coins/' . $data['icon'];
    }
}
