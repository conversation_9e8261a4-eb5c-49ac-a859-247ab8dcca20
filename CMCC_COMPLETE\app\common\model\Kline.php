<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * K线数据模型
 */
class Kline extends Model
{
    protected $name = 'gvd_klines';
    
    protected $type = [
        'open_price' => 'float',
        'high_price' => 'float',
        'low_price' => 'float',
        'close_price' => 'float',
        'volume' => 'float',
        'amount' => 'float',
        'open_time' => 'timestamp',
        'close_time' => 'timestamp'
    ];

    // 时间周期常量
    const INTERVAL_1M = '1m';
    const INTERVAL_3M = '3m';
    const INTERVAL_5M = '5m';
    const INTERVAL_15M = '15m';
    const INTERVAL_30M = '30m';
    const INTERVAL_1H = '1h';
    const INTERVAL_2H = '2h';
    const INTERVAL_4H = '4h';
    const INTERVAL_6H = '6h';
    const INTERVAL_8H = '8h';
    const INTERVAL_12H = '12h';
    const INTERVAL_1D = '1d';
    const INTERVAL_3D = '3d';
    const INTERVAL_1W = '1w';
    const INTERVAL_1M_MONTH = '1M';

    /**
     * 获取支持的时间周期
     */
    public static function getSupportedIntervals(): array
    {
        return [
            self::INTERVAL_1M => '1分钟',
            self::INTERVAL_3M => '3分钟',
            self::INTERVAL_5M => '5分钟',
            self::INTERVAL_15M => '15分钟',
            self::INTERVAL_30M => '30分钟',
            self::INTERVAL_1H => '1小时',
            self::INTERVAL_2H => '2小时',
            self::INTERVAL_4H => '4小时',
            self::INTERVAL_6H => '6小时',
            self::INTERVAL_8H => '8小时',
            self::INTERVAL_12H => '12小时',
            self::INTERVAL_1D => '1天',
            self::INTERVAL_3D => '3天',
            self::INTERVAL_1W => '1周',
            self::INTERVAL_1M_MONTH => '1月'
        ];
    }

    /**
     * 获取时间周期对应的秒数
     */
    public static function getIntervalSeconds(string $interval): int
    {
        $intervals = [
            self::INTERVAL_1M => 60,
            self::INTERVAL_3M => 180,
            self::INTERVAL_5M => 300,
            self::INTERVAL_15M => 900,
            self::INTERVAL_30M => 1800,
            self::INTERVAL_1H => 3600,
            self::INTERVAL_2H => 7200,
            self::INTERVAL_4H => 14400,
            self::INTERVAL_6H => 21600,
            self::INTERVAL_8H => 28800,
            self::INTERVAL_12H => 43200,
            self::INTERVAL_1D => 86400,
            self::INTERVAL_3D => 259200,
            self::INTERVAL_1W => 604800,
            self::INTERVAL_1M_MONTH => 2592000
        ];

        return $intervals[$interval] ?? 60;
    }

    /**
     * 根据交易对和时间周期获取K线数据
     */
    public static function getKlineData(string $symbol, string $interval, int $limit = 500): array
    {
        return self::where('symbol', $symbol)
                  ->where('interval', $interval)
                  ->order('open_time', 'desc')
                  ->limit($limit)
                  ->select()
                  ->toArray();
    }

    /**
     * 获取最新K线数据
     */
    public static function getLatestKline(string $symbol, string $interval): ?array
    {
        $kline = self::where('symbol', $symbol)
                    ->where('interval', $interval)
                    ->order('open_time', 'desc')
                    ->find();

        return $kline ? $kline->toArray() : null;
    }

    /**
     * 更新或创建K线数据
     */
    public static function updateOrCreate(array $data): bool
    {
        $exists = self::where([
            'symbol' => $data['symbol'],
            'interval' => $data['interval'],
            'open_time' => $data['open_time']
        ])->find();

        if ($exists) {
            return $exists->save($data);
        } else {
            $kline = new self();
            return $kline->save($data);
        }
    }

    /**
     * 生成K线数据（用于测试或模拟）
     */
    public static function generateKlineData(string $symbol, string $interval, int $count = 100): bool
    {
        $intervalSeconds = self::getIntervalSeconds($interval);
        $basePrice = 45000; // 基础价格
        $currentTime = time();
        
        for ($i = $count; $i > 0; $i--) {
            $openTime = $currentTime - ($i * $intervalSeconds);
            $closeTime = $openTime + $intervalSeconds - 1;
            
            // 生成随机价格数据
            $open = $basePrice + mt_rand(-1000, 1000);
            $high = $open + mt_rand(0, 500);
            $low = $open - mt_rand(0, 500);
            $close = $low + mt_rand(0, $high - $low);
            $volume = mt_rand(100, 10000) / 100;
            $amount = $volume * (($open + $close) / 2);
            
            $data = [
                'symbol' => $symbol,
                'interval' => $interval,
                'open_time' => $openTime,
                'close_time' => $closeTime,
                'open_price' => $open,
                'high_price' => $high,
                'low_price' => $low,
                'close_price' => $close,
                'volume' => $volume,
                'amount' => $amount,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            self::updateOrCreate($data);
            $basePrice = $close; // 下一根K线的基础价格
        }
        
        return true;
    }

    /**
     * 转换为TradingView格式
     */
    public function toTradingViewFormat(): array
    {
        return [
            'time' => $this->open_time * 1000, // TradingView需要毫秒时间戳
            'open' => (float)$this->open_price,
            'high' => (float)$this->high_price,
            'low' => (float)$this->low_price,
            'close' => (float)$this->close_price,
            'volume' => (float)$this->volume
        ];
    }

    /**
     * 批量转换为TradingView格式
     */
    public static function toTradingViewBatch(array $klines): array
    {
        $result = [];
        foreach ($klines as $kline) {
            if (is_array($kline)) {
                $result[] = [
                    'time' => $kline['open_time'] * 1000,
                    'open' => (float)$kline['open_price'],
                    'high' => (float)$kline['high_price'],
                    'low' => (float)$kline['low_price'],
                    'close' => (float)$kline['close_price'],
                    'volume' => (float)$kline['volume']
                ];
            } else {
                $result[] = $kline->toTradingViewFormat();
            }
        }
        return $result;
    }

    /**
     * 获取24小时统计数据
     */
    public static function get24hStats(string $symbol): array
    {
        $endTime = time();
        $startTime = $endTime - 86400; // 24小时前

        $klines = self::where('symbol', $symbol)
                     ->where('interval', self::INTERVAL_1H)
                     ->where('open_time', '>=', $startTime)
                     ->where('open_time', '<=', $endTime)
                     ->order('open_time', 'asc')
                     ->select();

        if ($klines->isEmpty()) {
            return [
                'open' => 0,
                'high' => 0,
                'low' => 0,
                'close' => 0,
                'volume' => 0,
                'amount' => 0,
                'change' => 0,
                'change_percent' => 0
            ];
        }

        $first = $klines->first();
        $last = $klines->last();
        $high = $klines->max('high_price');
        $low = $klines->min('low_price');
        $volume = $klines->sum('volume');
        $amount = $klines->sum('amount');

        $change = $last->close_price - $first->open_price;
        $changePercent = $first->open_price > 0 ? ($change / $first->open_price) * 100 : 0;

        return [
            'open' => (float)$first->open_price,
            'high' => (float)$high,
            'low' => (float)$low,
            'close' => (float)$last->close_price,
            'volume' => (float)$volume,
            'amount' => (float)$amount,
            'change' => (float)$change,
            'change_percent' => round($changePercent, 2)
        ];
    }
}
