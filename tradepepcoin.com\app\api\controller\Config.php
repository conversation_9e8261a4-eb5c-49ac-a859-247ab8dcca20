<?php
declare(strict_types=1);
namespace app\api\controller;
use think\Response;
class Config extends BaseController
{
    public function getVersion(): Response
    {
        return json([
            'code' => 200,
            'message' => 'success',
            'data' => [
                'version' => '1.0.0',
                'name' => 'GVD',
                'timestamp' => time()
            ]
        ]);
    }
    
    public function getFrontendConfig(): Response
    {
        return json([
            'code' => 200,
            'message' => 'success',
            'data' => [
                'site_name' => 'GVD',
                'site_url' => 'https://tradepepcoin.com'
            ]
        ]);
    }
}
