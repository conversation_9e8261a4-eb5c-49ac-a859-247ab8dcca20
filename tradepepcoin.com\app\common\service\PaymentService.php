<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Log;
use think\facade\Cache;

/**
 * 支付服务类
 */
class PaymentService
{
    // 支持的支付方式
    const PAYMENT_METHODS = [
        'alipay' => '支付宝',
        'wechat' => '微信支付',
        'bank_card' => '银行卡',
        'paypal' => 'PayPal',
        'stripe' => 'Stripe',
        'crypto' => '数字货币'
    ];

    // 支付状态
    const STATUS_PENDING = 0;    // 待支付
    const STATUS_PROCESSING = 1; // 处理中
    const STATUS_SUCCESS = 2;    // 成功
    const STATUS_FAILED = 3;     // 失败
    const STATUS_CANCELLED = 4;  // 已取消
    const STATUS_REFUNDED = 5;   // 已退款

    /**
     * 创建支付订单
     */
    public function createPayment(array $data): array
    {
        try {
            // 验证参数
            $validation = $this->validatePaymentData($data);
            if (!$validation['code']) {
                return $validation;
            }

            // 生成支付订单
            $paymentOrder = [
                'order_id' => $this->generateOrderId(),
                'user_id' => $data['user_id'],
                'amount' => $data['amount'],
                'currency' => $data['currency'] ?? 'USD',
                'payment_method' => $data['payment_method'],
                'description' => $data['description'] ?? '',
                'callback_url' => $data['callback_url'] ?? '',
                'return_url' => $data['return_url'] ?? '',
                'status' => self::STATUS_PENDING,
                'created_at' => date('Y-m-d H:i:s')
            ];

            // 保存到数据库
            $paymentId = $this->savePaymentOrder($paymentOrder);
            if (!$paymentId) {
                return ['code' => 0, 'msg' => '支付订单创建失败'];
            }

            $paymentOrder['id'] = $paymentId;

            // 根据支付方式调用相应的支付接口
            $result = $this->processPayment($paymentOrder);
            
            if ($result['code']) {
                // 更新支付订单状态
                $this->updatePaymentStatus($paymentId, self::STATUS_PROCESSING, $result['data']);
                
                return [
                    'code' => 1,
                    'msg' => '支付订单创建成功',
                    'data' => [
                        'order_id' => $paymentOrder['order_id'],
                        'payment_url' => $result['data']['payment_url'] ?? '',
                        'qr_code' => $result['data']['qr_code'] ?? '',
                        'payment_info' => $result['data']
                    ]
                ];
            } else {
                // 更新支付订单状态为失败
                $this->updatePaymentStatus($paymentId, self::STATUS_FAILED, ['error' => $result['msg']]);
                return $result;
            }
        } catch (\Exception $e) {
            Log::error('创建支付订单失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '支付服务异常'];
        }
    }

    /**
     * 处理支付
     */
    private function processPayment(array $paymentOrder): array
    {
        switch ($paymentOrder['payment_method']) {
            case 'alipay':
                return $this->processAlipay($paymentOrder);
            case 'wechat':
                return $this->processWechat($paymentOrder);
            case 'bank_card':
                return $this->processBankCard($paymentOrder);
            case 'paypal':
                return $this->processPaypal($paymentOrder);
            case 'stripe':
                return $this->processStripe($paymentOrder);
            case 'crypto':
                return $this->processCrypto($paymentOrder);
            default:
                return ['code' => 0, 'msg' => '不支持的支付方式'];
        }
    }

    /**
     * 处理支付宝支付
     */
    private function processAlipay(array $paymentOrder): array
    {
        try {
            $config = config('payment.alipay');
            
            // 构建支付参数
            $params = [
                'app_id' => $config['app_id'],
                'method' => 'alipay.trade.page.pay',
                'charset' => 'utf-8',
                'sign_type' => 'RSA2',
                'timestamp' => date('Y-m-d H:i:s'),
                'version' => '1.0',
                'notify_url' => $config['notify_url'],
                'return_url' => $paymentOrder['return_url'],
                'biz_content' => json_encode([
                    'out_trade_no' => $paymentOrder['order_id'],
                    'product_code' => 'FAST_INSTANT_TRADE_PAY',
                    'total_amount' => $paymentOrder['amount'],
                    'subject' => $paymentOrder['description'] ?: '充值'
                ])
            ];

            // 生成签名
            $params['sign'] = $this->generateAlipaySign($params, $config['private_key']);

            // 构建支付URL
            $paymentUrl = $config['gateway'] . '?' . http_build_query($params);

            return [
                'code' => 1,
                'msg' => '支付宝支付创建成功',
                'data' => [
                    'payment_url' => $paymentUrl,
                    'method' => 'GET'
                ]
            ];
        } catch (\Exception $e) {
            Log::error('支付宝支付处理失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '支付宝支付处理失败'];
        }
    }

    /**
     * 处理微信支付
     */
    private function processWechat(array $paymentOrder): array
    {
        try {
            $config = config('payment.wechat');
            
            // 构建支付参数
            $params = [
                'appid' => $config['app_id'],
                'mch_id' => $config['mch_id'],
                'nonce_str' => $this->generateNonceStr(),
                'body' => $paymentOrder['description'] ?: '充值',
                'out_trade_no' => $paymentOrder['order_id'],
                'total_fee' => $paymentOrder['amount'] * 100, // 微信支付金额单位为分
                'spbill_create_ip' => request()->ip(),
                'notify_url' => $config['notify_url'],
                'trade_type' => 'NATIVE'
            ];

            // 生成签名
            $params['sign'] = $this->generateWechatSign($params, $config['key']);

            // 调用微信支付API
            $xml = $this->arrayToXml($params);
            $response = $this->httpPost($config['api_url'], $xml);
            $result = $this->xmlToArray($response);

            if ($result['return_code'] === 'SUCCESS' && $result['result_code'] === 'SUCCESS') {
                return [
                    'code' => 1,
                    'msg' => '微信支付创建成功',
                    'data' => [
                        'qr_code' => $result['code_url'],
                        'prepay_id' => $result['prepay_id']
                    ]
                ];
            } else {
                return ['code' => 0, 'msg' => $result['err_code_des'] ?? '微信支付创建失败'];
            }
        } catch (\Exception $e) {
            Log::error('微信支付处理失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '微信支付处理失败'];
        }
    }

    /**
     * 处理PayPal支付
     */
    private function processPaypal(array $paymentOrder): array
    {
        try {
            $config = config('payment.paypal');
            
            // 获取访问令牌
            $accessToken = $this->getPaypalAccessToken($config);
            if (!$accessToken) {
                return ['code' => 0, 'msg' => 'PayPal认证失败'];
            }

            // 创建支付
            $paymentData = [
                'intent' => 'sale',
                'payer' => [
                    'payment_method' => 'paypal'
                ],
                'transactions' => [
                    [
                        'amount' => [
                            'total' => $paymentOrder['amount'],
                            'currency' => $paymentOrder['currency']
                        ],
                        'description' => $paymentOrder['description'] ?: 'Payment'
                    ]
                ],
                'redirect_urls' => [
                    'return_url' => $paymentOrder['return_url'],
                    'cancel_url' => $config['cancel_url']
                ]
            ];

            $headers = [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $accessToken
            ];

            $response = $this->httpPost($config['api_url'] . '/payments/payment', json_encode($paymentData), $headers);
            $result = json_decode($response, true);

            if (isset($result['id'])) {
                $approvalUrl = '';
                foreach ($result['links'] as $link) {
                    if ($link['rel'] === 'approval_url') {
                        $approvalUrl = $link['href'];
                        break;
                    }
                }

                return [
                    'code' => 1,
                    'msg' => 'PayPal支付创建成功',
                    'data' => [
                        'payment_url' => $approvalUrl,
                        'payment_id' => $result['id']
                    ]
                ];
            } else {
                return ['code' => 0, 'msg' => 'PayPal支付创建失败'];
            }
        } catch (\Exception $e) {
            Log::error('PayPal支付处理失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'PayPal支付处理失败'];
        }
    }

    /**
     * 处理Stripe支付
     */
    private function processStripe(array $paymentOrder): array
    {
        try {
            $config = config('payment.stripe');
            
            // 创建支付意图
            $paymentData = [
                'amount' => $paymentOrder['amount'] * 100, // Stripe金额单位为分
                'currency' => strtolower($paymentOrder['currency']),
                'metadata' => [
                    'order_id' => $paymentOrder['order_id']
                ]
            ];

            $headers = [
                'Authorization: Bearer ' . $config['secret_key'],
                'Content-Type: application/x-www-form-urlencoded'
            ];

            $response = $this->httpPost($config['api_url'] . '/payment_intents', http_build_query($paymentData), $headers);
            $result = json_decode($response, true);

            if (isset($result['id'])) {
                return [
                    'code' => 1,
                    'msg' => 'Stripe支付创建成功',
                    'data' => [
                        'client_secret' => $result['client_secret'],
                        'payment_intent_id' => $result['id']
                    ]
                ];
            } else {
                return ['code' => 0, 'msg' => 'Stripe支付创建失败'];
            }
        } catch (\Exception $e) {
            Log::error('Stripe支付处理失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'Stripe支付处理失败'];
        }
    }

    /**
     * 处理数字货币支付
     */
    private function processCrypto(array $paymentOrder): array
    {
        try {
            $config = config('payment.crypto');
            $currency = $paymentOrder['currency'];
            
            if (!isset($config['supported_currencies'][$currency])) {
                return ['code' => 0, 'msg' => '不支持的数字货币'];
            }

            // 生成收款地址
            $address = $this->generateCryptoAddress($currency);
            if (!$address) {
                return ['code' => 0, 'msg' => '生成收款地址失败'];
            }

            // 计算到期时间
            $expireTime = time() + ($config['expire_minutes'] * 60);

            return [
                'code' => 1,
                'msg' => '数字货币支付创建成功',
                'data' => [
                    'address' => $address,
                    'amount' => $paymentOrder['amount'],
                    'currency' => $currency,
                    'expire_time' => $expireTime,
                    'qr_code' => $this->generateQrCode($currency . ':' . $address . '?amount=' . $paymentOrder['amount'])
                ]
            ];
        } catch (\Exception $e) {
            Log::error('数字货币支付处理失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '数字货币支付处理失败'];
        }
    }

    /**
     * 支付回调处理
     */
    public function handleCallback(string $paymentMethod, array $data): array
    {
        try {
            switch ($paymentMethod) {
                case 'alipay':
                    return $this->handleAlipayCallback($data);
                case 'wechat':
                    return $this->handleWechatCallback($data);
                case 'paypal':
                    return $this->handlePaypalCallback($data);
                case 'stripe':
                    return $this->handleStripeCallback($data);
                case 'crypto':
                    return $this->handleCryptoCallback($data);
                default:
                    return ['code' => 0, 'msg' => '不支持的支付方式'];
            }
        } catch (\Exception $e) {
            Log::error('支付回调处理失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '回调处理失败'];
        }
    }

    /**
     * 验证支付数据
     */
    private function validatePaymentData(array $data): array
    {
        if (empty($data['user_id'])) {
            return ['code' => 0, 'msg' => '用户ID不能为空'];
        }

        if (empty($data['amount']) || $data['amount'] <= 0) {
            return ['code' => 0, 'msg' => '支付金额无效'];
        }

        if (empty($data['payment_method']) || !isset(self::PAYMENT_METHODS[$data['payment_method']])) {
            return ['code' => 0, 'msg' => '支付方式无效'];
        }

        return ['code' => 1, 'msg' => '验证通过'];
    }

    /**
     * 生成订单ID
     */
    private function generateOrderId(): string
    {
        return 'PAY' . date('YmdHis') . mt_rand(1000, 9999);
    }

    /**
     * 生成随机字符串
     */
    private function generateNonceStr(int $length = 32): string
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $str = '';
        for ($i = 0; $i < $length; $i++) {
            $str .= $chars[mt_rand(0, strlen($chars) - 1)];
        }
        return $str;
    }

    /**
     * 生成支付宝签名
     */
    private function generateAlipaySign(array $params, string $privateKey): string
    {
        unset($params['sign']);
        ksort($params);
        
        $stringToBeSigned = '';
        foreach ($params as $k => $v) {
            if ($v !== '' && $v !== null) {
                $stringToBeSigned .= $k . '=' . $v . '&';
            }
        }
        $stringToBeSigned = rtrim($stringToBeSigned, '&');

        $res = openssl_get_privatekey($privateKey);
        openssl_sign($stringToBeSigned, $sign, $res, OPENSSL_ALGO_SHA256);
        openssl_free_key($res);
        
        return base64_encode($sign);
    }

    /**
     * 生成微信支付签名
     */
    private function generateWechatSign(array $params, string $key): string
    {
        unset($params['sign']);
        ksort($params);
        
        $stringToBeSigned = '';
        foreach ($params as $k => $v) {
            if ($v !== '' && $v !== null) {
                $stringToBeSigned .= $k . '=' . $v . '&';
            }
        }
        $stringToBeSigned .= 'key=' . $key;
        
        return strtoupper(md5($stringToBeSigned));
    }

    /**
     * 数组转XML
     */
    private function arrayToXml(array $data): string
    {
        $xml = '<xml>';
        foreach ($data as $key => $value) {
            $xml .= '<' . $key . '>' . $value . '</' . $key . '>';
        }
        $xml .= '</xml>';
        return $xml;
    }

    /**
     * XML转数组
     */
    private function xmlToArray(string $xml): array
    {
        return json_decode(json_encode(simplexml_load_string($xml)), true);
    }

    /**
     * HTTP POST请求
     */
    private function httpPost(string $url, string $data, array $headers = []): string
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        if (!empty($headers)) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        }
        
        $response = curl_exec($ch);
        curl_close($ch);
        
        return $response;
    }

    /**
     * 保存支付订单
     */
    private function savePaymentOrder(array $order): int
    {
        // 这里应该保存到数据库
        // 简化处理，返回模拟ID
        return mt_rand(1000, 9999);
    }

    /**
     * 更新支付状态
     */
    private function updatePaymentStatus(int $paymentId, int $status, array $data = []): bool
    {
        // 这里应该更新数据库
        Log::info("更新支付状态", [
            'payment_id' => $paymentId,
            'status' => $status,
            'data' => $data
        ]);
        
        return true;
    }

    /**
     * 获取PayPal访问令牌
     */
    private function getPaypalAccessToken(array $config): string
    {
        $cacheKey = 'paypal_access_token';
        $token = Cache::get($cacheKey);
        
        if ($token) {
            return $token;
        }

        $auth = base64_encode($config['client_id'] . ':' . $config['client_secret']);
        $headers = [
            'Authorization: Basic ' . $auth,
            'Content-Type: application/x-www-form-urlencoded'
        ];

        $response = $this->httpPost($config['api_url'] . '/oauth2/token', 'grant_type=client_credentials', $headers);
        $result = json_decode($response, true);

        if (isset($result['access_token'])) {
            Cache::set($cacheKey, $result['access_token'], $result['expires_in'] - 60);
            return $result['access_token'];
        }

        return '';
    }

    /**
     * 生成数字货币地址
     */
    private function generateCryptoAddress(string $currency): string
    {
        // 这里应该调用区块链节点API生成地址
        // 简化处理，返回模拟地址
        $addresses = [
            'BTC' => '**********************************',
            'ETH' => '******************************************',
            'USDT' => '******************************************'
        ];

        return $addresses[$currency] ?? '';
    }

    /**
     * 生成二维码
     */
    private function generateQrCode(string $data): string
    {
        // 这里应该生成真正的二维码
        // 简化处理，返回二维码API链接
        return 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' . urlencode($data);
    }

    /**
     * 处理支付宝回调
     */
    private function handleAlipayCallback(array $data): array
    {
        // 验证签名和处理回调逻辑
        return ['code' => 1, 'msg' => '处理成功'];
    }

    /**
     * 处理微信支付回调
     */
    private function handleWechatCallback(array $data): array
    {
        // 验证签名和处理回调逻辑
        return ['code' => 1, 'msg' => '处理成功'];
    }

    /**
     * 处理PayPal回调
     */
    private function handlePaypalCallback(array $data): array
    {
        // 验证和处理PayPal回调
        return ['code' => 1, 'msg' => '处理成功'];
    }

    /**
     * 处理Stripe回调
     */
    private function handleStripeCallback(array $data): array
    {
        // 验证和处理Stripe回调
        return ['code' => 1, 'msg' => '处理成功'];
    }

    /**
     * 处理数字货币回调
     */
    private function handleCryptoCallback(array $data): array
    {
        // 验证区块链交易和处理回调
        return ['code' => 1, 'msg' => '处理成功'];
    }
}
