<?php
/**
 * K线数据初始化脚本
 * 用于生成模拟的K线数据
 */

require_once __DIR__ . '/vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;

// 初始化ThinkPHP
$app = new think\App();
$app->initialize();

class KlineDataInitializer
{
    private $symbols = ['BTCUSDT', 'ETHUSDT', 'LTCUSDT', 'EOSUSDT', 'XRPUSDT'];
    private $intervals = ['1m', '5m', '15m', '1h', '4h', '1d'];
    private $basePrices = [
        'BTCUSDT' => 45000,
        'ETHUSDT' => 3000,
        'LTCUSDT' => 150,
        'EOSUSDT' => 5,
        'XRPUSDT' => 0.6
    ];

    public function __construct()
    {
        echo "K线数据初始化器启动...\n";
    }

    /**
     * 初始化所有交易对的K线数据
     */
    public function initAllKlines()
    {
        foreach ($this->symbols as $symbol) {
            echo "正在初始化 {$symbol} 的K线数据...\n";
            
            foreach ($this->intervals as $interval) {
                $this->generateKlineData($symbol, $interval);
                echo "  - {$interval} 周期数据生成完成\n";
            }
        }
        
        echo "所有K线数据初始化完成！\n";
    }

    /**
     * 生成指定交易对和时间周期的K线数据
     */
    private function generateKlineData($symbol, $interval, $count = 500)
    {
        $intervalSeconds = $this->getIntervalSeconds($interval);
        $basePrice = $this->basePrices[$symbol] ?? 100;
        $currentTime = time();
        
        // 清除现有数据
        Db::name('klines')->where([
            'symbol' => $symbol,
            'interval' => $interval
        ])->delete();
        
        $data = [];
        $price = $basePrice;
        
        for ($i = $count; $i > 0; $i--) {
            $openTime = $currentTime - ($i * $intervalSeconds);
            $closeTime = $openTime + $intervalSeconds - 1;
            
            // 生成价格数据（随机游走）
            $volatility = $this->getVolatility($symbol, $interval);
            $change = (mt_rand(-100, 100) / 100) * $volatility;
            
            $open = $price;
            $close = $open * (1 + $change);
            $high = max($open, $close) * (1 + mt_rand(0, 50) / 10000);
            $low = min($open, $close) * (1 - mt_rand(0, 50) / 10000);
            
            // 确保价格合理性
            $close = max($low, min($high, $close));
            
            // 生成成交量
            $volume = $this->generateVolume($symbol, $interval);
            $amount = $volume * (($open + $close) / 2);
            
            $data[] = [
                'symbol' => $symbol,
                'interval' => $interval,
                'open_time' => $openTime,
                'close_time' => $closeTime,
                'open_price' => round($open, 8),
                'high_price' => round($high, 8),
                'low_price' => round($low, 8),
                'close_price' => round($close, 8),
                'volume' => round($volume, 8),
                'amount' => round($amount, 8),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $price = $close; // 下一根K线的起始价格
            
            // 批量插入（每100条）
            if (count($data) >= 100) {
                Db::name('klines')->insertAll($data);
                $data = [];
            }
        }
        
        // 插入剩余数据
        if (!empty($data)) {
            Db::name('klines')->insertAll($data);
        }
    }

    /**
     * 获取时间周期对应的秒数
     */
    private function getIntervalSeconds($interval)
    {
        $intervals = [
            '1m' => 60,
            '3m' => 180,
            '5m' => 300,
            '15m' => 900,
            '30m' => 1800,
            '1h' => 3600,
            '2h' => 7200,
            '4h' => 14400,
            '6h' => 21600,
            '8h' => 28800,
            '12h' => 43200,
            '1d' => 86400,
            '3d' => 259200,
            '1w' => 604800,
            '1M' => 2592000
        ];

        return $intervals[$interval] ?? 60;
    }

    /**
     * 获取波动率
     */
    private function getVolatility($symbol, $interval)
    {
        $baseVolatility = [
            'BTCUSDT' => 0.02,  // 2%
            'ETHUSDT' => 0.03,  // 3%
            'LTCUSDT' => 0.04,  // 4%
            'EOSUSDT' => 0.05,  // 5%
            'XRPUSDT' => 0.06   // 6%
        ];

        $intervalMultiplier = [
            '1m' => 0.1,
            '5m' => 0.2,
            '15m' => 0.3,
            '1h' => 0.5,
            '4h' => 0.8,
            '1d' => 1.0
        ];

        $base = $baseVolatility[$symbol] ?? 0.03;
        $multiplier = $intervalMultiplier[$interval] ?? 0.5;

        return $base * $multiplier;
    }

    /**
     * 生成成交量
     */
    private function generateVolume($symbol, $interval)
    {
        $baseVolume = [
            'BTCUSDT' => 100,
            'ETHUSDT' => 1000,
            'LTCUSDT' => 500,
            'EOSUSDT' => 10000,
            'XRPUSDT' => 50000
        ];

        $intervalMultiplier = [
            '1m' => 1,
            '5m' => 3,
            '15m' => 8,
            '1h' => 20,
            '4h' => 60,
            '1d' => 200
        ];

        $base = $baseVolume[$symbol] ?? 100;
        $multiplier = $intervalMultiplier[$interval] ?? 1;
        
        // 添加随机性
        $randomFactor = mt_rand(50, 150) / 100; // 0.5 - 1.5倍随机
        
        return $base * $multiplier * $randomFactor;
    }

    /**
     * 更新最新价格（模拟实时更新）
     */
    public function updateLatestPrices()
    {
        echo "更新最新价格...\n";
        
        foreach ($this->symbols as $symbol) {
            // 获取最新1分钟K线
            $latest = Db::name('klines')
                       ->where('symbol', $symbol)
                       ->where('interval', '1m')
                       ->order('open_time', 'desc')
                       ->find();
            
            if ($latest) {
                $currentTime = time();
                $intervalStart = intval($currentTime / 60) * 60;
                
                // 如果当前分钟的K线不存在，创建新的
                if ($latest['open_time'] < $intervalStart) {
                    $newPrice = $latest['close_price'] * (1 + (mt_rand(-50, 50) / 10000));
                    
                    $data = [
                        'symbol' => $symbol,
                        'interval' => '1m',
                        'open_time' => $intervalStart,
                        'close_time' => $intervalStart + 59,
                        'open_price' => $latest['close_price'],
                        'high_price' => max($latest['close_price'], $newPrice),
                        'low_price' => min($latest['close_price'], $newPrice),
                        'close_price' => $newPrice,
                        'volume' => $this->generateVolume($symbol, '1m'),
                        'amount' => $newPrice * $this->generateVolume($symbol, '1m'),
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];
                    
                    Db::name('klines')->insert($data);
                    echo "  - {$symbol}: 新价格 {$newPrice}\n";
                }
            }
        }
    }

    /**
     * 清理旧数据
     */
    public function cleanOldData($days = 30)
    {
        echo "清理 {$days} 天前的数据...\n";
        
        $cutoffTime = time() - ($days * 86400);
        
        $deleted = Db::name('klines')
                    ->where('open_time', '<', $cutoffTime)
                    ->delete();
        
        echo "已删除 {$deleted} 条旧数据\n";
    }

    /**
     * 显示统计信息
     */
    public function showStats()
    {
        echo "\n=== K线数据统计 ===\n";
        
        foreach ($this->symbols as $symbol) {
            echo "\n{$symbol}:\n";
            
            foreach ($this->intervals as $interval) {
                $count = Db::name('klines')
                          ->where('symbol', $symbol)
                          ->where('interval', $interval)
                          ->count();
                
                echo "  {$interval}: {$count} 条\n";
            }
        }
        
        $total = Db::name('klines')->count();
        echo "\n总计: {$total} 条K线数据\n";
    }
}

// 执行初始化
if (php_sapi_name() === 'cli') {
    $initializer = new KlineDataInitializer();
    
    $command = $argv[1] ?? 'init';
    
    switch ($command) {
        case 'init':
            $initializer->initAllKlines();
            break;
            
        case 'update':
            $initializer->updateLatestPrices();
            break;
            
        case 'clean':
            $days = intval($argv[2] ?? 30);
            $initializer->cleanOldData($days);
            break;
            
        case 'stats':
            $initializer->showStats();
            break;
            
        default:
            echo "用法:\n";
            echo "  php init_kline_data.php init   - 初始化所有K线数据\n";
            echo "  php init_kline_data.php update - 更新最新价格\n";
            echo "  php init_kline_data.php clean [天数] - 清理旧数据\n";
            echo "  php init_kline_data.php stats  - 显示统计信息\n";
    }
} else {
    echo "请在命令行环境下运行此脚本\n";
}
?>
