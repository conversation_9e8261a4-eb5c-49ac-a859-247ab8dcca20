<?php
/**
 * GVD交易平台环境检查脚本
 * 检查PHP 8.1、MySQL 8.0、Redis等环境配置
 */

echo "=== GVD交易平台环境检查 ===\n\n";

$errors = [];
$warnings = [];

// 1. 检查PHP版本
echo "1. 检查PHP版本...\n";
$phpVersion = PHP_VERSION;
echo "   当前PHP版本: {$phpVersion}\n";

if (version_compare($phpVersion, '8.1.0', '<')) {
    $errors[] = "PHP版本过低，需要PHP 8.1.0或更高版本";
} else {
    echo "   ✅ PHP版本符合要求\n";
}

// 2. 检查PHP扩展
echo "\n2. 检查PHP扩展...\n";
$requiredExtensions = [
    'pdo',
    'pdo_mysql',
    'redis',
    'curl',
    'json',
    'mbstring',
    'openssl',
    'fileinfo',
    'gd',
    'zip'
];

foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "   ✅ {$ext} 扩展已安装\n";
    } else {
        $errors[] = "缺少PHP扩展: {$ext}";
        echo "   ❌ {$ext} 扩展未安装\n";
    }
}

// 3. 检查目录权限
echo "\n3. 检查目录权限...\n";
$writableDirs = [
    '../runtime',
    '../public/uploads',
    '../storage/logs'
];

foreach ($writableDirs as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "   ✅ {$dir} 目录可写\n";
        } else {
            $errors[] = "目录不可写: {$dir}";
            echo "   ❌ {$dir} 目录不可写\n";
        }
    } else {
        if (mkdir($dir, 0755, true)) {
            echo "   ✅ {$dir} 目录已创建\n";
        } else {
            $errors[] = "无法创建目录: {$dir}";
            echo "   ❌ 无法创建目录: {$dir}\n";
        }
    }
}

// 4. 检查配置文件
echo "\n4. 检查配置文件...\n";
$configFiles = [
    '../config/database.php',
    '../config/cache.php',
    '../config/app.php'
];

foreach ($configFiles as $file) {
    if (file_exists($file)) {
        echo "   ✅ {$file} 配置文件存在\n";
    } else {
        $errors[] = "配置文件不存在: {$file}";
        echo "   ❌ {$file} 配置文件不存在\n";
    }
}

// 5. 检查数据库连接
echo "\n5. 检查数据库连接...\n";
try {
    $config = include '../config/database.php';
    $dbConfig = $config['connections']['mysql'];
    
    $dsn = "mysql:host={$dbConfig['hostname']};port={$dbConfig['hostport']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
    
    // 检查MySQL版本
    $version = $pdo->query('SELECT VERSION()')->fetchColumn();
    echo "   MySQL版本: {$version}\n";
    
    if (version_compare($version, '8.0.0', '>=')) {
        echo "   ✅ MySQL版本符合要求\n";
    } else {
        $warnings[] = "MySQL版本建议使用8.0或更高版本";
        echo "   ⚠️ MySQL版本建议升级到8.0\n";
    }
    
    // 检查数据库是否存在
    $dbName = $dbConfig['database'];
    $pdo->exec("USE {$dbName}");
    echo "   ✅ 数据库连接成功\n";
    
} catch (PDOException $e) {
    $errors[] = "数据库连接失败: " . $e->getMessage();
    echo "   ❌ 数据库连接失败: " . $e->getMessage() . "\n";
}

// 6. 检查Redis连接
echo "\n6. 检查Redis连接...\n";
try {
    $config = include '../config/cache.php';
    $redisConfig = $config['stores']['redis'];
    
    $redis = new Redis();
    $redis->connect($redisConfig['host'], $redisConfig['port']);
    
    if (!empty($redisConfig['password'])) {
        $redis->auth($redisConfig['password']);
    }
    
    $redis->ping();
    echo "   ✅ Redis连接成功\n";
    
    // 检查Redis版本
    $info = $redis->info();
    if (isset($info['redis_version'])) {
        echo "   Redis版本: {$info['redis_version']}\n";
    }
    
} catch (Exception $e) {
    $errors[] = "Redis连接失败: " . $e->getMessage();
    echo "   ❌ Redis连接失败: " . $e->getMessage() . "\n";
}

// 7. 检查Composer依赖
echo "\n7. 检查Composer依赖...\n";
if (file_exists('../vendor/autoload.php')) {
    echo "   ✅ Composer依赖已安装\n";
    
    // 检查关键依赖包
    $composerLock = json_decode(file_get_contents('../composer.lock'), true);
    $packages = $composerLock['packages'] ?? [];
    
    $requiredPackages = [
        'topthink/framework',
        'topthink/think-orm',
        'topthink/think-cache'
    ];
    
    foreach ($requiredPackages as $package) {
        $found = false;
        foreach ($packages as $pkg) {
            if ($pkg['name'] === $package) {
                echo "   ✅ {$package} 已安装 (版本: {$pkg['version']})\n";
                $found = true;
                break;
            }
        }
        if (!$found) {
            $warnings[] = "依赖包可能缺失: {$package}";
            echo "   ⚠️ {$package} 可能缺失\n";
        }
    }
} else {
    $errors[] = "Composer依赖未安装，请运行: composer install";
    echo "   ❌ Composer依赖未安装\n";
}

// 8. 检查内存和执行时间限制
echo "\n8. 检查PHP配置...\n";
$memoryLimit = ini_get('memory_limit');
$maxExecutionTime = ini_get('max_execution_time');
$uploadMaxFilesize = ini_get('upload_max_filesize');
$postMaxSize = ini_get('post_max_size');

echo "   内存限制: {$memoryLimit}\n";
echo "   执行时间限制: {$maxExecutionTime}秒\n";
echo "   上传文件大小限制: {$uploadMaxFilesize}\n";
echo "   POST数据大小限制: {$postMaxSize}\n";

// 转换为字节进行比较
function parseSize($size) {
    $unit = preg_replace('/[^bkmgtpezy]/i', '', $size);
    $size = preg_replace('/[^0-9\.]/', '', $size);
    if ($unit) {
        return round($size * pow(1024, stripos('bkmgtpezy', $unit[0])));
    }
    return round($size);
}

if (parseSize($memoryLimit) < parseSize('256M')) {
    $warnings[] = "内存限制建议设置为256M或更高";
    echo "   ⚠️ 建议增加内存限制到256M\n";
} else {
    echo "   ✅ 内存限制充足\n";
}

// 9. 生成环境报告
echo "\n=== 环境检查报告 ===\n";

if (empty($errors)) {
    echo "✅ 环境检查通过！系统可以正常部署。\n";
} else {
    echo "❌ 发现 " . count($errors) . " 个错误，需要修复后才能部署：\n";
    foreach ($errors as $i => $error) {
        echo "   " . ($i + 1) . ". {$error}\n";
    }
}

if (!empty($warnings)) {
    echo "\n⚠️ 发现 " . count($warnings) . " 个警告：\n";
    foreach ($warnings as $i => $warning) {
        echo "   " . ($i + 1) . ". {$warning}\n";
    }
}

// 10. 生成修复建议
if (!empty($errors) || !empty($warnings)) {
    echo "\n=== 修复建议 ===\n";
    
    if (in_array("PHP版本过低，需要PHP 8.1.0或更高版本", $errors)) {
        echo "• 升级PHP到8.1版本：\n";
        echo "  Ubuntu/Debian: sudo apt update && sudo apt install php8.1\n";
        echo "  CentOS/RHEL: sudo yum install php81\n";
    }
    
    if (array_filter($errors, fn($e) => strpos($e, 'PHP扩展') !== false)) {
        echo "• 安装缺失的PHP扩展：\n";
        echo "  Ubuntu/Debian: sudo apt install php8.1-mysql php8.1-redis php8.1-curl php8.1-gd php8.1-zip\n";
        echo "  CentOS/RHEL: sudo yum install php81-php-mysqlnd php81-php-redis php81-php-curl\n";
    }
    
    if (array_filter($errors, fn($e) => strpos($e, '目录不可写') !== false)) {
        echo "• 设置目录权限：\n";
        echo "  sudo chown -R www-data:www-data /path/to/gvd\n";
        echo "  sudo chmod -R 755 /path/to/gvd\n";
        echo "  sudo chmod -R 777 /path/to/gvd/runtime\n";
    }
    
    if (array_filter($errors, fn($e) => strpos($e, 'Composer') !== false)) {
        echo "• 安装Composer依赖：\n";
        echo "  cd /path/to/gvd && composer install --no-dev --optimize-autoloader\n";
    }
}

echo "\n=== 检查完成 ===\n";
exit(empty($errors) ? 0 : 1);
?>
