<?php
declare (strict_types = 1);

namespace app\index\controller;

use app\BaseController;
use app\common\model\User;
use app\common\model\AgentCommission;
use app\common\service\AgentService;
use think\facade\View;
use think\facade\Session;
use think\facade\Request;
use think\facade\Validate;

/**
 * 代理系统控制器
 */
class Agent extends BaseController
{
    protected $agentService;

    public function initialize()
    {
        parent::initialize();
        $this->agentService = new AgentService();
        
        // 检查登录状态
        if (!Session::has('user_id')) {
            $this->redirect('/auth/login');
        }
    }

    /**
     * 代理中心首页
     */
    public function index()
    {
        $userId = Session::get('user_id');
        
        // 获取用户信息
        $user = User::find($userId);
        
        // 获取代理统计
        $agentStats = $this->agentService->getAgentStats($userId);
        
        // 获取下级用户列表
        $subUsers = User::where('parent_id', $userId)
                       ->order('created_at desc')
                       ->limit(10)
                       ->select();
        
        // 获取最近佣金记录
        $recentCommissions = AgentCommission::where('agent_id', $userId)
                                           ->order('created_at desc')
                                           ->limit(10)
                                           ->select();
        
        View::assign([
            'user' => $user,
            'agent_stats' => $agentStats,
            'sub_users' => $subUsers,
            'recent_commissions' => $recentCommissions,
            'title' => '代理中心 - GVD数字货币交易平台'
        ]);
        
        return View::fetch('agent/index');
    }

    /**
     * 邀请管理
     */
    public function invite()
    {
        $userId = Session::get('user_id');
        
        // 获取用户邀请码
        $user = User::find($userId);
        if (!$user->invite_code) {
            $user->invite_code = User::generateInviteCode();
            $user->save();
        }
        
        // 获取邀请链接
        $inviteUrl = Request::domain() . '/auth/register?invite=' . $user->invite_code;
        
        // 获取邀请统计
        $inviteStats = $this->agentService->getInviteStats($userId);
        
        View::assign([
            'user' => $user,
            'invite_url' => $inviteUrl,
            'invite_stats' => $inviteStats,
            'title' => '邀请管理 - GVD数字货币交易平台'
        ]);
        
        return View::fetch('agent/invite');
    }

    /**
     * 下级用户管理
     */
    public function users()
    {
        $userId = Session::get('user_id');
        $page = Request::get('page', 1);
        $level = Request::get('level', 1); // 1:直推 2:二级 3:三级
        
        $users = $this->agentService->getSubUsers($userId, $level, $page);
        
        if (Request::isAjax()) {
            return json([
                'code' => 1,
                'data' => $users->items(),
                'total' => $users->total()
            ]);
        }
        
        View::assign([
            'users' => $users,
            'level' => $level,
            'title' => '下级用户 - GVD数字货币交易平台'
        ]);
        
        return View::fetch('agent/users');
    }

    /**
     * 佣金记录
     */
    public function commissions()
    {
        $userId = Session::get('user_id');
        $page = Request::get('page', 1);
        $type = Request::get('type', ''); // trade:交易佣金 contract:合约佣金
        
        $commissions = AgentCommission::where('agent_id', $userId);
        
        if ($type) {
            $commissions = $commissions->where('type', $type);
        }
        
        $commissions = $commissions->order('created_at desc')
                                  ->paginate([
                                      'list_rows' => 20,
                                      'page' => $page
                                  ]);
        
        if (Request::isAjax()) {
            return json([
                'code' => 1,
                'data' => $commissions->items(),
                'total' => $commissions->total()
            ]);
        }
        
        View::assign([
            'commissions' => $commissions,
            'type' => $type,
            'title' => '佣金记录 - GVD数字货币交易平台'
        ]);
        
        return View::fetch('agent/commissions');
    }

    /**
     * 申请提现佣金
     */
    public function withdrawCommission()
    {
        if (!Request::isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }
        
        $userId = Session::get('user_id');
        $amount = Request::post('amount');
        
        // 验证数据
        $validate = Validate::rule([
            'amount' => 'require|float|gt:0'
        ])->message([
            'amount.require' => '请输入提现金额',
            'amount.float' => '金额格式错误',
            'amount.gt' => '金额必须大于0'
        ]);

        if (!$validate->check(['amount' => $amount])) {
            return json(['code' => 0, 'msg' => $validate->getError()]);
        }
        
        // 申请提现
        $result = $this->agentService->withdrawCommission($userId, $amount);
        
        return json($result);
    }

    /**
     * 获取代理等级配置
     */
    public function levelConfig()
    {
        $config = $this->agentService->getLevelConfig();
        
        return json([
            'code' => 1,
            'data' => $config
        ]);
    }

    /**
     * 代理推广素材
     */
    public function materials()
    {
        $materials = $this->agentService->getPromotionMaterials();
        
        View::assign([
            'materials' => $materials,
            'title' => '推广素材 - GVD数字货币交易平台'
        ]);
        
        return View::fetch('agent/materials');
    }

    /**
     * 代理教程
     */
    public function tutorial()
    {
        View::assign([
            'title' => '代理教程 - GVD数字货币交易平台'
        ]);
        
        return View::fetch('agent/tutorial');
    }

    /**
     * 生成邀请二维码
     */
    public function qrcode()
    {
        $userId = Session::get('user_id');
        
        $user = User::find($userId);
        if (!$user->invite_code) {
            $user->invite_code = User::generateInviteCode();
            $user->save();
        }
        
        $inviteUrl = Request::domain() . '/auth/register?invite=' . $user->invite_code;
        
        // 生成二维码
        $qrcode = $this->agentService->generateQrcode($inviteUrl);
        
        return json([
            'code' => 1,
            'data' => [
                'qrcode' => $qrcode,
                'invite_url' => $inviteUrl
            ]
        ]);
    }
}
