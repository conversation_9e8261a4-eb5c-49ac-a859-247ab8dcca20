<?php
declare (strict_types = 1);

namespace app\middleware;

use Closure;
use think\Request;
use think\Response;
use think\facade\Session;
use app\common\service\AgentService;

/**
 * 代理认证中间件
 */
class AgentAuth
{
    protected $agentService;

    public function __construct(AgentService $agentService)
    {
        $this->agentService = $agentService;
    }

    /**
     * 处理请求
     *
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 获取代理ID
        $agentId = Session::get('agent_id');
        
        if (empty($agentId)) {
            if ($request->isAjax()) {
                return json([
                    'code' => 401,
                    'msg'  => '请先登录',
                    'data' => null
                ])->code(401);
            } else {
                return redirect('/agent/login');
            }
        }

        // 获取代理信息
        $agent = $this->agentService->getAgentById($agentId);
        
        if (!$agent || $agent['status'] != 1) {
            Session::delete('agent_id');
            
            if ($request->isAjax()) {
                return json([
                    'code' => 401,
                    'msg'  => '代理不存在或已被禁用',
                    'data' => null
                ])->code(401);
            } else {
                return redirect('/agent/login');
            }
        }

        // 将代理信息存储到请求中
        $request->agent = $agent;
        $request->agent_id = $agent['id'];

        return $next($request);
    }
}
