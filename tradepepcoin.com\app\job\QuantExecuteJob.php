<?php
declare (strict_types = 1);

namespace app\job;

use think\queue\Job;
use app\common\service\QuantTradingService;

/**
 * 量化策略执行任务
 */
class QuantExecuteJob
{
    /**
     * 执行任务
     */
    public function fire(Job $job, $data)
    {
        try {
            $quantService = new QuantTradingService();
            $strategyId = $data['strategy_id'];
            
            $result = $quantService->executeStrategy($strategyId);

            if ($result['code']) {
                // 任务执行成功
                \think\facade\Log::info('量化策略执行成功', [
                    'strategy_id' => $strategyId,
                    'result' => $result['data']
                ]);
                $job->delete();
            } else {
                // 任务执行失败，记录日志但不重试
                \think\facade\Log::warning('量化策略执行失败', [
                    'strategy_id' => $strategyId,
                    'error' => $result['msg']
                ]);
                $job->delete();
            }
        } catch (\Exception $e) {
            \think\facade\Log::error('量化策略执行任务异常: ' . $e->getMessage());
            $job->delete();
        }
    }

    /**
     * 任务失败处理
     */
    public function failed($data)
    {
        \think\facade\Log::error('量化策略执行任务最终失败', $data);
    }
}
