-- GVD数字货币交易平台数据库结构
-- 版本: 2.0.0
-- 生成时间: 2024-08-04

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 用户表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_users`;
CREATE TABLE `gvd_users` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `id_card` varchar(20) DEFAULT NULL COMMENT '身份证号',
  `user_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '用户类型:1正式用户,2测试用户',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `invite_code` varchar(20) NOT NULL COMMENT '邀请码',
  `inviter_id` int(11) DEFAULT '0' COMMENT '邀请人ID',
  `inviter_level1` int(11) DEFAULT '0' COMMENT '一级邀请人',
  `inviter_level2` int(11) DEFAULT '0' COMMENT '二级邀请人',
  `inviter_level3` int(11) DEFAULT '0' COMMENT '三级邀请人',
  `agent_id` int(11) DEFAULT '0' COMMENT '代理ID',
  `kyc_status` tinyint(1) DEFAULT '0' COMMENT 'KYC状态:0未认证,1已认证,2审核中,3已拒绝',
  `kyc_level` tinyint(1) DEFAULT '0' COMMENT 'KYC等级:0未认证,1初级,2高级',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `register_ip` varchar(50) DEFAULT NULL COMMENT '注册IP',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `login_count` int(11) DEFAULT '0' COMMENT '登录次数',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `invite_code` (`invite_code`),
  KEY `inviter_id` (`inviter_id`),
  KEY `agent_id` (`agent_id`),
  KEY `status` (`status`),
  KEY `user_type` (`user_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- ----------------------------
-- 用户资产表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_user_assets`;
CREATE TABLE `gvd_user_assets` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `coin_symbol` varchar(20) NOT NULL COMMENT '币种符号',
  `available` decimal(20,8) DEFAULT '0.00000000' COMMENT '可用余额',
  `frozen` decimal(20,8) DEFAULT '0.00000000' COMMENT '冻结余额',
  `total` decimal(20,8) DEFAULT '0.00000000' COMMENT '总余额',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_coin` (`user_id`,`coin_symbol`),
  KEY `user_id` (`user_id`),
  KEY `coin_symbol` (`coin_symbol`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户资产表';

-- ----------------------------
-- 杠杆订单表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_leverage_orders`;
CREATE TABLE `gvd_leverage_orders` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `symbol` varchar(20) NOT NULL COMMENT '交易对',
  `order_type` tinyint(1) NOT NULL COMMENT '订单类型:1做多,2做空',
  `margin` decimal(20,8) NOT NULL COMMENT '保证金',
  `leverage` int(11) NOT NULL COMMENT '杠杆倍数',
  `quantity` decimal(20,8) NOT NULL COMMENT '交易数量',
  `open_price` decimal(20,8) NOT NULL COMMENT '开仓价格',
  `close_price` decimal(20,8) DEFAULT '0.00000000' COMMENT '平仓价格',
  `stop_loss_price` decimal(20,8) DEFAULT '0.00000000' COMMENT '止损价格',
  `take_profit_price` decimal(20,8) DEFAULT '0.00000000' COMMENT '止盈价格',
  `liquidation_price` decimal(20,8) DEFAULT '0.00000000' COMMENT '强平价格',
  `profit_loss` decimal(20,8) DEFAULT '0.00000000' COMMENT '盈亏金额',
  `fee` decimal(20,8) DEFAULT '0.00000000' COMMENT '手续费',
  `status` varchar(20) DEFAULT 'open' COMMENT '状态:open开仓,closed平仓,liquidated强平',
  `close_type` varchar(20) DEFAULT NULL COMMENT '平仓类型:manual手动,stop_loss止损,take_profit止盈,liquidation强平',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  `closed_at` datetime DEFAULT NULL COMMENT '平仓时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `symbol` (`symbol`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='杠杆订单表';

-- ----------------------------
-- 期货合约表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_futures_contracts`;
CREATE TABLE `gvd_futures_contracts` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `symbol` varchar(20) NOT NULL COMMENT '合约符号',
  `base_coin` varchar(10) NOT NULL COMMENT '基础币种',
  `quote_coin` varchar(10) NOT NULL COMMENT '计价币种',
  `contract_type` varchar(20) NOT NULL COMMENT '合约类型:perpetual永续,delivery交割',
  `delivery_date` date DEFAULT NULL COMMENT '交割日期',
  `min_quantity` decimal(20,8) DEFAULT '0.00000001' COMMENT '最小交易数量',
  `max_quantity` decimal(20,8) DEFAULT '1000000.00000000' COMMENT '最大交易数量',
  `price_precision` int(11) DEFAULT '2' COMMENT '价格精度',
  `quantity_precision` int(11) DEFAULT '8' COMMENT '数量精度',
  `maker_fee` decimal(8,6) DEFAULT '0.000200' COMMENT 'Maker手续费',
  `taker_fee` decimal(8,6) DEFAULT '0.000500' COMMENT 'Taker手续费',
  `margin_rate` decimal(8,6) DEFAULT '0.100000' COMMENT '保证金比例',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `symbol` (`symbol`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='期货合约表';

-- ----------------------------
-- 期货订单表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_futures_orders`;
CREATE TABLE `gvd_futures_orders` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `contract_symbol` varchar(20) NOT NULL COMMENT '合约符号',
  `order_type` varchar(20) NOT NULL COMMENT '订单类型:limit限价,market市价',
  `side` varchar(10) NOT NULL COMMENT '方向:buy买入,sell卖出',
  `quantity` decimal(20,8) NOT NULL COMMENT '委托数量',
  `price` decimal(20,8) DEFAULT '0.00000000' COMMENT '委托价格',
  `filled_quantity` decimal(20,8) DEFAULT '0.00000000' COMMENT '成交数量',
  `avg_price` decimal(20,8) DEFAULT '0.00000000' COMMENT '成交均价',
  `margin` decimal(20,8) NOT NULL COMMENT '保证金',
  `fee` decimal(20,8) DEFAULT '0.00000000' COMMENT '手续费',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态:pending待成交,filled已成交,cancelled已取消,partial部分成交',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `filled_at` datetime DEFAULT NULL COMMENT '成交时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `contract_symbol` (`contract_symbol`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='期货订单表';

-- ----------------------------
-- 财务记录表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_financial_records`;
CREATE TABLE `gvd_financial_records` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `coin_symbol` varchar(20) NOT NULL COMMENT '币种',
  `amount` decimal(20,8) NOT NULL COMMENT '金额',
  `type` varchar(50) NOT NULL COMMENT '类型',
  `status` varchar(20) DEFAULT 'completed' COMMENT '状态',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `order_id` int(11) DEFAULT NULL COMMENT '关联订单ID',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `type` (`type`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务记录表';

-- ----------------------------
-- 期货持仓表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_futures_positions`;
CREATE TABLE `gvd_futures_positions` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `contract_symbol` varchar(20) NOT NULL COMMENT '合约符号',
  `quantity` decimal(20,8) NOT NULL COMMENT '持仓数量',
  `avg_price` decimal(20,8) NOT NULL COMMENT '平均价格',
  `margin` decimal(20,8) NOT NULL COMMENT '保证金',
  `unrealized_pnl` decimal(20,8) DEFAULT '0.00000000' COMMENT '未实现盈亏',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_contract` (`user_id`,`contract_symbol`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='期货持仓表';

-- ----------------------------
-- 期货交易记录表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_futures_trades`;
CREATE TABLE `gvd_futures_trades` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `contract_symbol` varchar(20) NOT NULL COMMENT '合约符号',
  `side` varchar(10) NOT NULL COMMENT '方向',
  `quantity` decimal(20,8) NOT NULL COMMENT '成交数量',
  `price` decimal(20,8) NOT NULL COMMENT '成交价格',
  `fee` decimal(20,8) DEFAULT '0.00000000' COMMENT '手续费',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `order_id` (`order_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='期货交易记录表';

-- ----------------------------
-- 管理员表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_admins`;
CREATE TABLE `gvd_admins` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `role` varchar(20) DEFAULT 'admin' COMMENT '角色',
  `permissions` text COMMENT '权限列表JSON',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `login_count` int(11) DEFAULT '0' COMMENT '登录次数',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- ----------------------------
-- 管理员日志表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_admin_log`;
CREATE TABLE `gvd_admin_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL COMMENT '管理员ID',
  `action` varchar(100) NOT NULL COMMENT '操作类型',
  `data` text COMMENT '操作数据',
  `ip` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(255) DEFAULT NULL COMMENT '用户代理',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `admin_id` (`admin_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员操作日志表';

-- ----------------------------
-- 代理日志表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_agent_log`;
CREATE TABLE `gvd_agent_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `agent_id` int(11) NOT NULL COMMENT '代理ID',
  `action` varchar(100) NOT NULL COMMENT '操作类型',
  `data` text COMMENT '操作数据',
  `ip` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(255) DEFAULT NULL COMMENT '用户代理',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `agent_id` (`agent_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代理操作日志表';

-- ----------------------------
-- 用户登录日志表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_user_login_log`;
CREATE TABLE `gvd_user_login_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `action` varchar(50) NOT NULL COMMENT '操作类型',
  `ip` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(255) DEFAULT NULL COMMENT '用户代理',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户登录日志表';

-- ----------------------------
-- 邀请记录表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_invite_records`;
CREATE TABLE `gvd_invite_records` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `inviter_id` int(11) NOT NULL COMMENT '邀请人ID',
  `invitee_id` int(11) NOT NULL COMMENT '被邀请人ID',
  `level` tinyint(1) DEFAULT '1' COMMENT '邀请层级',
  `commission` decimal(20,8) DEFAULT '0.00000000' COMMENT '佣金',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `inviter_id` (`inviter_id`),
  KEY `invitee_id` (`invitee_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邀请记录表';

-- ----------------------------
-- 代理佣金表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_agent_commission`;
CREATE TABLE `gvd_agent_commission` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `agent_id` int(11) NOT NULL COMMENT '代理ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `amount` decimal(20,8) NOT NULL COMMENT '佣金金额',
  `rate` decimal(8,6) NOT NULL COMMENT '佣金比例',
  `type` varchar(50) NOT NULL COMMENT '佣金类型',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `agent_id` (`agent_id`),
  KEY `user_id` (`user_id`),
  KEY `order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代理佣金表';

-- ----------------------------
-- 充值通知表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_deposit_notifications`;
CREATE TABLE `gvd_deposit_notifications` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `amount` decimal(20,8) NOT NULL COMMENT '充值金额',
  `coin_symbol` varchar(20) NOT NULL COMMENT '币种',
  `tx_hash` varchar(255) DEFAULT NULL COMMENT '交易哈希',
  `is_read` tinyint(1) DEFAULT '0' COMMENT '是否已读',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `is_read` (`is_read`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值通知表';

-- ----------------------------
-- 市场价格表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_market_prices`;
CREATE TABLE `gvd_market_prices` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `symbol` varchar(20) NOT NULL COMMENT '交易对',
  `price` decimal(20,8) NOT NULL COMMENT '价格',
  `change_24h` decimal(8,4) DEFAULT '0.0000' COMMENT '24小时涨跌幅',
  `volume_24h` decimal(20,8) DEFAULT '0.00000000' COMMENT '24小时交易量',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `symbol` (`symbol`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='市场价格表';

-- ----------------------------
-- K线数据表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_kline_data`;
CREATE TABLE `gvd_kline_data` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `symbol` varchar(20) NOT NULL COMMENT '交易对',
  `interval` varchar(10) NOT NULL COMMENT '时间间隔',
  `timestamp` int(11) NOT NULL COMMENT '时间戳',
  `open` decimal(20,8) NOT NULL COMMENT '开盘价',
  `high` decimal(20,8) NOT NULL COMMENT '最高价',
  `low` decimal(20,8) NOT NULL COMMENT '最低价',
  `close` decimal(20,8) NOT NULL COMMENT '收盘价',
  `volume` decimal(20,8) NOT NULL COMMENT '交易量',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `symbol_interval_timestamp` (`symbol`,`interval`,`timestamp`),
  KEY `symbol` (`symbol`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='K线数据表';

-- ----------------------------
-- 插入默认数据
-- ----------------------------

-- 插入默认管理员
INSERT INTO `gvd_admins` (`username`, `password`, `role`, `permissions`, `status`, `created_at`, `updated_at`) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'super_admin', '[]', 1, NOW(), NOW());

-- 插入期货合约
INSERT INTO `gvd_futures_contracts` (`symbol`, `base_coin`, `quote_coin`, `contract_type`, `delivery_date`, `min_quantity`, `max_quantity`, `price_precision`, `quantity_precision`, `maker_fee`, `taker_fee`, `margin_rate`, `status`, `created_at`, `updated_at`) VALUES
('BTCUSDT-PERP', 'BTC', 'USDT', 'perpetual', NULL, 0.001, 1000, 2, 3, 0.0002, 0.0005, 0.1, 1, NOW(), NOW()),
('ETHUSDT-PERP', 'ETH', 'USDT', 'perpetual', NULL, 0.01, 10000, 2, 2, 0.0002, 0.0005, 0.1, 1, NOW(), NOW()),
('BNBUSDT-PERP', 'BNB', 'USDT', 'perpetual', NULL, 0.1, 100000, 2, 1, 0.0002, 0.0005, 0.1, 1, NOW(), NOW());

-- ----------------------------
-- 客服会话表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_customer_sessions`;
CREATE TABLE `gvd_customer_sessions` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `session_id` varchar(32) NOT NULL COMMENT '会话ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `agent_id` int(11) DEFAULT NULL COMMENT '代理ID',
  `admin_id` int(11) DEFAULT NULL COMMENT '管理员ID',
  `status` varchar(20) DEFAULT 'active' COMMENT '会话状态:active活跃,closed关闭,waiting等待',
  `priority` tinyint(1) DEFAULT '1' COMMENT '优先级:1普通,2重要,3紧急',
  `last_message_time` datetime DEFAULT NULL COMMENT '最后消息时间',
  `unread_count_user` int(11) DEFAULT '0' COMMENT '用户未读消息数',
  `unread_count_agent` int(11) DEFAULT '0' COMMENT '代理未读消息数',
  `unread_count_admin` int(11) DEFAULT '0' COMMENT '管理员未读消息数',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `closed_at` datetime DEFAULT NULL COMMENT '关闭时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `session_id` (`session_id`),
  KEY `user_id` (`user_id`),
  KEY `agent_id` (`agent_id`),
  KEY `admin_id` (`admin_id`),
  KEY `status` (`status`),
  KEY `last_message_time` (`last_message_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服会话表';

-- ----------------------------
-- 客服消息表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_customer_messages`;
CREATE TABLE `gvd_customer_messages` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `session_id` varchar(32) NOT NULL COMMENT '会话ID',
  `sender_id` int(11) NOT NULL COMMENT '发送者ID',
  `sender_type` varchar(20) NOT NULL COMMENT '发送者类型:user用户,agent代理,admin管理员',
  `receiver_id` int(11) DEFAULT NULL COMMENT '接收者ID',
  `receiver_type` varchar(20) DEFAULT NULL COMMENT '接收者类型:user用户,agent代理,admin管理员',
  `message_type` varchar(20) DEFAULT 'text' COMMENT '消息类型:text文本,image图片,file文件,emoji表情',
  `content` text NOT NULL COMMENT '消息内容',
  `file_url` varchar(255) DEFAULT NULL COMMENT '文件URL',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `file_size` int(11) DEFAULT NULL COMMENT '文件大小',
  `is_read` tinyint(1) DEFAULT '0' COMMENT '是否已读',
  `read_at` datetime DEFAULT NULL COMMENT '已读时间',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `session_id` (`session_id`),
  KEY `sender_id` (`sender_id`),
  KEY `receiver_id` (`receiver_id`),
  KEY `created_at` (`created_at`),
  KEY `is_read` (`is_read`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服消息表';

-- ----------------------------
-- 客服快捷回复表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_customer_quick_replies`;
CREATE TABLE `gvd_customer_quick_replies` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL COMMENT '回复标题',
  `content` text NOT NULL COMMENT '回复内容',
  `category` varchar(50) DEFAULT 'general' COMMENT '分类:general通用,trading交易,account账户,technical技术',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `created_by` int(11) DEFAULT NULL COMMENT '创建者ID',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `category` (`category`),
  KEY `is_active` (`is_active`),
  KEY `sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服快捷回复表';

-- ----------------------------
-- 客服在线状态表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_customer_online_status`;
CREATE TABLE `gvd_customer_online_status` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `user_type` varchar(20) NOT NULL COMMENT '用户类型:user用户,agent代理,admin管理员',
  `status` varchar(20) DEFAULT 'offline' COMMENT '状态:online在线,offline离线,busy忙碌',
  `last_seen` datetime DEFAULT NULL COMMENT '最后在线时间',
  `socket_id` varchar(100) DEFAULT NULL COMMENT 'WebSocket连接ID',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(255) DEFAULT NULL COMMENT '用户代理',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_unique` (`user_id`,`user_type`),
  KEY `status` (`status`),
  KEY `last_seen` (`last_seen`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服在线状态表';

-- ----------------------------
-- 客服统计表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_customer_statistics`;
CREATE TABLE `gvd_customer_statistics` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL COMMENT '统计日期',
  `agent_id` int(11) DEFAULT NULL COMMENT '代理ID',
  `admin_id` int(11) DEFAULT NULL COMMENT '管理员ID',
  `total_sessions` int(11) DEFAULT '0' COMMENT '总会话数',
  `active_sessions` int(11) DEFAULT '0' COMMENT '活跃会话数',
  `closed_sessions` int(11) DEFAULT '0' COMMENT '关闭会话数',
  `total_messages` int(11) DEFAULT '0' COMMENT '总消息数',
  `avg_response_time` int(11) DEFAULT '0' COMMENT '平均响应时间(秒)',
  `satisfaction_score` decimal(3,2) DEFAULT '0.00' COMMENT '满意度评分',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `date_agent` (`date`,`agent_id`),
  UNIQUE KEY `date_admin` (`date`,`admin_id`),
  KEY `date` (`date`),
  KEY `agent_id` (`agent_id`),
  KEY `admin_id` (`admin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服统计表';

-- 插入默认快捷回复
INSERT INTO `gvd_customer_quick_replies` (`title`, `content`, `category`, `sort_order`, `created_at`, `updated_at`) VALUES
('欢迎语', '您好！欢迎使用GVD客服，我是您的专属客服，有什么可以帮助您的吗？', 'general', 1, NOW(), NOW()),
('账户问题', '关于账户问题，请提供您的用户名或邮箱，我来为您查询处理。', 'account', 2, NOW(), NOW()),
('交易咨询', '关于交易问题，请详细描述您遇到的情况，我会尽快为您解答。', 'trading', 3, NOW(), NOW()),
('技术支持', '技术问题我们会尽快处理，请稍等片刻，技术人员会联系您。', 'technical', 4, NOW(), NOW()),
('感谢语', '感谢您的咨询，如果还有其他问题，随时联系我们！', 'general', 5, NOW(), NOW());

-- 插入初始价格数据
INSERT INTO `gvd_market_prices` (`symbol`, `price`, `change_24h`, `volume_24h`, `created_at`) VALUES
('BTCUSDT', 50000.00, 2.5, 1000000, NOW()),
('ETHUSDT', 3000.00, 1.8, 500000, NOW()),
('BNBUSDT', 300.00, -0.5, 200000, NOW());

-- ----------------------------
-- 系统配置表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_system_config`;
CREATE TABLE `gvd_system_config` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `type` varchar(50) NOT NULL COMMENT '配置类型',
  `key` varchar(100) NOT NULL COMMENT '配置键',
  `value` text COMMENT '配置值',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `type_key` (`type`, `key`),
  KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- ----------------------------
-- 合约配置表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_contract_config`;
CREATE TABLE `gvd_contract_config` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `min_amount` decimal(10,2) NOT NULL DEFAULT '10.00' COMMENT '最小交易金额',
  `max_amount` decimal(10,2) NOT NULL DEFAULT '10000.00' COMMENT '最大交易金额',
  `win_rate` decimal(3,2) NOT NULL DEFAULT '0.85' COMMENT '胜率',
  `available_times` text COMMENT '可选时长(JSON)',
  `available_coins` text COMMENT '可交易币种(JSON)',
  `quick_amounts` text COMMENT '快捷金额(JSON)',
  `profit_rates` text COMMENT '赔率配置(JSON)',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合约配置表';

-- ----------------------------
-- 用户钱包地址表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_user_wallet_addresses`;
CREATE TABLE `gvd_user_wallet_addresses` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '地址ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `coin_symbol` varchar(20) NOT NULL COMMENT '币种符号',
  `address` varchar(255) NOT NULL COMMENT '钱包地址',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_coin` (`user_id`, `coin_symbol`),
  KEY `user_id` (`user_id`),
  KEY `coin_symbol` (`coin_symbol`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户钱包地址表';

-- ----------------------------
-- 管理员日志表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_admin_logs`;
CREATE TABLE `gvd_admin_logs` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `admin_id` int(11) NOT NULL COMMENT '管理员ID',
  `action` varchar(100) NOT NULL COMMENT '操作类型',
  `content` text COMMENT '操作内容',
  `ip` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `admin_id` (`admin_id`),
  KEY `action` (`action`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员操作日志表';

-- ----------------------------
-- 代理日志表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_agent_logs`;
CREATE TABLE `gvd_agent_logs` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `agent_id` int(11) NOT NULL COMMENT '代理ID',
  `action` varchar(100) NOT NULL COMMENT '操作类型',
  `content` text COMMENT '操作内容',
  `ip` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `agent_id` (`agent_id`),
  KEY `action` (`action`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代理操作日志表';

-- ----------------------------
-- 插入默认系统配置
-- ----------------------------
INSERT INTO `gvd_system_config` (`type`, `key`, `value`, `description`, `created_at`, `updated_at`) VALUES
('system', 'universal_verification_code', '888888', '万能验证码', NOW(), NOW()),
('deposit_address', 'USDT', '', 'USDT收款地址', NOW(), NOW()),
('deposit_address', 'BTC', '', 'BTC收款地址', NOW(), NOW()),
('deposit_address', 'ETH', '', 'ETH收款地址', NOW(), NOW()),
('image', 'logo', '/static/images/logo.png', '网站Logo', NOW(), NOW()),
('image', 'favicon', '/static/images/favicon.ico', '网站图标', NOW(), NOW());

-- ----------------------------
-- 插入默认合约配置
-- ----------------------------
INSERT INTO `gvd_contract_config` (`min_amount`, `max_amount`, `win_rate`, `available_times`, `available_coins`, `quick_amounts`, `profit_rates`, `created_at`, `updated_at`) VALUES
(10.00, 10000.00, 0.85, '[60,180,300,600]', '["BTC","ETH","LTC","EOS","XRP"]', '[50,100,500,1000]', '{"60":0.80,"180":0.85,"300":0.90,"600":0.95}', NOW(), NOW());

-- ----------------------------
-- K线数据表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_klines`;
CREATE TABLE `gvd_klines` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'K线ID',
  `symbol` varchar(20) NOT NULL COMMENT '交易对符号',
  `interval` varchar(10) NOT NULL COMMENT '时间周期',
  `open_time` int(11) NOT NULL COMMENT '开盘时间戳',
  `close_time` int(11) NOT NULL COMMENT '收盘时间戳',
  `open_price` decimal(20,8) NOT NULL COMMENT '开盘价',
  `high_price` decimal(20,8) NOT NULL COMMENT '最高价',
  `low_price` decimal(20,8) NOT NULL COMMENT '最低价',
  `close_price` decimal(20,8) NOT NULL COMMENT '收盘价',
  `volume` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '成交量',
  `amount` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '成交额',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `symbol_interval_time` (`symbol`, `interval`, `open_time`),
  KEY `symbol` (`symbol`),
  KEY `interval` (`interval`),
  KEY `open_time` (`open_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='K线数据表';

-- ----------------------------
-- 交易对配置表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_trading_pairs`;
CREATE TABLE `gvd_trading_pairs` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '交易对ID',
  `symbol` varchar(20) NOT NULL COMMENT '交易对符号',
  `base_coin` varchar(10) NOT NULL COMMENT '基础币种',
  `quote_coin` varchar(10) NOT NULL COMMENT '计价币种',
  `price_precision` int(2) NOT NULL DEFAULT '8' COMMENT '价格精度',
  `amount_precision` int(2) NOT NULL DEFAULT '8' COMMENT '数量精度',
  `min_amount` decimal(20,8) NOT NULL DEFAULT '0.00000001' COMMENT '最小交易数量',
  `max_amount` decimal(20,8) NOT NULL DEFAULT '999999999.00000000' COMMENT '最大交易数量',
  `min_total` decimal(20,8) NOT NULL DEFAULT '0.00000001' COMMENT '最小交易金额',
  `maker_fee` decimal(6,4) NOT NULL DEFAULT '0.0010' COMMENT 'Maker手续费率',
  `taker_fee` decimal(6,4) NOT NULL DEFAULT '0.0010' COMMENT 'Taker手续费率',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `symbol` (`symbol`),
  KEY `is_active` (`is_active`),
  KEY `sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='交易对配置表';

-- ----------------------------
-- 现货订单表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_spot_orders`;
CREATE TABLE `gvd_spot_orders` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `symbol` varchar(20) NOT NULL COMMENT '交易对',
  `order_id` varchar(32) NOT NULL COMMENT '订单号',
  `type` tinyint(1) NOT NULL COMMENT '订单类型:1买入,2卖出',
  `order_type` tinyint(1) NOT NULL COMMENT '订单方式:1限价,2市价',
  `price` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '委托价格',
  `amount` decimal(20,8) NOT NULL COMMENT '委托数量',
  `total` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '委托金额',
  `filled_amount` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '已成交数量',
  `filled_total` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '已成交金额',
  `avg_price` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '平均成交价',
  `fee` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '手续费',
  `fee_coin` varchar(10) NOT NULL DEFAULT '' COMMENT '手续费币种',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1待成交,2部分成交,3完全成交,4已取消',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  `filled_at` datetime DEFAULT NULL COMMENT '成交时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_id` (`order_id`),
  KEY `user_id` (`user_id`),
  KEY `symbol` (`symbol`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='现货订单表';

-- ----------------------------
-- 成交记录表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_trades`;
CREATE TABLE `gvd_trades` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '成交ID',
  `symbol` varchar(20) NOT NULL COMMENT '交易对',
  `trade_id` varchar(32) NOT NULL COMMENT '成交号',
  `buy_order_id` varchar(32) NOT NULL COMMENT '买单ID',
  `sell_order_id` varchar(32) NOT NULL COMMENT '卖单ID',
  `buy_user_id` int(11) NOT NULL COMMENT '买方用户ID',
  `sell_user_id` int(11) NOT NULL COMMENT '卖方用户ID',
  `price` decimal(20,8) NOT NULL COMMENT '成交价格',
  `amount` decimal(20,8) NOT NULL COMMENT '成交数量',
  `total` decimal(20,8) NOT NULL COMMENT '成交金额',
  `buy_fee` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '买方手续费',
  `sell_fee` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '卖方手续费',
  `created_at` datetime NOT NULL COMMENT '成交时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `trade_id` (`trade_id`),
  KEY `symbol` (`symbol`),
  KEY `buy_order_id` (`buy_order_id`),
  KEY `sell_order_id` (`sell_order_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成交记录表';

-- ----------------------------
-- 插入默认交易对
-- ----------------------------
INSERT INTO `gvd_trading_pairs` (`symbol`, `base_coin`, `quote_coin`, `price_precision`, `amount_precision`, `min_amount`, `max_amount`, `min_total`, `maker_fee`, `taker_fee`, `is_active`, `sort_order`, `created_at`, `updated_at`) VALUES
('BTCUSDT', 'BTC', 'USDT', 2, 6, 0.000001, 1000.000000, 10.00, 0.0010, 0.0010, 1, 1, NOW(), NOW()),
('ETHUSDT', 'ETH', 'USDT', 2, 6, 0.000001, 10000.000000, 10.00, 0.0010, 0.0010, 1, 2, NOW(), NOW()),
('LTCUSDT', 'LTC', 'USDT', 2, 6, 0.000001, 10000.000000, 10.00, 0.0010, 0.0010, 1, 3, NOW(), NOW()),
('EOSUSDT', 'EOS', 'USDT', 4, 6, 0.000001, 100000.000000, 10.00, 0.0010, 0.0010, 1, 4, NOW(), NOW()),
('XRPUSDT', 'XRP', 'USDT', 4, 6, 0.000001, 100000.000000, 10.00, 0.0010, 0.0010, 1, 5, NOW(), NOW());

-- ----------------------------
-- 杠杆订单表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_leverage_orders`;
CREATE TABLE `gvd_leverage_orders` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `symbol` varchar(20) NOT NULL COMMENT '交易对',
  `order_id` varchar(32) NOT NULL COMMENT '订单号',
  `type` tinyint(1) NOT NULL COMMENT '订单类型:1做多,2做空',
  `order_type` tinyint(1) NOT NULL COMMENT '订单方式:1限价,2市价',
  `price` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '委托价格',
  `amount` decimal(20,8) NOT NULL COMMENT '委托数量',
  `total` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '委托金额',
  `margin` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '保证金',
  `leverage` decimal(8,2) NOT NULL DEFAULT '1.00' COMMENT '杠杆倍数',
  `filled_amount` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '已成交数量',
  `filled_total` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '已成交金额',
  `avg_price` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '平均成交价',
  `fee` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '手续费',
  `pnl` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '已实现盈亏',
  `liquidation_price` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '强平价格',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1待成交,2部分成交,3完全成交,4已取消,5已强平',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  `filled_at` datetime DEFAULT NULL COMMENT '成交时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_id` (`order_id`),
  KEY `user_id` (`user_id`),
  KEY `symbol` (`symbol`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='杠杆订单表';

-- ----------------------------
-- 期货合约表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_futures_contracts`;
CREATE TABLE `gvd_futures_contracts` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '合约ID',
  `symbol` varchar(20) NOT NULL COMMENT '合约符号',
  `base_coin` varchar(10) NOT NULL COMMENT '基础币种',
  `quote_coin` varchar(10) NOT NULL COMMENT '计价币种',
  `contract_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '合约类型:1永续,2定期',
  `settlement_coin` varchar(10) NOT NULL DEFAULT 'USDT' COMMENT '结算币种',
  `contract_size` decimal(20,8) NOT NULL DEFAULT '1.00000000' COMMENT '合约面值',
  `tick_size` decimal(20,8) NOT NULL DEFAULT '0.01000000' COMMENT '最小价格变动',
  `max_leverage` int(3) NOT NULL DEFAULT '100' COMMENT '最大杠杆',
  `maintenance_margin_rate` decimal(6,4) NOT NULL DEFAULT '0.0050' COMMENT '维持保证金率',
  `maker_fee` decimal(6,4) NOT NULL DEFAULT '0.0002' COMMENT 'Maker手续费率',
  `taker_fee` decimal(6,4) NOT NULL DEFAULT '0.0004' COMMENT 'Taker手续费率',
  `funding_interval` int(11) NOT NULL DEFAULT '28800' COMMENT '资金费率结算间隔(秒)',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `symbol` (`symbol`),
  KEY `is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='期货合约表';

-- ----------------------------
-- 期货持仓表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_futures_positions`;
CREATE TABLE `gvd_futures_positions` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '持仓ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `symbol` varchar(20) NOT NULL COMMENT '合约符号',
  `side` tinyint(1) NOT NULL COMMENT '方向:1多头,2空头',
  `size` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '持仓数量',
  `entry_price` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '开仓均价',
  `mark_price` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '标记价格',
  `liquidation_price` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '强平价格',
  `margin` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '保证金',
  `unrealized_pnl` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '未实现盈亏',
  `realized_pnl` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '已实现盈亏',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_symbol` (`user_id`, `symbol`),
  KEY `user_id` (`user_id`),
  KEY `symbol` (`symbol`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='期货持仓表';

-- ----------------------------
-- 插入默认期货合约
-- ----------------------------
INSERT INTO `gvd_futures_contracts` (`symbol`, `base_coin`, `quote_coin`, `contract_type`, `settlement_coin`, `contract_size`, `tick_size`, `max_leverage`, `maintenance_margin_rate`, `maker_fee`, `taker_fee`, `funding_interval`, `is_active`, `created_at`, `updated_at`) VALUES
('BTCUSDT-PERP', 'BTC', 'USDT', 1, 'USDT', 1.00000000, 0.10000000, 100, 0.0050, 0.0002, 0.0004, 28800, 1, NOW(), NOW()),
('ETHUSDT-PERP', 'ETH', 'USDT', 1, 'USDT', 1.00000000, 0.01000000, 75, 0.0050, 0.0002, 0.0004, 28800, 1, NOW(), NOW()),
('LTCUSDT-PERP', 'LTC', 'USDT', 1, 'USDT', 1.00000000, 0.01000000, 50, 0.0100, 0.0002, 0.0004, 28800, 1, NOW(), NOW());

-- ----------------------------
-- 用户表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_users`;
CREATE TABLE `gvd_users` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT '' COMMENT '手机号',
  `password` varchar(255) NOT NULL COMMENT '登录密码',
  `pay_password` varchar(255) DEFAULT '' COMMENT '支付密码',
  `avatar` varchar(255) DEFAULT '' COMMENT '头像',
  `real_name` varchar(50) DEFAULT '' COMMENT '真实姓名',
  `invite_code` varchar(20) DEFAULT '' COMMENT '邀请码',
  `my_invite_code` varchar(20) DEFAULT '' COMMENT '我的邀请码',
  `inviter_id` int(11) DEFAULT '0' COMMENT '邀请人ID',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0未激活,1正常,2冻结,3封禁',
  `kyc_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'KYC状态:0未认证,1审核中,2已认证,3被拒绝',
  `email_verified` tinyint(1) NOT NULL DEFAULT '0' COMMENT '邮箱是否验证',
  `phone_verified` tinyint(1) NOT NULL DEFAULT '0' COMMENT '手机是否验证',
  `last_login_time` int(11) DEFAULT '0' COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT '' COMMENT '最后登录IP',
  `created_at` datetime NOT NULL COMMENT '注册时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `my_invite_code` (`my_invite_code`),
  KEY `invite_code` (`invite_code`),
  KEY `inviter_id` (`inviter_id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- ----------------------------
-- 用户资产表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_user_assets`;
CREATE TABLE `gvd_user_assets` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '资产ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `coin_symbol` varchar(20) NOT NULL COMMENT '币种符号',
  `coin_name` varchar(50) NOT NULL COMMENT '币种名称',
  `available` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '可用余额',
  `frozen` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '冻结余额',
  `total` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '总余额',
  `decimals` int(2) NOT NULL DEFAULT '8' COMMENT '精度',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_coin` (`user_id`, `coin_symbol`),
  KEY `user_id` (`user_id`),
  KEY `coin_symbol` (`coin_symbol`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户资产表';

-- ----------------------------
-- 资产变动记录表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_asset_records`;
CREATE TABLE `gvd_asset_records` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `coin_symbol` varchar(20) NOT NULL COMMENT '币种符号',
  `amount` decimal(20,8) NOT NULL COMMENT '变动金额',
  `balance` decimal(20,8) NOT NULL COMMENT '变动后余额',
  `type` varchar(20) NOT NULL COMMENT '变动类型',
  `description` varchar(255) NOT NULL COMMENT '变动描述',
  `tx_hash` varchar(100) DEFAULT '' COMMENT '交易哈希',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `coin_symbol` (`coin_symbol`),
  KEY `type` (`type`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资产变动记录表';

-- ----------------------------
-- 插入默认用户
-- ----------------------------
INSERT INTO `gvd_users` (`username`, `email`, `password`, `status`, `my_invite_code`, `created_at`, `updated_at`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, 'ADMIN001', NOW(), NOW()),
('test', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, 'TEST0001', NOW(), NOW());

-- ----------------------------
-- 管理员表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_admins`;
CREATE TABLE `gvd_admins` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) DEFAULT '' COMMENT '邮箱',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `role` tinyint(1) NOT NULL DEFAULT '3' COMMENT '角色:1超级管理员,2普通管理员,3操作员',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0禁用,1正常',
  `last_login_time` int(11) DEFAULT '0' COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT '' COMMENT '最后登录IP',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `status` (`status`),
  KEY `role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- ----------------------------
-- 插入默认管理员
-- ----------------------------
INSERT INTO `gvd_admins` (`username`, `email`, `password`, `role`, `status`, `created_at`, `updated_at`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, 1, NOW(), NOW()),
('operator', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 3, 1, NOW(), NOW());

-- ----------------------------
-- IDO项目表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_ido_projects`;
CREATE TABLE `gvd_ido_projects` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `name` varchar(100) NOT NULL COMMENT '项目名称',
  `symbol` varchar(20) NOT NULL COMMENT '代币符号',
  `description` text COMMENT '项目描述',
  `icon` varchar(255) DEFAULT '' COMMENT '项目图标',
  `website` varchar(255) DEFAULT '' COMMENT '官网地址',
  `whitepaper` varchar(255) DEFAULT '' COMMENT '白皮书地址',
  `target_amount` decimal(20,2) NOT NULL COMMENT '目标募集金额',
  `raised_amount` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '已募集金额',
  `token_price` decimal(20,8) NOT NULL COMMENT '代币价格',
  `min_purchase` decimal(20,2) NOT NULL DEFAULT '100.00' COMMENT '最小认购金额',
  `max_purchase` decimal(20,2) NOT NULL DEFAULT '10000.00' COMMENT '最大认购金额',
  `start_time` int(11) NOT NULL COMMENT '开始时间',
  `end_time` int(11) NOT NULL COMMENT '结束时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1即将开始,2进行中,3已完成,4已取消',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `symbol` (`symbol`),
  KEY `status` (`status`),
  KEY `start_time` (`start_time`),
  KEY `end_time` (`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IDO项目表';

-- ----------------------------
-- IDO订单表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_ido_orders`;
CREATE TABLE `gvd_ido_orders` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_id` varchar(32) NOT NULL COMMENT '订单号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `project_id` int(11) NOT NULL COMMENT '项目ID',
  `amount` decimal(20,2) NOT NULL COMMENT '认购金额',
  `token_amount` decimal(20,8) NOT NULL COMMENT '获得代币数量',
  `token_price` decimal(20,8) NOT NULL COMMENT '代币价格',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1待处理,2成功,3失败,4已取消',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_id` (`order_id`),
  KEY `user_id` (`user_id`),
  KEY `project_id` (`project_id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IDO订单表';

-- ----------------------------
-- 插入示例IDO项目
-- ----------------------------
INSERT INTO `gvd_ido_projects` (`name`, `symbol`, `description`, `target_amount`, `token_price`, `min_purchase`, `max_purchase`, `start_time`, `end_time`, `status`, `created_at`, `updated_at`) VALUES
('GVD Token', 'GVD', 'GVD交易平台原生代币，用于平台治理和手续费抵扣', 1000000.00, 0.10000000, 100.00, 10000.00, UNIX_TIMESTAMP(DATE_ADD(NOW(), INTERVAL 1 DAY)), UNIX_TIMESTAMP(DATE_ADD(NOW(), INTERVAL 8 DAY)), 1, NOW(), NOW()),
('DeFi Protocol', 'DFP', '去中心化金融协议代币，提供流动性挖矿和治理功能', 500000.00, 0.05000000, 50.00, 5000.00, UNIX_TIMESTAMP(DATE_ADD(NOW(), INTERVAL 3 DAY)), UNIX_TIMESTAMP(DATE_ADD(NOW(), INTERVAL 10 DAY)), 1, NOW(), NOW());

-- ----------------------------
-- 客服聊天表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_customer_service`;
CREATE TABLE `gvd_customer_service` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `content` text NOT NULL COMMENT '消息内容',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '消息类型:1文本,2图片,3文件,4系统',
  `is_admin` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否管理员发送:0用户,1管理员',
  `is_read` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读:0未读,1已读',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `is_admin` (`is_admin`),
  KEY `is_read` (`is_read`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服聊天表';

-- ----------------------------
-- 风控日志表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_risk_control_log`;
CREATE TABLE `gvd_risk_control_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `type` varchar(20) NOT NULL COMMENT '风控类型:login,trade,withdraw',
  `ip` varchar(45) NOT NULL COMMENT 'IP地址',
  `risk_score` int(11) NOT NULL DEFAULT '0' COMMENT '风险分数',
  `risk_level` tinyint(1) NOT NULL DEFAULT '1' COMMENT '风险等级:1低,2中,3高,4极高',
  `action` varchar(20) NOT NULL COMMENT '风控动作:allow,warning,restrict,block',
  `risk_factors` text COMMENT '风险因素JSON',
  `user_agent` text COMMENT '用户代理',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `type` (`type`),
  KEY `ip` (`ip`),
  KEY `risk_level` (`risk_level`),
  KEY `action` (`action`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='风控日志表';

-- ----------------------------
-- IP白名单表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_ip_whitelist`;
CREATE TABLE `gvd_ip_whitelist` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `ip` varchar(45) NOT NULL COMMENT 'IP地址',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联用户ID',
  `remark` varchar(255) DEFAULT '' COMMENT '备注',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ip` (`ip`),
  KEY `user_id` (`user_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IP白名单表';

-- ----------------------------
-- IP黑名单表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_ip_blacklist`;
CREATE TABLE `gvd_ip_blacklist` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `ip` varchar(45) NOT NULL COMMENT 'IP地址',
  `reason` varchar(255) DEFAULT '' COMMENT '加入黑名单原因',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ip` (`ip`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IP黑名单表';

-- ----------------------------
-- 杠杆订单表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_leverage_orders`;
CREATE TABLE `gvd_leverage_orders` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_id` varchar(32) NOT NULL COMMENT '订单号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `symbol` varchar(20) NOT NULL COMMENT '交易对',
  `direction` varchar(10) NOT NULL COMMENT '方向:long,short',
  `leverage` int(11) NOT NULL COMMENT '杠杆倍数',
  `margin` decimal(20,8) NOT NULL COMMENT '保证金',
  `amount` decimal(20,8) NOT NULL COMMENT '交易金额',
  `open_price` decimal(20,8) NOT NULL COMMENT '开仓价格',
  `current_price` decimal(20,8) DEFAULT '0.00000000' COMMENT '当前价格',
  `close_price` decimal(20,8) DEFAULT '0.00000000' COMMENT '平仓价格',
  `liquidation_price` decimal(20,8) NOT NULL COMMENT '强平价格',
  `stop_loss` decimal(20,8) DEFAULT '0.00000000' COMMENT '止损价格',
  `take_profit` decimal(20,8) DEFAULT '0.00000000' COMMENT '止盈价格',
  `profit_loss` decimal(20,8) DEFAULT '0.00000000' COMMENT '盈亏',
  `status` varchar(20) NOT NULL DEFAULT 'open' COMMENT '状态:open,closed,liquidated',
  `close_type` varchar(20) DEFAULT '' COMMENT '平仓类型:manual,stop_loss,take_profit,liquidation',
  `closed_at` datetime DEFAULT NULL COMMENT '平仓时间',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_id` (`order_id`),
  KEY `user_id` (`user_id`),
  KEY `symbol` (`symbol`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='杠杆订单表';

-- ----------------------------
-- 期货合约表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_futures_contracts`;
CREATE TABLE `gvd_futures_contracts` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '合约ID',
  `symbol` varchar(20) NOT NULL COMMENT '合约代码',
  `name` varchar(50) NOT NULL COMMENT '合约名称',
  `type` varchar(20) NOT NULL COMMENT '合约类型:perpetual,quarterly,weekly',
  `base_currency` varchar(10) NOT NULL COMMENT '基础货币',
  `quote_currency` varchar(10) NOT NULL COMMENT '计价货币',
  `contract_size` decimal(20,8) NOT NULL DEFAULT '1.00000000' COMMENT '合约面值',
  `tick_size` decimal(20,8) NOT NULL DEFAULT '0.01000000' COMMENT '最小价格变动',
  `min_order_size` decimal(20,8) NOT NULL DEFAULT '1.00000000' COMMENT '最小下单量',
  `max_leverage` int(11) NOT NULL DEFAULT '100' COMMENT '最大杠杆',
  `maker_fee` decimal(10,6) NOT NULL DEFAULT '0.000200' COMMENT 'Maker手续费',
  `taker_fee` decimal(10,6) NOT NULL DEFAULT '0.000500' COMMENT 'Taker手续费',
  `funding_interval` int(11) NOT NULL DEFAULT '28800' COMMENT '资金费率间隔(秒)',
  `settlement_time` time DEFAULT NULL COMMENT '结算时间',
  `expiry_time` datetime DEFAULT NULL COMMENT '到期时间',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `symbol` (`symbol`),
  KEY `type` (`type`),
  KEY `status` (`status`),
  KEY `sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='期货合约表';

-- ----------------------------
-- 期货订单表
-- ----------------------------
DROP TABLE IF EXISTS `gvd_futures_orders`;
CREATE TABLE `gvd_futures_orders` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_id` varchar(32) NOT NULL COMMENT '订单号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `symbol` varchar(20) NOT NULL COMMENT '合约代码',
  `contract_type` varchar(20) NOT NULL COMMENT '合约类型',
  `side` varchar(10) NOT NULL COMMENT '方向:long,short',
  `order_type` varchar(10) NOT NULL COMMENT '订单类型:market,limit',
  `quantity` decimal(20,8) NOT NULL COMMENT '数量',
  `price` decimal(20,8) NOT NULL COMMENT '价格',
  `fill_price` decimal(20,8) DEFAULT '0.00000000' COMMENT '成交价格',
  `filled_quantity` decimal(20,8) DEFAULT '0.00000000' COMMENT '成交数量',
  `margin` decimal(20,8) NOT NULL COMMENT '保证金',
  `leverage` int(11) NOT NULL COMMENT '杠杆倍数',
  `liquidation_price` decimal(20,8) NOT NULL COMMENT '强平价格',
  `stop_loss` decimal(20,8) DEFAULT '0.00000000' COMMENT '止损价格',
  `take_profit` decimal(20,8) DEFAULT '0.00000000' COMMENT '止盈价格',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态:pending,filled,cancelled,expired',
  `filled_at` datetime DEFAULT NULL COMMENT '成交时间',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_id` (`order_id`),
  KEY `user_id` (`user_id`),
  KEY `symbol` (`symbol`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='期货订单表';

SET FOREIGN_KEY_CHECKS = 1;
