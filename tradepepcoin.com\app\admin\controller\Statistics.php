<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\BaseController;
use app\common\service\StatisticsService;
use think\facade\View;

/**
 * 管理后台统计分析控制器
 */
class Statistics extends BaseController
{
    protected $statisticsService;

    public function initialize()
    {
        parent::initialize();
        $this->statisticsService = new StatisticsService();
    }

    /**
     * 统计概览
     */
    public function index()
    {
        $stats = $this->statisticsService->getPlatformStats();

        if ($this->request->isAjax()) {
            return $this->success($stats);
        }

        View::assign([
            'stats' => $stats,
            'title' => '统计概览'
        ]);

        return View::fetch();
    }

    /**
     * 用户统计
     */
    public function users()
    {
        $period = input('period', 'month');
        $stats = $this->statisticsService->getUserStats($period);

        if ($this->request->isAjax()) {
            return $this->success($stats);
        }

        View::assign([
            'stats' => $stats,
            'period' => $period,
            'title' => '用户统计'
        ]);

        return View::fetch();
    }

    /**
     * 交易统计
     */
    public function trades()
    {
        $period = input('period', 'month');
        $stats = $this->statisticsService->getTradeStats($period);

        if ($this->request->isAjax()) {
            return $this->success($stats);
        }

        View::assign([
            'stats' => $stats,
            'period' => $period,
            'title' => '交易统计'
        ]);

        return View::fetch();
    }

    /**
     * 资产统计
     */
    public function assets()
    {
        $stats = $this->statisticsService->getAssetStats();

        if ($this->request->isAjax()) {
            return $this->success($stats);
        }

        View::assign([
            'stats' => $stats,
            'title' => '资产统计'
        ]);

        return View::fetch();
    }

    /**
     * 佣金统计
     */
    public function commissions()
    {
        $period = input('period', 'month');
        $stats = $this->statisticsService->getCommissionStats($period);

        if ($this->request->isAjax()) {
            return $this->success($stats);
        }

        View::assign([
            'stats' => $stats,
            'period' => $period,
            'title' => '佣金统计'
        ]);

        return View::fetch();
    }

    /**
     * 财务报表
     */
    public function financial()
    {
        $startDate = input('start_date', date('Y-m-01'));
        $endDate = input('end_date', date('Y-m-d'));

        $report = $this->statisticsService->getFinancialReport($startDate, $endDate);

        if ($this->request->isAjax()) {
            return $this->success($report);
        }

        View::assign([
            'report' => $report,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'title' => '财务报表'
        ]);

        return View::fetch();
    }

    /**
     * 用户行为分析
     */
    public function userBehavior()
    {
        $userId = input('user_id/d', 0);
        
        if (!$userId) {
            return $this->error('请指定用户ID');
        }

        $analysis = $this->statisticsService->getUserBehaviorAnalysis($userId);

        if ($this->request->isAjax()) {
            return $this->success($analysis);
        }

        View::assign([
            'analysis' => $analysis,
            'user_id' => $userId,
            'title' => '用户行为分析'
        ]);

        return View::fetch();
    }

    /**
     * 实时数据
     */
    public function realtime()
    {
        $data = [
            'online_users' => $this->getOnlineUsers(),
            'recent_trades' => $this->getRecentTrades(),
            'recent_orders' => $this->getRecentOrders(),
            'recent_registrations' => $this->getRecentRegistrations(),
            'system_status' => $this->getSystemStatus()
        ];

        return $this->success($data);
    }

    /**
     * 导出报表
     */
    public function export()
    {
        $type = input('type', 'platform');
        $format = input('format', 'excel');
        $startDate = input('start_date', date('Y-m-01'));
        $endDate = input('end_date', date('Y-m-d'));

        try {
            switch ($type) {
                case 'platform':
                    $data = $this->statisticsService->getPlatformStats();
                    break;
                case 'users':
                    $data = $this->statisticsService->getUserStats();
                    break;
                case 'trades':
                    $data = $this->statisticsService->getTradeStats();
                    break;
                case 'financial':
                    $data = $this->statisticsService->getFinancialReport($startDate, $endDate);
                    break;
                default:
                    return $this->error('不支持的报表类型');
            }

            if ($format === 'excel') {
                return $this->exportToExcel($data, $type);
            } elseif ($format === 'csv') {
                return $this->exportToCsv($data, $type);
            } else {
                return $this->error('不支持的导出格式');
            }
        } catch (\Exception $e) {
            return $this->error('导出失败：' . $e->getMessage());
        }
    }

    /**
     * 获取在线用户数
     */
    private function getOnlineUsers(): int
    {
        // 这里应该从Redis或其他缓存中获取在线用户数
        // 简化处理，返回模拟数据
        return rand(100, 500);
    }

    /**
     * 获取最近交易
     */
    private function getRecentTrades(): array
    {
        return \app\common\model\Trade::order('created_at', 'desc')
                                     ->limit(10)
                                     ->with(['user'])
                                     ->select()
                                     ->toArray();
    }

    /**
     * 获取最近订单
     */
    private function getRecentOrders(): array
    {
        return \app\common\model\Order::order('created_at', 'desc')
                                     ->limit(10)
                                     ->with(['user'])
                                     ->select()
                                     ->toArray();
    }

    /**
     * 获取最近注册
     */
    private function getRecentRegistrations(): array
    {
        return \app\common\model\User::order('created_at', 'desc')
                                    ->limit(10)
                                    ->field('id,username,email,created_at')
                                    ->select()
                                    ->toArray();
    }

    /**
     * 获取系统状态
     */
    private function getSystemStatus(): array
    {
        return [
            'cpu_usage' => $this->getCpuUsage(),
            'memory_usage' => $this->getMemoryUsage(),
            'disk_usage' => $this->getDiskUsage(),
            'database_status' => $this->getDatabaseStatus(),
            'cache_status' => $this->getCacheStatus()
        ];
    }

    /**
     * 获取CPU使用率
     */
    private function getCpuUsage(): float
    {
        // 简化处理，返回模拟数据
        return round(rand(10, 80) + rand(0, 99) / 100, 2);
    }

    /**
     * 获取内存使用率
     */
    private function getMemoryUsage(): float
    {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = ini_get('memory_limit');
        
        if ($memoryLimit == -1) {
            return 0;
        }
        
        $memoryLimit = $this->convertToBytes($memoryLimit);
        
        return round(($memoryUsage / $memoryLimit) * 100, 2);
    }

    /**
     * 获取磁盘使用率
     */
    private function getDiskUsage(): float
    {
        $totalSpace = disk_total_space('.');
        $freeSpace = disk_free_space('.');
        
        if ($totalSpace === false || $freeSpace === false) {
            return 0;
        }
        
        $usedSpace = $totalSpace - $freeSpace;
        
        return round(($usedSpace / $totalSpace) * 100, 2);
    }

    /**
     * 获取数据库状态
     */
    private function getDatabaseStatus(): string
    {
        try {
            \think\facade\Db::query('SELECT 1');
            return 'online';
        } catch (\Exception $e) {
            return 'offline';
        }
    }

    /**
     * 获取缓存状态
     */
    private function getCacheStatus(): string
    {
        try {
            \think\facade\Cache::set('test_key', 'test_value', 1);
            \think\facade\Cache::delete('test_key');
            return 'online';
        } catch (\Exception $e) {
            return 'offline';
        }
    }

    /**
     * 转换为字节
     */
    private function convertToBytes(string $value): int
    {
        $value = trim($value);
        $last = strtolower($value[strlen($value) - 1]);
        $value = (int)$value;
        
        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }
        
        return $value;
    }

    /**
     * 导出到Excel
     */
    private function exportToExcel(array $data, string $type)
    {
        // 这里应该使用PhpSpreadsheet等库生成Excel文件
        // 简化处理，返回JSON格式
        $filename = $type . '_report_' . date('Y-m-d_H-i-s') . '.json';
        
        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        echo json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * 导出到CSV
     */
    private function exportToCsv(array $data, string $type)
    {
        // 这里应该生成CSV格式文件
        // 简化处理，返回JSON格式
        $filename = $type . '_report_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        // 简化的CSV输出
        echo "Type,Data\n";
        echo "{$type}," . json_encode($data) . "\n";
        exit;
    }
}
