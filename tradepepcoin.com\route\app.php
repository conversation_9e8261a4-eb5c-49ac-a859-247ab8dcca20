<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

// 首页路由
Route::get('/', 'index/Index/index');

// 用户认证路由
Route::group('auth', function () {
    Route::get('login', 'index/Auth/login');
    Route::post('login', 'index/Auth/login');
    Route::get('register', 'index/Auth/register');
    Route::post('register', 'index/Auth/register');
    Route::get('logout', 'index/Auth/logout');
    Route::get('forgot-password', 'index/Auth/forgotPassword');
    Route::post('forgot-password', 'index/Auth/forgotPassword');
    Route::post('sendEmailCode', 'index/Auth/sendEmailCode');
    Route::post('sendSmsCode', 'index/Auth/sendSmsCode');
    Route::get('captcha', 'index/Auth/captcha');
});

// 用户中心路由
Route::group('user', function () {
    Route::get('profile', 'index/User/profile');
    Route::get('assets', 'index/User/assets');
    Route::get('orders', 'index/User/orders');
    Route::get('deposits', 'index/User/deposits');
    Route::get('withdrawals', 'index/User/withdrawals');
    Route::get('commissions', 'index/User/commissions');
    Route::get('invite', 'index/User/invite');
    Route::get("customer-service", "index/CustomerService/index");
    Route::post("customer-service/send", "index/CustomerService/send");
    Route::get("customer-service/messages", "index/CustomerService/messages");
});

// 交易路由
Route::group('trade', function () {
    Route::get('/', 'index/Trade/index');
    Route::post('createOrder', 'index/Trade/createOrder');
    Route::post('cancelOrder', 'index/Trade/cancelOrder');
    Route::get('history', 'index/Trade/history');
    Route::get('klineData', 'index/Trade/klineData');
    Route::get('depthData', 'index/Trade/depthData');
    Route::get('recentTrades', 'index/Trade/recentTrades');
});

// 合约交易路由
Route::group('contract', function () {
    Route::get('/', 'index/Contract/index');
    Route::post('create', 'index/Contract/createOrder');
    Route::get('history', 'index/Contract/history');
    Route::get('detail', 'index/Contract/detail');
    Route::get('price', 'index/Contract/getPrice');
    Route::get('timer', 'index/Contract/getContractTimer');
    Route::post('settle', 'index/Contract/settle');
});

// K线图表路由
Route::group('chart', function () {
    Route::get('/', function() {
        return redirect('/trade/chart.html');
    });
    Route::get('tradingview', function() {
        return redirect('/trade/chart.html');
    });
});

// K线数据API路由
Route::group('api/kline', function () {
    Route::get('data', 'api/Kline/data');
    Route::get('ticker24hr', 'api/Kline/ticker24hr');
    Route::get('realtime', 'api/Kline/realtime');
    Route::get('depth', 'api/Kline/depth');
    Route::get('intervals', 'api/Kline/intervals');
    Route::get('config', 'api/Kline/config');
    Route::post('simulate', 'api/Kline/simulate');
    Route::post('init', 'api/Kline/init');
    Route::get('websocket-format', 'api/Kline/websocketFormat');
});

// KYC认证路由
Route::group('kyc', function () {
    Route::get('/', 'index/Kyc/index');
    Route::post('submit', 'index/Kyc/submit');
    Route::post('uploadImage', 'index/Kyc/uploadImage');
    Route::get('status', 'index/Kyc/status');
    Route::get('guide', 'index/Kyc/guide');
});

// 多语言路由
Route::group('language', function () {
    Route::get('switch', 'index/Language/switch');
    Route::get('current', 'index/Language/current');
    Route::get('pack', 'index/Language/pack');
    Route::get('list', 'index/Language/list');
});

// 代理系统路由
Route::group('agent', function () {
    Route::get('/', 'index/Agent/index');
    Route::get('invite', 'index/Agent/invite');
    Route::get('users', 'index/Agent/users');
    Route::get('commissions', 'index/Agent/commissions');
    Route::post('withdrawCommission', 'index/Agent/withdrawCommission');
    Route::get('levelConfig', 'index/Agent/levelConfig');
    Route::get('materials', 'index/Agent/materials');
    Route::get('tutorial', 'index/Agent/tutorial');
    Route::get('qrcode', 'index/Agent/qrcode');
});

// 合约交易路由
Route::group('contract', function () {
    Route::get('/', 'index/Contract/index');
    Route::post('order', 'index/Contract/order');
    Route::get('history', 'index/Contract/history');
    Route::get('config', 'index/Contract/config');
});

// 新币认购路由
Route::group('ido', function () {
    Route::get('/', 'index/Ido/index');
    Route::get('detail/<id>', 'index/Ido/detail');
    Route::post('purchase', 'index/Ido/purchase');
    Route::get('orders', 'index/Ido/orders');
});

// 资产管理路由
Route::group('wallet', function () {
    Route::get('/', 'index/Wallet/index');
    Route::get('deposit', 'index/Wallet/deposit');
    Route::post('deposit', 'index/Wallet/deposit');
    Route::get('withdraw', 'index/Wallet/withdraw');
    Route::post('withdraw', 'index/Wallet/withdraw');
    Route::get('records', 'index/Wallet/records');
});

// 客服路由
Route::group('service', function () {
    Route::get('/', 'index/Service/index');
    Route::post('send', 'index/Service/send');
    Route::get('messages', 'index/Service/messages');
});

// API路由
Route::group('api', function () {
    Route::get('market-data', 'index/Index/marketData');
    Route::get('kline-data', 'index/Index/klineData');
    Route::get('ticker/<symbol>', 'index/Api/ticker');
    Route::get('depth/<symbol>', 'index/Api/depth');
    Route::get('trades/<symbol>', 'index/Api/trades');
});

// 公告路由
Route::group('notice', function () {
    Route::get('/', 'index/Index/notice');
    Route::get('detail/<id>', 'index/Index/noticeDetail');
});

// 帮助路由
Route::get('help', 'index/Index/help');
Route::get('about', 'index/Index/about');

// 管理后台路由
Route::group('admin', function () {
    // 登录路由
    Route::get('login', 'admin/Auth/login');
    Route::post('login', 'admin/Auth/login');
    Route::get('logout', 'admin/Auth/logout');
    
    // 需要登录的路由
    Route::group('', function () {
        // 首页
        Route::get('/', 'admin/Index/index');
        Route::get('index', 'admin/Index/index');
        Route::get('system-info', 'admin/Index/systemInfo');
        Route::post('clear-cache', 'admin/Index/clearCache');
        
        // 用户管理
        Route::group('user', function () {
            Route::get('/', 'admin/User/index');
            Route::get('list', 'admin/User/index');
            Route::get('add', 'admin/User/add');
            Route::post('add', 'admin/User/add');
            Route::get('edit', 'admin/User/edit');
            Route::post('edit', 'admin/User/edit');
            Route::get('detail', 'admin/User/detail');
            Route::post('changeUserType', 'admin/User/changeUserType');
            Route::post('toggleStatus', 'admin/User/toggleStatus');
            Route::post('delete', 'admin/User/delete');
            Route::post('adjustBalance', 'admin/User/adjustBalance');
            Route::get('statistics', 'admin/User/statistics');
            Route::post('setUniversalCode', 'admin/User/setUniversalCode');
        });
        
        // 币种管理
        Route::group('coin', function () {
            Route::get('/', 'admin/Coin/index');
            Route::get('add', 'admin/Coin/add');
            Route::post('add', 'admin/Coin/add');
            Route::get('edit/<id>', 'admin/Coin/edit');
            Route::post('edit/<id>', 'admin/Coin/edit');
            Route::post('delete', 'admin/Coin/delete');
            Route::post('status', 'admin/Coin/status');
        });
        
        // 交易对管理
        Route::group('trading-pair', function () {
            Route::get('/', 'admin/TradingPair/index');
            Route::get('add', 'admin/TradingPair/add');
            Route::post('add', 'admin/TradingPair/add');
            Route::get('edit/<id>', 'admin/TradingPair/edit');
            Route::post('edit/<id>', 'admin/TradingPair/edit');
            Route::post('delete', 'admin/TradingPair/delete');
            Route::post('status', 'admin/TradingPair/status');
        });
        
        // 订单管理
        Route::group('order', function () {
            Route::get('/', 'admin/Order/index');
            Route::get('detail/<id>', 'admin/Order/detail');
            Route::post('cancel', 'admin/Order/cancel');
        });
        
        // 合约管理
        Route::group('contract', function () {
            Route::get('/', 'admin/Contract/index');
            Route::get('config', 'admin/Contract/config');
            Route::post('config', 'admin/Contract/config');
            Route::get('orders', 'admin/Contract/orders');
        });
        
        // 充值提现管理
        Route::group('finance', function () {
            Route::get('deposits', 'admin/Finance/deposits');
            Route::get('withdrawals', 'admin/Finance/withdrawals');
            Route::post('confirm-deposit', 'admin/Finance/confirmDeposit');
            Route::post('confirm-withdrawal', 'admin/Finance/confirmWithdrawal');
            Route::post('reject-withdrawal', 'admin/Finance/rejectWithdrawal');
        });
        
        // 认购管理
        Route::group('ido', function () {
            Route::get('/', 'admin/Ido/index');
            Route::get('add', 'admin/Ido/add');
            Route::post('add', 'admin/Ido/add');
            Route::get('edit/<id>', 'admin/Ido/edit');
            Route::post('edit/<id>', 'admin/Ido/edit');
            Route::post('delete', 'admin/Ido/delete');
            Route::get('orders/<id>', 'admin/Ido/orders');
        });
        
        // 代理管理
        Route::group('agent', function () {
            Route::get('/', 'admin/Agent/index');
            Route::post('set-agent', 'admin/Agent/setAgent');
            Route::get('commissions', 'admin/Agent/commissions');
        });
        
        // 客服管理
        Route::group('service', function () {
            Route::get('/', 'admin/Service/index');
            Route::get('chat/<id>', 'admin/Service/chat');
            Route::post('reply', 'admin/Service/reply');
        });
        
        // 系统配置
        Route::group('config', function () {
            Route::get('/', 'admin/Config/index');
            Route::post('save', 'admin/Config/save');
            Route::post('upload', 'admin/Config/upload');
        });

        // 系统管理
        Route::group('system', function () {
            Route::get('depositAddress', 'admin/System/depositAddress');
            Route::post('depositAddress', 'admin/System/depositAddress');
            Route::get('contractConfig', 'admin/System/contractConfig');
            Route::post('contractConfig', 'admin/System/contractConfig');
            Route::get('imageConfig', 'admin/System/imageConfig');
            Route::post('imageConfig', 'admin/System/imageConfig');
            Route::get('universalCode', 'admin/System/universalCode');
            Route::post('universalCode', 'admin/System/universalCode');
            Route::get('frontendConfig', 'admin/System/frontendConfig');
            Route::get('status', 'admin/System/status');
        });
        
        // 统计报表
        Route::group('report', function () {
            Route::get('/', 'admin/Report/index');
            Route::get('user', 'admin/Report/user');
            Route::get('trade', 'admin/Report/trade');
            Route::get('finance', 'admin/Report/finance');
        });
        
    })->middleware('admin_auth');
});

// 代理后台路由
Route::group('agent', function () {
    // 登录路由
    Route::get('login', 'agent/Auth/login');
    Route::post('login', 'agent/Auth/login');
    Route::get('logout', 'agent/Auth/logout');

    // 需要登录的路由
    Route::group('', function () {
        // 首页
        Route::get('/', 'agent/Index/index');
        Route::get('dashboard', 'agent/Index/dashboard');

        // 用户管理
        Route::group('user', function () {
            Route::get('/', 'agent/User/index');
            Route::get('add', 'agent/User/add');
            Route::post('add', 'agent/User/add');
            Route::get('edit', 'agent/User/edit');
            Route::post('edit', 'agent/User/edit');
            Route::get('detail', 'agent/User/detail');
            Route::post('delete', 'agent/User/delete');
            Route::get('statistics', 'agent/User/statistics');
        });

        // 合约管理
        Route::group('contract', function () {
            Route::get('/', 'agent/Contract/index');
            Route::post('setResult', 'agent/Contract/setResult');
            Route::post('batchSetResult', 'agent/Contract/batchSetResult');
        });

        // 客服管理
        Route::group('customer-service', function () {
            Route::get('/', 'agent/CustomerService/index');
            Route::post('reply', 'agent/CustomerService/reply');
            Route::post('close', 'agent/CustomerService/close');
            Route::get('sessions', 'agent/CustomerService/sessions');
        });

    })->middleware('AgentAuth');
});

// 代理后台路由
Route::group('agent', function () {
    // 登录路由
    Route::get('login', 'agent/Auth/login');
    Route::post('login', 'agent/Auth/login');
    Route::get('logout', 'agent/Auth/logout');
    
    // 需要登录的路由
    Route::group('', function () {
        Route::get('/', 'agent/Index/index');
        Route::get('users', 'agent/Index/users');
        Route::get('orders', 'agent/Index/orders');
        Route::get('commissions', 'agent/Index/commissions');
        Route::get('service', 'agent/Index/service');
        Route::post('reply', 'agent/Index/reply');
    })->middleware('agent_auth');
});

// 财务管理路由
Route::group('finance', function () {
    Route::get('/', 'index/Finance/index');
    Route::get('deposit', 'index/Finance/deposit');
    Route::get('withdraw', 'index/Finance/withdraw');
    Route::post('submitWithdraw', 'index/Finance/submitWithdraw');
    Route::get('depositHistory', 'index/Finance/depositHistory');
    Route::get('withdrawHistory', 'index/Finance/withdrawHistory');
    Route::get('getDepositAddress', 'index/Finance/getDepositAddress');
    Route::get('checkDeposit', 'index/Finance/checkDeposit');
    Route::post('cancelWithdraw', 'index/Finance/cancelWithdraw');
});

// 错误页面
Route::get('404', function () {
    return view('error/404');
});

Route::get('500', function () {
    return view('error/500');
});

// 缺失路由



