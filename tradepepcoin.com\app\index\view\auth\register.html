{extend name="index/layout" /}

{block name="css"}
<style>
.auth-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    padding-top: 56px;
}

.auth-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    overflow: hidden;
    max-width: 900px;
    margin: 0 auto;
}

.auth-left {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 60px 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
}

.auth-right {
    padding: 60px 40px;
}

.form-floating {
    margin-bottom: 1rem;
}

.btn-auth {
    height: 50px;
    font-size: 16px;
    font-weight: 500;
}

.password-strength {
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    margin-top: 5px;
    overflow: hidden;
}

.password-strength-bar {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-weak { background: #dc3545; width: 33%; }
.strength-medium { background: #ffc107; width: 66%; }
.strength-strong { background: #28a745; width: 100%; }

.invite-code-container {
    position: relative;
}

.invite-code-verify {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
}

.captcha-container {
    display: flex;
    gap: 10px;
    align-items: center;
}

.captcha-img {
    cursor: pointer;
    border: 1px solid #ced4da;
    border-radius: 5px;
}

.email-verify-container {
    display: flex;
    gap: 10px;
    align-items: center;
}

.verify-btn {
    white-space: nowrap;
    min-width: 100px;
}

@media (max-width: 768px) {
    .auth-left {
        display: none;
    }
    
    .auth-right {
        padding: 40px 20px;
    }
}
</style>
{/block}

{block name="content"}
<div class="auth-container">
    <div class="container">
        <div class="auth-card">
            <div class="row g-0">
                <div class="col-lg-6">
                    <div class="auth-left">
                        <div>
                            <h2 class="mb-4">加入我们！</h2>
                            <p class="mb-4">注册账户，开启您的数字货币交易之旅</p>
                            <div class="features">
                                <div class="feature-item mb-3">
                                    <i class="fas fa-gift me-2"></i>
                                    新用户注册奖励
                                </div>
                                <div class="feature-item mb-3">
                                    <i class="fas fa-percentage me-2"></i>
                                    超低交易手续费
                                </div>
                                <div class="feature-item mb-3">
                                    <i class="fas fa-mobile-alt me-2"></i>
                                    多平台支持
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="auth-right">
                        <div class="text-center mb-4">
                            <h3 class="fw-bold">用户注册</h3>
                            <p class="text-muted">创建您的交易账户</p>
                        </div>

                        <form id="registerForm">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="username" name="username" placeholder="用户名" required>
                                <label for="username">用户名</label>
                                <div class="invalid-feedback"></div>
                            </div>

                            <div class="form-floating">
                                <div class="email-verify-container">
                                    <input type="email" class="form-control" id="email" name="email" placeholder="邮箱地址" required>
                                    <button type="button" class="btn btn-outline-primary verify-btn" id="sendEmailCode">
                                        发送验证码
                                    </button>
                                </div>
                                <label for="email">邮箱地址</label>
                                <div class="invalid-feedback"></div>
                            </div>

                            <div class="form-floating">
                                <input type="text" class="form-control" id="email_code" name="email_code" placeholder="邮箱验证码" required>
                                <label for="email_code">邮箱验证码</label>
                                <div class="invalid-feedback"></div>
                            </div>

                            <div class="form-floating">
                                <input type="password" class="form-control" id="password" name="password" placeholder="密码" required>
                                <label for="password">密码（6-20位）</label>
                                <div class="password-strength">
                                    <div class="password-strength-bar" id="passwordStrengthBar"></div>
                                </div>
                                <div class="invalid-feedback"></div>
                            </div>

                            <div class="form-floating">
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="确认密码" required>
                                <label for="confirm_password">确认密码</label>
                                <div class="invalid-feedback"></div>
                            </div>

                            <div class="form-floating">
                                <div class="invite-code-container">
                                    <input type="text" class="form-control" id="invite_code" name="invite_code" placeholder="邀请码（可选）">
                                    <span class="invite-code-verify" id="inviteCodeStatus"></span>
                                </div>
                                <label for="invite_code">邀请码（可选）</label>
                            </div>

                            <div class="form-floating">
                                <div class="captcha-container">
                                    <input type="text" class="form-control" id="captcha" name="captcha" placeholder="验证码" required>
                                    <img src="/auth/captcha" alt="验证码" class="captcha-img" id="captchaImg" width="120" height="40">
                                </div>
                                <label for="captcha">验证码</label>
                            </div>

                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="agree_terms" name="agree_terms" required>
                                <label class="form-check-label" for="agree_terms">
                                    我已阅读并同意 <a href="/terms" target="_blank">服务条款</a> 和 <a href="/privacy" target="_blank">隐私政策</a>
                                </label>
                            </div>

                            <button type="submit" class="btn btn-primary w-100 btn-auth" id="registerBtn">
                                <span class="spinner-border spinner-border-sm me-2 d-none" id="registerSpinner"></span>
                                注册账户
                            </button>
                        </form>

                        <div class="text-center mt-4">
                            <p class="mb-0">已有账户？ <a href="/auth/login" class="text-decoration-none fw-bold">立即登录</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="js"}
<script>
$(document).ready(function() {
    let emailCodeSent = false;
    let emailCodeTimer = 0;

    // 刷新验证码
    $('#captchaImg').click(function() {
        $(this).attr('src', '/auth/captcha?' + Math.random());
    });

    // 用户名验证
    $('#username').on('blur', function() {
        const username = $(this).val();
        if (username.length < 3 || username.length > 20) {
            showFieldError($(this), '用户名长度应为3-20位');
            return;
        }

        // 检查用户名是否已存在
        $.post('/auth/check-username', {username: username})
            .done(function(response) {
                if (response.code === 1) {
                    showFieldSuccess($(this));
                } else {
                    showFieldError($(this), response.msg);
                }
            });
    });

    // 邮箱验证
    $('#email').on('blur', function() {
        const email = $(this).val();
        if (!isValidEmail(email)) {
            showFieldError($(this), '请输入有效的邮箱地址');
            return;
        }

        // 检查邮箱是否已存在
        $.post('/auth/check-email', {email: email})
            .done(function(response) {
                if (response.code === 1) {
                    showFieldSuccess($(this));
                } else {
                    showFieldError($(this), response.msg);
                }
            });
    });

    // 发送邮箱验证码
    $('#sendEmailCode').click(function() {
        const email = $('#email').val();
        if (!isValidEmail(email)) {
            showToast('请先输入有效的邮箱地址', 'warning');
            return;
        }

        const $btn = $(this);
        $btn.prop('disabled', true);

        $.post('/auth/send-email-code', {email: email})
            .done(function(response) {
                if (response.code === 1) {
                    showToast('验证码已发送到您的邮箱', 'success');
                    emailCodeSent = true;
                    startEmailCodeTimer();
                } else {
                    showToast(response.msg || '发送失败', 'error');
                    $btn.prop('disabled', false);
                }
            })
            .fail(function() {
                showToast('网络错误，请稍后重试', 'error');
                $btn.prop('disabled', false);
            });
    });

    // 密码强度检测
    $('#password').on('input', function() {
        const password = $(this).val();
        const strength = checkPasswordStrength(password);
        updatePasswordStrength(strength);
        
        if (password.length > 0 && password.length < 6) {
            showFieldError($(this), '密码长度不能少于6位');
        } else if (password.length > 20) {
            showFieldError($(this), '密码长度不能超过20位');
        } else {
            clearFieldError($(this));
        }
    });

    // 确认密码验证
    $('#confirm_password').on('blur', function() {
        const password = $('#password').val();
        const confirmPassword = $(this).val();
        
        if (confirmPassword && password !== confirmPassword) {
            showFieldError($(this), '两次输入的密码不一致');
        } else {
            clearFieldError($(this));
        }
    });

    // 邀请码验证
    $('#invite_code').on('blur', function() {
        const inviteCode = $(this).val();
        if (!inviteCode) {
            $('#inviteCodeStatus').html('');
            return;
        }

        $.post('/auth/check-invite-code', {invite_code: inviteCode})
            .done(function(response) {
                if (response.code === 1) {
                    $('#inviteCodeStatus').html('<i class="fas fa-check text-success"></i>');
                } else {
                    $('#inviteCodeStatus').html('<i class="fas fa-times text-danger"></i>');
                }
            });
    });

    // 注册表单提交
    $('#registerForm').submit(function(e) {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        const $btn = $('#registerBtn');
        const $spinner = $('#registerSpinner');
        const formData = $(this).serialize();
        
        // 显示加载状态
        $btn.prop('disabled', true);
        $spinner.removeClass('d-none');
        
        $.post('/auth/register', formData)
            .done(function(response) {
                if (response.code === 1) {
                    showToast('注册成功！', 'success');
                    setTimeout(() => {
                        window.location.href = '/auth/login';
                    }, 1500);
                } else {
                    showToast(response.msg || '注册失败', 'error');
                    $('#captchaImg').click();
                }
            })
            .fail(function() {
                showToast('网络错误，请稍后重试', 'error');
                $('#captchaImg').click();
            })
            .always(function() {
                // 恢复按钮状态
                $btn.prop('disabled', false);
                $spinner.addClass('d-none');
            });
    });

    // 邮箱验证码倒计时
    function startEmailCodeTimer() {
        emailCodeTimer = 60;
        const $btn = $('#sendEmailCode');
        
        const timer = setInterval(() => {
            $btn.text(`${emailCodeTimer}秒后重发`);
            emailCodeTimer--;
            
            if (emailCodeTimer < 0) {
                clearInterval(timer);
                $btn.text('发送验证码').prop('disabled', false);
            }
        }, 1000);
    }

    // 表单验证
    function validateForm() {
        let isValid = true;

        // 验证用户名
        const username = $('#username').val();
        if (username.length < 3 || username.length > 20) {
            showFieldError($('#username'), '用户名长度应为3-20位');
            isValid = false;
        }

        // 验证邮箱
        const email = $('#email').val();
        if (!isValidEmail(email)) {
            showFieldError($('#email'), '请输入有效的邮箱地址');
            isValid = false;
        }

        // 验证邮箱验证码
        const emailCode = $('#email_code').val();
        if (!emailCode) {
            showFieldError($('#email_code'), '请输入邮箱验证码');
            isValid = false;
        }

        // 验证密码
        const password = $('#password').val();
        if (password.length < 6 || password.length > 20) {
            showFieldError($('#password'), '密码长度应为6-20位');
            isValid = false;
        }

        // 验证确认密码
        const confirmPassword = $('#confirm_password').val();
        if (password !== confirmPassword) {
            showFieldError($('#confirm_password'), '两次输入的密码不一致');
            isValid = false;
        }

        // 验证协议同意
        if (!$('#agree_terms').is(':checked')) {
            showToast('请先同意服务条款和隐私政策', 'warning');
            isValid = false;
        }

        return isValid;
    }
});

// 密码强度检测
function checkPasswordStrength(password) {
    let score = 0;
    
    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;
    
    if (score < 2) return 'weak';
    if (score < 4) return 'medium';
    return 'strong';
}

// 更新密码强度显示
function updatePasswordStrength(strength) {
    const $bar = $('#passwordStrengthBar');
    $bar.removeClass('strength-weak strength-medium strength-strong');
    
    if (strength) {
        $bar.addClass('strength-' + strength);
    }
}

// 显示字段错误
function showFieldError($field, message) {
    $field.addClass('is-invalid');
    $field.siblings('.invalid-feedback').text(message);
}

// 显示字段成功
function showFieldSuccess($field) {
    $field.removeClass('is-invalid').addClass('is-valid');
}

// 清除字段错误
function clearFieldError($field) {
    $field.removeClass('is-invalid is-valid');
}

// 邮箱验证
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
</script>
{/block}
