<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>系统发生错误</title>
    <meta name="robots" content="noindex,nofollow" />
    <style>
        /* Base */
        body {
            color: #333;
            font: 16px <PERSON>erd<PERSON>, "Helvetica Neue", helvetica, Arial, 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0 20px 20px;
        }
        h1{
            margin: 10px 0 0;
            font-size: 28px;
            font-weight: 500;
            line-height: 32px;
        }
        h2{
            color: #4288ce;
            font-weight: 400;
            padding: 6px 0;
            margin: 6px 0 0;
            font-size: 18px;
            border-bottom: 1px solid #eee;
        }
        h3{
            margin: 12px;
            font-size: 16px;
            font-weight: bold;
        }
        abbr{
            cursor: help;
            text-decoration: underline;
            text-decoration-style: dotted;
        }
        a{
            color: #868686;
            cursor: pointer;
        }
        a:hover{
            text-decoration: underline;
        }
        .line-error{
            background: #f8cbcb;
        }
        .echo table {
            width: 100%;
        }
        .echo pre {
            padding: 16px;
            overflow: auto;
            font-size: 85%;
            line-height: 1.45;
            background-color: #f7f7f7;
            border: 0;
            border-radius: 3px;
            font-family: Consolas, "Liberation Mono", Menlo, Courier, monospace;
        }
        .echo pre > pre {
            padding: 0;
            margin: 0;
        }
        /* Exception Info */
        .exception {
            margin-top: 20px;
        }
        .exception .message{
            padding: 12px;
            border: 1px solid #ddd;
            border-bottom: 0 none;
            line-height: 18px;
            font-size:16px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            font-family: Consolas,"Liberation Mono",Courier,Verdana,"微软雅黑",serif;
        }
        .exception .code{
            float: left;
            text-align: center;
            color: #fff;
            margin-right: 12px;
            padding: 16px;
            border-radius: 4px;
            background: #999;
        }
        .exception .source-code{
            padding: 6px;
            border: 1px solid #ddd;

            background: #f9f9f9;
            overflow-x: auto;

        }
        .exception .source-code pre{
            margin: 0;
        }
        .exception .source-code pre ol{
            margin: 0;
            color: #4288ce;
            display: inline-block;
            min-width: 100%;
            box-sizing: border-box;
            font-size:14px;
            font-family: "Century Gothic",Consolas,"Liberation Mono",Courier,Verdana,serif;
            padding-left: 40px;
        }
        .exception .source-code pre li{
            border-left: 1px solid #ddd;
            height: 18px;
            line-height: 18px;
        }
        .exception .source-code pre code{
            color: #333;
            height: 100%;
            display: inline-block;
            border-left: 1px solid #fff;
            font-size:14px;
            font-family: Consolas,"Liberation Mono",Courier,Verdana,"微软雅黑",serif;
        }
        .exception .trace{
            padding: 6px;
            border: 1px solid #ddd;
            border-top: 0 none;
            line-height: 16px;
            font-size:14px;
            font-family: Consolas,"Liberation Mono",Courier,Verdana,"微软雅黑",serif;
        }
        .exception .trace h2:hover {
            text-decoration: underline;
            cursor: pointer;
        }
        .exception .trace ol{
            margin: 12px;
        }
        .exception .trace ol li{
            padding: 2px 4px;
        }
        .exception div:last-child{
            border-bottom-left-radius: 4px;
            border-bottom-right-radius: 4px;
        }

        /* Exception Variables */
        .exception-var table{
            width: 100%;
            margin: 12px 0;
            box-sizing: border-box;
            table-layout:fixed;
            word-wrap:break-word;
        }
        .exception-var table caption{
            text-align: left;
            font-size: 16px;
            font-weight: bold;
            padding: 6px 0;
        }
        .exception-var table caption small{
            font-weight: 300;
            display: inline-block;
            margin-left: 10px;
            color: #ccc;
        }
        .exception-var table tbody{
            font-size: 13px;
            font-family: Consolas, "Liberation Mono", Courier, "微软雅黑",serif;
        }
        .exception-var table td{
            padding: 0 6px;
            vertical-align: top;
            word-break: break-all;
        }
        .exception-var table td:first-child{
            width: 28%;
            font-weight: bold;
            white-space: nowrap;
        }
        .exception-var table td pre{
            margin: 0;
        }

        /* Copyright Info */
        .copyright{
            margin-top: 24px;
            padding: 12px 0;
            border-top: 1px solid #eee;
        }

        /* SPAN elements with the classes below are added by prettyprint. */
        pre.prettyprint .pln { color: #000 }  /* plain text */
        pre.prettyprint .str { color: #080 }  /* string content */
        pre.prettyprint .kwd { color: #008 }  /* a keyword */
        pre.prettyprint .com { color: #800 }  /* a comment */
        pre.prettyprint .typ { color: #606 }  /* a type name */
        pre.prettyprint .lit { color: #066 }  /* a literal value */
        /* punctuation, lisp open bracket, lisp close bracket */
        pre.prettyprint .pun, pre.prettyprint .opn, pre.prettyprint .clo { color: #660 }
        pre.prettyprint .tag { color: #008 }  /* a markup tag name */
        pre.prettyprint .atn { color: #606 }  /* a markup attribute name */
        pre.prettyprint .atv { color: #080 }  /* a markup attribute value */
        pre.prettyprint .dec, pre.prettyprint .var { color: #606 }  /* a declaration; a variable name */
        pre.prettyprint .fun { color: red }  /* a function name */
    </style>
</head>
<body>
                <div class="exception">
        <div class="message">
            <div class="info">
                <div>
                    <h2>#0 [0]<abbr title="ParseError">ParseError</abbr> in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/app/common/controller/BaseController.php line 188">BaseController.php line 188</a></h2>
                </div>
                <div><h1>语法错误: unexpected token &quot;protected&quot;, expecting end of file</h1></div>
            </div>
        </div>
                            <div class="source-code">
                    <pre class="prettyprint lang-php"><ol start="179"><li class="line-0-179"><code>}
</code></li><li class="line-0-180"><code>
</code></li><li class="line-0-181"><code>    /**
</code></li><li class="line-0-182"><code>     * 重定向
</code></li><li class="line-0-183"><code>     * @access protected
</code></li><li class="line-0-184"><code>     * @param  string $url 跳转的URL地址
</code></li><li class="line-0-185"><code>     * @param  int    $code 状态码
</code></li><li class="line-0-186"><code>     * @return void
</code></li><li class="line-0-187"><code>     */
</code></li><li class="line-0-188 line-error"><code>    protected function redirect($url, $code = 302)
</code></li><li class="line-0-189"><code>    {
</code></li><li class="line-0-190"><code>        $response = redirect($url, $code);
</code></li><li class="line-0-191"><code>        throw new \think\exception\HttpResponseException($response);
</code></li><li class="line-0-192"><code>    }
</code></li><li class="line-0-193"><code>}
</code></li></ol></pre>
                </div>
                    <div class="trace">
            <h2 data-expand="1">Call Stack</h2>
            <ol>
                <li>in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/app/common/controller/BaseController.php line 188">BaseController.php line 188</a></li>
                                    <li>
                        at Composer\Autoload\{closure}() in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/vendor/composer/ClassLoader.php line 427">ClassLoader.php line 427</a>                    </li>
                                    <li>
                        at <abbr title="Composer\Autoload\ClassLoader">ClassLoader</abbr>->loadClass() in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/app/agent/controller/Dashboard.php line 16">Dashboard.php line 16</a>                    </li>
                                    <li>
                        at include('<a class="toggle" title="/www/wwwroot/tradepepcoin.com/app/agent/controller/Dashboard.php">/www/wwwroot/tradepe...</a>') in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/vendor/composer/ClassLoader.php line 576">ClassLoader.php line 576</a>                    </li>
                                    <li>
                        at Composer\Autoload\{closure}() in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/vendor/composer/ClassLoader.php line 427">ClassLoader.php line 427</a>                    </li>
                                    <li>
                        at <abbr title="Composer\Autoload\ClassLoader">ClassLoader</abbr>->loadClass()                    </li>
                                    <li>
                        at class_exists() in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/vendor/topthink/framework/src/think/route/dispatch/Controller.php line 186">Controller.php line 186</a>                    </li>
                                    <li>
                        at <abbr title="think\route\dispatch\Controller">Controller</abbr>->controller() in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/vendor/topthink/framework/src/think/route/dispatch/Controller.php line 74">Controller.php line 74</a>                    </li>
                                    <li>
                        at <abbr title="think\route\dispatch\Controller">Controller</abbr>->exec() in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/vendor/topthink/framework/src/think/route/Dispatch.php line 80">Dispatch.php line 80</a>                    </li>
                                    <li>
                        at <abbr title="think\route\Dispatch">Dispatch</abbr>->run() in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/vendor/topthink/framework/src/think/Route.php line 793">Route.php line 793</a>                    </li>
                                    <li>
                        at <abbr title="think\Route">Route</abbr>->think\{closure}() in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/vendor/topthink/framework/src/think/Pipeline.php line 59">Pipeline.php line 59</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}() in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/vendor/topthink/framework/src/think/Pipeline.php line 66">Pipeline.php line 66</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->then() in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/vendor/topthink/framework/src/think/Route.php line 794">Route.php line 794</a>                    </li>
                                    <li>
                        at <abbr title="think\Route">Route</abbr>->dispatch() in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/vendor/topthink/framework/src/think/Http.php line 216">Http.php line 216</a>                    </li>
                                    <li>
                        at <abbr title="think\Http">Http</abbr>->dispatchToRoute() in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/vendor/topthink/framework/src/think/Http.php line 206">Http.php line 206</a>                    </li>
                                    <li>
                        at <abbr title="think\Http">Http</abbr>->think\{closure}() in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/vendor/topthink/framework/src/think/Pipeline.php line 59">Pipeline.php line 59</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}() in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/vendor/topthink/think-multi-app/src/MultiApp.php line 51">MultiApp.php line 51</a>                    </li>
                                    <li>
                        at <abbr title="think\app\MultiApp">MultiApp</abbr>->think\app\{closure}() in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/vendor/topthink/framework/src/think/Pipeline.php line 59">Pipeline.php line 59</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}() in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/vendor/topthink/framework/src/think/Pipeline.php line 66">Pipeline.php line 66</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->then() in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/vendor/topthink/think-multi-app/src/MultiApp.php line 52">MultiApp.php line 52</a>                    </li>
                                    <li>
                        at <abbr title="think\app\MultiApp">MultiApp</abbr>->handle()                    </li>
                                    <li>
                        at call_user_func() in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/vendor/topthink/framework/src/think/Middleware.php line 142">Middleware.php line 142</a>                    </li>
                                    <li>
                        at <abbr title="think\Middleware">Middleware</abbr>->think\{closure}() in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/vendor/topthink/framework/src/think/Pipeline.php line 85">Pipeline.php line 85</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->think\{closure}() in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/vendor/topthink/framework/src/think/Pipeline.php line 66">Pipeline.php line 66</a>                    </li>
                                    <li>
                        at <abbr title="think\Pipeline">Pipeline</abbr>->then() in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/vendor/topthink/framework/src/think/Http.php line 207">Http.php line 207</a>                    </li>
                                    <li>
                        at <abbr title="think\Http">Http</abbr>->runWithRequest() in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/vendor/topthink/framework/src/think/Http.php line 170">Http.php line 170</a>                    </li>
                                    <li>
                        at <abbr title="think\Http">Http</abbr>->run() in <a class="toggle" title="/www/wwwroot/tradepepcoin.com/public/index.php line 10">index.php line 10</a>                    </li>
                            </ol>
        </div>
    </div>
                
    
        <div class="exception-var">
        <h2>Environment Variables</h2>
                <table>
                        <caption>GET Data<small>empty</small></caption>
                    </table>
                <table>
                        <caption>POST Data<small>empty</small></caption>
                    </table>
                <table>
                        <caption>Files<small>empty</small></caption>
                    </table>
                <table>
                        <caption>Cookies<small>empty</small></caption>
                    </table>
                <table>
                        <caption>Session<small>empty</small></caption>
                    </table>
                <table>
                        <caption>Server/Request Data</caption>
            <tbody>
                                <tr>
                    <td>USER</td>
                    <td>www</td>
                </tr>
                                <tr>
                    <td>HOME</td>
                    <td>/home/<USER>/td>
                </tr>
                                <tr>
                    <td>HTTP_ACCEPT</td>
                    <td>*/*</td>
                </tr>
                                <tr>
                    <td>HTTP_USER_AGENT</td>
                    <td>curl/7.81.0</td>
                </tr>
                                <tr>
                    <td>HTTP_HOST</td>
                    <td>tradepepcoin.com</td>
                </tr>
                                <tr>
                    <td>PATH_INFO</td>
                    <td></td>
                </tr>
                                <tr>
                    <td>REDIRECT_STATUS</td>
                    <td>200</td>
                </tr>
                                <tr>
                    <td>SERVER_NAME</td>
                    <td>tradepepcoin.com</td>
                </tr>
                                <tr>
                    <td>SERVER_PORT</td>
                    <td>443</td>
                </tr>
                                <tr>
                    <td>SERVER_ADDR</td>
                    <td>**************</td>
                </tr>
                                <tr>
                    <td>REMOTE_PORT</td>
                    <td>58648</td>
                </tr>
                                <tr>
                    <td>REMOTE_ADDR</td>
                    <td>8.213.224.235</td>
                </tr>
                                <tr>
                    <td>SERVER_SOFTWARE</td>
                    <td>nginx/1.20.2</td>
                </tr>
                                <tr>
                    <td>GATEWAY_INTERFACE</td>
                    <td>CGI/1.1</td>
                </tr>
                                <tr>
                    <td>HTTPS</td>
                    <td>on</td>
                </tr>
                                <tr>
                    <td>REQUEST_SCHEME</td>
                    <td>https</td>
                </tr>
                                <tr>
                    <td>SERVER_PROTOCOL</td>
                    <td>HTTP/2.0</td>
                </tr>
                                <tr>
                    <td>DOCUMENT_ROOT</td>
                    <td>/www/wwwroot/tradepepcoin.com/public</td>
                </tr>
                                <tr>
                    <td>DOCUMENT_URI</td>
                    <td>/index.php</td>
                </tr>
                                <tr>
                    <td>REQUEST_URI</td>
                    <td>/agent/dashboard/customerService</td>
                </tr>
                                <tr>
                    <td>SCRIPT_NAME</td>
                    <td>/index.php</td>
                </tr>
                                <tr>
                    <td>CONTENT_LENGTH</td>
                    <td></td>
                </tr>
                                <tr>
                    <td>CONTENT_TYPE</td>
                    <td></td>
                </tr>
                                <tr>
                    <td>REQUEST_METHOD</td>
                    <td>GET</td>
                </tr>
                                <tr>
                    <td>QUERY_STRING</td>
                    <td>s=//agent/dashboard/customerService</td>
                </tr>
                                <tr>
                    <td>SCRIPT_FILENAME</td>
                    <td>/www/wwwroot/tradepepcoin.com/public/index.php</td>
                </tr>
                                <tr>
                    <td>FCGI_ROLE</td>
                    <td>RESPONDER</td>
                </tr>
                                <tr>
                    <td>PHP_SELF</td>
                    <td>/index.php</td>
                </tr>
                                <tr>
                    <td>REQUEST_TIME_FLOAT</td>
                    <td>1754220331.3515</td>
                </tr>
                                <tr>
                    <td>REQUEST_TIME</td>
                    <td>1754220331</td>
                </tr>
                            </tbody>
                    </table>
            </div>
    
    <div class="copyright">
        <a title="官方网站" href="http://www.thinkphp.cn">ThinkPHP</a> 
        <span>V6.1.4</span> 
        <span>{ 十年磨一剑-为API开发设计的高性能框架 }</span>
        <span>- <a title="官方手册" href="https://www.kancloud.cn/manual/thinkphp6_0/content">官方手册</a></span>
    </div>
        <script>
        function $(selector, node){
            var elements;

            node = node || document;
            if(document.querySelectorAll){
                elements = node.querySelectorAll(selector);
            } else {
                switch(selector.substr(0, 1)){
                    case '#':
                        elements = [node.getElementById(selector.substr(1))];
                        break;
                    case '.':
                        if(document.getElementsByClassName){
                            elements = node.getElementsByClassName(selector.substr(1));
                        } else {
                            elements = get_elements_by_class(selector.substr(1), node);
                        }
                        break;
                    default:
                        elements = node.getElementsByTagName();
                }
            }
            return elements;

            function get_elements_by_class(search_class, node, tag) {
                var elements = [], eles, 
                    pattern  = new RegExp('(^|\\s)' + search_class + '(\\s|$)');

                node = node || document;
                tag  = tag  || '*';

                eles = node.getElementsByTagName(tag);
                for(var i = 0; i < eles.length; i++) {
                    if(pattern.test(eles[i].className)) {
                        elements.push(eles[i])
                    }
                }

                return elements;
            }
        }

        $.getScript = function(src, func){
            var script = document.createElement('script');
            
            script.async  = 'async';
            script.src    = src;
            script.onload = func || function(){};
            
            $('head')[0].appendChild(script);
        }

        ;(function(){
            var files = $('.toggle');
            var ol    = $('ol', $('.prettyprint')[0]);
            var li    = $('li', ol[0]);   

            // 短路径和长路径变换
            for(var i = 0; i < files.length; i++){
                files[i].ondblclick = function(){
                    var title = this.title;

                    this.title = this.innerHTML;
                    this.innerHTML = title;
                }
            }

            (function () {
                var expand = function (dom, expand) {
                    var ol = $('ol', dom.parentNode)[0];
                    expand = undefined === expand ? dom.attributes['data-expand'].value === '0' : undefined;
                    if (expand) {
                        dom.attributes['data-expand'].value = '1';
                        ol.style.display = 'none';
                        dom.innerText = 'Call Stack (展开)';
                    } else {
                        dom.attributes['data-expand'].value = '0';
                        ol.style.display = 'block';
                        dom.innerText = 'Call Stack (折叠)';
                    }
                };
                var traces = $('.trace');
                for (var i = 0; i < traces.length; i ++) {
                    var h2 = $('h2', traces[i])[0];
                    expand(h2);
                    h2.onclick = function () {
                        expand(this);
                    };
                }
            })();

            $.getScript('//cdn.bootcdn.net/ajax/libs/prettify/r298/prettify.min.js', function(){
                prettyPrint();
            });
        })();
    </script>
    </body>
</html>
