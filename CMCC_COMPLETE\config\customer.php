<?php
// +----------------------------------------------------------------------
// | 客服系统配置
// +----------------------------------------------------------------------

return [
    // WebSocket服务配置
    'websocket' => [
        'host' => '0.0.0.0',
        'port' => 2346,
        'ssl' => false,
        'ssl_cert' => '',
        'ssl_key' => '',
        'worker_num' => 1,
        'max_connections' => 1000,
        'heartbeat_interval' => 30, // 心跳间隔(秒)
        'connection_timeout' => 300, // 连接超时(秒)
    ],

    // 消息配置
    'message' => [
        'max_length' => 1000, // 最大消息长度
        'history_limit' => 100, // 历史消息加载数量
        'retention_days' => 30, // 消息保留天数
        'auto_read_timeout' => 60, // 自动标记已读超时(秒)
    ],

    // 文件上传配置
    'upload' => [
        'max_size' => 5 * 1024 * 1024, // 5MB
        'allowed_types' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'path' => 'uploads/customer/',
        'url_prefix' => '/',
    ],

    // 会话配置
    'session' => [
        'auto_close_timeout' => 3600, // 自动关闭超时(秒)
        'max_concurrent_sessions' => 10, // 每个代理最大并发会话数
        'priority_levels' => [
            1 => '普通',
            2 => '重要', 
            3 => '紧急'
        ],
        'status_options' => [
            'active' => '活跃',
            'waiting' => '等待',
            'closed' => '已关闭'
        ],
    ],

    // 在线状态配置
    'online_status' => [
        'offline_timeout' => 300, // 离线超时(秒)
        'status_options' => [
            'online' => '在线',
            'offline' => '离线',
            'busy' => '忙碌'
        ],
    ],

    // 快捷回复配置
    'quick_reply' => [
        'categories' => [
            'general' => '通用',
            'trading' => '交易',
            'account' => '账户',
            'technical' => '技术'
        ],
        'max_replies_per_category' => 20,
    ],

    // 表情包配置
    'emoji' => [
        'categories' => [
            'smileys' => [
                'name' => '笑脸',
                'emojis' => [
                    '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', 
                    '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', 
                    '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', 
                    '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳'
                ]
            ],
            'emotions' => [
                'name' => '情感',
                'emojis' => [
                    '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', 
                    '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', 
                    '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', 
                    '😨', '😰', '😥', '😓', '🤗', '🤔'
                ]
            ],
            'gestures' => [
                'name' => '手势',
                'emojis' => [
                    '👍', '👎', '👌', '🤌', '🤏', '✌️', '🤞', '🤟', 
                    '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '☝️', 
                    '👋', '🤚', '🖐️', '✋', '🖖', '👏', '🙌', '🤲', 
                    '🤝', '🙏', '✍️', '💪', '🦾', '🦿'
                ]
            ],
            'objects' => [
                'name' => '物品',
                'emojis' => [
                    '💰', '💎', '💳', '💸', '📱', '💻', '⌨️', '🖥️', 
                    '🖨️', '📊', '📈', '📉', '💹', '🔔', '🔕', '📢', 
                    '📣', '📯', '🎯', '🎪', '🎭', '🎨', '🎬', '🎤', 
                    '🎧', '🎼', '🎵', '🎶', '🎹', '🥁'
                ]
            ]
        ]
    ],

    // 通知配置
    'notification' => [
        'browser_notification' => true, // 浏览器通知
        'sound_notification' => true, // 声音通知
        'desktop_notification' => false, // 桌面通知
        'email_notification' => false, // 邮件通知
        'sms_notification' => false, // 短信通知
    ],

    // 统计配置
    'statistics' => [
        'retention_days' => 365, // 统计数据保留天数
        'update_interval' => 3600, // 统计更新间隔(秒)
        'metrics' => [
            'session_count' => '会话数量',
            'message_count' => '消息数量',
            'response_time' => '响应时间',
            'satisfaction_score' => '满意度评分',
            'online_time' => '在线时长'
        ]
    ],

    // 权限配置
    'permissions' => [
        'user' => [
            'create_session',
            'send_message',
            'upload_image',
            'view_history'
        ],
        'agent' => [
            'view_sessions',
            'send_message',
            'upload_image',
            'view_history',
            'use_quick_reply',
            'view_customer_info',
            'contact_customer'
        ],
        'admin' => [
            'view_all_sessions',
            'send_message',
            'upload_image',
            'view_history',
            'close_session',
            'transfer_session',
            'manage_quick_reply',
            'view_statistics',
            'manage_agents'
        ]
    ],

    // 安全配置
    'security' => [
        'rate_limit' => [
            'message_per_minute' => 30, // 每分钟最大消息数
            'session_per_hour' => 5, // 每小时最大会话数
            'upload_per_hour' => 10, // 每小时最大上传数
        ],
        'content_filter' => [
            'enabled' => true,
            'forbidden_words' => [], // 禁用词列表
            'auto_block' => false, // 自动屏蔽
        ],
        'ip_whitelist' => [], // IP白名单
        'ip_blacklist' => [], // IP黑名单
    ],

    // 界面配置
    'ui' => [
        'theme' => 'dark', // 主题: dark/light
        'language' => 'zh-CN', // 语言
        'auto_scroll' => true, // 自动滚动
        'show_typing_indicator' => true, // 显示输入指示器
        'show_online_status' => true, // 显示在线状态
        'show_read_status' => true, // 显示已读状态
        'message_time_format' => 'H:i', // 消息时间格式
        'date_format' => 'Y-m-d', // 日期格式
    ],

    // 性能配置
    'performance' => [
        'cache_enabled' => true, // 启用缓存
        'cache_ttl' => 300, // 缓存TTL(秒)
        'lazy_load_messages' => true, // 懒加载消息
        'compress_images' => true, // 压缩图片
        'max_memory_usage' => '256M', // 最大内存使用
    ],

    // 日志配置
    'logging' => [
        'enabled' => true,
        'level' => 'info', // debug/info/warning/error
        'channels' => [
            'websocket' => 'customer_websocket',
            'message' => 'customer_message',
            'session' => 'customer_session',
            'error' => 'customer_error'
        ],
        'retention_days' => 30,
    ],

    // 备份配置
    'backup' => [
        'enabled' => true,
        'interval' => 86400, // 备份间隔(秒)
        'retention_days' => 7, // 备份保留天数
        'compress' => true, // 压缩备份
        'path' => 'backup/customer/',
    ],
];
