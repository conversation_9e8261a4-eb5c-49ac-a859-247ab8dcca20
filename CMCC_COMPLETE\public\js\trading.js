/**
 * 交易页面JavaScript逻辑
 */
class TradingPage {
    constructor() {
        this.currentSymbol = 'BTCUSDT';
        this.currentSide = 'buy';
        this.ws = null;
        this.token = localStorage.getItem('token');
        this.userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
        
        if (!this.token) {
            window.location.href = '/auth/login.html';
            return;
        }
        
        this.init();
    }
    
    init() {
        this.loadUserBalance();
        this.loadOrderBook();
        this.loadLatestTrades();
        this.connectWebSocket();
        this.bindEvents();
        this.updatePriceAndAmount();
    }
    
    bindEvents() {
        // 价格和数量输入框事件
        document.getElementById('tradePrice').addEventListener('input', () => {
            this.calculateTotal();
        });
        
        document.getElementById('tradeAmount').addEventListener('input', () => {
            this.calculateTotal();
        });
        
        // 时间周期切换
        document.querySelectorAll('.interval-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.interval-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.updateChartInterval(e.target.dataset.interval);
            });
        });
    }
    
    async loadUserBalance() {
        try {
            const response = await this.apiRequest('/api/asset/list');
            if (response.code === 1) {
                const usdtAsset = response.data.assets.find(asset => asset.coin_symbol === 'USDT');
                if (usdtAsset) {
                    document.getElementById('usdtBalance').textContent = usdtAsset.available;
                }
            }
        } catch (error) {
            console.error('加载用户余额失败:', error);
        }
    }
    
    async loadOrderBook() {
        try {
            const response = await this.apiRequest(`/api/spot/orderbook?symbol=${this.currentSymbol}`);
            if (response.code === 1) {
                this.renderOrderBook(response.data);
            }
        } catch (error) {
            console.error('加载订单簿失败:', error);
        }
    }
    
    renderOrderBook(data) {
        const sellOrdersElement = document.getElementById('sellOrders');
        const buyOrdersElement = document.getElementById('buyOrders');
        
        // 渲染卖单（价格从高到低）
        sellOrdersElement.innerHTML = '';
        data.asks.slice(0, 10).reverse().forEach(order => {
            const row = document.createElement('tr');
            row.className = 'sell-order';
            row.innerHTML = `
                <td onclick="setPrice(${order[0]})">${parseFloat(order[0]).toFixed(2)}</td>
                <td>${parseFloat(order[1]).toFixed(6)}</td>
                <td>${(parseFloat(order[0]) * parseFloat(order[1])).toFixed(2)}</td>
            `;
            sellOrdersElement.appendChild(row);
        });
        
        // 渲染买单（价格从高到低）
        buyOrdersElement.innerHTML = '';
        data.bids.slice(0, 10).forEach(order => {
            const row = document.createElement('tr');
            row.className = 'buy-order';
            row.innerHTML = `
                <td onclick="setPrice(${order[0]})">${parseFloat(order[0]).toFixed(2)}</td>
                <td>${parseFloat(order[1]).toFixed(6)}</td>
                <td>${(parseFloat(order[0]) * parseFloat(order[1])).toFixed(2)}</td>
            `;
            buyOrdersElement.appendChild(row);
        });
        
        // 更新价差
        if (data.asks.length > 0 && data.bids.length > 0) {
            const spread = parseFloat(data.asks[0][0]) - parseFloat(data.bids[0][0]);
            document.getElementById('spread').textContent = spread.toFixed(2);
        }
    }
    
    async loadLatestTrades() {
        try {
            const response = await this.apiRequest(`/api/spot/trades?symbol=${this.currentSymbol}&limit=20`);
            if (response.code === 1) {
                this.renderLatestTrades(response.data);
            }
        } catch (error) {
            console.error('加载最新成交失败:', error);
        }
    }
    
    renderLatestTrades(trades) {
        const tradesElement = document.getElementById('latestTrades');
        tradesElement.innerHTML = '';
        
        trades.forEach(trade => {
            const row = document.createElement('tr');
            const side = parseFloat(trade.price) > parseFloat(trade.prev_price || trade.price) ? 'buy' : 'sell';
            row.className = `trade-${side}`;
            
            const time = new Date(trade.created_at).toLocaleTimeString();
            row.innerHTML = `
                <td>${time}</td>
                <td>${parseFloat(trade.price).toFixed(2)}</td>
                <td>${parseFloat(trade.amount).toFixed(6)}</td>
            `;
            tradesElement.appendChild(row);
        });
    }
    
    connectWebSocket() {
        const wsUrl = `ws://localhost:8080?symbol=${this.currentSymbol}`;
        this.ws = new WebSocket(wsUrl);
        
        this.ws.onopen = () => {
            console.log('WebSocket连接已建立');
            // 订阅行情数据
            this.ws.send(JSON.stringify({
                method: 'subscribe',
                params: [`${this.currentSymbol.toLowerCase()}@ticker`]
            }));
        };
        
        this.ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleWebSocketMessage(data);
            } catch (error) {
                console.error('WebSocket消息解析失败:', error);
            }
        };
        
        this.ws.onclose = () => {
            console.log('WebSocket连接已关闭');
            // 5秒后重连
            setTimeout(() => {
                this.connectWebSocket();
            }, 5000);
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket错误:', error);
        };
    }
    
    handleWebSocketMessage(data) {
        if (data.type === 'ticker') {
            this.updatePriceDisplay(data.data);
        } else if (data.type === 'orderbook') {
            this.renderOrderBook(data.data);
        } else if (data.type === 'trade') {
            this.addNewTrade(data.data);
        }
    }
    
    updatePriceDisplay(tickerData) {
        const currentPriceElement = document.getElementById('currentPrice');
        const priceChangeElement = document.getElementById('priceChange');
        
        currentPriceElement.textContent = `$${parseFloat(tickerData.price).toFixed(2)}`;
        
        const changePercent = parseFloat(tickerData.change_percent);
        priceChangeElement.textContent = `${changePercent >= 0 ? '+' : ''}${changePercent.toFixed(2)}%`;
        priceChangeElement.className = `price-change ${changePercent >= 0 ? 'positive' : 'negative'}`;
        
        // 更新当前价格到交易表单
        if (!document.getElementById('tradePrice').value) {
            document.getElementById('tradePrice').value = parseFloat(tickerData.price).toFixed(2);
            this.calculateTotal();
        }
    }
    
    addNewTrade(tradeData) {
        const tradesElement = document.getElementById('latestTrades');
        const row = document.createElement('tr');
        const side = tradeData.side || 'buy';
        row.className = `trade-${side}`;
        
        const time = new Date().toLocaleTimeString();
        row.innerHTML = `
            <td>${time}</td>
            <td>${parseFloat(tradeData.price).toFixed(2)}</td>
            <td>${parseFloat(tradeData.amount).toFixed(6)}</td>
        `;
        
        tradesElement.insertBefore(row, tradesElement.firstChild);
        
        // 保持最多20条记录
        while (tradesElement.children.length > 20) {
            tradesElement.removeChild(tradesElement.lastChild);
        }
    }
    
    calculateTotal() {
        const price = parseFloat(document.getElementById('tradePrice').value) || 0;
        const amount = parseFloat(document.getElementById('tradeAmount').value) || 0;
        const total = price * amount;
        
        document.getElementById('tradeTotal').value = total.toFixed(2);
    }
    
    updatePriceAndAmount() {
        // 设置默认价格为当前市价
        const currentPrice = document.getElementById('currentPrice').textContent.replace('$', '').replace(',', '');
        if (currentPrice && currentPrice !== '0') {
            document.getElementById('tradePrice').value = currentPrice;
        }
    }
    
    updateChartInterval(interval) {
        const chartFrame = document.getElementById('chartFrame');
        const currentSrc = chartFrame.src;
        const url = new URL(currentSrc);
        url.searchParams.set('interval', interval);
        chartFrame.src = url.toString();
    }
    
    async submitOrder() {
        const price = parseFloat(document.getElementById('tradePrice').value);
        const amount = parseFloat(document.getElementById('tradeAmount').value);
        
        if (!price || !amount) {
            alert('请输入价格和数量');
            return;
        }
        
        const orderData = {
            symbol: this.currentSymbol,
            side: this.currentSide,
            type: 'limit',
            price: price,
            amount: amount
        };
        
        try {
            const response = await this.apiRequest('/api/spot/order', {
                method: 'POST',
                body: JSON.stringify(orderData)
            });
            
            if (response.code === 1) {
                alert('订单提交成功');
                this.clearForm();
                this.loadUserBalance();
                this.loadOrderBook();
            } else {
                alert(response.msg || '订单提交失败');
            }
        } catch (error) {
            console.error('提交订单失败:', error);
            alert('网络错误，请稍后重试');
        }
    }
    
    clearForm() {
        document.getElementById('tradeAmount').value = '';
        document.getElementById('tradeTotal').value = '';
    }
    
    async apiRequest(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Authorization': `Bearer ${this.token}`,
                'Content-Type': 'application/json'
            }
        };
        
        const response = await fetch(url, { ...defaultOptions, ...options });
        const result = await response.json();
        
        if (response.status === 401) {
            localStorage.removeItem('token');
            localStorage.removeItem('user_info');
            window.location.href = '/auth/login.html';
            return;
        }
        
        return result;
    }
}

// 全局函数
function switchTab(side) {
    const buyTab = document.getElementById('buyTab');
    const sellTab = document.getElementById('sellTab');
    const tradeBtn = document.getElementById('tradeBtn');
    
    if (side === 'buy') {
        buyTab.classList.add('active');
        sellTab.classList.remove('active');
        tradeBtn.className = 'trade-btn buy';
        tradeBtn.textContent = '买入 BTC';
        tradingPage.currentSide = 'buy';
    } else {
        sellTab.classList.add('active');
        buyTab.classList.remove('active');
        tradeBtn.className = 'trade-btn sell';
        tradeBtn.textContent = '卖出 BTC';
        tradingPage.currentSide = 'sell';
    }
}

function setPrice(price) {
    document.getElementById('tradePrice').value = price;
    tradingPage.calculateTotal();
}

function setPercentage(percentage) {
    // 根据当前余额计算数量
    const balance = parseFloat(document.getElementById('usdtBalance').textContent) || 0;
    const price = parseFloat(document.getElementById('tradePrice').value) || 0;
    
    if (price > 0) {
        const maxAmount = balance / price;
        const amount = (maxAmount * percentage / 100).toFixed(6);
        document.getElementById('tradeAmount').value = amount;
        tradingPage.calculateTotal();
    }
}

function showDepositModal() {
    window.location.href = '/user/deposit.html';
}

function showWithdrawModal() {
    window.location.href = '/user/withdraw.html';
}

function logout() {
    if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('token');
        localStorage.removeItem('user_info');
        window.location.href = '/auth/login.html';
    }
}

// 页面加载完成后初始化
let tradingPage;
document.addEventListener('DOMContentLoaded', () => {
    tradingPage = new TradingPage();
});
