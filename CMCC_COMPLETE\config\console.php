<?php
// +----------------------------------------------------------------------
// | 控制台配置
// +----------------------------------------------------------------------

return [
    // 指令定义
    'commands' => [
        // 交易相关命令
        'trading:settle'        => app\command\TradingSettle::class,
        'trading:price-update'  => app\command\PriceUpdate::class,
        'trading:risk-control'  => app\command\RiskControl::class,
        
        // 用户相关命令
        'user:cleanup'          => app\command\UserCleanup::class,
        'user:statistics'       => app\command\UserStatistics::class,
        
        // 系统相关命令
        'system:backup'         => app\command\SystemBackup::class,
        'system:optimize'       => app\command\SystemOptimize::class,

        // 客服相关命令
        'websocket:start'       => app\command\WebSocketServer::class,
        'customer:cleanup'      => app\command\CustomerCleanup::class,
        'system:health-check'   => app\command\HealthCheck::class,
        
        // 队列相关命令
        'queue:work'            => app\command\QueueWork::class,
        'queue:restart'         => app\command\QueueRestart::class,
        
        // 数据相关命令
        'data:migrate'          => app\command\DataMigrate::class,
        'data:seed'             => app\command\DataSeed::class,
        
        // WebSocket相关命令
        'websocket:start'       => app\command\WebSocketStart::class,
        'websocket:stop'        => app\command\WebSocketStop::class,
        'websocket:restart'     => app\command\WebSocketRestart::class,
    ],
];
