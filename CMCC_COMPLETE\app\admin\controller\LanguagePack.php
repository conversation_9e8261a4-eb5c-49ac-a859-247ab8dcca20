<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\common\service\LanguagePackService;
use think\response\Json;

/**
 * 语言包管理控制器
 */
class LanguagePack extends BaseController
{
    private $languagePackService;

    public function __construct()
    {
        parent::__construct();
        $this->languagePackService = new LanguagePackService();
    }

    /**
     * 获取语言包列表
     */
    public function index(): Json
    {
        $result = $this->languagePackService->getAllLanguagePacks();
        return json($result);
    }

    /**
     * 获取语言包详情
     */
    public function detail(): Json
    {
        $languageCode = $this->request->param('language_code', '');
        
        if (empty($languageCode)) {
            return json(['code' => 0, 'msg' => '语言代码不能为空']);
        }

        $result = $this->languagePackService->getLanguagePackDetail($languageCode);
        return json($result);
    }

    /**
     * 更新翻译
     */
    public function updateTranslation(): Json
    {
        $data = $this->request->post();
        $data['user_id'] = $this->userId;

        // 验证必要字段
        $requiredFields = ['language_code', 'key_path', 'value'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                return json(['code' => 0, 'msg' => "字段 {$field} 不能为空"]);
            }
        }

        $result = $this->languagePackService->updateTranslation($data);
        return json($result);
    }

    /**
     * 批量导入翻译
     */
    public function importTranslations(): Json
    {
        $languageCode = $this->request->post('language_code', '');
        $file = $this->request->file('file');

        if (empty($languageCode)) {
            return json(['code' => 0, 'msg' => '语言代码不能为空']);
        }

        if (!$file) {
            return json(['code' => 0, 'msg' => '请选择要导入的文件']);
        }

        try {
            $fileContent = file_get_contents($file->getPathname());
            $fileExtension = $file->getOriginalExtension();

            // 根据文件类型解析内容
            switch (strtolower($fileExtension)) {
                case 'json':
                    $translations = json_decode($fileContent, true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        return json(['code' => 0, 'msg' => 'JSON文件格式错误']);
                    }
                    $translations = $this->flattenArray($translations);
                    break;

                case 'csv':
                    $translations = $this->parseCsvFile($fileContent);
                    break;

                case 'php':
                    // 安全地解析PHP文件
                    $translations = $this->parsePhpFile($fileContent);
                    break;

                default:
                    return json(['code' => 0, 'msg' => '不支持的文件格式']);
            }

            if (empty($translations)) {
                return json(['code' => 0, 'msg' => '文件中没有找到有效的翻译内容']);
            }

            $result = $this->languagePackService->importTranslations($languageCode, $translations, $this->userId);
            return json($result);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '导入失败：' . $e->getMessage()]);
        }
    }

    /**
     * 导出语言包
     */
    public function exportLanguagePack(): Json
    {
        $languageCode = $this->request->param('language_code', '');
        $format = $this->request->param('format', 'php');

        if (empty($languageCode)) {
            return json(['code' => 0, 'msg' => '语言代码不能为空']);
        }

        $result = $this->languagePackService->exportLanguagePack($languageCode, $format);
        
        if ($result['code'] === 1) {
            // 设置下载头
            $filename = $result['data']['filename'];
            $content = $result['data']['content'];

            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Content-Length: ' . strlen($content));
            
            echo $content;
            exit;
        }

        return json($result);
    }

    /**
     * 审核翻译
     */
    public function reviewTranslation(): Json
    {
        $translationId = $this->request->post('translation_id', 0);
        $action = $this->request->post('action', ''); // approve 或 reject
        $comment = $this->request->post('comment', '');

        if (empty($translationId) || empty($action)) {
            return json(['code' => 0, 'msg' => '参数不完整']);
        }

        if (!in_array($action, ['approve', 'reject'])) {
            return json(['code' => 0, 'msg' => '无效的审核操作']);
        }

        $result = $this->languagePackService->reviewTranslation($translationId, $action, $this->userId, $comment);
        return json($result);
    }

    /**
     * 搜索翻译
     */
    public function searchTranslations(): Json
    {
        $params = $this->request->param();
        $result = $this->languagePackService->searchTranslations($params);
        return json($result);
    }

    /**
     * 获取翻译历史
     */
    public function getTranslationHistory(): Json
    {
        $translationId = $this->request->param('translation_id', 0);

        if (empty($translationId)) {
            return json(['code' => 0, 'msg' => '翻译ID不能为空']);
        }

        $result = $this->languagePackService->getTranslationHistory($translationId);
        return json($result);
    }

    /**
     * 获取翻译统计
     */
    public function getTranslationStats(): Json
    {
        $languageCode = $this->request->param('language_code', '');

        if (empty($languageCode)) {
            return json(['code' => 0, 'msg' => '语言代码不能为空']);
        }

        // 获取基础统计
        $stats = $this->languagePackService->getTranslationStats($languageCode);

        // 获取质量分布
        $qualityStats = $this->getQualityStats($languageCode);

        // 获取翻译者排行
        $translatorStats = $this->getTranslatorStats($languageCode);

        return json([
            'code' => 1,
            'msg' => '获取成功',
            'data' => [
                'basic_stats' => $stats,
                'quality_stats' => $qualityStats,
                'translator_stats' => $translatorStats
            ]
        ]);
    }

    /**
     * 批量审核翻译
     */
    public function batchReviewTranslations(): Json
    {
        $translationIds = $this->request->post('translation_ids', []);
        $action = $this->request->post('action', '');
        $comment = $this->request->post('comment', '');

        if (empty($translationIds) || empty($action)) {
            return json(['code' => 0, 'msg' => '参数不完整']);
        }

        if (!in_array($action, ['approve', 'reject'])) {
            return json(['code' => 0, 'msg' => '无效的审核操作']);
        }

        $successCount = 0;
        $failCount = 0;
        $errors = [];

        foreach ($translationIds as $translationId) {
            $result = $this->languagePackService->reviewTranslation($translationId, $action, $this->userId, $comment);
            
            if ($result['code'] === 1) {
                $successCount++;
            } else {
                $failCount++;
                $errors[] = "ID {$translationId}: " . $result['msg'];
            }
        }

        return json([
            'code' => 1,
            'msg' => '批量审核完成',
            'data' => [
                'success_count' => $successCount,
                'fail_count' => $failCount,
                'errors' => $errors
            ]
        ]);
    }

    /**
     * 扁平化数组
     */
    private function flattenArray(array $array, string $prefix = ''): array
    {
        $result = [];

        foreach ($array as $key => $value) {
            $newKey = $prefix ? $prefix . '.' . $key : $key;

            if (is_array($value)) {
                $result = array_merge($result, $this->flattenArray($value, $newKey));
            } else {
                $result[$newKey] = $value;
            }
        }

        return $result;
    }

    /**
     * 解析CSV文件
     */
    private function parseCsvFile(string $content): array
    {
        $lines = explode("\n", $content);
        $translations = [];

        // 跳过标题行
        for ($i = 1; $i < count($lines); $i++) {
            $line = trim($lines[$i]);
            if (empty($line)) continue;

            $data = str_getcsv($line);
            if (count($data) >= 2) {
                $translations[$data[0]] = $data[1];
            }
        }

        return $translations;
    }

    /**
     * 解析PHP文件
     */
    private function parsePhpFile(string $content): array
    {
        // 移除PHP标签
        $content = preg_replace('/^<\?php\s*/', '', $content);
        $content = preg_replace('/\s*\?>$/', '', $content);

        // 安全地评估数组
        if (preg_match('/return\s+(.+);/s', $content, $matches)) {
            try {
                $array = eval('return ' . $matches[1] . ';');
                if (is_array($array)) {
                    return $this->flattenArray($array);
                }
            } catch (\Exception $e) {
                throw new \Exception('PHP文件格式错误');
            }
        }

        throw new \Exception('无法解析PHP文件');
    }

    /**
     * 获取质量统计
     */
    private function getQualityStats(string $languageCode): array
    {
        $stats = \think\facade\Db::name('language_translations')
            ->where('language_code', $languageCode)
            ->field('
                COUNT(*) as total,
                AVG(quality_score) as avg_score,
                COUNT(CASE WHEN quality_score >= 90 THEN 1 END) as excellent,
                COUNT(CASE WHEN quality_score >= 70 AND quality_score < 90 THEN 1 END) as good,
                COUNT(CASE WHEN quality_score >= 50 AND quality_score < 70 THEN 1 END) as fair,
                COUNT(CASE WHEN quality_score < 50 THEN 1 END) as poor
            ')
            ->find();

        return $stats ?: [];
    }

    /**
     * 获取翻译者统计
     */
    private function getTranslatorStats(string $languageCode): array
    {
        $stats = \think\facade\Db::name('language_translations')
            ->alias('lt')
            ->join('users u', 'lt.translator_id = u.id')
            ->where('lt.language_code', $languageCode)
            ->field('u.username, COUNT(*) as translation_count, AVG(lt.quality_score) as avg_quality')
            ->group('lt.translator_id')
            ->order('translation_count desc')
            ->limit(10)
            ->select()
            ->toArray();

        return $stats;
    }
}
