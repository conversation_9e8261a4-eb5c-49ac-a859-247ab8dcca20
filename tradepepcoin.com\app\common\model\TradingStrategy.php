<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 交易策略模型
 */
class TradingStrategy extends Model
{
    protected $table = 'trading_strategies';

    // 设置字段信息
    protected $schema = [
        'id' => 'int',
        'user_id' => 'int',
        'strategy_id' => 'string',
        'name' => 'string',
        'description' => 'text',
        'type' => 'string',
        'symbols' => 'json',
        'parameters' => 'json',
        'risk_level' => 'int',
        'min_investment' => 'decimal',
        'max_investment' => 'decimal',
        'performance_data' => 'json',
        'total_return' => 'decimal',
        'max_drawdown' => 'decimal',
        'sharpe_ratio' => 'decimal',
        'win_rate' => 'decimal',
        'followers_count' => 'int',
        'total_copied_amount' => 'decimal',
        'status' => 'int',
        'is_public' => 'int',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // JSON字段
    protected $json = ['symbols', 'parameters', 'performance_data'];

    // 策略类型
    const TYPE_MANUAL = 'manual';           // 手动策略
    const TYPE_GRID = 'grid';               // 网格策略
    const TYPE_DCA = 'dca';                 // 定投策略
    const TYPE_MOMENTUM = 'momentum';       // 动量策略
    const TYPE_ARBITRAGE = 'arbitrage';     // 套利策略

    // 风险等级
    const RISK_LOW = 1;      // 低风险
    const RISK_MEDIUM = 2;   // 中风险
    const RISK_HIGH = 3;     // 高风险

    // 策略状态
    const STATUS_DRAFT = 0;      // 草稿
    const STATUS_ACTIVE = 1;     // 活跃
    const STATUS_PAUSED = 2;     // 暂停
    const STATUS_STOPPED = 3;    // 停止
    const STATUS_ARCHIVED = 4;   // 归档

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联跟单记录
     */
    public function copyTrades()
    {
        return $this->hasMany(CopyTrade::class, 'strategy_id', 'strategy_id');
    }

    /**
     * 关联策略订单
     */
    public function strategyOrders()
    {
        return $this->hasMany(StrategyOrder::class, 'strategy_id', 'strategy_id');
    }

    /**
     * 创建交易策略
     */
    public static function createStrategy(array $data): array
    {
        try {
            $strategy = new self();
            $strategy->user_id = $data['user_id'];
            $strategy->strategy_id = self::generateStrategyId();
            $strategy->name = $data['name'];
            $strategy->description = $data['description'] ?? '';
            $strategy->type = $data['type'];
            $strategy->symbols = $data['symbols'] ?? [];
            $strategy->parameters = $data['parameters'] ?? [];
            $strategy->risk_level = $data['risk_level'];
            $strategy->min_investment = $data['min_investment'] ?? 0;
            $strategy->max_investment = $data['max_investment'] ?? 0;
            $strategy->is_public = $data['is_public'] ?? 0;
            $strategy->status = self::STATUS_DRAFT;

            if ($strategy->save()) {
                return ['code' => 1, 'msg' => '策略创建成功', 'data' => $strategy];
            } else {
                return ['code' => 0, 'msg' => '策略创建失败'];
            }
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '创建失败：' . $e->getMessage()];
        }
    }

    /**
     * 发布策略
     */
    public function publish(): bool
    {
        if ($this->status !== self::STATUS_DRAFT) {
            return false;
        }

        $this->status = self::STATUS_ACTIVE;
        $this->is_public = 1;

        return $this->save();
    }

    /**
     * 暂停策略
     */
    public function pause(): bool
    {
        if ($this->status !== self::STATUS_ACTIVE) {
            return false;
        }

        $this->status = self::STATUS_PAUSED;
        return $this->save();
    }

    /**
     * 恢复策略
     */
    public function resume(): bool
    {
        if ($this->status !== self::STATUS_PAUSED) {
            return false;
        }

        $this->status = self::STATUS_ACTIVE;
        return $this->save();
    }

    /**
     * 停止策略
     */
    public function stop(): bool
    {
        if (!in_array($this->status, [self::STATUS_ACTIVE, self::STATUS_PAUSED])) {
            return false;
        }

        $this->status = self::STATUS_STOPPED;
        return $this->save();
    }

    /**
     * 更新策略表现
     */
    public function updatePerformance(array $data): bool
    {
        $this->total_return = $data['total_return'] ?? $this->total_return;
        $this->max_drawdown = $data['max_drawdown'] ?? $this->max_drawdown;
        $this->sharpe_ratio = $data['sharpe_ratio'] ?? $this->sharpe_ratio;
        $this->win_rate = $data['win_rate'] ?? $this->win_rate;

        // 更新表现数据
        $performanceData = $this->performance_data ?: [];
        $performanceData[] = [
            'date' => date('Y-m-d'),
            'return' => $data['daily_return'] ?? 0,
            'equity' => $data['equity'] ?? 0,
            'trades' => $data['trades'] ?? 0
        ];

        // 保留最近365天的数据
        if (count($performanceData) > 365) {
            $performanceData = array_slice($performanceData, -365);
        }

        $this->performance_data = $performanceData;

        return $this->save();
    }

    /**
     * 增加跟随者
     */
    public function addFollower(): bool
    {
        $this->followers_count++;
        return $this->save();
    }

    /**
     * 减少跟随者
     */
    public function removeFollower(): bool
    {
        if ($this->followers_count > 0) {
            $this->followers_count--;
            return $this->save();
        }
        return true;
    }

    /**
     * 增加跟单金额
     */
    public function addCopiedAmount(float $amount): bool
    {
        $this->total_copied_amount += $amount;
        return $this->save();
    }

    /**
     * 减少跟单金额
     */
    public function removeCopiedAmount(float $amount): bool
    {
        $this->total_copied_amount = max(0, $this->total_copied_amount - $amount);
        return $this->save();
    }

    /**
     * 获取策略排行榜
     */
    public static function getLeaderboard(string $period = '7d', int $limit = 20): array
    {
        $orderField = 'total_return';
        
        switch ($period) {
            case '1d':
                $orderField = 'daily_return';
                break;
            case '7d':
                $orderField = 'weekly_return';
                break;
            case '30d':
                $orderField = 'monthly_return';
                break;
            case 'all':
                $orderField = 'total_return';
                break;
        }

        return self::where('status', self::STATUS_ACTIVE)
                  ->where('is_public', 1)
                  ->with(['user'])
                  ->order($orderField, 'desc')
                  ->limit($limit)
                  ->select()
                  ->toArray();
    }

    /**
     * 搜索策略
     */
    public static function searchStrategies(array $filters = [], int $page = 1, int $limit = 20): array
    {
        $query = self::where('status', self::STATUS_ACTIVE)
                    ->where('is_public', 1)
                    ->with(['user']);

        // 策略类型筛选
        if (!empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        // 风险等级筛选
        if (!empty($filters['risk_level'])) {
            $query->where('risk_level', $filters['risk_level']);
        }

        // 收益率筛选
        if (!empty($filters['min_return'])) {
            $query->where('total_return', '>=', $filters['min_return']);
        }

        if (!empty($filters['max_return'])) {
            $query->where('total_return', '<=', $filters['max_return']);
        }

        // 最大回撤筛选
        if (!empty($filters['max_drawdown'])) {
            $query->where('max_drawdown', '<=', $filters['max_drawdown']);
        }

        // 跟随者数量筛选
        if (!empty($filters['min_followers'])) {
            $query->where('followers_count', '>=', $filters['min_followers']);
        }

        // 交易对筛选
        if (!empty($filters['symbol'])) {
            $query->whereRaw("JSON_CONTAINS(symbols, '\"" . $filters['symbol'] . "\"')");
        }

        // 排序
        $orderBy = $filters['order_by'] ?? 'total_return';
        $orderDirection = $filters['order_direction'] ?? 'desc';
        $query->order($orderBy, $orderDirection);

        $strategies = $query->paginate([
            'list_rows' => $limit,
            'page' => $page
        ]);

        return [
            'data' => $strategies->items(),
            'total' => $strategies->total(),
            'page' => $page,
            'limit' => $limit
        ];
    }

    /**
     * 生成策略ID
     */
    private static function generateStrategyId(): string
    {
        return 'STR' . date('YmdHis') . mt_rand(1000, 9999);
    }

    /**
     * 获取风险等级文本
     */
    public function getRiskLevelTextAttr(): string
    {
        $riskTexts = [
            self::RISK_LOW => '低风险',
            self::RISK_MEDIUM => '中风险',
            self::RISK_HIGH => '高风险'
        ];

        return $riskTexts[$this->risk_level] ?? '未知';
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr(): string
    {
        $statusTexts = [
            self::STATUS_DRAFT => '草稿',
            self::STATUS_ACTIVE => '活跃',
            self::STATUS_PAUSED => '暂停',
            self::STATUS_STOPPED => '停止',
            self::STATUS_ARCHIVED => '归档'
        ];

        return $statusTexts[$this->status] ?? '未知';
    }

    /**
     * 获取类型文本
     */
    public function getTypeTextAttr(): string
    {
        $typeTexts = [
            self::TYPE_MANUAL => '手动策略',
            self::TYPE_GRID => '网格策略',
            self::TYPE_DCA => '定投策略',
            self::TYPE_MOMENTUM => '动量策略',
            self::TYPE_ARBITRAGE => '套利策略'
        ];

        return $typeTexts[$this->type] ?? '未知类型';
    }

    /**
     * 获取年化收益率
     */
    public function getAnnualizedReturnAttr(): float
    {
        if (empty($this->performance_data)) {
            return 0;
        }

        $firstRecord = reset($this->performance_data);
        $lastRecord = end($this->performance_data);
        
        if (!$firstRecord || !$lastRecord) {
            return 0;
        }

        $startDate = new \DateTime($firstRecord['date']);
        $endDate = new \DateTime($lastRecord['date']);
        $daysDiff = $endDate->diff($startDate)->days;
        
        if ($daysDiff <= 0) {
            return 0;
        }

        $totalReturn = $this->total_return / 100; // 转换为小数
        $annualizedReturn = (pow(1 + $totalReturn, 365 / $daysDiff) - 1) * 100;

        return round($annualizedReturn, 2);
    }

    /**
     * 获取最近表现
     */
    public function getRecentPerformance(int $days = 30): array
    {
        if (empty($this->performance_data)) {
            return [];
        }

        $recentData = array_slice($this->performance_data, -$days);
        
        return array_map(function($item) {
            return [
                'date' => $item['date'],
                'return' => $item['return'],
                'equity' => $item['equity']
            ];
        }, $recentData);
    }

    /**
     * 计算夏普比率
     */
    public function calculateSharpeRatio(): float
    {
        if (empty($this->performance_data) || count($this->performance_data) < 30) {
            return 0;
        }

        $returns = array_column($this->performance_data, 'return');
        $avgReturn = array_sum($returns) / count($returns);
        
        // 计算标准差
        $variance = 0;
        foreach ($returns as $return) {
            $variance += pow($return - $avgReturn, 2);
        }
        $stdDev = sqrt($variance / count($returns));
        
        if ($stdDev == 0) {
            return 0;
        }

        // 假设无风险利率为年化3%，日化约0.008%
        $riskFreeRate = 0.008;
        $sharpeRatio = ($avgReturn - $riskFreeRate) / $stdDev;

        return round($sharpeRatio, 4);
    }

    /**
     * 搜索器：按用户
     */
    public function searchUserIdAttr($query, $value)
    {
        if ($value) {
            $query->where('user_id', $value);
        }
    }

    /**
     * 搜索器：按类型
     */
    public function searchTypeAttr($query, $value)
    {
        if ($value) {
            $query->where('type', $value);
        }
    }

    /**
     * 搜索器：按状态
     */
    public function searchStatusAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('status', $value);
        }
    }

    /**
     * 搜索器：按风险等级
     */
    public function searchRiskLevelAttr($query, $value)
    {
        if ($value) {
            $query->where('risk_level', $value);
        }
    }

    /**
     * 搜索器：按关键词
     */
    public function searchKeywordAttr($query, $value)
    {
        if ($value) {
            $query->where('name|description', 'like', "%{$value}%");
        }
    }
}
