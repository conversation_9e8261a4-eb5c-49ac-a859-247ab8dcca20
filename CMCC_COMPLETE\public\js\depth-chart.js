/**
 * 深度图表组件
 * 基于Chart.js实现的实时深度图表
 */

class DepthChart {
    constructor(containerId, options = {}) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.chart = null;
        this.ws = null;
        this.symbol = options.symbol || 'BTCUSDT';
        this.maxDepth = options.maxDepth || 50;
        this.theme = options.theme || 'dark';
        
        this.options = {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: 0 // 禁用动画以提高性能
            },
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        color: this.theme === 'dark' ? '#ffffff' : '#333333',
                        usePointStyle: true
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: this.theme === 'dark' ? 'rgba(0,0,0,0.8)' : 'rgba(255,255,255,0.8)',
                    titleColor: this.theme === 'dark' ? '#ffffff' : '#333333',
                    bodyColor: this.theme === 'dark' ? '#ffffff' : '#333333',
                    borderColor: this.theme === 'dark' ? '#444444' : '#cccccc',
                    borderWidth: 1,
                    callbacks: {
                        title: function(context) {
                            return `价格: ${context[0].label}`;
                        },
                        label: function(context) {
                            const label = context.dataset.label || '';
                            const value = parseFloat(context.parsed.y).toFixed(4);
                            return `${label}: ${value}`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    type: 'linear',
                    position: 'bottom',
                    title: {
                        display: true,
                        text: '价格',
                        color: this.theme === 'dark' ? '#ffffff' : '#333333'
                    },
                    grid: {
                        color: this.theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        color: this.theme === 'dark' ? '#ffffff' : '#333333',
                        callback: function(value) {
                            return parseFloat(value).toFixed(2);
                        }
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: '累计数量',
                        color: this.theme === 'dark' ? '#ffffff' : '#333333'
                    },
                    grid: {
                        color: this.theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        color: this.theme === 'dark' ? '#ffffff' : '#333333',
                        callback: function(value) {
                            return parseFloat(value).toFixed(4);
                        }
                    }
                }
            }
        };

        this.init();
    }

    /**
     * 初始化图表
     */
    init() {
        this.createChart();
        this.connectWebSocket();
        this.bindEvents();
    }

    /**
     * 创建图表
     */
    createChart() {
        const canvas = document.createElement('canvas');
        canvas.id = `${this.containerId}-canvas`;
        this.container.appendChild(canvas);

        const ctx = canvas.getContext('2d');
        
        this.chart = new Chart(ctx, {
            type: 'line',
            data: {
                datasets: [
                    {
                        label: '买单深度',
                        data: [],
                        borderColor: '#00C087',
                        backgroundColor: 'rgba(0, 192, 135, 0.1)',
                        fill: true,
                        tension: 0.1,
                        pointRadius: 0,
                        pointHoverRadius: 4,
                        borderWidth: 2
                    },
                    {
                        label: '卖单深度',
                        data: [],
                        borderColor: '#F84960',
                        backgroundColor: 'rgba(248, 73, 96, 0.1)',
                        fill: true,
                        tension: 0.1,
                        pointRadius: 0,
                        pointHoverRadius: 4,
                        borderWidth: 2
                    }
                ]
            },
            options: this.options
        });
    }

    /**
     * 连接WebSocket
     */
    connectWebSocket() {
        const wsUrl = `ws://${window.location.hostname}:8080`;
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
            console.log('深度图表WebSocket连接已建立');
            
            // 订阅订单簿数据
            this.subscribe();
        };

        this.ws.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                this.handleMessage(message);
            } catch (error) {
                console.error('解析WebSocket消息失败:', error);
            }
        };

        this.ws.onclose = () => {
            console.log('深度图表WebSocket连接已关闭');
            // 5秒后重连
            setTimeout(() => {
                this.connectWebSocket();
            }, 5000);
        };

        this.ws.onerror = (error) => {
            console.error('深度图表WebSocket错误:', error);
        };
    }

    /**
     * 订阅数据
     */
    subscribe() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({
                type: 'subscribe',
                channel: 'orderbook',
                symbol: this.symbol
            }));
        }
    }

    /**
     * 处理WebSocket消息
     */
    handleMessage(message) {
        switch (message.type) {
            case 'orderbook_snapshot':
            case 'orderbook_update':
                this.updateDepthChart(message.data);
                break;
            case 'subscribed':
                console.log('订阅成功:', message.data);
                break;
            case 'error':
                console.error('WebSocket错误:', message.data.message);
                break;
        }
    }

    /**
     * 更新深度图表
     */
    updateDepthChart(data) {
        if (!data.bids || !data.asks) {
            return;
        }

        // 处理买单数据（绿色，从高到低）
        const bidsData = this.processDepthData(data.bids, 'bids');
        
        // 处理卖单数据（红色，从低到高）
        const asksData = this.processDepthData(data.asks, 'asks');

        // 更新图表数据
        this.chart.data.datasets[0].data = bidsData;
        this.chart.data.datasets[1].data = asksData;

        // 更新图表
        this.chart.update('none'); // 使用'none'模式以提高性能
    }

    /**
     * 处理深度数据
     */
    processDepthData(orders, type) {
        if (!orders || orders.length === 0) {
            return [];
        }

        // 限制深度
        const limitedOrders = orders.slice(0, this.maxDepth);
        
        let cumulativeQuantity = 0;
        const depthData = [];

        if (type === 'bids') {
            // 买单：从高价到低价，累计数量
            for (let i = 0; i < limitedOrders.length; i++) {
                const [price, quantity] = limitedOrders[i];
                cumulativeQuantity += parseFloat(quantity);
                depthData.push({
                    x: parseFloat(price),
                    y: cumulativeQuantity
                });
            }
        } else {
            // 卖单：从低价到高价，累计数量
            for (let i = 0; i < limitedOrders.length; i++) {
                const [price, quantity] = limitedOrders[i];
                cumulativeQuantity += parseFloat(quantity);
                depthData.push({
                    x: parseFloat(price),
                    y: cumulativeQuantity
                });
            }
        }

        return depthData;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 窗口大小改变时重新调整图表
        window.addEventListener('resize', () => {
            if (this.chart) {
                this.chart.resize();
            }
        });

        // 主题切换
        document.addEventListener('themeChanged', (event) => {
            this.changeTheme(event.detail.theme);
        });
    }

    /**
     * 切换主题
     */
    changeTheme(theme) {
        this.theme = theme;
        
        const textColor = theme === 'dark' ? '#ffffff' : '#333333';
        const gridColor = theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)';

        // 更新图表配置
        this.chart.options.plugins.legend.labels.color = textColor;
        this.chart.options.plugins.tooltip.backgroundColor = theme === 'dark' ? 'rgba(0,0,0,0.8)' : 'rgba(255,255,255,0.8)';
        this.chart.options.plugins.tooltip.titleColor = textColor;
        this.chart.options.plugins.tooltip.bodyColor = textColor;
        this.chart.options.plugins.tooltip.borderColor = theme === 'dark' ? '#444444' : '#cccccc';
        
        this.chart.options.scales.x.title.color = textColor;
        this.chart.options.scales.x.grid.color = gridColor;
        this.chart.options.scales.x.ticks.color = textColor;
        
        this.chart.options.scales.y.title.color = textColor;
        this.chart.options.scales.y.grid.color = gridColor;
        this.chart.options.scales.y.ticks.color = textColor;

        this.chart.update();
    }

    /**
     * 切换交易对
     */
    changeSymbol(symbol) {
        if (this.symbol === symbol) {
            return;
        }

        // 取消当前订阅
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({
                type: 'unsubscribe',
                channel: 'orderbook',
                symbol: this.symbol
            }));
        }

        this.symbol = symbol;
        
        // 清空图表数据
        this.chart.data.datasets[0].data = [];
        this.chart.data.datasets[1].data = [];
        this.chart.update();

        // 订阅新交易对
        this.subscribe();
    }

    /**
     * 设置最大深度
     */
    setMaxDepth(depth) {
        this.maxDepth = depth;
    }

    /**
     * 获取图表实例
     */
    getChart() {
        return this.chart;
    }

    /**
     * 销毁图表
     */
    destroy() {
        if (this.ws) {
            this.ws.close();
        }
        
        if (this.chart) {
            this.chart.destroy();
        }
        
        // 清空容器
        this.container.innerHTML = '';
    }

    /**
     * 暂停更新
     */
    pause() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({
                type: 'unsubscribe',
                channel: 'orderbook',
                symbol: this.symbol
            }));
        }
    }

    /**
     * 恢复更新
     */
    resume() {
        this.subscribe();
    }

    /**
     * 导出图表为图片
     */
    exportAsImage(filename = 'depth-chart.png') {
        if (this.chart) {
            const canvas = this.chart.canvas;
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
    }

    /**
     * 获取当前深度数据
     */
    getCurrentDepthData() {
        if (!this.chart) {
            return null;
        }

        return {
            bids: this.chart.data.datasets[0].data,
            asks: this.chart.data.datasets[1].data,
            symbol: this.symbol,
            timestamp: Date.now()
        };
    }

    /**
     * 设置图表高度
     */
    setHeight(height) {
        this.container.style.height = height + 'px';
        if (this.chart) {
            this.chart.resize();
        }
    }

    /**
     * 显示/隐藏图例
     */
    toggleLegend(show = true) {
        if (this.chart) {
            this.chart.options.plugins.legend.display = show;
            this.chart.update();
        }
    }

    /**
     * 设置精度
     */
    setPrecision(pricePrecision = 2, quantityPrecision = 4) {
        this.options.scales.x.ticks.callback = function(value) {
            return parseFloat(value).toFixed(pricePrecision);
        };
        
        this.options.scales.y.ticks.callback = function(value) {
            return parseFloat(value).toFixed(quantityPrecision);
        };

        if (this.chart) {
            this.chart.options = this.options;
            this.chart.update();
        }
    }
}

// 全局导出
window.DepthChart = DepthChart;
