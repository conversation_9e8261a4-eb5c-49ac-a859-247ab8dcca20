<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\BaseController;
use app\common\model\User as UserModel;
use app\common\service\UserService;
use app\common\service\VerificationService;
use think\facade\View;
use think\facade\Session;
use think\facade\Request;
use think\facade\Validate;

/**
 * 管理端用户控制器
 */
class User extends BaseController
{
    protected $userService;
    protected $verificationService;

    public function initialize()
    {
        parent::initialize();
        $this->userService = new UserService();
        $this->verificationService = new VerificationService();
        
        // 检查管理员登录状态
        if (!Session::has('admin_id')) {
            $this->redirect('/admin/login');
        }
    }

    /**
     * 用户列表
     */
    public function index()
    {
        $userType = Request::get('user_type', ''); // 用户类型筛选
        $keyword = Request::get('keyword', '');
        $page = Request::get('page', 1);
        
        $users = UserModel::when($userType, function($query, $userType) {
                    return $query->where('user_type', $userType);
                })
                ->when($keyword, function($query, $keyword) {
                    return $query->where('username|email', 'like', '%' . $keyword . '%');
                })
                ->order('created_at desc')
                ->paginate([
                    'list_rows' => 20,
                    'page' => $page
                ]);
        
        // 统计数据（只统计正式用户）
        $stats = [
            'total_formal' => UserModel::where('user_type', UserModel::USER_TYPE_FORMAL)->count(),
            'total_test' => UserModel::where('user_type', UserModel::USER_TYPE_TEST)->count(),
            'today_register' => UserModel::where('user_type', UserModel::USER_TYPE_FORMAL)
                                        ->whereTime('created_at', 'today')
                                        ->count()
        ];
        
        View::assign([
            'users' => $users,
            'stats' => $stats,
            'user_type' => $userType,
            'keyword' => $keyword,
            'title' => '用户管理 - GVD管理后台'
        ]);
        
        return View::fetch('admin/user/index');
    }

    /**
     * 添加测试用户
     */
    public function add()
    {
        if (Request::isPost()) {
            return $this->doAdd();
        }
        
        View::assign([
            'title' => '添加测试用户 - GVD管理后台'
        ]);
        
        return View::fetch('admin/user/add');
    }

    /**
     * 执行添加用户
     */
    private function doAdd()
    {
        $data = Request::post();
        
        // 验证数据
        $validate = Validate::rule([
            'username' => 'require|alphaDash|length:3,20|unique:ce_users',
            'email' => 'require|email|unique:ce_users',
            'password' => 'require|min:6'
        ])->message([
            'username.require' => '请输入用户名',
            'username.unique' => '用户名已存在',
            'email.require' => '请输入邮箱',
            'email.unique' => '邮箱已被注册',
            'password.require' => '请输入密码'
        ]);

        if (!$validate->check($data)) {
            return json(['code' => 0, 'msg' => $validate->getError()]);
        }
        
        // 创建测试用户
        $adminId = Session::get('admin_id');
        $result = $this->userService->createTestUser($data, $adminId);
        
        return json($result);
    }

    /**
     * 编辑用户
     */
    public function edit()
    {
        $id = Request::get('id');
        
        if (Request::isPost()) {
            return $this->doEdit($id);
        }
        
        $user = UserModel::find($id);
        if (!$user) {
            $this->error('用户不存在');
        }
        
        View::assign([
            'user' => $user,
            'title' => '编辑用户 - GVD管理后台'
        ]);
        
        return View::fetch('admin/user/edit');
    }

    /**
     * 执行编辑用户
     */
    private function doEdit($id)
    {
        $data = Request::post();
        $user = UserModel::find($id);
        
        if (!$user) {
            return json(['code' => 0, 'msg' => '用户不存在']);
        }
        
        // 更新用户信息
        $user->user_type = $data['user_type'] ?? $user->user_type;
        $user->status = $data['status'] ?? $user->status;
        $user->level = $data['level'] ?? $user->level;
        
        if ($user->save()) {
            return json(['code' => 1, 'msg' => '更新成功']);
        } else {
            return json(['code' => 0, 'msg' => '更新失败']);
        }
    }

    /**
     * 用户详情
     */
    public function detail()
    {
        $id = Request::get('id');
        $user = UserModel::find($id);
        
        if (!$user) {
            $this->error('用户不存在');
        }
        
        // 获取用户资产
        $assets = $user->assets;
        
        // 获取用户订单统计
        $orderStats = [
            'total_orders' => $user->orders()->count(),
            'total_amount' => $user->orders()->sum('amount')
        ];
        
        View::assign([
            'user' => $user,
            'assets' => $assets,
            'order_stats' => $orderStats,
            'title' => '用户详情 - GVD管理后台'
        ]);
        
        return View::fetch('admin/user/detail');
    }

    /**
     * 删除用户
     */
    public function delete()
    {
        $id = Request::post('id');
        $user = UserModel::find($id);
        
        if (!$user) {
            return json(['code' => 0, 'msg' => '用户不存在']);
        }
        
        // 只能删除测试用户
        if ($user->user_type == UserModel::USER_TYPE_FORMAL) {
            return json(['code' => 0, 'msg' => '不能删除正式用户']);
        }
        
        if ($user->delete()) {
            return json(['code' => 1, 'msg' => '删除成功']);
        } else {
            return json(['code' => 0, 'msg' => '删除失败']);
        }
    }

    /**
     * 用户统计
     */
    public function statistics()
    {
        // 只统计正式用户
        $stats = [
            'total_users' => UserModel::where('user_type', UserModel::USER_TYPE_FORMAL)->count(),
            'today_register' => UserModel::where('user_type', UserModel::USER_TYPE_FORMAL)
                                        ->whereTime('created_at', 'today')
                                        ->count(),
            'this_month_register' => UserModel::where('user_type', UserModel::USER_TYPE_FORMAL)
                                            ->whereTime('created_at', 'month')
                                            ->count(),
            'active_users' => UserModel::where('user_type', UserModel::USER_TYPE_FORMAL)
                                      ->where('status', 1)
                                      ->count()
        ];
        
        return json(['code' => 1, 'data' => $stats]);
    }

    /**
     * 调整用户余额（只有管理员能修改正式用户余额）
     */
    public function adjustBalance()
    {
        $userId = Request::post('user_id');
        $coinSymbol = Request::post('coin_symbol', 'USDT');
        $amount = floatval(Request::post('amount', 0));
        $remark = Request::post('remark', '管理员调整余额');
        $adminId = Session::get('admin_id');

        if (!$userId || $amount == 0) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        // 验证用户
        $user = UserModel::find($userId);
        if (!$user) {
            return json(['code' => 0, 'msg' => '用户不存在']);
        }

        // 检查权限：只有管理员能修改正式用户余额
        if ($user->user_type == UserModel::USER_TYPE_FORMAL) {
            // 正式用户只有管理员能修改
            $admin = \think\facade\Db::name('admins')->where('id', $adminId)->find();
            if (!$admin || $admin['role'] !== 'admin') {
                return json(['code' => 0, 'msg' => '只有管理员才能修改正式用户余额']);
            }
        }

        try {
            \think\facade\Db::startTrans();

            // 获取用户资产
            $userAsset = \app\common\model\UserAsset::where('user_id', $userId)
                                                  ->where('coin_symbol', $coinSymbol)
                                                  ->find();

            if (!$userAsset) {
                // 创建资产记录
                $userAsset = \app\common\model\UserAsset::create([
                    'user_id' => $userId,
                    'coin_symbol' => $coinSymbol,
                    'available' => 0,
                    'frozen' => 0,
                    'total' => 0
                ]);
            }

            $oldBalance = $userAsset->available;
            $newBalance = $oldBalance + $amount;

            if ($newBalance < 0) {
                throw new \Exception('余额不足，无法扣减');
            }

            // 更新资产
            $userAsset->available = $newBalance;
            $userAsset->total = $newBalance + $userAsset->frozen;
            $userAsset->save();

            // 记录财务流水
            \app\common\model\FinancialRecord::create([
                'user_id' => $userId,
                'type' => 'admin_adjust',
                'coin_symbol' => $coinSymbol,
                'amount' => $amount,
                'balance' => $newBalance,
                'description' => $remark,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            // 记录操作日志
            \think\facade\Db::name('admin_logs')->insert([
                'admin_id' => $adminId,
                'action' => 'adjust_user_balance',
                'content' => "调整用户 {$user->username} 的 {$coinSymbol} 余额：{$amount}，原因：{$remark}",
                'created_at' => date('Y-m-d H:i:s')
            ]);

            \think\facade\Db::commit();

            return json([
                'code' => 1,
                'msg' => '余额调整成功',
                'data' => [
                    'old_balance' => $oldBalance,
                    'new_balance' => $newBalance,
                    'change_amount' => $amount
                ]
            ]);

        } catch (\Exception $e) {
            \think\facade\Db::rollback();
            return json(['code' => 0, 'msg' => '调整失败：' . $e->getMessage()]);
        }
    }

    /**
     * 设置万能验证码
     */
    public function setUniversalCode()
    {
        if (!Request::isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $code = Request::post('code');
        
        if (!$code || strlen($code) != 6 || !is_numeric($code)) {
            return json(['code' => 0, 'msg' => '验证码必须是6位数字']);
        }
        
        $result = $this->verificationService->setUniversalCode($code);
        
        if ($result) {
            return json(['code' => 1, 'msg' => '设置成功']);
        } else {
            return json(['code' => 0, 'msg' => '设置失败']);
        }
    }
}
