<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\User;
use app\common\model\Order;
use app\common\model\Trade;
use app\common\model\UserAsset;
use app\common\model\DepositRecord;
use app\common\model\WithdrawRecord;
use app\common\model\Commission;
use app\common\model\TradingPair;
use app\common\service\UserService;
use think\facade\Db;
use think\facade\Cache;

/**
 * 数据统计分析服务
 */
class StatisticsService
{
    /**
     * 获取平台总体统计
     */
    public function getPlatformStats(): array
    {
        $cacheKey = 'platform_stats';
        $stats = Cache::get($cacheKey);

        if (!$stats) {
            $stats = [
                // 只统计正式用户
                'total_users' => User::where('user_type', UserService::USER_TYPE_FORMAL)->count(),
                'active_users_today' => User::where('user_type', UserService::USER_TYPE_FORMAL)->whereTime('last_login_time', 'today')->count(),
                'active_users_week' => User::where('user_type', UserService::USER_TYPE_FORMAL)->whereTime('last_login_time', 'week')->count(),
                'active_users_month' => User::where('user_type', UserService::USER_TYPE_FORMAL)->whereTime('last_login_time', 'month')->count(),
                'new_users_today' => User::where('user_type', UserService::USER_TYPE_FORMAL)->whereTime('created_at', 'today')->count(),
                'new_users_week' => User::where('user_type', UserService::USER_TYPE_FORMAL)->whereTime('created_at', 'week')->count(),
                'new_users_month' => User::where('user_type', UserService::USER_TYPE_FORMAL)->whereTime('created_at', 'month')->count(),

                // 测试用户单独统计
                'test_users_total' => User::where('user_type', UserService::USER_TYPE_TEST)->count(),
                'test_users_today' => User::where('user_type', UserService::USER_TYPE_TEST)->whereTime('created_at', 'today')->count(),
                'total_orders' => Order::count(),
                'orders_today' => Order::whereTime('created_at', 'today')->count(),
                'completed_orders' => Order::where('status', 2)->count(),
                'total_trades' => Trade::count(),
                'trades_today' => Trade::whereTime('created_at', 'today')->count(),
                'total_volume' => Trade::sum('total'),
                'volume_today' => Trade::whereTime('created_at', 'today')->sum('total'),
                'volume_week' => Trade::whereTime('created_at', 'week')->sum('total'),
                'volume_month' => Trade::whereTime('created_at', 'month')->sum('total'),
                'total_deposits' => DepositRecord::where('status', 1)->sum('amount'),
                'deposits_today' => DepositRecord::where('status', 1)->whereTime('created_at', 'today')->sum('amount'),
                'total_withdrawals' => WithdrawRecord::where('status', 2)->sum('amount'),
                'withdrawals_today' => WithdrawRecord::where('status', 2)->whereTime('created_at', 'today')->sum('amount'),
                'total_commission' => Commission::where('status', 1)->sum('amount'),
                'commission_today' => Commission::where('status', 1)->whereTime('created_at', 'today')->sum('amount'),
                'supported_coins' => TradingPair::distinct()->count('base_coin') + TradingPair::distinct()->count('quote_coin')
            ];

            Cache::set($cacheKey, $stats, 300); // 缓存5分钟
        }

        return $stats;
    }

    /**
     * 获取用户统计
     */
    public function getUserStats(string $period = 'month'): array
    {
        $cacheKey = "user_stats_{$period}";
        $stats = Cache::get($cacheKey);

        if (!$stats) {
            $dateFormat = $this->getDateFormat($period);
            $days = $this->getPeriodDays($period);

            // 正式用户注册趋势（只统计正式用户）
            $registrationTrend = Db::table('ce_users')
                ->field("DATE_FORMAT(created_at, '{$dateFormat}') as date, COUNT(*) as count")
                ->where('created_at', '>=', date('Y-m-d H:i:s', time() - $days * 86400))
                ->where('user_type', UserService::USER_TYPE_FORMAL)
                ->group('date')
                ->order('date')
                ->select()
                ->toArray();

            // 测试用户注册趋势（单独统计）
            $testRegistrationTrend = Db::table('ce_users')
                ->field("DATE_FORMAT(created_at, '{$dateFormat}') as date, COUNT(*) as count")
                ->where('created_at', '>=', date('Y-m-d H:i:s', time() - $days * 86400))
                ->where('user_type', UserService::USER_TYPE_TEST)
                ->group('date')
                ->order('date')
                ->select();

            // 正式用户活跃度
            $activityTrend = Db::table('ce_users')
                ->field("DATE_FORMAT(last_login_time, '{$dateFormat}') as date, COUNT(*) as count")
                ->where('last_login_time', '>=', date('Y-m-d H:i:s', time() - $days * 86400))
                ->where('user_type', UserService::USER_TYPE_FORMAL)
                ->group('date')
                ->order('date')
                ->select()
                ->toArray();

            // 用户类型分布（分别统计）
            $formalUserCount = Db::table('ce_users')
                ->where('user_type', UserService::USER_TYPE_FORMAL)
                ->count();

            $testUserCount = Db::table('ce_users')
                ->where('user_type', UserService::USER_TYPE_TEST)
                ->count();

            $userTypeDistribution = [
                [
                    'user_type' => UserService::USER_TYPE_FORMAL,
                    'type_name' => '正式用户',
                    'count' => $formalUserCount,
                    'show_in_main_stats' => true
                ],
                [
                    'user_type' => UserService::USER_TYPE_TEST,
                    'type_name' => '测试用户',
                    'count' => $testUserCount,
                    'show_in_main_stats' => false
                ]
            ];

            $stats = [
                'registration_trend' => $registrationTrend,
                'test_registration_trend' => $testRegistrationTrend,
                'activity_trend' => $activityTrend,
                'user_type_distribution' => $userTypeDistribution,
                'formal_users_only' => true // 标识主要统计只包含正式用户
            ];

            Cache::set($cacheKey, $stats, 1800); // 缓存30分钟
        }

        return $stats;
    }

    /**
     * 获取交易统计
     */
    public function getTradeStats(string $period = 'month'): array
    {
        $cacheKey = "trade_stats_{$period}";
        $stats = Cache::get($cacheKey);

        if (!$stats) {
            $dateFormat = $this->getDateFormat($period);
            $days = $this->getPeriodDays($period);

            // 交易量趋势
            $volumeTrend = Db::table('trades')
                ->field("DATE_FORMAT(created_at, '{$dateFormat}') as date, SUM(total) as volume, COUNT(*) as count")
                ->where('created_at', '>=', date('Y-m-d H:i:s', time() - $days * 86400))
                ->group('date')
                ->order('date')
                ->select()
                ->toArray();

            // 订单统计
            $orderStats = Db::table('orders')
                ->field("DATE_FORMAT(created_at, '{$dateFormat}') as date, COUNT(*) as total_orders, 
                        SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as completed_orders,
                        SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as cancelled_orders")
                ->where('created_at', '>=', date('Y-m-d H:i:s', time() - $days * 86400))
                ->group('date')
                ->order('date')
                ->select()
                ->toArray();

            // 交易对统计
            $symbolStats = Db::table('trades')
                ->field('symbol, SUM(total) as volume, COUNT(*) as count')
                ->where('created_at', '>=', date('Y-m-d H:i:s', time() - $days * 86400))
                ->group('symbol')
                ->order('volume', 'desc')
                ->limit(10)
                ->select()
                ->toArray();

            // 买卖比例
            $buySellRatio = Db::table('trades')
                ->field('type, COUNT(*) as count, SUM(total) as volume')
                ->where('created_at', '>=', date('Y-m-d H:i:s', time() - $days * 86400))
                ->group('type')
                ->select()
                ->toArray();

            $stats = [
                'volume_trend' => $volumeTrend,
                'order_stats' => $orderStats,
                'symbol_stats' => $symbolStats,
                'buy_sell_ratio' => $buySellRatio
            ];

            Cache::set($cacheKey, $stats, 1800); // 缓存30分钟
        }

        return $stats;
    }

    /**
     * 获取资产统计
     */
    public function getAssetStats(): array
    {
        $cacheKey = 'asset_stats';
        $stats = Cache::get($cacheKey);

        if (!$stats) {
            // 币种分布
            $coinDistribution = Db::table('user_assets')
                ->field('coin_symbol, SUM(total) as total_amount, COUNT(DISTINCT user_id) as holder_count')
                ->where('total', '>', 0)
                ->group('coin_symbol')
                ->order('total_amount', 'desc')
                ->select()
                ->toArray();

            // 资产集中度（前10%用户持有的资产比例）
            $assetConcentration = [];
            foreach ($coinDistribution as $coin) {
                $totalUsers = Db::table('user_assets')
                    ->where('coin_symbol', $coin['coin_symbol'])
                    ->where('total', '>', 0)
                    ->count();

                $top10PercentCount = max(1, floor($totalUsers * 0.1));
                
                $top10PercentAmount = Db::table('user_assets')
                    ->where('coin_symbol', $coin['coin_symbol'])
                    ->where('total', '>', 0)
                    ->order('total', 'desc')
                    ->limit($top10PercentCount)
                    ->sum('total');

                $concentration = $coin['total_amount'] > 0 ? ($top10PercentAmount / $coin['total_amount']) * 100 : 0;

                $assetConcentration[] = [
                    'coin_symbol' => $coin['coin_symbol'],
                    'concentration' => round($concentration, 2)
                ];
            }

            // 充值提现统计
            $depositWithdrawStats = [
                'deposits' => [
                    'today' => DepositRecord::where('status', 1)->whereTime('created_at', 'today')->sum('amount'),
                    'week' => DepositRecord::where('status', 1)->whereTime('created_at', 'week')->sum('amount'),
                    'month' => DepositRecord::where('status', 1)->whereTime('created_at', 'month')->sum('amount'),
                    'count_today' => DepositRecord::where('status', 1)->whereTime('created_at', 'today')->count(),
                    'count_week' => DepositRecord::where('status', 1)->whereTime('created_at', 'week')->count(),
                    'count_month' => DepositRecord::where('status', 1)->whereTime('created_at', 'month')->count()
                ],
                'withdrawals' => [
                    'today' => WithdrawRecord::where('status', 2)->whereTime('created_at', 'today')->sum('amount'),
                    'week' => WithdrawRecord::where('status', 2)->whereTime('created_at', 'week')->sum('amount'),
                    'month' => WithdrawRecord::where('status', 2)->whereTime('created_at', 'month')->sum('amount'),
                    'count_today' => WithdrawRecord::where('status', 2)->whereTime('created_at', 'today')->count(),
                    'count_week' => WithdrawRecord::where('status', 2)->whereTime('created_at', 'week')->count(),
                    'count_month' => WithdrawRecord::where('status', 2)->whereTime('created_at', 'month')->count()
                ]
            ];

            $stats = [
                'coin_distribution' => $coinDistribution,
                'asset_concentration' => $assetConcentration,
                'deposit_withdraw_stats' => $depositWithdrawStats
            ];

            Cache::set($cacheKey, $stats, 1800); // 缓存30分钟
        }

        return $stats;
    }

    /**
     * 获取佣金统计
     */
    public function getCommissionStats(string $period = 'month'): array
    {
        $cacheKey = "commission_stats_{$period}";
        $stats = Cache::get($cacheKey);

        if (!$stats) {
            $dateFormat = $this->getDateFormat($period);
            $days = $this->getPeriodDays($period);

            // 佣金趋势
            $commissionTrend = Db::table('commissions')
                ->field("DATE_FORMAT(created_at, '{$dateFormat}') as date, SUM(amount) as amount, COUNT(*) as count")
                ->where('status', 1)
                ->where('created_at', '>=', date('Y-m-d H:i:s', time() - $days * 86400))
                ->group('date')
                ->order('date')
                ->select()
                ->toArray();

            // 佣金类型分布
            $typeDistribution = Db::table('commissions')
                ->field('type, SUM(amount) as amount, COUNT(*) as count')
                ->where('status', 1)
                ->where('created_at', '>=', date('Y-m-d H:i:s', time() - $days * 86400))
                ->group('type')
                ->select()
                ->toArray();

            // 佣金等级分布
            $levelDistribution = Db::table('commissions')
                ->field('level, SUM(amount) as amount, COUNT(*) as count')
                ->where('status', 1)
                ->where('created_at', '>=', date('Y-m-d H:i:s', time() - $days * 86400))
                ->group('level')
                ->select()
                ->toArray();

            // 顶级推广者
            $topPromoters = Db::table('commissions')
                ->field('user_id, SUM(amount) as total_commission, COUNT(*) as commission_count')
                ->where('status', 1)
                ->where('created_at', '>=', date('Y-m-d H:i:s', time() - $days * 86400))
                ->group('user_id')
                ->order('total_commission', 'desc')
                ->limit(10)
                ->select()
                ->toArray();

            // 添加用户名
            foreach ($topPromoters as &$promoter) {
                $user = User::find($promoter['user_id']);
                $promoter['username'] = $user ? $user->username : '未知用户';
            }

            $stats = [
                'commission_trend' => $commissionTrend,
                'type_distribution' => $typeDistribution,
                'level_distribution' => $levelDistribution,
                'top_promoters' => $topPromoters
            ];

            Cache::set($cacheKey, $stats, 1800); // 缓存30分钟
        }

        return $stats;
    }

    /**
     * 获取用户行为分析
     */
    public function getUserBehaviorAnalysis(int $userId): array
    {
        $cacheKey = "user_behavior_{$userId}";
        $analysis = Cache::get($cacheKey);

        if (!$analysis) {
            // 交易行为
            $tradeStats = [
                'total_orders' => Order::where('user_id', $userId)->count(),
                'completed_orders' => Order::where('user_id', $userId)->where('status', 2)->count(),
                'cancelled_orders' => Order::where('user_id', $userId)->where('status', 3)->count(),
                'total_volume' => Trade::where('user_id', $userId)->sum('total'),
                'avg_order_size' => Trade::where('user_id', $userId)->avg('total'),
                'favorite_symbols' => Db::table('trades')
                    ->field('symbol, COUNT(*) as count')
                    ->where('user_id', $userId)
                    ->group('symbol')
                    ->order('count', 'desc')
                    ->limit(5)
                    ->select()
                    ->toArray(),
                'trading_hours' => Db::table('trades')
                    ->field('HOUR(created_at) as hour, COUNT(*) as count')
                    ->where('user_id', $userId)
                    ->group('hour')
                    ->select()
                    ->toArray()
            ];

            // 资产行为
            $assetStats = [
                'total_deposits' => DepositRecord::where('user_id', $userId)->where('status', 1)->sum('amount'),
                'total_withdrawals' => WithdrawRecord::where('user_id', $userId)->where('status', 2)->sum('amount'),
                'deposit_count' => DepositRecord::where('user_id', $userId)->where('status', 1)->count(),
                'withdrawal_count' => WithdrawRecord::where('user_id', $userId)->where('status', 2)->count(),
                'current_assets' => UserAsset::where('user_id', $userId)->where('total', '>', 0)->count(),
                'asset_distribution' => UserAsset::where('user_id', $userId)
                    ->where('total', '>', 0)
                    ->field('coin_symbol, total')
                    ->select()
                    ->toArray()
            ];

            // 邀请行为
            $inviteStats = [
                'level1_count' => User::where('inviter_level1', $userId)->count(),
                'level2_count' => User::where('inviter_level2', $userId)->count(),
                'level3_count' => User::where('inviter_level3', $userId)->count(),
                'total_commission' => Commission::where('user_id', $userId)->where('status', 1)->sum('amount'),
                'commission_count' => Commission::where('user_id', $userId)->where('status', 1)->count()
            ];

            // 活跃度分析
            $activityStats = [
                'login_frequency' => $this->calculateLoginFrequency($userId),
                'trading_frequency' => $this->calculateTradingFrequency($userId),
                'last_active_days' => $this->getLastActiveDays($userId)
            ];

            $analysis = [
                'trade_stats' => $tradeStats,
                'asset_stats' => $assetStats,
                'invite_stats' => $inviteStats,
                'activity_stats' => $activityStats
            ];

            Cache::set($cacheKey, $analysis, 3600); // 缓存1小时
        }

        return $analysis;
    }

    /**
     * 获取财务报表
     */
    public function getFinancialReport(string $startDate, string $endDate): array
    {
        $cacheKey = "financial_report_{$startDate}_{$endDate}";
        $report = Cache::get($cacheKey);

        if (!$report) {
            // 收入统计
            $income = [
                'trading_fees' => Trade::whereBetween('created_at', [$startDate, $endDate])->sum('fee'),
                'withdrawal_fees' => WithdrawRecord::whereBetween('created_at', [$startDate, $endDate])
                    ->where('status', 2)->sum('fee'),
                'other_income' => 0 // 其他收入
            ];

            // 支出统计
            $expense = [
                'commission_paid' => Commission::whereBetween('created_at', [$startDate, $endDate])
                    ->where('status', 1)->sum('amount'),
                'operational_cost' => 0, // 运营成本
                'other_expense' => 0 // 其他支出
            ];

            // 用户资产变化
            $assetChange = [
                'total_deposits' => DepositRecord::whereBetween('created_at', [$startDate, $endDate])
                    ->where('status', 1)->sum('amount'),
                'total_withdrawals' => WithdrawRecord::whereBetween('created_at', [$startDate, $endDate])
                    ->where('status', 2)->sum('amount'),
                'net_inflow' => 0 // 净流入
            ];

            $assetChange['net_inflow'] = $assetChange['total_deposits'] - $assetChange['total_withdrawals'];

            // 利润计算
            $totalIncome = array_sum($income);
            $totalExpense = array_sum($expense);
            $netProfit = $totalIncome - $totalExpense;

            $report = [
                'income' => $income,
                'expense' => $expense,
                'asset_change' => $assetChange,
                'summary' => [
                    'total_income' => $totalIncome,
                    'total_expense' => $totalExpense,
                    'net_profit' => $netProfit,
                    'profit_margin' => $totalIncome > 0 ? ($netProfit / $totalIncome) * 100 : 0
                ]
            ];

            Cache::set($cacheKey, $report, 3600); // 缓存1小时
        }

        return $report;
    }

    /**
     * 获取日期格式
     */
    private function getDateFormat(string $period): string
    {
        switch ($period) {
            case 'day':
                return '%Y-%m-%d %H:00:00';
            case 'week':
            case 'month':
                return '%Y-%m-%d';
            case 'year':
                return '%Y-%m';
            default:
                return '%Y-%m-%d';
        }
    }

    /**
     * 获取周期天数
     */
    private function getPeriodDays(string $period): int
    {
        switch ($period) {
            case 'day':
                return 1;
            case 'week':
                return 7;
            case 'month':
                return 30;
            case 'year':
                return 365;
            default:
                return 30;
        }
    }

    /**
     * 计算登录频率
     */
    private function calculateLoginFrequency(int $userId): float
    {
        $user = User::find($userId);
        if (!$user) return 0;

        $daysSinceRegistration = (time() - strtotime($user->created_at)) / 86400;
        $lastLoginDays = $user->last_login_time ? (time() - strtotime($user->last_login_time)) / 86400 : $daysSinceRegistration;

        return $daysSinceRegistration > 0 ? (1 / max(1, $lastLoginDays)) : 0;
    }

    /**
     * 计算交易频率
     */
    private function calculateTradingFrequency(int $userId): float
    {
        $totalTrades = Trade::where('user_id', $userId)->count();
        $user = User::find($userId);
        
        if (!$user || $totalTrades == 0) return 0;

        $daysSinceRegistration = (time() - strtotime($user->created_at)) / 86400;
        
        return $daysSinceRegistration > 0 ? $totalTrades / $daysSinceRegistration : 0;
    }

    /**
     * 获取最后活跃天数
     */
    private function getLastActiveDays(int $userId): int
    {
        $user = User::find($userId);
        if (!$user || !$user->last_login_time) return 999;

        return (int)((time() - strtotime($user->last_login_time)) / 86400);
    }
}
