<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Session;
use think\facade\Cookie;
use think\facade\Cache;

/**
 * 多语言服务类
 */
class LanguageService
{
    // 支持的语言列表
    const SUPPORTED_LANGUAGES = [
        'zh-cn' => [
            'name' => '简体中文',
            'native_name' => '简体中文',
            'flag' => '🇨🇳',
            'direction' => 'ltr'
        ],
        'zh-tw' => [
            'name' => '繁體中文',
            'native_name' => '繁體中文',
            'flag' => '🇹🇼',
            'direction' => 'ltr'
        ],
        'en' => [
            'name' => 'English',
            'native_name' => 'English',
            'flag' => '🇺🇸',
            'direction' => 'ltr'
        ],
        'ja' => [
            'name' => '日本語',
            'native_name' => '日本語',
            'flag' => '🇯🇵',
            'direction' => 'ltr'
        ],
        'ko' => [
            'name' => '한국어',
            'native_name' => '한국어',
            'flag' => '🇰🇷',
            'direction' => 'ltr'
        ],
        'es' => [
            'name' => 'Español',
            'native_name' => 'Español',
            'flag' => '🇪🇸',
            'direction' => 'ltr'
        ],
        'fr' => [
            'name' => 'Français',
            'native_name' => 'Français',
            'flag' => '🇫🇷',
            'direction' => 'ltr'
        ],
        'de' => [
            'name' => 'Deutsch',
            'native_name' => 'Deutsch',
            'flag' => '🇩🇪',
            'direction' => 'ltr'
        ],
        'ru' => [
            'name' => 'Русский',
            'native_name' => 'Русский',
            'flag' => '🇷🇺',
            'direction' => 'ltr'
        ],
        'ar' => [
            'name' => 'العربية',
            'native_name' => 'العربية',
            'flag' => '🇸🇦',
            'direction' => 'rtl'
        ],
        'hi' => [
            'name' => 'हिन्दी',
            'native_name' => 'हिन्दी',
            'flag' => '🇮🇳',
            'direction' => 'ltr'
        ],
        'pt' => [
            'name' => 'Português',
            'native_name' => 'Português',
            'flag' => '🇧🇷',
            'direction' => 'ltr'
        ]
    ];

    // 默认语言
    const DEFAULT_LANGUAGE = 'zh-cn';

    // 语言包缓存时间（秒）
    const CACHE_TIME = 3600;

    /**
     * 获取当前语言
     */
    public function getCurrentLanguage(): string
    {
        // 优先级：URL参数 > Session > Cookie > 浏览器语言 > 默认语言
        $language = input('lang', '');
        
        if (!$language) {
            $language = Session::get('language', '');
        }
        
        if (!$language) {
            $language = Cookie::get('language', '');
        }
        
        if (!$language) {
            $language = $this->detectBrowserLanguage();
        }
        
        if (!$language || !$this->isLanguageSupported($language)) {
            $language = self::DEFAULT_LANGUAGE;
        }

        return $language;
    }

    /**
     * 设置当前语言
     */
    public function setLanguage(string $language): bool
    {
        if (!$this->isLanguageSupported($language)) {
            return false;
        }

        // 保存到Session和Cookie
        Session::set('language', $language);
        Cookie::set('language', $language, 86400 * 30); // 30天

        return true;
    }

    /**
     * 检查语言是否支持
     */
    public function isLanguageSupported(string $language): bool
    {
        return isset(self::SUPPORTED_LANGUAGES[$language]);
    }

    /**
     * 获取支持的语言列表
     */
    public function getSupportedLanguages(): array
    {
        return self::SUPPORTED_LANGUAGES;
    }

    /**
     * 获取语言信息
     */
    public function getLanguageInfo(string $language): array
    {
        return self::SUPPORTED_LANGUAGES[$language] ?? self::SUPPORTED_LANGUAGES[self::DEFAULT_LANGUAGE];
    }

    /**
     * 检测浏览器语言
     */
    private function detectBrowserLanguage(): string
    {
        $acceptLanguage = $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '';
        
        if (empty($acceptLanguage)) {
            return self::DEFAULT_LANGUAGE;
        }

        // 解析Accept-Language头
        $languages = [];
        $parts = explode(',', $acceptLanguage);
        
        foreach ($parts as $part) {
            $part = trim($part);
            $langParts = explode(';', $part);
            $lang = trim($langParts[0]);
            $quality = 1.0;
            
            if (isset($langParts[1])) {
                $qParts = explode('=', $langParts[1]);
                if (count($qParts) === 2 && trim($qParts[0]) === 'q') {
                    $quality = (float)trim($qParts[1]);
                }
            }
            
            $languages[$lang] = $quality;
        }

        // 按质量排序
        arsort($languages);

        // 查找支持的语言
        foreach ($languages as $lang => $quality) {
            // 完全匹配
            if ($this->isLanguageSupported($lang)) {
                return $lang;
            }
            
            // 主语言匹配
            $mainLang = explode('-', $lang)[0];
            foreach (self::SUPPORTED_LANGUAGES as $supportedLang => $info) {
                if (strpos($supportedLang, $mainLang) === 0) {
                    return $supportedLang;
                }
            }
        }

        return self::DEFAULT_LANGUAGE;
    }

    /**
     * 加载语言包
     */
    public function loadLanguagePack(string $language, string $module = 'common'): array
    {
        $cacheKey = "lang_pack_{$language}_{$module}";
        $langPack = Cache::get($cacheKey);

        if ($langPack === null) {
            $langFile = app_path() . "lang/{$language}/{$module}.php";
            
            if (file_exists($langFile)) {
                $langPack = include $langFile;
            } else {
                // 回退到默认语言
                $defaultLangFile = app_path() . "lang/" . self::DEFAULT_LANGUAGE . "/{$module}.php";
                if (file_exists($defaultLangFile)) {
                    $langPack = include $defaultLangFile;
                } else {
                    $langPack = [];
                }
            }

            Cache::set($cacheKey, $langPack, self::CACHE_TIME);
        }

        return $langPack;
    }

    /**
     * 翻译文本
     */
    public function translate(string $key, array $params = [], string $module = 'common', string $language = ''): string
    {
        if (empty($language)) {
            $language = $this->getCurrentLanguage();
        }

        $langPack = $this->loadLanguagePack($language, $module);
        
        // 支持点号分隔的嵌套键
        $keys = explode('.', $key);
        $value = $langPack;
        
        foreach ($keys as $k) {
            if (isset($value[$k])) {
                $value = $value[$k];
            } else {
                // 如果找不到翻译，返回原键名
                return $key;
            }
        }

        // 如果不是字符串，返回原键名
        if (!is_string($value)) {
            return $key;
        }

        // 替换参数
        if (!empty($params)) {
            foreach ($params as $param => $replacement) {
                $value = str_replace(":{$param}", $replacement, $value);
            }
        }

        return $value;
    }

    /**
     * 格式化数字
     */
    public function formatNumber(float $number, int $decimals = 2, string $language = ''): string
    {
        if (empty($language)) {
            $language = $this->getCurrentLanguage();
        }

        $formatters = [
            'zh-cn' => ['decimal_point' => '.', 'thousands_sep' => ','],
            'zh-tw' => ['decimal_point' => '.', 'thousands_sep' => ','],
            'en' => ['decimal_point' => '.', 'thousands_sep' => ','],
            'ja' => ['decimal_point' => '.', 'thousands_sep' => ','],
            'ko' => ['decimal_point' => '.', 'thousands_sep' => ','],
            'es' => ['decimal_point' => ',', 'thousands_sep' => '.'],
            'fr' => ['decimal_point' => ',', 'thousands_sep' => ' '],
            'de' => ['decimal_point' => ',', 'thousands_sep' => '.'],
            'ru' => ['decimal_point' => ',', 'thousands_sep' => ' '],
            'ar' => ['decimal_point' => '.', 'thousands_sep' => ','],
            'hi' => ['decimal_point' => '.', 'thousands_sep' => ','],
            'pt' => ['decimal_point' => ',', 'thousands_sep' => '.']
        ];

        $formatter = $formatters[$language] ?? $formatters['en'];
        
        return number_format($number, $decimals, $formatter['decimal_point'], $formatter['thousands_sep']);
    }

    /**
     * 格式化货币
     */
    public function formatCurrency(float $amount, string $currency = 'USD', string $language = ''): string
    {
        if (empty($language)) {
            $language = $this->getCurrentLanguage();
        }

        $formattedNumber = $this->formatNumber($amount, 2, $language);

        $currencySymbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'JPY' => '¥',
            'CNY' => '¥',
            'KRW' => '₩',
            'RUB' => '₽',
            'BTC' => '₿',
            'ETH' => 'Ξ'
        ];

        $symbol = $currencySymbols[$currency] ?? $currency;

        // 根据语言调整货币符号位置
        $prefixCurrencies = ['zh-cn', 'zh-tw', 'en', 'ja', 'ko'];
        
        if (in_array($language, $prefixCurrencies)) {
            return $symbol . $formattedNumber;
        } else {
            return $formattedNumber . ' ' . $symbol;
        }
    }

    /**
     * 格式化日期时间
     */
    public function formatDateTime(\DateTime $dateTime, string $format = 'full', string $language = ''): string
    {
        if (empty($language)) {
            $language = $this->getCurrentLanguage();
        }

        $formats = [
            'zh-cn' => [
                'full' => 'Y年m月d日 H:i:s',
                'date' => 'Y年m月d日',
                'time' => 'H:i:s',
                'short' => 'm-d H:i'
            ],
            'zh-tw' => [
                'full' => 'Y年m月d日 H:i:s',
                'date' => 'Y年m月d日',
                'time' => 'H:i:s',
                'short' => 'm-d H:i'
            ],
            'en' => [
                'full' => 'M j, Y g:i:s A',
                'date' => 'M j, Y',
                'time' => 'g:i:s A',
                'short' => 'm/d H:i'
            ],
            'ja' => [
                'full' => 'Y年m月d日 H:i:s',
                'date' => 'Y年m月d日',
                'time' => 'H:i:s',
                'short' => 'm/d H:i'
            ],
            'ko' => [
                'full' => 'Y년 m월 d일 H:i:s',
                'date' => 'Y년 m월 d일',
                'time' => 'H:i:s',
                'short' => 'm/d H:i'
            ]
        ];

        $langFormats = $formats[$language] ?? $formats['en'];
        $dateFormat = $langFormats[$format] ?? $langFormats['full'];

        return $dateTime->format($dateFormat);
    }

    /**
     * 获取相对时间
     */
    public function getRelativeTime(\DateTime $dateTime, string $language = ''): string
    {
        if (empty($language)) {
            $language = $this->getCurrentLanguage();
        }

        $now = new \DateTime();
        $diff = $now->getTimestamp() - $dateTime->getTimestamp();

        $timeUnits = [
            'zh-cn' => [
                'just_now' => '刚刚',
                'minutes_ago' => ':count分钟前',
                'hours_ago' => ':count小时前',
                'days_ago' => ':count天前',
                'weeks_ago' => ':count周前',
                'months_ago' => ':count个月前',
                'years_ago' => ':count年前'
            ],
            'en' => [
                'just_now' => 'just now',
                'minutes_ago' => ':count minutes ago',
                'hours_ago' => ':count hours ago',
                'days_ago' => ':count days ago',
                'weeks_ago' => ':count weeks ago',
                'months_ago' => ':count months ago',
                'years_ago' => ':count years ago'
            ]
        ];

        $units = $timeUnits[$language] ?? $timeUnits['en'];

        if ($diff < 60) {
            return $units['just_now'];
        } elseif ($diff < 3600) {
            $minutes = floor($diff / 60);
            return str_replace(':count', $minutes, $units['minutes_ago']);
        } elseif ($diff < 86400) {
            $hours = floor($diff / 3600);
            return str_replace(':count', $hours, $units['hours_ago']);
        } elseif ($diff < 604800) {
            $days = floor($diff / 86400);
            return str_replace(':count', $days, $units['days_ago']);
        } elseif ($diff < 2592000) {
            $weeks = floor($diff / 604800);
            return str_replace(':count', $weeks, $units['weeks_ago']);
        } elseif ($diff < 31536000) {
            $months = floor($diff / 2592000);
            return str_replace(':count', $months, $units['months_ago']);
        } else {
            $years = floor($diff / 31536000);
            return str_replace(':count', $years, $units['years_ago']);
        }
    }

    /**
     * 获取时区列表
     */
    public function getTimezones(): array
    {
        return [
            'UTC' => 'UTC (协调世界时)',
            'Asia/Shanghai' => 'Asia/Shanghai (北京时间)',
            'Asia/Hong_Kong' => 'Asia/Hong_Kong (香港时间)',
            'Asia/Taipei' => 'Asia/Taipei (台北时间)',
            'Asia/Tokyo' => 'Asia/Tokyo (东京时间)',
            'Asia/Seoul' => 'Asia/Seoul (首尔时间)',
            'America/New_York' => 'America/New_York (纽约时间)',
            'America/Los_Angeles' => 'America/Los_Angeles (洛杉矶时间)',
            'Europe/London' => 'Europe/London (伦敦时间)',
            'Europe/Paris' => 'Europe/Paris (巴黎时间)',
            'Europe/Berlin' => 'Europe/Berlin (柏林时间)',
            'Europe/Moscow' => 'Europe/Moscow (莫斯科时间)'
        ];
    }

    /**
     * 清除语言包缓存
     */
    public function clearLanguageCache(string $language = '', string $module = ''): bool
    {
        if ($language && $module) {
            $cacheKey = "lang_pack_{$language}_{$module}";
            return Cache::delete($cacheKey);
        } elseif ($language) {
            // 清除指定语言的所有缓存
            $pattern = "lang_pack_{$language}_*";
            return Cache::clear($pattern);
        } else {
            // 清除所有语言包缓存
            $pattern = "lang_pack_*";
            return Cache::clear($pattern);
        }
    }

    /**
     * 导出语言包
     */
    public function exportLanguagePack(string $language, string $module = 'common'): array
    {
        return $this->loadLanguagePack($language, $module);
    }

    /**
     * 导入语言包
     */
    public function importLanguagePack(string $language, string $module, array $data): bool
    {
        try {
            $langDir = app_path() . "lang/{$language}";
            if (!is_dir($langDir)) {
                mkdir($langDir, 0755, true);
            }

            $langFile = $langDir . "/{$module}.php";
            $content = "<?php\n\nreturn " . var_export($data, true) . ";\n";
            
            if (file_put_contents($langFile, $content)) {
                // 清除缓存
                $this->clearLanguageCache($language, $module);
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            return false;
        }
    }
}
