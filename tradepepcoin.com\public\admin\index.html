<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字货币交易平台 - 管理后台</title>
    <link href="https://cdn.jsdelivr.net/npm/element-ui@2.15.9/lib/theme-chalk/index.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { margin: 0; font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif; }
        .header { background: #304156; color: white; padding: 0 20px; display: flex; justify-content: space-between; align-items: center; height: 60px; }
        .logo { font-size: 20px; font-weight: bold; }
        .user-info { display: flex; align-items: center; gap: 15px; }
        .main-container { display: flex; height: calc(100vh - 60px); }
        .sidebar { width: 200px; background: #263445; color: white; overflow-y: auto; }
        .content { flex: 1; padding: 20px; background: #f0f2f5; overflow-y: auto; }
        .menu-item { padding: 15px 20px; cursor: pointer; border-bottom: 1px solid #34495e; }
        .menu-item:hover { background: #34495e; }
        .menu-item.active { background: #409eff; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 12px rgba(0,0,0,0.1); }
        .stat-number { font-size: 32px; font-weight: bold; color: #409eff; }
        .stat-label { color: #666; margin-top: 5px; }
        .chart-container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 12px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .hidden { display: none; }
        .table-container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 12px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div id="app">
        <!-- 头部 -->
        <div class="header">
            <div class="logo">
                <i class="fas fa-coins"></i> 数字货币交易平台
            </div>
            <div class="user-info">
                <span>管理员</span>
                <el-button type="text" style="color: white;" @click="logout">
                    <i class="fas fa-sign-out-alt"></i> 退出
                </el-button>
            </div>
        </div>

        <!-- 主体 -->
        <div class="main-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="menu-item" :class="{active: currentPage === 'dashboard'}" @click="showPage('dashboard')">
                    <i class="fas fa-tachometer-alt"></i> 仪表板
                </div>
                <div class="menu-item" :class="{active: currentPage === 'users'}" @click="showPage('users')">
                    <i class="fas fa-users"></i> 用户管理
                </div>
                <div class="menu-item" :class="{active: currentPage === 'orders'}" @click="showPage('orders')">
                    <i class="fas fa-chart-line"></i> 订单管理
                </div>
                <div class="menu-item" :class="{active: currentPage === 'finance'}" @click="showPage('finance')">
                    <i class="fas fa-wallet"></i> 财务管理
                </div>
                <div class="menu-item" :class="{active: currentPage === 'config'}" @click="showPage('config')">
                    <i class="fas fa-cog"></i> 系统配置
                </div>
                <div class="menu-item" :class="{active: currentPage === 'service'}" @click="showPage('service')">
                    <i class="fas fa-headset"></i> 客服管理
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 仪表板 -->
                <div v-show="currentPage === 'dashboard'">
                    <h2>数据概览</h2>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">{{ stats.totalUsers }}</div>
                            <div class="stat-label">总用户数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">{{ stats.todayOrders }}</div>
                            <div class="stat-label">今日订单</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">{{ stats.totalVolume }}</div>
                            <div class="stat-label">总交易量</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">{{ stats.onlineUsers }}</div>
                            <div class="stat-label">在线用户</div>
                        </div>
                    </div>

                    <div class="chart-container">
                        <h3>交易量趋势</h3>
                        <div id="volumeChart" style="height: 300px;"></div>
                    </div>
                </div>

                <!-- 用户管理 -->
                <div v-show="currentPage === 'users'">
                    <h2>用户管理</h2>
                    <div class="table-container">
                        <el-table :data="users" style="width: 100%">
                            <el-table-column prop="id" label="ID" width="80"></el-table-column>
                            <el-table-column prop="username" label="用户名"></el-table-column>
                            <el-table-column prop="email" label="邮箱"></el-table-column>
                            <el-table-column prop="userType" label="用户类型">
                                <template slot-scope="scope">
                                    <el-tag :type="scope.row.userType === 1 ? 'success' : 'info'">
                                        {{ scope.row.userType === 1 ? '正式用户' : '测试用户' }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="status" label="状态">
                                <template slot-scope="scope">
                                    <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                                        {{ scope.row.status === 1 ? '正常' : '禁用' }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="createdAt" label="注册时间"></el-table-column>
                            <el-table-column label="操作" width="200">
                                <template slot-scope="scope">
                                    <el-button size="mini" @click="editUser(scope.row)">编辑</el-button>
                                    <el-button size="mini" type="danger" @click="deleteUser(scope.row)">删除</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>

                <!-- 订单管理 -->
                <div v-show="currentPage === 'orders'">
                    <h2>订单管理</h2>
                    <div class="table-container">
                        <el-table :data="orders" style="width: 100%">
                            <el-table-column prop="id" label="订单ID"></el-table-column>
                            <el-table-column prop="username" label="用户"></el-table-column>
                            <el-table-column prop="symbol" label="交易对"></el-table-column>
                            <el-table-column prop="direction" label="方向">
                                <template slot-scope="scope">
                                    <el-tag :type="scope.row.direction === 'up' ? 'success' : 'danger'">
                                        {{ scope.row.direction === 'up' ? '看涨' : '看跌' }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="amount" label="金额"></el-table-column>
                            <el-table-column prop="status" label="状态">
                                <template slot-scope="scope">
                                    <el-tag :type="getOrderStatusType(scope.row.status)">
                                        {{ getOrderStatusText(scope.row.status) }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="createdAt" label="创建时间"></el-table-column>
                        </el-table>
                    </div>
                </div>

                <!-- 财务管理 -->
                <div v-show="currentPage === 'finance'">
                    <h2>财务管理</h2>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">{{ financeStats.totalDeposits }}</div>
                            <div class="stat-label">总充值金额</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">{{ financeStats.totalWithdraws }}</div>
                            <div class="stat-label">总提币金额</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">{{ financeStats.totalFees }}</div>
                            <div class="stat-label">总手续费</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">{{ financeStats.totalProfit }}</div>
                            <div class="stat-label">总利润</div>
                        </div>
                    </div>
                </div>

                <!-- 系统配置 -->
                <div v-show="currentPage === 'config'">
                    <h2>系统配置</h2>
                    <div class="table-container">
                        <el-form :model="config" label-width="120px">
                            <el-form-item label="网站名称">
                                <el-input v-model="config.siteName"></el-input>
                            </el-form-item>
                            <el-form-item label="交易手续费">
                                <el-input v-model="config.tradeFee" type="number"></el-input>
                            </el-form-item>
                            <el-form-item label="最小交易金额">
                                <el-input v-model="config.minTradeAmount" type="number"></el-input>
                            </el-form-item>
                            <el-form-item label="最大交易金额">
                                <el-input v-model="config.maxTradeAmount" type="number"></el-input>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="saveConfig">保存配置</el-button>
                            </el-form-item>
                        </el-form>
                    </div>
                </div>

                <!-- 客服管理 -->
                <div v-show="currentPage === 'service'">
                    <h2>客服管理</h2>
                    <div class="table-container">
                        <el-table :data="tickets" style="width: 100%">
                            <el-table-column prop="ticketId" label="工单ID"></el-table-column>
                            <el-table-column prop="username" label="用户"></el-table-column>
                            <el-table-column prop="subject" label="主题"></el-table-column>
                            <el-table-column prop="status" label="状态">
                                <template slot-scope="scope">
                                    <el-tag :type="getTicketStatusType(scope.row.status)">
                                        {{ getTicketStatusText(scope.row.status) }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="createdAt" label="创建时间"></el-table-column>
                            <el-table-column label="操作" width="150">
                                <template slot-scope="scope">
                                    <el-button size="mini" @click="viewTicket(scope.row)">查看</el-button>
                                    <el-button size="mini" type="primary" @click="replyTicket(scope.row)">回复</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.9/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@0.27.2/dist/axios.min.js"></script>
    <script>
        new Vue({
            el: '#app',
            data: {
                currentPage: 'dashboard',
                stats: {
                    totalUsers: 1234,
                    todayOrders: 567,
                    totalVolume: '¥12,345,678',
                    onlineUsers: 89
                },
                users: [],
                orders: [],
                tickets: [],
                financeStats: {
                    totalDeposits: '¥1,234,567',
                    totalWithdraws: '¥987,654',
                    totalFees: '¥12,345',
                    totalProfit: '¥234,567'
                },
                config: {
                    siteName: '数字货币交易平台',
                    tradeFee: 0.001,
                    minTradeAmount: 10,
                    maxTradeAmount: 100000
                }
            },
            mounted() {
                this.loadData();
                this.initChart();
            },
            methods: {
                showPage(page) {
                    this.currentPage = page;
                    this.loadData();
                },
                loadData() {
                    // 模拟数据加载
                    if (this.currentPage === 'users') {
                        this.users = [
                            { id: 1, username: 'user001', email: '<EMAIL>', userType: 1, status: 1, createdAt: '2024-01-01 10:00:00' },
                            { id: 2, username: 'test001', email: '<EMAIL>', userType: 2, status: 1, createdAt: '2024-01-02 11:00:00' }
                        ];
                    } else if (this.currentPage === 'orders') {
                        this.orders = [
                            { id: 'ORD001', username: 'user001', symbol: 'BTCUSDT', direction: 'up', amount: 100, status: 2, createdAt: '2024-01-01 10:00:00' },
                            { id: 'ORD002', username: 'user002', symbol: 'ETHUSDT', direction: 'down', amount: 200, status: 3, createdAt: '2024-01-01 11:00:00' }
                        ];
                    } else if (this.currentPage === 'service') {
                        this.tickets = [
                            { ticketId: 'TK001', username: 'user001', subject: '充值问题', status: 1, createdAt: '2024-01-01 10:00:00' },
                            { ticketId: 'TK002', username: 'user002', subject: '提币问题', status: 2, createdAt: '2024-01-01 11:00:00' }
                        ];
                    }
                },
                initChart() {
                    const chart = echarts.init(document.getElementById('volumeChart'));
                    const option = {
                        title: { text: '7日交易量趋势' },
                        xAxis: { type: 'category', data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'] },
                        yAxis: { type: 'value' },
                        series: [{
                            data: [120, 200, 150, 80, 70, 110, 130],
                            type: 'line',
                            smooth: true
                        }]
                    };
                    chart.setOption(option);
                },
                getOrderStatusType(status) {
                    const types = { 0: 'info', 1: 'warning', 2: 'success', 3: 'danger', 4: 'info', 5: 'info' };
                    return types[status] || 'info';
                },
                getOrderStatusText(status) {
                    const texts = { 0: '待开始', 1: '进行中', 2: '盈利', 3: '亏损', 4: '平局', 5: '已取消' };
                    return texts[status] || '未知';
                },
                getTicketStatusType(status) {
                    const types = { 1: 'warning', 2: 'info', 3: 'success', 4: 'info' };
                    return types[status] || 'info';
                },
                getTicketStatusText(status) {
                    const texts = { 1: '开启', 2: '等待回复', 3: '已解决', 4: '已关闭' };
                    return texts[status] || '未知';
                },
                editUser(user) {
                    this.$message.info('编辑用户功能');
                },
                deleteUser(user) {
                    this.$confirm('确定删除该用户吗？', '提示', { type: 'warning' })
                        .then(() => this.$message.success('删除成功'))
                        .catch(() => {});
                },
                viewTicket(ticket) {
                    this.$message.info('查看工单详情');
                },
                replyTicket(ticket) {
                    this.$message.info('回复工单');
                },
                saveConfig() {
                    this.$message.success('配置保存成功');
                },
                logout() {
                    this.$confirm('确定退出登录吗？', '提示', { type: 'warning' })
                        .then(() => {
                            this.$message.success('退出成功');
                            // 跳转到登录页
                        })
                        .catch(() => {});
                }
            }
        });
    </script>
</body>
</html>
