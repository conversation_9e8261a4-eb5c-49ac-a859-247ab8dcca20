<?php

return [
    // Common
    'app_name' => 'Cryptocurrency Exchange Platform',
    'welcome' => 'Welcome',
    'home' => 'Home',
    'login' => 'Login',
    'logout' => 'Logout',
    'register' => 'Register',
    'submit' => 'Submit',
    'cancel' => 'Cancel',
    'confirm' => 'Confirm',
    'save' => 'Save',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'view' => 'View',
    'back' => 'Back',
    'next' => 'Next',
    'previous' => 'Previous',
    'search' => 'Search',
    'filter' => 'Filter',
    'sort' => 'Sort',
    'refresh' => 'Refresh',
    'loading' => 'Loading...',
    'no_data' => 'No Data',
    'operation_success' => 'Operation Successful',
    'operation_failed' => 'Operation Failed',
    'network_error' => 'Network Error',
    'system_error' => 'System Error',
    'permission_denied' => 'Permission Denied',
    'page_not_found' => 'Page Not Found',
    'coming_soon' => 'Coming Soon',

    // User
    'user' => [
        'profile' => 'Profile',
        'username' => 'Username',
        'email' => 'Email',
        'phone' => 'Phone',
        'password' => 'Password',
        'confirm_password' => 'Confirm Password',
        'old_password' => 'Old Password',
        'new_password' => 'New Password',
        'nickname' => 'Nickname',
        'avatar' => 'Avatar',
        'real_name' => 'Real Name',
        'id_card' => 'ID Card',
        'birthday' => 'Birthday',
        'gender' => 'Gender',
        'address' => 'Address',
        'login_time' => 'Login Time',
        'register_time' => 'Register Time',
        'last_login' => 'Last Login',
        'status' => 'Status',
        'active' => 'Active',
        'inactive' => 'Inactive',
        'pending' => 'Pending',
        'verified' => 'Verified',
        'unverified' => 'Unverified'
    ],

    // Trading
    'trade' => [
        'trading' => 'Trading',
        'spot_trading' => 'Spot Trading',
        'contract_trading' => 'Contract Trading',
        'symbol' => 'Symbol',
        'price' => 'Price',
        'amount' => 'Amount',
        'total' => 'Total',
        'buy' => 'Buy',
        'sell' => 'Sell',
        'order' => 'Order',
        'order_type' => 'Order Type',
        'market_order' => 'Market Order',
        'limit_order' => 'Limit Order',
        'stop_order' => 'Stop Order',
        'order_status' => 'Order Status',
        'pending' => 'Pending',
        'partial_filled' => 'Partial Filled',
        'filled' => 'Filled',
        'cancelled' => 'Cancelled',
        'rejected' => 'Rejected',
        'open_orders' => 'Open Orders',
        'order_history' => 'Order History',
        'trade_history' => 'Trade History',
        'balance' => 'Balance',
        'available' => 'Available',
        'frozen' => 'Frozen',
        'fee' => 'Fee',
        'profit_loss' => 'P&L',
        'volume' => 'Volume',
        'turnover' => 'Turnover',
        'high' => 'High',
        'low' => 'Low',
        'change' => 'Change',
        'change_percent' => 'Change %'
    ],

    // Assets
    'asset' => [
        'assets' => 'Assets',
        'wallet' => 'Wallet',
        'deposit' => 'Deposit',
        'withdraw' => 'Withdraw',
        'transfer' => 'Transfer',
        'deposit_address' => 'Deposit Address',
        'withdraw_address' => 'Withdraw Address',
        'deposit_amount' => 'Deposit Amount',
        'withdraw_amount' => 'Withdraw Amount',
        'min_deposit' => 'Min Deposit',
        'min_withdraw' => 'Min Withdraw',
        'withdraw_fee' => 'Withdraw Fee',
        'deposit_record' => 'Deposit Record',
        'withdraw_record' => 'Withdraw Record',
        'transaction_id' => 'Transaction ID',
        'confirmations' => 'Confirmations',
        'required_confirmations' => 'Required Confirmations',
        'processing' => 'Processing',
        'completed' => 'Completed',
        'failed' => 'Failed',
        'total_assets' => 'Total Assets',
        'estimated_value' => 'Estimated Value'
    ],

    // Security
    'security' => [
        'security' => 'Security',
        'security_center' => 'Security Center',
        'two_factor_auth' => 'Two-Factor Authentication',
        'google_auth' => 'Google Authenticator',
        'sms_auth' => 'SMS Authentication',
        'email_auth' => 'Email Authentication',
        'login_password' => 'Login Password',
        'trade_password' => 'Trade Password',
        'api_management' => 'API Management',
        'device_management' => 'Device Management',
        'login_history' => 'Login History',
        'security_log' => 'Security Log',
        'risk_warning' => 'Risk Warning',
        'verification_code' => 'Verification Code',
        'send_code' => 'Send Code',
        'verify' => 'Verify',
        'bind' => 'Bind',
        'unbind' => 'Unbind',
        'enable' => 'Enable',
        'disable' => 'Disable'
    ],

    // Time
    'time' => [
        'now' => 'Now',
        'today' => 'Today',
        'yesterday' => 'Yesterday',
        'tomorrow' => 'Tomorrow',
        'this_week' => 'This Week',
        'last_week' => 'Last Week',
        'this_month' => 'This Month',
        'last_month' => 'Last Month',
        'this_year' => 'This Year',
        'last_year' => 'Last Year',
        'minute' => 'Minute',
        'hour' => 'Hour',
        'day' => 'Day',
        'week' => 'Week',
        'month' => 'Month',
        'year' => 'Year',
        'ago' => 'Ago',
        'later' => 'Later',
        'just_now' => 'Just Now',
        'minutes_ago' => ':count minutes ago',
        'hours_ago' => ':count hours ago',
        'days_ago' => ':count days ago'
    ],

    // Status
    'status' => [
        'online' => 'Online',
        'offline' => 'Offline',
        'normal' => 'Normal',
        'abnormal' => 'Abnormal',
        'enabled' => 'Enabled',
        'disabled' => 'Disabled',
        'active' => 'Active',
        'inactive' => 'Inactive',
        'success' => 'Success',
        'failed' => 'Failed',
        'pending' => 'Pending',
        'processing' => 'Processing',
        'completed' => 'Completed',
        'cancelled' => 'Cancelled',
        'expired' => 'Expired'
    ],

    // Navigation
    'menu' => [
        'dashboard' => 'Dashboard',
        'trading' => 'Trading',
        'wallet' => 'Wallet',
        'orders' => 'Orders',
        'history' => 'History',
        'security' => 'Security',
        'settings' => 'Settings',
        'help' => 'Help',
        'support' => 'Support',
        'about' => 'About',
        'terms' => 'Terms of Service',
        'privacy' => 'Privacy Policy',
        'fees' => 'Fee Schedule',
        'api_docs' => 'API Documentation'
    ],

    // Validation
    'validation' => [
        'required' => 'The :field field is required',
        'email' => 'Please enter a valid email address',
        'phone' => 'Please enter a valid phone number',
        'min_length' => 'The :field must be at least :min characters',
        'max_length' => 'The :field may not be greater than :max characters',
        'numeric' => 'The :field must be a number',
        'positive' => 'The :field must be greater than 0',
        'confirmed' => 'The :field confirmation does not match',
        'unique' => 'The :field has already been taken',
        'exists' => 'The :field does not exist',
        'invalid' => 'The :field format is invalid'
    ],

    // Messages
    'message' => [
        'login_success' => 'Login successful',
        'login_failed' => 'Login failed',
        'logout_success' => 'Logout successful',
        'register_success' => 'Registration successful',
        'register_failed' => 'Registration failed',
        'update_success' => 'Update successful',
        'update_failed' => 'Update failed',
        'delete_success' => 'Delete successful',
        'delete_failed' => 'Delete failed',
        'save_success' => 'Save successful',
        'save_failed' => 'Save failed',
        'send_success' => 'Send successful',
        'send_failed' => 'Send failed',
        'verify_success' => 'Verification successful',
        'verify_failed' => 'Verification failed',
        'order_success' => 'Order placed successfully',
        'order_failed' => 'Order failed',
        'cancel_success' => 'Cancel successful',
        'cancel_failed' => 'Cancel failed',
        'deposit_success' => 'Deposit successful',
        'deposit_failed' => 'Deposit failed',
        'withdraw_success' => 'Withdraw successful',
        'withdraw_failed' => 'Withdraw failed',
        'insufficient_balance' => 'Insufficient balance',
        'invalid_amount' => 'Invalid amount',
        'invalid_price' => 'Invalid price',
        'market_closed' => 'Market is closed',
        'maintenance' => 'System maintenance',
        'confirm_delete' => 'Are you sure you want to delete?',
        'confirm_cancel' => 'Are you sure you want to cancel?',
        'confirm_logout' => 'Are you sure you want to logout?'
    ]
];
