/**
 * GVD WebSocket客户端
 * 用于实时接收市场数据
 */
class GVDWebSocket {
    constructor(url = 'ws://localhost:9501') {
        this.url = url;
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 3000;
        this.subscriptions = new Set();
        this.callbacks = new Map();
        this.requestId = 1;
        this.pendingRequests = new Map();
        
        // 事件回调
        this.onOpen = null;
        this.onClose = null;
        this.onError = null;
        this.onMessage = null;
        
        this.connect();
    }
    
    /**
     * 连接WebSocket
     */
    connect() {
        try {
            this.ws = new WebSocket(this.url);
            
            this.ws.onopen = (event) => {
                console.log('WebSocket连接成功');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                
                // 重新订阅之前的频道
                this.resubscribe();
                
                if (this.onOpen) {
                    this.onOpen(event);
                }
            };
            
            this.ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleMessage(data);
                } catch (error) {
                    console.error('解析WebSocket消息失败:', error);
                }
            };
            
            this.ws.onclose = (event) => {
                console.log('WebSocket连接关闭');
                this.isConnected = false;
                
                if (this.onClose) {
                    this.onClose(event);
                }
                
                // 自动重连
                this.attemptReconnect();
            };
            
            this.ws.onerror = (event) => {
                console.error('WebSocket连接错误:', event);
                
                if (this.onError) {
                    this.onError(event);
                }
            };
            
        } catch (error) {
            console.error('创建WebSocket连接失败:', error);
            this.attemptReconnect();
        }
    }
    
    /**
     * 尝试重连
     */
    attemptReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('WebSocket重连次数超过限制');
            return;
        }
        
        this.reconnectAttempts++;
        console.log(`尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        
        setTimeout(() => {
            this.connect();
        }, this.reconnectInterval);
    }
    
    /**
     * 处理接收到的消息
     */
    handleMessage(data) {
        if (this.onMessage) {
            this.onMessage(data);
        }
        
        // 处理响应消息
        if (data.id && this.pendingRequests.has(data.id)) {
            const callback = this.pendingRequests.get(data.id);
            callback(data);
            this.pendingRequests.delete(data.id);
            return;
        }
        
        // 处理推送消息
        if (data.stream) {
            const callbacks = this.callbacks.get(data.stream);
            if (callbacks) {
                callbacks.forEach(callback => {
                    try {
                        callback(data.data);
                    } catch (error) {
                        console.error('执行回调函数失败:', error);
                    }
                });
            }
        }
    }
    
    /**
     * 发送消息
     */
    send(message) {
        if (!this.isConnected || this.ws.readyState !== WebSocket.OPEN) {
            console.warn('WebSocket未连接，无法发送消息');
            return false;
        }
        
        try {
            this.ws.send(JSON.stringify(message));
            return true;
        } catch (error) {
            console.error('发送WebSocket消息失败:', error);
            return false;
        }
    }
    
    /**
     * 发送请求并等待响应
     */
    request(method, params = {}) {
        return new Promise((resolve, reject) => {
            const id = this.requestId++;
            const message = {
                method: method,
                params: params,
                id: id
            };
            
            // 设置超时
            const timeout = setTimeout(() => {
                this.pendingRequests.delete(id);
                reject(new Error('请求超时'));
            }, 10000);
            
            // 保存回调
            this.pendingRequests.set(id, (response) => {
                clearTimeout(timeout);
                if (response.error) {
                    reject(new Error(response.error.msg || '请求失败'));
                } else {
                    resolve(response.result);
                }
            });
            
            // 发送请求
            if (!this.send(message)) {
                clearTimeout(timeout);
                this.pendingRequests.delete(id);
                reject(new Error('发送请求失败'));
            }
        });
    }
    
    /**
     * 订阅数据流
     */
    subscribe(streams, callback) {
        if (typeof streams === 'string') {
            streams = [streams];
        }
        
        streams.forEach(stream => {
            this.subscriptions.add(stream);
            
            if (!this.callbacks.has(stream)) {
                this.callbacks.set(stream, new Set());
            }
            
            if (callback) {
                this.callbacks.get(stream).add(callback);
            }
        });
        
        // 发送订阅请求
        if (this.isConnected) {
            this.request('SUBSCRIBE', streams).catch(error => {
                console.error('订阅失败:', error);
            });
        }
    }
    
    /**
     * 取消订阅
     */
    unsubscribe(streams, callback = null) {
        if (typeof streams === 'string') {
            streams = [streams];
        }
        
        streams.forEach(stream => {
            if (callback) {
                const callbacks = this.callbacks.get(stream);
                if (callbacks) {
                    callbacks.delete(callback);
                    if (callbacks.size === 0) {
                        this.callbacks.delete(stream);
                        this.subscriptions.delete(stream);
                    }
                }
            } else {
                this.callbacks.delete(stream);
                this.subscriptions.delete(stream);
            }
        });
        
        // 发送取消订阅请求
        if (this.isConnected) {
            this.request('UNSUBSCRIBE', streams).catch(error => {
                console.error('取消订阅失败:', error);
            });
        }
    }
    
    /**
     * 重新订阅所有频道
     */
    resubscribe() {
        if (this.subscriptions.size > 0) {
            const streams = Array.from(this.subscriptions);
            this.request('SUBSCRIBE', streams).catch(error => {
                console.error('重新订阅失败:', error);
            });
        }
    }
    
    /**
     * 订阅K线数据
     */
    subscribeKline(symbol, interval, callback) {
        const stream = `${symbol.toLowerCase()}@kline_${interval}`;
        this.subscribe(stream, callback);
    }
    
    /**
     * 订阅24小时行情
     */
    subscribeTicker(symbol, callback) {
        const stream = `${symbol.toLowerCase()}@ticker`;
        this.subscribe(stream, callback);
    }
    
    /**
     * 订阅深度数据
     */
    subscribeDepth(symbol, callback) {
        const stream = `${symbol.toLowerCase()}@depth`;
        this.subscribe(stream, callback);
    }
    
    /**
     * 订阅成交记录
     */
    subscribeTrades(symbol, callback) {
        const stream = `${symbol.toLowerCase()}@trade`;
        this.subscribe(stream, callback);
    }
    
    /**
     * 获取24小时行情
     */
    async getTicker(symbol) {
        return this.request('GET_TICKER', { symbol });
    }
    
    /**
     * 获取深度数据
     */
    async getDepth(symbol, limit = 20) {
        return this.request('GET_DEPTH', { symbol, limit });
    }
    
    /**
     * 获取K线数据
     */
    async getKline(symbol, interval) {
        return this.request('GET_KLINE', { symbol, interval });
    }
    
    /**
     * 发送心跳
     */
    ping() {
        return this.request('PING');
    }
    
    /**
     * 关闭连接
     */
    close() {
        if (this.ws) {
            this.ws.close();
        }
    }
    
    /**
     * 获取连接状态
     */
    getStatus() {
        return {
            connected: this.isConnected,
            readyState: this.ws ? this.ws.readyState : WebSocket.CLOSED,
            subscriptions: Array.from(this.subscriptions)
        };
    }
}

// 工具函数
const WebSocketUtils = {
    /**
     * 格式化K线数据为TradingView格式
     */
    formatKlineForTradingView(klineData) {
        const k = klineData.k;
        return {
            time: k.t,
            open: parseFloat(k.o),
            high: parseFloat(k.h),
            low: parseFloat(k.l),
            close: parseFloat(k.c),
            volume: parseFloat(k.v)
        };
    },
    
    /**
     * 格式化深度数据
     */
    formatDepthData(depthData) {
        return {
            bids: depthData.bids.map(item => ({
                price: parseFloat(item[0]),
                amount: parseFloat(item[1])
            })),
            asks: depthData.asks.map(item => ({
                price: parseFloat(item[0]),
                amount: parseFloat(item[1])
            }))
        };
    },
    
    /**
     * 格式化行情数据
     */
    formatTickerData(tickerData) {
        return {
            symbol: tickerData.s,
            price: parseFloat(tickerData.c),
            change: parseFloat(tickerData.p),
            changePercent: parseFloat(tickerData.P),
            high: parseFloat(tickerData.h),
            low: parseFloat(tickerData.l),
            volume: parseFloat(tickerData.v),
            amount: parseFloat(tickerData.q),
            openTime: tickerData.O,
            closeTime: tickerData.C
        };
    }
};

// 导出到全局
if (typeof window !== 'undefined') {
    window.GVDWebSocket = GVDWebSocket;
    window.WebSocketUtils = WebSocketUtils;
}

// Node.js环境
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { GVDWebSocket, WebSocketUtils };
}
