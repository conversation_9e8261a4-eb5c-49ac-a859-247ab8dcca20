#!/bin/bash

# GVD交易平台自动部署脚本
# 版本: 2.0.0
# 作者: GVD Team

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统环境
check_environment() {
    log_info "检查系统环境..."
    
    # 检查操作系统
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        log_success "操作系统: Linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        log_success "操作系统: macOS"
    else
        log_error "不支持的操作系统: $OSTYPE"
        exit 1
    fi
    
    # 检查PHP版本
    if command -v php &> /dev/null; then
        PHP_VERSION=$(php -r "echo PHP_VERSION;")
        if php -r "exit(version_compare(PHP_VERSION, '8.1.0', '>=') ? 0 : 1);"; then
            log_success "PHP版本: $PHP_VERSION"
        else
            log_error "PHP版本过低，需要8.1.0或更高版本，当前版本: $PHP_VERSION"
            exit 1
        fi
    else
        log_error "PHP未安装"
        exit 1
    fi
    
    # 检查Composer
    if command -v composer &> /dev/null; then
        COMPOSER_VERSION=$(composer --version | cut -d' ' -f3)
        log_success "Composer版本: $COMPOSER_VERSION"
    else
        log_error "Composer未安装"
        exit 1
    fi
    
    # 检查MySQL
    if command -v mysql &> /dev/null; then
        MYSQL_VERSION=$(mysql --version | cut -d' ' -f6 | cut -d',' -f1)
        log_success "MySQL版本: $MYSQL_VERSION"
    else
        log_warning "MySQL未安装或不在PATH中"
    fi
    
    # 检查Redis
    if command -v redis-cli &> /dev/null; then
        REDIS_VERSION=$(redis-cli --version | cut -d' ' -f2)
        log_success "Redis版本: $REDIS_VERSION"
    else
        log_warning "Redis未安装或不在PATH中"
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装Composer依赖..."
    
    if [ -f "composer.json" ]; then
        composer install --no-dev --optimize-autoloader
        log_success "Composer依赖安装完成"
    else
        log_error "composer.json文件不存在"
        exit 1
    fi
}

# 配置环境
setup_environment() {
    log_info "配置环境文件..."
    
    if [ ! -f ".env" ]; then
        log_error ".env文件不存在，请先配置环境文件"
        exit 1
    fi
    
    # 检查必要的环境变量
    if ! grep -q "DATABASE_DATABASE" .env; then
        log_error ".env文件中缺少数据库配置"
        exit 1
    fi
    
    log_success "环境配置检查完成"
}

# 设置目录权限
setup_permissions() {
    log_info "设置目录权限..."
    
    # 创建必要的目录
    mkdir -p runtime/log
    mkdir -p runtime/cache
    mkdir -p runtime/temp
    mkdir -p public/uploads
    
    # 设置权限
    chmod -R 755 .
    chmod -R 777 runtime/
    chmod -R 777 public/uploads/
    chmod +x think
    
    log_success "目录权限设置完成"
}

# 数据库操作
setup_database() {
    log_info "设置数据库..."
    
    # 从.env文件读取数据库配置
    DB_HOST=$(grep DATABASE_HOSTNAME .env | cut -d'=' -f2 | tr -d ' ')
    DB_NAME=$(grep DATABASE_DATABASE .env | cut -d'=' -f2 | tr -d ' ')
    DB_USER=$(grep DATABASE_USERNAME .env | cut -d'=' -f2 | tr -d ' ')
    DB_PASS=$(grep DATABASE_PASSWORD .env | cut -d'=' -f2 | tr -d ' ')
    
    if [ -z "$DB_HOST" ] || [ -z "$DB_NAME" ] || [ -z "$DB_USER" ]; then
        log_error "数据库配置不完整"
        exit 1
    fi
    
    # 测试数据库连接
    if command -v mysql &> /dev/null; then
        if mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASS" -e "SELECT 1;" &> /dev/null; then
            log_success "数据库连接测试成功"
            
            # 检查数据库是否存在
            if mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASS" -e "USE $DB_NAME;" &> /dev/null; then
                log_success "数据库 $DB_NAME 已存在"
            else
                log_info "创建数据库 $DB_NAME..."
                mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASS" -e "CREATE DATABASE $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
                log_success "数据库创建成功"
            fi
            
            # 导入数据库结构
            if [ -f "database/gvd_trading.sql" ]; then
                log_info "导入数据库结构..."
                mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" < database/gvd_trading.sql
                log_success "数据库结构导入完成"
            else
                log_warning "数据库结构文件不存在"
            fi
        else
            log_error "数据库连接失败，请检查配置"
            exit 1
        fi
    else
        log_warning "跳过数据库操作（MySQL客户端未安装）"
    fi
}

# 清理缓存
clear_cache() {
    log_info "清理缓存..."
    
    if [ -f "think" ]; then
        php think clear
        log_success "应用缓存清理完成"
    fi
    
    # 清理运行时文件
    rm -rf runtime/cache/*
    rm -rf runtime/log/*
    rm -rf runtime/temp/*
    
    log_success "运行时文件清理完成"
}

# 启动服务
start_services() {
    log_info "启动相关服务..."
    
    # 检查并启动Redis
    if command -v redis-server &> /dev/null; then
        if ! pgrep redis-server > /dev/null; then
            log_info "启动Redis服务..."
            redis-server --daemonize yes
            sleep 2
            if pgrep redis-server > /dev/null; then
                log_success "Redis服务启动成功"
            else
                log_warning "Redis服务启动失败"
            fi
        else
            log_success "Redis服务已运行"
        fi
    fi
    
    # 启动定时任务（如果是生产环境）
    if [ "$1" = "production" ]; then
        log_info "配置定时任务..."
        
        # 检查crontab是否已配置
        if ! crontab -l 2>/dev/null | grep -q "trading:settle"; then
            # 添加定时任务
            (crontab -l 2>/dev/null; echo "* * * * * cd $(pwd) && php think trading:settle >> /var/log/gvd_trading.log 2>&1") | crontab -
            (crontab -l 2>/dev/null; echo "* * * * * cd $(pwd) && php think trading:price-update >> /var/log/gvd_price.log 2>&1") | crontab -
            log_success "定时任务配置完成"
        else
            log_success "定时任务已配置"
        fi
    fi
}

# 运行测试
run_tests() {
    log_info "运行基础测试..."
    
    # 测试PHP语法
    if find . -name "*.php" -not -path "./vendor/*" -exec php -l {} \; | grep -q "Parse error"; then
        log_error "PHP语法检查失败"
        exit 1
    else
        log_success "PHP语法检查通过"
    fi
    
    # 测试数据库连接
    if [ -f "think" ]; then
        if php think test:database 2>/dev/null; then
            log_success "数据库连接测试通过"
        else
            log_warning "数据库连接测试失败（可能是命令不存在）"
        fi
    fi
    
    # 测试Redis连接
    if command -v redis-cli &> /dev/null; then
        if redis-cli ping | grep -q "PONG"; then
            log_success "Redis连接测试通过"
        else
            log_warning "Redis连接测试失败"
        fi
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "🎉 GVD交易平台部署完成！"
    echo ""
    echo "📋 部署信息："
    echo "   项目路径: $(pwd)"
    echo "   PHP版本: $(php -r 'echo PHP_VERSION;')"
    echo "   环境模式: $(grep APP_DEBUG .env | cut -d'=' -f2 | tr -d ' ' | sed 's/true/开发模式/g' | sed 's/false/生产模式/g')"
    echo ""
    echo "🌐 访问地址："
    echo "   前台首页: http://your-domain.com/"
    echo "   管理后台: http://your-domain.com/admin"
    echo "   代理后台: http://your-domain.com/agent"
    echo "   API接口: http://your-domain.com/api"
    echo ""
    echo "👤 默认账号："
    echo "   管理员: admin / admin123"
    echo ""
    echo "📚 相关文档："
    echo "   README: $(pwd)/README.md"
    echo "   安装向导: http://your-domain.com/install.php"
    echo ""
    echo "⚠️  重要提醒："
    echo "   1. 请及时修改默认密码"
    echo "   2. 请删除install.php文件"
    echo "   3. 请配置SSL证书"
    echo "   4. 请设置防火墙规则"
    echo ""
}

# 主函数
main() {
    echo "🚀 GVD数字货币交易平台自动部署脚本"
    echo "================================================"
    echo ""
    
    # 检查参数
    ENVIRONMENT=${1:-development}
    
    if [ "$ENVIRONMENT" != "development" ] && [ "$ENVIRONMENT" != "production" ]; then
        log_error "无效的环境参数，请使用 development 或 production"
        exit 1
    fi
    
    log_info "部署环境: $ENVIRONMENT"
    echo ""
    
    # 执行部署步骤
    check_environment
    echo ""
    
    install_dependencies
    echo ""
    
    setup_environment
    echo ""
    
    setup_permissions
    echo ""
    
    setup_database
    echo ""
    
    clear_cache
    echo ""
    
    start_services "$ENVIRONMENT"
    echo ""
    
    run_tests
    echo ""
    
    show_deployment_info
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
