/**
 * GVD管理端客服系统
 */

class AdminCustomerService {
    constructor() {
        this.currentSessionId = null;
        this.currentUserId = null;
        this.websocket = null;
        this.sessions = [];
        this.quickReplies = [];
        this.agents = [];
        this.token = localStorage.getItem('admin_token');
        
        this.init();
    }

    init() {
        this.connectWebSocket();
        this.loadStatistics();
        this.loadSessions();
        this.loadQuickReplies();
        this.loadAgents();
        this.bindEvents();
        
        // 定时刷新数据
        setInterval(() => {
            this.loadStatistics();
            this.loadSessions();
        }, 30000);
    }

    /**
     * 连接WebSocket
     */
    async connectWebSocket() {
        try {
            const wsUrl = `ws://${window.location.hostname}:2346`;
            this.websocket = new WebSocket(wsUrl);

            this.websocket.onopen = () => {
                console.log('管理端WebSocket连接成功');
                
                // 发送认证消息
                this.sendWebSocketMessage({
                    type: 'auth',
                    data: {
                        token: this.token,
                        user_type: 'admin'
                    }
                });
            };

            this.websocket.onmessage = (event) => {
                this.handleWebSocketMessage(JSON.parse(event.data));
            };

            this.websocket.onclose = () => {
                console.log('管理端WebSocket连接关闭');
                
                // 尝试重连
                setTimeout(() => {
                    this.connectWebSocket();
                }, 3000);
            };

            this.websocket.onerror = (error) => {
                console.error('管理端WebSocket错误:', error);
            };

        } catch (error) {
            console.error('WebSocket连接失败:', error);
        }
    }

    /**
     * 发送WebSocket消息
     */
    sendWebSocketMessage(message) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify(message));
        }
    }

    /**
     * 处理WebSocket消息
     */
    handleWebSocketMessage(message) {
        switch (message.type) {
            case 'auth_success':
                console.log('管理端认证成功');
                break;
                
            case 'new_message':
                this.handleNewMessage(message.data);
                break;
                
            case 'session_update':
                this.handleSessionUpdate(message.data);
                break;
                
            case 'user_typing':
                this.handleTypingIndicator(message.data);
                break;
        }
    }

    /**
     * 处理新消息
     */
    handleNewMessage(message) {
        // 更新会话列表中的最后消息
        this.updateSessionLastMessage(message.session_id, message);
        
        // 如果是当前会话，显示消息
        if (message.session_id === this.currentSessionId) {
            this.displayMessage(message);
        }
        
        // 播放通知声音
        this.playNotificationSound();
    }

    /**
     * 加载统计数据
     */
    async loadStatistics() {
        try {
            const response = await fetch('/admin/customer/statistics', {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            const result = await response.json();
            
            if (result.code === 1) {
                this.updateStatistics(result.data);
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    /**
     * 更新统计显示
     */
    updateStatistics(data) {
        document.getElementById('totalSessions').textContent = data.session_stats?.total_sessions || 0;
        document.getElementById('activeSessions').textContent = data.session_stats?.active_sessions || 0;
        document.getElementById('onlineAgents').textContent = data.online_stats?.find(s => s.user_type === 'agent')?.online_count || 0;
        document.getElementById('avgResponseTime').textContent = Math.round(data.session_stats?.avg_session_duration || 0) + 's';
    }

    /**
     * 加载会话列表
     */
    async loadSessions() {
        try {
            const response = await fetch('/admin/customer/sessions', {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            const result = await response.json();
            
            if (result.code === 1) {
                this.sessions = result.data;
                this.renderSessionsList();
            }
        } catch (error) {
            console.error('加载会话列表失败:', error);
        }
    }

    /**
     * 渲染会话列表
     */
    renderSessionsList() {
        const container = document.getElementById('sessionsList');
        
        if (this.sessions.length === 0) {
            container.innerHTML = `
                <div class="empty-sessions">
                    <div class="empty-icon">💬</div>
                    <div class="empty-text">暂无客服会话</div>
                </div>
            `;
            return;
        }

        const html = this.sessions.map(session => {
            const lastMessage = session.last_message;
            const unreadCount = session.unread_count_admin || 0;
            const statusClass = session.status === 'active' ? 'active' : session.status === 'waiting' ? 'waiting' : 'closed';
            
            return `
                <div class="session-item ${this.currentSessionId === session.session_id ? 'selected' : ''}" 
                     onclick="adminCustomer.selectSession('${session.session_id}', ${session.user_id})">
                    <div class="session-avatar">
                        ${session.username?.charAt(0)?.toUpperCase() || 'U'}
                    </div>
                    <div class="session-content">
                        <div class="session-header">
                            <div class="session-name">${session.username || '未知用户'}</div>
                            <div class="session-time">${this.formatTime(session.last_message_time || session.created_at)}</div>
                        </div>
                        <div class="session-preview">
                            <span class="session-status ${statusClass}">${this.getStatusText(session.status)}</span>
                            <span class="session-message">${this.getMessagePreview(lastMessage)}</span>
                        </div>
                    </div>
                    ${unreadCount > 0 ? `<div class="session-badge">${unreadCount}</div>` : ''}
                </div>
            `;
        }).join('');

        container.innerHTML = html;
    }

    /**
     * 选择会话
     */
    async selectSession(sessionId, userId) {
        this.currentSessionId = sessionId;
        this.currentUserId = userId;
        
        // 更新选中状态
        document.querySelectorAll('.session-item').forEach(item => {
            item.classList.remove('selected');
        });
        event.currentTarget.classList.add('selected');
        
        // 加入WebSocket会话
        this.sendWebSocketMessage({
            type: 'join_session',
            data: {
                session_id: sessionId
            }
        });
        
        // 加载消息历史
        await this.loadMessages(sessionId);
        
        // 加载用户信息
        await this.loadUserInfo(userId);
        
        // 显示聊天界面
        this.showChatInterface();
        
        // 标记消息为已读
        this.markMessagesAsRead(sessionId);
    }

    /**
     * 加载消息历史
     */
    async loadMessages(sessionId) {
        try {
            const response = await fetch(`/admin/customer/messages?session_id=${sessionId}`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            const result = await response.json();
            
            if (result.code === 1) {
                this.renderMessages(result.data.messages);
            }
        } catch (error) {
            console.error('加载消息失败:', error);
        }
    }

    /**
     * 渲染消息列表
     */
    renderMessages(messages) {
        const container = document.getElementById('chatMessages');
        
        if (messages.length === 0) {
            container.innerHTML = `
                <div class="empty-chat">
                    <div class="empty-icon">💬</div>
                    <div class="empty-text">
                        <h3>暂无消息</h3>
                        <p>这个会话还没有任何消息</p>
                    </div>
                </div>
            `;
            return;
        }

        const html = messages.map(message => this.renderMessage(message)).join('');
        container.innerHTML = html;
        
        // 滚动到底部
        container.scrollTop = container.scrollHeight;
    }

    /**
     * 渲染单条消息
     */
    renderMessage(message) {
        const isAdmin = message.sender_type === 'admin';
        const isAgent = message.sender_type === 'agent';
        const time = new Date(message.created_at).toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });

        let messageContent = '';
        switch (message.message_type) {
            case 'image':
                messageContent = `<div class="message-image"><img src="${message.file_url}" alt="图片"></div>`;
                break;
            case 'emoji':
                messageContent = `<div class="message-emoji">${message.content}</div>`;
                break;
            default:
                messageContent = `<div class="message-text">${this.formatMessageText(message.content)}</div>`;
        }

        return `
            <div class="message ${isAdmin ? 'admin' : isAgent ? 'agent' : 'user'}">
                <div class="message-avatar">
                    ${message.sender_info?.username?.charAt(0)?.toUpperCase() || (isAdmin ? 'A' : isAgent ? 'S' : 'U')}
                </div>
                <div class="message-content">
                    <div class="message-header">
                        <span class="message-sender">${message.sender_info?.username || '未知'}</span>
                        <span class="message-time">${time}</span>
                    </div>
                    <div class="message-bubble">
                        ${messageContent}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 显示消息
     */
    displayMessage(message) {
        const container = document.getElementById('chatMessages');
        const messageHtml = this.renderMessage(message);
        
        // 移除空状态
        const emptyChat = container.querySelector('.empty-chat');
        if (emptyChat) {
            emptyChat.remove();
        }
        
        container.insertAdjacentHTML('beforeend', messageHtml);
        container.scrollTop = container.scrollHeight;
    }

    /**
     * 发送消息
     */
    async sendMessage() {
        const input = document.getElementById('messageInput');
        const content = input.value.trim();
        
        if (!content || !this.currentSessionId) return;

        try {
            // 通过WebSocket发送
            this.sendWebSocketMessage({
                type: 'send_message',
                data: {
                    session_id: this.currentSessionId,
                    content: content,
                    message_type: 'text'
                }
            });

            // 清空输入框
            input.value = '';
            this.autoResizeTextarea(input);

        } catch (error) {
            console.error('发送消息失败:', error);
            this.showNotification('消息发送失败', 'error');
        }
    }

    /**
     * 加载快捷回复
     */
    async loadQuickReplies() {
        try {
            const response = await fetch('/admin/customer/quick-replies', {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            const result = await response.json();
            
            if (result.code === 1) {
                this.quickReplies = result.data;
                this.renderQuickReplies();
                this.renderQuickReplyList();
            }
        } catch (error) {
            console.error('加载快捷回复失败:', error);
        }
    }

    /**
     * 渲染快捷回复按钮
     */
    renderQuickReplies() {
        const container = document.getElementById('quickReplies');
        
        const html = this.quickReplies.slice(0, 6).map(reply => `
            <button class="quick-reply-btn" onclick="adminCustomer.useQuickReply('${reply.content}')">
                ${reply.title}
            </button>
        `).join('');

        container.innerHTML = html;
    }

    /**
     * 使用快捷回复
     */
    useQuickReply(content) {
        const input = document.getElementById('messageInput');
        input.value = content;
        this.autoResizeTextarea(input);
        input.focus();
    }

    /**
     * 显示聊天界面
     */
    showChatInterface() {
        document.getElementById('chatInput').style.display = 'block';
        
        // 更新聊天头部
        const session = this.sessions.find(s => s.session_id === this.currentSessionId);
        if (session) {
            const chatHeader = document.getElementById('chatHeader');
            chatHeader.querySelector('.user-name').textContent = session.username || '未知用户';
            chatHeader.querySelector('.user-status').textContent = this.getStatusText(session.status);
            chatHeader.querySelector('.user-avatar').textContent = session.username?.charAt(0)?.toUpperCase() || 'U';
        }
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 消息输入
        const messageInput = document.getElementById('messageInput');
        messageInput.addEventListener('input', (e) => {
            this.autoResizeTextarea(e.target);
        });

        messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // 模态框关闭
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });
    }

    /**
     * 工具函数
     */
    formatTime(timeString) {
        const time = new Date(timeString);
        const now = new Date();
        const diff = now - time;
        
        if (diff < 60000) { // 1分钟内
            return '刚刚';
        } else if (diff < 3600000) { // 1小时内
            return Math.floor(diff / 60000) + '分钟前';
        } else if (diff < 86400000) { // 24小时内
            return time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        } else {
            return time.toLocaleDateString('zh-CN');
        }
    }

    getStatusText(status) {
        const statusMap = {
            'active': '活跃',
            'waiting': '等待',
            'closed': '已关闭'
        };
        return statusMap[status] || status;
    }

    getMessagePreview(message) {
        if (!message) return '暂无消息';
        
        switch (message.message_type) {
            case 'image':
                return '[图片]';
            case 'emoji':
                return message.content;
            default:
                return message.content.length > 30 ? message.content.substring(0, 30) + '...' : message.content;
        }
    }

    formatMessageText(text) {
        return text.replace(/\n/g, '<br>').replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>');
    }

    autoResizeTextarea(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }

    playNotificationSound() {
        try {
            const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
            audio.volume = 0.3;
            audio.play().catch(() => {});
        } catch (error) {
            // 忽略音频播放错误
        }
    }

    /**
     * 加载代理列表
     */
    async loadAgents() {
        try {
            const response = await fetch('/admin/users?user_type=2', {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            const result = await response.json();

            if (result.code === 1) {
                this.agents = result.data;
                this.renderAgentOptions();
            }
        } catch (error) {
            console.error('加载代理列表失败:', error);
        }
    }

    /**
     * 渲染代理选项
     */
    renderAgentOptions() {
        const agentFilter = document.getElementById('agentFilter');
        const targetAgent = document.getElementById('targetAgent');

        const options = this.agents.map(agent =>
            `<option value="${agent.id}">${agent.username}</option>`
        ).join('');

        if (agentFilter) {
            agentFilter.innerHTML = '<option value="">全部客服</option>' + options;
        }

        if (targetAgent) {
            targetAgent.innerHTML = '<option value="">请选择客服</option>' + options;
        }
    }

    /**
     * 筛选会话
     */
    filterSessions() {
        const statusFilter = document.getElementById('statusFilter').value;
        const agentFilter = document.getElementById('agentFilter').value;

        let filteredSessions = this.sessions;

        if (statusFilter) {
            filteredSessions = filteredSessions.filter(s => s.status === statusFilter);
        }

        if (agentFilter) {
            filteredSessions = filteredSessions.filter(s => s.agent_id == agentFilter);
        }

        this.renderFilteredSessions(filteredSessions);
    }

    /**
     * 渲染筛选后的会话
     */
    renderFilteredSessions(sessions) {
        const container = document.getElementById('sessionsList');

        if (sessions.length === 0) {
            container.innerHTML = `
                <div class="empty-sessions">
                    <div class="empty-icon">🔍</div>
                    <div class="empty-text">没有找到匹配的会话</div>
                </div>
            `;
            return;
        }

        const html = sessions.map(session => {
            const lastMessage = session.last_message;
            const unreadCount = session.unread_count_admin || 0;
            const statusClass = session.status === 'active' ? 'active' : session.status === 'waiting' ? 'waiting' : 'closed';

            return `
                <div class="session-item ${this.currentSessionId === session.session_id ? 'selected' : ''}"
                     onclick="adminCustomer.selectSession('${session.session_id}', ${session.user_id})">
                    <div class="session-avatar">
                        ${session.username?.charAt(0)?.toUpperCase() || 'U'}
                    </div>
                    <div class="session-content">
                        <div class="session-header">
                            <div class="session-name">${session.username || '未知用户'}</div>
                            <div class="session-time">${this.formatTime(session.last_message_time || session.created_at)}</div>
                        </div>
                        <div class="session-preview">
                            <span class="session-status ${statusClass}">${this.getStatusText(session.status)}</span>
                            <span class="session-message">${this.getMessagePreview(lastMessage)}</span>
                        </div>
                    </div>
                    ${unreadCount > 0 ? `<div class="session-badge">${unreadCount}</div>` : ''}
                </div>
            `;
        }).join('');

        container.innerHTML = html;
    }

    /**
     * 加载用户信息
     */
    async loadUserInfo(userId) {
        try {
            const response = await fetch(`/admin/users/${userId}`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            const result = await response.json();

            if (result.code === 1) {
                this.renderUserInfo(result.data);
            }
        } catch (error) {
            console.error('加载用户信息失败:', error);
        }
    }

    /**
     * 渲染用户信息
     */
    renderUserInfo(user) {
        document.getElementById('profileUsername').textContent = user.username || '-';
        document.getElementById('profileEmail').textContent = user.email || '-';
        document.getElementById('profileBalance').textContent = user.balance ? `${user.balance} USDT` : '-';
        document.getElementById('profileRegTime').textContent = user.created_at ? new Date(user.created_at).toLocaleDateString() : '-';
        document.getElementById('profileAgent').textContent = user.agent_name || '无';

        // 更新头像
        const avatar = document.querySelector('.avatar-placeholder');
        if (avatar) {
            avatar.textContent = user.username?.charAt(0)?.toUpperCase() || '?';
        }
    }

    /**
     * 标记消息为已读
     */
    async markMessagesAsRead(sessionId) {
        try {
            await fetch('/admin/customer/message/read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.token}`
                },
                body: JSON.stringify({
                    session_id: sessionId
                })
            });
        } catch (error) {
            console.error('标记已读失败:', error);
        }
    }

    /**
     * 更新会话最后消息
     */
    updateSessionLastMessage(sessionId, message) {
        const sessionIndex = this.sessions.findIndex(s => s.session_id === sessionId);
        if (sessionIndex !== -1) {
            this.sessions[sessionIndex].last_message = message;
            this.sessions[sessionIndex].last_message_time = message.created_at;
            this.sessions[sessionIndex].unread_count_admin = (this.sessions[sessionIndex].unread_count_admin || 0) + 1;

            // 重新渲染会话列表
            this.renderSessionsList();
        }
    }

    /**
     * 处理会话更新
     */
    handleSessionUpdate(data) {
        const sessionIndex = this.sessions.findIndex(s => s.session_id === data.session_id);
        if (sessionIndex !== -1) {
            this.sessions[sessionIndex] = { ...this.sessions[sessionIndex], ...data };
            this.renderSessionsList();
        }
    }

    /**
     * 处理输入指示器
     */
    handleTypingIndicator(data) {
        if (data.session_id === this.currentSessionId) {
            const typingElement = document.getElementById('typingIndicator');
            if (typingElement) {
                if (data.typing) {
                    typingElement.style.display = 'flex';
                    typingElement.textContent = `${data.user_name || '用户'} 正在输入...`;
                } else {
                    typingElement.style.display = 'none';
                }
            }
        }
    }

    /**
     * 显示快捷回复模态框
     */
    showQuickReplyModal(replyId = null) {
        const modal = document.getElementById('quickReplyModal');
        const title = document.getElementById('modalTitle');
        const form = document.getElementById('quickReplyForm');

        if (replyId) {
            // 编辑模式
            const reply = this.quickReplies.find(r => r.id === replyId);
            if (reply) {
                title.textContent = '编辑快捷回复';
                document.getElementById('replyTitle').value = reply.title;
                document.getElementById('replyCategory').value = reply.category;
                document.getElementById('replyContent').value = reply.content;
                form.dataset.replyId = replyId;
            }
        } else {
            // 新增模式
            title.textContent = '添加快捷回复';
            form.reset();
            delete form.dataset.replyId;
        }

        modal.style.display = 'block';
    }

    /**
     * 隐藏快捷回复模态框
     */
    hideQuickReplyModal() {
        document.getElementById('quickReplyModal').style.display = 'none';
    }

    /**
     * 保存快捷回复
     */
    async saveQuickReply() {
        const form = document.getElementById('quickReplyForm');
        const replyId = form.dataset.replyId;

        const data = {
            title: document.getElementById('replyTitle').value,
            category: document.getElementById('replyCategory').value,
            content: document.getElementById('replyContent').value
        };

        try {
            const url = replyId ? `/admin/customer/quick-reply/${replyId}` : '/admin/customer/quick-reply';
            const method = replyId ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.token}`
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.code === 1) {
                this.showNotification(replyId ? '更新成功' : '添加成功', 'success');
                this.hideQuickReplyModal();
                this.loadQuickReplies();
            } else {
                this.showNotification(result.msg || '操作失败', 'error');
            }
        } catch (error) {
            console.error('保存快捷回复失败:', error);
            this.showNotification('操作失败', 'error');
        }
    }

    /**
     * 渲染快捷回复列表
     */
    renderQuickReplyList() {
        const container = document.getElementById('quickReplyList');

        if (this.quickReplies.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">💬</div>
                    <div class="empty-text">暂无快捷回复</div>
                </div>
            `;
            return;
        }

        const html = this.quickReplies.map(reply => `
            <div class="quick-reply-item">
                <div class="reply-header">
                    <div class="reply-title">${reply.title}</div>
                    <div class="reply-category">${this.getCategoryText(reply.category)}</div>
                </div>
                <div class="reply-content">${reply.content}</div>
                <div class="reply-actions">
                    <button class="btn btn-sm btn-outline" onclick="adminCustomer.useQuickReply('${reply.content}')">
                        使用
                    </button>
                    <button class="btn btn-sm btn-outline" onclick="adminCustomer.showQuickReplyModal(${reply.id})">
                        编辑
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="adminCustomer.deleteQuickReply(${reply.id})">
                        删除
                    </button>
                </div>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    /**
     * 删除快捷回复
     */
    async deleteQuickReply(replyId) {
        if (!confirm('确定要删除这个快捷回复吗？')) {
            return;
        }

        try {
            const response = await fetch(`/admin/customer/quick-reply/${replyId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            const result = await response.json();

            if (result.code === 1) {
                this.showNotification('删除成功', 'success');
                this.loadQuickReplies();
            } else {
                this.showNotification(result.msg || '删除失败', 'error');
            }
        } catch (error) {
            console.error('删除快捷回复失败:', error);
            this.showNotification('删除失败', 'error');
        }
    }

    /**
     * 显示转接模态框
     */
    showTransferModal() {
        if (!this.currentSessionId) {
            this.showNotification('请先选择一个会话', 'warning');
            return;
        }

        document.getElementById('transferModal').style.display = 'block';
    }

    /**
     * 隐藏转接模态框
     */
    hideTransferModal() {
        document.getElementById('transferModal').style.display = 'none';
    }

    /**
     * 转接会话
     */
    async transferSession() {
        this.showTransferModal();
    }

    /**
     * 确认转接
     */
    async confirmTransfer() {
        const targetAgentId = document.getElementById('targetAgent').value;
        const transferReason = document.getElementById('transferReason').value;

        if (!targetAgentId) {
            this.showNotification('请选择目标客服', 'warning');
            return;
        }

        try {
            const response = await fetch('/admin/customer/session/transfer', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.token}`
                },
                body: JSON.stringify({
                    session_id: this.currentSessionId,
                    target_agent_id: targetAgentId,
                    reason: transferReason
                })
            });

            const result = await response.json();

            if (result.code === 1) {
                this.showNotification('转接成功', 'success');
                this.hideTransferModal();
                this.loadSessions();
            } else {
                this.showNotification(result.msg || '转接失败', 'error');
            }
        } catch (error) {
            console.error('转接会话失败:', error);
            this.showNotification('转接失败', 'error');
        }
    }

    /**
     * 关闭会话
     */
    async closeSession() {
        if (!this.currentSessionId) {
            this.showNotification('请先选择一个会话', 'warning');
            return;
        }

        if (!confirm('确定要关闭这个会话吗？')) {
            return;
        }

        try {
            const response = await fetch('/admin/customer/session/close', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.token}`
                },
                body: JSON.stringify({
                    session_id: this.currentSessionId
                })
            });

            const result = await response.json();

            if (result.code === 1) {
                this.showNotification('会话已关闭', 'success');
                this.loadSessions();

                // 清空当前会话
                this.currentSessionId = null;
                this.currentUserId = null;
                document.getElementById('chatInput').style.display = 'none';
                document.getElementById('chatMessages').innerHTML = `
                    <div class="empty-chat">
                        <div class="empty-icon">💬</div>
                        <div class="empty-text">
                            <h3>选择一个会话开始对话</h3>
                            <p>从左侧选择一个客服会话来查看消息历史</p>
                        </div>
                    </div>
                `;
            } else {
                this.showNotification(result.msg || '关闭失败', 'error');
            }
        } catch (error) {
            console.error('关闭会话失败:', error);
            this.showNotification('关闭失败', 'error');
        }
    }

    /**
     * 查看用户订单
     */
    viewUserOrders() {
        if (!this.currentUserId) {
            this.showNotification('请先选择一个会话', 'warning');
            return;
        }

        // 打开新窗口查看用户订单
        window.open(`/admin/orders?user_id=${this.currentUserId}`, '_blank');
    }

    /**
     * 查看用户交易记录
     */
    viewUserTransactions() {
        if (!this.currentUserId) {
            this.showNotification('请先选择一个会话', 'warning');
            return;
        }

        // 打开新窗口查看用户交易记录
        window.open(`/admin/transactions?user_id=${this.currentUserId}`, '_blank');
    }

    /**
     * 导出统计数据
     */
    async exportStatistics() {
        try {
            const response = await fetch('/admin/customer/statistics/export', {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `customer-statistics-${new Date().toISOString().split('T')[0]}.xlsx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                this.showNotification('导出成功', 'success');
            } else {
                this.showNotification('导出失败', 'error');
            }
        } catch (error) {
            console.error('导出统计失败:', error);
            this.showNotification('导出失败', 'error');
        }
    }

    /**
     * 获取分类文本
     */
    getCategoryText(category) {
        const categoryMap = {
            'general': '通用',
            'trading': '交易',
            'account': '账户',
            'technical': '技术'
        };
        return categoryMap[category] || category;
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">×</button>
        `;

        document.body.appendChild(notification);

        // 自动移除
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
}

// 初始化管理端客服系统
document.addEventListener('DOMContentLoaded', function() {
    window.adminCustomer = new AdminCustomerService();
});
