<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 期货合约模型
 */
class FuturesContract extends Model
{
    protected $name = 'gvd_futures_contracts';
    
    protected $type = [
        'contract_size' => 'float',
        'tick_size' => 'float',
        'maintenance_margin_rate' => 'float',
        'maker_fee' => 'float',
        'taker_fee' => 'float',
        'is_active' => 'boolean'
    ];

    // 合约类型
    const TYPE_PERPETUAL = 1; // 永续合约
    const TYPE_DELIVERY = 2;  // 定期合约

    /**
     * 获取所有活跃的期货合约
     */
    public static function getActiveContracts(): array
    {
        return self::where('is_active', 1)
                  ->order('symbol', 'asc')
                  ->select()
                  ->toArray();
    }

    /**
     * 根据符号获取合约
     */
    public static function getBySymbol(string $symbol): ?FuturesContract
    {
        return self::where('symbol', $symbol)->find();
    }

    /**
     * 获取合约类型文本
     */
    public function getContractTypeTextAttr($value, $data): string
    {
        $types = [
            self::TYPE_PERPETUAL => '永续合约',
            self::TYPE_DELIVERY => '定期合约'
        ];
        return $types[$data['contract_type']] ?? '未知';
    }

    /**
     * 计算初始保证金
     */
    public function calculateInitialMargin(float $size, float $price, int $leverage): float
    {
        $notionalValue = $size * $price * $this->contract_size;
        return $notionalValue / $leverage;
    }

    /**
     * 计算维持保证金
     */
    public function calculateMaintenanceMargin(float $size, float $price): float
    {
        $notionalValue = $size * $price * $this->contract_size;
        return $notionalValue * $this->maintenance_margin_rate;
    }

    /**
     * 计算手续费
     */
    public function calculateFee(float $size, float $price, bool $isMaker = false): float
    {
        $notionalValue = $size * $price * $this->contract_size;
        $feeRate = $isMaker ? $this->maker_fee : $this->taker_fee;
        return $notionalValue * $feeRate;
    }

    /**
     * 验证价格精度
     */
    public function validatePrice(float $price): bool
    {
        $remainder = fmod($price, $this->tick_size);
        return abs($remainder) < 0.00000001; // 浮点数精度误差
    }

    /**
     * 格式化价格
     */
    public function formatPrice(float $price): float
    {
        return round($price / $this->tick_size) * $this->tick_size;
    }

    /**
     * 获取合约配置信息
     */
    public function getConfig(): array
    {
        return [
            'symbol' => $this->symbol,
            'base_coin' => $this->base_coin,
            'quote_coin' => $this->quote_coin,
            'contract_type' => $this->contract_type,
            'contract_type_text' => $this->contract_type_text,
            'settlement_coin' => $this->settlement_coin,
            'contract_size' => $this->contract_size,
            'tick_size' => $this->tick_size,
            'max_leverage' => $this->max_leverage,
            'maintenance_margin_rate' => $this->maintenance_margin_rate,
            'maker_fee' => $this->maker_fee,
            'taker_fee' => $this->taker_fee,
            'funding_interval' => $this->funding_interval,
            'is_active' => $this->is_active
        ];
    }

    /**
     * 获取资金费率（永续合约）
     */
    public function getCurrentFundingRate(): float
    {
        if ($this->contract_type != self::TYPE_PERPETUAL) {
            return 0;
        }

        // 这里应该从资金费率表获取，暂时返回模拟值
        return 0.0001; // 0.01%
    }

    /**
     * 计算下次资金费率结算时间
     */
    public function getNextFundingTime(): int
    {
        if ($this->contract_type != self::TYPE_PERPETUAL) {
            return 0;
        }

        $now = time();
        $interval = $this->funding_interval;
        $nextTime = ceil($now / $interval) * $interval;
        
        return $nextTime;
    }

    /**
     * 获取合约的24小时统计
     */
    public function get24hStats(): array
    {
        // 从K线数据获取统计
        $kline = Kline::get24hStats($this->symbol);
        
        return [
            'symbol' => $this->symbol,
            'price' => $kline['close'],
            'open' => $kline['open'],
            'high' => $kline['high'],
            'low' => $kline['low'],
            'volume' => $kline['volume'],
            'amount' => $kline['amount'],
            'change' => $kline['change'],
            'change_percent' => $kline['change_percent'],
            'funding_rate' => $this->getCurrentFundingRate(),
            'next_funding_time' => $this->getNextFundingTime()
        ];
    }

    /**
     * 获取合约列表（用于前端显示）
     */
    public static function getContractsList(): array
    {
        $contracts = self::getActiveContracts();
        $result = [];
        
        foreach ($contracts as $contract) {
            $stats = (new self($contract))->get24hStats();
            
            $result[] = [
                'symbol' => $contract['symbol'],
                'base_coin' => $contract['base_coin'],
                'quote_coin' => $contract['quote_coin'],
                'contract_type' => $contract['contract_type'],
                'price' => $stats['price'],
                'change_percent' => $stats['change_percent'],
                'volume' => $stats['volume'],
                'funding_rate' => $stats['funding_rate'],
                'max_leverage' => $contract['max_leverage'],
                'is_active' => $contract['is_active']
            ];
        }
        
        return $result;
    }

    /**
     * 创建新的期货合约
     */
    public static function createContract(array $data): array
    {
        try {
            // 验证数据
            $required = ['symbol', 'base_coin', 'quote_coin', 'contract_type'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return ['code' => 0, 'msg' => "字段 {$field} 不能为空"];
                }
            }

            // 检查合约是否已存在
            $exists = self::where('symbol', $data['symbol'])->find();
            if ($exists) {
                return ['code' => 0, 'msg' => '合约已存在'];
            }

            // 设置默认值
            $defaults = [
                'settlement_coin' => 'USDT',
                'contract_size' => 1.0,
                'tick_size' => 0.01,
                'max_leverage' => 100,
                'maintenance_margin_rate' => 0.005,
                'maker_fee' => 0.0002,
                'taker_fee' => 0.0004,
                'funding_interval' => 28800, // 8小时
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $data = array_merge($defaults, $data);
            
            $contract = self::create($data);
            
            return [
                'code' => 1,
                'msg' => '合约创建成功',
                'data' => $contract->toArray()
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '创建失败：' . $e->getMessage()];
        }
    }

    /**
     * 更新合约配置
     */
    public function updateConfig(array $data): array
    {
        try {
            $allowedFields = [
                'tick_size', 'max_leverage', 'maintenance_margin_rate',
                'maker_fee', 'taker_fee', 'funding_interval', 'is_active'
            ];

            $updateData = [];
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }

            if (empty($updateData)) {
                return ['code' => 0, 'msg' => '没有需要更新的数据'];
            }

            $updateData['updated_at'] = date('Y-m-d H:i:s');
            
            $this->save($updateData);
            
            return [
                'code' => 1,
                'msg' => '配置更新成功',
                'data' => $this->toArray()
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '更新失败：' . $e->getMessage()];
        }
    }

    /**
     * 检查合约是否可交易
     */
    public function isTradeEnabled(): bool
    {
        return $this->is_active;
    }

    /**
     * 获取合约的最新价格
     */
    public function getLatestPrice(): float
    {
        $latestKline = Kline::getLatestKline($this->symbol, '1m');
        return $latestKline ? $latestKline['close_price'] : 0;
    }

    /**
     * 获取标记价格（用于强平计算）
     */
    public function getMarkPrice(): float
    {
        // 标记价格通常是现货价格和期货价格的加权平均
        // 这里简化为使用最新价格
        return $this->getLatestPrice();
    }
}
