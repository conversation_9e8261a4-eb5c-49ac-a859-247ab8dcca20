/**
 * GVD交易平台 - 主要JavaScript文件
 * 包含全局函数、工具类、通用组件
 */

// 全局配置
window.GVD = {
    config: {
        apiBaseUrl: '/api',
        wsUrl: 'ws://localhost:8080',
        uploadMaxSize: 10 * 1024 * 1024, // 10MB
        tokenKey: 'token',
        userInfoKey: 'user_info'
    },
    
    // 工具函数
    utils: {
        // 格式化数字
        formatNumber: function(num, decimals = 2) {
            if (isNaN(num)) return '0';
            
            const number = parseFloat(num);
            if (number >= 1000000000) {
                return (number / 1000000000).toFixed(decimals) + 'B';
            } else if (number >= 1000000) {
                return (number / 1000000).toFixed(decimals) + 'M';
            } else if (number >= 1000) {
                return (number / 1000).toFixed(decimals) + 'K';
            }
            return number.toFixed(decimals);
        },
        
        // 格式化货币
        formatCurrency: function(amount, symbol = '$', decimals = 2) {
            return symbol + this.formatNumber(amount, decimals);
        },
        
        // 格式化时间
        formatTime: function(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleString('zh-CN');
        },
        
        // 时间差显示
        timeAgo: function(timestamp) {
            const now = new Date();
            const time = new Date(timestamp);
            const diff = Math.floor((now - time) / 1000);
            
            if (diff < 60) return '刚刚';
            if (diff < 3600) return Math.floor(diff / 60) + '分钟前';
            if (diff < 86400) return Math.floor(diff / 3600) + '小时前';
            if (diff < 2592000) return Math.floor(diff / 86400) + '天前';
            return time.toLocaleDateString('zh-CN');
        },
        
        // 复制到剪贴板
        copyToClipboard: function(text) {
            if (navigator.clipboard) {
                return navigator.clipboard.writeText(text).then(() => {
                    this.showToast('已复制到剪贴板', 'success');
                }).catch(() => {
                    this.fallbackCopyTextToClipboard(text);
                });
            } else {
                this.fallbackCopyTextToClipboard(text);
            }
        },
        
        // 降级复制方案
        fallbackCopyTextToClipboard: function(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.top = '-9999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                document.execCommand('copy');
                this.showToast('已复制到剪贴板', 'success');
            } catch (err) {
                this.showToast('复制失败', 'error');
            }
            
            document.body.removeChild(textArea);
        },
        
        // 显示提示消息
        showToast: function(message, type = 'info', duration = 3000) {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.textContent = message;
            
            // 添加样式
            Object.assign(toast.style, {
                position: 'fixed',
                top: '20px',
                right: '20px',
                padding: '12px 20px',
                borderRadius: '8px',
                color: 'white',
                fontSize: '14px',
                fontWeight: '500',
                zIndex: '9999',
                opacity: '0',
                transform: 'translateX(100%)',
                transition: 'all 0.3s ease'
            });
            
            // 设置背景色
            const colors = {
                success: '#2ecc71',
                error: '#e74c3c',
                warning: '#f39c12',
                info: '#3498db'
            };
            toast.style.backgroundColor = colors[type] || colors.info;
            
            document.body.appendChild(toast);
            
            // 显示动画
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 100);
            
            // 自动隐藏
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, duration);
        },
        
        // 验证邮箱
        validateEmail: function(email) {
            const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        },
        
        // 验证手机号
        validatePhone: function(phone) {
            const re = /^1[3-9]\d{9}$/;
            return re.test(phone);
        },
        
        // 生成随机字符串
        randomString: function(length = 8) {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            let result = '';
            for (let i = 0; i < length; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        },
        
        // 防抖函数
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
        
        // 节流函数
        throttle: function(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        }
    },
    
    // API请求封装
    api: {
        // 获取token
        getToken: function() {
            return localStorage.getItem(GVD.config.tokenKey);
        },
        
        // 设置token
        setToken: function(token) {
            localStorage.setItem(GVD.config.tokenKey, token);
        },
        
        // 清除token
        clearToken: function() {
            localStorage.removeItem(GVD.config.tokenKey);
            localStorage.removeItem(GVD.config.userInfoKey);
        },
        
        // 通用请求方法
        request: async function(url, options = {}) {
            const token = this.getToken();
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    ...(token && { 'Authorization': `Bearer ${token}` })
                }
            };
            
            const finalOptions = {
                ...defaultOptions,
                ...options,
                headers: {
                    ...defaultOptions.headers,
                    ...options.headers
                }
            };
            
            try {
                const response = await fetch(GVD.config.apiBaseUrl + url, finalOptions);
                const result = await response.json();
                
                // 处理认证失败
                if (response.status === 401) {
                    this.clearToken();
                    if (window.location.pathname !== '/auth/login.html') {
                        window.location.href = '/auth/login.html';
                    }
                    return;
                }
                
                return result;
            } catch (error) {
                console.error('API请求失败:', error);
                GVD.utils.showToast('网络错误，请稍后重试', 'error');
                throw error;
            }
        },
        
        // GET请求
        get: function(url, params = {}) {
            const queryString = new URLSearchParams(params).toString();
            const fullUrl = queryString ? `${url}?${queryString}` : url;
            return this.request(fullUrl);
        },
        
        // POST请求
        post: function(url, data = {}) {
            return this.request(url, {
                method: 'POST',
                body: JSON.stringify(data)
            });
        },
        
        // PUT请求
        put: function(url, data = {}) {
            return this.request(url, {
                method: 'PUT',
                body: JSON.stringify(data)
            });
        },
        
        // DELETE请求
        delete: function(url) {
            return this.request(url, {
                method: 'DELETE'
            });
        }
    },
    
    // WebSocket管理
    ws: {
        connection: null,
        reconnectAttempts: 0,
        maxReconnectAttempts: 5,
        reconnectInterval: 5000,
        
        // 连接WebSocket
        connect: function(url = GVD.config.wsUrl) {
            try {
                this.connection = new WebSocket(url);
                
                this.connection.onopen = () => {
                    console.log('WebSocket连接已建立');
                    this.reconnectAttempts = 0;
                    this.onOpen && this.onOpen();
                };
                
                this.connection.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        this.onMessage && this.onMessage(data);
                    } catch (error) {
                        console.error('WebSocket消息解析失败:', error);
                    }
                };
                
                this.connection.onclose = () => {
                    console.log('WebSocket连接已关闭');
                    this.onClose && this.onClose();
                    this.reconnect();
                };
                
                this.connection.onerror = (error) => {
                    console.error('WebSocket错误:', error);
                    this.onError && this.onError(error);
                };
                
            } catch (error) {
                console.error('WebSocket连接失败:', error);
            }
        },
        
        // 重连
        reconnect: function() {
            if (this.reconnectAttempts < this.maxReconnectAttempts) {
                this.reconnectAttempts++;
                console.log(`尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                setTimeout(() => {
                    this.connect();
                }, this.reconnectInterval);
            }
        },
        
        // 发送消息
        send: function(data) {
            if (this.connection && this.connection.readyState === WebSocket.OPEN) {
                this.connection.send(JSON.stringify(data));
            } else {
                console.warn('WebSocket未连接');
            }
        },
        
        // 关闭连接
        close: function() {
            if (this.connection) {
                this.connection.close();
                this.connection = null;
            }
        }
    }
};

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化全局事件监听
    initGlobalEvents();
    
    // 检查登录状态
    checkAuthStatus();
});

// 初始化全局事件
function initGlobalEvents() {
    // 全局点击事件
    document.addEventListener('click', function(e) {
        // 处理复制按钮
        if (e.target.classList.contains('copy-btn')) {
            const text = e.target.dataset.copy;
            if (text) {
                GVD.utils.copyToClipboard(text);
            }
        }
        
        // 处理确认按钮
        if (e.target.classList.contains('confirm-btn')) {
            const message = e.target.dataset.confirm || '确定要执行此操作吗？';
            if (!confirm(message)) {
                e.preventDefault();
                e.stopPropagation();
            }
        }
    });
    
    // 全局表单验证
    document.addEventListener('submit', function(e) {
        const form = e.target;
        if (form.classList.contains('validate-form')) {
            if (!validateForm(form)) {
                e.preventDefault();
            }
        }
    });
}

// 检查认证状态
function checkAuthStatus() {
    const token = GVD.api.getToken();
    const currentPath = window.location.pathname;
    
    // 需要登录的页面
    const protectedPaths = ['/user/', '/trade/', '/ido/'];
    const isProtectedPath = protectedPaths.some(path => currentPath.includes(path));
    
    if (isProtectedPath && !token) {
        window.location.href = '/auth/login.html';
    }
}

// 表单验证
function validateForm(form) {
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            showFieldError(input, '此字段为必填项');
            isValid = false;
        } else if (input.type === 'email' && !GVD.utils.validateEmail(input.value)) {
            showFieldError(input, '请输入有效的邮箱地址');
            isValid = false;
        } else if (input.type === 'tel' && !GVD.utils.validatePhone(input.value)) {
            showFieldError(input, '请输入有效的手机号码');
            isValid = false;
        } else {
            clearFieldError(input);
        }
    });
    
    return isValid;
}

// 显示字段错误
function showFieldError(input, message) {
    clearFieldError(input);
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.textContent = message;
    errorDiv.style.color = '#e74c3c';
    errorDiv.style.fontSize = '12px';
    errorDiv.style.marginTop = '5px';
    
    input.style.borderColor = '#e74c3c';
    input.parentNode.appendChild(errorDiv);
}

// 清除字段错误
function clearFieldError(input) {
    const existingError = input.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
    input.style.borderColor = '';
}

// 导出全局对象
window.GVD = GVD;
