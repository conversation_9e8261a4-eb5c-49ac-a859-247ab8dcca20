{extend name="index/layout" /}

{block name="css"}
<style>
.contract-container {
    padding: 20px 0;
    background: #f8f9fa;
    min-height: 100vh;
}

.contract-header {
    background: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.price-display {
    text-align: center;
    padding: 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    margin-bottom: 30px;
}

.price-value {
    font-size: 48px;
    font-weight: bold;
    margin: 10px 0;
}

.price-change {
    font-size: 18px;
}

.contract-form {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
}

.direction-buttons {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.direction-btn {
    flex: 1;
    padding: 15px;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 10px;
    cursor: pointer;
    text-align: center;
    font-weight: bold;
    transition: all 0.3s;
}

.direction-btn.up {
    border-color: #28a745;
    color: #28a745;
}

.direction-btn.down {
    border-color: #dc3545;
    color: #dc3545;
}

.direction-btn.active.up {
    background: #28a745;
    color: white;
}

.direction-btn.active.down {
    background: #dc3545;
    color: white;
}

.time-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-bottom: 20px;
}

.time-btn {
    padding: 12px;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    text-align: center;
    font-weight: 500;
    transition: all 0.3s;
}

.time-btn.active {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

.submit-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.3s;
}

.submit-btn:hover {
    transform: translateY(-2px);
}

.submit-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.balance-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: center;
}

.active-contracts {
    background: white;
    padding: 20px;
    border-radius: 10px;
    margin-top: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.contract-item {
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contract-info {
    flex: 1;
}

.contract-timer {
    font-weight: bold;
    color: #667eea;
}

.profit-loss {
    font-weight: bold;
}

.profit-loss.profit {
    color: #28a745;
}

.profit-loss.loss {
    color: #dc3545;
}
</style>
{/block}

{block name="content"}
<div class="contract-container">
    <div class="container">
        <div class="row">
            <div class="col-md-8">
                <!-- 价格显示 -->
                <div class="price-display">
                    <h3 id="current-coin">BTC/USDT</h3>
                    <div class="price-value" id="current-price">$45,000.00</div>
                    <div class="price-change" id="price-change">+2.5% (+$1,200.00)</div>
                </div>

                <!-- K线图区域 -->
                <div class="chart-container">
                    <div id="kline-chart" style="height: 400px; background: white; border-radius: 10px;"></div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- 合约交易表单 -->
                <div class="contract-form">
                    <h4 class="mb-4">合约交易</h4>

                    <!-- 余额信息 -->
                    <div class="balance-info">
                        <strong>可用余额: {$user_balance|default=0} USDT</strong>
                    </div>

                    <form id="contract-form">
                        <!-- 币种选择 -->
                        <div class="form-group">
                            <label>选择币种</label>
                            <select class="form-control" name="coin_name" id="coin-select">
                                <option value="BTC">BTC/USDT</option>
                                <option value="ETH">ETH/USDT</option>
                                <option value="LTC">LTC/USDT</option>
                                <option value="EOS">EOS/USDT</option>
                                <option value="XRP">XRP/USDT</option>
                            </select>
                        </div>

                        <!-- 交易方向 -->
                        <div class="form-group">
                            <label>交易方向</label>
                            <div class="direction-buttons">
                                <div class="direction-btn up" data-direction="1">
                                    <i class="fas fa-arrow-up"></i><br>买涨
                                </div>
                                <div class="direction-btn down" data-direction="2">
                                    <i class="fas fa-arrow-down"></i><br>买跌
                                </div>
                            </div>
                            <input type="hidden" name="direction" id="direction-input">
                        </div>

                        <!-- 交易金额 -->
                        <div class="form-group">
                            <label>交易金额 (USDT)</label>
                            <input type="number" class="form-control" name="amount" placeholder="最小10 USDT" min="10" max="10000" step="1">
                        </div>

                        <!-- 合约时长 -->
                        <div class="form-group">
                            <label>合约时长</label>
                            <div class="time-buttons">
                                <div class="time-btn" data-time="1">1分钟</div>
                                <div class="time-btn" data-time="3">3分钟</div>
                                <div class="time-btn" data-time="5">5分钟</div>
                                <div class="time-btn" data-time="10">10分钟</div>
                                <div class="time-btn" data-time="15">15分钟</div>
                                <div class="time-btn" data-time="30">30分钟</div>
                            </div>
                            <input type="hidden" name="time" id="time-input">
                        </div>

                        <button type="submit" class="submit-btn" disabled>
                            <i class="fas fa-rocket"></i> 创建合约
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- 活跃合约 -->
        {if condition="$active_contracts"}
        <div class="active-contracts">
            <h4 class="mb-3">进行中的合约</h4>
            <div id="active-contracts-list">
                {volist name="active_contracts" id="contract"}
                <div class="contract-item" data-id="{$contract.id}">
                    <div class="contract-info">
                        <strong>{$contract.coin_name}/USDT</strong>
                        <span class="badge badge-{$contract.direction == 1 ? 'success' : 'danger'}">
                            {$contract.direction == 1 ? '买涨' : '买跌'}
                        </span>
                        <br>
                        <small>金额: {$contract.amount} USDT | 买入价: ${$contract.buy_price}</small>
                    </div>
                    <div class="contract-timer" data-end-time="{$contract.end_time}">
                        计算中...
                    </div>
                </div>
                {/volist}
            </div>
        </div>
        {/if}
    </div>
</div>
{/block}

{block name="js"}
<script>
$(document).ready(function() {
    let selectedDirection = null;
    let selectedTime = null;
    
    // 方向选择
    $('.direction-btn').click(function() {
        $('.direction-btn').removeClass('active');
        $(this).addClass('active');
        selectedDirection = $(this).data('direction');
        $('#direction-input').val(selectedDirection);
        checkFormValid();
    });
    
    // 时长选择
    $('.time-btn').click(function() {
        $('.time-btn').removeClass('active');
        $(this).addClass('active');
        selectedTime = $(this).data('time');
        $('#time-input').val(selectedTime);
        checkFormValid();
    });
    
    // 检查表单有效性
    function checkFormValid() {
        const amount = $('input[name="amount"]').val();
        const isValid = selectedDirection && selectedTime && amount >= 10;
        $('.submit-btn').prop('disabled', !isValid);
    }
    
    $('input[name="amount"]').on('input', checkFormValid);
    
    // 提交表单
    $('#contract-form').submit(function(e) {
        e.preventDefault();
        
        const formData = {
            coin_name: $('#coin-select').val(),
            direction: selectedDirection,
            amount: parseFloat($('input[name="amount"]').val()),
            time: selectedTime
        };
        
        $.post('/contract/create', formData, function(response) {
            if (response.code === 1) {
                alert('合约创建成功！');
                location.reload();
            } else {
                alert(response.msg);
            }
        });
    });
    
    // 更新价格
    function updatePrice() {
        const coinName = $('#coin-select').val();
        $.get('/contract/price', {coin_name: coinName}, function(response) {
            if (response.code === 1) {
                $('#current-price').text('$' + response.data.price.toFixed(2));
            }
        });
    }
    
    // 币种切换
    $('#coin-select').change(function() {
        const coinName = $(this).val();
        $('#current-coin').text(coinName + '/USDT');
        updatePrice();
    });
    
    // 定时更新价格
    setInterval(updatePrice, 5000);
    
    // 更新合约倒计时
    function updateContractTimers() {
        $('.contract-item').each(function() {
            const contractId = $(this).data('id');
            const timerElement = $(this).find('.contract-timer');
            
            $.get('/contract/timer', {id: contractId}, function(response) {
                if (response.code === 1) {
                    const remainingTime = response.data.remaining_time;
                    const minutes = Math.floor(remainingTime / 60);
                    const seconds = remainingTime % 60;
                    timerElement.text(minutes + ':' + (seconds < 10 ? '0' : '') + seconds);
                } else if (response.code === 2) {
                    timerElement.text('结算中...');
                }
            });
        });
    }
    
    // 定时更新倒计时
    if ($('.contract-item').length > 0) {
        setInterval(updateContractTimers, 1000);
    }
});
</script>
{/block}
