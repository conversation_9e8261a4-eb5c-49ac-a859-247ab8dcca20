<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GVD - 全球领先的数字货币交易平台</title>
    <meta name="description" content="GVD是全球领先的数字货币交易平台，提供比特币、以太坊等数字货币的现货、杠杆、期货交易服务。安全可靠，专业高效。">
    <meta name="keywords" content="GVD,数字货币,比特币,以太坊,交易平台,杠杆交易,期货合约">
    <link rel="stylesheet" href="/static/css/app.css">
    <link rel="stylesheet" href="/static/css/customer-service.css">
    <link rel="icon" href="/static/images/favicon.ico">
    <!-- 预加载关键资源 -->
    <link rel="preload" href="/static/css/app.css" as="style">
    <link rel="preload" href="/static/css/customer-service.css" as="style">
    <link rel="preload" href="/static/js/app.js" as="script">
    <link rel="preload" href="/static/js/customer-service.js" as="script">
</head>
<body>
    <!-- 火币风格头部导航 -->
    <header class="header">
        <div class="header-content">
            <a href="/" class="logo">GVD</a>

            <nav class="nav">
                <div class="nav-item">
                    <a href="/" class="nav-link active">首页</a>
                </div>
                <div class="nav-item">
                    <a href="/trade" class="nav-link">现货交易</a>
                </div>
                <div class="nav-item">
                    <a href="/leverage" class="nav-link">杠杆交易</a>
                </div>
                <div class="nav-item">
                    <a href="/futures" class="nav-link">合约交易</a>
                </div>
                <div class="nav-item">
                    <a href="/market" class="nav-link">行情</a>
                </div>
                <div class="nav-item">
                    <a href="/wallet" class="nav-link">资产</a>
                </div>
            </nav>

            <div class="user-menu" id="userMenu">
                <!-- 未登录状态 -->
                <div class="guest-menu">
                    <a href="/login" class="btn btn-outline btn-sm">登录</a>
                    <a href="/register" class="btn btn-primary btn-sm">注册</a>
                </div>
                <!-- 已登录状态 -->
                <div class="user-info" style="display: none;">
                    <div class="user-avatar">G</div>
                    <div class="user-details">
                        <div class="user-name">用户名</div>
                        <div class="user-balance">≈ 0.00 USDT</div>
                    </div>
                </div>

                <!-- 移动端菜单按钮 -->
                <button class="mobile-menu-toggle" onclick="app.toggleMobileMenu()" style="display: none;">
                    ☰
                </button>
            </div>
        </div>

        <!-- 移动端导航菜单 -->
        <div class="mobile-menu" id="mobileMenu">
            <nav class="mobile-nav">
                <a href="/" class="nav-link">首页</a>
                <a href="/trade" class="nav-link">现货交易</a>
                <a href="/leverage" class="nav-link">杠杆交易</a>
                <a href="/futures" class="nav-link">合约交易</a>
                <a href="/market" class="nav-link">行情</a>
                <a href="/wallet" class="nav-link">资产</a>
            </nav>
        </div>
        </div>
    </header>

    <!-- 火币风格主要内容 -->
    <main class="main-content">
        <!-- 英雄区域 -->
        <section class="hero-section">
            <div class="hero-bg"></div>
            <div class="container">
                <div class="hero-content">
                    <div class="hero-text">
                        <h1 class="hero-title">
                            <span class="gradient-text">全球领先</span>的数字货币交易平台
                        </h1>
                        <p class="hero-subtitle">
                            安全可靠 · 专业高效 · 7×24小时服务<br>
                            支持现货、杠杆、合约交易，满足您的多样化投资需求
                        </p>
                        <div class="hero-stats">
                            <div class="stat-item">
                                <div class="stat-number">1000万+</div>
                                <div class="stat-label">全球用户</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">$500亿+</div>
                                <div class="stat-label">24H交易量</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">200+</div>
                                <div class="stat-label">交易对</div>
                            </div>
                        </div>
                        <div class="hero-actions">
                            <a href="/register" class="btn btn-primary btn-xl">
                                <span class="icon">🚀</span>
                                立即注册
                            </a>
                            <a href="/trade" class="btn btn-outline btn-xl">开始交易</a>
                        </div>
                    </div>
                    <div class="hero-visual">
                        <div class="trading-card">
                            <div class="card-header">
                                <h3>实时行情</h3>
                                <span class="status-indicator"></span>
                            </div>
                            <div class="price-display">
                                <div class="price-item">
                                    <span class="symbol">BTC/USDT</span>
                                    <span class="price" id="btc-price">$50,000.00</span>
                                    <span class="change positive" id="btc-change">+2.5%</span>
                                </div>
                                <div class="price-item">
                                    <span class="symbol">ETH/USDT</span>
                                    <span class="price" id="eth-price">$3,000.00</span>
                                    <span class="change positive" id="eth-change">+1.8%</span>
                                </div>
                                <div class="price-item">
                                    <span class="symbol">BNB/USDT</span>
                                    <span class="price" id="bnb-price">$300.00</span>
                                    <span class="change negative" id="bnb-change">-0.5%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 热门交易对 -->
        <section class="market-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">热门交易对</h2>
                    <div class="market-tabs">
                        <button class="tab-btn active" data-tab="spot">现货</button>
                        <button class="tab-btn" data-tab="futures">合约</button>
                        <button class="tab-btn" data-tab="leverage">杠杆</button>
                    </div>
                </div>

                <div class="market-table-container">
                    <div class="market-table">
                        <div class="table-header">
                            <div class="th">交易对</div>
                            <div class="th">最新价格</div>
                            <div class="th">24H涨跌</div>
                            <div class="th">24H成交量</div>
                            <div class="th">操作</div>
                        </div>
                        <div class="table-body" id="marketTableBody">
                            <!-- 动态生成市场数据 -->
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 产品特色 -->
        <section class="features-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">为什么选择GVD</h2>
                    <p class="section-subtitle">专业、安全、高效的数字货币交易服务</p>
                </div>

                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🛡️</div>
                        <h3 class="feature-title">安全可靠</h3>
                        <p class="feature-desc">多重安全防护，冷热钱包分离，资金安全有保障</p>
                        <div class="feature-highlight">银行级安全</div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <h3 class="feature-title">极速交易</h3>
                        <p class="feature-desc">毫秒级撮合引擎，订单执行快速稳定</p>
                        <div class="feature-highlight">99.9%可用性</div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">📊</div>
                        <h3 class="feature-title">专业工具</h3>
                        <p class="feature-desc">丰富的技术指标，专业的图表分析工具</p>
                        <div class="feature-highlight">TradingView</div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">🎯</div>
                        <h3 class="feature-title">多样产品</h3>
                        <p class="feature-desc">现货、杠杆、合约，满足不同投资需求</p>
                        <div class="feature-highlight">200+交易对</div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">💰</div>
                        <h3 class="feature-title">低费率</h3>
                        <p class="feature-desc">行业领先的低费率，节省您的交易成本</p>
                        <div class="feature-highlight">0.1%起</div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">🌍</div>
                        <h3 class="feature-title">全球服务</h3>
                        <p class="feature-desc">7×24小时客服支持，多语言服务</p>
                        <div class="feature-highlight">24/7在线</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 数据统计区域 -->
        <section class="stats-section">
            <div class="container">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">👥</div>
                        <div class="stat-content">
                            <div class="stat-number" id="totalUsers">1,000万+</div>
                            <div class="stat-label">全球用户</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">💰</div>
                        <div class="stat-content">
                            <div class="stat-number" id="totalVolume">$500亿+</div>
                            <div class="stat-label">24H交易量</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">🔒</div>
                        <div class="stat-content">
                            <div class="stat-number" id="securityLevel">99.9%</div>
                            <div class="stat-label">安全保障</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">⚡</div>
                        <div class="stat-content">
                            <div class="stat-number" id="responseTime">&lt;10ms</div>
                            <div class="stat-label">响应时间</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 新闻公告区域 -->
        <section class="news-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">最新动态</h2>
                    <a href="/news" class="more-link">查看更多 →</a>
                </div>

                <div class="news-grid">
                    <div class="news-card featured">
                        <div class="news-badge">重要</div>
                        <div class="news-content">
                            <h3 class="news-title">GVD平台升级公告</h3>
                            <p class="news-desc">为提供更好的交易体验，平台将于本周末进行系统升级...</p>
                            <div class="news-meta">
                                <span class="news-date">2024-08-04</span>
                                <span class="news-category">系统公告</span>
                            </div>
                        </div>
                    </div>

                    <div class="news-card">
                        <div class="news-content">
                            <h3 class="news-title">新增交易对上线通知</h3>
                            <p class="news-desc">平台新增多个热门交易对，为用户提供更多投资选择...</p>
                            <div class="news-meta">
                                <span class="news-date">2024-08-03</span>
                                <span class="news-category">产品更新</span>
                            </div>
                        </div>
                    </div>

                    <div class="news-card">
                        <div class="news-content">
                            <h3 class="news-title">手续费优惠活动</h3>
                            <p class="news-desc">限时手续费减免活动，新用户享受更多优惠...</p>
                            <div class="news-meta">
                                <span class="news-date">2024-08-02</span>
                                <span class="news-category">活动公告</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

            <!-- 交易数据 -->
            <section class="stats-section">
                <h2>平台数据</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">1,000,000+</div>
                        <div class="stat-label">注册用户</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">$10B+</div>
                        <div class="stat-label">累计交易额</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">50+</div>
                        <div class="stat-label">交易对</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">24/7</div>
                        <div class="stat-label">客服支持</div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>产品</h4>
                    <ul>
                        <li><a href="/trade">现货交易</a></li>
                        <li><a href="/leverage">杠杆交易</a></li>
                        <li><a href="/futures">期货合约</a></li>
                        <li><a href="/wallet">数字钱包</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>服务</h4>
                    <ul>
                        <li><a href="/api">API文档</a></li>
                        <li><a href="/help">帮助中心</a></li>
                        <li><a href="/fees">费率说明</a></li>
                        <li><a href="/security">安全中心</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>公司</h4>
                    <ul>
                        <li><a href="/about">关于我们</a></li>
                        <li><a href="/careers">招聘信息</a></li>
                        <li><a href="/news">新闻公告</a></li>
                        <li><a href="/contact">联系我们</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>关注我们</h4>
                    <div class="social-links">
                        <a href="#" class="social-link">微信</a>
                        <a href="#" class="social-link">微博</a>
                        <a href="#" class="social-link">Telegram</a>
                        <a href="#" class="social-link">Twitter</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 GVD Trading Platform. All rights reserved.</p>
                <div class="footer-links">
                    <a href="/privacy">隐私政策</a>
                    <a href="/terms">服务条款</a>
                    <a href="/risk">风险提示</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="/static/js/app.js"></script>
    <script src="/static/js/customer-service.js"></script>
    
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化应用
            window.app = new GVDApp();

            // 添加页面加载动画
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.5s ease';

            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);

            // 预加载关键图片
            const preloadImages = [
                '/static/images/logo.svg',
                '/static/images/favicon.svg'
            ];

            preloadImages.forEach(src => {
                const img = new Image();
                img.src = src;
            });

            // 添加键盘快捷键支持
            document.addEventListener('keydown', function(e) {
                // Ctrl/Cmd + K 打开搜索
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    // 这里可以添加搜索功能
                    console.log('搜索快捷键触发');
                }

                // ESC 关闭移动端菜单
                if (e.key === 'Escape') {
                    const mobileMenu = document.getElementById('mobileMenu');
                    if (mobileMenu && mobileMenu.classList.contains('active')) {
                        mobileMenu.classList.remove('active');
                    }
                }
            });

            // 添加网络状态监听
            window.addEventListener('online', function() {
                app.showNotification('网络连接已恢复', 'success');
                app.updatePrices();
            });

            window.addEventListener('offline', function() {
                app.showNotification('网络连接已断开', 'warning');
            });

            // 添加页面可见性监听
            document.addEventListener('visibilitychange', function() {
                if (document.visibilityState === 'visible') {
                    // 页面重新可见时更新数据
                    app.updatePrices();
                } else {
                    // 页面隐藏时暂停更新
                    if (app.priceUpdateInterval) {
                        clearInterval(app.priceUpdateInterval);
                    }
                }
            });
        });

        // 添加全局错误处理
        window.addEventListener('error', function(e) {
            console.error('页面错误:', e.error);
            // 可以在这里添加错误上报逻辑
        });

        // 添加未处理的Promise错误处理
        window.addEventListener('unhandledrejection', function(e) {
            console.error('未处理的Promise错误:', e.reason);
            e.preventDefault();
        });
    </script>

    <style>
        /* 首页特定样式 */
        .hero-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            align-items: center;
            padding: 80px 0;
            background: linear-gradient(135deg, #1e2329 0%, #2b3139 100%);
            border-radius: 12px;
            margin: 40px 0;
        }
        
        .hero-content h1 {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #f0b90b, #ffd700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .hero-content p {
            font-size: 18px;
            color: #848e9c;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .hero-buttons {
            display: flex;
            gap: 20px;
        }
        
        .btn-large {
            padding: 15px 30px;
            font-size: 16px;
            font-weight: 600;
        }
        
        .market-section, .features-section, .stats-section {
            margin: 60px 0;
        }
        
        .market-section h2, .features-section h2, .stats-section h2 {
            text-align: center;
            margin-bottom: 40px;
            font-size: 32px;
            color: #ffffff;
        }
        
        .market-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .market-item {
            background: #1e2329;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #2b3139;
        }
        
        .market-symbol {
            font-size: 16px;
            color: #848e9c;
            margin-bottom: 10px;
        }
        
        .market-price {
            font-size: 24px;
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 5px;
        }
        
        .market-change {
            font-size: 14px;
            font-weight: 500;
        }
        
        .market-change.positive {
            color: #02c076;
        }
        
        .market-change.negative {
            color: #f84960;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
        }
        
        .feature-item {
            background: #1e2329;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid #2b3139;
            transition: transform 0.3s ease;
        }
        
        .feature-item:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        .feature-item h3 {
            font-size: 20px;
            margin-bottom: 15px;
            color: #ffffff;
        }
        
        .feature-item p {
            color: #848e9c;
            line-height: 1.6;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: #f0b90b;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 16px;
            color: #848e9c;
        }
        
        .footer {
            background: #1e2329;
            border-top: 1px solid #2b3139;
            padding: 40px 0 20px;
            margin-top: 80px;
        }
        
        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .footer-section h4 {
            color: #ffffff;
            margin-bottom: 15px;
            font-size: 16px;
        }
        
        .footer-section ul {
            list-style: none;
        }
        
        .footer-section ul li {
            margin-bottom: 8px;
        }
        
        .footer-section ul li a {
            color: #848e9c;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .footer-section ul li a:hover {
            color: #f0b90b;
        }
        
        .social-links {
            display: flex;
            gap: 15px;
        }
        
        .social-link {
            color: #848e9c;
            text-decoration: none;
            padding: 8px 12px;
            border: 1px solid #2b3139;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        .social-link:hover {
            color: #f0b90b;
            border-color: #f0b90b;
        }
        
        .footer-bottom {
            border-top: 1px solid #2b3139;
            padding-top: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #848e9c;
        }
        
        .footer-links {
            display: flex;
            gap: 20px;
        }
        
        .footer-links a {
            color: #848e9c;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .footer-links a:hover {
            color: #f0b90b;
        }
        
        @media (max-width: 768px) {
            .hero-section {
                grid-template-columns: 1fr;
                text-align: center;
                padding: 40px 20px;
            }
            
            .hero-content h1 {
                font-size: 32px;
            }
            
            .hero-buttons {
                justify-content: center;
            }
            
            .footer-bottom {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
        }
    </style>
</body>
</html>
