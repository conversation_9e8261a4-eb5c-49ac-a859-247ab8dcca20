<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#007bff">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="交易平台">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    
    <title>{$title|default='数字货币交易平台'}</title>
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">
    
    <!-- iOS Icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="/static/images/icons/icon-180x180.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/static/images/icons/icon-152x152.png">
    <link rel="apple-touch-icon" sizes="144x144" href="/static/images/icons/icon-144x144.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/static/images/icons/icon-120x120.png">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="/static/images/icons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/static/images/icons/favicon-16x16.png">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #007bff;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --border-color: #dee2e6;
            --text-muted: #6c757d;
            --safe-area-inset-top: env(safe-area-inset-top);
            --safe-area-inset-bottom: env(safe-area-inset-bottom);
        }

        * {
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--light-color);
            padding-top: var(--safe-area-inset-top);
            padding-bottom: var(--safe-area-inset-bottom);
            overflow-x: hidden;
        }

        .mobile-header {
            position: fixed;
            top: var(--safe-area-inset-top);
            left: 0;
            right: 0;
            height: 56px;
            background: white;
            border-bottom: 1px solid var(--border-color);
            z-index: 1000;
            display: flex;
            align-items: center;
            padding: 0 16px;
        }

        .mobile-header .header-left,
        .mobile-header .header-right {
            flex: 0 0 auto;
            width: 40px;
        }

        .mobile-header .header-center {
            flex: 1;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            color: var(--dark-color);
        }

        .mobile-header .btn-header {
            width: 40px;
            height: 40px;
            border: none;
            background: none;
            color: var(--dark-color);
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
        }

        .mobile-header .btn-header:active {
            background: var(--light-color);
        }

        .mobile-content {
            margin-top: 56px;
            margin-bottom: 70px;
            min-height: calc(100vh - 126px);
        }

        .mobile-bottom-nav {
            position: fixed;
            bottom: var(--safe-area-inset-bottom);
            left: 0;
            right: 0;
            height: 70px;
            background: white;
            border-top: 1px solid var(--border-color);
            z-index: 1000;
            display: flex;
            align-items: center;
            padding: 8px 0;
        }

        .nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: var(--text-muted);
            font-size: 12px;
            padding: 4px;
            border-radius: 8px;
            margin: 0 4px;
            transition: all 0.2s;
        }

        .nav-item.active {
            color: var(--primary-color);
        }

        .nav-item:hover,
        .nav-item:focus {
            color: var(--primary-color);
            text-decoration: none;
        }

        .nav-item i {
            font-size: 20px;
            margin-bottom: 2px;
        }

        .nav-item .badge {
            position: absolute;
            top: -2px;
            right: 8px;
            min-width: 16px;
            height: 16px;
            font-size: 10px;
            line-height: 16px;
            border-radius: 8px;
        }

        /* 触摸优化 */
        .btn, .form-control, .nav-link {
            min-height: 44px;
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
        }

        .form-control {
            border-radius: 8px;
            font-size: 16px; /* 防止iOS缩放 */
        }

        .card {
            border-radius: 12px;
            border: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* 列表样式 */
        .list-group-item {
            border: none;
            border-bottom: 1px solid var(--border-color);
            padding: 16px;
        }

        .list-group-item:last-child {
            border-bottom: none;
        }

        /* 模态框优化 */
        .modal-content {
            border-radius: 16px 16px 0 0;
            border: none;
        }

        .modal.fade .modal-dialog {
            transform: translateY(100%);
        }

        .modal.show .modal-dialog {
            transform: translateY(0);
        }

        /* 下拉刷新 */
        .pull-refresh {
            position: relative;
            overflow: hidden;
        }

        .pull-refresh-indicator {
            position: absolute;
            top: -60px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            transition: all 0.3s;
        }

        .pull-refresh.pulling .pull-refresh-indicator {
            top: 20px;
        }

        /* 加载动画 */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-muted);
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        /* 价格颜色 */
        .price-up {
            color: var(--success-color);
        }

        .price-down {
            color: var(--danger-color);
        }

        /* 响应式隐藏 */
        @media (min-width: 768px) {
            .mobile-only {
                display: none !important;
            }
        }

        @media (max-width: 767px) {
            .desktop-only {
                display: none !important;
            }
        }

        /* iOS样式优化 */
        @supports (-webkit-touch-callout: none) {
            .form-control {
                font-size: 16px;
                transform: translateZ(0);
            }
        }

        /* 安全区域适配 */
        @supports (padding: max(0px)) {
            .mobile-header {
                padding-top: max(var(--safe-area-inset-top), 0px);
            }
            
            .mobile-bottom-nav {
                padding-bottom: max(var(--safe-area-inset-bottom), 8px);
            }
        }
    </style>
    
    {block name="css"}{/block}
</head>
<body>
    <!-- 移动端头部 -->
    <header class="mobile-header mobile-only">
        <div class="header-left">
            {block name="header-left"}
            <button class="btn-header" onclick="history.back()">
                <i class="fas fa-arrow-left"></i>
            </button>
            {/block}
        </div>
        <div class="header-center">
            {block name="header-title"}{$title|default='交易平台'}{/block}
        </div>
        <div class="header-right">
            {block name="header-right"}
            <button class="btn-header" onclick="showMenu()">
                <i class="fas fa-ellipsis-v"></i>
            </button>
            {/block}
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="mobile-content">
        {block name="content"}{/block}
    </main>

    <!-- 移动端底部导航 -->
    <nav class="mobile-bottom-nav mobile-only">
        <a href="/" class="nav-item {if $current_page == 'home'}active{/if}">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="/trade" class="nav-item {if $current_page == 'trade'}active{/if}">
            <i class="fas fa-chart-line"></i>
            <span>交易</span>
        </a>
        <a href="/wallet" class="nav-item {if $current_page == 'wallet'}active{/if}">
            <i class="fas fa-wallet"></i>
            <span>资产</span>
        </a>
        <a href="/contract" class="nav-item {if $current_page == 'contract'}active{/if}">
            <i class="fas fa-file-contract"></i>
            <span>合约</span>
        </a>
        <a href="/user" class="nav-item {if $current_page == 'user'}active{/if}">
            <i class="fas fa-user"></i>
            <span>我的</span>
            {if $unread_notifications > 0}
            <span class="badge bg-danger">{$unread_notifications}</span>
            {/if}
        </a>
    </nav>

    <!-- Toast 容器 -->
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">
        <div id="liveToast" class="toast" role="alert">
            <div class="toast-header">
                <strong class="me-auto">提示</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body"></div>
        </div>
    </div>

    <!-- 加载遮罩 -->
    <div id="loadingOverlay" class="position-fixed top-0 start-0 w-100 h-100 d-none" style="background: rgba(0,0,0,0.5); z-index: 9998;">
        <div class="d-flex align-items-center justify-content-center h-100">
            <div class="text-center text-white">
                <div class="loading-spinner mb-2"></div>
                <div>加载中...</div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // PWA 相关功能
        let deferredPrompt;
        let swRegistration;

        // 注册 Service Worker
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', async () => {
                try {
                    swRegistration = await navigator.serviceWorker.register('/sw.js');
                    console.log('SW registered: ', swRegistration);
                    
                    // 检查更新
                    swRegistration.addEventListener('updatefound', () => {
                        const newWorker = swRegistration.installing;
                        newWorker.addEventListener('statechange', () => {
                            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                showUpdateAvailable();
                            }
                        });
                    });
                } catch (error) {
                    console.log('SW registration failed: ', error);
                }
            });
        }

        // 监听安装提示
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            showInstallPrompt();
        });

        // 显示安装提示
        function showInstallPrompt() {
            const installBanner = `
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="fas fa-download me-2"></i>
                    安装应用到主屏幕，获得更好的体验
                    <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="installApp()">安装</button>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            if (!localStorage.getItem('install-prompt-dismissed')) {
                $('main').prepend(installBanner);
            }
        }

        // 安装应用
        async function installApp() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                console.log(`User response to the install prompt: ${outcome}`);
                deferredPrompt = null;
                localStorage.setItem('install-prompt-dismissed', 'true');
            }
        }

        // 显示更新可用提示
        function showUpdateAvailable() {
            showToast('发现新版本，点击刷新页面更新', 'info', 0, () => {
                window.location.reload();
            });
        }

        // 通用工具函数
        function showToast(message, type = 'info', duration = 3000, callback = null) {
            const toast = $('#liveToast');
            const toastBody = toast.find('.toast-body');
            
            toastBody.text(message);
            toast.removeClass('text-bg-primary text-bg-success text-bg-danger text-bg-warning text-bg-info');
            toast.addClass(`text-bg-${type}`);
            
            const bsToast = new bootstrap.Toast(toast[0], {
                delay: duration,
                autohide: duration > 0
            });
            
            if (callback) {
                toast.on('click', callback);
            }
            
            bsToast.show();
        }

        function showLoading() {
            $('#loadingOverlay').removeClass('d-none');
        }

        function hideLoading() {
            $('#loadingOverlay').addClass('d-none');
        }

        function showMenu() {
            // 显示菜单
            console.log('Show menu');
        }

        // 触摸手势支持
        let startY = 0;
        let currentY = 0;
        let pullRefreshElement = null;

        function initPullRefresh(element, callback) {
            pullRefreshElement = element;
            
            element.addEventListener('touchstart', (e) => {
                startY = e.touches[0].pageY;
            });

            element.addEventListener('touchmove', (e) => {
                currentY = e.touches[0].pageY;
                const pullDistance = currentY - startY;
                
                if (pullDistance > 0 && element.scrollTop === 0) {
                    e.preventDefault();
                    
                    if (pullDistance > 60) {
                        element.classList.add('pulling');
                    } else {
                        element.classList.remove('pulling');
                    }
                }
            });

            element.addEventListener('touchend', () => {
                if (element.classList.contains('pulling')) {
                    element.classList.remove('pulling');
                    if (callback) callback();
                }
            });
        }

        // 页面可见性变化处理
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                // 页面变为可见时刷新数据
                if (typeof refreshData === 'function') {
                    refreshData();
                }
            }
        });

        // 网络状态监听
        window.addEventListener('online', () => {
            showToast('网络连接已恢复', 'success');
        });

        window.addEventListener('offline', () => {
            showToast('网络连接已断开，部分功能可能不可用', 'warning');
        });

        // 防止双击缩放
        let lastTouchEnd = 0;
        document.addEventListener('touchend', (event) => {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);

        // 页面加载完成
        $(document).ready(function() {
            // 初始化下拉刷新
            const mainContent = document.querySelector('main');
            if (mainContent) {
                initPullRefresh(mainContent, () => {
                    if (typeof refreshData === 'function') {
                        refreshData();
                    } else {
                        window.location.reload();
                    }
                });
            }

            // 自动隐藏地址栏（iOS Safari）
            setTimeout(() => {
                window.scrollTo(0, 1);
            }, 100);
        });
    </script>
    
    {block name="js"}{/block}
</body>
</html>
