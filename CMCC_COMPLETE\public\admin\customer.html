<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服管理 - GVD管理后台</title>
    <link rel="stylesheet" href="/static/css/app.css">
    <link rel="stylesheet" href="/static/css/admin.css">
    <link rel="stylesheet" href="/static/css/customer-admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <h2>GVD管理后台</h2>
            </div>
            <nav class="sidebar-nav">
                <a href="/admin/dashboard" class="nav-item">
                    <span class="nav-icon">📊</span>
                    <span class="nav-text">仪表盘</span>
                </a>
                <a href="/admin/users" class="nav-item">
                    <span class="nav-icon">👥</span>
                    <span class="nav-text">用户管理</span>
                </a>
                <a href="/admin/customer" class="nav-item active">
                    <span class="nav-icon">💬</span>
                    <span class="nav-text">客服管理</span>
                </a>
                <a href="/admin/orders" class="nav-item">
                    <span class="nav-icon">📋</span>
                    <span class="nav-text">订单管理</span>
                </a>
                <a href="/admin/finance" class="nav-item">
                    <span class="nav-icon">💰</span>
                    <span class="nav-text">财务管理</span>
                </a>
                <a href="/admin/settings" class="nav-item">
                    <span class="nav-icon">⚙️</span>
                    <span class="nav-text">系统设置</span>
                </a>
            </nav>
        </aside>

        <!-- 主要内容 -->
        <main class="admin-main">
            <div class="admin-header">
                <div class="header-title">
                    <h1>客服管理</h1>
                    <p>管理客服会话、快捷回复和统计数据</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="adminCustomer.showQuickReplyModal()">
                        <span class="icon">➕</span>
                        添加快捷回复
                    </button>
                    <button class="btn btn-outline" onclick="adminCustomer.exportStatistics()">
                        <span class="icon">📊</span>
                        导出统计
                    </button>
                </div>
            </div>

            <div class="admin-content">
                <!-- 统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">💬</div>
                        <div class="stat-content">
                            <div class="stat-number" id="totalSessions">0</div>
                            <div class="stat-label">总会话数</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">⚡</div>
                        <div class="stat-content">
                            <div class="stat-number" id="activeSessions">0</div>
                            <div class="stat-label">活跃会话</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">👥</div>
                        <div class="stat-content">
                            <div class="stat-number" id="onlineAgents">0</div>
                            <div class="stat-label">在线客服</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">⏱️</div>
                        <div class="stat-content">
                            <div class="stat-number" id="avgResponseTime">0s</div>
                            <div class="stat-label">平均响应时间</div>
                        </div>
                    </div>
                </div>

                <!-- 客服界面 -->
                <div class="customer-interface">
                    <!-- 会话列表 -->
                    <div class="sessions-panel">
                        <div class="panel-header">
                            <h3>会话列表</h3>
                            <div class="panel-filters">
                                <select id="statusFilter" onchange="adminCustomer.filterSessions()">
                                    <option value="">全部状态</option>
                                    <option value="active">活跃</option>
                                    <option value="waiting">等待</option>
                                    <option value="closed">已关闭</option>
                                </select>
                                <select id="agentFilter" onchange="adminCustomer.filterSessions()">
                                    <option value="">全部客服</option>
                                </select>
                            </div>
                        </div>
                        <div class="sessions-list" id="sessionsList">
                            <!-- 会话列表内容 -->
                        </div>
                    </div>

                    <!-- 聊天区域 -->
                    <div class="chat-panel">
                        <div class="chat-header" id="chatHeader">
                            <div class="chat-info">
                                <div class="user-avatar">?</div>
                                <div class="user-details">
                                    <div class="user-name">选择一个会话</div>
                                    <div class="user-status">开始客服对话</div>
                                </div>
                            </div>
                            <div class="chat-actions">
                                <button class="action-btn" onclick="adminCustomer.transferSession()" title="转接">
                                    <span class="icon">🔄</span>
                                </button>
                                <button class="action-btn" onclick="adminCustomer.closeSession()" title="关闭">
                                    <span class="icon">❌</span>
                                </button>
                            </div>
                        </div>

                        <div class="chat-messages" id="chatMessages">
                            <div class="empty-chat">
                                <div class="empty-icon">💬</div>
                                <div class="empty-text">
                                    <h3>选择一个会话开始对话</h3>
                                    <p>从左侧选择一个客服会话来查看消息历史</p>
                                </div>
                            </div>
                        </div>

                        <div class="chat-input" id="chatInput" style="display: none;">
                            <div class="quick-replies" id="quickReplies">
                                <!-- 快捷回复按钮 -->
                            </div>
                            <div class="input-area">
                                <textarea 
                                    id="messageInput" 
                                    placeholder="输入回复消息..."
                                    rows="1"
                                    maxlength="1000"
                                ></textarea>
                                <button class="send-btn" id="sendBtn" onclick="adminCustomer.sendMessage()">
                                    <span class="icon">📤</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 用户信息面板 -->
                    <div class="user-info-panel" id="userInfoPanel">
                        <div class="panel-header">
                            <h3>用户信息</h3>
                        </div>
                        <div class="user-profile" id="userProfile">
                            <div class="profile-avatar">
                                <div class="avatar-placeholder">?</div>
                            </div>
                            <div class="profile-info">
                                <div class="info-item">
                                    <label>用户名</label>
                                    <span id="profileUsername">-</span>
                                </div>
                                <div class="info-item">
                                    <label>邮箱</label>
                                    <span id="profileEmail">-</span>
                                </div>
                                <div class="info-item">
                                    <label>余额</label>
                                    <span id="profileBalance">-</span>
                                </div>
                                <div class="info-item">
                                    <label>注册时间</label>
                                    <span id="profileRegTime">-</span>
                                </div>
                                <div class="info-item">
                                    <label>代理</label>
                                    <span id="profileAgent">-</span>
                                </div>
                            </div>
                        </div>

                        <div class="user-actions">
                            <button class="btn btn-sm btn-outline" onclick="adminCustomer.viewUserOrders()">
                                查看订单
                            </button>
                            <button class="btn btn-sm btn-outline" onclick="adminCustomer.viewUserTransactions()">
                                交易记录
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 快捷回复管理 -->
                <div class="quick-reply-section">
                    <div class="section-header">
                        <h3>快捷回复管理</h3>
                        <button class="btn btn-sm btn-primary" onclick="adminCustomer.showQuickReplyModal()">
                            添加回复
                        </button>
                    </div>
                    <div class="quick-reply-list" id="quickReplyList">
                        <!-- 快捷回复列表 -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 快捷回复模态框 -->
    <div class="modal" id="quickReplyModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">添加快捷回复</h3>
                <button class="modal-close" onclick="adminCustomer.hideQuickReplyModal()">×</button>
            </div>
            <div class="modal-body">
                <form id="quickReplyForm">
                    <div class="form-group">
                        <label>标题</label>
                        <input type="text" id="replyTitle" placeholder="输入回复标题" maxlength="100" required>
                    </div>
                    <div class="form-group">
                        <label>分类</label>
                        <select id="replyCategory" required>
                            <option value="general">通用</option>
                            <option value="trading">交易</option>
                            <option value="account">账户</option>
                            <option value="technical">技术</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>内容</label>
                        <textarea id="replyContent" placeholder="输入回复内容" maxlength="1000" rows="4" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="adminCustomer.hideQuickReplyModal()">取消</button>
                <button class="btn btn-primary" onclick="adminCustomer.saveQuickReply()">保存</button>
            </div>
        </div>
    </div>

    <!-- 转接模态框 -->
    <div class="modal" id="transferModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>转接会话</h3>
                <button class="modal-close" onclick="adminCustomer.hideTransferModal()">×</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>选择目标客服</label>
                    <select id="targetAgent" required>
                        <option value="">请选择客服</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>转接说明</label>
                    <textarea id="transferReason" placeholder="可选：转接原因说明" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="adminCustomer.hideTransferModal()">取消</button>
                <button class="btn btn-primary" onclick="adminCustomer.confirmTransfer()">确认转接</button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/static/js/admin-customer.js"></script>
</body>
</html>
