<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * IDO项目模型
 */
class IdoProject extends Model
{
    protected $table = 'ce_ido_projects';
    
    // 设置字段信息
    protected $schema = [
        'id'                  => 'int',
        'name'                => 'string',
        'symbol'              => 'string',
        'icon'                => 'string',
        'description'         => 'text',
        'total_supply'        => 'decimal',
        'sale_amount'         => 'decimal',
        'price'               => 'decimal',
        'payment_coin'        => 'string',
        'min_purchase'        => 'decimal',
        'max_purchase'        => 'decimal',
        'start_time'          => 'datetime',
        'end_time'            => 'datetime',
        'lock_period'         => 'int',
        'daily_release_rate'  => 'decimal',
        'commission_level1'   => 'decimal',
        'commission_level2'   => 'decimal',
        'commission_level3'   => 'decimal',
        'status'              => 'int',
        'created_at'          => 'datetime',
        'updated_at'          => 'datetime',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 类型转换
    protected $type = [
        'total_supply'        => 'decimal:8',
        'sale_amount'         => 'decimal:8',
        'price'               => 'decimal:8',
        'min_purchase'        => 'decimal:8',
        'max_purchase'        => 'decimal:8',
        'lock_period'         => 'integer',
        'daily_release_rate'  => 'decimal:6',
        'commission_level1'   => 'decimal:4',
        'commission_level2'   => 'decimal:4',
        'commission_level3'   => 'decimal:4',
        'status'              => 'integer',
    ];

    /**
     * 关联认购订单
     */
    public function orders()
    {
        return $this->hasMany(IdoOrder::class, 'project_id');
    }

    /**
     * 关联支付币种
     */
    public function paymentCoin()
    {
        return $this->belongsTo(Coin::class, 'payment_coin', 'symbol');
    }

    /**
     * 状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [0 => '禁用', 1 => '启用'];
        return $status[$data['status']] ?? '未知';
    }

    /**
     * 项目状态
     */
    public function getProjectStatusAttr($value, $data)
    {
        $now = date('Y-m-d H:i:s');
        
        if ($data['start_time'] > $now) {
            return 'upcoming'; // 即将开始
        } elseif ($data['end_time'] > $now) {
            return 'ongoing'; // 进行中
        } else {
            return 'ended'; // 已结束
        }
    }

    /**
     * 项目状态文本
     */
    public function getProjectStatusTextAttr($value, $data)
    {
        $status = $this->project_status;
        $texts = [
            'upcoming' => '即将开始',
            'ongoing' => '进行中',
            'ended' => '已结束'
        ];
        return $texts[$status] ?? '未知';
    }

    /**
     * 项目状态颜色
     */
    public function getProjectStatusColorAttr($value, $data)
    {
        $status = $this->project_status;
        $colors = [
            'upcoming' => 'info',
            'ongoing' => 'success',
            'ended' => 'secondary'
        ];
        return $colors[$status] ?? 'secondary';
    }

    /**
     * 图标URL
     */
    public function getIconUrlAttr($value, $data)
    {
        if (empty($data['icon'])) {
            return '/static/images/coins/default.png';
        }
        
        if (strpos($data['icon'], 'http') === 0) {
            return $data['icon'];
        }
        
        return '/uploads/ido/' . $data['icon'];
    }

    /**
     * 格式化总发行量
     */
    public function getTotalSupplyFormatAttr($value, $data)
    {
        return number_format($data['total_supply'], 0, '.', ',');
    }

    /**
     * 格式化销售数量
     */
    public function getSaleAmountFormatAttr($value, $data)
    {
        return number_format($data['sale_amount'], 0, '.', ',');
    }

    /**
     * 格式化价格
     */
    public function getPriceFormatAttr($value, $data)
    {
        return number_format($data['price'], 8, '.', '');
    }

    /**
     * 格式化最小认购
     */
    public function getMinPurchaseFormatAttr($value, $data)
    {
        return number_format($data['min_purchase'], 8, '.', '');
    }

    /**
     * 格式化最大认购
     */
    public function getMaxPurchaseFormatAttr($value, $data)
    {
        return number_format($data['max_purchase'], 8, '.', '');
    }

    /**
     * 锁仓期文本
     */
    public function getLockPeriodTextAttr($value, $data)
    {
        return $data['lock_period'] . '天';
    }

    /**
     * 每日释放比例百分比
     */
    public function getDailyReleaseRatePercentAttr($value, $data)
    {
        return number_format($data['daily_release_rate'] * 100, 2) . '%';
    }

    /**
     * 一级佣金百分比
     */
    public function getCommissionLevel1PercentAttr($value, $data)
    {
        return number_format($data['commission_level1'] * 100, 2) . '%';
    }

    /**
     * 二级佣金百分比
     */
    public function getCommissionLevel2PercentAttr($value, $data)
    {
        return number_format($data['commission_level2'] * 100, 2) . '%';
    }

    /**
     * 三级佣金百分比
     */
    public function getCommissionLevel3PercentAttr($value, $data)
    {
        return number_format($data['commission_level3'] * 100, 2) . '%';
    }

    /**
     * 剩余时间（秒）
     */
    public function getRemainingTimeAttr($value, $data)
    {
        $now = time();
        $status = $this->project_status;
        
        if ($status == 'upcoming') {
            return strtotime($data['start_time']) - $now;
        } elseif ($status == 'ongoing') {
            return strtotime($data['end_time']) - $now;
        } else {
            return 0;
        }
    }

    /**
     * 进度百分比
     */
    public function getProgressAttr($value, $data)
    {
        $now = time();
        $startTime = strtotime($data['start_time']);
        $endTime = strtotime($data['end_time']);
        
        if ($now < $startTime) {
            return 0;
        } elseif ($now > $endTime) {
            return 100;
        } else {
            $total = $endTime - $startTime;
            $elapsed = $now - $startTime;
            return round($elapsed / $total * 100, 2);
        }
    }

    /**
     * 是否可以认购
     */
    public function getCanPurchaseAttr($value, $data)
    {
        return $this->project_status == 'ongoing' && $data['status'] == 1;
    }

    /**
     * 获取已售数量
     */
    public function getSoldAmountAttr($value, $data)
    {
        return IdoOrder::where('project_id', $data['id'])
                      ->where('status', 1)
                      ->sum('amount');
    }

    /**
     * 获取已售百分比
     */
    public function getSoldPercentAttr($value, $data)
    {
        $soldAmount = $this->sold_amount;
        if ($data['sale_amount'] <= 0) {
            return 0;
        }
        return round($soldAmount / $data['sale_amount'] * 100, 2);
    }

    /**
     * 获取参与人数
     */
    public function getParticipantsAttr($value, $data)
    {
        return IdoOrder::where('project_id', $data['id'])
                      ->where('status', 1)
                      ->count('DISTINCT user_id');
    }

    /**
     * 获取筹集金额
     */
    public function getRaisedAmountAttr($value, $data)
    {
        return IdoOrder::where('project_id', $data['id'])
                      ->where('status', 1)
                      ->sum('total_payment');
    }

    /**
     * 验证认购数量
     */
    public function validatePurchaseAmount(float $amount): array
    {
        if ($amount < $this->min_purchase) {
            return ['valid' => false, 'msg' => "最小认购数量为 {$this->min_purchase_format} {$this->symbol}"];
        }
        
        if ($this->max_purchase > 0 && $amount > $this->max_purchase) {
            return ['valid' => false, 'msg' => "最大认购数量为 {$this->max_purchase_format} {$this->symbol}"];
        }
        
        // 检查剩余数量
        $soldAmount = $this->sold_amount;
        $remainingAmount = $this->sale_amount - $soldAmount;
        
        if ($amount > $remainingAmount) {
            return ['valid' => false, 'msg' => "剩余数量不足，仅剩 " . number_format($remainingAmount, 8) . " {$this->symbol}"];
        }
        
        return ['valid' => true, 'msg' => ''];
    }

    /**
     * 计算支付金额
     */
    public function calculatePayment(float $amount): float
    {
        return $amount * $this->price;
    }

    /**
     * 获取佣金比例
     */
    public function getCommissionRate(int $level): float
    {
        switch ($level) {
            case 1:
                return $this->commission_level1;
            case 2:
                return $this->commission_level2;
            case 3:
                return $this->commission_level3;
            default:
                return 0;
        }
    }

    /**
     * 获取启用的项目
     */
    public static function getEnabled()
    {
        return self::where('status', 1)->order('created_at', 'desc')->select();
    }

    /**
     * 获取进行中的项目
     */
    public static function getOngoing()
    {
        $now = date('Y-m-d H:i:s');
        return self::where('status', 1)
                  ->where('start_time', '<=', $now)
                  ->where('end_time', '>', $now)
                  ->order('end_time', 'asc')
                  ->select();
    }

    /**
     * 获取即将开始的项目
     */
    public static function getUpcoming()
    {
        $now = date('Y-m-d H:i:s');
        return self::where('status', 1)
                  ->where('start_time', '>', $now)
                  ->order('start_time', 'asc')
                  ->select();
    }
}
