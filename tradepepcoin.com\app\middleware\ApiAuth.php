<?php
declare(strict_types=1);
namespace app\middleware;
use think\Request;
use think\Response;
class ApiAuth
{
    public function handle(Request $request, \Closure $next): Response
    {
        $path = $request->pathinfo();
        $publicPaths = ['api/v1/config', 'api/v1/market', 'api/v1/auth'];
        foreach ($publicPaths as $publicPath) {
            if (strpos($path, $publicPath) === 0) {
                return $next($request);
            }
        }
        return $next($request);
    }
}
