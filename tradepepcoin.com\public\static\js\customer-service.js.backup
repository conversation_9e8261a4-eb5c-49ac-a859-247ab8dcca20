/* 前端客服系统 */

class CustomerService {
    constructor() {
        this.isOpen = false;
        this.unreadCount = 0;
        this.messages = [];
        this.currentPage = 1;
        this.isLoading = false;
        this.websocket = null;
        this.userId = null;
        this.init();
    }

    // 初始化客服系统
    init() {
        this.createServiceWidget();
        this.bindEvents();
        this.loadUserInfo();
        this.connectWebSocket();
        this.loadMessages();
        this.checkUnreadMessages();
    }

    // 创建客服组件
    createServiceWidget() {
        const widget = document.createElement('div');
        widget.id = 'customerServiceWidget';
        widget.innerHTML = `
            <!-- 客服按钮 -->
            <div class="service-button" id="serviceButton">
                <div class="service-icon">💬</div>
                <div class="service-badge" id="serviceBadge" style="display: none;">0</div>
            </div>

            <!-- 客服窗口 -->
            <div class="service-window" id="serviceWindow" style="display: none;">
                <div class="service-header">
                    <div class="service-title">
                        <span class="service-avatar">🎧</span>
                        <div class="service-info">
                            <div class="service-name">在线客服</div>
                            <div class="service-status online">在线</div>
                        </div>
                    </div>
                    <div class="service-actions">
                        <button class="service-minimize" id="serviceMinimize">−</button>
                        <button class="service-close" id="serviceClose">×</button>
                    </div>
                </div>

                <div class="service-body">
                    <!-- 消息列表 -->
                    <div class="message-list" id="messageList">
                        <div class="message-loading" id="messageLoading" style="display: none;">
                            <div class="loading-spinner"></div>
                            <span>加载中...</span>
                        </div>
                    </div>

                    <!-- 输入区域 -->
                    <div class="message-input-area">
                        <!-- 工具栏 -->
                        <div class="input-toolbar">
                            <button class="toolbar-btn emoji-btn" id="emojiBtn" title="表情">😊</button>
                            <button class="toolbar-btn image-btn" id="imageBtn" title="图片">📷</button>
                            <button class="toolbar-btn video-btn" id="videoBtn" title="视频">🎥</button>
                            <input type="file" id="imageInput" accept="image/*" style="display: none;">
                            <input type="file" id="videoInput" accept="video/*" style="display: none;">
                        </div>

                        <!-- 表情面板 -->
                        <div class="emoji-panel" id="emojiPanel" style="display: none;">
                            <div class="emoji-grid">
                                ${this.getEmojiList().map(emoji => `<span class="emoji-item" data-emoji="${emoji}">${emoji}</span>`).join('')}
                            </div>
                        </div>

                        <!-- 输入框 -->
                        <div class="input-container">
                            <textarea id="messageInput" placeholder="请输入消息..." rows="2"></textarea>
                            <button class="send-btn" id="sendBtn">发送</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(widget);
    }

    // 绑定事件
    bindEvents() {
        // 客服按钮点击
        document.getElementById('serviceButton').addEventListener('click', () => {
            this.toggleWindow();
        });

        // 最小化按钮
        document.getElementById('serviceMinimize').addEventListener('click', () => {
            this.minimizeWindow();
        });

        // 关闭按钮
        document.getElementById('serviceClose').addEventListener('click', () => {
            this.closeWindow();
        });

        // 发送按钮
        document.getElementById('sendBtn').addEventListener('click', () => {
            this.sendMessage();
        });

        // 输入框回车发送
        document.getElementById('messageInput').addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // 表情按钮
        document.getElementById('emojiBtn').addEventListener('click', () => {
            this.toggleEmojiPanel();
        });

        // 图片按钮
        document.getElementById('imageBtn').addEventListener('click', () => {
            document.getElementById('imageInput').click();
        });

        // 视频按钮
        document.getElementById('videoBtn').addEventListener('click', () => {
            document.getElementById('videoInput').click();
        });

        // 文件选择
        document.getElementById('imageInput').addEventListener('change', (e) => {
            this.handleFileUpload(e.target.files[0], 'image');
        });

        document.getElementById('videoInput').addEventListener('change', (e) => {
            this.handleFileUpload(e.target.files[0], 'video');
        });

        // 表情选择
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('emoji-item')) {
                this.insertEmoji(e.target.dataset.emoji);
            }
        });

        // 消息列表滚动加载
        document.getElementById('messageList').addEventListener('scroll', (e) => {
            if (e.target.scrollTop === 0 && !this.isLoading) {
                this.loadMoreMessages();
            }
        });
    }

    // 获取表情列表
    getEmojiList() {
        return [
            '😊', '😂', '😍', '🤔', '😢', '😭', '😡', '😱', '🤗', '😴',
            '🙄', '😎', '🤩', '🥳', '😇', '🤪', '🤭', '🤫', '🤨', '😏',
            '👍', '👎', '👌', '✌️', '🤞', '🤝', '👏', '🙏', '💪', '🎉',
            '❤️', '💔', '💕', '💖', '💗', '💘', '💝', '💞', '💟', '💯'
        ];
    }

    // 切换窗口显示
    toggleWindow() {
        const window = document.getElementById('serviceWindow');
        if (this.isOpen) {
            this.closeWindow();
        } else {
            this.openWindow();
        }
    }

    // 打开窗口
    openWindow() {
        const window = document.getElementById('serviceWindow');
        window.style.display = 'block';
        window.classList.add('service-window-open');
        this.isOpen = true;
        this.markMessagesAsRead();
        this.scrollToBottom();
    }

    // 关闭窗口
    closeWindow() {
        const window = document.getElementById('serviceWindow');
        window.style.display = 'none';
        window.classList.remove('service-window-open');
        this.isOpen = false;
    }

    // 最小化窗口
    minimizeWindow() {
        this.closeWindow();
    }

    // 切换表情面板
    toggleEmojiPanel() {
        const panel = document.getElementById('emojiPanel');
        panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
    }

    // 插入表情
    insertEmoji(emoji) {
        const input = document.getElementById('messageInput');
        const start = input.selectionStart;
        const end = input.selectionEnd;
        const text = input.value;
        
        input.value = text.substring(0, start) + emoji + text.substring(end);
        input.focus();
        input.setSelectionRange(start + emoji.length, start + emoji.length);
        
        this.toggleEmojiPanel();
    }

    // 发送消息
    async sendMessage() {
        const input = document.getElementById('messageInput');
        const content = input.value.trim();
        
        if (!content) return;

        const message = {
            type: 1, // 文本消息
            content: content,
            sender_type: 1 // 用户发送
        };

        try {
            const result = await this.sendMessageToServer(message);
            if (result.code === 1) {
                input.value = '';
                this.addMessageToList(result.data);
                this.scrollToBottom();
            } else {
                this.showError(result.msg || '发送失败');
            }
        } catch (error) {
            this.showError('网络错误，请重试');
        }
    }

    // 处理文件上传
    async handleFileUpload(file, type) {
        if (!file) return;

        // 检查文件大小
        const maxSize = type === 'image' ? 5 * 1024 * 1024 : 50 * 1024 * 1024; // 图片5MB，视频50MB
        if (file.size > maxSize) {
            this.showError(`文件大小不能超过 ${maxSize / 1024 / 1024}MB`);
            return;
        }

        // 显示上传进度
        this.showUploadProgress();

        try {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('type', type);

            const response = await fetch('/user/customer-service/upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            
            if (result.code === 1) {
                const message = {
                    type: type === 'image' ? 2 : 3,
                    content: file.name,
                    media_url: result.data.url,
                    sender_type: 1
                };

                const sendResult = await this.sendMessageToServer(message);
                if (sendResult.code === 1) {
                    this.addMessageToList(sendResult.data);
                    this.scrollToBottom();
                }
            } else {
                this.showError(result.msg || '上传失败');
            }
        } catch (error) {
            this.showError('上传失败，请重试');
        } finally {
            this.hideUploadProgress();
        }
    }

    // 发送消息到服务器
    async sendMessageToServer(message) {
        const response = await fetch('/user/customer-service/send', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(message)
        });

        return await response.json();
    }

    // 加载用户信息
    async loadUserInfo() {
        try {
            const response = await fetch('/user/info');
            const result = await response.json();
            if (result.code === 1) {
                this.userId = result.data.id;
            }
        } catch (error) {
            console.error('加载用户信息失败:', error);
        }
    }

    // 加载消息
    async loadMessages() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading();

        try {
            const response = await fetch(`/user/customer-service/messages?page=${this.currentPage}`);
            const result = await response.json();
            
            if (result.code === 1) {
                this.messages = [...result.data.messages, ...this.messages];
                this.renderMessages();
                this.currentPage++;
            }
        } catch (error) {
            console.error('加载消息失败:', error);
        } finally {
            this.isLoading = false;
            this.hideLoading();
        }
    }

    // 加载更多消息
    async loadMoreMessages() {
        await this.loadMessages();
    }

    // 渲染消息列表
    renderMessages() {
        const messageList = document.getElementById('messageList');
        const loadingEl = document.getElementById('messageLoading');
        
        messageList.innerHTML = '';
        messageList.appendChild(loadingEl);

        this.messages.forEach(message => {
            this.addMessageToList(message, false);
        });
    }

    // 添加消息到列表
    addMessageToList(message, append = true) {
        const messageList = document.getElementById('messageList');
        const messageEl = this.createMessageElement(message);
        
        if (append) {
            messageList.appendChild(messageEl);
        } else {
            const loadingEl = document.getElementById('messageLoading');
            messageList.insertBefore(messageEl, loadingEl);
        }
    }

    // 创建消息元素
    createMessageElement(message) {
        const div = document.createElement('div');
        const isOwn = message.sender_type === 1;
        
        div.className = `message ${isOwn ? 'message-own' : 'message-other'}`;
        
        let content = '';
        switch (message.type) {
            case 1: // 文本
                content = `<div class="message-text">${this.escapeHtml(message.content)}</div>`;
                break;
            case 2: // 图片
                content = `<div class="message-image"><img src="${message.media_url}" alt="图片" onclick="this.parentElement.parentElement.classList.toggle('image-fullscreen')"></div>`;
                break;
            case 3: // 视频
                content = `<div class="message-video"><video src="${message.media_url}" controls></video></div>`;
                break;
            case 4: // 表情
                content = `<div class="message-emoji">${message.content}</div>`;
                break;
        }

        div.innerHTML = `
            <div class="message-avatar">
                ${isOwn ? '👤' : '🎧'}
            </div>
            <div class="message-content">
                <div class="message-info">
                    <span class="message-sender">${message.sender_name || (isOwn ? '我' : '客服')}</span>
                    <span class="message-time">${this.formatTime(message.created_at)}</span>
                </div>
                ${content}
            </div>
        `;

        return div;
    }

    // 连接WebSocket
    connectWebSocket() {
        if (!this.userId) return;

        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/customer-service/${this.userId}`;
        
        this.websocket = new WebSocket(wsUrl);
        
        this.websocket.onopen = () => {
            console.log('客服WebSocket连接成功');
        };
        
        this.websocket.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.handleNewMessage(message);
        };
        
        this.websocket.onclose = () => {
            console.log('客服WebSocket连接关闭');
            // 重连
            setTimeout(() => {
                this.connectWebSocket();
            }, 5000);
        };
        
        this.websocket.onerror = (error) => {
            console.error('客服WebSocket错误:', error);
        };
    }

    // 处理新消息
    handleNewMessage(message) {
        if (message.sender_type !== 1) { // 不是自己发送的消息
            this.addMessageToList(message);
            this.scrollToBottom();
            
            if (!this.isOpen) {
                this.updateUnreadCount();
                this.showNotification(message);
            }
        }
    }

    // 检查未读消息
    async checkUnreadMessages() {
        try {
            const response = await fetch('/user/customer-service/unread-count');
            const result = await response.json();
            if (result.code === 1) {
                this.updateUnreadBadge(result.data.count);
            }
        } catch (error) {
            console.error('检查未读消息失败:', error);
        }
    }

    // 更新未读数量
    updateUnreadCount() {
        this.unreadCount++;
        this.updateUnreadBadge(this.unreadCount);
    }

    // 更新未读徽章
    updateUnreadBadge(count) {
        const badge = document.getElementById('serviceBadge');
        if (count > 0) {
            badge.textContent = count > 99 ? '99+' : count;
            badge.style.display = 'block';
        } else {
            badge.style.display = 'none';
        }
    }

    // 标记消息为已读
    async markMessagesAsRead() {
        try {
            await fetch('/user/customer-service/mark-read', {
                method: 'POST'
            });
            this.unreadCount = 0;
            this.updateUnreadBadge(0);
        } catch (error) {
            console.error('标记已读失败:', error);
        }
    }

    // 显示通知
    showNotification(message) {
        if (Notification.permission === 'granted') {
            new Notification('新消息', {
                body: message.content,
                icon: '/static/images/service-icon.png'
            });
        }
    }

    // 工具方法
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) return '刚刚';
        if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
        if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
        
        return date.toLocaleString('zh-CN', {
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    scrollToBottom() {
        const messageList = document.getElementById('messageList');
        messageList.scrollTop = messageList.scrollHeight;
    }

    showLoading() {
        document.getElementById('messageLoading').style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('messageLoading').style.display = 'none';
    }

    showUploadProgress() {
        // 显示上传进度
        console.log('显示上传进度');
    }

    hideUploadProgress() {
        // 隐藏上传进度
        console.log('隐藏上传进度');
    }

    showError(message) {
        alert(message); // 可以替换为更好的错误提示
    }
}

// 初始化客服系统
document.addEventListener('DOMContentLoaded', function() {
    // 请求通知权限
    if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission();
    }
    
    // 创建客服实例
    window.customerService = new CustomerService();
});
