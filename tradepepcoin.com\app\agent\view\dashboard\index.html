<!DOCTYPE html>
<html lang="zh-CN" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理端控制台 - {$site_name|default='加密货币交易平台'}</title>
    <link rel="stylesheet" href="/static/css/modern-theme.css">
    <link rel="stylesheet" href="/static/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <div class="admin-sidebar">
            <div class="sidebar-header">
                <h2>代理端管理</h2>
                <p>欢迎，{$agent.username}</p>
            </div>
            
            <nav class="admin-nav">
                <a href="/agent/dashboard/index" class="nav-item active">
                    <span class="nav-icon">📊</span>
                    <span class="nav-text">数据统计</span>
                </a>
                <a href="/agent/dashboard/users" class="nav-item">
                    <span class="nav-icon">👥</span>
                    <span class="nav-text">下级用户</span>
                </a>
                <a href="/agent/dashboard/testUsers" class="nav-item">
                    <span class="nav-icon">🧪</span>
                    <span class="nav-text">测试用户</span>
                </a>
                <a href="/agent/dashboard/customerService" class="nav-item">
                    <span class="nav-icon">💬</span>
                    <span class="nav-text">客服管理</span>
                </a>
                <a href="/agent/dashboard/finances" class="nav-item">
                    <span class="nav-icon">💰</span>
                    <span class="nav-text">财务记录</span>
                </a>
                <a href="/agent/commission" class="nav-item">
                    <span class="nav-icon">💎</span>
                    <span class="nav-text">佣金收益</span>
                </a>
            </nav>
        </div>

        <!-- 主要内容 -->
        <div class="admin-main">
            <!-- 页面头部 -->
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1>数据统计</h1>
                        <p class="text-secondary">代理端数据概览（仅统计正式用户）</p>
                    </div>
                    <div class="header-actions">
                        <button class="btn btn-outline btn-sm" onclick="toggleTheme()">
                            <span id="themeIcon">🌙</span>
                        </button>
                        <button class="btn btn-primary btn-sm" onclick="refreshData()">刷新数据</button>
                    </div>
                </div>
            </div>

            <!-- 实时数据监控 -->
            <div class="row mb-4">
                <div class="col-3">
                    <div class="stat-card realtime-card">
                        <div class="stat-icon bg-primary">👥</div>
                        <div class="stat-content">
                            <h3 class="stat-value" id="todayRegister">{$user_stats.today_users}</h3>
                            <p class="stat-label">今日注册人数</p>
                            <small class="stat-desc">
                                总用户: <span id="totalUsers">{$user_stats.direct_users}</span>
                                <span class="realtime-indicator">🔴</span>
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="stat-card realtime-card">
                        <div class="stat-icon bg-success">💰</div>
                        <div class="stat-content">
                            <h3 class="stat-value" id="todayDeposit">{$finance_stats.today_deposit}</h3>
                            <p class="stat-label">今日充值金额</p>
                            <small class="stat-desc">
                                总充值: <span id="totalDeposit">{$finance_stats.total_deposit}</span>
                                <span class="realtime-indicator">🔴</span>
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="stat-card realtime-card">
                        <div class="stat-icon bg-warning">⚡</div>
                        <div class="stat-content">
                            <h3 class="stat-value" id="todayContracts">0</h3>
                            <p class="stat-label">今日合约订单</p>
                            <small class="stat-desc">
                                待处理: <span id="pendingContracts" class="text-danger">0</span>
                                <span class="realtime-indicator">🔴</span>
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="stat-card realtime-card">
                        <div class="stat-icon bg-info">💸</div>
                        <div class="stat-content">
                            <h3 class="stat-value" id="pendingWithdrawals">0</h3>
                            <p class="stat-label">待审核提币</p>
                            <small class="stat-desc">
                                金额: <span id="withdrawalAmount">0.00</span> USDT
                                <span class="realtime-indicator">🔴</span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 充值到账提醒 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card deposit-alert-card" id="depositAlertCard" style="display: none;">
                        <div class="card-header bg-success">
                            <h3 class="card-title text-white">
                                💰 客户充值到账提醒
                                <span class="badge badge-light" id="newDepositsBadge">0</span>
                            </h3>
                            <button class="btn btn-sm btn-light" onclick="hideDepositAlert()">
                                <span>✕</span> 关闭
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="deposit-alerts" id="depositAlertsList">
                                <!-- 动态加载充值提醒 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 合约订单实时监控 -->
            <div class="row mb-4">
                <div class="col-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                合约订单实时监控
                                <span class="badge badge-danger" id="newOrdersBadge" style="display: none;">NEW</span>
                            </h3>
                            <div class="card-actions">
                                <button class="btn btn-sm btn-warning" onclick="showBatchControlModal()">
                                    ⚡ 一键控制
                                </button>
                                <button class="btn btn-sm btn-outline" onclick="toggleAutoRefresh()" id="autoRefreshBtn">
                                    <span id="refreshIcon">⏸️</span> 自动刷新
                                </button>
                                <button class="btn btn-sm btn-primary" onclick="refreshContracts()">刷新</button>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="contract-monitor">
                                <div class="monitor-header">
                                    <div class="monitor-tabs">
                                        <button class="monitor-tab active" onclick="switchMonitorTab('pending')">
                                            待处理 <span class="tab-count" id="pendingCount">0</span>
                                        </button>
                                        <button class="monitor-tab" onclick="switchMonitorTab('processing')">
                                            进行中 <span class="tab-count" id="processingCount">0</span>
                                        </button>
                                        <button class="monitor-tab" onclick="switchMonitorTab('recent')">
                                            最近完成 <span class="tab-count" id="recentCount">0</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="monitor-content">
                                    <!-- 待处理订单 -->
                                    <div class="monitor-panel active" id="pendingPanel">
                                        <div class="contract-list" id="pendingContractsList">
                                            <div class="empty-state">
                                                <div class="empty-icon">⏳</div>
                                                <div class="empty-text">暂无待处理订单</div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 进行中订单 -->
                                    <div class="monitor-panel" id="processingPanel">
                                        <div class="contract-list" id="processingContractsList">
                                            <div class="empty-state">
                                                <div class="empty-icon">⚡</div>
                                                <div class="empty-text">暂无进行中订单</div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 最近完成订单 -->
                                    <div class="monitor-panel" id="recentPanel">
                                        <div class="contract-list" id="recentContractsList">
                                            <div class="empty-state">
                                                <div class="empty-icon">✅</div>
                                                <div class="empty-text">暂无最近完成订单</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                提币审核
                                <span class="badge badge-warning" id="withdrawalBadge" style="display: none;">待审核</span>
                            </h3>
                        </div>
                        <div class="card-body p-0">
                            <div class="withdrawal-list" id="withdrawalList">
                                <div class="empty-state">
                                    <div class="empty-icon">💸</div>
                                    <div class="empty-text">暂无待审核提币</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 客户收支详情 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">客户收支详情（正式用户）</h3>
                            <div class="card-actions">
                                <select class="form-control form-control-sm" id="financeFilter" onchange="filterFinanceRecords()">
                                    <option value="">全部类型</option>
                                    <option value="9">充值</option>
                                    <option value="10">提现</option>
                                    <option value="7">合约收益</option>
                                    <option value="5,6">交易</option>
                                </select>
                                <button class="btn btn-sm btn-outline" onclick="exportFinanceData()">导出</button>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>时间</th>
                                            <th>用户</th>
                                            <th>类型</th>
                                            <th>币种</th>
                                            <th>金额</th>
                                            <th>余额</th>
                                            <th>说明</th>
                                        </tr>
                                    </thead>
                                    <tbody id="financeRecordsTable">
                                        <tr>
                                            <td colspan="7" class="text-center">加载中...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="card-footer">
                                <div class="pagination-wrapper" id="financePagination"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速操作面板 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">快速操作</h3>
                        </div>
                        <div class="card-body">
                            <div class="quick-actions">
                                <a href="/agent/dashboard/testUsers" class="quick-action">
                                    <div class="action-icon bg-primary">🧪</div>
                                    <div class="action-content">
                                        <h4>测试用户管理</h4>
                                        <p>创建和管理测试账户</p>
                                    </div>
                                </a>
                                <a href="/agent/dashboard/users" class="quick-action">
                                    <div class="action-icon bg-success">👥</div>
                                    <div class="action-content">
                                        <h4>下级用户</h4>
                                        <p>查看正式用户列表</p>
                                    </div>
                                </a>
                                <a href="/agent/dashboard/contracts" class="quick-action">
                                    <div class="action-icon bg-warning">⚡</div>
                                    <div class="action-content">
                                        <h4>合约管理</h4>
                                        <p>合约订单控制</p>
                                    </div>
                                </a>
                                <a href="/agent/dashboard/withdrawals" class="quick-action">
                                    <div class="action-icon bg-info">💸</div>
                                    <div class="action-content">
                                        <h4>提币审核</h4>
                                        <p>审核客户提币申请</p>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>

    <!-- 一键控制合约模态框 -->
    <div class="modal" id="batchControlModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>⚡ 一键控制合约输赢</h3>
                <button class="modal-close" onclick="hideBatchControlModal()">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 控制统计 -->
                <div class="control-stats mb-3" id="controlStats">
                    <div class="row">
                        <div class="col-4">
                            <div class="stat-mini">
                                <div class="stat-value" id="pendingContractsCount">0</div>
                                <div class="stat-label">待处理订单</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-mini">
                                <div class="stat-value" id="pendingContractsAmount">0</div>
                                <div class="stat-label">待处理金额</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-mini">
                                <div class="stat-value" id="todayWinRate">0%</div>
                                <div class="stat-label">今日胜率</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 控制选项 -->
                <div class="control-options">
                    <h4>选择控制策略：</h4>
                    <div class="control-buttons">
                        <button class="control-btn all-win" onclick="batchControlContracts('all_win')">
                            <div class="control-icon">🎯</div>
                            <div class="control-text">
                                <h5>全部盈利</h5>
                                <p>让所有待处理订单都盈利</p>
                            </div>
                        </button>
                        <button class="control-btn all-loss" onclick="batchControlContracts('all_loss')">
                            <div class="control-icon">💥</div>
                            <div class="control-text">
                                <h5>全部亏损</h5>
                                <p>让所有待处理订单都亏损</p>
                            </div>
                        </button>
                        <button class="control-btn smart-control" onclick="batchControlContracts('smart_control')">
                            <div class="control-icon">🤖</div>
                            <div class="control-text">
                                <h5>智能控制</h5>
                                <p>根据用户情况智能决定输赢</p>
                            </div>
                        </button>
                    </div>
                </div>

                <!-- 今日控制记录 -->
                <div class="today-records mt-3">
                    <h4>今日控制记录：</h4>
                    <div class="record-stats" id="todayRecords">
                        <div class="record-item">
                            <span>已处理订单：</span>
                            <span id="todayTotalCount">0</span>
                        </div>
                        <div class="record-item">
                            <span>盈利订单：</span>
                            <span id="todayWinCount" class="text-success">0</span>
                        </div>
                        <div class="record-item">
                            <span>亏损订单：</span>
                            <span id="todayLossCount" class="text-danger">0</span>
                        </div>
                        <div class="record-item">
                            <span>总盈亏：</span>
                            <span id="todayProfitLoss">0 USDT</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="hideBatchControlModal()">关闭</button>
                <button class="btn btn-info" onclick="loadControlStats()">刷新统计</button>
            </div>
        </div>
    </div>

    <script src="/static/js/admin.js"></script>
    <script src="/static/js/agent-dashboard.js"></script>
    <script>
        // 全局变量
        let autoRefreshInterval = null;
        let isAutoRefresh = true;
        let lastUpdateTime = Date.now();

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initTheme();
            initAgentDashboard();
            startRealTimeMonitoring();
        });

        // 初始化代理端控制台
        function initAgentDashboard() {
            setActiveNavigation();
            loadInitialData();
            startAutoRefresh();
            initDepositMonitoring(); // 启动充值监控
        }

        // 加载初始数据
        function loadInitialData() {
            loadContractOrders();
            loadWithdrawalRequests();
            loadFinanceRecords();
        }

        // 开始实时监控
        function startRealTimeMonitoring() {
            // 每5秒更新一次数据
            setInterval(updateRealTimeData, 5000);

            // 每30秒检查新订单
            setInterval(checkNewOrders, 30000);
        }

        // 更新实时数据
        function updateRealTimeData() {
            fetch('/agent/dashboard/realtime-stats')
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    updateStatsDisplay(data.data);
                }
            })
            .catch(error => {
                console.error('更新实时数据失败:', error);
            });
        }

        // 更新统计显示
        function updateStatsDisplay(stats) {
            // 更新今日注册
            const todayRegisterEl = document.getElementById('todayRegister');
            if (todayRegisterEl && stats.today_register !== undefined) {
                todayRegisterEl.textContent = stats.today_register;
                animateNumber(todayRegisterEl);
            }

            // 更新今日充值
            const todayDepositEl = document.getElementById('todayDeposit');
            if (todayDepositEl && stats.today_deposit !== undefined) {
                todayDepositEl.textContent = parseFloat(stats.today_deposit).toFixed(2);
                animateNumber(todayDepositEl);
            }

            // 更新今日合约
            const todayContractsEl = document.getElementById('todayContracts');
            if (todayContractsEl && stats.today_contracts !== undefined) {
                todayContractsEl.textContent = stats.today_contracts;
                animateNumber(todayContractsEl);
            }

            // 更新待审核提币
            const pendingWithdrawalsEl = document.getElementById('pendingWithdrawals');
            if (pendingWithdrawalsEl && stats.pending_withdrawals !== undefined) {
                pendingWithdrawalsEl.textContent = stats.pending_withdrawals;
                animateNumber(pendingWithdrawalsEl);
            }
        }

        // 数字动画效果
        function animateNumber(element) {
            element.style.transform = 'scale(1.1)';
            element.style.color = 'var(--color-primary)';
            setTimeout(() => {
                element.style.transform = 'scale(1)';
                element.style.color = '';
            }, 300);
        }

        // 检查新订单和充值
        function checkNewOrders() {
            fetch('/agent/dashboard/check-new-orders')
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    // 检查新订单
                    if (data.data.has_new_orders) {
                        showNewOrderNotification(data.data.new_orders_count);
                        playNotificationSound();
                    }

                    // 检查新充值
                    if (data.data.has_new_deposits) {
                        showDepositAlert(data.data.new_deposits);
                        playDepositSound();
                    }
                }
            })
            .catch(error => {
                console.error('检查新订单和充值失败:', error);
            });
        }

        // 显示新订单通知
        function showNewOrderNotification(count) {
            const badge = document.getElementById('newOrdersBadge');
            if (badge) {
                badge.style.display = 'inline-block';
                badge.textContent = `${count} NEW`;
            }

            // 显示浮动通知
            showNotification(`有 ${count} 个新的合约订单需要处理！`, 'warning');
        }

        // 播放通知声音
        function playNotificationSound() {
            // 创建音频提示
            const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
            audio.play().catch(() => {
                // 忽略音频播放错误
            });
        }

        // 播放充值提示音
        function playDepositSound() {
            // 创建充值专用提示音（更愉悦的音调）
            const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
            audio.volume = 0.7;
            audio.play().catch(() => {
                // 忽略音频播放错误
            });
        }

        // 显示充值提醒
        function showDepositAlert(deposits) {
            const alertCard = document.getElementById('depositAlertCard');
            const alertsList = document.getElementById('depositAlertsList');
            const badge = document.getElementById('newDepositsBadge');

            if (!alertCard || !alertsList || !badge) return;

            // 更新徽章
            badge.textContent = deposits.length;

            // 生成充值提醒列表
            alertsList.innerHTML = deposits.map(deposit => `
                <div class="deposit-alert-item">
                    <div class="alert-icon">💰</div>
                    <div class="alert-content">
                        <div class="alert-title">
                            <strong>${deposit.username}</strong> 充值到账
                        </div>
                        <div class="alert-details">
                            <span class="amount">+${deposit.amount} ${deposit.coin_symbol}</span>
                            <span class="time">${formatTime(deposit.created_at)}</span>
                        </div>
                    </div>
                    <div class="alert-actions">
                        <button class="btn btn-sm btn-success" onclick="viewUserDetail(${deposit.user_id})">
                            查看用户
                        </button>
                    </div>
                </div>
            `).join('');

            // 显示提醒卡片
            alertCard.style.display = 'block';
            alertCard.scrollIntoView({ behavior: 'smooth', block: 'start' });

            // 添加闪烁效果
            alertCard.classList.add('deposit-alert-flash');
            setTimeout(() => {
                alertCard.classList.remove('deposit-alert-flash');
            }, 2000);
        }

        // 隐藏充值提醒
        function hideDepositAlert() {
            const alertCard = document.getElementById('depositAlertCard');
            if (alertCard) {
                alertCard.style.display = 'none';
            }
        }

        // 查看用户详情
        function viewUserDetail(userId) {
            window.open(`/agent/dashboard/user-detail/${userId}`, '_blank');
        }

        // 显示一键控制模态框
        function showBatchControlModal() {
            document.getElementById('batchControlModal').style.display = 'flex';
            loadControlStats();
        }

        // 隐藏一键控制模态框
        function hideBatchControlModal() {
            document.getElementById('batchControlModal').style.display = 'none';
        }

        // 加载控制统计
        function loadControlStats() {
            fetch('/agent/dashboard/contract-control-stats')
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    updateControlStats(data.data);
                }
            })
            .catch(error => {
                console.error('加载控制统计失败:', error);
            });
        }

        // 更新控制统计显示
        function updateControlStats(stats) {
            // 待处理统计
            const pendingCountEl = document.getElementById('pendingContractsCount');
            const pendingAmountEl = document.getElementById('pendingContractsAmount');
            if (pendingCountEl) pendingCountEl.textContent = stats.pending.count;
            if (pendingAmountEl) pendingAmountEl.textContent = stats.pending.amount + ' USDT';

            // 今日统计
            const todayWinRateEl = document.getElementById('todayWinRate');
            const todayTotalCountEl = document.getElementById('todayTotalCount');
            const todayWinCountEl = document.getElementById('todayWinCount');
            const todayLossCountEl = document.getElementById('todayLossCount');
            const todayProfitLossEl = document.getElementById('todayProfitLoss');

            if (todayWinRateEl) todayWinRateEl.textContent = stats.today.win_rate + '%';
            if (todayTotalCountEl) todayTotalCountEl.textContent = stats.today.total_count;
            if (todayWinCountEl) todayWinCountEl.textContent = stats.today.win_count;
            if (todayLossCountEl) todayLossCountEl.textContent = stats.today.loss_count;
            if (todayProfitLossEl) {
                const profitLoss = stats.today.total_profit_loss;
                todayProfitLossEl.textContent = profitLoss + ' USDT';
                todayProfitLossEl.className = profitLoss >= 0 ? 'text-success' : 'text-danger';
            }
        }

        // 批量控制合约
        function batchControlContracts(action) {
            const actionNames = {
                'all_win': '全部盈利',
                'all_loss': '全部亏损',
                'smart_control': '智能控制'
            };

            if (!confirm(`确定要执行"${actionNames[action]}"操作吗？这将影响所有待处理的合约订单！`)) {
                return;
            }

            const formData = new FormData();
            formData.append('action', action);

            // 显示加载状态
            const loadingBtn = event.target;
            const originalText = loadingBtn.innerHTML;
            loadingBtn.innerHTML = '<span class="loading-spinner"></span> 处理中...';
            loadingBtn.disabled = true;

            fetch('/agent/dashboard/batch-set-contract-result', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    showNotification(data.msg, 'success');
                    loadControlStats(); // 刷新统计
                    loadContractOrders(); // 刷新合约列表
                } else {
                    showNotification(data.msg || '操作失败', 'error');
                }
            })
            .catch(error => {
                console.error('批量控制失败:', error);
                showNotification('网络错误，请重试', 'error');
            })
            .finally(() => {
                // 恢复按钮状态
                loadingBtn.innerHTML = originalText;
                loadingBtn.disabled = false;
            });
        }

        // 切换自动刷新
        function toggleAutoRefresh() {
            isAutoRefresh = !isAutoRefresh;
            const btn = document.getElementById('autoRefreshBtn');
            const icon = document.getElementById('refreshIcon');

            if (isAutoRefresh) {
                startAutoRefresh();
                icon.textContent = '⏸️';
                btn.classList.remove('btn-outline');
                btn.classList.add('btn-success');
            } else {
                stopAutoRefresh();
                icon.textContent = '▶️';
                btn.classList.remove('btn-success');
                btn.classList.add('btn-outline');
            }
        }

        // 开始自动刷新
        function startAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
            autoRefreshInterval = setInterval(() => {
                if (isAutoRefresh) {
                    loadContractOrders();
                    loadWithdrawalRequests();
                }
            }, 10000); // 每10秒刷新一次
        }

        // 停止自动刷新
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
        }

        // 主题切换
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const themeIcon = document.getElementById('themeIcon');
            if (themeIcon) {
                themeIcon.textContent = newTheme === 'dark' ? '🌙' : '☀️';
            }
        }

        // 初始化主题
        function initTheme() {
            const savedTheme = localStorage.getItem('theme') || 'dark';
            document.documentElement.setAttribute('data-theme', savedTheme);

            const themeIcon = document.getElementById('themeIcon');
            if (themeIcon) {
                themeIcon.textContent = savedTheme === 'dark' ? '🌙' : '☀️';
            }
        }

        // 设置导航高亮
        function setActiveNavigation() {
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.nav-item');

            navItems.forEach(item => {
                item.classList.remove('active');
                if (item.getAttribute('href') === currentPath) {
                    item.classList.add('active');
                }
            });
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <span>${message}</span>
                <button onclick="this.parentElement.remove()">×</button>
            `;

            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 16px;
                border-radius: 4px;
                color: white;
                z-index: 10000;
                display: flex;
                align-items: center;
                gap: 8px;
                background: ${type === 'success' ? '#0ECB81' : type === 'error' ? '#F6465D' : type === 'warning' ? '#FF8F00' : '#1890FF'};
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                animation: slideIn 0.3s ease;
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideOut 0.3s ease';
                    setTimeout(() => notification.remove(), 300);
                }
            }, 5000);
        }
    </script>

    <style>
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }

        .realtime-indicator {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</body>
</html>
