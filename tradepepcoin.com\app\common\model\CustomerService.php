<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 客服聊天消息模型
 */
class CustomerService extends Model
{
    protected $table = 'ce_customer_service';
    
    protected $pk = 'id';
    
    protected $autoWriteTimestamp = false;
    
    // 消息类型常量
    const TYPE_TEXT = 1;      // 文本消息
    const TYPE_IMAGE = 2;     // 图片消息
    const TYPE_VIDEO = 3;     // 视频消息
    const TYPE_EMOJI = 4;     // 表情消息
    const TYPE_FILE = 5;      // 文件消息
    const TYPE_SYSTEM = 6;    // 系统消息
    
    // 发送者类型常量
    const SENDER_USER = 1;    // 用户发送
    const SENDER_AGENT = 2;   // 代理商发送
    const SENDER_ADMIN = 3;   // 管理员发送
    const SENDER_SYSTEM = 4;  // 系统发送
    
    // 消息状态常量
    const STATUS_UNREAD = 0;  // 未读
    const STATUS_READ = 1;    // 已读
    const STATUS_DELETED = 2; // 已删除
    
    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
    
    /**
     * 关联代理商
     */
    public function agent()
    {
        return $this->belongsTo(User::class, 'agent_id', 'id');
    }
    
    /**
     * 关联发送者
     */
    public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id', 'id');
    }
    
    /**
     * 消息类型文本
     */
    public function getTypeTextAttr($value, $data)
    {
        $types = [
            self::TYPE_TEXT => '文本',
            self::TYPE_IMAGE => '图片',
            self::TYPE_VIDEO => '视频',
            self::TYPE_EMOJI => '表情',
            self::TYPE_FILE => '文件',
            self::TYPE_SYSTEM => '系统'
        ];
        
        return $types[$data['type']] ?? '未知';
    }
    
    /**
     * 发送者类型文本
     */
    public function getSenderTypeTextAttr($value, $data)
    {
        $types = [
            self::SENDER_USER => '用户',
            self::SENDER_AGENT => '代理商',
            self::SENDER_ADMIN => '管理员',
            self::SENDER_SYSTEM => '系统'
        ];
        
        return $types[$data['sender_type']] ?? '未知';
    }
    
    /**
     * 创建消息
     */
    public static function createMessage(array $data): array
    {
        try {
            // 获取用户信息
            $user = User::find($data['user_id']);
            if (!$user) {
                return ['code' => 0, 'msg' => '用户不存在'];
            }
            
            $message = self::create([
                'user_id' => $data['user_id'],
                'agent_id' => $user->agent_id,
                'sender_id' => $data['sender_id'],
                'sender_type' => $data['sender_type'],
                'type' => $data['type'],
                'content' => $data['content'],
                'media_url' => $data['media_url'] ?? '',
                'status' => self::STATUS_UNREAD,
                'created_at' => date('Y-m-d H:i:s'),
            ]);
            
            return ['code' => 1, 'msg' => '消息发送成功', 'data' => $message];
            
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '消息发送失败：' . $e->getMessage()];
        }
    }
    
    /**
     * 获取对话列表
     */
    public static function getConversations(int $agentId = 0, int $limit = 20): array
    {
        $query = self::alias('cs')
                    ->join('ce_users u', 'cs.user_id = u.id')
                    ->field([
                        'cs.user_id',
                        'u.username',
                        'u.avatar',
                        'MAX(cs.created_at) as last_message_time',
                        'COUNT(CASE WHEN cs.status = 0 AND cs.sender_type = 1 THEN 1 END) as unread_count',
                        'cs.content as last_message',
                        'cs.type as last_message_type'
                    ])
                    ->where('u.user_type', 1); // 只显示正式用户
        
        if ($agentId > 0) {
            $query->where('cs.agent_id', $agentId);
        }
        
        $conversations = $query->group('cs.user_id')
                              ->order('last_message_time', 'desc')
                              ->limit($limit)
                              ->select()
                              ->toArray();
        
        return $conversations;
    }
    
    /**
     * 获取对话消息
     */
    public static function getMessages(int $userId, int $agentId = 0, int $page = 1, int $limit = 50): array
    {
        $query = self::alias('cs')
                    ->join('ce_users u', 'cs.sender_id = u.id', 'LEFT')
                    ->where('cs.user_id', $userId)
                    ->field([
                        'cs.*',
                        'u.username as sender_name',
                        'u.avatar as sender_avatar'
                    ]);
        
        if ($agentId > 0) {
            $query->where('cs.agent_id', $agentId);
        }
        
        $total = $query->count();
        $messages = $query->order('cs.created_at', 'desc')
                         ->page($page, $limit)
                         ->select()
                         ->toArray();
        
        // 反转消息顺序，最新的在底部
        $messages = array_reverse($messages);
        
        return [
            'messages' => $messages,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }
    
    /**
     * 标记消息为已读
     */
    public static function markAsRead(int $userId, int $receiverType): bool
    {
        try {
            $where = ['user_id' => $userId, 'status' => self::STATUS_UNREAD];
            
            // 根据接收者类型标记不同的消息为已读
            if ($receiverType == self::SENDER_AGENT) {
                // 代理商标记用户消息为已读
                $where['sender_type'] = self::SENDER_USER;
            } elseif ($receiverType == self::SENDER_USER) {
                // 用户标记代理商/管理员消息为已读
                $where['sender_type'] = ['in', [self::SENDER_AGENT, self::SENDER_ADMIN]];
            }
            
            self::where($where)->update(['status' => self::STATUS_READ]);
            
            return true;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取未读消息数量
     */
    public static function getUnreadCount(int $userId, int $receiverType): int
    {
        $where = ['user_id' => $userId, 'status' => self::STATUS_UNREAD];
        
        if ($receiverType == self::SENDER_AGENT) {
            // 代理商查看用户发送的未读消息
            $where['sender_type'] = self::SENDER_USER;
        } elseif ($receiverType == self::SENDER_USER) {
            // 用户查看代理商/管理员发送的未读消息
            $where['sender_type'] = ['in', [self::SENDER_AGENT, self::SENDER_ADMIN]];
        }
        
        return self::where($where)->count();
    }
    
    /**
     * 获取代理商未读消息统计
     */
    public static function getAgentUnreadStats(int $agentId): array
    {
        // 获取该代理商下级用户的未读消息
        $unreadMessages = self::alias('cs')
                             ->join('ce_users u', 'cs.user_id = u.id')
                             ->where('cs.agent_id', $agentId)
                             ->where('cs.sender_type', self::SENDER_USER)
                             ->where('cs.status', self::STATUS_UNREAD)
                             ->where('u.user_type', 1)
                             ->field([
                                 'cs.user_id',
                                 'u.username',
                                 'COUNT(*) as unread_count'
                             ])
                             ->group('cs.user_id')
                             ->select()
                             ->toArray();
        
        $totalUnread = array_sum(array_column($unreadMessages, 'unread_count'));
        
        return [
            'total_unread' => $totalUnread,
            'user_unread' => $unreadMessages
        ];
    }
    
    /**
     * 删除对话
     */
    public static function deleteConversation(int $userId, int $agentId = 0): bool
    {
        try {
            $where = ['user_id' => $userId];
            
            if ($agentId > 0) {
                $where['agent_id'] = $agentId;
            }
            
            self::where($where)->update(['status' => self::STATUS_DELETED]);
            
            return true;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取客服统计
     */
    public static function getServiceStats(int $agentId = 0): array
    {
        $where = [];
        if ($agentId > 0) {
            $where['agent_id'] = $agentId;
        }
        
        // 今日消息数
        $todayMessages = self::where($where)
                            ->whereTime('created_at', 'today')
                            ->count();
        
        // 总消息数
        $totalMessages = self::where($where)->count();
        
        // 活跃对话数
        $activeConversations = self::where($where)
                                  ->whereTime('created_at', 'today')
                                  ->group('user_id')
                                  ->count();
        
        // 未读消息数
        $unreadMessages = self::where($where)
                             ->where('status', self::STATUS_UNREAD)
                             ->where('sender_type', self::SENDER_USER)
                             ->count();
        
        return [
            'today_messages' => $todayMessages,
            'total_messages' => $totalMessages,
            'active_conversations' => $activeConversations,
            'unread_messages' => $unreadMessages,
        ];
    }
}
