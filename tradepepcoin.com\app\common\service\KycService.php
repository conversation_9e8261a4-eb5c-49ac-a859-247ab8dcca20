<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\User;
use app\common\model\KycRecord;
use think\facade\Log;
use think\facade\Cache;

/**
 * KYC认证服务类
 */
class KycService
{
    // KYC等级
    const LEVEL_NONE = 0;        // 未认证
    const LEVEL_BASIC = 1;       // 基础认证
    const LEVEL_ADVANCED = 2;    // 高级认证
    const LEVEL_PREMIUM = 3;     // 专业认证

    // KYC状态
    const STATUS_PENDING = 0;    // 待审核
    const STATUS_APPROVED = 1;   // 已通过
    const STATUS_REJECTED = 2;   // 已拒绝
    const STATUS_EXPIRED = 3;    // 已过期

    // 支持的证件类型
    const ID_TYPES = [
        'id_card' => '身份证',
        'passport' => '护照',
        'driver_license' => '驾驶证',
        'residence_permit' => '居住证'
    ];

    /**
     * 提交KYC认证
     */
    public function submitKyc(int $userId, array $data): array
    {
        try {
            // 验证用户
            $user = User::find($userId);
            if (!$user) {
                return ['code' => 0, 'msg' => '用户不存在'];
            }

            // 验证数据
            $validation = $this->validateKycData($data);
            if (!$validation['code']) {
                return $validation;
            }

            // 检查是否已有待审核的申请
            $existingRecord = KycRecord::where('user_id', $userId)
                                     ->where('status', self::STATUS_PENDING)
                                     ->find();
            
            if ($existingRecord) {
                return ['code' => 0, 'msg' => '您已有待审核的KYC申请'];
            }

            // 创建KYC记录
            $kycData = [
                'user_id' => $userId,
                'level' => $data['level'],
                'real_name' => $data['real_name'],
                'id_type' => $data['id_type'],
                'id_number' => $data['id_number'],
                'birthday' => $data['birthday'] ?? '',
                'nationality' => $data['nationality'] ?? '',
                'address' => $data['address'] ?? '',
                'occupation' => $data['occupation'] ?? '',
                'income_source' => $data['income_source'] ?? '',
                'id_front_image' => $data['id_front_image'] ?? '',
                'id_back_image' => $data['id_back_image'] ?? '',
                'selfie_image' => $data['selfie_image'] ?? '',
                'proof_of_address' => $data['proof_of_address'] ?? '',
                'status' => self::STATUS_PENDING,
                'submitted_at' => date('Y-m-d H:i:s')
            ];

            $kycRecord = KycRecord::create($kycData);
            
            if ($kycRecord) {
                // 调用第三方KYC服务进行自动审核
                $this->processAutoVerification($kycRecord);
                
                Log::info("KYC申请提交成功", [
                    'user_id' => $userId,
                    'kyc_id' => $kycRecord->id,
                    'level' => $data['level']
                ]);

                return [
                    'code' => 1,
                    'msg' => 'KYC申请提交成功',
                    'data' => [
                        'kyc_id' => $kycRecord->id,
                        'status' => 'pending'
                    ]
                ];
            } else {
                return ['code' => 0, 'msg' => 'KYC申请提交失败'];
            }
        } catch (\Exception $e) {
            Log::error('KYC申请提交失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'KYC服务异常'];
        }
    }

    /**
     * 自动验证处理
     */
    private function processAutoVerification(KycRecord $kycRecord): void
    {
        try {
            // 身份证OCR识别
            $ocrResult = $this->performOcr($kycRecord);
            
            // 人脸识别验证
            $faceResult = $this->performFaceVerification($kycRecord);
            
            // 身份信息验证
            $identityResult = $this->performIdentityVerification($kycRecord);
            
            // 地址验证
            $addressResult = $this->performAddressVerification($kycRecord);

            // 综合评分
            $score = $this->calculateVerificationScore([
                'ocr' => $ocrResult,
                'face' => $faceResult,
                'identity' => $identityResult,
                'address' => $addressResult
            ]);

            // 更新验证结果
            $kycRecord->verification_score = $score;
            $kycRecord->verification_details = json_encode([
                'ocr_result' => $ocrResult,
                'face_result' => $faceResult,
                'identity_result' => $identityResult,
                'address_result' => $addressResult
            ]);

            // 自动审核决策
            if ($score >= 80) {
                $this->approveKyc($kycRecord->id, '自动审核通过');
            } elseif ($score < 50) {
                $this->rejectKyc($kycRecord->id, '自动审核未通过，请检查提交的资料');
            } else {
                // 需要人工审核
                $kycRecord->status = self::STATUS_PENDING;
                $kycRecord->save();
                
                // 通知管理员进行人工审核
                $this->notifyManualReview($kycRecord);
            }
        } catch (\Exception $e) {
            Log::error('自动验证处理失败: ' . $e->getMessage());
        }
    }

    /**
     * OCR识别
     */
    private function performOcr(KycRecord $kycRecord): array
    {
        try {
            $config = config('kyc.ocr');
            
            // 调用第三方OCR服务
            $result = $this->callOcrApi($kycRecord->id_front_image, $config);
            
            if ($result['success']) {
                // 比对OCR结果与用户填写信息
                $accuracy = $this->compareOcrResult($result['data'], [
                    'name' => $kycRecord->real_name,
                    'id_number' => $kycRecord->id_number
                ]);

                return [
                    'success' => true,
                    'accuracy' => $accuracy,
                    'data' => $result['data']
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $result['error']
                ];
            }
        } catch (\Exception $e) {
            Log::error('OCR识别失败: ' . $e->getMessage());
            return ['success' => false, 'error' => 'OCR服务异常'];
        }
    }

    /**
     * 人脸识别验证
     */
    private function performFaceVerification(KycRecord $kycRecord): array
    {
        try {
            $config = config('kyc.face_recognition');
            
            // 调用人脸识别API
            $result = $this->callFaceApi($kycRecord->id_front_image, $kycRecord->selfie_image, $config);
            
            return [
                'success' => $result['success'],
                'similarity' => $result['similarity'] ?? 0,
                'confidence' => $result['confidence'] ?? 0
            ];
        } catch (\Exception $e) {
            Log::error('人脸识别失败: ' . $e->getMessage());
            return ['success' => false, 'error' => '人脸识别服务异常'];
        }
    }

    /**
     * 身份信息验证
     */
    private function performIdentityVerification(KycRecord $kycRecord): array
    {
        try {
            $config = config('kyc.identity_verification');
            
            // 调用身份验证API
            $result = $this->callIdentityApi([
                'name' => $kycRecord->real_name,
                'id_number' => $kycRecord->id_number,
                'id_type' => $kycRecord->id_type
            ], $config);
            
            return [
                'success' => $result['success'],
                'verified' => $result['verified'] ?? false,
                'details' => $result['details'] ?? []
            ];
        } catch (\Exception $e) {
            Log::error('身份验证失败: ' . $e->getMessage());
            return ['success' => false, 'error' => '身份验证服务异常'];
        }
    }

    /**
     * 地址验证
     */
    private function performAddressVerification(KycRecord $kycRecord): array
    {
        try {
            if (empty($kycRecord->proof_of_address)) {
                return ['success' => true, 'verified' => false, 'reason' => '未提供地址证明'];
            }

            $config = config('kyc.address_verification');
            
            // 调用地址验证API
            $result = $this->callAddressApi($kycRecord->proof_of_address, $kycRecord->address, $config);
            
            return [
                'success' => $result['success'],
                'verified' => $result['verified'] ?? false,
                'confidence' => $result['confidence'] ?? 0
            ];
        } catch (\Exception $e) {
            Log::error('地址验证失败: ' . $e->getMessage());
            return ['success' => false, 'error' => '地址验证服务异常'];
        }
    }

    /**
     * 计算验证分数
     */
    private function calculateVerificationScore(array $results): int
    {
        $score = 0;
        $weights = [
            'ocr' => 30,
            'face' => 25,
            'identity' => 35,
            'address' => 10
        ];

        foreach ($results as $type => $result) {
            if (!$result['success']) {
                continue;
            }

            $typeScore = 0;
            switch ($type) {
                case 'ocr':
                    $typeScore = $result['accuracy'] ?? 0;
                    break;
                case 'face':
                    $typeScore = $result['similarity'] ?? 0;
                    break;
                case 'identity':
                    $typeScore = $result['verified'] ? 100 : 0;
                    break;
                case 'address':
                    $typeScore = $result['verified'] ? 100 : 50;
                    break;
            }

            $score += ($typeScore * $weights[$type]) / 100;
        }

        return min(100, max(0, (int)$score));
    }

    /**
     * 审核通过
     */
    public function approveKyc(int $kycId, string $remark = ''): array
    {
        try {
            $kycRecord = KycRecord::find($kycId);
            if (!$kycRecord) {
                return ['code' => 0, 'msg' => 'KYC记录不存在'];
            }

            if ($kycRecord->status !== self::STATUS_PENDING) {
                return ['code' => 0, 'msg' => 'KYC状态不正确'];
            }

            // 更新KYC记录
            $kycRecord->status = self::STATUS_APPROVED;
            $kycRecord->approved_at = date('Y-m-d H:i:s');
            $kycRecord->remark = $remark;
            $kycRecord->save();

            // 更新用户KYC等级
            $user = User::find($kycRecord->user_id);
            if ($user) {
                $user->kyc_level = $kycRecord->level;
                $user->kyc_status = self::STATUS_APPROVED;
                $user->save();
            }

            // 发送通知
            $this->sendKycNotification($kycRecord->user_id, 'approved', $kycRecord->level);

            Log::info("KYC审核通过", [
                'kyc_id' => $kycId,
                'user_id' => $kycRecord->user_id,
                'level' => $kycRecord->level
            ]);

            return ['code' => 1, 'msg' => 'KYC审核通过'];
        } catch (\Exception $e) {
            Log::error('KYC审核通过失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'KYC审核失败'];
        }
    }

    /**
     * 审核拒绝
     */
    public function rejectKyc(int $kycId, string $reason = ''): array
    {
        try {
            $kycRecord = KycRecord::find($kycId);
            if (!$kycRecord) {
                return ['code' => 0, 'msg' => 'KYC记录不存在'];
            }

            if ($kycRecord->status !== self::STATUS_PENDING) {
                return ['code' => 0, 'msg' => 'KYC状态不正确'];
            }

            // 更新KYC记录
            $kycRecord->status = self::STATUS_REJECTED;
            $kycRecord->rejected_at = date('Y-m-d H:i:s');
            $kycRecord->reject_reason = $reason;
            $kycRecord->save();

            // 发送通知
            $this->sendKycNotification($kycRecord->user_id, 'rejected', $kycRecord->level, $reason);

            Log::info("KYC审核拒绝", [
                'kyc_id' => $kycId,
                'user_id' => $kycRecord->user_id,
                'reason' => $reason
            ]);

            return ['code' => 1, 'msg' => 'KYC审核拒绝'];
        } catch (\Exception $e) {
            Log::error('KYC审核拒绝失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'KYC审核失败'];
        }
    }

    /**
     * 获取用户KYC状态
     */
    public function getUserKycStatus(int $userId): array
    {
        $user = User::find($userId);
        if (!$user) {
            return ['code' => 0, 'msg' => '用户不存在'];
        }

        $kycRecord = KycRecord::where('user_id', $userId)
                             ->order('created_at', 'desc')
                             ->find();

        return [
            'code' => 1,
            'data' => [
                'kyc_level' => $user->kyc_level ?? self::LEVEL_NONE,
                'kyc_status' => $user->kyc_status ?? self::STATUS_PENDING,
                'current_record' => $kycRecord ? $kycRecord->toArray() : null,
                'can_submit' => $this->canSubmitKyc($userId),
                'limits' => $this->getKycLimits($user->kyc_level ?? self::LEVEL_NONE)
            ]
        ];
    }

    /**
     * 检查是否可以提交KYC
     */
    private function canSubmitKyc(int $userId): bool
    {
        $pendingRecord = KycRecord::where('user_id', $userId)
                                 ->where('status', self::STATUS_PENDING)
                                 ->find();

        return !$pendingRecord;
    }

    /**
     * 获取KYC等级限制
     */
    private function getKycLimits(int $level): array
    {
        $limits = [
            self::LEVEL_NONE => [
                'daily_withdraw' => 0,
                'monthly_withdraw' => 0,
                'trade_limit' => 1000
            ],
            self::LEVEL_BASIC => [
                'daily_withdraw' => 2000,
                'monthly_withdraw' => 20000,
                'trade_limit' => 10000
            ],
            self::LEVEL_ADVANCED => [
                'daily_withdraw' => 10000,
                'monthly_withdraw' => 100000,
                'trade_limit' => 100000
            ],
            self::LEVEL_PREMIUM => [
                'daily_withdraw' => 50000,
                'monthly_withdraw' => 500000,
                'trade_limit' => 1000000
            ]
        ];

        return $limits[$level] ?? $limits[self::LEVEL_NONE];
    }

    /**
     * 验证KYC数据
     */
    private function validateKycData(array $data): array
    {
        if (empty($data['level']) || !in_array($data['level'], [self::LEVEL_BASIC, self::LEVEL_ADVANCED, self::LEVEL_PREMIUM])) {
            return ['code' => 0, 'msg' => 'KYC等级无效'];
        }

        if (empty($data['real_name'])) {
            return ['code' => 0, 'msg' => '真实姓名不能为空'];
        }

        if (empty($data['id_type']) || !isset(self::ID_TYPES[$data['id_type']])) {
            return ['code' => 0, 'msg' => '证件类型无效'];
        }

        if (empty($data['id_number'])) {
            return ['code' => 0, 'msg' => '证件号码不能为空'];
        }

        if (empty($data['id_front_image'])) {
            return ['code' => 0, 'msg' => '证件正面照片不能为空'];
        }

        if (empty($data['selfie_image'])) {
            return ['code' => 0, 'msg' => '自拍照片不能为空'];
        }

        return ['code' => 1, 'msg' => '验证通过'];
    }

    /**
     * 发送KYC通知
     */
    private function sendKycNotification(int $userId, string $status, int $level, string $reason = ''): void
    {
        $notificationService = new NotificationService();
        
        $titles = [
            'approved' => 'KYC认证通过',
            'rejected' => 'KYC认证未通过'
        ];

        $contents = [
            'approved' => "恭喜！您的KYC{$level}级认证已通过审核。",
            'rejected' => "很抱歉，您的KYC{$level}级认证未通过审核。原因：{$reason}"
        ];

        $notificationService->sendInternalNotification(
            $userId,
            'kyc',
            $titles[$status],
            $contents[$status]
        );
    }

    /**
     * 通知人工审核
     */
    private function notifyManualReview(KycRecord $kycRecord): void
    {
        // 这里可以发送邮件或其他方式通知管理员
        Log::info("需要人工审核KYC", [
            'kyc_id' => $kycRecord->id,
            'user_id' => $kycRecord->user_id,
            'level' => $kycRecord->level
        ]);
    }

    /**
     * 调用OCR API（模拟）
     */
    private function callOcrApi(string $imageUrl, array $config): array
    {
        // 模拟OCR结果
        return [
            'success' => true,
            'data' => [
                'name' => '张三',
                'id_number' => '110101199001011234',
                'confidence' => 0.95
            ]
        ];
    }

    /**
     * 调用人脸识别API（模拟）
     */
    private function callFaceApi(string $idImage, string $selfieImage, array $config): array
    {
        // 模拟人脸识别结果
        return [
            'success' => true,
            'similarity' => 85,
            'confidence' => 0.9
        ];
    }

    /**
     * 调用身份验证API（模拟）
     */
    private function callIdentityApi(array $data, array $config): array
    {
        // 模拟身份验证结果
        return [
            'success' => true,
            'verified' => true,
            'details' => ['status' => 'valid']
        ];
    }

    /**
     * 调用地址验证API（模拟）
     */
    private function callAddressApi(string $proofImage, string $address, array $config): array
    {
        // 模拟地址验证结果
        return [
            'success' => true,
            'verified' => true,
            'confidence' => 0.8
        ];
    }

    /**
     * 比对OCR结果
     */
    private function compareOcrResult(array $ocrData, array $userData): int
    {
        $score = 0;
        
        if (isset($ocrData['name']) && $ocrData['name'] === $userData['name']) {
            $score += 50;
        }
        
        if (isset($ocrData['id_number']) && $ocrData['id_number'] === $userData['id_number']) {
            $score += 50;
        }
        
        return $score;
    }
}
