<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\BaseController;
use app\common\model\Order;
use app\common\model\Trade as TradeModel;
use app\common\model\UserAsset;
use app\common\service\TradeService;
use think\facade\Session;

/**
 * 交易API控制器
 */
class Trade extends BaseController
{
    protected $tradeService;

    public function initialize()
    {
        parent::initialize();
        $this->tradeService = new TradeService();
        
        // API需要认证
        $this->checkAuth();
    }

    /**
     * 下单
     */
    public function order()
    {
        if (!$this->request->isPost()) {
            return $this->error('请求方式错误');
        }

        $data = $this->request->post();
        
        // 验证参数
        $validate = [
            'symbol' => 'require',
            'side' => 'require|in:BUY,SELL',
            'type' => 'require|in:LIMIT,MARKET',
            'quantity' => 'require|float|gt:0'
        ];

        if ($data['type'] == 'LIMIT' && empty($data['price'])) {
            return $this->error('限价单必须指定价格');
        }

        $result = $this->validate($data, $validate);
        if ($result !== true) {
            return $this->error($result);
        }

        // 转换参数格式
        $orderData = [
            'symbol' => $data['symbol'],
            'type' => strtolower($data['side']),
            'order_type' => strtolower($data['type']),
            'amount' => $data['quantity'],
            'price' => $data['price'] ?? 0
        ];

        $userId = $this->getUserId();
        $result = $this->tradeService->createOrder($userId, $orderData);

        if ($result['code']) {
            return $this->success([
                'symbol' => $data['symbol'],
                'order_id' => $result['data']['order_id'],
                'client_order_id' => $data['new_client_order_id'] ?? '',
                'transact_time' => time() * 1000
            ]);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 取消订单
     */
    public function cancelOrder()
    {
        if (!$this->request->isDelete() && !$this->request->isPost()) {
            return $this->error('请求方式错误');
        }

        $symbol = input('symbol', '', 'trim');
        $orderId = input('order_id', '', 'trim');
        $clientOrderId = input('orig_client_order_id', '', 'trim');

        if (empty($symbol)) {
            return $this->error('交易对不能为空');
        }

        if (empty($orderId) && empty($clientOrderId)) {
            return $this->error('订单ID不能为空');
        }

        $userId = $this->getUserId();
        
        // 查找订单
        $where = ['user_id' => $userId, 'symbol' => $symbol];
        if ($orderId) {
            $where['order_id'] = $orderId;
        } else {
            $where['client_order_id'] = $clientOrderId;
        }

        $order = Order::where($where)->find();
        if (!$order) {
            return $this->error('订单不存在');
        }

        $result = $this->tradeService->cancelOrder($order->order_id);

        if ($result['code']) {
            return $this->success([
                'symbol' => $order->symbol,
                'order_id' => $order->order_id,
                'client_order_id' => $order->client_order_id ?? '',
                'status' => 'CANCELED'
            ]);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 查询订单
     */
    public function getOrder()
    {
        $symbol = input('symbol', '', 'trim');
        $orderId = input('order_id', '', 'trim');
        $clientOrderId = input('orig_client_order_id', '', 'trim');

        if (empty($symbol)) {
            return $this->error('交易对不能为空');
        }

        if (empty($orderId) && empty($clientOrderId)) {
            return $this->error('订单ID不能为空');
        }

        $userId = $this->getUserId();
        
        // 查找订单
        $where = ['user_id' => $userId, 'symbol' => $symbol];
        if ($orderId) {
            $where['order_id'] = $orderId;
        } else {
            $where['client_order_id'] = $clientOrderId;
        }

        $order = Order::where($where)->find();
        if (!$order) {
            return $this->error('订单不存在');
        }

        return $this->success([
            'symbol' => $order->symbol,
            'order_id' => $order->order_id,
            'client_order_id' => $order->client_order_id ?? '',
            'price' => $order->price,
            'orig_qty' => $order->amount,
            'executed_qty' => $order->filled_amount,
            'cummulative_quote_qty' => $order->filled_total,
            'status' => $this->getOrderStatus($order->status),
            'time_in_force' => 'GTC',
            'type' => strtoupper($order->order_type),
            'side' => strtoupper($order->type),
            'time' => strtotime($order->created_at) * 1000,
            'update_time' => strtotime($order->updated_at) * 1000
        ]);
    }

    /**
     * 查询当前挂单
     */
    public function openOrders()
    {
        $symbol = input('symbol', '', 'trim');
        $userId = $this->getUserId();

        $where = ['user_id' => $userId, 'status' => ['in', [0, 1]]];
        if ($symbol) {
            $where['symbol'] = $symbol;
        }

        $orders = Order::where($where)
                      ->order('created_at', 'desc')
                      ->select();

        $data = [];
        foreach ($orders as $order) {
            $data[] = [
                'symbol' => $order->symbol,
                'order_id' => $order->order_id,
                'client_order_id' => $order->client_order_id ?? '',
                'price' => $order->price,
                'orig_qty' => $order->amount,
                'executed_qty' => $order->filled_amount,
                'cummulative_quote_qty' => $order->filled_total,
                'status' => $this->getOrderStatus($order->status),
                'time_in_force' => 'GTC',
                'type' => strtoupper($order->order_type),
                'side' => strtoupper($order->type),
                'time' => strtotime($order->created_at) * 1000,
                'update_time' => strtotime($order->updated_at) * 1000
            ];
        }

        return $this->success($data);
    }

    /**
     * 查询历史订单
     */
    public function allOrders()
    {
        $symbol = input('symbol', '', 'trim');
        $orderId = input('order_id/d', 0);
        $startTime = input('start_time/d', 0);
        $endTime = input('end_time/d', 0);
        $limit = input('limit/d', 500);

        if (empty($symbol)) {
            return $this->error('交易对不能为空');
        }

        $userId = $this->getUserId();
        $where = ['user_id' => $userId, 'symbol' => $symbol];

        if ($orderId > 0) {
            $where[] = ['id', '>=', $orderId];
        }

        if ($startTime > 0) {
            $where[] = ['created_at', '>=', date('Y-m-d H:i:s', $startTime / 1000)];
        }

        if ($endTime > 0) {
            $where[] = ['created_at', '<=', date('Y-m-d H:i:s', $endTime / 1000)];
        }

        $orders = Order::where($where)
                      ->order('created_at', 'desc')
                      ->limit($limit)
                      ->select();

        $data = [];
        foreach ($orders as $order) {
            $data[] = [
                'symbol' => $order->symbol,
                'order_id' => $order->order_id,
                'client_order_id' => $order->client_order_id ?? '',
                'price' => $order->price,
                'orig_qty' => $order->amount,
                'executed_qty' => $order->filled_amount,
                'cummulative_quote_qty' => $order->filled_total,
                'status' => $this->getOrderStatus($order->status),
                'time_in_force' => 'GTC',
                'type' => strtoupper($order->order_type),
                'side' => strtoupper($order->type),
                'time' => strtotime($order->created_at) * 1000,
                'update_time' => strtotime($order->updated_at) * 1000
            ];
        }

        return $this->success($data);
    }

    /**
     * 查询成交历史
     */
    public function myTrades()
    {
        $symbol = input('symbol', '', 'trim');
        $orderId = input('order_id', '', 'trim');
        $startTime = input('start_time/d', 0);
        $endTime = input('end_time/d', 0);
        $fromId = input('from_id/d', 0);
        $limit = input('limit/d', 500);

        if (empty($symbol)) {
            return $this->error('交易对不能为空');
        }

        $userId = $this->getUserId();
        $where = ['user_id' => $userId, 'symbol' => $symbol];

        if ($orderId) {
            $where['order_id'] = $orderId;
        }

        if ($fromId > 0) {
            $where[] = ['id', '>=', $fromId];
        }

        if ($startTime > 0) {
            $where[] = ['created_at', '>=', date('Y-m-d H:i:s', $startTime / 1000)];
        }

        if ($endTime > 0) {
            $where[] = ['created_at', '<=', date('Y-m-d H:i:s', $endTime / 1000)];
        }

        $trades = TradeModel::where($where)
                           ->order('created_at', 'desc')
                           ->limit($limit)
                           ->select();

        $data = [];
        foreach ($trades as $trade) {
            $data[] = [
                'symbol' => $trade->symbol,
                'id' => $trade->id,
                'order_id' => $trade->order_id,
                'price' => $trade->price,
                'qty' => $trade->amount,
                'quote_qty' => $trade->total,
                'commission' => $trade->fee,
                'commission_asset' => $trade->fee_coin,
                'time' => strtotime($trade->created_at) * 1000,
                'is_buyer' => $trade->type == 'buy',
                'is_maker' => false
            ];
        }

        return $this->success($data);
    }

    /**
     * 获取账户信息
     */
    public function account()
    {
        $userId = $this->getUserId();
        
        $assets = UserAsset::where('user_id', $userId)
                          ->with('coin')
                          ->select();

        $balances = [];
        foreach ($assets as $asset) {
            $balances[] = [
                'asset' => $asset->coin_symbol,
                'free' => $asset->available,
                'locked' => $asset->frozen
            ];
        }

        return $this->success([
            'maker_commission' => 10,
            'taker_commission' => 10,
            'buyer_commission' => 0,
            'seller_commission' => 0,
            'can_trade' => true,
            'can_withdraw' => true,
            'can_deposit' => true,
            'update_time' => time() * 1000,
            'account_type' => 'SPOT',
            'balances' => $balances
        ]);
    }

    /**
     * 转换订单状态
     */
    private function getOrderStatus($status)
    {
        $statusMap = [
            0 => 'NEW',
            1 => 'PARTIALLY_FILLED',
            2 => 'FILLED',
            3 => 'CANCELED',
            4 => 'REJECTED'
        ];

        return $statusMap[$status] ?? 'NEW';
    }

    /**
     * 检查API认证
     */
    private function checkAuth()
    {
        $apiKey = $this->request->header('X-MBX-APIKEY');
        $signature = $this->request->header('X-MBX-SIGNATURE');
        $timestamp = input('timestamp/d', 0);

        // 简化的认证逻辑，实际应该验证签名
        if (empty($apiKey)) {
            // 尝试从Session获取用户ID
            if (!Session::has('user_id')) {
                $this->error('未授权访问', 401);
            }
        }
    }

    /**
     * 获取用户ID
     */
    private function getUserId()
    {
        // 从API Key获取用户ID或从Session获取
        return Session::get('user_id', 1);
    }
}
