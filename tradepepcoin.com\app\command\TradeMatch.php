<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\common\service\TradeService;
use app\common\model\TradingPair;

/**
 * 交易撮合命令 - 兼容老系统的自动撮合逻辑
 */
class TradeMatch extends Command
{
    protected function configure()
    {
        $this->setName('trade:match')
             ->setDescription('执行交易订单撮合');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始执行交易撮合...');
        
        $tradeService = new TradeService();
        $totalMatched = 0;
        
        try {
            // 获取所有活跃的交易对
            $tradingPairs = TradingPair::where('is_active', 1)->select();
            
            foreach ($tradingPairs as $pair) {
                $output->writeln("处理交易对: {$pair->symbol}");
                
                // 执行撮合
                $matched = $tradeService->matchLimitOrders($pair->symbol);
                $totalMatched += $matched;
                
                if ($matched > 0) {
                    $output->writeln("  撮合成功: {$matched} 笔");
                }
                
                // 避免过于频繁的数据库操作
                usleep(100000); // 0.1秒
            }
            
            $output->writeln("撮合完成，总计撮合: {$totalMatched} 笔");
            
        } catch (\Exception $e) {
            $output->writeln("撮合过程中发生错误: " . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
}
