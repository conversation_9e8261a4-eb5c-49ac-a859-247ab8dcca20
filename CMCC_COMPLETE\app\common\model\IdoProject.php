<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * IDO项目模型
 */
class IdoProject extends Model
{
    protected $name = 'gvd_ido_projects';
    
    protected $type = [
        'start_time' => 'timestamp',
        'end_time' => 'timestamp',
        'target_amount' => 'float',
        'raised_amount' => 'float',
        'token_price' => 'float',
        'min_purchase' => 'float',
        'max_purchase' => 'float',
        'is_active' => 'boolean'
    ];

    // 项目状态
    const STATUS_UPCOMING = 1;     // 即将开始
    const STATUS_ACTIVE = 2;       // 进行中
    const STATUS_COMPLETED = 3;    // 已完成
    const STATUS_CANCELLED = 4;    // 已取消

    /**
     * 获取项目状态文本
     */
    public function getStatusText(): string
    {
        $statusMap = [
            self::STATUS_UPCOMING => '即将开始',
            self::STATUS_ACTIVE => '进行中',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_CANCELLED => '已取消'
        ];

        return $statusMap[$this->status] ?? '未知';
    }

    /**
     * 获取项目进度百分比
     */
    public function getProgress(): float
    {
        if ($this->target_amount <= 0) {
            return 0;
        }

        return min(100, ($this->raised_amount / $this->target_amount) * 100);
    }

    /**
     * 检查项目是否可以认购
     */
    public function canPurchase(): bool
    {
        $now = time();
        return $this->status == self::STATUS_ACTIVE 
               && $this->start_time <= $now 
               && $this->end_time > $now
               && $this->raised_amount < $this->target_amount;
    }

    /**
     * 获取剩余时间（秒）
     */
    public function getRemainingTime(): int
    {
        $now = time();
        
        if ($this->status == self::STATUS_UPCOMING) {
            return max(0, $this->start_time - $now);
        } elseif ($this->status == self::STATUS_ACTIVE) {
            return max(0, $this->end_time - $now);
        }
        
        return 0;
    }

    /**
     * 获取项目详细信息
     */
    public function getProjectInfo(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'symbol' => $this->symbol,
            'description' => $this->description,
            'icon' => $this->icon,
            'website' => $this->website,
            'whitepaper' => $this->whitepaper,
            'target_amount' => $this->target_amount,
            'raised_amount' => $this->raised_amount,
            'token_price' => $this->token_price,
            'min_purchase' => $this->min_purchase,
            'max_purchase' => $this->max_purchase,
            'start_time' => $this->start_time,
            'end_time' => $this->end_time,
            'status' => $this->status,
            'status_text' => $this->getStatusText(),
            'progress' => $this->getProgress(),
            'can_purchase' => $this->canPurchase(),
            'remaining_time' => $this->getRemainingTime(),
            'created_at' => $this->created_at
        ];
    }

    /**
     * 获取项目列表
     */
    public static function getProjectList(array $params = []): array
    {
        try {
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 20;
            $status = $params['status'] ?? '';

            $query = self::query();

            if (!empty($status)) {
                $query->where('status', $status);
            }

            $projects = $query->order('created_at', 'desc')
                             ->paginate([
                                 'list_rows' => $limit,
                                 'page' => $page
                             ]);

            $list = [];
            foreach ($projects->items() as $project) {
                $list[] = $project->getProjectInfo();
            }

            return [
                'code' => 1,
                'data' => [
                    'list' => $list,
                    'total' => $projects->total(),
                    'page' => $page,
                    'limit' => $limit
                ]
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '获取失败：' . $e->getMessage()];
        }
    }

    /**
     * 创建IDO项目
     */
    public static function createProject(array $data): array
    {
        try {
            $projectData = [
                'name' => $data['name'],
                'symbol' => $data['symbol'],
                'description' => $data['description'] ?? '',
                'icon' => $data['icon'] ?? '',
                'website' => $data['website'] ?? '',
                'whitepaper' => $data['whitepaper'] ?? '',
                'target_amount' => $data['target_amount'],
                'token_price' => $data['token_price'],
                'min_purchase' => $data['min_purchase'] ?? 100,
                'max_purchase' => $data['max_purchase'] ?? 10000,
                'start_time' => strtotime($data['start_time']),
                'end_time' => strtotime($data['end_time']),
                'status' => self::STATUS_UPCOMING,
                'raised_amount' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $project = self::create($projectData);

            return [
                'code' => 1,
                'msg' => '项目创建成功',
                'data' => $project->getProjectInfo()
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '创建失败：' . $e->getMessage()];
        }
    }

    /**
     * 更新项目状态
     */
    public function updateStatus(): bool
    {
        $now = time();
        $oldStatus = $this->status;

        if ($this->status == self::STATUS_UPCOMING && $now >= $this->start_time) {
            $this->status = self::STATUS_ACTIVE;
        } elseif ($this->status == self::STATUS_ACTIVE && $now >= $this->end_time) {
            $this->status = self::STATUS_COMPLETED;
        } elseif ($this->status == self::STATUS_ACTIVE && $this->raised_amount >= $this->target_amount) {
            $this->status = self::STATUS_COMPLETED;
        }

        if ($this->status != $oldStatus) {
            $this->updated_at = date('Y-m-d H:i:s');
            return $this->save();
        }

        return true;
    }

    /**
     * 增加认购金额
     */
    public function addRaisedAmount(float $amount): bool
    {
        try {
            $this->raised_amount += $amount;
            $this->updated_at = date('Y-m-d H:i:s');
            
            // 检查是否达到目标金额
            if ($this->raised_amount >= $this->target_amount) {
                $this->status = self::STATUS_COMPLETED;
            }
            
            return $this->save();
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取热门项目
     */
    public static function getHotProjects(int $limit = 6): array
    {
        try {
            $projects = self::where('status', 'in', [self::STATUS_UPCOMING, self::STATUS_ACTIVE])
                           ->order('raised_amount', 'desc')
                           ->limit($limit)
                           ->select();

            $list = [];
            foreach ($projects as $project) {
                $list[] = $project->getProjectInfo();
            }

            return [
                'code' => 1,
                'data' => $list
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '获取失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取项目统计
     */
    public static function getProjectStats(): array
    {
        try {
            $totalProjects = self::count();
            $activeProjects = self::where('status', self::STATUS_ACTIVE)->count();
            $completedProjects = self::where('status', self::STATUS_COMPLETED)->count();
            $totalRaised = self::sum('raised_amount');

            return [
                'code' => 1,
                'data' => [
                    'total_projects' => $totalProjects,
                    'active_projects' => $activeProjects,
                    'completed_projects' => $completedProjects,
                    'total_raised' => $totalRaised
                ]
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '获取失败：' . $e->getMessage()];
        }
    }

    /**
     * 关联认购订单
     */
    public function orders()
    {
        return $this->hasMany(IdoOrder::class, 'project_id');
    }

    /**
     * 获取项目认购用户数
     */
    public function getParticipantCount(): int
    {
        return IdoOrder::where('project_id', $this->id)
                      ->where('status', IdoOrder::STATUS_SUCCESS)
                      ->group('user_id')
                      ->count();
    }

    /**
     * 检查用户是否已认购
     */
    public function hasUserPurchased(int $userId): bool
    {
        return IdoOrder::where('project_id', $this->id)
                      ->where('user_id', $userId)
                      ->where('status', IdoOrder::STATUS_SUCCESS)
                      ->count() > 0;
    }

    /**
     * 获取用户认购金额
     */
    public function getUserPurchaseAmount(int $userId): float
    {
        return IdoOrder::where('project_id', $this->id)
                      ->where('user_id', $userId)
                      ->where('status', IdoOrder::STATUS_SUCCESS)
                      ->sum('amount');
    }
}
