# GVD交易平台部署指南

## 🚀 Linux服务器部署步骤

### 环境要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **PHP**: 8.1 或更高版本
- **MySQL**: 8.0 或更高版本
- **Nginx**: 1.18 或更高版本
- **Redis**: 6.0 或更高版本
- **Composer**: 2.0 或更高版本

### 第一步：上传项目文件
```bash
# 1. 将CMCC_COMPLETE目录上传到服务器
scp -r CMCC_COMPLETE/ user@your-server:/var/www/

# 2. 设置目录权限
sudo chown -R www-data:www-data /var/www/CMCC_COMPLETE
sudo chmod -R 755 /var/www/CMCC_COMPLETE
sudo chmod -R 777 /var/www/CMCC_COMPLETE/runtime
```

### 第二步：安装PHP依赖
```bash
cd /var/www/CMCC_COMPLETE

# 安装composer依赖
composer install --no-dev --optimize-autoloader

# 如果composer未安装，先安装composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

### 第三步：配置数据库
```bash
# 1. 创建数据库
mysql -u root -p
CREATE DATABASE gvd_trading CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'gvd_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON gvd_trading.* TO 'gvd_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# 2. 导入数据库结构
mysql -u gvd_user -p gvd_trading < database/gvd_trading.sql
```

### 第四步：配置应用
```bash
# 1. 复制配置文件
cp config/database.php.example config/database.php

# 2. 编辑数据库配置
nano config/database.php
```

### 第五步：配置Nginx
```bash
# 1. 创建站点配置
sudo nano /etc/nginx/sites-available/gvd-trading

# 2. 复制nginx.conf内容到配置文件中

# 3. 启用站点
sudo ln -s /etc/nginx/sites-available/gvd-trading /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 第六步：配置SSL证书（推荐）
```bash
# 使用Let's Encrypt免费证书
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 第七步：启动服务
```bash
# 1. 启动队列服务
php think queue:work

# 2. 启动WebSocket服务
php think workerman start -d

# 3. 设置定时任务
crontab -e
# 添加以下行
* * * * * cd /var/www/CMCC_COMPLETE && php think schedule:run >> /dev/null 2>&1
```

## 🔧 配置文件说明

### 数据库配置 (config/database.php)
```php
return [
    'default' => 'mysql',
    'connections' => [
        'mysql' => [
            'type' => 'mysql',
            'hostname' => 'localhost',
            'database' => 'gvd_trading',
            'username' => 'gvd_user',
            'password' => 'your_password',
            'hostport' => '3306',
            'charset' => 'utf8mb4',
            'prefix' => '',
        ],
    ],
];
```

### Redis配置 (config/cache.php)
```php
return [
    'default' => 'redis',
    'stores' => [
        'redis' => [
            'type' => 'redis',
            'host' => '127.0.0.1',
            'port' => 6379,
            'password' => '',
            'select' => 0,
        ],
    ],
];
```

## 🛠️ 故障排除

### 常见问题
1. **权限问题**: 确保runtime目录可写
2. **依赖问题**: 运行composer install
3. **数据库连接**: 检查数据库配置
4. **PHP版本**: 确保PHP 8.1+

### 日志位置
- 应用日志: `/var/www/CMCC_COMPLETE/runtime/log/`
- Nginx日志: `/var/log/nginx/`
- PHP日志: `/var/log/php/`

## 📊 性能优化

### 1. PHP优化
```bash
# 安装OPcache
sudo apt install php8.1-opcache

# 配置php.ini
opcache.enable=1
opcache.memory_consumption=256
opcache.max_accelerated_files=20000
```

### 2. MySQL优化
```sql
-- 优化MySQL配置
SET GLOBAL innodb_buffer_pool_size = 1G;
SET GLOBAL query_cache_size = 256M;
```

### 3. Redis优化
```bash
# 配置Redis持久化
echo "save 900 1" >> /etc/redis/redis.conf
echo "save 300 10" >> /etc/redis/redis.conf
```

## 🔒 安全配置

### 1. 防火墙设置
```bash
sudo ufw allow 22
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 2. 隐藏敏感信息
```bash
# 隐藏PHP版本
echo "expose_php = Off" >> /etc/php/8.1/fpm/php.ini

# 隐藏Nginx版本
echo "server_tokens off;" >> /etc/nginx/nginx.conf
```

## 📈 监控和维护

### 1. 系统监控
```bash
# 安装监控工具
sudo apt install htop iotop nethogs

# 监控日志
tail -f /var/www/CMCC_COMPLETE/runtime/log/error.log
```

### 2. 备份策略
```bash
# 数据库备份脚本
#!/bin/bash
mysqldump -u gvd_user -p gvd_trading > /backup/gvd_trading_$(date +%Y%m%d).sql

# 文件备份
tar -czf /backup/gvd_files_$(date +%Y%m%d).tar.gz /var/www/CMCC_COMPLETE
```

## ✅ 部署检查清单

- [ ] 服务器环境配置完成
- [ ] 项目文件上传完成
- [ ] Composer依赖安装完成
- [ ] 数据库创建和导入完成
- [ ] 应用配置文件设置完成
- [ ] Nginx配置完成
- [ ] SSL证书配置完成
- [ ] 服务启动完成
- [ ] 权限设置正确
- [ ] 防火墙配置完成
- [ ] 监控和备份设置完成

部署完成后，访问 https://your-domain.com 即可使用系统。
