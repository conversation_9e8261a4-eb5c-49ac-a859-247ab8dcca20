<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\Coin;
use app\common\model\User;
use app\common\model\UserAsset;
use app\common\model\DepositRecord;
use app\common\model\WithdrawRecord;
use app\common\model\FinancialRecord;
use app\common\model\DepositAddress;
use think\facade\Db;
use think\facade\Log;

/**
 * 钱包服务类
 */
class WalletService
{
    /**
     * 获取充值地址（原始逻辑：管理端配置的收款地址）
     */
    public function getDepositAddress(int $userId, string $coinSymbol): string
    {
        $coin = Coin::getBySymbol($coinSymbol);
        if (!$coin || !$coin->is_deposit) {
            return '';
        }

        // 从系统配置获取收款地址（管理端配置）
        $depositAddress = $this->getSystemDepositAddress($coinSymbol);

        if (empty($depositAddress)) {
            // 如果没有配置系统地址，返回空
            return '';
        }

        return $depositAddress;
    }

    /**
     * 获取系统配置的收款地址
     */
    private function getSystemDepositAddress(string $coinSymbol): string
    {
        // 从系统配置表获取管理端配置的收款地址
        $config = Db::name('system_config')
                   ->where('type', 'deposit_address')
                   ->where('key', $coinSymbol)
                   ->find();

        return $config['value'] ?? '';
    }

    /**
     * 绑定用户钱包地址
     */
    public function bindUserWalletAddress(int $userId, string $coinSymbol, string $address): array
    {
        try {
            // 验证地址格式
            if (!$this->validateAddress($coinSymbol, $address)) {
                return ['code' => 0, 'msg' => '钱包地址格式不正确'];
            }

            // 检查地址是否已被其他用户绑定
            $exists = Db::name('user_wallet_addresses')
                       ->where('coin_symbol', $coinSymbol)
                       ->where('address', $address)
                       ->where('user_id', '<>', $userId)
                       ->find();

            if ($exists) {
                return ['code' => 0, 'msg' => '该地址已被其他用户绑定'];
            }

            // 检查用户是否已绑定该币种地址
            $userAddress = Db::name('user_wallet_addresses')
                            ->where('user_id', $userId)
                            ->where('coin_symbol', $coinSymbol)
                            ->find();

            if ($userAddress) {
                // 更新地址
                Db::name('user_wallet_addresses')
                  ->where('id', $userAddress['id'])
                  ->update([
                      'address' => $address,
                      'updated_at' => date('Y-m-d H:i:s')
                  ]);
            } else {
                // 新增地址
                Db::name('user_wallet_addresses')->insert([
                    'user_id' => $userId,
                    'coin_symbol' => $coinSymbol,
                    'address' => $address,
                    'status' => 1,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }

            return ['code' => 1, 'msg' => '钱包地址绑定成功'];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '绑定失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取用户绑定的钱包地址
     */
    public function getUserWalletAddress(int $userId, string $coinSymbol): string
    {
        $address = Db::name('user_wallet_addresses')
                    ->where('user_id', $userId)
                    ->where('coin_symbol', $coinSymbol)
                    ->where('status', 1)
                    ->value('address');

        return $address ?: '';
    }

    /**
     * 生成独立充值地址（每个用户独立地址）
     */
    private function generateUniqueDepositAddress(string $coinSymbol, int $userId): string
    {
        // 为每个用户生成独立的充值地址
        // 这里应该调用区块链API生成真实地址
        switch ($coinSymbol) {
            case 'BTC':
                // 生成BTC地址
                return '1' . substr(hash('sha256', $userId . $coinSymbol . time() . mt_rand()), 0, 33);
            case 'ETH':
            case 'USDT':
                // 生成以太坊地址
                return '0x' . substr(hash('sha256', $userId . $coinSymbol . time() . mt_rand()), 0, 40);
            case 'TRX':
                // 生成TRON地址
                return 'T' . substr(hash('sha256', $userId . $coinSymbol . time() . mt_rand()), 0, 33);
            default:
                return substr(hash('sha256', $userId . $coinSymbol . time() . mt_rand()), 0, 34);
        }
    }

    /**
     * 保存充值地址记录
     */
    private function saveDepositAddress(int $userId, string $coinSymbol, string $address): void
    {
        try {
            // 创建地址记录
            DepositAddress::create([
                'user_id' => $userId,
                'coin_symbol' => $coinSymbol,
                'address' => $address,
                'status' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            Log::error('保存充值地址失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建充值记录
     */
    public function createDepositRecord(int $userId, array $data): array
    {
        // 获取币种信息
        $coin = Coin::getBySymbol($data['coin_symbol']);
        if (!$coin || !$coin->is_deposit) {
            return ['code' => 0, 'msg' => '该币种不支持充值'];
        }

        // 验证最小充值金额
        if ($data['amount'] < $coin->min_deposit) {
            return ['code' => 0, 'msg' => "最小充值金额为 {$coin->min_deposit} {$coin->symbol}"];
        }

        // 检查交易ID是否已存在
        $existingRecord = DepositRecord::where('txid', $data['txid'])->find();
        if ($existingRecord) {
            return ['code' => 0, 'msg' => '该交易ID已存在'];
        }

        // 开启事务
        Db::startTrans();
        try {
            // 创建充值记录
            $record = new DepositRecord();
            $record->user_id = $userId;
            $record->coin_symbol = $data['coin_symbol'];
            $record->amount = $data['amount'];
            $record->address = $data['address'] ?? $this->getDepositAddress($userId, $data['coin_symbol']);
            $record->txid = $data['txid'];
            $record->confirmations = 0;
            $record->required_confirmations = $this->getRequiredConfirmations($data['coin_symbol']);
            $record->status = 0; // 待确认
            $record->save();

            // 如果开启自动确认，直接到账
            if (config('finance.auto_confirm_deposit', false)) {
                $this->confirmDeposit($record->id);
            }

            Db::commit();

            return ['code' => 1, 'msg' => '充值申请提交成功，请等待区块确认'];
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 0, 'msg' => '充值申请失败：' . $e->getMessage()];
        }
    }

    /**
     * 确认充值
     */
    public function confirmDeposit(int $recordId): array
    {
        $record = DepositRecord::find($recordId);
        if (!$record) {
            return ['code' => 0, 'msg' => '充值记录不存在'];
        }

        if ($record->status != 0) {
            return ['code' => 0, 'msg' => '充值记录状态不允许确认'];
        }

        // 开启事务
        Db::startTrans();
        try {
            // 更新充值记录状态
            $record->status = 1; // 已确认
            $record->confirmed_at = date('Y-m-d H:i:s');
            $record->save();

            // 增加用户资产
            $asset = UserAsset::getOrCreate($record->user_id, $record->coin_symbol);
            $asset->addAvailable($record->amount);

            // 记录财务流水
            FinancialRecord::create([
                'user_id' => $record->user_id,
                'coin_symbol' => $record->coin_symbol,
                'type' => 'deposit',
                'amount' => $record->amount,
                'balance_before' => $asset->available - $record->amount,
                'balance_after' => $asset->available,
                'related_id' => $record->id,
                'description' => "充值 {$record->coin_symbol}"
            ]);

            Db::commit();

            return ['code' => 1, 'msg' => '充值确认成功'];
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 0, 'msg' => '充值确认失败：' . $e->getMessage()];
        }
    }

    /**
     * 创建提现记录
     */
    public function createWithdrawRecord(int $userId, array $data): array
    {
        // 获取币种信息
        $coin = Coin::getBySymbol($data['coin_symbol']);
        if (!$coin || !$coin->is_withdraw) {
            return ['code' => 0, 'msg' => '该币种不支持提现'];
        }

        // 验证最小提现金额
        if ($data['amount'] < $coin->min_withdraw) {
            return ['code' => 0, 'msg' => "最小提现金额为 {$coin->min_withdraw} {$coin->symbol}"];
        }

        // 验证提现地址
        if (!$this->validateAddress($data['coin_symbol'], $data['address'])) {
            return ['code' => 0, 'msg' => '提现地址格式不正确'];
        }

        // 计算手续费
        $fee = $this->calculateWithdrawFee($data['coin_symbol'], $data['amount']);
        $totalAmount = $data['amount'] + $fee;

        // 检查用户余额
        $asset = UserAsset::getOrCreate($userId, $data['coin_symbol']);
        if ($asset->available < $totalAmount) {
            return ['code' => 0, 'msg' => '余额不足'];
        }

        // 开启事务
        Db::startTrans();
        try {
            // 冻结用户资产
            $asset->freeze($totalAmount);

            // 创建提现记录
            $record = new WithdrawRecord();
            $record->user_id = $userId;
            $record->coin_symbol = $data['coin_symbol'];
            $record->amount = $data['amount'];
            $record->fee = $fee;
            $record->actual_amount = $data['amount'];
            $record->address = $data['address'];
            $record->memo = $data['memo'] ?? '';
            $record->txid = '';
            $record->status = 0; // 待审核
            $record->save();

            // 记录财务流水
            FinancialRecord::create([
                'user_id' => $userId,
                'coin_symbol' => $data['coin_symbol'],
                'type' => 'withdraw',
                'amount' => -$data['amount'],
                'balance_before' => $asset->available + $totalAmount,
                'balance_after' => $asset->available,
                'related_id' => $record->id,
                'description' => "提现 {$data['coin_symbol']}"
            ]);

            // 记录手续费流水
            if ($fee > 0) {
                FinancialRecord::create([
                    'user_id' => $userId,
                    'coin_symbol' => $data['coin_symbol'],
                    'type' => 'withdraw',
                    'amount' => -$fee,
                    'balance_before' => $asset->available + $fee,
                    'balance_after' => $asset->available,
                    'related_id' => $record->id,
                    'description' => "提现手续费 {$data['coin_symbol']}"
                ]);
            }

            Db::commit();

            return ['code' => 1, 'msg' => '提现申请提交成功，请等待审核'];
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 0, 'msg' => '提现申请失败：' . $e->getMessage()];
        }
    }

    /**
     * 确认提现
     */
    public function confirmWithdraw(int $recordId, string $txid = ''): array
    {
        $record = WithdrawRecord::find($recordId);
        if (!$record) {
            return ['code' => 0, 'msg' => '提现记录不存在'];
        }

        if ($record->status != 1) {
            return ['code' => 0, 'msg' => '提现记录状态不允许确认'];
        }

        // 开启事务
        Db::startTrans();
        try {
            // 更新提现记录
            $record->status = 2; // 已完成
            $record->txid = $txid;
            $record->processed_at = date('Y-m-d H:i:s');
            $record->save();

            // 扣除冻结资产
            $asset = UserAsset::getOrCreate($record->user_id, $record->coin_symbol);
            $totalAmount = $record->amount + $record->fee;
            $asset->subFrozen($totalAmount);

            Db::commit();

            return ['code' => 1, 'msg' => '提现确认成功'];
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 0, 'msg' => '提现确认失败：' . $e->getMessage()];
        }
    }

    /**
     * 拒绝提现
     */
    public function rejectWithdraw(int $recordId, string $reason = ''): array
    {
        $record = WithdrawRecord::find($recordId);
        if (!$record) {
            return ['code' => 0, 'msg' => '提现记录不存在'];
        }

        if ($record->status != 0 && $record->status != 1) {
            return ['code' => 0, 'msg' => '提现记录状态不允许拒绝'];
        }

        // 开启事务
        Db::startTrans();
        try {
            // 更新提现记录
            $record->status = 3; // 已拒绝
            $record->reject_reason = $reason;
            $record->processed_at = date('Y-m-d H:i:s');
            $record->save();

            // 解冻用户资产
            $asset = UserAsset::getOrCreate($record->user_id, $record->coin_symbol);
            $totalAmount = $record->amount + $record->fee;
            $asset->unfreeze($totalAmount);

            Db::commit();

            return ['code' => 1, 'msg' => '提现已拒绝'];
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 0, 'msg' => '操作失败：' . $e->getMessage()];
        }
    }

    /**
     * 验证地址格式
     */
    public function validateAddress(string $coinSymbol, string $address): bool
    {
        switch ($coinSymbol) {
            case 'BTC':
                // 比特币地址验证
                return preg_match('/^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/', $address) ||
                       preg_match('/^bc1[a-z0-9]{39,59}$/', $address);
                       
            case 'ETH':
            case 'USDT':
                // 以太坊地址验证
                return preg_match('/^0x[a-fA-F0-9]{40}$/', $address);
                
            default:
                // 通用验证
                return strlen($address) >= 20 && strlen($address) <= 50;
        }
    }

    /**
     * 计算提现手续费
     */
    public function calculateWithdrawFee(string $coinSymbol, float $amount): float
    {
        $coin = Coin::getBySymbol($coinSymbol);
        if (!$coin) {
            return 0;
        }

        if ($coin->withdraw_fee_type == 1) {
            // 固定手续费
            return $coin->withdraw_fee;
        } else {
            // 比例手续费
            return $amount * $coin->withdraw_fee;
        }
    }

    /**
     * 获取所需确认数
     */
    private function getRequiredConfirmations(string $coinSymbol): int
    {
        $confirmations = [
            'BTC' => 3,
            'ETH' => 12,
            'USDT' => 12,
            'BNB' => 1
        ];

        return $confirmations[$coinSymbol] ?? 6;
    }

    /**
     * 获取充值记录
     */
    public function getDepositHistory(int $userId, array $filters = []): array
    {
        try {
            $query = DepositRecord::where('user_id', $userId);

            // 筛选条件
            if (!empty($filters['coin_symbol'])) {
                $query->where('coin_symbol', $filters['coin_symbol']);
            }

            if (isset($filters['status']) && $filters['status'] !== '') {
                $query->where('status', $filters['status']);
            }

            if (!empty($filters['start_date'])) {
                $query->where('created_at', '>=', $filters['start_date']);
            }

            if (!empty($filters['end_date'])) {
                $query->where('created_at', '<=', $filters['end_date'] . ' 23:59:59');
            }

            // 分页
            $page = $filters['page'] ?? 1;
            $limit = $filters['limit'] ?? 20;
            $offset = ($page - 1) * $limit;

            $total = $query->count();
            $records = $query->order('created_at', 'desc')
                           ->limit($offset, $limit)
                           ->select();

            return [
                'code' => 1,
                'data' => [
                    'list' => $records->toArray(),
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取充值记录失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取充值记录失败'];
        }
    }

    /**
     * 获取提币记录
     */
    public function getWithdrawHistory(int $userId, array $filters = []): array
    {
        try {
            $query = WithdrawRecord::where('user_id', $userId);

            // 筛选条件
            if (!empty($filters['coin_symbol'])) {
                $query->where('coin_symbol', $filters['coin_symbol']);
            }

            if (isset($filters['status']) && $filters['status'] !== '') {
                $query->where('status', $filters['status']);
            }

            if (!empty($filters['start_date'])) {
                $query->where('created_at', '>=', $filters['start_date']);
            }

            if (!empty($filters['end_date'])) {
                $query->where('created_at', '<=', $filters['end_date'] . ' 23:59:59');
            }

            // 分页
            $page = $filters['page'] ?? 1;
            $limit = $filters['limit'] ?? 20;
            $offset = ($page - 1) * $limit;

            $total = $query->count();
            $records = $query->order('created_at', 'desc')
                           ->limit($offset, $limit)
                           ->select();

            return [
                'code' => 1,
                'data' => [
                    'list' => $records->toArray(),
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取提币记录失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取提币记录失败'];
        }
    }

    /**
     * 自动充值到账处理
     */
    public function processAutoDeposit(array $transactionData): array
    {
        try {
            // 验证交易数据
            if (empty($transactionData['txid']) || empty($transactionData['address']) || empty($transactionData['amount'])) {
                return ['code' => 0, 'msg' => '交易数据不完整'];
            }

            // 检查交易是否已处理
            $existingRecord = DepositRecord::where('txid', $transactionData['txid'])->find();
            if ($existingRecord) {
                return ['code' => 0, 'msg' => '交易已处理'];
            }

            // 根据地址查找用户
            $depositAddress = DepositAddress::where('address', $transactionData['address'])->find();
            if (!$depositAddress) {
                return ['code' => 0, 'msg' => '充值地址不存在'];
            }

            $userId = $depositAddress->user_id;
            $coinSymbol = $depositAddress->coin_symbol;

            // 验证币种
            $coin = Coin::getBySymbol($coinSymbol);
            if (!$coin || !$coin->is_deposit) {
                return ['code' => 0, 'msg' => '币种不支持充值'];
            }

            // 验证最小充值金额
            if ($transactionData['amount'] < $coin->min_deposit) {
                return ['code' => 0, 'msg' => '充值金额低于最小限制'];
            }

            // 开始事务
            Db::startTrans();
            try {
                // 创建充值记录
                $depositRecord = DepositRecord::create([
                    'user_id' => $userId,
                    'coin_symbol' => $coinSymbol,
                    'amount' => $transactionData['amount'],
                    'address' => $transactionData['address'],
                    'txid' => $transactionData['txid'],
                    'confirmations' => $transactionData['confirmations'] ?? 0,
                    'status' => 1, // 自动确认
                    'created_at' => date('Y-m-d H:i:s'),
                    'confirmed_at' => date('Y-m-d H:i:s')
                ]);

                // 更新用户资产
                $userAsset = UserAsset::where('user_id', $userId)
                                     ->where('coin_symbol', $coinSymbol)
                                     ->find();

                if (!$userAsset) {
                    // 创建新的资产记录
                    $userAsset = UserAsset::create([
                        'user_id' => $userId,
                        'coin_symbol' => $coinSymbol,
                        'available' => $transactionData['amount'],
                        'frozen' => 0,
                        'total' => $transactionData['amount'],
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                } else {
                    // 更新现有资产
                    $userAsset->available += $transactionData['amount'];
                    $userAsset->total += $transactionData['amount'];
                    $userAsset->updated_at = date('Y-m-d H:i:s');
                    $userAsset->save();
                }

                // 记录财务流水
                FinancialRecord::create([
                    'user_id' => $userId,
                    'coin_symbol' => $coinSymbol,
                    'type' => 'deposit',
                    'amount' => $transactionData['amount'],
                    'balance_before' => $userAsset->available - $transactionData['amount'],
                    'balance_after' => $userAsset->available,
                    'related_id' => $depositRecord->id,
                    'description' => '充值到账',
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                Db::commit();

                // 发送到账通知
                $this->sendDepositNotification($userId, $transactionData['amount'], $coinSymbol);

                Log::info("自动充值到账成功", [
                    'user_id' => $userId,
                    'coin_symbol' => $coinSymbol,
                    'amount' => $transactionData['amount'],
                    'txid' => $transactionData['txid']
                ]);

                return [
                    'code' => 1,
                    'msg' => '充值到账成功',
                    'data' => [
                        'user_id' => $userId,
                        'amount' => $transactionData['amount'],
                        'coin_symbol' => $coinSymbol
                    ]
                ];

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('自动充值处理失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '充值处理失败'];
        }
    }

    /**
     * 发送充值到账通知
     */
    private function sendDepositNotification(int $userId, float $amount, string $coinSymbol): void
    {
        try {
            $user = User::find($userId);
            if ($user && !empty($user->email)) {
                $emailService = new EmailService();
                $emailService->sendDepositNotification($user->email, $amount, $coinSymbol);
            }

            // 可以添加站内信通知
            $notificationService = new NotificationService();
            $notificationService->sendNotification($userId, 'deposit', [
                'amount' => $amount,
                'coin_symbol' => $coinSymbol,
                'message' => "您的 {$amount} {$coinSymbol} 充值已到账"
            ]);

        } catch (\Exception $e) {
            Log::error('发送充值通知失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取用户资产
     */
    public function getUserAssets(int $userId, string $coinSymbol = ''): array
    {
        try {
            $query = UserAsset::where('user_id', $userId);

            if ($coinSymbol) {
                $query->where('coin_symbol', $coinSymbol);
            }

            $assets = $query->select();

            return [
                'code' => 1,
                'data' => $assets->toArray()
            ];

        } catch (\Exception $e) {
            Log::error('获取用户资产失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取用户资产失败'];
        }
    }

    /**
     * 获取支持的币种
     */
    public function getSupportedCoins(string $type = 'all'): array
    {
        try {
            $query = Coin::where('status', 1);

            switch ($type) {
                case 'deposit':
                    $query->where('is_deposit', 1);
                    break;
                case 'withdraw':
                    $query->where('is_withdraw', 1);
                    break;
            }

            $coins = $query->select();

            return $coins->toArray();

        } catch (\Exception $e) {
            Log::error('获取支持币种失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取币种信息
     */
    public function getCoinInfo(string $coinSymbol): array
    {
        try {
            $coin = Coin::getBySymbol($coinSymbol);

            if (!$coin) {
                return ['code' => 0, 'msg' => '币种不存在'];
            }

            return [
                'code' => 1,
                'data' => $coin->toArray()
            ];

        } catch (\Exception $e) {
            Log::error('获取币种信息失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取币种信息失败'];
        }
    }

    /**
     * 获取资产统计
     */
    public function getAssetStats(int $userId): array
    {
        try {
            $assets = UserAsset::where('user_id', $userId)->select();

            $stats = [
                'total_assets' => 0,
                'available_assets' => 0,
                'frozen_assets' => 0,
                'coin_count' => count($assets)
            ];

            foreach ($assets as $asset) {
                $stats['total_assets'] += $asset->total;
                $stats['available_assets'] += $asset->available;
                $stats['frozen_assets'] += $asset->frozen;
            }

            return $stats;

        } catch (\Exception $e) {
            Log::error('获取资产统计失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取财务流水
     */
    public function getFinancialRecords(int $userId, array $filters = []): array
    {
        try {
            $query = FinancialRecord::where('user_id', $userId);

            // 筛选条件
            if (!empty($filters['coin_symbol'])) {
                $query->where('coin_symbol', $filters['coin_symbol']);
            }

            if (!empty($filters['type'])) {
                $query->where('type', $filters['type']);
            }

            if (!empty($filters['start_date'])) {
                $query->where('created_at', '>=', $filters['start_date']);
            }

            if (!empty($filters['end_date'])) {
                $query->where('created_at', '<=', $filters['end_date'] . ' 23:59:59');
            }

            // 分页
            $page = $filters['page'] ?? 1;
            $limit = $filters['limit'] ?? 20;
            $offset = ($page - 1) * $limit;

            $total = $query->count();
            $records = $query->order('created_at', 'desc')
                           ->limit($offset, $limit)
                           ->select();

            return [
                'code' => 1,
                'data' => [
                    'list' => $records->toArray(),
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取财务流水失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取财务流水失败'];
        }
    }

    /**
     * 内部转账
     */
    public function internalTransfer(int $fromUserId, array $data): array
    {
        try {
            // 验证参数
            if (empty($data['to_username']) || empty($data['coin_symbol']) ||
                empty($data['amount']) || empty($data['password'])) {
                return ['code' => 0, 'msg' => '参数不完整'];
            }

            // 查找接收用户
            $toUser = User::where('username', $data['to_username'])->find();
            if (!$toUser) {
                return ['code' => 0, 'msg' => '接收用户不存在'];
            }

            if ($toUser->id == $fromUserId) {
                return ['code' => 0, 'msg' => '不能转账给自己'];
            }

            // 验证密码
            $fromUser = User::find($fromUserId);
            if (!$fromUser || !$this->verifyPassword($data['password'], $fromUser->password, $fromUser->salt)) {
                return ['code' => 0, 'msg' => '密码错误'];
            }

            // 验证余额
            $fromAsset = UserAsset::where('user_id', $fromUserId)
                                 ->where('coin_symbol', $data['coin_symbol'])
                                 ->find();

            if (!$fromAsset || $fromAsset->available < $data['amount']) {
                return ['code' => 0, 'msg' => '余额不足'];
            }

            // 开始事务
            Db::startTrans();
            try {
                // 扣除发送方余额
                $fromAsset->available -= $data['amount'];
                $fromAsset->total -= $data['amount'];
                $fromAsset->save();

                // 增加接收方余额
                $toAsset = UserAsset::where('user_id', $toUser->id)
                                   ->where('coin_symbol', $data['coin_symbol'])
                                   ->find();

                if (!$toAsset) {
                    $toAsset = UserAsset::create([
                        'user_id' => $toUser->id,
                        'coin_symbol' => $data['coin_symbol'],
                        'available' => $data['amount'],
                        'frozen' => 0,
                        'total' => $data['amount'],
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                } else {
                    $toAsset->available += $data['amount'];
                    $toAsset->total += $data['amount'];
                    $toAsset->save();
                }

                // 记录财务流水
                FinancialRecord::create([
                    'user_id' => $fromUserId,
                    'coin_symbol' => $data['coin_symbol'],
                    'type' => 'transfer_out',
                    'amount' => -$data['amount'],
                    'balance_before' => $fromAsset->available + $data['amount'],
                    'balance_after' => $fromAsset->available,
                    'description' => "转账给 {$data['to_username']}",
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                FinancialRecord::create([
                    'user_id' => $toUser->id,
                    'coin_symbol' => $data['coin_symbol'],
                    'type' => 'transfer_in',
                    'amount' => $data['amount'],
                    'balance_before' => $toAsset->available - $data['amount'],
                    'balance_after' => $toAsset->available,
                    'description' => "来自 {$fromUser->username} 的转账",
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                Db::commit();

                return [
                    'code' => 1,
                    'msg' => '转账成功',
                    'data' => [
                        'from_user' => $fromUser->username,
                        'to_user' => $toUser->username,
                        'amount' => $data['amount'],
                        'coin_symbol' => $data['coin_symbol']
                    ]
                ];

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('内部转账失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '转账失败'];
        }
    }

    /**
     * 取消提币申请
     */
    public function cancelWithdraw(int $userId, int $withdrawId): array
    {
        try {
            $withdraw = WithdrawRecord::where('id', $withdrawId)
                                     ->where('user_id', $userId)
                                     ->find();

            if (!$withdraw) {
                return ['code' => 0, 'msg' => '提币记录不存在'];
            }

            if ($withdraw->status != 0) {
                return ['code' => 0, 'msg' => '该提币申请无法取消'];
            }

            // 开始事务
            Db::startTrans();
            try {
                // 更新提币状态
                $withdraw->status = -1; // 已取消
                $withdraw->updated_at = date('Y-m-d H:i:s');
                $withdraw->save();

                // 解冻资产
                $userAsset = UserAsset::where('user_id', $userId)
                                     ->where('coin_symbol', $withdraw->coin_symbol)
                                     ->find();

                if ($userAsset) {
                    $userAsset->frozen -= $withdraw->amount;
                    $userAsset->available += $withdraw->amount;
                    $userAsset->save();
                }

                // 记录财务流水
                FinancialRecord::create([
                    'user_id' => $userId,
                    'coin_symbol' => $withdraw->coin_symbol,
                    'type' => 'withdraw_cancel',
                    'amount' => $withdraw->amount,
                    'balance_before' => $userAsset->available - $withdraw->amount,
                    'balance_after' => $userAsset->available,
                    'related_id' => $withdrawId,
                    'description' => '取消提币申请',
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                Db::commit();

                return [
                    'code' => 1,
                    'msg' => '提币申请已取消'
                ];

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('取消提币申请失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '取消提币申请失败'];
        }
    }

    /**
     * 验证密码
     */
    private function verifyPassword(string $password, string $hash, string $salt): bool
    {
        return hash('sha256', $password . $salt) === $hash;
    }
}
