<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;
use think\facade\Cache;

/**
 * 用户模型
 */
class User extends Model
{
    protected $name = 'gvd_users';
    
    protected $type = [
        'is_active' => 'boolean',
        'email_verified' => 'boolean',
        'phone_verified' => 'boolean',
        'kyc_status' => 'integer',
        'last_login_time' => 'timestamp'
    ];

    // 隐藏字段
    protected $hidden = ['password', 'pay_password'];

    // 用户状态
    const STATUS_INACTIVE = 0;  // 未激活
    const STATUS_ACTIVE = 1;    // 正常
    const STATUS_FROZEN = 2;    // 冻结
    const STATUS_BANNED = 3;    // 封禁

    // KYC状态
    const KYC_UNVERIFIED = 0;   // 未认证
    const KYC_PENDING = 1;      // 审核中
    const KYC_VERIFIED = 2;     // 已认证
    const KYC_REJECTED = 3;     // 被拒绝

    /**
     * 密码修改器
     */
    public function setPasswordAttr($value)
    {
        return password_hash($value, PASSWORD_DEFAULT);
    }

    /**
     * 支付密码修改器
     */
    public function setPayPasswordAttr($value)
    {
        return password_hash($value, PASSWORD_DEFAULT);
    }

    /**
     * 验证密码
     */
    public function verifyPassword(string $password): bool
    {
        return password_verify($password, $this->password);
    }

    /**
     * 验证支付密码
     */
    public function verifyPayPassword(string $payPassword): bool
    {
        return password_verify($payPassword, $this->pay_password);
    }

    /**
     * 生成JWT Token
     */
    public function generateToken(): string
    {
        $payload = [
            'user_id' => $this->id,
            'username' => $this->username,
            'email' => $this->email,
            'iat' => time(),
            'exp' => time() + 7200 // 2小时过期
        ];

        return $this->createJWT($payload);
    }

    /**
     * 创建JWT
     */
    private function createJWT(array $payload): string
    {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode($payload);
        
        $headerEncoded = $this->base64UrlEncode($header);
        $payloadEncoded = $this->base64UrlEncode($payload);
        
        $signature = hash_hmac('sha256', $headerEncoded . "." . $payloadEncoded, 'your-secret-key', true);
        $signatureEncoded = $this->base64UrlEncode($signature);
        
        return $headerEncoded . "." . $payloadEncoded . "." . $signatureEncoded;
    }

    /**
     * Base64 URL编码
     */
    private function base64UrlEncode(string $data): string
    {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    /**
     * 验证JWT Token
     */
    public static function verifyToken(string $token): ?array
    {
        $parts = explode('.', $token);
        if (count($parts) !== 3) {
            return null;
        }

        [$headerEncoded, $payloadEncoded, $signatureEncoded] = $parts;
        
        $signature = hash_hmac('sha256', $headerEncoded . "." . $payloadEncoded, 'your-secret-key', true);
        $expectedSignature = self::base64UrlDecode($signatureEncoded);
        
        if (!hash_equals($signature, $expectedSignature)) {
            return null;
        }

        $payload = json_decode(self::base64UrlDecode($payloadEncoded), true);
        
        if ($payload['exp'] < time()) {
            return null; // Token已过期
        }

        return $payload;
    }

    /**
     * Base64 URL解码
     */
    private static function base64UrlDecode(string $data): string
    {
        return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
    }

    /**
     * 根据Token获取用户
     */
    public static function getUserByToken(string $token): ?User
    {
        $payload = self::verifyToken($token);
        if (!$payload) {
            return null;
        }

        return self::find($payload['user_id']);
    }

    /**
     * 用户注册
     */
    public static function register(array $data): array
    {
        try {
            // 验证邮箱是否已存在
            $exists = self::where('email', $data['email'])->find();
            if ($exists) {
                return ['code' => 0, 'msg' => '邮箱已被注册'];
            }

            // 验证用户名是否已存在
            if (isset($data['username'])) {
                $exists = self::where('username', $data['username'])->find();
                if ($exists) {
                    return ['code' => 0, 'msg' => '用户名已被使用'];
                }
            }

            // 创建用户
            $userData = [
                'username' => $data['username'] ?? $data['email'],
                'email' => $data['email'],
                'password' => $data['password'],
                'phone' => $data['phone'] ?? '',
                'invite_code' => $data['invite_code'] ?? '',
                'status' => self::STATUS_ACTIVE,
                'kyc_status' => self::KYC_UNVERIFIED,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $user = self::create($userData);

            // 初始化用户资产
            UserAsset::initUserAssets($user->id);

            return [
                'code' => 1,
                'msg' => '注册成功',
                'data' => [
                    'user_id' => $user->id,
                    'username' => $user->username,
                    'email' => $user->email,
                    'token' => $user->generateToken()
                ]
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '注册失败：' . $e->getMessage()];
        }
    }

    /**
     * 用户登录
     */
    public static function login(string $account, string $password): array
    {
        try {
            // 查找用户（支持邮箱或用户名登录）
            $user = self::where('email', $account)
                       ->whereOr('username', $account)
                       ->find();

            if (!$user) {
                return ['code' => 0, 'msg' => '用户不存在'];
            }

            if (!$user->verifyPassword($password)) {
                return ['code' => 0, 'msg' => '密码错误'];
            }

            if ($user->status != self::STATUS_ACTIVE) {
                return ['code' => 0, 'msg' => '账户已被禁用'];
            }

            // 更新登录信息
            $user->last_login_time = time();
            $user->last_login_ip = request()->ip();
            $user->save();

            return [
                'code' => 1,
                'msg' => '登录成功',
                'data' => [
                    'user_id' => $user->id,
                    'username' => $user->username,
                    'email' => $user->email,
                    'status' => $user->status,
                    'kyc_status' => $user->kyc_status,
                    'token' => $user->generateToken()
                ]
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '登录失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取用户信息
     */
    public function getProfile(): array
    {
        return [
            'user_id' => $this->id,
            'username' => $this->username,
            'email' => $this->email,
            'phone' => $this->phone,
            'avatar' => $this->avatar,
            'status' => $this->status,
            'kyc_status' => $this->kyc_status,
            'email_verified' => $this->email_verified,
            'phone_verified' => $this->phone_verified,
            'created_at' => $this->created_at,
            'last_login_time' => $this->last_login_time
        ];
    }

    /**
     * 更新用户信息
     */
    public function updateProfile(array $data): array
    {
        try {
            $allowedFields = ['username', 'phone', 'avatar', 'real_name'];
            $updateData = [];

            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }

            if (empty($updateData)) {
                return ['code' => 0, 'msg' => '没有需要更新的数据'];
            }

            $updateData['updated_at'] = date('Y-m-d H:i:s');
            $this->save($updateData);

            return [
                'code' => 1,
                'msg' => '更新成功',
                'data' => $this->getProfile()
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '更新失败：' . $e->getMessage()];
        }
    }

    /**
     * 修改密码
     */
    public function changePassword(string $oldPassword, string $newPassword): array
    {
        try {
            if (!$this->verifyPassword($oldPassword)) {
                return ['code' => 0, 'msg' => '原密码错误'];
            }

            $this->password = $newPassword;
            $this->updated_at = date('Y-m-d H:i:s');
            $this->save();

            return ['code' => 1, 'msg' => '密码修改成功'];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '密码修改失败：' . $e->getMessage()];
        }
    }

    /**
     * 设置支付密码
     */
    public function setPayPassword(string $payPassword): array
    {
        try {
            $this->pay_password = $payPassword;
            $this->updated_at = date('Y-m-d H:i:s');
            $this->save();

            return ['code' => 1, 'msg' => '支付密码设置成功'];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '支付密码设置失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取用户统计信息
     */
    public function getStats(): array
    {
        // 获取用户资产总值
        $totalAssets = UserAsset::where('user_id', $this->id)->sum('total');
        
        // 获取交易统计
        $tradeStats = SpotOrder::getOrderStats($this->id);

        return [
            'total_assets' => $totalAssets,
            'total_orders' => $tradeStats['total_orders'],
            'total_volume' => $tradeStats['total_volume'],
            'total_fee' => $tradeStats['total_fee']
        ];
    }

    /**
     * 关联用户资产
     */
    public function assets()
    {
        return $this->hasMany(UserAsset::class, 'user_id');
    }

    /**
     * 关联订单
     */
    public function orders()
    {
        return $this->hasMany(SpotOrder::class, 'user_id');
    }
}
