<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\BaseController;
use app\common\service\KlineService;
use app\common\model\Kline as KlineModel;
use think\facade\Request;
use think\facade\Validate;

/**
 * K线数据API控制器
 */
class Kline extends BaseController
{
    protected $klineService;

    public function initialize()
    {
        parent::initialize();
        $this->klineService = new KlineService();
    }

    /**
     * 获取K线数据
     * GET /api/kline/data
     */
    public function data()
    {
        $symbol = Request::get('symbol', 'BTCUSDT');
        $interval = Request::get('interval', '1h');
        $limit = Request::get('limit/d', 500);
        $startTime = Request::get('startTime/d', 0);
        $endTime = Request::get('endTime/d', 0);

        // 验证参数
        $validate = Validate::rule([
            'symbol' => 'require|alphaNum',
            'interval' => 'require|in:1m,3m,5m,15m,30m,1h,2h,4h,6h,8h,12h,1d,3d,1w,1M',
            'limit' => 'integer|between:1,1000'
        ])->message([
            'symbol.require' => '交易对不能为空',
            'interval.require' => '时间周期不能为空',
            'interval.in' => '不支持的时间周期',
            'limit.between' => '数量限制在1-1000之间'
        ]);

        $data = [
            'symbol' => $symbol,
            'interval' => $interval,
            'limit' => $limit
        ];

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        $klineData = $this->klineService->getKlineData($symbol, $interval, $limit, $startTime, $endTime);

        return $this->success('获取成功', $klineData);
    }

    /**
     * 获取24小时行情统计
     * GET /api/kline/ticker24hr
     */
    public function ticker24hr()
    {
        $symbol = Request::get('symbol', '');

        if (empty($symbol)) {
            // 获取所有交易对行情
            $result = $this->klineService->getAllTickers();
            return json($result);
        } else {
            // 获取指定交易对行情
            $ticker = $this->klineService->get24hTicker($symbol);
            return $this->success('获取成功', $ticker);
        }
    }

    /**
     * 获取实时K线数据
     * GET /api/kline/realtime
     */
    public function realtime()
    {
        $symbol = Request::get('symbol', 'BTCUSDT');
        $interval = Request::get('interval', '1m');

        $realtimeData = $this->klineService->getRealtimeKline($symbol, $interval);

        return $this->success('获取成功', $realtimeData);
    }

    /**
     * 获取深度数据
     * GET /api/kline/depth
     */
    public function depth()
    {
        $symbol = Request::get('symbol', 'BTCUSDT');
        $limit = Request::get('limit/d', 20);

        $depthData = $this->klineService->getDepthData($symbol, $limit);

        return $this->success('获取成功', $depthData);
    }

    /**
     * 获取支持的时间周期
     * GET /api/kline/intervals
     */
    public function intervals()
    {
        $intervals = KlineModel::getSupportedIntervals();

        return $this->success('获取成功', $intervals);
    }

    /**
     * 生成模拟K线数据（测试用）
     * POST /api/kline/simulate
     */
    public function simulate()
    {
        if (!Request::isPost()) {
            return $this->error('请求方式错误');
        }

        $symbol = Request::post('symbol', 'BTCUSDT');
        $interval = Request::post('interval', '1h');
        $count = Request::post('count/d', 100);

        // 验证参数
        $validate = Validate::rule([
            'symbol' => 'require|alphaNum',
            'interval' => 'require|in:1m,3m,5m,15m,30m,1h,2h,4h,6h,8h,12h,1d,3d,1w,1M',
            'count' => 'integer|between:1,1000'
        ]);

        $data = [
            'symbol' => $symbol,
            'interval' => $interval,
            'count' => $count
        ];

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        $result = $this->klineService->generateSimulateData($symbol, $interval, $count);

        return json($result);
    }

    /**
     * 初始化交易对K线数据
     * POST /api/kline/init
     */
    public function init()
    {
        if (!Request::isPost()) {
            return $this->error('请求方式错误');
        }

        $symbol = Request::post('symbol', 'BTCUSDT');

        $result = $this->klineService->initSymbolKlines($symbol);

        return json($result);
    }

    /**
     * 获取K线配置信息
     * GET /api/kline/config
     */
    public function config()
    {
        $config = [
            'supported_intervals' => KlineModel::getSupportedIntervals(),
            'default_interval' => '1h',
            'max_limit' => 1000,
            'default_limit' => 500,
            'supported_symbols' => [
                'BTCUSDT' => 'BTC/USDT',
                'ETHUSDT' => 'ETH/USDT',
                'LTCUSDT' => 'LTC/USDT',
                'EOSUSDT' => 'EOS/USDT',
                'XRPUSDT' => 'XRP/USDT'
            ]
        ];

        return $this->success('获取成功', $config);
    }

    /**
     * WebSocket K线数据推送格式
     * 这个方法用于文档说明，实际推送在WebSocket服务中实现
     */
    public function websocketFormat()
    {
        $format = [
            'stream' => 'btcusdt@kline_1m',
            'data' => [
                'e' => 'kline',
                'E' => 1640995200000,
                's' => 'BTCUSDT',
                'k' => [
                    't' => 1640995200000,  // 开盘时间
                    'T' => 1640995259999,  // 收盘时间
                    's' => 'BTCUSDT',      // 交易对
                    'i' => '1m',           // 时间周期
                    'f' => 100,            // 第一笔交易ID
                    'L' => 200,            // 最后一笔交易ID
                    'o' => '0.0010',       // 开盘价
                    'c' => '0.0020',       // 收盘价
                    'h' => '0.0025',       // 最高价
                    'l' => '0.0015',       // 最低价
                    'v' => '1000',         // 成交量
                    'n' => 100,            // 成交笔数
                    'x' => false,          // 是否完结
                    'q' => '1.0000',       // 成交额
                    'V' => '500',          // 主动买入成交量
                    'Q' => '0.500'         // 主动买入成交额
                ]
            ]
        ];

        return $this->success('WebSocket推送格式', $format);
    }
}
