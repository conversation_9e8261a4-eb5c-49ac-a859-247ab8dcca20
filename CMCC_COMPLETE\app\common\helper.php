<?php
/**
 * 全局帮助函数
 * 提供常用的工具函数和快捷方法
 */

if (!function_exists('success')) {
    /**
     * 返回成功响应
     */
    function success($msg = '操作成功', $data = [], $code = 1)
    {
        return json([
            'code' => $code,
            'msg' => $msg,
            'data' => $data,
            'timestamp' => time()
        ]);
    }
}

if (!function_exists('error')) {
    /**
     * 返回错误响应
     */
    function error($msg = '操作失败', $data = [], $code = 0)
    {
        return json([
            'code' => $code,
            'msg' => $msg,
            'data' => $data,
            'timestamp' => time()
        ]);
    }
}

if (!function_exists('format_number')) {
    /**
     * 格式化数字
     */
    function format_number($number, $decimals = 8)
    {
        return number_format((float)$number, $decimals, '.', '');
    }
}

if (!function_exists('format_currency')) {
    /**
     * 格式化货币
     */
    function format_currency($amount, $symbol = '$', $decimals = 2)
    {
        return $symbol . number_format((float)$amount, $decimals, '.', ',');
    }
}

if (!function_exists('generate_order_id')) {
    /**
     * 生成订单号
     */
    function generate_order_id($prefix = '')
    {
        return $prefix . date('YmdHis') . mt_rand(1000, 9999);
    }
}

if (!function_exists('encrypt_password')) {
    /**
     * 加密密码
     */
    function encrypt_password($password)
    {
        return password_hash($password, PASSWORD_DEFAULT);
    }
}

if (!function_exists('verify_password')) {
    /**
     * 验证密码
     */
    function verify_password($password, $hash)
    {
        return password_verify($password, $hash);
    }
}

if (!function_exists('generate_token')) {
    /**
     * 生成随机token
     */
    function generate_token($length = 32)
    {
        return bin2hex(random_bytes($length / 2));
    }
}

if (!function_exists('mask_email')) {
    /**
     * 邮箱脱敏
     */
    function mask_email($email)
    {
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return $email;
        }
        
        $parts = explode('@', $email);
        $username = $parts[0];
        $domain = $parts[1];
        
        $usernameLength = strlen($username);
        if ($usernameLength <= 2) {
            $maskedUsername = str_repeat('*', $usernameLength);
        } else {
            $maskedUsername = substr($username, 0, 1) . str_repeat('*', $usernameLength - 2) . substr($username, -1);
        }
        
        return $maskedUsername . '@' . $domain;
    }
}

if (!function_exists('mask_phone')) {
    /**
     * 手机号脱敏
     */
    function mask_phone($phone)
    {
        if (empty($phone) || strlen($phone) < 7) {
            return $phone;
        }
        
        return substr($phone, 0, 3) . '****' . substr($phone, -4);
    }
}

if (!function_exists('get_client_ip')) {
    /**
     * 获取客户端IP
     */
    function get_client_ip()
    {
        $ip = '';
        
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED'];
        } elseif (!empty($_SERVER['HTTP_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_FORWARDED_FOR'];
        } elseif (!empty($_SERVER['HTTP_FORWARDED'])) {
            $ip = $_SERVER['HTTP_FORWARDED'];
        } elseif (!empty($_SERVER['REMOTE_ADDR'])) {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        
        // 处理多个IP的情况
        if (strpos($ip, ',') !== false) {
            $ip = trim(explode(',', $ip)[0]);
        }
        
        return filter_var($ip, FILTER_VALIDATE_IP) ? $ip : '0.0.0.0';
    }
}

if (!function_exists('get_user_agent')) {
    /**
     * 获取用户代理
     */
    function get_user_agent()
    {
        return $_SERVER['HTTP_USER_AGENT'] ?? '';
    }
}

if (!function_exists('is_mobile')) {
    /**
     * 判断是否为移动设备
     */
    function is_mobile()
    {
        $userAgent = get_user_agent();
        return preg_match('/(android|iphone|ipad|mobile)/i', $userAgent);
    }
}

if (!function_exists('calculate_percentage')) {
    /**
     * 计算百分比
     */
    function calculate_percentage($part, $total, $decimals = 2)
    {
        if ($total == 0) {
            return 0;
        }
        
        return round(($part / $total) * 100, $decimals);
    }
}

if (!function_exists('time_ago')) {
    /**
     * 时间差显示
     */
    function time_ago($timestamp)
    {
        $time = is_numeric($timestamp) ? $timestamp : strtotime($timestamp);
        $diff = time() - $time;
        
        if ($diff < 60) {
            return '刚刚';
        } elseif ($diff < 3600) {
            return floor($diff / 60) . '分钟前';
        } elseif ($diff < 86400) {
            return floor($diff / 3600) . '小时前';
        } elseif ($diff < 2592000) {
            return floor($diff / 86400) . '天前';
        } else {
            return date('Y-m-d', $time);
        }
    }
}

if (!function_exists('validate_email')) {
    /**
     * 验证邮箱
     */
    function validate_email($email)
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
}

if (!function_exists('validate_phone')) {
    /**
     * 验证手机号
     */
    function validate_phone($phone)
    {
        return preg_match('/^1[3-9]\d{9}$/', $phone);
    }
}

if (!function_exists('generate_verification_code')) {
    /**
     * 生成验证码
     */
    function generate_verification_code($length = 6)
    {
        return str_pad(mt_rand(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
    }
}

if (!function_exists('safe_json_decode')) {
    /**
     * 安全的JSON解码
     */
    function safe_json_decode($json, $assoc = true, $default = [])
    {
        $result = json_decode($json, $assoc);
        return json_last_error() === JSON_ERROR_NONE ? $result : $default;
    }
}

if (!function_exists('array_get')) {
    /**
     * 安全获取数组值
     */
    function array_get($array, $key, $default = null)
    {
        if (is_null($key)) {
            return $array;
        }
        
        if (isset($array[$key])) {
            return $array[$key];
        }
        
        foreach (explode('.', $key) as $segment) {
            if (is_array($array) && array_key_exists($segment, $array)) {
                $array = $array[$segment];
            } else {
                return $default;
            }
        }
        
        return $array;
    }
}

if (!function_exists('env')) {
    /**
     * 获取环境变量
     */
    function env($key, $default = null)
    {
        $value = $_ENV[$key] ?? $_SERVER[$key] ?? getenv($key);
        
        if ($value === false) {
            return $default;
        }
        
        // 转换布尔值
        switch (strtolower($value)) {
            case 'true':
            case '(true)':
                return true;
            case 'false':
            case '(false)':
                return false;
            case 'empty':
            case '(empty)':
                return '';
            case 'null':
            case '(null)':
                return null;
        }
        
        // 处理引号包围的值
        if (strlen($value) > 1 && $value[0] === '"' && $value[-1] === '"') {
            return substr($value, 1, -1);
        }
        
        return $value;
    }
}

if (!function_exists('config')) {
    /**
     * 获取配置值
     */
    function config($key, $default = null)
    {
        return \think\facade\Config::get($key, $default);
    }
}

if (!function_exists('cache')) {
    /**
     * 缓存操作
     */
    function cache($key, $value = null, $ttl = null)
    {
        if (is_null($value)) {
            return \think\facade\Cache::get($key);
        }
        
        if (is_null($ttl)) {
            return \think\facade\Cache::set($key, $value);
        }
        
        return \think\facade\Cache::set($key, $value, $ttl);
    }
}

if (!function_exists('log_info')) {
    /**
     * 记录信息日志
     */
    function log_info($message, $context = [])
    {
        \think\facade\Log::info($message, $context);
    }
}

if (!function_exists('log_error')) {
    /**
     * 记录错误日志
     */
    function log_error($message, $context = [])
    {
        \think\facade\Log::error($message, $context);
    }
}
