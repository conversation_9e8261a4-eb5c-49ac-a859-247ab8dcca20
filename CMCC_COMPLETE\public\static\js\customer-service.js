/**
 * GVD客服系统 - 用户端
 * WhatsApp风格聊天界面
 */

class CustomerService {
    constructor() {
        this.isOpen = false;
        this.isConnected = false;
        this.sessionId = null;
        this.userId = null;
        this.token = localStorage.getItem('gvd_token');
        this.websocket = null;
        this.unreadCount = 0;
        this.isTyping = false;
        this.typingTimer = null;
        this.messageContainer = null;
        this.inputElement = null;
        
        this.init();
    }

    init() {
        this.createFloatingButton();
        this.createChatWindow();
        this.bindEvents();
        this.loadEmojis();
        
        // 如果用户已登录，自动连接WebSocket
        if (this.token) {
            this.connectWebSocket();
        }
    }

    /**
     * 创建悬浮按钮
     */
    createFloatingButton() {
        const button = document.createElement('div');
        button.id = 'gvd-customer-btn';
        button.className = 'gvd-customer-btn';
        button.innerHTML = `
            <div class="btn-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M20 2H4C2.9 2 2 2.9 2 4V22L6 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z" fill="currentColor"/>
                    <circle cx="8" cy="10" r="1.5" fill="white"/>
                    <circle cx="12" cy="10" r="1.5" fill="white"/>
                    <circle cx="16" cy="10" r="1.5" fill="white"/>
                </svg>
            </div>
            <div class="btn-badge" id="gvd-unread-badge" style="display: none;">0</div>
            <div class="btn-pulse"></div>
        `;
        
        document.body.appendChild(button);
    }

    /**
     * 创建聊天窗口
     */
    createChatWindow() {
        const chatWindow = document.createElement('div');
        chatWindow.id = 'gvd-chat-window';
        chatWindow.className = 'gvd-chat-window';
        chatWindow.innerHTML = `
            <div class="chat-header">
                <div class="header-info">
                    <div class="avatar">
                        <img src="/static/images/customer-service.png" alt="客服" onerror="this.style.display='none'">
                        <div class="avatar-fallback">客服</div>
                    </div>
                    <div class="info">
                        <div class="name">GVD客服</div>
                        <div class="status" id="gvd-service-status">离线</div>
                    </div>
                </div>
                <div class="header-actions">
                    <button class="action-btn" id="gvd-minimize-btn" title="最小化">
                        <svg width="16" height="16" viewBox="0 0 16 16">
                            <path d="M3 8h10" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </button>
                    <button class="action-btn" id="gvd-close-btn" title="关闭">
                        <svg width="16" height="16" viewBox="0 0 16 16">
                            <path d="M12 4L4 12M4 4l8 8" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </button>
                </div>
            </div>
            
            <div class="chat-body">
                <div class="messages-container" id="gvd-messages">
                    <div class="welcome-message">
                        <div class="welcome-icon">👋</div>
                        <div class="welcome-text">
                            <h3>欢迎使用GVD客服</h3>
                            <p>我们的客服团队随时为您提供帮助</p>
                        </div>
                    </div>
                </div>
                
                <div class="typing-indicator" id="gvd-typing" style="display: none;">
                    <div class="typing-avatar">客服</div>
                    <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>
            
            <div class="chat-footer">
                <div class="emoji-panel" id="gvd-emoji-panel" style="display: none;">
                    <div class="emoji-tabs">
                        <button class="emoji-tab active" data-category="smileys">😀</button>
                        <button class="emoji-tab" data-category="emotions">😢</button>
                        <button class="emoji-tab" data-category="gestures">👍</button>
                        <button class="emoji-tab" data-category="objects">💰</button>
                    </div>
                    <div class="emoji-content" id="gvd-emoji-content">
                        <!-- 表情包内容 -->
                    </div>
                </div>
                
                <div class="input-area">
                    <button class="tool-btn" id="gvd-emoji-btn" title="表情">
                        <svg width="20" height="20" viewBox="0 0 20 20">
                            <circle cx="10" cy="10" r="8" stroke="currentColor" stroke-width="1.5" fill="none"/>
                            <circle cx="7" cy="8" r="1" fill="currentColor"/>
                            <circle cx="13" cy="8" r="1" fill="currentColor"/>
                            <path d="M6.5 12.5c1 1.5 3 1.5 4 0s3-1.5 4 0" stroke="currentColor" stroke-width="1.5" fill="none"/>
                        </svg>
                    </button>
                    
                    <button class="tool-btn" id="gvd-image-btn" title="图片">
                        <svg width="20" height="20" viewBox="0 0 20 20">
                            <rect x="2" y="2" width="16" height="16" rx="2" stroke="currentColor" stroke-width="1.5" fill="none"/>
                            <circle cx="7" cy="7" r="1.5" fill="currentColor"/>
                            <path d="M18 12l-4-4-6 6-2-2-4 4" stroke="currentColor" stroke-width="1.5" fill="none"/>
                        </svg>
                    </button>
                    
                    <div class="input-wrapper">
                        <textarea 
                            id="gvd-message-input" 
                            placeholder="输入消息..." 
                            rows="1"
                            maxlength="1000"
                        ></textarea>
                        <div class="input-counter">
                            <span id="gvd-char-count">0</span>/1000
                        </div>
                    </div>
                    
                    <button class="send-btn" id="gvd-send-btn" disabled title="发送">
                        <svg width="20" height="20" viewBox="0 0 20 20">
                            <path d="M18 2L2 8l4 2 8-6-6 8 2 4 6-16z" fill="currentColor"/>
                        </svg>
                    </button>
                </div>
                
                <input type="file" id="gvd-file-input" accept="image/*" style="display: none;">
            </div>
        `;
        
        document.body.appendChild(chatWindow);
        
        // 保存重要元素引用
        this.messageContainer = document.getElementById('gvd-messages');
        this.inputElement = document.getElementById('gvd-message-input');
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 悬浮按钮点击
        document.getElementById('gvd-customer-btn').addEventListener('click', () => {
            this.toggleChat();
        });

        // 窗口控制按钮
        document.getElementById('gvd-minimize-btn').addEventListener('click', () => {
            this.minimizeChat();
        });

        document.getElementById('gvd-close-btn').addEventListener('click', () => {
            this.closeChat();
        });

        // 消息输入
        this.inputElement.addEventListener('input', (e) => {
            this.handleInput(e);
        });

        this.inputElement.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // 发送按钮
        document.getElementById('gvd-send-btn').addEventListener('click', () => {
            this.sendMessage();
        });

        // 表情按钮
        document.getElementById('gvd-emoji-btn').addEventListener('click', () => {
            this.toggleEmojiPanel();
        });

        // 图片按钮
        document.getElementById('gvd-image-btn').addEventListener('click', () => {
            document.getElementById('gvd-file-input').click();
        });

        // 文件选择
        document.getElementById('gvd-file-input').addEventListener('change', (e) => {
            this.handleFileSelect(e);
        });

        // 表情选择
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('emoji-item')) {
                this.insertEmoji(e.target.textContent);
            }
        });

        // 表情分类切换
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('emoji-tab')) {
                this.switchEmojiCategory(e.target.dataset.category);
            }
        });

        // 点击外部关闭表情面板
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.chat-footer')) {
                this.hideEmojiPanel();
            }
        });

        // 窗口大小变化
        window.addEventListener('resize', () => {
            this.adjustChatPosition();
        });
    }

    /**
     * 切换聊天窗口
     */
    toggleChat() {
        if (this.isOpen) {
            this.closeChat();
        } else {
            this.openChat();
        }
    }

    /**
     * 打开聊天窗口
     */
    async openChat() {
        if (!this.token) {
            this.showLoginPrompt();
            return;
        }

        this.isOpen = true;
        const chatWindow = document.getElementById('gvd-chat-window');
        const floatingBtn = document.getElementById('gvd-customer-btn');
        
        chatWindow.classList.add('open');
        floatingBtn.classList.add('active');
        
        // 连接WebSocket
        if (!this.isConnected) {
            await this.connectWebSocket();
        }
        
        // 创建或获取会话
        if (!this.sessionId) {
            await this.createSession();
        }
        
        // 加载消息历史
        await this.loadMessages();
        
        // 聚焦输入框
        setTimeout(() => {
            this.inputElement.focus();
        }, 300);
        
        // 清除未读计数
        this.clearUnreadCount();
    }

    /**
     * 关闭聊天窗口
     */
    closeChat() {
        this.isOpen = false;
        const chatWindow = document.getElementById('gvd-chat-window');
        const floatingBtn = document.getElementById('gvd-customer-btn');
        
        chatWindow.classList.remove('open');
        floatingBtn.classList.remove('active');
        
        this.hideEmojiPanel();
    }

    /**
     * 最小化聊天窗口
     */
    minimizeChat() {
        this.closeChat();
    }

    /**
     * 显示登录提示
     */
    showLoginPrompt() {
        const notification = this.createNotification(
            '请先登录',
            '使用客服功能需要先登录您的账户',
            'warning',
            [
                {
                    text: '立即登录',
                    action: () => {
                        window.location.href = '/login';
                    }
                },
                {
                    text: '取消',
                    action: () => {}
                }
            ]
        );
        
        document.body.appendChild(notification);
    }

    /**
     * 连接WebSocket
     */
    async connectWebSocket() {
        if (this.websocket) {
            return;
        }

        try {
            const wsUrl = `ws://${window.location.hostname}:2346`;
            this.websocket = new WebSocket(wsUrl);

            this.websocket.onopen = () => {
                console.log('WebSocket连接成功');
                this.isConnected = true;
                this.updateServiceStatus('在线');
                
                // 发送认证消息
                this.sendWebSocketMessage({
                    type: 'auth',
                    data: {
                        token: this.token,
                        user_type: 'user'
                    }
                });
            };

            this.websocket.onmessage = (event) => {
                this.handleWebSocketMessage(JSON.parse(event.data));
            };

            this.websocket.onclose = () => {
                console.log('WebSocket连接关闭');
                this.isConnected = false;
                this.updateServiceStatus('离线');
                
                // 尝试重连
                setTimeout(() => {
                    if (this.isOpen) {
                        this.connectWebSocket();
                    }
                }, 3000);
            };

            this.websocket.onerror = (error) => {
                console.error('WebSocket错误:', error);
                this.updateServiceStatus('连接错误');
            };

        } catch (error) {
            console.error('WebSocket连接失败:', error);
            this.updateServiceStatus('连接失败');
        }
    }

    /**
     * 发送WebSocket消息
     */
    sendWebSocketMessage(message) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify(message));
        }
    }

    /**
     * 处理WebSocket消息
     */
    handleWebSocketMessage(message) {
        switch (message.type) {
            case 'auth_success':
                console.log('认证成功:', message.data);
                this.userId = message.data.user_id;
                break;
                
            case 'new_message':
                this.displayMessage(message.data);
                this.playNotificationSound();
                if (!this.isOpen) {
                    this.incrementUnreadCount();
                }
                break;
                
            case 'user_typing':
                this.showTypingIndicator(message.data);
                break;
                
            case 'error':
                console.error('WebSocket错误:', message.data.message);
                this.showErrorMessage(message.data.message);
                break;
                
            case 'heartbeat_response':
                // 心跳响应
                break;
        }
    }

    /**
     * 更新服务状态
     */
    updateServiceStatus(status) {
        const statusElement = document.getElementById('gvd-service-status');
        if (statusElement) {
            statusElement.textContent = status;
            statusElement.className = `status ${status === '在线' ? 'online' : 'offline'}`;
        }
    }

    /**
     * 创建会话
     */
    async createSession() {
        try {
            const response = await fetch('/api/customer/session/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.token}`
                }
            });

            const result = await response.json();

            if (result.code === 1) {
                this.sessionId = result.data.session_id;

                // 加入WebSocket会话
                this.sendWebSocketMessage({
                    type: 'join_session',
                    data: {
                        session_id: this.sessionId
                    }
                });

                return true;
            } else {
                this.showErrorMessage(result.msg || '创建会话失败');
                return false;
            }
        } catch (error) {
            console.error('创建会话失败:', error);
            this.showErrorMessage('网络错误，请稍后重试');
            return false;
        }
    }

    /**
     * 加载消息历史
     */
    async loadMessages() {
        if (!this.sessionId) return;

        try {
            const response = await fetch(`/api/customer/messages?session_id=${this.sessionId}`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            const result = await response.json();

            if (result.code === 1) {
                this.clearMessages();
                result.data.messages.forEach(message => {
                    this.displayMessage(message, false);
                });
                this.scrollToBottom();
            }
        } catch (error) {
            console.error('加载消息失败:', error);
        }
    }

    /**
     * 发送消息
     */
    async sendMessage() {
        const content = this.inputElement.value.trim();
        if (!content || !this.sessionId) return;

        // 清空输入框
        this.inputElement.value = '';
        this.updateCharCount();
        this.updateSendButton();

        // 显示发送中的消息
        const tempMessage = this.displayMessage({
            content: content,
            sender_type: 'user',
            sender_info: { username: '我' },
            created_at: new Date().toISOString(),
            sending: true
        });

        try {
            // 通过WebSocket发送
            this.sendWebSocketMessage({
                type: 'send_message',
                data: {
                    session_id: this.sessionId,
                    content: content,
                    message_type: 'text'
                }
            });

            // 移除发送中状态
            setTimeout(() => {
                tempMessage.classList.remove('sending');
            }, 1000);

        } catch (error) {
            console.error('发送消息失败:', error);
            tempMessage.classList.add('failed');
            this.showErrorMessage('消息发送失败');
        }
    }

    /**
     * 显示消息
     */
    displayMessage(message, animate = true) {
        const messageElement = document.createElement('div');
        const isOwn = message.sender_type === 'user';
        const time = new Date(message.created_at).toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });

        messageElement.className = `message ${isOwn ? 'own' : 'other'}`;
        if (message.sending) messageElement.classList.add('sending');

        let messageContent = '';

        switch (message.message_type) {
            case 'image':
                messageContent = `
                    <div class="message-image">
                        <img src="${message.file_url}" alt="图片" onclick="this.requestFullscreen()">
                    </div>
                `;
                break;
            case 'emoji':
                messageContent = `<div class="message-emoji">${message.content}</div>`;
                break;
            default:
                messageContent = `<div class="message-text">${this.formatMessageText(message.content)}</div>`;
        }

        messageElement.innerHTML = `
            <div class="message-avatar">
                ${isOwn ? '我' : (message.sender_info?.username?.charAt(0) || '客')}
            </div>
            <div class="message-content">
                <div class="message-bubble">
                    ${messageContent}
                </div>
                <div class="message-time">${time}</div>
            </div>
        `;

        // 移除欢迎消息
        const welcomeMessage = this.messageContainer.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }

        this.messageContainer.appendChild(messageElement);

        if (animate) {
            messageElement.style.opacity = '0';
            messageElement.style.transform = 'translateY(20px)';

            requestAnimationFrame(() => {
                messageElement.style.transition = 'all 0.3s ease';
                messageElement.style.opacity = '1';
                messageElement.style.transform = 'translateY(0)';
            });
        }

        this.scrollToBottom();
        return messageElement;
    }

    /**
     * 格式化消息文本
     */
    formatMessageText(text) {
        // 转义HTML
        text = text.replace(/[<>&"]/g, (match) => {
            const escapeMap = {
                '<': '&lt;',
                '>': '&gt;',
                '&': '&amp;',
                '"': '&quot;'
            };
            return escapeMap[match];
        });

        // 处理换行
        text = text.replace(/\n/g, '<br>');

        // 处理链接
        text = text.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>');

        return text;
    }

    /**
     * 处理输入
     */
    handleInput(e) {
        this.updateCharCount();
        this.updateSendButton();
        this.autoResizeTextarea();
        this.handleTyping();
    }

    /**
     * 更新字符计数
     */
    updateCharCount() {
        const count = this.inputElement.value.length;
        const counter = document.getElementById('gvd-char-count');
        if (counter) {
            counter.textContent = count;
            counter.style.color = count > 900 ? '#f84960' : '#848e9c';
        }
    }

    /**
     * 更新发送按钮状态
     */
    updateSendButton() {
        const sendBtn = document.getElementById('gvd-send-btn');
        const hasContent = this.inputElement.value.trim().length > 0;

        sendBtn.disabled = !hasContent || !this.sessionId;
        sendBtn.classList.toggle('active', hasContent && this.sessionId);
    }

    /**
     * 自动调整文本框高度
     */
    autoResizeTextarea() {
        this.inputElement.style.height = 'auto';
        const newHeight = Math.min(this.inputElement.scrollHeight, 120);
        this.inputElement.style.height = newHeight + 'px';
    }

    /**
     * 处理输入状态
     */
    handleTyping() {
        if (!this.sessionId) return;

        if (!this.isTyping) {
            this.isTyping = true;
            this.sendWebSocketMessage({
                type: 'typing',
                data: {
                    session_id: this.sessionId,
                    typing: true
                }
            });
        }

        // 清除之前的定时器
        if (this.typingTimer) {
            clearTimeout(this.typingTimer);
        }

        // 3秒后停止输入状态
        this.typingTimer = setTimeout(() => {
            this.isTyping = false;
            this.sendWebSocketMessage({
                type: 'typing',
                data: {
                    session_id: this.sessionId,
                    typing: false
                }
            });
        }, 3000);
    }

    /**
     * 显示输入指示器
     */
    showTypingIndicator(data) {
        if (data.user_type === 'user') return; // 不显示用户的输入状态

        const typingElement = document.getElementById('gvd-typing');

        if (data.typing) {
            typingElement.style.display = 'flex';
            this.scrollToBottom();
        } else {
            typingElement.style.display = 'none';
        }
    }

    /**
     * 滚动到底部
     */
    scrollToBottom() {
        setTimeout(() => {
            this.messageContainer.scrollTop = this.messageContainer.scrollHeight;
        }, 100);
    }

    /**
     * 清空消息
     */
    clearMessages() {
        this.messageContainer.innerHTML = '';
    }

    /**
     * 加载表情包
     */
    async loadEmojis() {
        try {
            const response = await fetch('/api/customer/emojis');
            const result = await response.json();

            if (result.code === 1) {
                this.emojis = result.data;
                this.renderEmojiPanel('smileys');
            }
        } catch (error) {
            console.error('加载表情包失败:', error);
        }
    }

    /**
     * 切换表情面板
     */
    toggleEmojiPanel() {
        const panel = document.getElementById('gvd-emoji-panel');
        const isVisible = panel.style.display !== 'none';

        if (isVisible) {
            this.hideEmojiPanel();
        } else {
            this.showEmojiPanel();
        }
    }

    /**
     * 显示表情面板
     */
    showEmojiPanel() {
        const panel = document.getElementById('gvd-emoji-panel');
        panel.style.display = 'block';

        // 添加动画
        panel.style.opacity = '0';
        panel.style.transform = 'translateY(10px)';

        requestAnimationFrame(() => {
            panel.style.transition = 'all 0.2s ease';
            panel.style.opacity = '1';
            panel.style.transform = 'translateY(0)';
        });
    }

    /**
     * 隐藏表情面板
     */
    hideEmojiPanel() {
        const panel = document.getElementById('gvd-emoji-panel');
        panel.style.display = 'none';
    }

    /**
     * 切换表情分类
     */
    switchEmojiCategory(category) {
        // 更新标签状态
        document.querySelectorAll('.emoji-tab').forEach(tab => {
            tab.classList.toggle('active', tab.dataset.category === category);
        });

        // 渲染表情
        this.renderEmojiPanel(category);
    }

    /**
     * 渲染表情面板
     */
    renderEmojiPanel(category) {
        const content = document.getElementById('gvd-emoji-content');
        if (!this.emojis || !this.emojis[category]) return;

        const emojis = this.emojis[category].emojis;
        content.innerHTML = emojis.map(emoji =>
            `<span class="emoji-item" title="${emoji}">${emoji}</span>`
        ).join('');
    }

    /**
     * 插入表情
     */
    insertEmoji(emoji) {
        const input = this.inputElement;
        const start = input.selectionStart;
        const end = input.selectionEnd;
        const text = input.value;

        input.value = text.substring(0, start) + emoji + text.substring(end);
        input.selectionStart = input.selectionEnd = start + emoji.length;

        this.updateCharCount();
        this.updateSendButton();
        this.hideEmojiPanel();
        input.focus();
    }

    /**
     * 处理文件选择
     */
    async handleFileSelect(e) {
        const file = e.target.files[0];
        if (!file) return;

        // 验证文件类型
        if (!file.type.startsWith('image/')) {
            this.showErrorMessage('只支持图片文件');
            return;
        }

        // 验证文件大小 (5MB)
        if (file.size > 5 * 1024 * 1024) {
            this.showErrorMessage('图片大小不能超过5MB');
            return;
        }

        // 显示上传进度
        const progressMessage = this.displayMessage({
            content: '正在上传图片...',
            sender_type: 'user',
            sender_info: { username: '我' },
            created_at: new Date().toISOString(),
            uploading: true
        });

        try {
            // 上传文件
            const formData = new FormData();
            formData.append('image', file);

            const response = await fetch('/api/customer/image/upload', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`
                },
                body: formData
            });

            const result = await response.json();

            if (result.code === 1) {
                // 移除进度消息
                progressMessage.remove();

                // 发送图片消息
                this.sendWebSocketMessage({
                    type: 'send_message',
                    data: {
                        session_id: this.sessionId,
                        content: result.data.name,
                        message_type: 'image',
                        file_url: result.data.url
                    }
                });
            } else {
                progressMessage.remove();
                this.showErrorMessage(result.msg || '图片上传失败');
            }
        } catch (error) {
            progressMessage.remove();
            console.error('图片上传失败:', error);
            this.showErrorMessage('图片上传失败');
        }

        // 清空文件输入
        e.target.value = '';
    }

    /**
     * 增加未读计数
     */
    incrementUnreadCount() {
        this.unreadCount++;
        this.updateUnreadBadge();
        this.showBrowserNotification();
    }

    /**
     * 清除未读计数
     */
    clearUnreadCount() {
        this.unreadCount = 0;
        this.updateUnreadBadge();
    }

    /**
     * 更新未读徽章
     */
    updateUnreadBadge() {
        const badge = document.getElementById('gvd-unread-badge');
        if (this.unreadCount > 0) {
            badge.textContent = this.unreadCount > 99 ? '99+' : this.unreadCount;
            badge.style.display = 'block';
        } else {
            badge.style.display = 'none';
        }
    }

    /**
     * 播放通知声音
     */
    playNotificationSound() {
        if (!this.isOpen) {
            try {
                const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                audio.volume = 0.3;
                audio.play().catch(() => {});
            } catch (error) {
                // 忽略音频播放错误
            }
        }
    }

    /**
     * 显示浏览器通知
     */
    showBrowserNotification() {
        if (!this.isOpen && 'Notification' in window) {
            if (Notification.permission === 'granted') {
                new Notification('GVD客服', {
                    body: '您有新的客服消息',
                    icon: '/static/images/favicon.svg',
                    tag: 'gvd-customer-service'
                });
            } else if (Notification.permission !== 'denied') {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        this.showBrowserNotification();
                    }
                });
            }
        }
    }

    /**
     * 显示错误消息
     */
    showErrorMessage(message) {
        const notification = this.createNotification('错误', message, 'error');
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 5000);
    }

    /**
     * 创建通知
     */
    createNotification(title, message, type = 'info', actions = []) {
        const notification = document.createElement('div');
        notification.className = `gvd-notification ${type}`;

        const actionsHtml = actions.map(action =>
            `<button class="notification-btn" onclick="this.parentElement.parentElement.remove(); (${action.action})()">${action.text}</button>`
        ).join('');

        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-title">${title}</div>
                <div class="notification-message">${message}</div>
                ${actionsHtml ? `<div class="notification-actions">${actionsHtml}</div>` : ''}
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">×</button>
        `;

        return notification;
    }

    /**
     * 调整聊天位置
     */
    adjustChatPosition() {
        const chatWindow = document.getElementById('gvd-chat-window');
        const windowHeight = window.innerHeight;
        const windowWidth = window.innerWidth;

        // 移动端适配
        if (windowWidth <= 768) {
            chatWindow.style.width = '100%';
            chatWindow.style.height = '100%';
            chatWindow.style.right = '0';
            chatWindow.style.bottom = '0';
        } else {
            chatWindow.style.width = '380px';
            chatWindow.style.height = '600px';
            chatWindow.style.right = '20px';
            chatWindow.style.bottom = '90px';
        }
    }

    /**
     * 添加快捷键支持
     */
    initKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Shift + C 打开/关闭客服
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'C') {
                e.preventDefault();
                this.toggleChat();
            }

            // ESC 关闭客服窗口
            if (e.key === 'Escape' && this.isOpen) {
                this.closeChat();
            }
        });
    }

    /**
     * 添加拖拽支持
     */
    initDragSupport() {
        const floatingBtn = document.getElementById('gvd-customer-btn');
        let isDragging = false;
        let startX, startY, startLeft, startTop;

        floatingBtn.addEventListener('mousedown', (e) => {
            if (e.button !== 0) return; // 只响应左键

            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;

            const rect = floatingBtn.getBoundingClientRect();
            startLeft = rect.left;
            startTop = rect.top;

            floatingBtn.style.transition = 'none';
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;

            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;

            let newLeft = startLeft + deltaX;
            let newTop = startTop + deltaY;

            // 边界检查
            const maxLeft = window.innerWidth - floatingBtn.offsetWidth;
            const maxTop = window.innerHeight - floatingBtn.offsetHeight;

            newLeft = Math.max(0, Math.min(newLeft, maxLeft));
            newTop = Math.max(0, Math.min(newTop, maxTop));

            floatingBtn.style.left = newLeft + 'px';
            floatingBtn.style.top = newTop + 'px';
            floatingBtn.style.right = 'auto';
            floatingBtn.style.bottom = 'auto';
        });

        document.addEventListener('mouseup', (e) => {
            if (!isDragging) return;

            isDragging = false;
            floatingBtn.style.transition = '';

            // 吸附到边缘
            const rect = floatingBtn.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const windowWidth = window.innerWidth;

            if (centerX < windowWidth / 2) {
                // 吸附到左边
                floatingBtn.style.left = '20px';
                floatingBtn.style.right = 'auto';
            } else {
                // 吸附到右边
                floatingBtn.style.left = 'auto';
                floatingBtn.style.right = '20px';
            }
        });
    }

    /**
     * 添加消息搜索功能
     */
    initMessageSearch() {
        // 在聊天头部添加搜索按钮
        const chatHeader = document.querySelector('.chat-header .header-actions');
        if (chatHeader) {
            const searchBtn = document.createElement('button');
            searchBtn.className = 'action-btn';
            searchBtn.title = '搜索消息';
            searchBtn.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 16 16">
                    <circle cx="6" cy="6" r="4" stroke="currentColor" stroke-width="1.5" fill="none"/>
                    <path d="M10 10l4 4" stroke="currentColor" stroke-width="1.5"/>
                </svg>
            `;
            searchBtn.addEventListener('click', () => this.showSearchPanel());
            chatHeader.insertBefore(searchBtn, chatHeader.firstChild);
        }
    }

    /**
     * 显示搜索面板
     */
    showSearchPanel() {
        const searchPanel = document.createElement('div');
        searchPanel.className = 'search-panel';
        searchPanel.innerHTML = `
            <div class="search-header">
                <input type="text" id="messageSearch" placeholder="搜索消息..." autocomplete="off">
                <button class="search-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
            <div class="search-results" id="searchResults">
                <div class="search-tip">输入关键词搜索消息历史</div>
            </div>
        `;

        const chatBody = document.querySelector('.chat-body');
        chatBody.appendChild(searchPanel);

        // 绑定搜索事件
        const searchInput = document.getElementById('messageSearch');
        searchInput.focus();

        let searchTimer;
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimer);
            searchTimer = setTimeout(() => {
                this.searchMessages(e.target.value);
            }, 300);
        });
    }

    /**
     * 搜索消息
     */
    async searchMessages(keyword) {
        if (!keyword.trim() || !this.sessionId) {
            document.getElementById('searchResults').innerHTML = '<div class="search-tip">输入关键词搜索消息历史</div>';
            return;
        }

        try {
            const response = await fetch(`/api/customer/messages/search?session_id=${this.sessionId}&keyword=${encodeURIComponent(keyword)}`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            const result = await response.json();

            if (result.code === 1) {
                this.renderSearchResults(result.data.messages, keyword);
            }
        } catch (error) {
            console.error('搜索消息失败:', error);
        }
    }

    /**
     * 渲染搜索结果
     */
    renderSearchResults(messages, keyword) {
        const container = document.getElementById('searchResults');

        if (messages.length === 0) {
            container.innerHTML = '<div class="search-tip">没有找到相关消息</div>';
            return;
        }

        const html = messages.map(message => {
            const time = new Date(message.created_at).toLocaleString('zh-CN');
            const highlightedContent = this.highlightKeyword(message.content, keyword);

            return `
                <div class="search-result-item" onclick="customerService.scrollToMessage('${message.id}')">
                    <div class="result-content">${highlightedContent}</div>
                    <div class="result-meta">
                        <span class="result-sender">${message.sender_info?.username || '未知'}</span>
                        <span class="result-time">${time}</span>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = html;
    }

    /**
     * 高亮关键词
     */
    highlightKeyword(text, keyword) {
        if (!keyword) return text;

        const regex = new RegExp(`(${keyword})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }

    /**
     * 滚动到指定消息
     */
    scrollToMessage(messageId) {
        // 关闭搜索面板
        const searchPanel = document.querySelector('.search-panel');
        if (searchPanel) {
            searchPanel.remove();
        }

        // 滚动到消息（这里需要在消息元素上添加ID）
        const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
        if (messageElement) {
            messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            messageElement.classList.add('highlight');
            setTimeout(() => {
                messageElement.classList.remove('highlight');
            }, 2000);
        }
    }

    /**
     * 添加消息反应功能
     */
    initMessageReactions() {
        // 为消息添加右键菜单
        document.addEventListener('contextmenu', (e) => {
            const messageElement = e.target.closest('.message');
            if (messageElement && this.isOpen) {
                e.preventDefault();
                this.showMessageContextMenu(e, messageElement);
            }
        });
    }

    /**
     * 显示消息右键菜单
     */
    showMessageContextMenu(event, messageElement) {
        // 移除已存在的菜单
        const existingMenu = document.querySelector('.message-context-menu');
        if (existingMenu) {
            existingMenu.remove();
        }

        const menu = document.createElement('div');
        menu.className = 'message-context-menu';
        menu.innerHTML = `
            <div class="context-menu-item" onclick="customerService.copyMessage(this)">
                <span class="icon">📋</span>
                <span class="text">复制消息</span>
            </div>
            <div class="context-menu-item" onclick="customerService.replyToMessage(this)">
                <span class="icon">↩️</span>
                <span class="text">回复消息</span>
            </div>
            <div class="context-menu-item" onclick="customerService.reportMessage(this)">
                <span class="icon">⚠️</span>
                <span class="text">举报消息</span>
            </div>
        `;

        // 设置菜单位置
        menu.style.position = 'fixed';
        menu.style.left = event.clientX + 'px';
        menu.style.top = event.clientY + 'px';
        menu.style.zIndex = '10001';

        document.body.appendChild(menu);

        // 点击外部关闭菜单
        const closeMenu = (e) => {
            if (!menu.contains(e.target)) {
                menu.remove();
                document.removeEventListener('click', closeMenu);
            }
        };

        setTimeout(() => {
            document.addEventListener('click', closeMenu);
        }, 100);
    }

    /**
     * 复制消息
     */
    copyMessage(element) {
        const messageElement = element.closest('.message-context-menu').previousElementSibling;
        const messageText = messageElement.querySelector('.message-text')?.textContent || '';

        navigator.clipboard.writeText(messageText).then(() => {
            this.showNotification('消息已复制', 'success');
        }).catch(() => {
            this.showNotification('复制失败', 'error');
        });

        element.closest('.message-context-menu').remove();
    }

    /**
     * 回复消息
     */
    replyToMessage(element) {
        const messageElement = element.closest('.message-context-menu').previousElementSibling;
        const messageText = messageElement.querySelector('.message-text')?.textContent || '';
        const senderName = messageElement.querySelector('.message-sender')?.textContent || '';

        // 在输入框中添加回复引用
        const input = this.inputElement;
        const replyText = `回复 ${senderName}: "${messageText.substring(0, 50)}${messageText.length > 50 ? '...' : '"}"\n\n`;
        input.value = replyText;
        input.focus();
        input.setSelectionRange(input.value.length, input.value.length);

        this.updateCharCount();
        this.updateSendButton();
        this.autoResizeTextarea();

        element.closest('.message-context-menu').remove();
    }

    /**
     * 举报消息
     */
    reportMessage(element) {
        if (confirm('确定要举报这条消息吗？')) {
            this.showNotification('举报已提交，我们会尽快处理', 'success');
        }

        element.closest('.message-context-menu').remove();
    }

    /**
     * 添加消息状态显示
     */
    updateMessageStatus(messageElement, status) {
        const statusElement = messageElement.querySelector('.message-status');
        if (statusElement) {
            statusElement.className = `message-status ${status}`;
            statusElement.innerHTML = this.getStatusIcon(status);
        }
    }

    /**
     * 获取状态图标
     */
    getStatusIcon(status) {
        const icons = {
            'sending': '⏳',
            'sent': '✓',
            'delivered': '✓✓',
            'read': '✓✓',
            'failed': '❌'
        };
        return icons[status] || '';
    }

    /**
     * 添加打字音效
     */
    playTypingSound() {
        if (this.isOpen) return; // 窗口打开时不播放

        try {
            const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
            audio.volume = 0.1;
            audio.play().catch(() => {});
        } catch (error) {
            // 忽略音频播放错误
        }
    }

    /**
     * 初始化所有增强功能
     */
    initEnhancements() {
        this.initKeyboardShortcuts();
        this.initDragSupport();
        this.initMessageSearch();
        this.initMessageReactions();
    }
}

// 自动初始化客服系统
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保页面完全加载
    setTimeout(() => {
        window.customerService = new CustomerService();

        // 初始化增强功能
        window.customerService.initEnhancements();
    }, 1000);
});
