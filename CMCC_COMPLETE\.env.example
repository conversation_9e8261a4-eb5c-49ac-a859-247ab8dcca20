# 应用配置
APP_DEBUG=true
APP_TRACE=false

# 数据库配置
DATABASE_TYPE=mysql
DATABASE_HOSTNAME=127.0.0.1
DATABASE_DATABASE=gvd_trading
DATABASE_USERNAME=root
DATABASE_PASSWORD=
DATABASE_HOSTPORT=3306
DATABASE_CHARSET=utf8mb4
DATABASE_PREFIX=

# Redis配置
REDIS_HOSTNAME=127.0.0.1
REDIS_PASSWORD=
REDIS_PORT=6379
REDIS_SELECT=0

# 缓存配置
CACHE_DRIVER=redis
CACHE_PREFIX=gvd_

# 队列配置
QUEUE_CONNECTOR=redis
QUEUE_REDIS_HOST=127.0.0.1
QUEUE_REDIS_PORT=6379
QUEUE_REDIS_PASSWORD=
QUEUE_REDIS_SELECT=1

# 邮件配置
MAIL_TYPE=smtp
MAIL_SMTP_HOST=smtp.gmail.com
MAIL_SMTP_PORT=587
MAIL_SMTP_USERNAME=<EMAIL>
MAIL_SMTP_PASSWORD=your-app-password
MAIL_FROM_EMAIL=<EMAIL>
MAIL_FROM_NAME=GVD交易平台

# JWT配置
JWT_SECRET=your-jwt-secret-key-here
JWT_TTL=7200
JWT_REFRESH_TTL=20160

# WebSocket配置
WEBSOCKET_HOST=0.0.0.0
WEBSOCKET_PORT=8080
WEBSOCKET_WORKER_NUM=4

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_EXT=jpg,jpeg,png,gif,pdf,doc,docx,txt,zip,rar

# API配置
API_RATE_LIMIT=1000
API_RATE_LIMIT_TIME=3600

# 交易配置
TRADING_FEE_RATE=0.001
TRADING_MIN_AMOUNT=0.001
TRADING_MAX_AMOUNT=1000000

# IDO配置
IDO_MIN_PURCHASE=100
IDO_MAX_PURCHASE=10000
IDO_FEE_RATE=0.02

# 安全配置
PASSWORD_MIN_LENGTH=6
PAY_PASSWORD_LENGTH=6
LOGIN_MAX_ATTEMPTS=5
LOGIN_LOCKOUT_TIME=900

# 第三方服务配置
# 短信服务
SMS_ACCESS_KEY=
SMS_SECRET_KEY=
SMS_SIGN_NAME=GVD交易平台
SMS_TEMPLATE_CODE=

# 区块链节点配置
BTC_NODE_URL=
ETH_NODE_URL=
USDT_CONTRACT_ADDRESS=

# 日志配置
LOG_CHANNEL=daily
LOG_LEVEL=info
LOG_MAX_FILES=30

# 监控配置
MONITOR_ENABLED=true
MONITOR_ALERT_EMAIL=<EMAIL>

# 客服配置
CUSTOMER_SERVICE_ENABLED=true
CUSTOMER_SERVICE_HOURS=9:00-21:00
CUSTOMER_SERVICE_EMAIL=<EMAIL>
CUSTOMER_SERVICE_PHONE=************

# 系统配置
SYSTEM_NAME=GVD交易平台
SYSTEM_VERSION=1.0.0
SYSTEM_TIMEZONE=Asia/Shanghai
SYSTEM_LOCALE=zh-CN

# 开发配置
DEVELOPER_MODE=true
DEBUG_TOOLBAR=true
SQL_DEBUG=true
