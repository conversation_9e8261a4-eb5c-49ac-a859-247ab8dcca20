/* 用户中心样式 - 现代化设计 */

/* ==================== 用户中心布局 ==================== */
.user-layout {
  display: flex;
  min-height: 100vh;
  background: var(--bg-primary);
}

.user-sidebar {
  width: 280px;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  padding: 20px 0;
  overflow-y: auto;
}

.user-main {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* ==================== 用户资料 ==================== */
.user-profile {
  display: flex;
  align-items: center;
  padding: 0 20px 20px;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 20px;
}

.avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 16px;
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info {
  flex: 1;
}

.username {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.user-level {
  font-size: 12px;
  color: var(--color-primary);
  background: rgba(240, 185, 11, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
  margin: 0;
}

/* ==================== 导航菜单 ==================== */
.user-nav {
  padding: 0 10px;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 4px;
  border-radius: var(--border-radius-lg);
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.2s ease;
  position: relative;
}

.nav-item:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.nav-item.active {
  background: rgba(240, 185, 11, 0.1);
  color: var(--color-primary);
}

.nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: var(--color-primary);
  border-radius: 0 2px 2px 0;
}

.nav-icon {
  font-size: 18px;
  margin-right: 12px;
  width: 20px;
  text-align: center;
}

.nav-text {
  font-size: 14px;
  font-weight: 500;
}

/* ==================== 页面头部 ==================== */
.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.page-header p {
  margin: 0;
  font-size: 14px;
}

/* ==================== 统计卡片 ==================== */
.stat-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 20px;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-right: 16px;
}

.stat-icon.bg-primary {
  background: rgba(240, 185, 11, 0.1);
  color: var(--color-primary);
}

.stat-icon.bg-success {
  background: rgba(14, 203, 129, 0.1);
  color: var(--color-success);
}

.stat-icon.bg-info {
  background: rgba(24, 144, 255, 0.1);
  color: var(--color-info);
}

.stat-icon.bg-warning {
  background: rgba(255, 143, 0, 0.1);
  color: var(--color-warning);
}

.stat-icon.bg-danger {
  background: rgba(246, 70, 93, 0.1);
  color: var(--color-danger);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
  font-family: 'Monaco', 'Menlo', monospace;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0;
}

/* ==================== 快速操作 ==================== */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.quick-action {
  display: flex;
  align-items: center;
  padding: 16px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  text-decoration: none;
  transition: all 0.2s ease;
}

.quick-action:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin-right: 12px;
}

.action-content h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 2px 0;
}

.action-content p {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0;
}

/* ==================== 资产列表 ==================== */
.asset-list {
  max-height: 300px;
  overflow-y: auto;
}

.asset-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
}

.asset-item:last-child {
  border-bottom: none;
}

.asset-info {
  display: flex;
  align-items: center;
}

.asset-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: #000;
  margin-right: 12px;
}

.asset-details h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 2px 0;
}

.asset-details p {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0;
}

.asset-balance {
  text-align: right;
}

.balance-amount {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  font-family: 'Monaco', 'Menlo', monospace;
}

.balance-value {
  font-size: 12px;
  color: var(--text-secondary);
  font-family: 'Monaco', 'Menlo', monospace;
}

/* ==================== 最近交易 ==================== */
.recent-trades {
  max-height: 300px;
  overflow-y: auto;
}

.trade-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
}

.trade-item:last-child {
  border-bottom: none;
}

.trade-info h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 2px 0;
}

.trade-info p {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0;
}

.trade-result {
  text-align: right;
}

.trade-amount {
  font-size: 14px;
  font-weight: 600;
  font-family: 'Monaco', 'Menlo', monospace;
}

.trade-time {
  font-size: 12px;
  color: var(--text-secondary);
}

/* ==================== 通知列表 ==================== */
.notification-list,
.announcement-list {
  max-height: 250px;
  overflow-y: auto;
}

.notification-item,
.announcement-item {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s ease;
}

.notification-item:hover,
.announcement-item:hover {
  background: var(--bg-hover);
}

.notification-item:last-child,
.announcement-item:last-child {
  border-bottom: none;
}

.notification-item.unread {
  background: rgba(240, 185, 11, 0.05);
  border-left: 3px solid var(--color-primary);
}

.notification-title,
.announcement-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-content,
.announcement-content {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0 0 4px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-time,
.announcement-time {
  font-size: 11px;
  color: var(--text-tertiary);
}

/* ==================== 空状态 ==================== */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  color: var(--text-secondary);
  font-size: 14px;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 992px) {
  .user-layout {
    flex-direction: column;
  }
  
  .user-sidebar {
    width: 100%;
    padding: 16px 0;
  }
  
  .user-profile {
    padding: 0 16px 16px;
  }
  
  .user-nav {
    display: flex;
    overflow-x: auto;
    padding: 0 16px;
  }
  
  .nav-item {
    flex-shrink: 0;
    margin-right: 8px;
    margin-bottom: 0;
  }
  
  .user-main {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .quick-actions {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stat-card {
    padding: 16px;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
  
  .stat-value {
    font-size: 20px;
  }
}

@media (max-width: 576px) {
  .quick-actions {
    grid-template-columns: 1fr;
  }
  
  .page-header h1 {
    font-size: 24px;
  }
}
