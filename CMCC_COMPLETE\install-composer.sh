#!/bin/bash

# GVD交易平台 - Composer依赖安装脚本
# 用于自动安装所需的PHP依赖包

echo "==================================="
echo "GVD交易平台 - 依赖安装脚本"
echo "==================================="

# 检查PHP是否安装
if ! command -v php &> /dev/null; then
    echo "错误: PHP未安装或不在PATH中"
    exit 1
fi

echo "PHP版本: $(php -v | head -n 1)"

# 检查Composer是否安装
if ! command -v composer &> /dev/null; then
    echo "Composer未安装，正在下载安装..."
    
    # 下载Composer安装程序
    php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
    
    # 验证安装程序
    HASH="$(wget -q -O - https://composer.github.io/installer.sig)"
    php -r "if (hash_file('SHA384', 'composer-setup.php') === '$HASH') { echo 'Installer verified'; } else { echo 'Installer corrupt'; unlink('composer-setup.php'); } echo PHP_EOL;"
    
    # 安装Composer
    php composer-setup.php --install-dir=/usr/local/bin --filename=composer
    php -r "unlink('composer-setup.php');"
    
    echo "Composer安装完成"
else
    echo "Composer版本: $(composer --version)"
fi

# 安装项目依赖
echo "正在安装项目依赖..."
composer install --no-dev --optimize-autoloader

# 检查安装结果
if [ $? -eq 0 ]; then
    echo "==================================="
    echo "依赖安装成功！"
    echo "==================================="
    echo "接下来请："
    echo "1. 配置 .env 文件"
    echo "2. 导入数据库"
    echo "3. 启动WebSocket服务"
    echo "4. 配置Web服务器"
    echo "==================================="
else
    echo "==================================="
    echo "依赖安装失败！"
    echo "请检查网络连接和PHP配置"
    echo "==================================="
    exit 1
fi
