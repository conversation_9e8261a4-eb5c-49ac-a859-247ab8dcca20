<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 合约订单模型
 */
class ContractOrder extends Model
{
    protected $name = 'gvd_contract_orders';
    
    // 设置字段信息
    protected $schema = [
        'id'            => 'int',
        'user_id'       => 'int',
        'coin_name'     => 'string',
        'direction'     => 'int',
        'amount'        => 'decimal',
        'buy_price'     => 'decimal',
        'settle_price'  => 'decimal',
        'time_length'   => 'int',
        'end_time'      => 'datetime',
        'is_win'        => 'int',
        'profit_loss'   => 'decimal',
        'status'        => 'int',
        'settle_time'   => 'datetime',
        'created_at'    => 'datetime',
        'updated_at'    => 'datetime'
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 状态常量
    const STATUS_ACTIVE = 1;    // 进行中
    const STATUS_SETTLED = 2;   // 已结算
    const STATUS_CANCELLED = 3; // 已取消
    
    // 方向常量
    const DIRECTION_UP = 1;     // 买涨
    const DIRECTION_DOWN = 2;   // 买跌
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            self::STATUS_ACTIVE => '进行中',
            self::STATUS_SETTLED => '已结算',
            self::STATUS_CANCELLED => '已取消'
        ];
        return $status[$data['status']] ?? '未知';
    }
    
    /**
     * 获取方向文本
     */
    public function getDirectionTextAttr($value, $data)
    {
        $direction = [
            self::DIRECTION_UP => '买涨',
            self::DIRECTION_DOWN => '买跌'
        ];
        return $direction[$data['direction']] ?? '未知';
    }
    
    /**
     * 获取盈亏状态文本
     */
    public function getWinStatusTextAttr($value, $data)
    {
        if ($data['status'] != self::STATUS_SETTLED) {
            return '进行中';
        }
        
        return $data['is_win'] ? '盈利' : '亏损';
    }
    
    /**
     * 获取剩余时间（秒）
     */
    public function getRemainingTimeAttr($value, $data)
    {
        if ($data['status'] != self::STATUS_ACTIVE) {
            return 0;
        }
        
        $endTime = strtotime($data['end_time']);
        $remaining = $endTime - time();
        
        return max(0, $remaining);
    }
    
    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    
    /**
     * 获取用户的活跃合约
     */
    public static function getActiveByUser(int $userId)
    {
        return self::where('user_id', $userId)
                  ->where('status', self::STATUS_ACTIVE)
                  ->order('id desc')
                  ->select();
    }
    
    /**
     * 获取用户的合约历史
     */
    public static function getHistoryByUser(int $userId, int $page = 1, int $limit = 20)
    {
        return self::where('user_id', $userId)
                  ->order('id desc')
                  ->paginate([
                      'list_rows' => $limit,
                      'page' => $page
                  ]);
    }
    
    /**
     * 获取到期的合约
     */
    public static function getExpiredContracts()
    {
        return self::where('status', self::STATUS_ACTIVE)
                  ->where('end_time', '<=', date('Y-m-d H:i:s'))
                  ->select();
    }
    
    /**
     * 检查合约是否到期
     */
    public function isExpired(): bool
    {
        return $this->status == self::STATUS_ACTIVE && 
               strtotime($this->end_time) <= time();
    }
    
    /**
     * 检查合约是否盈利
     */
    public function isWin(): bool
    {
        return $this->status == self::STATUS_SETTLED && $this->is_win == 1;
    }
    
    /**
     * 获取合约统计信息
     */
    public static function getStats(int $userId): array
    {
        $total = self::where('user_id', $userId)->count();
        $win = self::where('user_id', $userId)
                  ->where('status', self::STATUS_SETTLED)
                  ->where('is_win', 1)
                  ->count();
        $lose = self::where('user_id', $userId)
                   ->where('status', self::STATUS_SETTLED)
                   ->where('is_win', 0)
                   ->count();
        
        $totalProfit = self::where('user_id', $userId)
                          ->where('status', self::STATUS_SETTLED)
                          ->sum('profit_loss');
        
        $winRate = $total > 0 ? round(($win / $total) * 100, 2) : 0;
        
        return [
            'total' => $total,
            'win' => $win,
            'lose' => $lose,
            'win_rate' => $winRate,
            'total_profit' => $totalProfit
        ];
    }
}
