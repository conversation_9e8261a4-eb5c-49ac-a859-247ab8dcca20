<?php
/**
 * GVD交易平台安装脚本
 * 版本: 2.0.0
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

class GVDInstaller
{
    private $requirements = [
        'php_version' => '8.1.0',
        'extensions' => [
            'pdo', 'pdo_mysql', 'mbstring', 'openssl', 
            'tokenizer', 'xml', 'ctype', 'json', 'bcmath',
            'curl', 'fileinfo', 'gd', 'redis'
        ],
        'functions' => [
            'exec', 'shell_exec', 'proc_open'
        ]
    ];

    public function run()
    {
        echo "<!DOCTYPE html>\n";
        echo "<html lang='zh-CN'>\n";
        echo "<head>\n";
        echo "    <meta charset='UTF-8'>\n";
        echo "    <meta name='viewport' content='width=device-width, initial-scale=1.0'>\n";
        echo "    <title>GVD交易平台安装向导</title>\n";
        echo "    <style>\n";
        echo "        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; background: #f5f5f5; }\n";
        echo "        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n";
        echo "        h1 { color: #333; text-align: center; margin-bottom: 30px; }\n";
        echo "        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #007cba; background: #f9f9f9; }\n";
        echo "        .success { border-left-color: #28a745; background: #d4edda; }\n";
        echo "        .error { border-left-color: #dc3545; background: #f8d7da; }\n";
        echo "        .warning { border-left-color: #ffc107; background: #fff3cd; }\n";
        echo "        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }\n";
        echo "        .btn { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 10px 5px; }\n";
        echo "        .btn:hover { background: #0056b3; }\n";
        echo "        ul { margin: 10px 0; padding-left: 20px; }\n";
        echo "    </style>\n";
        echo "</head>\n";
        echo "<body>\n";
        echo "    <div class='container'>\n";
        echo "        <h1>🚀 GVD数字货币交易平台安装向导</h1>\n";

        $this->checkEnvironment();
        $this->showInstallationSteps();

        echo "    </div>\n";
        echo "</body>\n";
        echo "</html>\n";
    }

    private function checkEnvironment()
    {
        echo "<div class='step'>\n";
        echo "<h2>📋 环境检查</h2>\n";

        // 检查PHP版本
        $phpVersion = PHP_VERSION;
        if (version_compare($phpVersion, $this->requirements['php_version'], '>=')) {
            echo "<p class='success'>✅ PHP版本: {$phpVersion} (要求: {$this->requirements['php_version']}+)</p>\n";
        } else {
            echo "<p class='error'>❌ PHP版本: {$phpVersion} (要求: {$this->requirements['php_version']}+)</p>\n";
        }

        // 检查扩展
        echo "<h3>PHP扩展检查:</h3>\n";
        echo "<ul>\n";
        foreach ($this->requirements['extensions'] as $ext) {
            if (extension_loaded($ext)) {
                echo "<li class='success'>✅ {$ext}</li>\n";
            } else {
                echo "<li class='error'>❌ {$ext} (未安装)</li>\n";
            }
        }
        echo "</ul>\n";

        // 检查函数
        echo "<h3>系统函数检查:</h3>\n";
        echo "<ul>\n";
        foreach ($this->requirements['functions'] as $func) {
            if (function_exists($func)) {
                echo "<li class='success'>✅ {$func}</li>\n";
            } else {
                echo "<li class='warning'>⚠️ {$func} (可能被禁用)</li>\n";
            }
        }
        echo "</ul>\n";

        // 检查目录权限
        echo "<h3>目录权限检查:</h3>\n";
        $directories = ['runtime', 'public/uploads'];
        echo "<ul>\n";
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
            if (is_writable($dir)) {
                echo "<li class='success'>✅ {$dir} (可写)</li>\n";
            } else {
                echo "<li class='error'>❌ {$dir} (不可写)</li>\n";
            }
        }
        echo "</ul>\n";

        echo "</div>\n";
    }

    private function showInstallationSteps()
    {
        echo "<div class='step'>\n";
        echo "<h2>📦 安装步骤</h2>\n";

        echo "<h3>1. 安装Composer依赖</h3>\n";
        echo "<div class='code'>composer install --no-dev --optimize-autoloader</div>\n";

        echo "<h3>2. 配置环境文件</h3>\n";
        echo "<p>编辑 <code>.env</code> 文件，配置数据库连接信息：</p>\n";
        echo "<div class='code'>\n";
        echo "DATABASE_HOSTNAME = 127.0.0.1<br>\n";
        echo "DATABASE_DATABASE = gvd_trading<br>\n";
        echo "DATABASE_USERNAME = root<br>\n";
        echo "DATABASE_PASSWORD = your_password<br>\n";
        echo "</div>\n";

        echo "<h3>3. 创建数据库</h3>\n";
        echo "<div class='code'>mysql -u root -p -e \"CREATE DATABASE gvd_trading CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\"</div>\n";

        echo "<h3>4. 导入数据库结构</h3>\n";
        echo "<div class='code'>mysql -u root -p gvd_trading < database/gvd_trading.sql</div>\n";

        echo "<h3>5. 设置目录权限</h3>\n";
        echo "<div class='code'>\n";
        echo "chmod -R 755 .<br>\n";
        echo "chmod -R 777 runtime/<br>\n";
        echo "chmod -R 777 public/uploads/<br>\n";
        echo "</div>\n";

        echo "<h3>6. 配置Web服务器</h3>\n";
        echo "<p><strong>Nginx配置示例:</strong></p>\n";
        echo "<div class='code'>\n";
        echo "server {<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;listen 80;<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;server_name your-domain.com;<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;root /path/to/CMCC_COMPLETE/public;<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;index index.php;<br>\n";
        echo "<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;location / {<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;try_files \$uri \$uri/ /index.php?\$query_string;<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;}<br>\n";
        echo "<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;location ~ \\.php\$ {<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;fastcgi_pass 127.0.0.1:9000;<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;fastcgi_index index.php;<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;fastcgi_param SCRIPT_FILENAME \$document_root\$fastcgi_script_name;<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;include fastcgi_params;<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;}<br>\n";
        echo "}<br>\n";
        echo "</div>\n";

        echo "<h3>7. 启动定时任务</h3>\n";
        echo "<p>添加到crontab:</p>\n";
        echo "<div class='code'>\n";
        echo "# 每分钟执行交易结算<br>\n";
        echo "* * * * * cd /path/to/CMCC_COMPLETE && php think trading:settle<br>\n";
        echo "<br>\n";
        echo "# 每分钟更新价格<br>\n";
        echo "* * * * * cd /path/to/CMCC_COMPLETE && php think trading:price-update<br>\n";
        echo "</div>\n";

        echo "<h3>8. 启动WebSocket服务</h3>\n";
        echo "<div class='code'>php think websocket:start</div>\n";

        echo "</div>\n";

        echo "<div class='step success'>\n";
        echo "<h2>🎉 安装完成</h2>\n";
        echo "<p>恭喜！GVD交易平台安装完成。</p>\n";
        echo "<p><strong>默认管理员账号:</strong></p>\n";
        echo "<ul>\n";
        echo "<li>用户名: admin</li>\n";
        echo "<li>密码: admin123</li>\n";
        echo "</ul>\n";
        echo "<p><strong>访问地址:</strong></p>\n";
        echo "<ul>\n";
        echo "<li>前台: <a href='/' target='_blank'>http://your-domain.com/</a></li>\n";
        echo "<li>管理后台: <a href='/admin' target='_blank'>http://your-domain.com/admin</a></li>\n";
        echo "<li>代理后台: <a href='/agent' target='_blank'>http://your-domain.com/agent</a></li>\n";
        echo "</ul>\n";
        echo "<p class='warning'>⚠️ 请及时修改默认密码并删除此安装文件！</p>\n";
        echo "</div>\n";

        echo "<div class='step'>\n";
        echo "<h2>📚 相关文档</h2>\n";
        echo "<ul>\n";
        echo "<li><a href='README.md' target='_blank'>使用说明</a></li>\n";
        echo "<li><a href='database/gvd_trading.sql' target='_blank'>数据库结构</a></li>\n";
        echo "<li><a href='config/' target='_blank'>配置文件</a></li>\n";
        echo "</ul>\n";
        echo "</div>\n";
    }
}

// 运行安装程序
$installer = new GVDInstaller();
$installer->run();
?>
