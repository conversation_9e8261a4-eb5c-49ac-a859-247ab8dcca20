<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 现货订单模型
 */
class SpotOrder extends Model
{
    protected $name = 'gvd_spot_orders';
    
    protected $type = [
        'price' => 'float',
        'amount' => 'float',
        'total' => 'float',
        'filled_amount' => 'float',
        'filled_total' => 'float',
        'avg_price' => 'float',
        'fee' => 'float'
    ];

    // 订单类型
    const TYPE_BUY = 1;   // 买入
    const TYPE_SELL = 2;  // 卖出

    // 订单方式
    const ORDER_TYPE_LIMIT = 1;  // 限价单
    const ORDER_TYPE_MARKET = 2; // 市价单

    // 订单状态
    const STATUS_PENDING = 1;    // 待成交
    const STATUS_PARTIAL = 2;    // 部分成交
    const STATUS_FILLED = 3;     // 完全成交
    const STATUS_CANCELLED = 4;  // 已取消

    /**
     * 生成订单号
     */
    public static function generateOrderId(): string
    {
        return 'SO' . date('YmdHis') . mt_rand(1000, 9999);
    }

    /**
     * 获取订单类型文本
     */
    public function getTypeTextAttr($value, $data): string
    {
        $types = [
            self::TYPE_BUY => '买入',
            self::TYPE_SELL => '卖出'
        ];
        return $types[$data['type']] ?? '未知';
    }

    /**
     * 获取订单方式文本
     */
    public function getOrderTypeTextAttr($value, $data): string
    {
        $types = [
            self::ORDER_TYPE_LIMIT => '限价单',
            self::ORDER_TYPE_MARKET => '市价单'
        ];
        return $types[$data['order_type']] ?? '未知';
    }

    /**
     * 获取订单状态文本
     */
    public function getStatusTextAttr($value, $data): string
    {
        $statuses = [
            self::STATUS_PENDING => '待成交',
            self::STATUS_PARTIAL => '部分成交',
            self::STATUS_FILLED => '完全成交',
            self::STATUS_CANCELLED => '已取消'
        ];
        return $statuses[$data['status']] ?? '未知';
    }

    /**
     * 部分成交
     */
    public function partialFill(float $fillAmount, float $fillPrice): void
    {
        $this->filled_amount += $fillAmount;
        $this->filled_total += $fillAmount * $fillPrice;
        
        // 计算平均成交价
        if ($this->filled_amount > 0) {
            $this->avg_price = $this->filled_total / $this->filled_amount;
        }

        // 更新状态
        if ($this->filled_amount >= $this->amount) {
            $this->status = self::STATUS_FILLED;
            $this->filled_at = date('Y-m-d H:i:s');
        } else {
            $this->status = self::STATUS_PARTIAL;
        }

        $this->updated_at = date('Y-m-d H:i:s');
        $this->save();
    }

    /**
     * 取消订单
     */
    public function cancel(): bool
    {
        if ($this->status == self::STATUS_FILLED) {
            return false; // 已成交的订单不能取消
        }

        $this->status = self::STATUS_CANCELLED;
        $this->updated_at = date('Y-m-d H:i:s');
        
        return $this->save();
    }

    /**
     * 获取用户的活跃订单
     */
    public static function getUserActiveOrders(int $userId, string $symbol = ''): array
    {
        $query = self::where('user_id', $userId)
                    ->where('status', 'in', [self::STATUS_PENDING, self::STATUS_PARTIAL]);
        
        if (!empty($symbol)) {
            $query->where('symbol', $symbol);
        }
        
        return $query->order('created_at', 'desc')
                    ->select()
                    ->toArray();
    }

    /**
     * 获取用户的历史订单
     */
    public static function getUserHistoryOrders(int $userId, string $symbol = '', int $page = 1, int $limit = 20): array
    {
        $query = self::where('user_id', $userId);
        
        if (!empty($symbol)) {
            $query->where('symbol', $symbol);
        }
        
        $orders = $query->order('created_at', 'desc')
                       ->paginate([
                           'list_rows' => $limit,
                           'page' => $page
                       ]);
        
        return [
            'data' => $orders->items(),
            'total' => $orders->total(),
            'page' => $page,
            'limit' => $limit
        ];
    }

    /**
     * 获取可撮合的卖单
     */
    public static function getMatchableSellOrders(string $symbol, float $buyPrice, int $limit = 10): array
    {
        return self::where('symbol', $symbol)
                  ->where('type', self::TYPE_SELL)
                  ->where('status', 'in', [self::STATUS_PENDING, self::STATUS_PARTIAL])
                  ->where('price', '<=', $buyPrice)
                  ->order('price', 'asc')
                  ->order('created_at', 'asc')
                  ->limit($limit)
                  ->select()
                  ->toArray();
    }

    /**
     * 获取可撮合的买单
     */
    public static function getMatchableBuyOrders(string $symbol, float $sellPrice, int $limit = 10): array
    {
        return self::where('symbol', $symbol)
                  ->where('type', self::TYPE_BUY)
                  ->where('status', 'in', [self::STATUS_PENDING, self::STATUS_PARTIAL])
                  ->where('price', '>=', $sellPrice)
                  ->order('price', 'desc')
                  ->order('created_at', 'asc')
                  ->limit($limit)
                  ->select()
                  ->toArray();
    }

    /**
     * 获取订单簿
     */
    public static function getOrderBook(string $symbol, int $depth = 20): array
    {
        // 获取买单（按价格降序）
        $buyOrders = self::where('symbol', $symbol)
                        ->where('type', self::TYPE_BUY)
                        ->where('status', 'in', [self::STATUS_PENDING, self::STATUS_PARTIAL])
                        ->field('price, SUM(amount - filled_amount) as total_amount')
                        ->group('price')
                        ->order('price', 'desc')
                        ->limit($depth)
                        ->select()
                        ->toArray();

        // 获取卖单（按价格升序）
        $sellOrders = self::where('symbol', $symbol)
                         ->where('type', self::TYPE_SELL)
                         ->where('status', 'in', [self::STATUS_PENDING, self::STATUS_PARTIAL])
                         ->field('price, SUM(amount - filled_amount) as total_amount')
                         ->group('price')
                         ->order('price', 'asc')
                         ->limit($depth)
                         ->select()
                         ->toArray();

        $bids = [];
        foreach ($buyOrders as $order) {
            $bids[] = [
                'price' => (float)$order['price'],
                'amount' => (float)$order['total_amount'],
                'total' => (float)($order['price'] * $order['total_amount'])
            ];
        }

        $asks = [];
        foreach ($sellOrders as $order) {
            $asks[] = [
                'price' => (float)$order['price'],
                'amount' => (float)$order['total_amount'],
                'total' => (float)($order['price'] * $order['total_amount'])
            ];
        }

        return [
            'symbol' => $symbol,
            'bids' => $bids,
            'asks' => $asks,
            'timestamp' => time()
        ];
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联交易对
     */
    public function tradingPair()
    {
        return $this->belongsTo(TradingPair::class, 'symbol', 'symbol');
    }

    /**
     * 获取订单统计
     */
    public static function getOrderStats(int $userId): array
    {
        $stats = self::where('user_id', $userId)
                    ->field([
                        'COUNT(*) as total_orders',
                        'SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as filled_orders',
                        'SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as cancelled_orders',
                        'SUM(CASE WHEN type = 1 THEN filled_total ELSE 0 END) as buy_volume',
                        'SUM(CASE WHEN type = 2 THEN filled_total ELSE 0 END) as sell_volume',
                        'SUM(fee) as total_fee'
                    ])
                    ->find();

        if (!$stats) {
            return [
                'total_orders' => 0,
                'filled_orders' => 0,
                'cancelled_orders' => 0,
                'fill_rate' => 0,
                'buy_volume' => 0,
                'sell_volume' => 0,
                'total_volume' => 0,
                'total_fee' => 0
            ];
        }

        $fillRate = $stats['total_orders'] > 0 ? ($stats['filled_orders'] / $stats['total_orders']) * 100 : 0;
        $totalVolume = $stats['buy_volume'] + $stats['sell_volume'];

        return [
            'total_orders' => $stats['total_orders'],
            'filled_orders' => $stats['filled_orders'],
            'cancelled_orders' => $stats['cancelled_orders'],
            'fill_rate' => round($fillRate, 2),
            'buy_volume' => $stats['buy_volume'],
            'sell_volume' => $stats['sell_volume'],
            'total_volume' => $totalVolume,
            'total_fee' => $stats['total_fee']
        ];
    }
}
