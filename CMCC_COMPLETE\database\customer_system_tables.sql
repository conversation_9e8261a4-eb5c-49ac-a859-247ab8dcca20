-- 客服系统相关表结构
-- 需要添加到主数据库中

-- 客服会话表
CREATE TABLE `gvd_customer_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(32) NOT NULL COMMENT '会话ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `agent_id` int(11) DEFAULT NULL COMMENT '代理ID',
  `admin_id` int(11) DEFAULT NULL COMMENT '客服管理员ID',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态:active,closed,waiting',
  `priority` tinyint(1) NOT NULL DEFAULT '1' COMMENT '优先级:1=普通,2=重要,3=紧急',
  `category` varchar(50) DEFAULT NULL COMMENT '问题分类',
  `title` varchar(255) DEFAULT NULL COMMENT '会话标题',
  `last_message_time` datetime DEFAULT NULL COMMENT '最后消息时间',
  `last_message_content` text COMMENT '最后消息内容',
  `unread_count_user` int(11) NOT NULL DEFAULT '0' COMMENT '用户未读消息数',
  `unread_count_admin` int(11) NOT NULL DEFAULT '0' COMMENT '客服未读消息数',
  `unread_count_agent` int(11) NOT NULL DEFAULT '0' COMMENT '代理未读消息数',
  `satisfaction_score` tinyint(1) DEFAULT NULL COMMENT '满意度评分:1-5',
  `satisfaction_comment` text COMMENT '满意度评价',
  `closed_at` datetime DEFAULT NULL COMMENT '关闭时间',
  `closed_by` int(11) DEFAULT NULL COMMENT '关闭人ID',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服会话表';

-- 客服消息表
CREATE TABLE `gvd_customer_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(32) NOT NULL COMMENT '会话ID',
  `sender_id` int(11) NOT NULL COMMENT '发送者ID',
  `sender_type` varchar(20) NOT NULL COMMENT '发送者类型:user,admin,agent,system',
  `receiver_id` int(11) DEFAULT NULL COMMENT '接收者ID',
  `receiver_type` varchar(20) DEFAULT NULL COMMENT '接收者类型:user,admin,agent',
  `message_type` varchar(20) NOT NULL DEFAULT 'text' COMMENT '消息类型:text,image,file,system',
  `content` text NOT NULL COMMENT '消息内容',
  `file_url` varchar(500) DEFAULT NULL COMMENT '文件URL',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名',
  `file_size` int(11) DEFAULT NULL COMMENT '文件大小',
  `is_read` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `read_at` datetime DEFAULT NULL COMMENT '阅读时间',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_sender` (`sender_id`,`sender_type`),
  KEY `idx_receiver` (`receiver_id`,`receiver_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服消息表';

-- 客服快捷回复表
CREATE TABLE `gvd_customer_quick_replies` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category` varchar(50) NOT NULL COMMENT '分类',
  `title` varchar(100) NOT NULL COMMENT '标题',
  `content` text NOT NULL COMMENT '回复内容',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `usage_count` int(11) NOT NULL DEFAULT '0' COMMENT '使用次数',
  `created_by` int(11) DEFAULT NULL COMMENT '创建人ID',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_active` (`is_active`),
  KEY `idx_sort` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服快捷回复表';

-- 客服在线状态表
CREATE TABLE `gvd_customer_online_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `user_type` varchar(20) NOT NULL COMMENT '用户类型:user,admin,agent',
  `status` varchar(20) NOT NULL DEFAULT 'online' COMMENT '状态:online,offline,busy,away',
  `last_activity` datetime NOT NULL COMMENT '最后活动时间',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `socket_id` varchar(100) DEFAULT NULL COMMENT 'Socket连接ID',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user` (`user_id`,`user_type`),
  KEY `idx_status` (`status`),
  KEY `idx_last_activity` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服在线状态表';

-- 客服工单表
CREATE TABLE `gvd_customer_tickets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ticket_no` varchar(20) NOT NULL COMMENT '工单号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `category` varchar(50) NOT NULL COMMENT '问题分类',
  `subject` varchar(255) NOT NULL COMMENT '主题',
  `content` text NOT NULL COMMENT '问题描述',
  `priority` tinyint(1) NOT NULL DEFAULT '1' COMMENT '优先级:1=普通,2=重要,3=紧急',
  `status` varchar(20) NOT NULL DEFAULT 'open' COMMENT '状态:open,processing,resolved,closed',
  `assigned_to` int(11) DEFAULT NULL COMMENT '分配给客服ID',
  `resolution` text COMMENT '解决方案',
  `satisfaction_score` tinyint(1) DEFAULT NULL COMMENT '满意度评分',
  `satisfaction_comment` text COMMENT '满意度评价',
  `resolved_at` datetime DEFAULT NULL COMMENT '解决时间',
  `closed_at` datetime DEFAULT NULL COMMENT '关闭时间',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_ticket_no` (`ticket_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_assigned_to` (`assigned_to`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服工单表';

-- 插入默认快捷回复
INSERT INTO `gvd_customer_quick_replies` (`category`, `title`, `content`, `sort_order`, `created_at`) VALUES
('general', '欢迎语', '您好！欢迎使用我们的客服服务，我是您的专属客服，有什么可以帮助您的吗？', 1, NOW()),
('general', '稍等回复', '好的，请稍等，我马上为您查询处理。', 2, NOW()),
('general', '感谢语', '感谢您的咨询，如果还有其他问题，随时联系我们。', 3, NOW()),
('account', '账户安全', '为了您的账户安全，请不要向任何人透露您的密码和验证码。', 1, NOW()),
('account', '实名认证', '请完成实名认证以提高账户安全等级和交易限额。', 2, NOW()),
('trading', '交易规则', '请仔细阅读交易规则，合理控制风险，理性投资。', 1, NOW()),
('trading', '手续费说明', '交易手续费根据您的VIP等级而定，等级越高手续费越低。', 2, NOW()),
('deposit', '充值到账', '充值一般在1-3个区块确认后到账，请耐心等待。', 1, NOW()),
('withdraw', '提现审核', '提现申请已提交，我们会在24小时内完成审核。', 1, NOW());
