<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理商管理后台 - GVD交易平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .container {
            max-width: 1400px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 30px;
            color: #333;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .stat-value {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .stat-change {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 12px;
        }

        .stat-change.positive {
            background: rgba(46, 204, 113, 0.2);
            color: #27ae60;
        }

        .stat-change.negative {
            background: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .card-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .card-body {
            padding: 20px;
        }

        .chart-container {
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 8px;
            color: #666;
        }

        .team-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .team-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .team-item:last-child {
            border-bottom: none;
        }

        .team-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .team-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .team-details h4 {
            margin-bottom: 4px;
            color: #333;
        }

        .team-details p {
            color: #666;
            font-size: 12px;
        }

        .team-stats {
            text-align: right;
        }

        .team-stats .amount {
            font-weight: bold;
            color: #667eea;
        }

        .team-stats .count {
            font-size: 12px;
            color: #666;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .action-btn {
            padding: 15px;
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            text-decoration: none;
            color: #333;
            text-align: center;
            transition: all 0.3s;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .action-btn:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .action-icon {
            width: 32px;
            height: 32px;
            background: #667eea;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .recent-activities {
            max-height: 300px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            flex-shrink: 0;
        }

        .activity-icon.register {
            background: rgba(46, 204, 113, 0.2);
            color: #27ae60;
        }

        .activity-icon.trade {
            background: rgba(52, 152, 219, 0.2);
            color: #3498db;
        }

        .activity-icon.deposit {
            background: rgba(243, 156, 18, 0.2);
            color: #f39c12;
        }

        .activity-content {
            flex: 1;
        }

        .activity-content h5 {
            margin-bottom: 4px;
            color: #333;
        }

        .activity-content p {
            color: #666;
            font-size: 12px;
        }

        .activity-time {
            color: #999;
            font-size: 11px;
        }

        .nav-tabs {
            display: flex;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 20px;
        }

        .nav-tab {
            padding: 12px 20px;
            background: none;
            border: none;
            cursor: pointer;
            color: #666;
            font-weight: 500;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .nav-tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        @media (max-width: 1200px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .quick-actions {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">GVD代理商后台</div>
            <div class="user-info">
                <span id="agentName">代理商</span>
                <button class="btn btn-outline" onclick="logout()">退出</button>
            </div>
        </div>
    </div>

    <div class="container">
        <h1 class="page-title">代理商仪表板</h1>
        
        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <div class="stat-value" id="totalUsers">0</div>
                <div class="stat-label">团队总人数</div>
                <div class="stat-change positive" id="userChange">+0</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalCommission">$0</div>
                <div class="stat-label">总佣金收入</div>
                <div class="stat-change positive" id="commissionChange">+0%</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="monthlyCommission">$0</div>
                <div class="stat-label">本月佣金</div>
                <div class="stat-change positive" id="monthlyChange">+0%</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="activeUsers">0</div>
                <div class="stat-label">活跃用户</div>
                <div class="stat-change positive" id="activeChange">+0%</div>
            </div>
        </div>

        <div class="content-grid">
            <div class="main-content">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">佣金收入趋势</h3>
                    </div>
                    <div class="card-body">
                        <div class="nav-tabs">
                            <button class="nav-tab active" onclick="switchChart('daily')">日收入</button>
                            <button class="nav-tab" onclick="switchChart('weekly')">周收入</button>
                            <button class="nav-tab" onclick="switchChart('monthly')">月收入</button>
                        </div>
                        <div class="chart-container" id="commissionChart">
                            📊 佣金收入图表
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">团队成员</h3>
                    </div>
                    <div class="card-body">
                        <div class="team-list" id="teamList">
                            <!-- 团队成员列表将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>

            <div class="sidebar">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">快捷操作</h3>
                    </div>
                    <div class="card-body">
                        <div class="quick-actions">
                            <a href="/agent/invite.html" class="action-btn">
                                <div class="action-icon">👥</div>
                                <span>邀请用户</span>
                            </a>
                            <a href="/agent/commission.html" class="action-btn">
                                <div class="action-icon">💰</div>
                                <span>佣金明细</span>
                            </a>
                            <a href="/agent/team.html" class="action-btn">
                                <div class="action-icon">📊</div>
                                <span>团队数据</span>
                            </a>
                            <a href="/agent/tools.html" class="action-btn">
                                <div class="action-icon">🔧</div>
                                <span>推广工具</span>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">最近活动</h3>
                    </div>
                    <div class="card-body">
                        <div class="recent-activities" id="recentActivities">
                            <!-- 最近活动将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class AgentDashboard {
            constructor() {
                this.token = localStorage.getItem('agent_token');
                this.agentInfo = JSON.parse(localStorage.getItem('agent_info') || '{}');
                
                if (!this.token) {
                    window.location.href = '/agent/login.html';
                    return;
                }
                
                this.init();
            }
            
            async init() {
                this.updateAgentInfo();
                await this.loadDashboardData();
                this.bindEvents();
            }
            
            updateAgentInfo() {
                if (this.agentInfo.username) {
                    document.getElementById('agentName').textContent = this.agentInfo.username;
                }
            }
            
            async loadDashboardData() {
                try {
                    const response = await this.apiRequest('/api/agent/dashboard');
                    if (response.code === 1) {
                        this.updateStats(response.data.stats);
                        this.updateTeamList(response.data.team_members);
                        this.updateRecentActivities(response.data.recent_activities);
                    }
                } catch (error) {
                    console.error('加载仪表板数据失败:', error);
                }
            }
            
            updateStats(stats) {
                document.getElementById('totalUsers').textContent = stats.total_users || 0;
                document.getElementById('totalCommission').textContent = `$${this.formatNumber(stats.total_commission || 0)}`;
                document.getElementById('monthlyCommission').textContent = `$${this.formatNumber(stats.monthly_commission || 0)}`;
                document.getElementById('activeUsers').textContent = stats.active_users || 0;
                
                // 更新变化百分比
                document.getElementById('userChange').textContent = `+${stats.user_change || 0}`;
                document.getElementById('commissionChange').textContent = `+${stats.commission_change || 0}%`;
                document.getElementById('monthlyChange').textContent = `+${stats.monthly_change || 0}%`;
                document.getElementById('activeChange').textContent = `+${stats.active_change || 0}%`;
            }
            
            updateTeamList(teamMembers) {
                const teamList = document.getElementById('teamList');
                
                if (!teamMembers || teamMembers.length === 0) {
                    teamList.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">暂无团队成员</p>';
                    return;
                }
                
                const html = teamMembers.map(member => `
                    <div class="team-item">
                        <div class="team-info">
                            <div class="team-avatar">${member.username.charAt(0).toUpperCase()}</div>
                            <div class="team-details">
                                <h4>${member.username}</h4>
                                <p>注册时间：${new Date(member.created_at).toLocaleDateString()}</p>
                            </div>
                        </div>
                        <div class="team-stats">
                            <div class="amount">$${this.formatNumber(member.commission || 0)}</div>
                            <div class="count">${member.trade_count || 0}笔交易</div>
                        </div>
                    </div>
                `).join('');
                
                teamList.innerHTML = html;
            }
            
            updateRecentActivities(activities) {
                const activitiesContainer = document.getElementById('recentActivities');
                
                if (!activities || activities.length === 0) {
                    activitiesContainer.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">暂无最近活动</p>';
                    return;
                }
                
                const html = activities.map(activity => `
                    <div class="activity-item">
                        <div class="activity-icon ${activity.type}">
                            ${this.getActivityIcon(activity.type)}
                        </div>
                        <div class="activity-content">
                            <h5>${activity.title}</h5>
                            <p>${activity.description}</p>
                        </div>
                        <div class="activity-time">${this.timeAgo(activity.created_at)}</div>
                    </div>
                `).join('');
                
                activitiesContainer.innerHTML = html;
            }
            
            getActivityIcon(type) {
                const icons = {
                    register: '👤',
                    trade: '💹',
                    deposit: '💰',
                    withdraw: '🏦'
                };
                return icons[type] || '📝';
            }
            
            bindEvents() {
                // 绑定事件监听器
            }
            
            formatNumber(num) {
                if (num >= 1000000) {
                    return (num / 1000000).toFixed(1) + 'M';
                } else if (num >= 1000) {
                    return (num / 1000).toFixed(1) + 'K';
                }
                return num.toFixed(2);
            }
            
            timeAgo(timestamp) {
                const now = new Date();
                const time = new Date(timestamp);
                const diff = Math.floor((now - time) / 1000);
                
                if (diff < 60) return '刚刚';
                if (diff < 3600) return Math.floor(diff / 60) + '分钟前';
                if (diff < 86400) return Math.floor(diff / 3600) + '小时前';
                return Math.floor(diff / 86400) + '天前';
            }
            
            async apiRequest(url, options = {}) {
                const defaultOptions = {
                    headers: {
                        'Authorization': `Bearer ${this.token}`,
                        'Content-Type': 'application/json'
                    }
                };
                
                const response = await fetch(url, { ...defaultOptions, ...options });
                const result = await response.json();
                
                if (response.status === 401) {
                    localStorage.removeItem('agent_token');
                    localStorage.removeItem('agent_info');
                    window.location.href = '/agent/login.html';
                    return;
                }
                
                return result;
            }
        }
        
        function switchChart(period) {
            // 切换图表显示
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 这里可以加载不同时期的图表数据
            console.log('切换到', period, '图表');
        }
        
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('agent_token');
                localStorage.removeItem('agent_info');
                window.location.href = '/agent/login.html';
            }
        }
        
        let agentDashboard;
        document.addEventListener('DOMContentLoaded', () => {
            agentDashboard = new AgentDashboard();
        });
    </script>
</body>
</html>
