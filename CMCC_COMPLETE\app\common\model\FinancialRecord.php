<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 财务记录模型
 */
class FinancialRecord extends Model
{
    protected $name = 'gvd_financial_records';
    
    protected $type = [
        'amount' => 'float',
        'order_id' => 'integer'
    ];

    // 记录类型
    const TYPE_DEPOSIT = 'deposit';           // 充值
    const TYPE_WITHDRAW = 'withdraw';         // 提现
    const TYPE_TRADE_FEE = 'trade_fee';       // 交易手续费
    const TYPE_WITHDRAW_FEE = 'withdraw_fee'; // 提现手续费
    const TYPE_COMMISSION = 'commission';     // 佣金
    const TYPE_BONUS = 'bonus';               // 奖励
    const TYPE_PENALTY = 'penalty';           // 罚金
    const TYPE_ADJUSTMENT = 'adjustment';     // 调整

    // 状态
    const STATUS_PENDING = 'pending';     // 待处理
    const STATUS_COMPLETED = 'completed'; // 已完成
    const STATUS_FAILED = 'failed';       // 失败
    const STATUS_CANCELLED = 'cancelled'; // 已取消

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 获取类型文本
     */
    public function getTypeTextAttr($value, $data)
    {
        $typeMap = [
            self::TYPE_DEPOSIT => '充值',
            self::TYPE_WITHDRAW => '提现',
            self::TYPE_TRADE_FEE => '交易手续费',
            self::TYPE_WITHDRAW_FEE => '提现手续费',
            self::TYPE_COMMISSION => '佣金',
            self::TYPE_BONUS => '奖励',
            self::TYPE_PENALTY => '罚金',
            self::TYPE_ADJUSTMENT => '调整'
        ];
        
        return $typeMap[$data['type']] ?? '未知';
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = [
            self::STATUS_PENDING => '待处理',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_FAILED => '失败',
            self::STATUS_CANCELLED => '已取消'
        ];
        
        return $statusMap[$data['status']] ?? '未知';
    }

    /**
     * 创建财务记录
     */
    public static function createRecord($userId, $coinSymbol, $amount, $type, $remark = '', $orderId = null)
    {
        $data = [
            'user_id' => $userId,
            'coin_symbol' => $coinSymbol,
            'amount' => $amount,
            'type' => $type,
            'status' => self::STATUS_COMPLETED,
            'remark' => $remark,
            'order_id' => $orderId,
            'created_at' => date('Y-m-d H:i:s')
        ];

        return self::create($data);
    }

    /**
     * 获取用户财务记录
     */
    public static function getUserRecords($userId, $type = null, $limit = 20, $page = 1)
    {
        $query = self::where('user_id', $userId);
        
        if ($type) {
            $query->where('type', $type);
        }
        
        return $query->order('id desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);
    }

    /**
     * 获取收入统计
     */
    public static function getIncomeStats($startDate = null, $endDate = null)
    {
        $query = self::where('amount', '>', 0);
        
        if ($startDate) {
            $query->where('created_at', '>=', $startDate);
        }
        
        if ($endDate) {
            $query->where('created_at', '<=', $endDate);
        }
        
        return [
            'total_income' => $query->sum('amount'),
            'deposit_income' => $query->where('type', self::TYPE_DEPOSIT)->sum('amount'),
            'fee_income' => $query->whereIn('type', [self::TYPE_TRADE_FEE, self::TYPE_WITHDRAW_FEE])->sum('amount'),
            'commission_income' => $query->where('type', self::TYPE_COMMISSION)->sum('amount')
        ];
    }

    /**
     * 获取支出统计
     */
    public static function getExpenseStats($startDate = null, $endDate = null)
    {
        $query = self::where('amount', '<', 0);
        
        if ($startDate) {
            $query->where('created_at', '>=', $startDate);
        }
        
        if ($endDate) {
            $query->where('created_at', '<=', $endDate);
        }
        
        return [
            'total_expense' => abs($query->sum('amount')),
            'withdraw_expense' => abs($query->where('type', self::TYPE_WITHDRAW)->sum('amount')),
            'bonus_expense' => abs($query->where('type', self::TYPE_BONUS)->sum('amount')),
            'penalty_expense' => abs($query->where('type', self::TYPE_PENALTY)->sum('amount'))
        ];
    }

    /**
     * 获取每日统计
     */
    public static function getDailyStats($days = 30)
    {
        $startDate = date('Y-m-d', strtotime("-{$days} days"));
        
        return self::where('created_at', '>=', $startDate)
            ->field('DATE(created_at) as date, SUM(amount) as total_amount, COUNT(*) as count')
            ->group('DATE(created_at)')
            ->order('date desc')
            ->select();
    }

    /**
     * 搜索器 - 按用户ID
     */
    public function searchUserIdAttr($query, $value)
    {
        $query->where('user_id', $value);
    }

    /**
     * 搜索器 - 按类型
     */
    public function searchTypeAttr($query, $value)
    {
        $query->where('type', $value);
    }

    /**
     * 搜索器 - 按币种
     */
    public function searchCoinSymbolAttr($query, $value)
    {
        $query->where('coin_symbol', $value);
    }

    /**
     * 搜索器 - 按状态
     */
    public function searchStatusAttr($query, $value)
    {
        $query->where('status', $value);
    }

    /**
     * 搜索器 - 按时间范围
     */
    public function searchTimeRangeAttr($query, $value)
    {
        if (is_array($value) && count($value) === 2) {
            $query->whereBetweenTime('created_at', $value[0], $value[1]);
        }
    }

    /**
     * 搜索器 - 按金额范围
     */
    public function searchAmountRangeAttr($query, $value)
    {
        if (is_array($value) && count($value) === 2) {
            $query->whereBetween('amount', $value);
        }
    }
}
