<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\common\service\QuantTradingService;
use app\common\model\QuantStrategy;

/**
 * 量化策略运行器
 */
class QuantRunner extends Command
{
    protected function configure()
    {
        $this->setName('quant:run')
             ->setDescription('运行量化交易策略');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始执行量化策略...');

        $quantService = new QuantTradingService();
        
        // 获取所有运行中的策略
        $strategies = QuantStrategy::getRunningStrategies();
        
        $output->writeln('找到 ' . count($strategies) . ' 个运行中的策略');

        foreach ($strategies as $strategy) {
            try {
                $output->writeln("执行策略: {$strategy->name} ({$strategy->strategy_id})");
                
                $result = $quantService->executeStrategy($strategy->strategy_id);
                
                if ($result['code']) {
                    $output->writeln("策略执行成功: " . json_encode($result['data']));
                } else {
                    $output->writeln("策略执行失败: " . $result['msg']);
                }
            } catch (\Exception $e) {
                $output->writeln("策略执行异常: " . $e->getMessage());
            }
        }

        $output->writeln('量化策略执行完成');
        return 0;
    }
}
