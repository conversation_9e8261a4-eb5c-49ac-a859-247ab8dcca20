<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - GVD</title>
    <script src="/static/js/lang.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: Arial, sans-serif; background: #1a1a1a; color: #fff; 
            min-height: 100vh; display: flex; align-items: center; justify-content: center;
            background-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .container { 
            max-width: 400px; width: 100%; padding: 40px; background: #2d2d2d; 
            border-radius: 10px; border: 1px solid #333; box-shadow: 0 10px 30px rgba(0,0,0,0.5);
        }
        .logo { 
            text-align: center; font-size: 32px; font-weight: bold; margin-bottom: 30px; 
            color: #007bff; 
        }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; color: #ccc; }
        .form-group input { 
            width: 100%; padding: 12px; border: 1px solid #333; background: #1a1a1a; 
            color: #fff; border-radius: 5px; font-size: 16px;
        }
        .btn { 
            width: 100%; padding: 15px; background: #007bff; color: #fff; border: none; 
            border-radius: 5px; font-size: 16px; font-weight: bold; cursor: pointer; 
        }
        .links { text-align: center; margin-top: 20px; }
        .links a { color: #007bff; text-decoration: none; }
        .back-home { text-align: center; margin-bottom: 20px; }
        .back-home a { color: #999; text-decoration: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="back-home">
            <a href="/" data-lang="home">← 返回首页</a>
        </div>
        <div class="logo">🚀 <span data-lang="register">用户注册</span></div>
        <form id="registerForm">
            <div class="form-group">
                <label>邮箱地址</label>
                <input type="email" id="email" placeholder="请输入邮箱地址" required>
            </div>
            <div class="form-group">
                <label>密码</label>
                <input type="password" id="password" placeholder="请输入密码（至少6位）" required>
            </div>
            <div class="form-group">
                <label>确认密码</label>
                <input type="password" id="confirmPassword" placeholder="请再次输入密码" required>
            </div>
            <div class="form-group">
                <label>邀请码</label>
                <input type="text" id="inviteCode" placeholder="请输入邀请码（必填）" required>
            </div>
            <button type="submit" class="btn">立即注册</button>
        </form>
        <div class="links">
            <p>已有账户？<a href="/auth/login.html">立即登录</a></p>
        </div>
    </div>
    <script>
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const inviteCode = document.getElementById('inviteCode').value;
            if (!email || !password || !confirmPassword || !inviteCode) {
                alert('请填写完整信息');
                return;
            }
            if (password !== confirmPassword) {
                alert('两次输入的密码不一致');
                return;
            }
            if (password.length < 6) {
                alert('密码至少6个字符');
                return;
            }
            alert('注册成功！请登录');
            window.location.href = '/auth/login.html';
        });
    </script>
</body>
</html>
