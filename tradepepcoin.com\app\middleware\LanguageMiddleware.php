<?php
declare (strict_types = 1);

namespace app\middleware;

use app\common\service\LanguageService;
use think\facade\View;
use think\facade\Lang;

/**
 * 多语言中间件
 */
class LanguageMiddleware
{
    protected $languageService;

    public function __construct()
    {
        $this->languageService = new LanguageService();
    }

    /**
     * 处理请求
     */
    public function handle($request, \Closure $next)
    {
        // 获取当前语言
        $currentLanguage = $this->languageService->getCurrentLanguage();
        
        // 如果URL中有语言参数，设置语言
        $urlLang = $request->param('lang', '');
        if ($urlLang && $this->languageService->isLanguageSupported($urlLang)) {
            $this->languageService->setLanguage($urlLang);
            $currentLanguage = $urlLang;
        }

        // 设置应用语言
        Lang::setLangSet($currentLanguage);

        // 加载语言包
        $this->loadLanguagePacks($currentLanguage);

        // 设置模板变量
        $this->setTemplateVars($currentLanguage);

        // 设置响应头
        $response = $next($request);
        
        // 设置Content-Language头
        $response->header('Content-Language', $currentLanguage);
        
        return $response;
    }

    /**
     * 加载语言包
     */
    private function loadLanguagePacks(string $language): void
    {
        // 加载通用语言包
        $commonLang = $this->languageService->loadLanguagePack($language, 'common');
        Lang::load($commonLang, $language);

        // 加载其他模块语言包
        $modules = ['trade', 'user', 'admin'];
        foreach ($modules as $module) {
            $moduleLang = $this->languageService->loadLanguagePack($language, $module);
            if (!empty($moduleLang)) {
                Lang::load($moduleLang, $language);
            }
        }
    }

    /**
     * 设置模板变量
     */
    private function setTemplateVars(string $language): void
    {
        $languageInfo = $this->languageService->getLanguageInfo($language);
        $supportedLanguages = $this->languageService->getSupportedLanguages();

        View::assign([
            'current_language' => $language,
            'language_info' => $languageInfo,
            'supported_languages' => $supportedLanguages,
            'is_rtl' => $languageInfo['direction'] === 'rtl'
        ]);

        // 注册模板函数
        View::addGlobal('lang', function($key, $params = [], $module = 'common') use ($language) {
            return $this->languageService->translate($key, $params, $module, $language);
        });

        View::addGlobal('format_number', function($number, $decimals = 2) use ($language) {
            return $this->languageService->formatNumber($number, $decimals, $language);
        });

        View::addGlobal('format_currency', function($amount, $currency = 'USD') use ($language) {
            return $this->languageService->formatCurrency($amount, $currency, $language);
        });

        View::addGlobal('format_datetime', function($datetime, $format = 'full') use ($language) {
            if (is_string($datetime)) {
                $datetime = new \DateTime($datetime);
            }
            return $this->languageService->formatDateTime($datetime, $format, $language);
        });

        View::addGlobal('relative_time', function($datetime) use ($language) {
            if (is_string($datetime)) {
                $datetime = new \DateTime($datetime);
            }
            return $this->languageService->getRelativeTime($datetime, $language);
        });
    }
}

/**
 * API语言中间件
 */
class ApiLanguageMiddleware
{
    protected $languageService;

    public function __construct()
    {
        $this->languageService = new LanguageService();
    }

    /**
     * 处理API请求
     */
    public function handle($request, \Closure $next)
    {
        // 从请求头获取语言
        $acceptLanguage = $request->header('Accept-Language', '');
        $language = $this->parseAcceptLanguage($acceptLanguage);
        
        // 从URL参数获取语言（优先级更高）
        $urlLang = $request->param('lang', '');
        if ($urlLang && $this->languageService->isLanguageSupported($urlLang)) {
            $language = $urlLang;
        }

        // 设置当前语言
        if ($language) {
            $this->languageService->setLanguage($language);
        } else {
            $language = $this->languageService->getCurrentLanguage();
        }

        // 设置应用语言
        Lang::setLangSet($language);

        // 加载语言包
        $commonLang = $this->languageService->loadLanguagePack($language, 'common');
        Lang::load($commonLang, $language);

        // 处理请求
        $response = $next($request);

        // 设置响应头
        $response->header('Content-Language', $language);

        return $response;
    }

    /**
     * 解析Accept-Language头
     */
    private function parseAcceptLanguage(string $acceptLanguage): string
    {
        if (empty($acceptLanguage)) {
            return '';
        }

        $languages = [];
        $parts = explode(',', $acceptLanguage);
        
        foreach ($parts as $part) {
            $part = trim($part);
            $langParts = explode(';', $part);
            $lang = trim($langParts[0]);
            $quality = 1.0;
            
            if (isset($langParts[1])) {
                $qParts = explode('=', $langParts[1]);
                if (count($qParts) === 2 && trim($qParts[0]) === 'q') {
                    $quality = (float)trim($qParts[1]);
                }
            }
            
            $languages[$lang] = $quality;
        }

        // 按质量排序
        arsort($languages);

        // 查找支持的语言
        foreach ($languages as $lang => $quality) {
            if ($this->languageService->isLanguageSupported($lang)) {
                return $lang;
            }
            
            // 主语言匹配
            $mainLang = explode('-', $lang)[0];
            foreach ($this->languageService->getSupportedLanguages() as $supportedLang => $info) {
                if (strpos($supportedLang, $mainLang) === 0) {
                    return $supportedLang;
                }
            }
        }

        return '';
    }
}
