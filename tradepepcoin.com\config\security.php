<?php
// 安全配置

return [
    // IP白名单（为空则不限制）
    'ip_whitelist' => [
        // '127.0.0.1',
        // '***********/24',
    ],

    // IP黑名单
    'ip_blacklist' => [
        // '*************',
        // '10.0.0.0/8',
    ],

    // 登录安全配置
    'login' => [
        'max_attempts' => 5,        // 最大失败次数
        'lock_time' => 1800,       // 锁定时间（秒）
        'ip_max_attempts' => 10,   // IP最大失败次数
        'ip_lock_time' => 3600,    // IP锁定时间（秒）
    ],

    // API安全配置
    'api' => [
        'rate_limit' => 1000,      // 每分钟最大请求数
        'signature_timeout' => 300, // 签名有效期（秒）
        'require_signature' => true, // 是否需要签名验证
    ],

    // 防刷配置
    'rate_limit' => [
        'default' => [
            'max_requests' => 100,  // 最大请求数
            'time_window' => 60,    // 时间窗口（秒）
        ],
        'login' => [
            'max_requests' => 5,
            'time_window' => 300,
        ],
        'register' => [
            'max_requests' => 3,
            'time_window' => 3600,
        ],
        'send_code' => [
            'max_requests' => 5,
            'time_window' => 3600,
        ],
        'trade' => [
            'max_requests' => 200,
            'time_window' => 60,
        ],
        'withdraw' => [
            'max_requests' => 10,
            'time_window' => 3600,
        ],
    ],

    // 密码安全配置
    'password' => [
        'min_length' => 6,         // 最小长度
        'max_length' => 20,        // 最大长度
        'require_uppercase' => false, // 需要大写字母
        'require_lowercase' => false, // 需要小写字母
        'require_numbers' => false,   // 需要数字
        'require_symbols' => false,   // 需要特殊字符
        'history_count' => 5,      // 记住最近几次密码
    ],

    // 会话安全配置
    'session' => [
        'timeout' => 7200,         // 会话超时时间（秒）
        'regenerate_interval' => 300, // 会话ID重新生成间隔
        'max_concurrent' => 3,     // 最大并发会话数
    ],

    // 设备安全配置
    'device' => [
        'enable_fingerprint' => true,  // 启用设备指纹
        'fingerprint_timeout' => 2592000, // 设备指纹有效期（30天）
        'require_verification' => false,  // 新设备需要验证
    ],

    // 谷歌验证器配置
    'google_authenticator' => [
        'enable' => true,          // 启用谷歌验证器
        'tolerance' => 1,          // 时间容差
        'issuer' => 'Exchange',    // 发行者名称
    ],

    // 邮件验证配置
    'email_verification' => [
        'enable' => true,          // 启用邮件验证
        'code_length' => 6,        // 验证码长度
        'code_timeout' => 300,     // 验证码有效期（秒）
        'max_send_count' => 5,     // 每小时最大发送次数
    ],

    // 短信验证配置
    'sms_verification' => [
        'enable' => false,         // 启用短信验证
        'code_length' => 6,        // 验证码长度
        'code_timeout' => 300,     // 验证码有效期（秒）
        'max_send_count' => 5,     // 每小时最大发送次数
    ],

    // 文件上传安全配置
    'upload' => [
        'max_size' => 10485760,    // 最大文件大小（10MB）
        'allowed_types' => [       // 允许的文件类型
            'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'document' => ['pdf', 'doc', 'docx', 'xls', 'xlsx'],
        ],
        'scan_virus' => false,     // 病毒扫描
        'check_content' => true,   // 检查文件内容
    ],

    // 加密配置
    'encryption' => [
        'algorithm' => 'AES-256-CBC', // 加密算法
        'key_rotation' => false,   // 密钥轮换
        'key_rotation_interval' => 86400, // 密钥轮换间隔（天）
    ],

    // 日志配置
    'logging' => [
        'enable_security_log' => true, // 启用安全日志
        'log_level' => 'warning',   // 日志级别
        'log_retention' => 90,      // 日志保留天数
    ],

    // 监控配置
    'monitoring' => [
        'enable_intrusion_detection' => true, // 启用入侵检测
        'suspicious_activity_threshold' => 50, // 可疑活动阈值
        'alert_email' => '',        // 告警邮箱
        'alert_webhook' => '',      // 告警Webhook
    ],

    // CSRF保护
    'csrf' => [
        'enable' => true,          // 启用CSRF保护
        'token_name' => '_token',  // Token名称
        'token_timeout' => 3600,   // Token有效期
    ],

    // XSS保护
    'xss' => [
        'enable' => true,          // 启用XSS保护
        'filter_input' => true,    // 过滤输入
        'escape_output' => true,   // 转义输出
    ],

    // SQL注入保护
    'sql_injection' => [
        'enable' => true,          // 启用SQL注入保护
        'strict_mode' => false,    // 严格模式
    ],

    // 请求头安全
    'headers' => [
        'x_frame_options' => 'DENY',           // X-Frame-Options
        'x_content_type_options' => 'nosniff', // X-Content-Type-Options
        'x_xss_protection' => '1; mode=block', // X-XSS-Protection
        'strict_transport_security' => 'max-age=31536000; includeSubDomains', // HSTS
        'content_security_policy' => "default-src 'self'", // CSP
    ],

    // 备份和恢复
    'backup' => [
        'enable_auto_backup' => true, // 启用自动备份
        'backup_interval' => 86400, // 备份间隔（秒）
        'backup_retention' => 30,   // 备份保留天数
        'encrypt_backup' => true,   // 加密备份
    ],

    // 审计配置
    'audit' => [
        'enable' => true,          // 启用审计
        'log_all_requests' => false, // 记录所有请求
        'log_sensitive_operations' => true, // 记录敏感操作
        'retention_period' => 365, // 审计日志保留期（天）
    ],
];
