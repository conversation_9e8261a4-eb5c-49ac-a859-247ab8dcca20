@echo off
chcp 65001 >nul
echo ===================================
echo GVD交易平台 - 依赖安装脚本
echo ===================================

REM 检查PHP是否安装
php -v >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: PHP未安装或不在PATH中
    echo 请先安装PHP并添加到系统PATH
    pause
    exit /b 1
)

echo PHP版本:
php -v | findstr "PHP"

REM 检查Composer是否安装
composer --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Composer未安装，请手动安装Composer
    echo 下载地址: https://getcomposer.org/download/
    pause
    exit /b 1
)

echo Composer版本:
composer --version

REM 安装项目依赖
echo.
echo 正在安装项目依赖...
composer install --no-dev --optimize-autoloader

if %errorlevel% equ 0 (
    echo.
    echo ===================================
    echo 依赖安装成功！
    echo ===================================
    echo 接下来请：
    echo 1. 配置 .env 文件
    echo 2. 导入数据库
    echo 3. 启动WebSocket服务
    echo 4. 配置Web服务器
    echo ===================================
) else (
    echo.
    echo ===================================
    echo 依赖安装失败！
    echo 请检查网络连接和PHP配置
    echo ===================================
)

pause
