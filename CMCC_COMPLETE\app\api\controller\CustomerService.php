<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\BaseController;
use app\common\service\CustomerServiceService;
use app\common\model\User;
use think\facade\Request;
use think\facade\Validate;

/**
 * 客服控制器
 */
class CustomerService extends BaseController
{
    protected $customerServiceService;

    public function initialize()
    {
        parent::initialize();
        $this->customerServiceService = new CustomerServiceService();
    }

    /**
     * 初始化客服会话
     * POST /api/customer-service/init
     */
    public function init()
    {
        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $result = $this->customerServiceService->initUserService($userId);
        return json($result);
    }

    /**
     * 发送消息
     * POST /api/customer-service/send
     */
    public function sendMessage()
    {
        if (!Request::isPost()) {
            return $this->error('请求方式错误');
        }

        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $data = Request::post();

        $validate = Validate::rule([
            'content' => 'require|max:1000',
            'type' => 'integer|in:1,2,3'
        ])->message([
            'content.require' => '消息内容不能为空',
            'content.max' => '消息内容不能超过1000字符',
            'type.in' => '消息类型无效'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        $type = $data['type'] ?? 1;
        $result = $this->customerServiceService->sendUserMessage($userId, $data['content'], $type);

        return json($result);
    }

    /**
     * 获取聊天记录
     * GET /api/customer-service/messages
     */
    public function getMessages()
    {
        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $page = Request::get('page/d', 1);
        $limit = Request::get('limit/d', 50);

        $result = $this->customerServiceService->getUserChatHistory($userId, $page, $limit);
        return json($result);
    }

    /**
     * 获取未读消息数量
     * GET /api/customer-service/unread-count
     */
    public function getUnreadCount()
    {
        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $count = $this->customerServiceService->getUnreadCount($userId, false);
        
        return $this->success('获取成功', ['unread_count' => $count]);
    }

    /**
     * 上传图片
     * POST /api/customer-service/upload-image
     */
    public function uploadImage()
    {
        if (!Request::isPost()) {
            return $this->error('请求方式错误');
        }

        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $file = Request::file('image');
        if (!$file) {
            return $this->error('请选择图片文件');
        }

        try {
            // 验证文件类型
            $allowedTypes = ['jpg', 'jpeg', 'png', 'gif'];
            $extension = strtolower($file->getOriginalExtension());
            
            if (!in_array($extension, $allowedTypes)) {
                return $this->error('只支持JPG、PNG、GIF格式的图片');
            }

            // 验证文件大小（5MB）
            if ($file->getSize() > 5 * 1024 * 1024) {
                return $this->error('图片大小不能超过5MB');
            }

            // 保存文件
            $saveName = \think\facade\Filesystem::disk('public')->putFile('customer-service', $file);
            $imageUrl = '/storage/' . $saveName;

            // 发送图片消息
            $result = $this->customerServiceService->sendImageMessage($userId, $imageUrl, false);

            if ($result['code'] == 1) {
                return $this->success('图片发送成功', [
                    'image_url' => $imageUrl,
                    'message' => $result['data']
                ]);
            } else {
                return $this->error($result['msg']);
            }

        } catch (\Exception $e) {
            return $this->error('图片上传失败：' . $e->getMessage());
        }
    }

    /**
     * 上传文件
     * POST /api/customer-service/upload-file
     */
    public function uploadFile()
    {
        if (!Request::isPost()) {
            return $this->error('请求方式错误');
        }

        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $file = Request::file('file');
        if (!$file) {
            return $this->error('请选择文件');
        }

        try {
            // 验证文件类型
            $allowedTypes = ['pdf', 'doc', 'docx', 'txt', 'zip', 'rar'];
            $extension = strtolower($file->getOriginalExtension());
            
            if (!in_array($extension, $allowedTypes)) {
                return $this->error('不支持的文件类型');
            }

            // 验证文件大小（10MB）
            if ($file->getSize() > 10 * 1024 * 1024) {
                return $this->error('文件大小不能超过10MB');
            }

            // 保存文件
            $saveName = \think\facade\Filesystem::disk('public')->putFile('customer-service', $file);
            $fileUrl = '/storage/' . $saveName;

            // 发送文件消息
            $fileName = $file->getOriginalName();
            $fileInfo = json_encode([
                'name' => $fileName,
                'url' => $fileUrl,
                'size' => $file->getSize()
            ]);

            $result = $this->customerServiceService->sendFileMessage($userId, $fileInfo, false);

            if ($result['code'] == 1) {
                return $this->success('文件发送成功', [
                    'file_url' => $fileUrl,
                    'file_name' => $fileName,
                    'message' => $result['data']
                ]);
            } else {
                return $this->error($result['msg']);
            }

        } catch (\Exception $e) {
            return $this->error('文件上传失败：' . $e->getMessage());
        }
    }

    /**
     * 获取当前用户ID
     */
    private function getUserId(): int
    {
        $token = Request::header('Authorization');
        if (!$token) {
            return 0;
        }

        $token = str_replace('Bearer ', '', $token);
        $payload = User::verifyToken($token);
        return $payload ? $payload['user_id'] : 0;
    }
}
