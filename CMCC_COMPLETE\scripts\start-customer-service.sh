#!/bin/bash

# GVD客服系统启动脚本
# 用于启动WebSocket服务和相关组件

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 日志文件
LOG_DIR="$PROJECT_ROOT/runtime/logs"
WEBSOCKET_LOG="$LOG_DIR/websocket.log"
CLEANUP_LOG="$LOG_DIR/customer_cleanup.log"

# PID文件
PID_DIR="$PROJECT_ROOT/runtime/pids"
WEBSOCKET_PID="$PID_DIR/websocket.pid"

# 创建必要的目录
mkdir -p "$LOG_DIR"
mkdir -p "$PID_DIR"

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] ${message}${NC}"
}

# 检查PHP环境
check_php() {
    if ! command -v php &> /dev/null; then
        print_message $RED "错误: PHP未安装或不在PATH中"
        exit 1
    fi
    
    local php_version=$(php -v | head -n1 | cut -d' ' -f2 | cut -d'.' -f1,2)
    print_message $BLUE "PHP版本: $php_version"
}

# 检查Composer依赖
check_composer() {
    if [ ! -f "vendor/autoload.php" ]; then
        print_message $YELLOW "警告: Composer依赖未安装，正在安装..."
        if command -v composer &> /dev/null; then
            composer install --no-dev --optimize-autoloader
        else
            print_message $RED "错误: Composer未安装"
            exit 1
        fi
    fi
}

# 检查数据库连接
check_database() {
    print_message $BLUE "检查数据库连接..."
    
    # 这里可以添加数据库连接检查逻辑
    # php think database:check
    
    print_message $GREEN "数据库连接正常"
}

# 启动WebSocket服务
start_websocket() {
    print_message $BLUE "启动WebSocket客服服务..."
    
    # 检查是否已经运行
    if [ -f "$WEBSOCKET_PID" ]; then
        local pid=$(cat "$WEBSOCKET_PID")
        if ps -p $pid > /dev/null 2>&1; then
            print_message $YELLOW "WebSocket服务已在运行 (PID: $pid)"
            return 0
        else
            rm -f "$WEBSOCKET_PID"
        fi
    fi
    
    # 启动WebSocket服务
    nohup php think websocket:start > "$WEBSOCKET_LOG" 2>&1 &
    local websocket_pid=$!
    
    # 保存PID
    echo $websocket_pid > "$WEBSOCKET_PID"
    
    # 等待服务启动
    sleep 3
    
    # 检查服务是否成功启动
    if ps -p $websocket_pid > /dev/null 2>&1; then
        print_message $GREEN "WebSocket服务启动成功 (PID: $websocket_pid)"
        print_message $BLUE "WebSocket地址: ws://localhost:2346"
    else
        print_message $RED "WebSocket服务启动失败"
        rm -f "$WEBSOCKET_PID"
        exit 1
    fi
}

# 停止WebSocket服务
stop_websocket() {
    print_message $BLUE "停止WebSocket客服服务..."
    
    if [ -f "$WEBSOCKET_PID" ]; then
        local pid=$(cat "$WEBSOCKET_PID")
        if ps -p $pid > /dev/null 2>&1; then
            kill $pid
            sleep 2
            
            # 强制杀死进程
            if ps -p $pid > /dev/null 2>&1; then
                kill -9 $pid
            fi
            
            rm -f "$WEBSOCKET_PID"
            print_message $GREEN "WebSocket服务已停止"
        else
            print_message $YELLOW "WebSocket服务未运行"
            rm -f "$WEBSOCKET_PID"
        fi
    else
        print_message $YELLOW "WebSocket服务未运行"
    fi
}

# 重启WebSocket服务
restart_websocket() {
    stop_websocket
    sleep 2
    start_websocket
}

# 检查服务状态
check_status() {
    print_message $BLUE "检查客服系统状态..."
    
    # 检查WebSocket服务
    if [ -f "$WEBSOCKET_PID" ]; then
        local pid=$(cat "$WEBSOCKET_PID")
        if ps -p $pid > /dev/null 2>&1; then
            print_message $GREEN "WebSocket服务运行中 (PID: $pid)"
            
            # 检查端口是否监听
            if command -v netstat &> /dev/null; then
                if netstat -ln | grep -q ":2346"; then
                    print_message $GREEN "WebSocket端口2346正在监听"
                else
                    print_message $YELLOW "WebSocket端口2346未监听"
                fi
            fi
        else
            print_message $RED "WebSocket服务未运行"
            rm -f "$WEBSOCKET_PID"
        fi
    else
        print_message $RED "WebSocket服务未运行"
    fi
    
    # 检查日志文件
    if [ -f "$WEBSOCKET_LOG" ]; then
        local log_size=$(du -h "$WEBSOCKET_LOG" | cut -f1)
        print_message $BLUE "WebSocket日志大小: $log_size"
    fi
}

# 查看日志
view_logs() {
    local log_type=${1:-websocket}
    
    case $log_type in
        websocket)
            if [ -f "$WEBSOCKET_LOG" ]; then
                print_message $BLUE "WebSocket服务日志 (最后50行):"
                tail -n 50 "$WEBSOCKET_LOG"
            else
                print_message $YELLOW "WebSocket日志文件不存在"
            fi
            ;;
        cleanup)
            if [ -f "$CLEANUP_LOG" ]; then
                print_message $BLUE "清理任务日志 (最后50行):"
                tail -n 50 "$CLEANUP_LOG"
            else
                print_message $YELLOW "清理任务日志文件不存在"
            fi
            ;;
        *)
            print_message $RED "未知的日志类型: $log_type"
            print_message $BLUE "可用的日志类型: websocket, cleanup"
            ;;
    esac
}

# 运行清理任务
run_cleanup() {
    print_message $BLUE "运行客服数据清理任务..."
    
    php think customer:cleanup >> "$CLEANUP_LOG" 2>&1
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "清理任务执行成功"
    else
        print_message $RED "清理任务执行失败"
        exit 1
    fi
}

# 安装系统服务 (systemd)
install_service() {
    print_message $BLUE "安装系统服务..."
    
    local service_file="/etc/systemd/system/gvd-customer-service.service"
    
    if [ ! -w "/etc/systemd/system/" ]; then
        print_message $RED "错误: 需要root权限安装系统服务"
        exit 1
    fi
    
    cat > "$service_file" << EOF
[Unit]
Description=GVD Customer Service WebSocket Server
After=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=$PROJECT_ROOT
ExecStart=/usr/bin/php think websocket:start
ExecReload=/bin/kill -USR1 \$MAINPID
KillMode=mixed
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable gvd-customer-service
    
    print_message $GREEN "系统服务安装成功"
    print_message $BLUE "使用以下命令管理服务:"
    print_message $BLUE "  启动: systemctl start gvd-customer-service"
    print_message $BLUE "  停止: systemctl stop gvd-customer-service"
    print_message $BLUE "  重启: systemctl restart gvd-customer-service"
    print_message $BLUE "  状态: systemctl status gvd-customer-service"
}

# 显示帮助信息
show_help() {
    echo "GVD客服系统管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start       启动WebSocket服务"
    echo "  stop        停止WebSocket服务"
    echo "  restart     重启WebSocket服务"
    echo "  status      检查服务状态"
    echo "  logs        查看WebSocket日志"
    echo "  cleanup     运行数据清理任务"
    echo "  install     安装系统服务"
    echo "  help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start                # 启动服务"
    echo "  $0 logs websocket       # 查看WebSocket日志"
    echo "  $0 logs cleanup         # 查看清理任务日志"
}

# 主函数
main() {
    local command=${1:-help}
    
    case $command in
        start)
            check_php
            check_composer
            check_database
            start_websocket
            ;;
        stop)
            stop_websocket
            ;;
        restart)
            restart_websocket
            ;;
        status)
            check_status
            ;;
        logs)
            view_logs $2
            ;;
        cleanup)
            run_cleanup
            ;;
        install)
            install_service
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_message $RED "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
