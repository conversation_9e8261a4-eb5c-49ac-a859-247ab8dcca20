<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Log;
use think\facade\Cache;
use think\facade\Db;

/**
 * AI智能客服服务类
 * 集成自然语言处理和知识库
 */
class AiCustomerService
{
    // 消息类型
    const MSG_TYPE_TEXT = 'text';
    const MSG_TYPE_IMAGE = 'image';
    const MSG_TYPE_FILE = 'file';
    const MSG_TYPE_QUICK_REPLY = 'quick_reply';
    const MSG_TYPE_CARD = 'card';

    // 意图类型
    const INTENT_GREETING = 'greeting';
    const INTENT_ACCOUNT = 'account';
    const INTENT_TRADING = 'trading';
    const INTENT_DEPOSIT = 'deposit';
    const INTENT_WITHDRAW = 'withdraw';
    const INTENT_SECURITY = 'security';
    const INTENT_COMPLAINT = 'complaint';
    const INTENT_UNKNOWN = 'unknown';

    // 情感分析
    const SENTIMENT_POSITIVE = 'positive';
    const SENTIMENT_NEUTRAL = 'neutral';
    const SENTIMENT_NEGATIVE = 'negative';

    private $knowledgeBase;
    private $nlpService;

    public function __construct()
    {
        $this->knowledgeBase = $this->loadKnowledgeBase();
        $this->nlpService = new NlpService();
    }

    /**
     * 处理用户消息
     */
    public function processMessage(array $messageData): array
    {
        try {
            $userId = $messageData['user_id'];
            $message = $messageData['message'];
            $messageType = $messageData['type'] ?? self::MSG_TYPE_TEXT;

            // 记录用户消息
            $this->logMessage($userId, $message, 'user', $messageType);

            // 获取用户上下文
            $context = $this->getUserContext($userId);

            // 文本消息处理
            if ($messageType === self::MSG_TYPE_TEXT) {
                $response = $this->processTextMessage($message, $context, $userId);
            } else {
                $response = $this->processNonTextMessage($messageData, $context);
            }

            // 记录AI回复
            $this->logMessage($userId, $response['content'], 'ai', $response['type']);

            // 更新用户上下文
            $this->updateUserContext($userId, $message, $response);

            return [
                'code' => 1,
                'msg' => '处理成功',
                'data' => $response
            ];

        } catch (\Exception $e) {
            Log::error('AI客服处理消息失败：' . $e->getMessage());
            return [
                'code' => 0,
                'msg' => '处理失败',
                'data' => $this->getErrorResponse()
            ];
        }
    }

    /**
     * 处理文本消息
     */
    private function processTextMessage(string $message, array $context, int $userId): array
    {
        // 1. 预处理消息
        $cleanMessage = $this->preprocessMessage($message);

        // 2. 意图识别
        $intent = $this->recognizeIntent($cleanMessage, $context);

        // 3. 实体提取
        $entities = $this->extractEntities($cleanMessage);

        // 4. 情感分析
        $sentiment = $this->analyzeSentiment($cleanMessage);

        // 5. 生成回复
        $response = $this->generateResponse($intent, $entities, $sentiment, $context, $userId);

        // 6. 添加快捷回复选项
        $response['quick_replies'] = $this->getQuickReplies($intent);

        return $response;
    }

    /**
     * 意图识别
     */
    private function recognizeIntent(string $message, array $context): string
    {
        // 关键词匹配
        $intentKeywords = [
            self::INTENT_GREETING => ['你好', '您好', 'hello', 'hi', '客服'],
            self::INTENT_ACCOUNT => ['账户', '账号', '登录', '注册', '密码', '忘记密码'],
            self::INTENT_TRADING => ['交易', '买入', '卖出', '下单', '撤单', '订单'],
            self::INTENT_DEPOSIT => ['充值', '入金', '转账', '到账'],
            self::INTENT_WITHDRAW => ['提现', '出金', '提币', '转出'],
            self::INTENT_SECURITY => ['安全', '验证', '绑定', '解绑', '2FA', '谷歌验证'],
            self::INTENT_COMPLAINT => ['投诉', '举报', '问题', '故障', '错误', '不满']
        ];

        foreach ($intentKeywords as $intent => $keywords) {
            foreach ($keywords as $keyword) {
                if (strpos($message, $keyword) !== false) {
                    return $intent;
                }
            }
        }

        // 基于上下文的意图推断
        if (!empty($context['last_intent'])) {
            $contextIntents = [
                self::INTENT_ACCOUNT => ['是', '对', '确认', '继续'],
                self::INTENT_TRADING => ['是的', '对的', '查看', '详情']
            ];

            if (isset($contextIntents[$context['last_intent']])) {
                foreach ($contextIntents[$context['last_intent']] as $keyword) {
                    if (strpos($message, $keyword) !== false) {
                        return $context['last_intent'];
                    }
                }
            }
        }

        return self::INTENT_UNKNOWN;
    }

    /**
     * 实体提取
     */
    private function extractEntities(string $message): array
    {
        $entities = [];

        // 提取数字
        if (preg_match_all('/\d+(?:\.\d+)?/', $message, $matches)) {
            $entities['numbers'] = $matches[0];
        }

        // 提取币种
        $coins = ['BTC', 'ETH', 'USDT', 'BNB', 'ADA', 'DOT', 'LINK'];
        foreach ($coins as $coin) {
            if (stripos($message, $coin) !== false) {
                $entities['coins'][] = $coin;
            }
        }

        // 提取交易对
        if (preg_match_all('/([A-Z]{3,5})\/([A-Z]{3,5})|([A-Z]{3,5})([A-Z]{3,5})/', $message, $matches)) {
            $entities['trading_pairs'] = array_filter($matches[0]);
        }

        return $entities;
    }

    /**
     * 情感分析
     */
    private function analyzeSentiment(string $message): string
    {
        $positiveWords = ['好', '棒', '满意', '喜欢', '感谢', '谢谢', '不错', '很好'];
        $negativeWords = ['差', '烂', '不满', '讨厌', '垃圾', '问题', '故障', '错误'];

        $positiveCount = 0;
        $negativeCount = 0;

        foreach ($positiveWords as $word) {
            if (strpos($message, $word) !== false) {
                $positiveCount++;
            }
        }

        foreach ($negativeWords as $word) {
            if (strpos($message, $word) !== false) {
                $negativeCount++;
            }
        }

        if ($positiveCount > $negativeCount) {
            return self::SENTIMENT_POSITIVE;
        } elseif ($negativeCount > $positiveCount) {
            return self::SENTIMENT_NEGATIVE;
        } else {
            return self::SENTIMENT_NEUTRAL;
        }
    }

    /**
     * 生成回复
     */
    private function generateResponse(string $intent, array $entities, string $sentiment, array $context, int $userId): array
    {
        switch ($intent) {
            case self::INTENT_GREETING:
                return $this->getGreetingResponse($sentiment);

            case self::INTENT_ACCOUNT:
                return $this->getAccountResponse($entities, $userId);

            case self::INTENT_TRADING:
                return $this->getTradingResponse($entities, $userId);

            case self::INTENT_DEPOSIT:
                return $this->getDepositResponse($entities, $userId);

            case self::INTENT_WITHDRAW:
                return $this->getWithdrawResponse($entities, $userId);

            case self::INTENT_SECURITY:
                return $this->getSecurityResponse($entities, $userId);

            case self::INTENT_COMPLAINT:
                return $this->getComplaintResponse($sentiment, $userId);

            default:
                return $this->getUnknownResponse($context);
        }
    }

    /**
     * 问候回复
     */
    private function getGreetingResponse(string $sentiment): array
    {
        $responses = [
            '您好！我是GVD智能客服小助手，很高兴为您服务！',
            '欢迎来到GVD交易平台！有什么可以帮助您的吗？',
            '您好！请问有什么问题需要我协助解决的吗？'
        ];

        return [
            'type' => self::MSG_TYPE_TEXT,
            'content' => $responses[array_rand($responses)],
            'suggestions' => [
                '账户问题',
                '交易咨询',
                '充值提现',
                '安全设置'
            ]
        ];
    }

    /**
     * 账户相关回复
     */
    private function getAccountResponse(array $entities, int $userId): array
    {
        $userInfo = $this->getUserInfo($userId);
        
        $response = "关于您的账户信息：\n";
        $response .= "• 账户状态：" . ($userInfo['status'] == 1 ? '正常' : '异常') . "\n";
        $response .= "• 实名状态：" . ($userInfo['kyc_status'] == 1 ? '已认证' : '未认证') . "\n";
        $response .= "• 注册时间：" . $userInfo['created_at'] . "\n\n";
        $response .= "如需修改密码或其他账户设置，请点击下方选项：";

        return [
            'type' => self::MSG_TYPE_TEXT,
            'content' => $response,
            'suggestions' => [
                '修改密码',
                '实名认证',
                '安全设置',
                '联系人工客服'
            ]
        ];
    }

    /**
     * 交易相关回复
     */
    private function getTradingResponse(array $entities, int $userId): array
    {
        $response = "关于交易的常见问题：\n\n";
        $response .= "📈 **交易类型**\n";
        $response .= "• 现货交易：买卖数字货币现货\n";
        $response .= "• 杠杆交易：使用杠杆放大收益\n";
        $response .= "• 合约交易：期货合约交易\n\n";
        $response .= "💰 **手续费**\n";
        $response .= "• 现货交易：0.1%\n";
        $response .= "• 杠杆交易：0.15%\n";
        $response .= "• 合约交易：0.05%\n\n";
        $response .= "需要了解具体交易对信息吗？";

        return [
            'type' => self::MSG_TYPE_TEXT,
            'content' => $response,
            'suggestions' => [
                '查看交易对',
                '手续费详情',
                '交易教程',
                '风险提示'
            ]
        ];
    }

    /**
     * 充值相关回复
     */
    private function getDepositResponse(array $entities, int $userId): array
    {
        $response = "💰 **充值指南**\n\n";
        $response .= "**支持的充值方式：**\n";
        $response .= "• 数字货币充值（推荐）\n";
        $response .= "• 银行卡充值\n";
        $response .= "• 第三方支付\n\n";
        $response .= "**充值到账时间：**\n";
        $response .= "• BTC/ETH：1-3个确认\n";
        $response .= "• USDT：1个确认\n";
        $response .= "• 银行卡：5-30分钟\n\n";
        $response .= "⚠️ 请确保充值地址正确，错误地址将导致资产丢失！";

        return [
            'type' => self::MSG_TYPE_TEXT,
            'content' => $response,
            'suggestions' => [
                '获取充值地址',
                '充值记录查询',
                '充值未到账',
                '联系客服'
            ]
        ];
    }

    /**
     * 提现相关回复
     */
    private function getWithdrawResponse(array $entities, int $userId): array
    {
        $response = "💸 **提现指南**\n\n";
        $response .= "**提现要求：**\n";
        $response .= "• 完成实名认证\n";
        $response .= "• 绑定提现地址\n";
        $response .= "• 通过安全验证\n\n";
        $response .= "**提现限额：**\n";
        $response .= "• 单笔最小：0.001 BTC\n";
        $response .= "• 单日最大：10 BTC\n";
        $response .= "• 手续费：0.0005 BTC\n\n";
        $response .= "**到账时间：**\n";
        $response .= "• 通常在1-2小时内处理完成";

        return [
            'type' => self::MSG_TYPE_TEXT,
            'content' => $response,
            'suggestions' => [
                '提现地址管理',
                '提现记录查询',
                '提现未到账',
                '手续费说明'
            ]
        ];
    }

    /**
     * 安全相关回复
     */
    private function getSecurityResponse(array $entities, int $userId): array
    {
        $response = "🔒 **安全设置指南**\n\n";
        $response .= "**推荐安全设置：**\n";
        $response .= "• ✅ 设置强密码\n";
        $response .= "• ✅ 开启双重验证(2FA)\n";
        $response .= "• ✅ 绑定手机号码\n";
        $response .= "• ✅ 设置资金密码\n";
        $response .= "• ✅ 添加提现白名单\n\n";
        $response .= "**安全提醒：**\n";
        $response .= "• 不要在公共网络登录\n";
        $response .= "• 定期更换密码\n";
        $response .= "• 谨防钓鱼网站";

        return [
            'type' => self::MSG_TYPE_TEXT,
            'content' => $response,
            'suggestions' => [
                '开启2FA',
                '修改密码',
                '绑定手机',
                '安全检测'
            ]
        ];
    }

    /**
     * 投诉处理回复
     */
    private function getComplaintResponse(string $sentiment, int $userId): array
    {
        if ($sentiment === self::SENTIMENT_NEGATIVE) {
            // 负面情绪，转人工客服
            $this->escalateToHuman($userId, '用户情绪负面，需要人工处理');
            
            $response = "非常抱歉给您带来不便！我已经为您转接人工客服，专业客服将在3分钟内为您提供服务。\n\n";
            $response .= "在等待期间，您也可以：\n";
            $response .= "• 详细描述遇到的问题\n";
            $response .= "• 提供相关截图或订单号\n";
            $response .= "• 留下您的联系方式";
        } else {
            $response = "感谢您的反馈！我们非常重视每一位用户的意见。\n\n";
            $response .= "请详细描述您遇到的问题，我会尽力为您解决：\n";
            $response .= "• 问题发生的时间\n";
            $response .= "• 具体的操作步骤\n";
            $response .= "• 相关的订单号或截图";
        }

        return [
            'type' => self::MSG_TYPE_TEXT,
            'content' => $response,
            'suggestions' => [
                '详细描述问题',
                '上传截图',
                '提供订单号',
                '转人工客服'
            ]
        ];
    }

    /**
     * 未知意图回复
     */
    private function getUnknownResponse(array $context): array
    {
        $responses = [
            '抱歉，我没有完全理解您的问题。能否请您换个方式描述一下？',
            '我正在学习中，这个问题可能需要人工客服来帮助您。',
            '您可以尝试使用下方的快捷选项，或者直接转接人工客服。'
        ];

        return [
            'type' => self::MSG_TYPE_TEXT,
            'content' => $responses[array_rand($responses)],
            'suggestions' => [
                '账户问题',
                '交易咨询',
                '充值提现',
                '转人工客服'
            ]
        ];
    }

    /**
     * 获取快捷回复选项
     */
    private function getQuickReplies(string $intent): array
    {
        $quickReplies = [
            self::INTENT_GREETING => ['账户问题', '交易咨询', '充值提现', '安全设置'],
            self::INTENT_ACCOUNT => ['修改密码', '实名认证', '安全设置', '联系客服'],
            self::INTENT_TRADING => ['查看交易对', '手续费详情', '交易教程', '风险提示'],
            self::INTENT_DEPOSIT => ['获取充值地址', '充值记录', '充值未到账', '联系客服'],
            self::INTENT_WITHDRAW => ['提现地址', '提现记录', '提现未到账', '手续费说明'],
            self::INTENT_SECURITY => ['开启2FA', '修改密码', '绑定手机', '安全检测'],
            self::INTENT_COMPLAINT => ['详细描述', '上传截图', '提供订单号', '转人工客服']
        ];

        return $quickReplies[$intent] ?? ['转人工客服', '常见问题', '联系我们'];
    }

    /**
     * 预处理消息
     */
    private function preprocessMessage(string $message): string
    {
        // 去除多余空格
        $message = preg_replace('/\s+/', ' ', trim($message));
        
        // 转换为小写（保留原始消息用于显示）
        return strtolower($message);
    }

    /**
     * 获取用户上下文
     */
    private function getUserContext(int $userId): array
    {
        $cacheKey = "ai_context:{$userId}";
        $context = Cache::get($cacheKey);
        
        return $context ?: [
            'last_intent' => '',
            'last_entities' => [],
            'conversation_count' => 0,
            'last_message_time' => 0
        ];
    }

    /**
     * 更新用户上下文
     */
    private function updateUserContext(int $userId, string $userMessage, array $aiResponse): void
    {
        $context = $this->getUserContext($userId);
        
        $context['last_intent'] = $this->recognizeIntent($userMessage, $context);
        $context['last_entities'] = $this->extractEntities($userMessage);
        $context['conversation_count']++;
        $context['last_message_time'] = time();
        
        $cacheKey = "ai_context:{$userId}";
        Cache::set($cacheKey, $context, 3600); // 1小时过期
    }

    /**
     * 记录消息
     */
    private function logMessage(int $userId, string $content, string $sender, string $type): void
    {
        try {
            Db::name('ai_chat_logs')->insert([
                'user_id' => $userId,
                'sender' => $sender,
                'content' => $content,
                'type' => $type,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            Log::error('记录AI聊天日志失败：' . $e->getMessage());
        }
    }

    /**
     * 转接人工客服
     */
    private function escalateToHuman(int $userId, string $reason): void
    {
        try {
            Db::name('human_escalations')->insert([
                'user_id' => $userId,
                'reason' => $reason,
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            // 通知人工客服
            $this->notifyHumanAgent($userId, $reason);
            
        } catch (\Exception $e) {
            Log::error('转接人工客服失败：' . $e->getMessage());
        }
    }

    /**
     * 通知人工客服
     */
    private function notifyHumanAgent(int $userId, string $reason): void
    {
        // 发送通知给在线客服
        // 这里可以集成WebSocket或其他实时通知方式
        Log::info("用户 {$userId} 需要人工客服，原因：{$reason}");
    }

    /**
     * 获取用户信息
     */
    private function getUserInfo(int $userId): array
    {
        return Db::name('users')
            ->field('status, kyc_status, created_at')
            ->where('id', $userId)
            ->find() ?: [];
    }

    /**
     * 加载知识库
     */
    private function loadKnowledgeBase(): array
    {
        // 这里应该从数据库或文件加载知识库
        // 简化实现，返回基础知识库
        return [
            'faq' => [
                '如何注册账户' => '点击注册按钮，填写邮箱和密码即可注册',
                '如何充值' => '登录后进入资产页面，选择充值币种获取地址',
                '如何交易' => '进入交易页面，选择交易对进行买卖操作'
            ],
            'keywords' => [
                '注册' => '账户注册相关问题',
                '充值' => '资金充值相关问题',
                '交易' => '交易操作相关问题'
            ]
        ];
    }

    /**
     * 获取错误回复
     */
    private function getErrorResponse(): array
    {
        return [
            'type' => self::MSG_TYPE_TEXT,
            'content' => '抱歉，系统暂时无法处理您的请求，请稍后重试或联系人工客服。',
            'suggestions' => ['转人工客服', '稍后重试']
        ];
    }
}
