<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Cache;
use think\facade\Log;
use think\facade\Db;
use think\facade\View;

/**
 * 邮箱验证服务类
 */
class EmailVerificationService
{
    // 验证类型
    const TYPE_REGISTER = 'register';
    const TYPE_LOGIN = 'login';
    const TYPE_RESET_PASSWORD = 'reset_password';
    const TYPE_CHANGE_EMAIL = 'change_email';
    const TYPE_WITHDRAW = 'withdraw';
    const TYPE_SECURITY = 'security';

    private $emailService;

    public function __construct()
    {
        $this->emailService = new EmailService();
    }

    /**
     * 发送邮箱验证码
     */
    public function sendVerificationCode(string $email, string $type = self::TYPE_REGISTER, array $extraData = []): array
    {
        try {
            // 验证邮箱格式
            if (!$this->validateEmail($email)) {
                return ['code' => 0, 'msg' => '邮箱格式不正确'];
            }

            // 检查发送频率限制
            $rateLimitResult = $this->checkRateLimit($email, $type);
            if (!$rateLimitResult['allow']) {
                return ['code' => 0, 'msg' => $rateLimitResult['msg']];
            }

            // 生成验证码
            $code = $this->generateVerificationCode();

            // 发送验证邮件
            $result = $this->sendVerificationEmail($email, $code, $type, $extraData);

            if ($result['success']) {
                // 保存验证码到缓存
                $this->saveVerificationCode($email, $code, $type);
                
                // 记录发送日志
                $this->logEmailRecord($email, $type, $code, 'success');

                return [
                    'code' => 1,
                    'msg' => '验证邮件发送成功',
                    'data' => [
                        'email' => $this->maskEmail($email),
                        'expires_in' => 1800 // 30分钟
                    ]
                ];
            } else {
                $this->logEmailRecord($email, $type, $code, 'failed', $result['error']);
                return ['code' => 0, 'msg' => '验证邮件发送失败：' . $result['error']];
            }

        } catch (\Exception $e) {
            Log::error('邮箱验证码发送异常：' . $e->getMessage());
            return ['code' => 0, 'msg' => '系统异常，请稍后重试'];
        }
    }

    /**
     * 发送邮箱验证链接
     */
    public function sendVerificationLink(string $email, string $type = self::TYPE_REGISTER, array $extraData = []): array
    {
        try {
            // 验证邮箱格式
            if (!$this->validateEmail($email)) {
                return ['code' => 0, 'msg' => '邮箱格式不正确'];
            }

            // 检查发送频率限制
            $rateLimitResult = $this->checkRateLimit($email, $type);
            if (!$rateLimitResult['allow']) {
                return ['code' => 0, 'msg' => $rateLimitResult['msg']];
            }

            // 生成验证令牌
            $token = $this->generateVerificationToken();

            // 发送验证邮件
            $result = $this->sendVerificationLinkEmail($email, $token, $type, $extraData);

            if ($result['success']) {
                // 保存验证令牌到缓存
                $this->saveVerificationToken($email, $token, $type);
                
                // 记录发送日志
                $this->logEmailRecord($email, $type, $token, 'success');

                return [
                    'code' => 1,
                    'msg' => '验证邮件发送成功',
                    'data' => [
                        'email' => $this->maskEmail($email),
                        'expires_in' => 1800 // 30分钟
                    ]
                ];
            } else {
                $this->logEmailRecord($email, $type, $token, 'failed', $result['error']);
                return ['code' => 0, 'msg' => '验证邮件发送失败：' . $result['error']];
            }

        } catch (\Exception $e) {
            Log::error('邮箱验证链接发送异常：' . $e->getMessage());
            return ['code' => 0, 'msg' => '系统异常，请稍后重试'];
        }
    }

    /**
     * 验证邮箱验证码
     */
    public function verifyCode(string $email, string $code, string $type = self::TYPE_REGISTER): array
    {
        try {
            $cacheKey = "email_code:{$type}:{$email}";
            $savedCode = Cache::get($cacheKey);

            if (!$savedCode) {
                return ['code' => 0, 'msg' => '验证码已过期或不存在'];
            }

            if ($savedCode !== $code) {
                // 记录验证失败
                $this->logVerificationAttempt($email, $code, $type, false);
                return ['code' => 0, 'msg' => '验证码错误'];
            }

            // 验证成功，删除验证码
            Cache::delete($cacheKey);
            $this->logVerificationAttempt($email, $code, $type, true);

            // 更新用户邮箱验证状态
            if ($type === self::TYPE_REGISTER) {
                $this->updateEmailVerificationStatus($email);
            }

            return [
                'code' => 1,
                'msg' => '验证成功',
                'data' => [
                    'email' => $email,
                    'verified_at' => date('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            Log::error('邮箱验证码验证异常：' . $e->getMessage());
            return ['code' => 0, 'msg' => '验证失败'];
        }
    }

    /**
     * 验证邮箱验证链接
     */
    public function verifyToken(string $token): array
    {
        try {
            $cacheKey = "email_token:{$token}";
            $tokenData = Cache::get($cacheKey);

            if (!$tokenData) {
                return ['code' => 0, 'msg' => '验证链接已过期或无效'];
            }

            // 验证成功，删除令牌
            Cache::delete($cacheKey);
            $this->logVerificationAttempt($tokenData['email'], $token, $tokenData['type'], true);

            // 更新用户邮箱验证状态
            if ($tokenData['type'] === self::TYPE_REGISTER) {
                $this->updateEmailVerificationStatus($tokenData['email']);
            }

            return [
                'code' => 1,
                'msg' => '邮箱验证成功',
                'data' => [
                    'email' => $tokenData['email'],
                    'type' => $tokenData['type'],
                    'verified_at' => date('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            Log::error('邮箱验证链接验证异常：' . $e->getMessage());
            return ['code' => 0, 'msg' => '验证失败'];
        }
    }

    /**
     * 发送验证码邮件
     */
    private function sendVerificationEmail(string $email, string $code, string $type, array $extraData): array
    {
        $subject = $this->getEmailSubject($type);
        $template = $this->getEmailTemplate($type);
        
        $templateData = array_merge([
            'code' => $code,
            'email' => $email,
            'type' => $type,
            'expires_in' => 30,
            'site_name' => 'GVD交易平台',
            'site_url' => request()->domain(),
            'current_time' => date('Y-m-d H:i:s'),
            'ip' => request()->ip()
        ], $extraData);

        return $this->emailService->sendTemplateEmail($email, $subject, $template, $templateData);
    }

    /**
     * 发送验证链接邮件
     */
    private function sendVerificationLinkEmail(string $email, string $token, string $type, array $extraData): array
    {
        $subject = $this->getEmailSubject($type);
        $template = $this->getEmailTemplate($type . '_link');
        
        $verificationUrl = request()->domain() . "/api/email/verify?token={$token}";
        
        $templateData = array_merge([
            'token' => $token,
            'verification_url' => $verificationUrl,
            'email' => $email,
            'type' => $type,
            'expires_in' => 30,
            'site_name' => 'GVD交易平台',
            'site_url' => request()->domain(),
            'current_time' => date('Y-m-d H:i:s'),
            'ip' => request()->ip()
        ], $extraData);

        return $this->emailService->sendTemplateEmail($email, $subject, $template, $templateData);
    }

    /**
     * 检查发送频率限制
     */
    private function checkRateLimit(string $email, string $type): array
    {
        $cacheKey = "email_rate_limit:{$email}";
        $attempts = Cache::get($cacheKey, 0);

        // 每小时最多发送3次
        if ($attempts >= 3) {
            return ['allow' => false, 'msg' => '发送过于频繁，请1小时后再试'];
        }

        // 检查最近一次发送时间（120秒内不能重复发送）
        $lastSendKey = "email_last_send:{$type}:{$email}";
        $lastSendTime = Cache::get($lastSendKey);
        if ($lastSendTime && (time() - $lastSendTime) < 120) {
            return ['allow' => false, 'msg' => '请2分钟后再试'];
        }

        return ['allow' => true, 'msg' => ''];
    }

    /**
     * 保存验证码到缓存
     */
    private function saveVerificationCode(string $email, string $code, string $type): void
    {
        $cacheKey = "email_code:{$type}:{$email}";
        Cache::set($cacheKey, $code, 1800); // 30分钟有效期

        // 更新发送频率计数
        $rateLimitKey = "email_rate_limit:{$email}";
        $attempts = Cache::get($rateLimitKey, 0);
        Cache::set($rateLimitKey, $attempts + 1, 3600); // 1小时

        // 记录最后发送时间
        $lastSendKey = "email_last_send:{$type}:{$email}";
        Cache::set($lastSendKey, time(), 3600);
    }

    /**
     * 保存验证令牌到缓存
     */
    private function saveVerificationToken(string $email, string $token, string $type): void
    {
        $cacheKey = "email_token:{$token}";
        $tokenData = [
            'email' => $email,
            'type' => $type,
            'created_at' => time()
        ];
        Cache::set($cacheKey, $tokenData, 1800); // 30分钟有效期

        // 更新发送频率计数
        $rateLimitKey = "email_rate_limit:{$email}";
        $attempts = Cache::get($rateLimitKey, 0);
        Cache::set($rateLimitKey, $attempts + 1, 3600); // 1小时

        // 记录最后发送时间
        $lastSendKey = "email_last_send:{$type}:{$email}";
        Cache::set($lastSendKey, time(), 3600);
    }

    /**
     * 生成验证码
     */
    private function generateVerificationCode(int $length = 6): string
    {
        return str_pad((string)mt_rand(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
    }

    /**
     * 生成验证令牌
     */
    private function generateVerificationToken(): string
    {
        return bin2hex(random_bytes(32));
    }

    /**
     * 验证邮箱格式
     */
    private function validateEmail(string $email): bool
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * 邮箱脱敏
     */
    private function maskEmail(string $email): string
    {
        $parts = explode('@', $email);
        if (count($parts) !== 2) {
            return $email;
        }
        
        $username = $parts[0];
        $domain = $parts[1];
        
        $usernameLength = strlen($username);
        if ($usernameLength <= 2) {
            $maskedUsername = str_repeat('*', $usernameLength);
        } else {
            $maskedUsername = substr($username, 0, 1) . str_repeat('*', $usernameLength - 2) . substr($username, -1);
        }
        
        return $maskedUsername . '@' . $domain;
    }

    /**
     * 获取邮件主题
     */
    private function getEmailSubject(string $type): string
    {
        $subjects = [
            self::TYPE_REGISTER => '【GVD交易平台】邮箱验证',
            self::TYPE_LOGIN => '【GVD交易平台】登录验证',
            self::TYPE_RESET_PASSWORD => '【GVD交易平台】密码重置',
            self::TYPE_CHANGE_EMAIL => '【GVD交易平台】邮箱变更验证',
            self::TYPE_WITHDRAW => '【GVD交易平台】提现验证',
            self::TYPE_SECURITY => '【GVD交易平台】安全验证'
        ];

        return $subjects[$type] ?? '【GVD交易平台】邮箱验证';
    }

    /**
     * 获取邮件模板
     */
    private function getEmailTemplate(string $type): string
    {
        $templates = [
            self::TYPE_REGISTER => 'email/verification_code',
            self::TYPE_REGISTER . '_link' => 'email/verification_link',
            self::TYPE_LOGIN => 'email/login_code',
            self::TYPE_RESET_PASSWORD => 'email/reset_password',
            self::TYPE_CHANGE_EMAIL => 'email/change_email',
            self::TYPE_WITHDRAW => 'email/withdraw_code',
            self::TYPE_SECURITY => 'email/security_code'
        ];

        return $templates[$type] ?? 'email/verification_code';
    }

    /**
     * 更新用户邮箱验证状态
     */
    private function updateEmailVerificationStatus(string $email): void
    {
        try {
            Db::name('users')
                ->where('email', $email)
                ->update([
                    'email_verified' => 1,
                    'email_verified_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
        } catch (\Exception $e) {
            Log::error('更新邮箱验证状态失败：' . $e->getMessage());
        }
    }

    /**
     * 记录邮件发送日志
     */
    private function logEmailRecord(string $email, string $type, string $code, string $status, string $error = ''): void
    {
        try {
            Db::name('email_records')->insert([
                'email' => $email,
                'type' => $type,
                'code' => $code,
                'status' => $status,
                'error_msg' => $error,
                'ip' => request()->ip(),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            Log::error('记录邮件日志失败：' . $e->getMessage());
        }
    }

    /**
     * 记录验证尝试日志
     */
    private function logVerificationAttempt(string $email, string $code, string $type, bool $success): void
    {
        try {
            Db::name('email_verifications')->insert([
                'email' => $email,
                'code' => $code,
                'type' => $type,
                'success' => $success ? 1 : 0,
                'ip' => request()->ip(),
                'user_agent' => request()->header('user-agent'),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            Log::error('记录邮箱验证日志失败：' . $e->getMessage());
        }
    }
}
