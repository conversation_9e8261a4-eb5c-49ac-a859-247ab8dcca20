<?php

return [
    // 默认队列连接
    'default' => env('queue.driver', 'redis'),

    // 队列连接配置
    'connections' => [
        'sync' => [
            'type' => 'sync',
        ],
        'database' => [
            'type' => 'database',
            'queue' => 'default',
            'table' => 'jobs',
            'connection' => null,
        ],
        'redis' => [
            'type' => 'redis',
            'queue' => 'default',
            'host' => env('redis.host', '127.0.0.1'),
            'port' => env('redis.port', 6379),
            'password' => env('redis.password', ''),
            'select' => env('queue.redis_select', 2),
            'timeout' => 0,
            'persistent' => false,
        ],
    ],

    // 失败任务配置
    'failed' => [
        'type' => 'database',
        'table' => 'failed_jobs',
    ],

    // 队列任务配置
    'jobs' => [
        // 邮件发送队列
        'email' => [
            'queue' => 'email',
            'delay' => 0,
            'sleep' => 3,
            'maxTries' => 3,
            'memory' => 128,
            'timeout' => 60,
        ],
        
        // 短信发送队列
        'sms' => [
            'queue' => 'sms',
            'delay' => 0,
            'sleep' => 3,
            'maxTries' => 3,
            'memory' => 128,
            'timeout' => 60,
        ],
        
        // 交易处理队列
        'trade' => [
            'queue' => 'trade',
            'delay' => 0,
            'sleep' => 1,
            'maxTries' => 5,
            'memory' => 256,
            'timeout' => 120,
        ],
        
        // 提币处理队列
        'withdraw' => [
            'queue' => 'withdraw',
            'delay' => 0,
            'sleep' => 5,
            'maxTries' => 3,
            'memory' => 256,
            'timeout' => 300,
        ],
        
        // 量化策略执行队列
        'quant' => [
            'queue' => 'quant',
            'delay' => 0,
            'sleep' => 10,
            'maxTries' => 3,
            'memory' => 512,
            'timeout' => 600,
        ],
        
        // AI预测队列
        'ai' => [
            'queue' => 'ai',
            'delay' => 0,
            'sleep' => 30,
            'maxTries' => 2,
            'memory' => 1024,
            'timeout' => 1800,
        ],
        
        // 数据同步队列
        'sync' => [
            'queue' => 'sync',
            'delay' => 0,
            'sleep' => 5,
            'maxTries' => 3,
            'memory' => 256,
            'timeout' => 300,
        ],
        
        // 清理任务队列
        'cleanup' => [
            'queue' => 'cleanup',
            'delay' => 0,
            'sleep' => 60,
            'maxTries' => 1,
            'memory' => 128,
            'timeout' => 300,
        ],
    ],
];
