/* 管理端样式 - 现代化设计 */

/* ==================== 管理端布局 ==================== */
.admin-layout {
  display: flex;
  min-height: 100vh;
  background: var(--bg-primary);
}

.admin-sidebar {
  width: 280px;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  padding: 20px 0;
  overflow-y: auto;
}

.admin-main {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* ==================== 侧边栏头部 ==================== */
.sidebar-header {
  padding: 0 20px 20px;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 20px;
}

.sidebar-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.sidebar-header p {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
}

/* ==================== 管理导航 ==================== */
.admin-nav {
  padding: 0 10px;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 4px;
  border-radius: var(--border-radius-lg);
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.2s ease;
  position: relative;
}

.nav-item:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.nav-item.active {
  background: rgba(240, 185, 11, 0.1);
  color: var(--color-primary);
}

.nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: var(--color-primary);
  border-radius: 0 2px 2px 0;
}

.nav-icon {
  font-size: 18px;
  margin-right: 12px;
  width: 20px;
  text-align: center;
}

.nav-text {
  font-size: 14px;
  font-weight: 500;
}

/* ==================== 页面头部 ==================== */
.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.page-header p {
  margin: 0;
  font-size: 14px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* ==================== 统计卡片 ==================== */
.stat-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 20px;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-right: 16px;
}

.stat-icon.bg-primary {
  background: rgba(240, 185, 11, 0.1);
  color: var(--color-primary);
}

.stat-icon.bg-success {
  background: rgba(14, 203, 129, 0.1);
  color: var(--color-success);
}

.stat-icon.bg-info {
  background: rgba(24, 144, 255, 0.1);
  color: var(--color-info);
}

.stat-icon.bg-warning {
  background: rgba(255, 143, 0, 0.1);
  color: var(--color-warning);
}

.stat-icon.bg-danger {
  background: rgba(246, 70, 93, 0.1);
  color: var(--color-danger);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
  font-family: 'Monaco', 'Menlo', monospace;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0 0 2px 0;
}

.stat-desc {
  font-size: 12px;
  color: var(--text-tertiary);
}

/* ==================== 统计网格 ==================== */
.stat-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-item .stat-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.stat-item .stat-value {
  font-size: 16px;
  font-weight: 600;
  font-family: 'Monaco', 'Menlo', monospace;
}

/* ==================== 快速操作 ==================== */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.quick-action {
  display: flex;
  align-items: center;
  padding: 16px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  text-decoration: none;
  transition: all 0.2s ease;
}

.quick-action:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin-right: 12px;
}

.action-content h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 2px 0;
}

.action-content p {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0;
}

/* ==================== 搜索表单 ==================== */
.search-form {
  background: var(--bg-secondary);
  padding: 16px;
  border-radius: var(--border-radius-lg);
}

.search-form .row {
  align-items: end;
}

/* ==================== 模态框 ==================== */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background: var(--bg-card);
  border-radius: var(--border-radius-lg);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-lg);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid var(--border-color);
}

/* ==================== 徽章 ==================== */
.badge {
  display: inline-block;
  padding: 2px 8px;
  font-size: 11px;
  font-weight: 500;
  border-radius: 12px;
  text-transform: uppercase;
}

.badge-primary {
  background: rgba(240, 185, 11, 0.1);
  color: var(--color-primary);
}

.badge-success {
  background: rgba(14, 203, 129, 0.1);
  color: var(--color-success);
}

.badge-info {
  background: rgba(24, 144, 255, 0.1);
  color: var(--color-info);
}

.badge-warning {
  background: rgba(255, 143, 0, 0.1);
  color: var(--color-warning);
}

.badge-danger {
  background: rgba(246, 70, 93, 0.1);
  color: var(--color-danger);
}

/* ==================== 按钮组 ==================== */
.btn-group {
  display: flex;
  gap: 4px;
}

.btn-group .btn {
  border-radius: var(--border-radius);
}

.btn-group .btn:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.btn-group .btn:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.btn-group .btn:not(:first-child):not(:last-child) {
  border-radius: 0;
}

/* ==================== 表格增强 ==================== */
.table-responsive {
  overflow-x: auto;
}

.table th {
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table .balance-amount {
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: 600;
}

/* ==================== 分页 ==================== */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin: 20px 0;
}

.pagination a,
.pagination span {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.2s ease;
}

.pagination a:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.pagination .current {
  background: var(--color-primary);
  color: #000;
  border-color: var(--color-primary);
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 992px) {
  .admin-layout {
    flex-direction: column;
  }
  
  .admin-sidebar {
    width: 100%;
    padding: 16px 0;
  }
  
  .admin-nav {
    display: flex;
    overflow-x: auto;
    padding: 0 16px;
  }
  
  .nav-item {
    flex-shrink: 0;
    margin-right: 8px;
    margin-bottom: 0;
  }
  
  .admin-main {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .quick-actions {
    grid-template-columns: 1fr;
  }
  
  .stat-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-content {
    width: 95%;
    margin: 20px;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .btn-group {
    flex-direction: column;
  }
}

@media (max-width: 576px) {
  .page-header h1 {
    font-size: 24px;
  }

  .stat-card {
    padding: 16px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .stat-value {
    font-size: 20px;
  }
}

/* ==================== 代理端专用样式 ==================== */

/* 实时卡片 */
.realtime-card {
  position: relative;
  overflow: hidden;
}

.realtime-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-success));
  animation: pulse-line 2s infinite;
}

@keyframes pulse-line {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

.realtime-indicator {
  font-size: 8px;
  margin-left: 4px;
}

/* 合约监控面板 */
.contract-monitor {
  min-height: 400px;
}

.monitor-header {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.monitor-tabs {
  display: flex;
  padding: 0;
}

.monitor-tab {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.monitor-tab:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.monitor-tab.active {
  color: var(--color-primary);
  background: rgba(240, 185, 11, 0.1);
}

.monitor-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--color-primary);
}

.tab-count {
  background: var(--color-primary);
  color: #000;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 600;
  min-width: 18px;
  text-align: center;
}

.monitor-content {
  position: relative;
}

.monitor-panel {
  display: none;
  padding: 16px;
  max-height: 350px;
  overflow-y: auto;
}

.monitor-panel.active {
  display: block;
}

/* 合约项目 */
.contract-item {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 16px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
}

.contract-item:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

.contract-item:last-child {
  margin-bottom: 0;
}

/* 批量操作样式 */
.batch-operations {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  margin-bottom: 16px;
}

.batch-select {
  display: flex;
  align-items: center;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: var(--text-primary);
}

.checkbox-label input[type="checkbox"] {
  margin-right: 8px;
  width: 16px;
  height: 16px;
  accent-color: var(--color-primary);
}

.batch-actions {
  display: flex;
  gap: 8px;
}

.contract-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.contract-select {
  margin-right: 12px;
}

.contract-select input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--color-primary);
}

.contract-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.contract-user {
  font-weight: 600;
  color: var(--text-primary);
}

.contract-symbol {
  background: var(--bg-tertiary);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  color: var(--text-secondary);
}

.contract-direction {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

.contract-direction.buy {
  background: rgba(14, 203, 129, 0.1);
  color: var(--color-success);
}

.contract-direction.sell {
  background: rgba(246, 70, 93, 0.1);
  color: var(--color-danger);
}

.contract-amount {
  font-weight: 600;
  color: var(--color-primary);
  font-family: 'Monaco', 'Menlo', monospace;
}

.contract-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
  margin-bottom: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.detail-row span:first-child {
  color: var(--text-secondary);
}

.detail-row span:last-child {
  color: var(--text-primary);
  font-family: 'Monaco', 'Menlo', monospace;
}

.contract-progress {
  margin-bottom: 12px;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: var(--border-color);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 4px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-success));
  transition: width 0.3s ease;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: var(--text-secondary);
}

.contract-actions {
  display: flex;
  gap: 8px;
}

.contract-result {
  font-weight: 600;
  font-family: 'Monaco', 'Menlo', monospace;
}

.contract-result.profit {
  color: var(--color-success);
}

.contract-result.loss {
  color: var(--color-danger);
}

/* 提币项目 */
.withdrawal-item {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 16px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
}

.withdrawal-item:hover {
  border-color: var(--color-warning);
  box-shadow: var(--shadow-sm);
}

.withdrawal-item:last-child {
  margin-bottom: 0;
}

.withdrawal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.withdrawal-user {
  font-weight: 600;
  color: var(--text-primary);
}

.withdrawal-amount {
  font-weight: 600;
  color: var(--color-warning);
  font-family: 'Monaco', 'Menlo', monospace;
}

.withdrawal-details {
  margin-bottom: 12px;
}

.withdrawal-address {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 11px;
  cursor: pointer;
}

.withdrawal-actions {
  display: flex;
  gap: 8px;
}

/* 财务记录表格 */
.table .text-success {
  color: var(--color-success) !important;
}

.table .text-danger {
  color: var(--color-danger) !important;
}

/* 空状态优化 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  color: var(--text-secondary);
  font-size: 14px;
}

/* 卡片操作 */
.card-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 分页包装器 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 16px 0;
}

/* ==================== 充值提醒样式 ==================== */
.deposit-alert-card {
  border: 2px solid var(--color-success);
  box-shadow: 0 4px 20px rgba(14, 203, 129, 0.2);
}

.deposit-alert-flash {
  animation: depositFlash 2s ease-in-out;
}

@keyframes depositFlash {
  0%, 100% {
    box-shadow: 0 4px 20px rgba(14, 203, 129, 0.2);
  }
  50% {
    box-shadow: 0 8px 30px rgba(14, 203, 129, 0.5);
    transform: translateY(-2px);
  }
}

.deposit-alert-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: rgba(14, 203, 129, 0.05);
  border: 1px solid rgba(14, 203, 129, 0.2);
  border-radius: var(--border-radius-lg);
  margin-bottom: 12px;
  transition: all 0.2s ease;
}

.deposit-alert-item:hover {
  background: rgba(14, 203, 129, 0.1);
  transform: translateX(4px);
}

.deposit-alert-item:last-child {
  margin-bottom: 0;
}

.alert-icon {
  font-size: 32px;
  margin-right: 16px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.alert-details {
  display: flex;
  align-items: center;
  gap: 16px;
}

.alert-details .amount {
  font-size: 18px;
  font-weight: 700;
  color: var(--color-success);
  font-family: 'Monaco', 'Menlo', monospace;
}

.alert-details .time {
  font-size: 12px;
  color: var(--text-secondary);
}

.alert-actions {
  margin-left: 16px;
}

/* ==================== 一键控制样式 ==================== */
.control-stats {
  background: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: 16px;
}

.stat-mini {
  text-align: center;
  padding: 12px;
  background: var(--bg-tertiary);
  border-radius: var(--border-radius);
}

.stat-mini .stat-value {
  font-size: 20px;
  font-weight: 600;
  color: var(--color-primary);
  margin-bottom: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
}

.stat-mini .stat-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.control-options h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16px;
}

.control-buttons {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.control-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 16px;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  background: var(--bg-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.control-btn:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.control-btn.all-win {
  border-color: var(--color-success);
}

.control-btn.all-win:hover {
  background: rgba(14, 203, 129, 0.1);
  border-color: var(--color-success);
}

.control-btn.all-loss {
  border-color: var(--color-danger);
}

.control-btn.all-loss:hover {
  background: rgba(246, 70, 93, 0.1);
  border-color: var(--color-danger);
}

.control-btn.smart-control {
  border-color: var(--color-primary);
}

.control-btn.smart-control:hover {
  background: rgba(240, 185, 11, 0.1);
  border-color: var(--color-primary);
}

.control-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.control-text h5 {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.control-text p {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
}

.today-records h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.record-stats {
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  padding: 16px;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--border-color);
  font-size: 14px;
}

.record-item:last-child {
  border-bottom: none;
}

.record-item span:first-child {
  color: var(--text-secondary);
}

.record-item span:last-child {
  font-weight: 600;
  font-family: 'Monaco', 'Menlo', monospace;
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 12px;
  height: 12px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ==================== 用户详情样式 ==================== */
.user-detail-info {
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  padding: 16px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--border-color);
  font-size: 14px;
}

.info-row:last-child {
  border-bottom: none;
}

.info-row span:first-child {
  color: var(--text-secondary);
  font-weight: 500;
}

.info-row span:last-child {
  color: var(--text-primary);
  font-weight: 600;
  font-family: 'Monaco', 'Menlo', monospace;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .contract-details {
    grid-template-columns: 1fr;
  }

  .contract-actions {
    flex-direction: column;
  }

  .withdrawal-actions {
    flex-direction: column;
  }

  .monitor-tabs {
    flex-direction: column;
  }

  .monitor-tab {
    border-bottom: 1px solid var(--border-color);
  }

  .monitor-tab:last-child {
    border-bottom: none;
  }

  .control-buttons {
    grid-template-columns: 1fr;
  }

  .alert-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .deposit-alert-item {
    flex-direction: column;
    text-align: center;
  }

  .alert-actions {
    margin-left: 0;
    margin-top: 12px;
  }
}
