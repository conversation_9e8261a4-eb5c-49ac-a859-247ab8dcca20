<?php
/**
 * GVD客服系统配置检查脚本
 * 用于检查客服系统的配置和依赖是否正确
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 颜色输出
class ColorOutput {
    const RED = "\033[0;31m";
    const GREEN = "\033[0;32m";
    const YELLOW = "\033[1;33m";
    const BLUE = "\033[0;34m";
    const NC = "\033[0m"; // No Color
    
    public static function print($message, $color = self::NC) {
        echo $color . '[' . date('Y-m-d H:i:s') . '] ' . $message . self::NC . PHP_EOL;
    }
    
    public static function success($message) {
        self::print($message, self::GREEN);
    }
    
    public static function error($message) {
        self::print($message, self::RED);
    }
    
    public static function warning($message) {
        self::print($message, self::YELLOW);
    }
    
    public static function info($message) {
        self::print($message, self::BLUE);
    }
}

class CustomerServiceChecker {
    private $projectRoot;
    private $errors = [];
    private $warnings = [];
    
    public function __construct() {
        $this->projectRoot = dirname(__DIR__);
    }
    
    /**
     * 运行所有检查
     */
    public function runAllChecks() {
        ColorOutput::info("开始检查GVD客服系统配置...");
        
        $this->checkPHPVersion();
        $this->checkPHPExtensions();
        $this->checkDirectories();
        $this->checkFiles();
        $this->checkDatabase();
        $this->checkWebSocketConfig();
        $this->checkPermissions();
        $this->checkDependencies();
        
        $this->printSummary();
    }
    
    /**
     * 检查PHP版本
     */
    private function checkPHPVersion() {
        ColorOutput::info("检查PHP版本...");
        
        $version = PHP_VERSION;
        $minVersion = '7.4.0';
        
        if (version_compare($version, $minVersion, '>=')) {
            ColorOutput::success("PHP版本: {$version} ✓");
        } else {
            $this->errors[] = "PHP版本过低: {$version}，需要 >= {$minVersion}";
            ColorOutput::error("PHP版本过低: {$version}，需要 >= {$minVersion}");
        }
    }
    
    /**
     * 检查PHP扩展
     */
    private function checkPHPExtensions() {
        ColorOutput::info("检查PHP扩展...");
        
        $requiredExtensions = [
            'pdo',
            'pdo_mysql',
            'json',
            'mbstring',
            'openssl',
            'curl',
            'fileinfo',
            'gd',
            'zip'
        ];
        
        $recommendedExtensions = [
            'redis',
            'swoole',
            'event'
        ];
        
        foreach ($requiredExtensions as $ext) {
            if (extension_loaded($ext)) {
                ColorOutput::success("扩展 {$ext}: 已安装 ✓");
            } else {
                $this->errors[] = "缺少必需的PHP扩展: {$ext}";
                ColorOutput::error("缺少必需的PHP扩展: {$ext}");
            }
        }
        
        foreach ($recommendedExtensions as $ext) {
            if (extension_loaded($ext)) {
                ColorOutput::success("扩展 {$ext}: 已安装 ✓");
            } else {
                $this->warnings[] = "建议安装PHP扩展: {$ext}";
                ColorOutput::warning("建议安装PHP扩展: {$ext}");
            }
        }
    }
    
    /**
     * 检查目录结构
     */
    private function checkDirectories() {
        ColorOutput::info("检查目录结构...");
        
        $requiredDirs = [
            'app/common/service',
            'app/command',
            'config',
            'public/static/js',
            'public/static/css',
            'runtime/logs',
            'runtime/pids',
            'uploads/customer'
        ];
        
        foreach ($requiredDirs as $dir) {
            $fullPath = $this->projectRoot . '/' . $dir;
            if (is_dir($fullPath)) {
                ColorOutput::success("目录 {$dir}: 存在 ✓");
            } else {
                $this->warnings[] = "目录不存在: {$dir}";
                ColorOutput::warning("目录不存在: {$dir}，将尝试创建...");
                
                if (mkdir($fullPath, 0755, true)) {
                    ColorOutput::success("目录 {$dir}: 创建成功 ✓");
                } else {
                    $this->errors[] = "无法创建目录: {$dir}";
                    ColorOutput::error("无法创建目录: {$dir}");
                }
            }
        }
    }
    
    /**
     * 检查关键文件
     */
    private function checkFiles() {
        ColorOutput::info("检查关键文件...");
        
        $requiredFiles = [
            'app/common/service/CustomerService.php',
            'app/command/WebSocketServer.php',
            'app/command/CustomerCleanup.php',
            'config/customer.php',
            'public/static/js/customer-service.js',
            'public/static/css/customer-service.css'
        ];
        
        foreach ($requiredFiles as $file) {
            $fullPath = $this->projectRoot . '/' . $file;
            if (file_exists($fullPath)) {
                ColorOutput::success("文件 {$file}: 存在 ✓");
            } else {
                $this->errors[] = "关键文件缺失: {$file}";
                ColorOutput::error("关键文件缺失: {$file}");
            }
        }
    }
    
    /**
     * 检查数据库配置和表结构
     */
    private function checkDatabase() {
        ColorOutput::info("检查数据库配置...");
        
        try {
            // 加载ThinkPHP配置
            $configFile = $this->projectRoot . '/config/database.php';
            if (!file_exists($configFile)) {
                $this->errors[] = "数据库配置文件不存在: config/database.php";
                ColorOutput::error("数据库配置文件不存在");
                return;
            }
            
            $config = include $configFile;
            $dbConfig = $config['connections']['mysql'];
            
            // 测试数据库连接
            $dsn = "mysql:host={$dbConfig['hostname']};port={$dbConfig['hostport']};dbname={$dbConfig['database']}";
            $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
            
            ColorOutput::success("数据库连接: 正常 ✓");
            
            // 检查客服相关表
            $requiredTables = [
                'gvd_customer_sessions',
                'gvd_customer_messages',
                'gvd_customer_quick_replies',
                'gvd_customer_online_status',
                'gvd_customer_statistics'
            ];
            
            foreach ($requiredTables as $table) {
                $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
                if ($stmt->rowCount() > 0) {
                    ColorOutput::success("数据表 {$table}: 存在 ✓");
                } else {
                    $this->errors[] = "数据表不存在: {$table}";
                    ColorOutput::error("数据表不存在: {$table}");
                }
            }
            
        } catch (Exception $e) {
            $this->errors[] = "数据库连接失败: " . $e->getMessage();
            ColorOutput::error("数据库连接失败: " . $e->getMessage());
        }
    }
    
    /**
     * 检查WebSocket配置
     */
    private function checkWebSocketConfig() {
        ColorOutput::info("检查WebSocket配置...");
        
        $configFile = $this->projectRoot . '/config/customer.php';
        if (!file_exists($configFile)) {
            $this->errors[] = "客服配置文件不存在: config/customer.php";
            ColorOutput::error("客服配置文件不存在");
            return;
        }
        
        $config = include $configFile;
        $wsConfig = $config['websocket'];
        
        // 检查端口是否被占用
        $host = $wsConfig['host'];
        $port = $wsConfig['port'];
        
        $socket = @fsockopen($host, $port, $errno, $errstr, 1);
        if ($socket) {
            fclose($socket);
            $this->warnings[] = "WebSocket端口 {$port} 已被占用";
            ColorOutput::warning("WebSocket端口 {$port} 已被占用");
        } else {
            ColorOutput::success("WebSocket端口 {$port}: 可用 ✓");
        }
        
        // 检查配置项
        $requiredConfigs = ['host', 'port', 'worker_num', 'max_connections'];
        foreach ($requiredConfigs as $key) {
            if (isset($wsConfig[$key])) {
                ColorOutput::success("WebSocket配置 {$key}: 已设置 ✓");
            } else {
                $this->warnings[] = "WebSocket配置缺失: {$key}";
                ColorOutput::warning("WebSocket配置缺失: {$key}");
            }
        }
    }
    
    /**
     * 检查文件权限
     */
    private function checkPermissions() {
        ColorOutput::info("检查文件权限...");
        
        $writableDirs = [
            'runtime',
            'runtime/logs',
            'runtime/pids',
            'uploads',
            'uploads/customer'
        ];
        
        foreach ($writableDirs as $dir) {
            $fullPath = $this->projectRoot . '/' . $dir;
            if (is_writable($fullPath)) {
                ColorOutput::success("目录权限 {$dir}: 可写 ✓");
            } else {
                $this->errors[] = "目录不可写: {$dir}";
                ColorOutput::error("目录不可写: {$dir}");
            }
        }
    }
    
    /**
     * 检查依赖
     */
    private function checkDependencies() {
        ColorOutput::info("检查依赖包...");
        
        $composerFile = $this->projectRoot . '/composer.json';
        $vendorDir = $this->projectRoot . '/vendor';
        
        if (!file_exists($composerFile)) {
            $this->warnings[] = "composer.json文件不存在";
            ColorOutput::warning("composer.json文件不存在");
            return;
        }
        
        if (!is_dir($vendorDir)) {
            $this->errors[] = "vendor目录不存在，请运行 composer install";
            ColorOutput::error("vendor目录不存在，请运行 composer install");
            return;
        }
        
        ColorOutput::success("Composer依赖: 已安装 ✓");
        
        // 检查Workerman
        $workermanPath = $vendorDir . '/workerman/workerman';
        if (is_dir($workermanPath)) {
            ColorOutput::success("Workerman: 已安装 ✓");
        } else {
            $this->errors[] = "Workerman未安装，请运行 composer require workerman/workerman";
            ColorOutput::error("Workerman未安装");
        }
    }
    
    /**
     * 打印检查摘要
     */
    private function printSummary() {
        echo PHP_EOL;
        ColorOutput::info("=== 检查摘要 ===");
        
        if (empty($this->errors) && empty($this->warnings)) {
            ColorOutput::success("✓ 所有检查通过，客服系统配置正常！");
        } else {
            if (!empty($this->errors)) {
                ColorOutput::error("发现 " . count($this->errors) . " 个错误:");
                foreach ($this->errors as $error) {
                    ColorOutput::error("  - " . $error);
                }
            }
            
            if (!empty($this->warnings)) {
                ColorOutput::warning("发现 " . count($this->warnings) . " 个警告:");
                foreach ($this->warnings as $warning) {
                    ColorOutput::warning("  - " . $warning);
                }
            }
            
            echo PHP_EOL;
            if (!empty($this->errors)) {
                ColorOutput::error("请修复上述错误后重新检查");
                exit(1);
            } else {
                ColorOutput::warning("建议处理上述警告以获得更好的性能");
            }
        }
        
        echo PHP_EOL;
        ColorOutput::info("检查完成！");
    }
}

// 运行检查
$checker = new CustomerServiceChecker();
$checker->runAllChecks();
