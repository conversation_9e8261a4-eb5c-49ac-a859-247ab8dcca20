<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 成交记录模型
 */
class Trade extends Model
{
    protected $name = 'gvd_trades';
    
    protected $type = [
        'price' => 'float',
        'amount' => 'float',
        'total' => 'float',
        'buy_fee' => 'float',
        'sell_fee' => 'float'
    ];

    /**
     * 获取交易对的最新成交记录
     */
    public static function getLatestTrades(string $symbol, int $limit = 50): array
    {
        return self::where('symbol', $symbol)
                  ->order('created_at', 'desc')
                  ->limit($limit)
                  ->select()
                  ->toArray();
    }

    /**
     * 获取用户的成交记录
     */
    public static function getUserTrades(int $userId, string $symbol = '', int $page = 1, int $limit = 20): array
    {
        $query = self::where(function($query) use ($userId) {
            $query->where('buy_user_id', $userId)->whereOr('sell_user_id', $userId);
        });
        
        if (!empty($symbol)) {
            $query->where('symbol', $symbol);
        }
        
        $trades = $query->order('created_at', 'desc')
                       ->paginate([
                           'list_rows' => $limit,
                           'page' => $page
                       ]);
        
        return [
            'data' => $trades->items(),
            'total' => $trades->total(),
            'page' => $page,
            'limit' => $limit
        ];
    }

    /**
     * 获取24小时成交统计
     */
    public static function get24hTradeStats(string $symbol): array
    {
        $endTime = date('Y-m-d H:i:s');
        $startTime = date('Y-m-d H:i:s', time() - 86400);
        
        $stats = self::where('symbol', $symbol)
                    ->where('created_at', '>=', $startTime)
                    ->where('created_at', '<=', $endTime)
                    ->field([
                        'COUNT(*) as trade_count',
                        'SUM(amount) as total_volume',
                        'SUM(total) as total_amount',
                        'MAX(price) as high_price',
                        'MIN(price) as low_price'
                    ])
                    ->find();
        
        return $stats ? $stats->toArray() : [
            'trade_count' => 0,
            'total_volume' => 0,
            'total_amount' => 0,
            'high_price' => 0,
            'low_price' => 0
        ];
    }

    /**
     * 获取成交记录（用于前端显示）
     */
    public static function getTradesForDisplay(string $symbol, int $limit = 50): array
    {
        $trades = self::getLatestTrades($symbol, $limit);
        
        $result = [];
        foreach ($trades as $trade) {
            $result[] = [
                'price' => (float)$trade['price'],
                'amount' => (float)$trade['amount'],
                'total' => (float)$trade['total'],
                'time' => strtotime($trade['created_at']) * 1000,
                'type' => 'buy' // 简化处理
            ];
        }
        
        return $result;
    }

    /**
     * 创建成交记录
     */
    public static function createTrade(array $data): bool
    {
        try {
            $tradeData = [
                'symbol' => $data['symbol'],
                'trade_id' => 'T' . date('YmdHis') . mt_rand(1000, 9999),
                'buy_order_id' => $data['buy_order_id'],
                'sell_order_id' => $data['sell_order_id'],
                'buy_user_id' => $data['buy_user_id'],
                'sell_user_id' => $data['sell_user_id'],
                'price' => $data['price'],
                'amount' => $data['amount'],
                'total' => $data['amount'] * $data['price'],
                'buy_fee' => $data['buy_fee'] ?? 0,
                'sell_fee' => $data['sell_fee'] ?? 0,
                'created_at' => date('Y-m-d H:i:s')
            ];

            self::create($tradeData);
            return true;

        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取交易统计
     */
    public static function getTradeStatistics(string $symbol = '', int $days = 7): array
    {
        $query = self::query();
        
        if (!empty($symbol)) {
            $query->where('symbol', $symbol);
        }
        
        $startTime = date('Y-m-d H:i:s', time() - ($days * 86400));
        $query->where('created_at', '>=', $startTime);
        
        $stats = $query->field([
            'COUNT(*) as total_trades',
            'COUNT(DISTINCT symbol) as active_pairs',
            'SUM(amount) as total_volume',
            'SUM(total) as total_amount',
            'AVG(price) as avg_price'
        ])->find();
        
        return $stats ? $stats->toArray() : [
            'total_trades' => 0,
            'active_pairs' => 0,
            'total_volume' => 0,
            'total_amount' => 0,
            'avg_price' => 0
        ];
    }

    /**
     * 获取用户交易统计
     */
    public static function getUserTradeStats(int $userId): array
    {
        $buyStats = self::where('buy_user_id', $userId)
                       ->field([
                           'COUNT(*) as buy_count',
                           'SUM(amount) as buy_volume',
                           'SUM(total) as buy_amount',
                           'SUM(buy_fee) as buy_fee'
                       ])
                       ->find();

        $sellStats = self::where('sell_user_id', $userId)
                        ->field([
                            'COUNT(*) as sell_count',
                            'SUM(amount) as sell_volume',
                            'SUM(total) as sell_amount',
                            'SUM(sell_fee) as sell_fee'
                        ])
                        ->find();

        $buyData = $buyStats ? $buyStats->toArray() : [
            'buy_count' => 0, 'buy_volume' => 0, 'buy_amount' => 0, 'buy_fee' => 0
        ];
        
        $sellData = $sellStats ? $sellStats->toArray() : [
            'sell_count' => 0, 'sell_volume' => 0, 'sell_amount' => 0, 'sell_fee' => 0
        ];

        return [
            'total_trades' => $buyData['buy_count'] + $sellData['sell_count'],
            'buy_count' => $buyData['buy_count'],
            'sell_count' => $sellData['sell_count'],
            'buy_volume' => $buyData['buy_volume'],
            'sell_volume' => $sellData['sell_volume'],
            'total_volume' => $buyData['buy_volume'] + $sellData['sell_volume'],
            'buy_amount' => $buyData['buy_amount'],
            'sell_amount' => $sellData['sell_amount'],
            'total_amount' => $buyData['buy_amount'] + $sellData['sell_amount'],
            'total_fee' => $buyData['buy_fee'] + $sellData['sell_fee']
        ];
    }
}
