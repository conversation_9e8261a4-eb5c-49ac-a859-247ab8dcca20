<?php
declare (strict_types = 1);

namespace app\api\controller;

use think\Request;
use app\common\service\LeverageService;
use app\common\service\FuturesService;
use app\common\service\RiskControlService;

/**
 * 合约交易控制器
 */
class Contract extends BaseController
{
    protected $leverageService;
    protected $futuresService;
    protected $riskControlService;

    public function __construct()
    {
        parent::__construct();
        $this->leverageService = new LeverageService();
        $this->futuresService = new FuturesService();
        $this->riskControlService = new RiskControlService();
    }

    /**
     * 获取合约列表
     */
    public function getContracts()
    {
        try {
            $result = $this->futuresService->getContracts();
            return $this->success($result['msg'], $result['data']);
        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 创建杠杆订单
     */
    public function createLeverageOrder()
    {
        $data = Request::post();
        
        // 参数验证
        $validate = [
            'symbol' => 'require',
            'direction' => 'require|in:long,short',
            'leverage' => 'require|integer|between:1,100',
            'margin' => 'require|float|gt:0'
        ];

        if (!$this->validateParams($data, $validate)) {
            return $this->error('参数验证失败');
        }

        // 风控检测
        $riskResult = $this->riskControlService->checkTradeRisk($this->userId, $data);
        if ($riskResult['code'] === 1 && !$riskResult['data']['allow_trade']) {
            return $this->error('操作被风控系统阻止：' . implode('、', $riskResult['data']['risk_factors']));
        }

        $data['user_id'] = $this->userId;
        $result = $this->leverageService->createOrder($data);

        if ($result['code'] === 1) {
            return $this->success($result['msg'], $result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 创建期货订单
     */
    public function createFuturesOrder()
    {
        $data = Request::post();
        
        // 参数验证
        $validate = [
            'symbol' => 'require',
            'side' => 'require|in:long,short',
            'order_type' => 'require|in:market,limit',
            'quantity' => 'require|float|gt:0',
            'margin' => 'require|float|gt:0',
            'leverage' => 'require|integer|between:1,100'
        ];

        if (!$this->validateParams($data, $validate)) {
            return $this->error('参数验证失败');
        }

        // 风控检测
        $riskResult = $this->riskControlService->checkTradeRisk($this->userId, $data);
        if ($riskResult['code'] === 1 && !$riskResult['data']['allow_trade']) {
            return $this->error('操作被风控系统阻止：' . implode('、', $riskResult['data']['risk_factors']));
        }

        $data['user_id'] = $this->userId;
        $result = $this->futuresService->createOrder($data);

        if ($result['code'] === 1) {
            return $this->success($result['msg'], $result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 平仓杠杆订单
     */
    public function closeLeverageOrder()
    {
        $orderId = Request::post('order_id/d', 0);
        
        if (!$orderId) {
            return $this->error('订单ID不能为空');
        }

        $result = $this->leverageService->closeOrder($orderId, $this->userId);

        if ($result['code'] === 1) {
            return $this->success($result['msg'], $result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 平仓期货持仓
     */
    public function closeFuturesPosition()
    {
        $positionId = Request::post('position_id/d', 0);
        $quantity = Request::post('quantity/f', 0);
        
        if (!$positionId) {
            return $this->error('持仓ID不能为空');
        }

        $result = $this->futuresService->closePosition($positionId, $this->userId, $quantity);

        if ($result['code'] === 1) {
            return $this->success($result['msg'], $result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 设置止盈止损
     */
    public function setStopLossTakeProfit()
    {
        $data = Request::post();
        
        $validate = [
            'order_id' => 'require|integer',
            'stop_loss' => 'float',
            'take_profit' => 'float'
        ];

        if (!$this->validateParams($data, $validate)) {
            return $this->error('参数验证失败');
        }

        $result = $this->leverageService->setStopLossTakeProfit(
            $data['order_id'],
            $this->userId,
            $data['stop_loss'] ?? 0,
            $data['take_profit'] ?? 0
        );

        if ($result['code'] === 1) {
            return $this->success($result['msg'], $result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取杠杆订单列表
     */
    public function getLeverageOrders()
    {
        $params = Request::get();
        $result = $this->leverageService->getUserOrders($this->userId, $params);

        if ($result['code'] === 1) {
            return $this->success($result['msg'], $result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取期货持仓
     */
    public function getFuturesPositions()
    {
        $params = Request::get();
        $result = $this->futuresService->getUserPositions($this->userId, $params);

        if ($result['code'] === 1) {
            return $this->success($result['msg'], $result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取合约详情
     */
    public function getContractDetail()
    {
        $symbol = Request::get('symbol', '');
        
        if (empty($symbol)) {
            return $this->error('合约代码不能为空');
        }

        try {
            $result = $this->futuresService->getContractDetail($symbol);
            return $this->success('获取成功', $result);
        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 获取资金费率
     */
    public function getFundingRate()
    {
        $symbol = Request::get('symbol', '');
        
        if (empty($symbol)) {
            return $this->error('合约代码不能为空');
        }

        try {
            $result = $this->futuresService->calculateFundingRate($symbol);
            
            if ($result['code'] === 1) {
                return $this->success($result['msg'], $result['data']);
            } else {
                return $this->error($result['msg']);
            }
        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 获取持仓风险
     */
    public function getPositionRisk()
    {
        try {
            // 获取用户所有持仓
            $leveragePositions = $this->leverageService->getUserOrders($this->userId, ['status' => 'open']);
            $futuresPositions = $this->futuresService->getUserPositions($this->userId, ['status' => 'open']);

            $totalRisk = 0;
            $riskPositions = [];

            // 计算杠杆订单风险
            if ($leveragePositions['code'] === 1) {
                foreach ($leveragePositions['data']['orders'] as $order) {
                    $risk = $this->calculatePositionRisk($order, 'leverage');
                    $totalRisk += $risk['risk_score'];
                    if ($risk['risk_level'] >= 3) {
                        $riskPositions[] = $risk;
                    }
                }
            }

            // 计算期货持仓风险
            if ($futuresPositions['code'] === 1) {
                foreach ($futuresPositions['data'] as $position) {
                    $risk = $this->calculatePositionRisk($position, 'futures');
                    $totalRisk += $risk['risk_score'];
                    if ($risk['risk_level'] >= 3) {
                        $riskPositions[] = $risk;
                    }
                }
            }

            return $this->success('获取成功', [
                'total_risk_score' => $totalRisk,
                'risk_level' => $this->getRiskLevel($totalRisk),
                'high_risk_positions' => $riskPositions,
                'risk_warning' => $totalRisk > 80 ? '持仓风险过高，建议减仓' : ''
            ]);

        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 计算持仓风险
     */
    private function calculatePositionRisk(array $position, string $type): array
    {
        $riskScore = 0;
        $riskFactors = [];

        // 杠杆风险
        if ($position['leverage'] > 50) {
            $riskScore += 30;
            $riskFactors[] = '高杠杆';
        } elseif ($position['leverage'] > 20) {
            $riskScore += 15;
            $riskFactors[] = '中等杠杆';
        }

        // 盈亏风险
        $pnlRatio = 0;
        if ($type === 'leverage' && isset($position['unrealized_pnl'])) {
            $pnlRatio = $position['unrealized_pnl'] / $position['margin'];
        } elseif ($type === 'futures' && isset($position['unrealized_pnl'])) {
            $pnlRatio = $position['unrealized_pnl'] / $position['margin'];
        }

        if ($pnlRatio < -0.5) {
            $riskScore += 40;
            $riskFactors[] = '大幅亏损';
        } elseif ($pnlRatio < -0.2) {
            $riskScore += 20;
            $riskFactors[] = '亏损较大';
        }

        // 保证金率风险
        if (isset($position['margin_ratio']) && $position['margin_ratio'] < 0.1) {
            $riskScore += 50;
            $riskFactors[] = '保证金率过低';
        }

        return [
            'position_id' => $position['id'] ?? $position['order_id'],
            'symbol' => $position['symbol'],
            'type' => $type,
            'risk_score' => $riskScore,
            'risk_level' => $this->getRiskLevel($riskScore),
            'risk_factors' => $riskFactors
        ];
    }

    /**
     * 获取风险等级
     */
    private function getRiskLevel(int $score): int
    {
        if ($score >= 80) return 4; // 极高风险
        if ($score >= 50) return 3; // 高风险
        if ($score >= 20) return 2; // 中风险
        return 1; // 低风险
    }
}
