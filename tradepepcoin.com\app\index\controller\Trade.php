<?php
declare (strict_types = 1);

namespace app\index\controller;

use app\BaseController;
use app\common\model\TradingPair;
use app\common\model\Order;
use app\common\model\UserAsset;
use app\common\service\TradeService;
use think\facade\View;
use think\facade\Session;

/**
 * 币币交易控制器
 */
class Trade extends BaseController
{
    protected $tradeService;

    public function initialize()
    {
        parent::initialize();
        $this->tradeService = new TradeService();
        
        // 交易页面允许游客访问，但交易操作需要登录
    }

    /**
     * 交易首页
     */
    public function index()
    {
        $symbol = Request::get('symbol', 'BTC/USDT');

        // 获取交易对信息
        $tradingPair = TradingPair::getBySymbol($symbol);
        if (!$tradingPair) {
            $tradingPair = TradingPair::where('status', 1)->find();
            $symbol = $tradingPair ? $tradingPair->symbol : 'BTC/USDT';
        }

        // 获取所有交易对
        $tradingPairs = TradingPair::getEnabled();

        // 获取用户资产
        $userId = Session::get('user_id');
        $userAssets = [];
        if ($userId) {
            $userAssets = UserAsset::where('user_id', $userId)
                                  ->select()
                                  ->toArray();
        }

        // 获取24小时行情数据
        $marketData = $this->tradeService->getMarketData($symbol);

        // 获取深度数据
        $depthData = $this->tradeService->getDepthData($symbol);

        // 获取最新成交记录
        $recentTrades = $this->tradeService->getRecentTrades($symbol, 50);

        // 获取用户当前委托订单
        $activeOrders = [];
        if ($userId) {
            $activeOrders = Order::where('user_id', $userId)
                                ->where('symbol', $symbol)
                                ->where('status', 'pending')
                                ->order('created_at desc')
                                ->limit(20)
                                ->select();
        }

        View::assign([
            'symbol' => $symbol,
            'trading_pair' => $tradingPair,
            'trading_pairs' => $tradingPairs,
            'user_assets' => $userAssets,
            'market_data' => $marketData,
            'depth_data' => $depthData,
            'recent_trades' => $recentTrades,
            'active_orders' => $activeOrders,
            'user_id' => $userId,
            'title' => '币币交易 - ' . $symbol . ' - GVD数字货币交易平台'
        ]);

        return View::fetch('trade/index');
    }

    /**
     * 创建交易订单
     */
    public function createOrder()
    {
        if (!Request::isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $userId = Session::get('user_id');
        if (!$userId) {
            return json(['code' => 0, 'msg' => '请先登录']);
        }

        $data = Request::post();

        // 验证数据
        $validate = Validate::rule([
            'symbol' => 'require',
            'type' => 'require|in:buy,sell',
            'price' => 'require|float|gt:0',
            'amount' => 'require|float|gt:0'
        ])->message([
            'symbol.require' => '请选择交易对',
            'type.require' => '请选择交易类型',
            'type.in' => '交易类型参数错误',
            'price.require' => '请输入交易价格',
            'price.float' => '价格格式错误',
            'price.gt' => '价格必须大于0',
            'amount.require' => '请输入交易数量',
            'amount.float' => '数量格式错误',
            'amount.gt' => '数量必须大于0'
        ]);

        if (!$validate->check($data)) {
            return json(['code' => 0, 'msg' => $validate->getError()]);
        }

        // 创建订单
        $result = $this->tradeService->createOrder($userId, $data);

        return json($result);
    }

    /**
     * 取消订单
     */
    public function cancelOrder()
    {
        if (!Request::isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $userId = Session::get('user_id');
        if (!$userId) {
            return json(['code' => 0, 'msg' => '请先登录']);
        }

        $orderId = Request::post('order_id');
        if (!$orderId) {
            return json(['code' => 0, 'msg' => '订单ID不能为空']);
        }

        $result = $this->tradeService->cancelOrder($userId, $orderId);

        return json($result);
    }

    /**
     * 获取交易历史
     */
    public function history()
    {
        $userId = Session::get('user_id');
        if (!$userId) {
            return json(['code' => 0, 'msg' => '请先登录']);
        }

        $symbol = Request::get('symbol', '');
        $page = Request::get('page', 1);
        $limit = 20;

        $orders = Order::where('user_id', $userId);

        if ($symbol) {
            $orders = $orders->where('symbol', $symbol);
        }

        $orders = $orders->order('created_at desc')
                        ->paginate([
                            'list_rows' => $limit,
                            'page' => $page
                        ]);

        if (Request::isAjax()) {
            return json([
                'code' => 1,
                'data' => $orders->items(),
                'total' => $orders->total()
            ]);
        }

        View::assign([
            'orders' => $orders,
            'symbol' => $symbol,
            'title' => '交易历史 - GVD数字货币交易平台'
        ]);

        return View::fetch('trade/history');
    }

    /**
     * 获取K线数据
     */
    public function klineData()
    {
        $symbol = Request::get('symbol', 'BTC/USDT');
        $interval = Request::get('interval', '1m');
        $limit = Request::get('limit', 500);

        $klineData = $this->tradeService->getKlineData($symbol, $interval, $limit);

        return json([
            'code' => 1,
            'data' => $klineData
        ]);
    }

    /**
     * 获取深度数据
     */
    public function depthData()
    {
        $symbol = Request::get('symbol', 'BTC/USDT');

        $depthData = $this->tradeService->getDepthData($symbol);

        return json([
            'code' => 1,
            'data' => $depthData
        ]);
    }

    /**
     * 获取最新成交
     */
    public function recentTrades()
    {
        $symbol = Request::get('symbol', 'BTC/USDT');
        $limit = Request::get('limit', 50);

        $trades = $this->tradeService->getRecentTrades($symbol, $limit);

        return json([
            'code' => 1,
            'data' => $trades
        ]);
    }
                              ->where('total', '>', 0)
                              ->select();

        View::assign([
            'tradingPair' => $tradingPair,
            'tradingPairs' => $tradingPairs,
            'userAssets' => $userAssets,
            'symbol' => $symbol,
            'title' => '币币交易'
        ]);

        return View::fetch();
    }

    /**
     * 现货交易页面
     */
    public function spot()
    {
        return $this->index();
    }

    /**
     * 下单
     */
    public function order()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $data = $this->request->post();
        
        // 验证参数
        $validate = [
            'symbol' => 'require',
            'type' => 'require|in:buy,sell',
            'order_type' => 'require|in:market,limit',
            'amount' => 'require|float|gt:0',
        ];

        if ($data['order_type'] == 'limit') {
            $validate['price'] = 'require|float|gt:0';
        }

        $result = $this->validate($data, $validate);
        if ($result !== true) {
            return json(['code' => 0, 'msg' => $result]);
        }

        // 执行下单
        $userId = Session::get('user_id');
        $result = $this->tradeService->createOrder($userId, $data);

        return json($result);
    }

    /**
     * 取消订单
     */
    public function cancel()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $orderId = input('order_id');
        if (empty($orderId)) {
            return json(['code' => 0, 'msg' => '订单ID不能为空']);
        }

        $userId = Session::get('user_id');
        $result = $this->tradeService->cancelOrder($userId, $orderId);

        return json($result);
    }

    /**
     * 交易历史
     */
    public function history()
    {
        $userId = Session::get('user_id');
        $symbol = input('symbol', '');
        $status = input('status', '');
        $page = input('page/d', 1);
        $limit = input('limit/d', 20);

        $where = ['user_id' => $userId];
        
        if (!empty($symbol)) {
            $where['symbol'] = $symbol;
        }
        
        if ($status !== '') {
            $where['status'] = $status;
        }

        $orders = Order::where($where)
                      ->order('created_at', 'desc')
                      ->paginate([
                          'list_rows' => $limit,
                          'page' => $page
                      ]);

        if ($this->request->isAjax()) {
            return json([
                'code' => 1,
                'data' => $orders->items(),
                'total' => $orders->total(),
                'page' => $page,
                'limit' => $limit
            ]);
        }

        View::assign([
            'orders' => $orders,
            'symbol' => $symbol,
            'status' => $status,
            'title' => '交易历史'
        ]);

        return View::fetch();
    }

    /**
     * 获取订单簿
     */
    public function orderBook()
    {
        $symbol = input('symbol', 'BTC/USDT');
        $depth = input('depth/d', 20);

        $result = $this->tradeService->getOrderBook($symbol, $depth);

        return json($result);
    }

    /**
     * 获取最新成交
     */
    public function recentTrades()
    {
        $symbol = input('symbol', 'BTC/USDT');
        $limit = input('limit/d', 50);

        $result = $this->tradeService->getRecentTrades($symbol, $limit);

        return json($result);
    }

    /**
     * 获取K线数据
     */
    public function kline()
    {
        $symbol = input('symbol', 'BTC/USDT');
        $interval = input('interval', '1h');
        $limit = input('limit/d', 100);

        $result = $this->tradeService->getKlineData($symbol, $interval, $limit);

        return json($result);
    }

    /**
     * 获取24小时统计
     */
    public function ticker24hr()
    {
        $symbol = input('symbol', '');

        if (empty($symbol)) {
            // 获取所有交易对的24小时统计
            $result = $this->tradeService->getAllTicker24hr();
        } else {
            // 获取指定交易对的24小时统计
            $result = $this->tradeService->getTicker24hr($symbol);
        }

        return json($result);
    }

    /**
     * 获取用户当前委托
     */
    public function openOrders()
    {
        $userId = Session::get('user_id');
        $symbol = input('symbol', '');

        $where = [
            'user_id' => $userId,
            'status' => ['in', [0, 1]] // 待成交和部分成交
        ];

        if (!empty($symbol)) {
            $where['symbol'] = $symbol;
        }

        $orders = Order::where($where)
                      ->order('created_at', 'desc')
                      ->limit(50)
                      ->select();

        return json([
            'code' => 1,
            'data' => $orders
        ]);
    }

    /**
     * 获取用户交易统计
     */
    public function userStats()
    {
        $userId = Session::get('user_id');
        $symbol = input('symbol', '');

        $where = ['user_id' => $userId];
        if (!empty($symbol)) {
            $where['symbol'] = $symbol;
        }

        // 总交易次数
        $totalTrades = Order::where($where)->where('status', 2)->count();

        // 总交易量
        $totalVolume = Order::where($where)->where('status', 2)->sum('filled_total');

        // 总手续费
        $totalFee = Order::where($where)->where('status', 2)->sum('fee');

        // 今日交易次数
        $todayTrades = Order::where($where)
                           ->where('status', 2)
                           ->whereTime('created_at', 'today')
                           ->count();

        // 今日交易量
        $todayVolume = Order::where($where)
                           ->where('status', 2)
                           ->whereTime('created_at', 'today')
                           ->sum('filled_total');

        return json([
            'code' => 1,
            'data' => [
                'total_trades' => $totalTrades,
                'total_volume' => $totalVolume,
                'total_fee' => $totalFee,
                'today_trades' => $todayTrades,
                'today_volume' => $todayVolume
            ]
        ]);
    }

    // ==================== 老系统兼容功能 ====================

    /**
     * 币币交易买入 - 兼容老系统upbbbuy
     */
    public function upbbbuy()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'info' => '请求方式错误']);
        }

        $symbol = input('symbol', '');
        $price = input('mprice', 0);
        $amount = input('mnum', 0);
        $total = input('musdt', 0);
        $orderType = input('buytype', 1); // 1限价 2市价

        // 验证参数
        if (empty($symbol) || $amount <= 0) {
            return json(['code' => 0, 'info' => '参数错误']);
        }

        if ($orderType == 1 && $price <= 0) {
            return json(['code' => 0, 'info' => '限价单价格必须大于0']);
        }

        $userId = Session::get('user_id');

        // 构造订单数据
        $orderData = [
            'symbol' => $symbol,
            'price' => $price,
            'amount' => $amount,
            'order_type' => $orderType,
        ];

        $result = $this->tradeService->createBuyOrder($userId, $orderData);

        // 兼容老系统返回格式
        return json([
            'code' => $result['code'],
            'info' => $result['msg'],
            'data' => $result['data'] ?? null
        ]);
    }

    /**
     * 币币交易卖出 - 兼容老系统upbbsell
     */
    public function upbbsell()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'info' => '请求方式错误']);
        }

        $symbol = input('symbol', '');
        $price = input('mprice', 0);
        $amount = input('mnum', 0);
        $total = input('musdt', 0);
        $orderType = input('selltype', 1); // 1限价 2市价

        // 验证参数
        if (empty($symbol) || $amount <= 0) {
            return json(['code' => 0, 'info' => '参数错误']);
        }

        if ($orderType == 1 && $price <= 0) {
            return json(['code' => 0, 'info' => '限价单价格必须大于0']);
        }

        $userId = Session::get('user_id');

        // 构造订单数据
        $orderData = [
            'symbol' => $symbol,
            'price' => $price,
            'amount' => $amount,
            'order_type' => $orderType,
        ];

        $result = $this->tradeService->createSellOrder($userId, $orderData);

        // 兼容老系统返回格式
        return json([
            'code' => $result['code'],
            'info' => $result['msg'],
            'data' => $result['data'] ?? null
        ]);
    }

    /**
     * 撤销订单 - 兼容老系统cancelbborder
     */
    public function cancelbborder()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'info' => '请求方式错误']);
        }

        $orderId = input('oid', '');

        if (empty($orderId)) {
            return json(['code' => 0, 'info' => '订单ID不能为空']);
        }

        $userId = Session::get('user_id');
        $result = $this->tradeService->cancelOrder($userId, $orderId);

        // 兼容老系统返回格式
        return json([
            'code' => $result['code'],
            'info' => $result['msg']
        ]);
    }

    /**
     * 币币订单列表 - 兼容老系统bborder
     */
    public function bborder()
    {
        $userId = Session::get('user_id');
        $status = input('status', 1); // 1委托中 2已成交 3已撤销
        $page = input('page', 1);

        $where = ['user_id' => $userId];
        if ($status) {
            $where['status'] = $status;
        }

        $orders = Order::where($where)
                      ->order('created_at', 'desc')
                      ->paginate([
                          'list_rows' => 20,
                          'page' => $page
                      ]);

        if ($this->request->isAjax()) {
            return json([
                'code' => 1,
                'data' => $orders->items(),
                'total' => $orders->total()
            ]);
        }

        View::assign([
            'orders' => $orders,
            'status' => $status,
            'title' => '我的订单'
        ]);

        return View::fetch();
    }

    /**
     * 获取交易深度 - 兼容老系统
     */
    public function getDepth()
    {
        $symbol = input('symbol', 'BTCUSDT');

        // 简化实现，实际项目中应该从订单簿计算
        $depth = [
            'buy' => [
                ['price' => 44900, 'amount' => 1.5],
                ['price' => 44800, 'amount' => 2.3],
                ['price' => 44700, 'amount' => 1.8],
            ],
            'sell' => [
                ['price' => 45100, 'amount' => 1.2],
                ['price' => 45200, 'amount' => 2.1],
                ['price' => 45300, 'amount' => 1.6],
            ]
        ];

        return json(['code' => 1, 'data' => $depth]);
    }

    /**
     * 获取最新成交记录 - 兼容老系统
     */
    public function getLatestTrades()
    {
        $symbol = input('symbol', 'BTCUSDT');
        $limit = input('limit', 20);

        $trades = \app\common\model\Trade::where('symbol', $symbol)
                                        ->order('created_at', 'desc')
                                        ->limit($limit)
                                        ->select();

        return json(['code' => 1, 'data' => $trades]);
    }
}
