<?php
declare (strict_types = 1);

namespace app\middleware;

use Closure;
use think\Request;
use think\Response;
use think\facade\Config;

/**
 * 维护模式检查中间件
 */
class CheckMaintenance
{
    /**
     * 处理请求
     *
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 检查是否开启维护模式
        $maintenanceMode = Config::get('app.maintenance_mode', false);
        
        if ($maintenanceMode) {
            // 允许管理员访问
            $allowedIps = Config::get('app.maintenance_allowed_ips', []);
            $clientIp = $request->ip();
            
            if (!in_array($clientIp, $allowedIps)) {
                if ($request->isAjax()) {
                    return json([
                        'code' => 503,
                        'msg'  => '系统维护中，请稍后访问',
                        'data' => null
                    ])->code(503);
                } else {
                    return Response::create('
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <title>系统维护中</title>
                            <meta charset="utf-8">
                            <style>
                                body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                                .container { max-width: 600px; margin: 0 auto; }
                                h1 { color: #333; }
                                p { color: #666; line-height: 1.6; }
                            </style>
                        </head>
                        <body>
                            <div class="container">
                                <h1>🔧 系统维护中</h1>
                                <p>我们正在对系统进行升级维护，预计很快就会完成。</p>
                                <p>给您带来的不便，我们深表歉意。</p>
                                <p>请稍后再试，谢谢您的理解！</p>
                            </div>
                        </body>
                        </html>
                    ', 'html')->code(503);
                }
            }
        }
        
        return $next($request);
    }
}
