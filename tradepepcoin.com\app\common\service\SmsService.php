<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Cache;

/**
 * 短信服务类
 */
class SmsService
{
    private $apiUrl;
    private $balanceUrl;
    private $sendUrl;
    private $appkey;
    private $appsecret;
    private $appcode;
    private $signature;
    
    public function __construct()
    {
        $this->apiUrl = env('SMS_API_URL', 'http://43.128.110.137:9090');
        $this->balanceUrl = env('SMS_BALANCE_URL', 'http://43.128.110.137:9090/sms/balance/v1');
        $this->sendUrl = env('SMS_SEND_URL', 'http://43.128.110.137:9090/sms/send/v1');
        $this->appkey = env('SMS_APPKEY', 'GR3J2V');
        $this->appsecret = env('SMS_APPSECRET', 'BuGrDN');
        $this->appcode = env('SMS_APPCODE', '1000');
        $this->signature = env('SMS_SIGNATURE', 'GVD');
    }
    
    /**
     * 发送验证码短信
     */
    public function sendVerificationCode(string $phone, string $code): array
    {
        try {
            // 检查发送频率限制
            $limitKey = 'sms_send_limit_' . $phone;
            if (Cache::has($limitKey)) {
                return ['code' => 0, 'msg' => '发送过于频繁，请60秒后再试'];
            }
            
            // 检查余额
            $balanceResult = $this->checkBalance();
            if ($balanceResult['code'] !== 1) {
                return ['code' => 0, 'msg' => '短信服务暂时不可用'];
            }
            
            // 构建短信内容
            $content = "【{$this->signature}】您的验证码是：{$code}，有效期5分钟。请勿泄露给他人。";
            
            // 发送短信
            $result = $this->sendSms($phone, $content);
            
            if ($result['code'] === 1) {
                // 设置发送限制
                Cache::set($limitKey, time(), 60);
                
                // 缓存验证码
                Cache::set('sms_code_' . $phone, $code, 300);
                
                return ['code' => 1, 'msg' => '验证码已发送到您的手机'];
            } else {
                return $result;
            }
            
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '发送失败: ' . $e->getMessage()];
        }
    }
    
    /**
     * 发送通知短信
     */
    public function sendNotification(string $phone, string $message): array
    {
        try {
            $content = "【{$this->signature}】{$message}";
            return $this->sendSms($phone, $content);
            
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '发送失败: ' . $e->getMessage()];
        }
    }
    
    /**
     * 检查短信余额
     */
    public function checkBalance(): array
    {
        try {
            $params = [
                'appkey' => $this->appkey
            ];
            
            $url = $this->balanceUrl . '?' . http_build_query($params);
            
            $response = $this->httpGet($url);
            $result = json_decode($response, true);
            
            if ($result && isset($result['code']) && $result['code'] == 200) {
                return [
                    'code' => 1,
                    'msg' => '查询成功',
                    'data' => [
                        'balance' => $result['data']['balance'] ?? 0,
                        'currency' => $result['data']['currency'] ?? 'CNY'
                    ]
                ];
            } else {
                return [
                    'code' => 0,
                    'msg' => $result['msg'] ?? '查询失败'
                ];
            }
            
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '查询失败: ' . $e->getMessage()];
        }
    }
    
    /**
     * 发送短信核心方法
     */
    private function sendSms(string $phone, string $content): array
    {
        try {
            // 生成签名
            $timestamp = time();
            $nonce = $this->generateNonce();
            $signature = $this->generateSignature($timestamp, $nonce);
            
            $params = [
                'appkey' => $this->appkey,
                'timestamp' => $timestamp,
                'nonce' => $nonce,
                'signature' => $signature,
                'phone' => $phone,
                'content' => $content,
                'appcode' => $this->appcode
            ];
            
            $response = $this->httpPost($this->sendUrl, $params);
            $result = json_decode($response, true);
            
            if ($result && isset($result['code'])) {
                if ($result['code'] == 200) {
                    return [
                        'code' => 1,
                        'msg' => '发送成功',
                        'data' => [
                            'msgid' => $result['data']['msgid'] ?? '',
                            'phone' => $phone
                        ]
                    ];
                } else {
                    return [
                        'code' => 0,
                        'msg' => $result['msg'] ?? '发送失败'
                    ];
                }
            } else {
                return ['code' => 0, 'msg' => '接口返回格式错误'];
            }
            
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '发送失败: ' . $e->getMessage()];
        }
    }
    
    /**
     * 生成随机字符串
     */
    private function generateNonce(int $length = 16): string
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $nonce = '';
        for ($i = 0; $i < $length; $i++) {
            $nonce .= $chars[mt_rand(0, strlen($chars) - 1)];
        }
        return $nonce;
    }
    
    /**
     * 生成签名
     */
    private function generateSignature(int $timestamp, string $nonce): string
    {
        $string = $this->appkey . $timestamp . $nonce . $this->appsecret;
        return md5($string);
    }
    
    /**
     * HTTP GET请求
     */
    private function httpGet(string $url): string
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'GVD SMS Client');
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new \Exception('CURL Error: ' . $error);
        }
        
        if ($httpCode !== 200) {
            throw new \Exception('HTTP Error: ' . $httpCode);
        }
        
        return $response;
    }
    
    /**
     * HTTP POST请求
     */
    private function httpPost(string $url, array $data): string
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'GVD SMS Client');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new \Exception('CURL Error: ' . $error);
        }
        
        if ($httpCode !== 200) {
            throw new \Exception('HTTP Error: ' . $httpCode);
        }
        
        return $response;
    }
    
    /**
     * 验证手机号格式
     */
    public function validatePhone(string $phone): bool
    {
        // 国际手机号验证（支持+86、+1等国家代码）
        $pattern = '/^(\+\d{1,3})?[1-9]\d{6,14}$/';
        return preg_match($pattern, $phone) === 1;
    }
    
    /**
     * 格式化手机号（添加国家代码）
     */
    public function formatPhone(string $phone, string $countryCode = '+86'): string
    {
        // 如果已经有国家代码，直接返回
        if (strpos($phone, '+') === 0) {
            return $phone;
        }
        
        // 中国手机号特殊处理
        if ($countryCode === '+86' && strlen($phone) === 11 && strpos($phone, '1') === 0) {
            return '+86' . $phone;
        }
        
        return $countryCode . $phone;
    }
}
