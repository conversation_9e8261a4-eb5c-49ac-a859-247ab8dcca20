<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

/**
 * 语言包管理服务类
 * 支持在线编辑和管理语言包
 */
class LanguagePackService
{
    // 语言包状态
    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;

    // 翻译状态
    const TRANSLATION_PENDING = 'pending';
    const TRANSLATION_APPROVED = 'approved';
    const TRANSLATION_REJECTED = 'rejected';

    // 翻译质量等级
    const QUALITY_EXCELLENT = 'excellent';
    const QUALITY_GOOD = 'good';
    const QUALITY_FAIR = 'fair';
    const QUALITY_POOR = 'poor';

    private $languageService;

    public function __construct()
    {
        $this->languageService = new LanguageService();
    }

    /**
     * 获取所有语言包
     */
    public function getAllLanguagePacks(): array
    {
        try {
            $languages = Db::name('language_packs')
                ->field('code, name, native_name, status, progress, created_at, updated_at')
                ->order('code asc')
                ->select()
                ->toArray();

            foreach ($languages as &$language) {
                // 计算翻译进度
                $language['progress'] = $this->calculateProgress($language['code']);
                
                // 获取翻译统计
                $language['stats'] = $this->getTranslationStats($language['code']);
            }

            return [
                'code' => 1,
                'msg' => '获取成功',
                'data' => $languages
            ];

        } catch (\Exception $e) {
            Log::error('获取语言包失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 获取语言包详情
     */
    public function getLanguagePackDetail(string $languageCode): array
    {
        try {
            $languagePack = Db::name('language_packs')
                ->where('code', $languageCode)
                ->find();

            if (!$languagePack) {
                return ['code' => 0, 'msg' => '语言包不存在'];
            }

            // 获取所有翻译键
            $translations = Db::name('language_translations')
                ->where('language_code', $languageCode)
                ->order('key_path asc')
                ->select()
                ->toArray();

            // 按模块分组
            $groupedTranslations = $this->groupTranslationsByModule($translations);

            return [
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'language_pack' => $languagePack,
                    'translations' => $groupedTranslations,
                    'stats' => $this->getTranslationStats($languageCode)
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取语言包详情失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 更新翻译
     */
    public function updateTranslation(array $data): array
    {
        try {
            Db::startTrans();

            $translationId = $data['id'] ?? 0;
            $languageCode = $data['language_code'];
            $keyPath = $data['key_path'];
            $value = $data['value'];
            $userId = $data['user_id'];

            // 检查翻译是否存在
            $translation = Db::name('language_translations')
                ->where('id', $translationId)
                ->find();

            if (!$translation) {
                // 创建新翻译
                $translationData = [
                    'language_code' => $languageCode,
                    'key_path' => $keyPath,
                    'value' => $value,
                    'status' => self::TRANSLATION_PENDING,
                    'translator_id' => $userId,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                $translationId = Db::name('language_translations')->insertGetId($translationData);
            } else {
                // 更新现有翻译
                Db::name('language_translations')
                    ->where('id', $translationId)
                    ->update([
                        'value' => $value,
                        'status' => self::TRANSLATION_PENDING,
                        'translator_id' => $userId,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            }

            // 记录翻译历史
            $this->recordTranslationHistory($translationId, $userId, $value, 'update');

            // 自动质量检测
            $qualityScore = $this->checkTranslationQuality($keyPath, $value, $languageCode);

            // 更新质量评分
            Db::name('language_translations')
                ->where('id', $translationId)
                ->update(['quality_score' => $qualityScore]);

            // 清除缓存
            $this->clearLanguageCache($languageCode);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '翻译更新成功',
                'data' => [
                    'translation_id' => $translationId,
                    'quality_score' => $qualityScore
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('更新翻译失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '更新失败'];
        }
    }

    /**
     * 批量导入翻译
     */
    public function importTranslations(string $languageCode, array $translations, int $userId): array
    {
        try {
            Db::startTrans();

            $successCount = 0;
            $failCount = 0;
            $errors = [];

            foreach ($translations as $keyPath => $value) {
                try {
                    // 检查翻译是否已存在
                    $existing = Db::name('language_translations')
                        ->where('language_code', $languageCode)
                        ->where('key_path', $keyPath)
                        ->find();

                    if ($existing) {
                        // 更新现有翻译
                        Db::name('language_translations')
                            ->where('id', $existing['id'])
                            ->update([
                                'value' => $value,
                                'status' => self::TRANSLATION_PENDING,
                                'translator_id' => $userId,
                                'updated_at' => date('Y-m-d H:i:s')
                            ]);

                        $this->recordTranslationHistory($existing['id'], $userId, $value, 'import_update');
                    } else {
                        // 创建新翻译
                        $translationData = [
                            'language_code' => $languageCode,
                            'key_path' => $keyPath,
                            'value' => $value,
                            'status' => self::TRANSLATION_PENDING,
                            'translator_id' => $userId,
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ];

                        $translationId = Db::name('language_translations')->insertGetId($translationData);
                        $this->recordTranslationHistory($translationId, $userId, $value, 'import_create');
                    }

                    $successCount++;

                } catch (\Exception $e) {
                    $failCount++;
                    $errors[] = "键 '{$keyPath}': " . $e->getMessage();
                }
            }

            // 清除缓存
            $this->clearLanguageCache($languageCode);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '导入完成',
                'data' => [
                    'success_count' => $successCount,
                    'fail_count' => $failCount,
                    'errors' => $errors
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('批量导入翻译失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '导入失败'];
        }
    }

    /**
     * 导出语言包
     */
    public function exportLanguagePack(string $languageCode, string $format = 'php'): array
    {
        try {
            $translations = Db::name('language_translations')
                ->where('language_code', $languageCode)
                ->where('status', self::TRANSLATION_APPROVED)
                ->select()
                ->toArray();

            if (empty($translations)) {
                return ['code' => 0, 'msg' => '没有可导出的翻译'];
            }

            // 构建嵌套数组
            $nestedArray = [];
            foreach ($translations as $translation) {
                $this->setNestedValue($nestedArray, $translation['key_path'], $translation['value']);
            }

            // 根据格式生成内容
            switch ($format) {
                case 'php':
                    $content = $this->generatePhpContent($nestedArray);
                    $filename = "{$languageCode}.php";
                    break;

                case 'json':
                    $content = json_encode($nestedArray, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                    $filename = "{$languageCode}.json";
                    break;

                case 'csv':
                    $content = $this->generateCsvContent($translations);
                    $filename = "{$languageCode}.csv";
                    break;

                default:
                    return ['code' => 0, 'msg' => '不支持的导出格式'];
            }

            return [
                'code' => 1,
                'msg' => '导出成功',
                'data' => [
                    'content' => $content,
                    'filename' => $filename,
                    'size' => strlen($content)
                ]
            ];

        } catch (\Exception $e) {
            Log::error('导出语言包失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '导出失败'];
        }
    }

    /**
     * 审核翻译
     */
    public function reviewTranslation(int $translationId, string $action, int $reviewerId, string $comment = ''): array
    {
        try {
            Db::startTrans();

            $translation = Db::name('language_translations')
                ->where('id', $translationId)
                ->find();

            if (!$translation) {
                return ['code' => 0, 'msg' => '翻译不存在'];
            }

            $status = $action === 'approve' ? self::TRANSLATION_APPROVED : self::TRANSLATION_REJECTED;

            // 更新翻译状态
            Db::name('language_translations')
                ->where('id', $translationId)
                ->update([
                    'status' => $status,
                    'reviewer_id' => $reviewerId,
                    'review_comment' => $comment,
                    'reviewed_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            // 记录审核历史
            $this->recordTranslationHistory($translationId, $reviewerId, $translation['value'], $action);

            // 如果审核通过，清除缓存
            if ($status === self::TRANSLATION_APPROVED) {
                $this->clearLanguageCache($translation['language_code']);
            }

            Db::commit();

            return [
                'code' => 1,
                'msg' => '审核完成',
                'data' => ['status' => $status]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('审核翻译失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '审核失败'];
        }
    }

    /**
     * 搜索翻译
     */
    public function searchTranslations(array $params): array
    {
        try {
            $query = Db::name('language_translations')
                ->alias('lt')
                ->join('language_packs lp', 'lt.language_code = lp.code')
                ->field('lt.*, lp.name as language_name');

            // 搜索条件
            if (!empty($params['keyword'])) {
                $keyword = $params['keyword'];
                $query->where(function($q) use ($keyword) {
                    $q->whereOr('lt.key_path', 'like', "%{$keyword}%")
                      ->whereOr('lt.value', 'like', "%{$keyword}%");
                });
            }

            if (!empty($params['language_code'])) {
                $query->where('lt.language_code', $params['language_code']);
            }

            if (!empty($params['status'])) {
                $query->where('lt.status', $params['status']);
            }

            if (!empty($params['module'])) {
                $query->where('lt.key_path', 'like', $params['module'] . '.%');
            }

            // 分页
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 20;

            $total = $query->count();
            $translations = $query->page($page, $limit)
                ->order('lt.updated_at desc')
                ->select()
                ->toArray();

            return [
                'code' => 1,
                'msg' => '搜索成功',
                'data' => [
                    'list' => $translations,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit
                ]
            ];

        } catch (\Exception $e) {
            Log::error('搜索翻译失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '搜索失败'];
        }
    }

    /**
     * 获取翻译历史
     */
    public function getTranslationHistory(int $translationId): array
    {
        try {
            $history = Db::name('language_translation_history')
                ->alias('lth')
                ->join('users u', 'lth.user_id = u.id')
                ->field('lth.*, u.username')
                ->where('lth.translation_id', $translationId)
                ->order('lth.created_at desc')
                ->select()
                ->toArray();

            return [
                'code' => 1,
                'msg' => '获取成功',
                'data' => $history
            ];

        } catch (\Exception $e) {
            Log::error('获取翻译历史失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 计算翻译进度
     */
    private function calculateProgress(string $languageCode): float
    {
        $total = Db::name('language_translations')
            ->where('language_code', $languageCode)
            ->count();

        if ($total === 0) {
            return 0;
        }

        $completed = Db::name('language_translations')
            ->where('language_code', $languageCode)
            ->where('status', self::TRANSLATION_APPROVED)
            ->count();

        return round(($completed / $total) * 100, 2);
    }

    /**
     * 获取翻译统计
     */
    private function getTranslationStats(string $languageCode): array
    {
        $stats = Db::name('language_translations')
            ->where('language_code', $languageCode)
            ->field('status, COUNT(*) as count')
            ->group('status')
            ->select()
            ->toArray();

        $result = [
            'total' => 0,
            'pending' => 0,
            'approved' => 0,
            'rejected' => 0
        ];

        foreach ($stats as $stat) {
            $result['total'] += $stat['count'];
            $result[$stat['status']] = $stat['count'];
        }

        return $result;
    }

    /**
     * 按模块分组翻译
     */
    private function groupTranslationsByModule(array $translations): array
    {
        $grouped = [];

        foreach ($translations as $translation) {
            $parts = explode('.', $translation['key_path']);
            $module = $parts[0];

            if (!isset($grouped[$module])) {
                $grouped[$module] = [];
            }

            $grouped[$module][] = $translation;
        }

        return $grouped;
    }

    /**
     * 设置嵌套数组值
     */
    private function setNestedValue(array &$array, string $keyPath, $value): void
    {
        $keys = explode('.', $keyPath);
        $current = &$array;

        foreach ($keys as $key) {
            if (!isset($current[$key])) {
                $current[$key] = [];
            }
            $current = &$current[$key];
        }

        $current = $value;
    }

    /**
     * 生成PHP内容
     */
    private function generatePhpContent(array $array): string
    {
        $content = "<?php\n";
        $content .= "/**\n";
        $content .= " * 语言包文件\n";
        $content .= " * 生成时间：" . date('Y-m-d H:i:s') . "\n";
        $content .= " */\n";
        $content .= "return " . var_export($array, true) . ";\n";

        return $content;
    }

    /**
     * 生成CSV内容
     */
    private function generateCsvContent(array $translations): string
    {
        $content = "Key Path,Value,Status,Updated At\n";

        foreach ($translations as $translation) {
            $content .= sprintf(
                '"%s","%s","%s","%s"' . "\n",
                str_replace('"', '""', $translation['key_path']),
                str_replace('"', '""', $translation['value']),
                $translation['status'],
                $translation['updated_at']
            );
        }

        return $content;
    }

    /**
     * 翻译质量检测
     */
    private function checkTranslationQuality(string $keyPath, string $value, string $languageCode): float
    {
        $score = 100;

        // 检查是否为空
        if (empty(trim($value))) {
            return 0;
        }

        // 检查长度合理性
        if (strlen($value) < 2) {
            $score -= 20;
        }

        // 检查是否包含占位符
        $originalValue = $this->getOriginalValue($keyPath);
        if ($originalValue) {
            preg_match_all('/\{[^}]+\}/', $originalValue, $originalPlaceholders);
            preg_match_all('/\{[^}]+\}/', $value, $translatedPlaceholders);

            if (count($originalPlaceholders[0]) !== count($translatedPlaceholders[0])) {
                $score -= 30;
            }
        }

        // 检查特殊字符
        if (preg_match('/[<>]/', $value)) {
            $score -= 10;
        }

        // 语言特定检查
        switch ($languageCode) {
            case 'ja-JP':
                // 日语检查：是否包含假名或汉字
                if (!preg_match('/[\x{3040}-\x{309F}\x{30A0}-\x{30FF}\x{4E00}-\x{9FAF}]/u', $value)) {
                    $score -= 15;
                }
                break;

            case 'ko-KR':
                // 韩语检查：是否包含韩文字符
                if (!preg_match('/[\x{AC00}-\x{D7AF}]/u', $value)) {
                    $score -= 15;
                }
                break;
        }

        return max(0, min(100, $score));
    }

    /**
     * 获取原始值（中文）
     */
    private function getOriginalValue(string $keyPath): ?string
    {
        $original = Db::name('language_translations')
            ->where('language_code', 'zh-CN')
            ->where('key_path', $keyPath)
            ->value('value');

        return $original;
    }

    /**
     * 记录翻译历史
     */
    private function recordTranslationHistory(int $translationId, int $userId, string $value, string $action): void
    {
        try {
            Db::name('language_translation_history')->insert([
                'translation_id' => $translationId,
                'user_id' => $userId,
                'value' => $value,
                'action' => $action,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            Log::error('记录翻译历史失败：' . $e->getMessage());
        }
    }

    /**
     * 清除语言缓存
     */
    private function clearLanguageCache(string $languageCode): void
    {
        $cacheKey = LanguageService::CACHE_PREFIX . $languageCode;
        Cache::delete($cacheKey);
    }
}
