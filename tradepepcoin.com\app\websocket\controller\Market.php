<?php
declare (strict_types = 1);

namespace app\websocket\controller;

use think\swoole\websocket\Room;
use app\common\model\TradingPair;
use app\common\model\Trade;
use app\common\model\Order;
use think\facade\Cache;

/**
 * WebSocket市场数据推送控制器
 */
class Market
{
    /**
     * 连接建立时触发
     */
    public function onOpen($server, $request)
    {
        echo "WebSocket连接建立: {$request->fd}\n";
        
        // 发送欢迎消息
        $server->push($request->fd, json_encode([
            'event' => 'connected',
            'data' => [
                'message' => 'WebSocket连接成功',
                'server_time' => time() * 1000
            ]
        ]));
    }

    /**
     * 接收到消息时触发
     */
    public function onMessage($server, $frame)
    {
        $data = json_decode($frame->data, true);
        
        if (!$data || !isset($data['method'])) {
            $this->sendError($server, $frame->fd, 'Invalid message format');
            return;
        }

        switch ($data['method']) {
            case 'SUBSCRIBE':
                $this->handleSubscribe($server, $frame->fd, $data);
                break;
                
            case 'UNSUBSCRIBE':
                $this->handleUnsubscribe($server, $frame->fd, $data);
                break;
                
            case 'LIST_SUBSCRIPTIONS':
                $this->handleListSubscriptions($server, $frame->fd, $data);
                break;
                
            default:
                $this->sendError($server, $frame->fd, 'Unknown method: ' . $data['method']);
        }
    }

    /**
     * 连接关闭时触发
     */
    public function onClose($server, $fd)
    {
        echo "WebSocket连接关闭: {$fd}\n";
        
        // 清理订阅
        $this->cleanupSubscriptions($fd);
    }

    /**
     * 处理订阅
     */
    private function handleSubscribe($server, $fd, $data)
    {
        if (!isset($data['params']) || !is_array($data['params'])) {
            $this->sendError($server, $fd, 'Invalid params');
            return;
        }

        $subscribed = [];
        
        foreach ($data['params'] as $stream) {
            if ($this->isValidStream($stream)) {
                Room::add($fd, $stream);
                $subscribed[] = $stream;
                
                // 发送初始数据
                $this->sendInitialData($server, $fd, $stream);
            }
        }

        $this->sendResponse($server, $fd, [
            'result' => null,
            'id' => $data['id'] ?? null
        ]);

        echo "用户 {$fd} 订阅了: " . implode(', ', $subscribed) . "\n";
    }

    /**
     * 处理取消订阅
     */
    private function handleUnsubscribe($server, $fd, $data)
    {
        if (!isset($data['params']) || !is_array($data['params'])) {
            $this->sendError($server, $fd, 'Invalid params');
            return;
        }

        $unsubscribed = [];
        
        foreach ($data['params'] as $stream) {
            Room::del($fd, $stream);
            $unsubscribed[] = $stream;
        }

        $this->sendResponse($server, $fd, [
            'result' => null,
            'id' => $data['id'] ?? null
        ]);

        echo "用户 {$fd} 取消订阅了: " . implode(', ', $unsubscribed) . "\n";
    }

    /**
     * 处理列出订阅
     */
    private function handleListSubscriptions($server, $fd, $data)
    {
        $subscriptions = Room::getRooms($fd);
        
        $this->sendResponse($server, $fd, [
            'result' => $subscriptions,
            'id' => $data['id'] ?? null
        ]);
    }

    /**
     * 验证流名称
     */
    private function isValidStream($stream)
    {
        // 支持的流格式:
        // btcusdt@ticker - 24小时价格统计
        // btcusdt@depth - 订单簿
        // btcusdt@trade - 最新成交
        // btcusdt@kline_1m - K线数据
        
        $patterns = [
            '/^[a-z]+@ticker$/',
            '/^[a-z]+@depth(\d+)?$/',
            '/^[a-z]+@trade$/',
            '/^[a-z]+@kline_\d+[mhd]$/',
            '/^!ticker@arr$/',
            '/^!miniTicker@arr$/'
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $stream)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 发送初始数据
     */
    private function sendInitialData($server, $fd, $stream)
    {
        if (strpos($stream, '@ticker') !== false) {
            $this->sendTickerData($server, $fd, $stream);
        } elseif (strpos($stream, '@depth') !== false) {
            $this->sendDepthData($server, $fd, $stream);
        } elseif (strpos($stream, '@trade') !== false) {
            $this->sendTradeData($server, $fd, $stream);
        } elseif (strpos($stream, '@kline') !== false) {
            $this->sendKlineData($server, $fd, $stream);
        }
    }

    /**
     * 发送价格统计数据
     */
    private function sendTickerData($server, $fd, $stream)
    {
        if ($stream === '!ticker@arr') {
            // 发送所有交易对统计
            $pairs = TradingPair::getEnabled();
            $data = [];
            
            foreach ($pairs as $pair) {
                $data[] = [
                    's' => strtoupper(str_replace('/', '', $pair->symbol)),
                    'c' => $pair->current_price,
                    'P' => $pair->change_24h,
                    'h' => $pair->high_24h,
                    'l' => $pair->low_24h,
                    'v' => $pair->volume_24h,
                    'E' => time() * 1000
                ];
            }
        } else {
            // 发送单个交易对统计
            $symbol = str_replace('@ticker', '', $stream);
            $pair = TradingPair::getBySymbol(strtoupper($symbol));
            
            if (!$pair) return;
            
            $data = [
                's' => strtoupper(str_replace('/', '', $pair->symbol)),
                'c' => $pair->current_price,
                'P' => $pair->change_24h,
                'h' => $pair->high_24h,
                'l' => $pair->low_24h,
                'v' => $pair->volume_24h,
                'E' => time() * 1000
            ];
        }

        $server->push($fd, json_encode([
            'stream' => $stream,
            'data' => $data
        ]));
    }

    /**
     * 发送订单簿数据
     */
    private function sendDepthData($server, $fd, $stream)
    {
        $parts = explode('@', $stream);
        $symbol = $parts[0];
        
        // 生成模拟订单簿数据
        $data = [
            'lastUpdateId' => time(),
            'bids' => [],
            'asks' => []
        ];

        // 生成买单和卖单数据
        $basePrice = 45000; // 模拟价格
        
        for ($i = 1; $i <= 20; $i++) {
            $data['bids'][] = [
                number_format($basePrice * (1 - $i * 0.0001), 2, '.', ''),
                number_format(mt_rand(1, 100) / 10, 4, '.', '')
            ];
            
            $data['asks'][] = [
                number_format($basePrice * (1 + $i * 0.0001), 2, '.', ''),
                number_format(mt_rand(1, 100) / 10, 4, '.', '')
            ];
        }

        $server->push($fd, json_encode([
            'stream' => $stream,
            'data' => $data
        ]));
    }

    /**
     * 发送成交数据
     */
    private function sendTradeData($server, $fd, $stream)
    {
        $symbol = str_replace('@trade', '', $stream);
        
        // 获取最新成交记录
        $trades = Trade::where('symbol', strtoupper($symbol))
                      ->order('created_at', 'desc')
                      ->limit(1)
                      ->select();

        foreach ($trades as $trade) {
            $data = [
                'e' => 'trade',
                'E' => time() * 1000,
                's' => strtoupper(str_replace('/', '', $trade->symbol)),
                't' => $trade->trade_id,
                'p' => $trade->price,
                'q' => $trade->amount,
                'T' => strtotime($trade->created_at) * 1000,
                'm' => $trade->type == 'sell'
            ];

            $server->push($fd, json_encode([
                'stream' => $stream,
                'data' => $data
            ]));
        }
    }

    /**
     * 发送K线数据
     */
    private function sendKlineData($server, $fd, $stream)
    {
        $parts = explode('@', $stream);
        $symbol = $parts[0];
        $interval = str_replace('kline_', '', $parts[1]);
        
        // 生成模拟K线数据
        $data = [
            'e' => 'kline',
            'E' => time() * 1000,
            's' => strtoupper($symbol),
            'k' => [
                't' => time() * 1000,
                'T' => (time() + 60) * 1000,
                's' => strtoupper($symbol),
                'i' => $interval,
                'o' => '45000.00',
                'c' => '45100.00',
                'h' => '45200.00',
                'l' => '44900.00',
                'v' => '123.45',
                'n' => 100,
                'x' => false,
                'q' => '5567890.12',
                'V' => '67.89',
                'Q' => '3045678.90'
            ]
        ];

        $server->push($fd, json_encode([
            'stream' => $stream,
            'data' => $data
        ]));
    }

    /**
     * 发送响应
     */
    private function sendResponse($server, $fd, $data)
    {
        $server->push($fd, json_encode($data));
    }

    /**
     * 发送错误
     */
    private function sendError($server, $fd, $message, $code = -1)
    {
        $server->push($fd, json_encode([
            'error' => [
                'code' => $code,
                'msg' => $message
            ]
        ]));
    }

    /**
     * 清理订阅
     */
    private function cleanupSubscriptions($fd)
    {
        $rooms = Room::getRooms($fd);
        foreach ($rooms as $room) {
            Room::del($fd, $room);
        }
    }

    /**
     * 广播市场数据更新
     */
    public static function broadcastMarketUpdate($symbol, $type, $data)
    {
        $stream = strtolower($symbol) . '@' . $type;
        
        Room::broadcast($stream, json_encode([
            'stream' => $stream,
            'data' => $data
        ]));
    }

    /**
     * 广播所有交易对统计
     */
    public static function broadcastAllTickers()
    {
        $pairs = TradingPair::getEnabled();
        $data = [];
        
        foreach ($pairs as $pair) {
            $data[] = [
                's' => strtoupper(str_replace('/', '', $pair->symbol)),
                'c' => $pair->current_price,
                'P' => $pair->change_24h,
                'h' => $pair->high_24h,
                'l' => $pair->low_24h,
                'v' => $pair->volume_24h,
                'E' => time() * 1000
            ];
        }

        Room::broadcast('!ticker@arr', json_encode([
            'stream' => '!ticker@arr',
            'data' => $data
        ]));
    }
}
