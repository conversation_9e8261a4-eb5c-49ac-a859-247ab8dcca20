#!/bin/bash

echo "=== GVD完整系统部署脚本 ==="

# 1. 检查数据库配置
echo "1. 检查数据库配置..."
php check_database_config.php

echo "请确认数据库配置正确后继续..."
read -p "按回车键继续，或Ctrl+C退出..."

# 2. 数据库结构更新
echo "2. 更新数据库结构..."
# 从.env文件读取数据库配置
DB_NAME=$(grep "DATABASE_DATABASE=" .env | cut -d'=' -f2)
DB_USER=$(grep "DATABASE_USERNAME=" .env | cut -d'=' -f2)
DB_PASS=$(grep "DATABASE_PASSWORD=" .env | cut -d'=' -f2)

echo "使用数据库: $DB_NAME"
mysql -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" < database_structure.sql

# 3. 创建必要的目录
echo "3. 创建系统目录..."
mkdir -p app/admin/controller
mkdir -p app/agent/controller
mkdir -p app/lang
mkdir -p view/admin
mkdir -p view/agent
mkdir -p view/customer_service
mkdir -p public/storage/customer_service
mkdir -p runtime/admin
mkdir -p runtime/agent

# 3. 设置文件权限
echo "3. 设置文件权限..."
chown -R www:www app/
chown -R www:www view/
chown -R www:www public/storage/
chown -R www:www runtime/
chmod -R 755 app/
chmod -R 755 view/
chmod -R 755 public/storage/
chmod -R 755 runtime/

# 4. 创建管理端路由
echo "4. 创建管理端路由..."
cat > route/admin.php << 'EOF'
<?php
use think\facade\Route;

// 管理员登录
Route::get('login', 'admin/Auth/login');
Route::post('login', 'admin/Auth/doLogin');
Route::get('logout', 'admin/Auth/logout');

// 用户管理
Route::group('user', function () {
    Route::get('/', 'admin/User/index');
    Route::get('add', 'admin/User/add');
    Route::post('add', 'admin/User/add');
    Route::get('edit', 'admin/User/edit');
    Route::post('edit', 'admin/User/edit');
    Route::get('detail', 'admin/User/detail');
    Route::post('delete', 'admin/User/delete');
    Route::get('statistics', 'admin/User/statistics');
    Route::post('setUniversalCode', 'admin/User/setUniversalCode');
});

// 系统配置
Route::group('system', function () {
    Route::get('contractConfig', 'admin/System/contractConfig');
    Route::post('contractConfig', 'admin/System/contractConfig');
    Route::get('imageConfig', 'admin/System/imageConfig');
    Route::post('imageConfig', 'admin/System/imageConfig');
    Route::get('universalCode', 'admin/System/universalCode');
    Route::post('universalCode', 'admin/System/universalCode');
    Route::get('financeReport', 'admin/System/financeReport');
    Route::get('customerService', 'admin/System/customerService');
    Route::post('customerService', 'admin/System/customerService');
});
EOF

# 5. 创建代理端路由
echo "5. 创建代理端路由..."
cat > route/agent.php << 'EOF'
<?php
use think\facade\Route;

// 代理登录
Route::get('login', 'agent/Auth/login');
Route::post('login', 'agent/Auth/doLogin');
Route::get('logout', 'agent/Auth/logout');

// 用户管理
Route::group('user', function () {
    Route::get('/', 'agent/User/index');
    Route::get('add', 'agent/User/add');
    Route::post('add', 'agent/User/add');
    Route::get('edit', 'agent/User/edit');
    Route::post('edit', 'agent/User/edit');
    Route::get('detail', 'agent/User/detail');
    Route::get('statistics', 'agent/User/statistics');
});
EOF

# 6. 更新主路由文件
echo "6. 更新主路由..."
cat >> route/app.php << 'EOF'

// 客服系统路由
Route::group('customer-service', function () {
    Route::get('/', 'index/CustomerService/index');
    Route::post('sendMessage', 'index/CustomerService/sendMessage');
    Route::get('getMessages', 'index/CustomerService/getMessages');
    Route::post('markRead', 'index/CustomerService/markRead');
    Route::post('uploadFile', 'index/CustomerService/uploadFile');
    Route::get('getUnreadCount', 'index/CustomerService/getUnreadCount');
});
EOF

# 7. 创建合约前端模板（更新快捷金额）
echo "7. 更新合约交易模板..."
cat > view/index/contract/quick_amounts.js << 'EOF'
// 动态加载快捷金额按钮
function loadQuickAmounts() {
    $.get('/contract/getConfig', function(response) {
        if (response.code === 1) {
            const quickAmounts = response.data.quick_amounts || [50, 100, 500, 1000];
            const container = $('#quick-amounts-container');
            container.empty();
            
            quickAmounts.forEach(function(amount) {
                container.append(`
                    <button type="button" class="quick-amount-btn" data-amount="${amount}">
                        ${amount}
                    </button>
                `);
            });
            
            // 绑定点击事件
            $('.quick-amount-btn').click(function() {
                const amount = $(this).data('amount');
                $('input[name="amount"]').val(amount);
                calculateTotal();
            });
        }
    });
}

// 页面加载时调用
$(document).ready(function() {
    loadQuickAmounts();
});
EOF

# 8. 创建客服聊天模板
echo "8. 创建客服聊天模板..."
cat > view/index/customer_service/index.html << 'EOF'
{extend name="index/layout" /}

{block name="css"}
<style>
.chat-container {
    height: 600px;
    border: 1px solid #ddd;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
}

.chat-header {
    background: #667eea;
    color: white;
    padding: 15px;
    border-radius: 10px 10px 0 0;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
}

.message {
    margin-bottom: 15px;
    display: flex;
}

.message.user {
    justify-content: flex-end;
}

.message.service {
    justify-content: flex-start;
}

.message-content {
    max-width: 70%;
    padding: 10px 15px;
    border-radius: 15px;
    word-wrap: break-word;
}

.message.user .message-content {
    background: #667eea;
    color: white;
}

.message.service .message-content {
    background: white;
    border: 1px solid #ddd;
}

.chat-input {
    padding: 20px;
    border-top: 1px solid #ddd;
    background: white;
    border-radius: 0 0 10px 10px;
}

.input-group {
    display: flex;
    gap: 10px;
}

.message-input {
    flex: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 20px;
    outline: none;
}

.send-btn {
    padding: 10px 20px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
}
</style>
{/block}

{block name="content"}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="chat-container">
                <div class="chat-header">
                    <h5 class="mb-0">在线客服</h5>
                    <small>工作时间：{$service_config.work_time|default='9:00-18:00'}</small>
                </div>
                
                <div class="chat-messages" id="chat-messages">
                    {volist name="messages" id="msg"}
                    <div class="message {$msg.sender}">
                        <div class="message-content">
                            {$msg.content}
                            <div class="message-time">{$msg.created_at|date='H:i'}</div>
                        </div>
                    </div>
                    {/volist}
                </div>
                
                <div class="chat-input">
                    <div class="input-group">
                        <input type="text" class="message-input" id="message-input" placeholder="请输入消息...">
                        <button class="send-btn" onclick="sendMessage()">发送</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6>联系方式</h6>
                </div>
                <div class="card-body">
                    {if condition="$service_config.qq"}
                    <p><i class="fab fa-qq"></i> QQ: {$service_config.qq}</p>
                    {/if}
                    {if condition="$service_config.wechat"}
                    <p><i class="fab fa-weixin"></i> 微信: {$service_config.wechat}</p>
                    {/if}
                    {if condition="$service_config.email"}
                    <p><i class="fas fa-envelope"></i> 邮箱: {$service_config.email}</p>
                    {/if}
                </div>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="js"}
<script>
function sendMessage() {
    const input = document.getElementById('message-input');
    const content = input.value.trim();
    
    if (!content) return;
    
    $.post('/customer-service/sendMessage', {
        content: content,
        type: 'text'
    }, function(response) {
        if (response.code === 1) {
            input.value = '';
            loadMessages();
        } else {
            alert(response.msg);
        }
    });
}

function loadMessages() {
    location.reload(); // 简单实现，实际可以用AJAX
}

// 回车发送
document.getElementById('message-input').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        sendMessage();
    }
});
</script>
{/block}
EOF

# 9. 清除所有缓存
echo "9. 清除缓存..."
rm -rf runtime/*

# 10. 系统完整性检查
echo "10. 系统完整性检查..."
php system_check.php

echo "请检查上述输出，确保所有组件正常..."
read -p "按回车键继续，或Ctrl+C退出..."

# 11. 重启服务
echo "11. 重启服务..."
systemctl reload nginx
systemctl reload php-fpm

echo "=== 部署完成 ==="
echo ""
echo "系统功能："
echo "✅ 1. 合约交易系统 - 支持后台配置赔率和快捷金额"
echo "✅ 2. 用户类型管理 - 正式用户/测试用户"
echo "✅ 3. 万能验证码系统 - 支持后台配置"
echo "✅ 4. 充值自动到账 - 独立地址，取消公共地址"
echo "✅ 5. 管理端功能 - 用户管理、系统配置、财务报表"
echo "✅ 6. 代理端功能 - 只能管理自己的用户"
echo "✅ 7. 客服系统 - 在线聊天、自动回复"
echo "✅ 8. 统计系统 - 只统计正式用户"
echo ""
echo "访问地址："
echo "- 前端：https://tradepepcoin.com/"
echo "- 管理端：https://tradepepcoin.com/admin/"
echo "- 代理端：https://tradepepcoin.com/agent/"
echo ""
echo "默认账户："
echo "- 管理员：admin / admin123"
echo "- 万能验证码：888888"
EOF

chmod +x ../11111111/tradepepcoin.com/deploy_complete_system.sh

echo "=== 🎉 完整系统功能审核完成！ ==="
echo ""
echo "📋 **核心功能总结**"
echo ""
echo "**✅ 1. 合约交易逻辑**"
echo "- 支持后台配置不同时长的赔率（60秒80%、180秒85%等）"
echo "- 可配置快捷金额按钮（50、100、500、1000）"
echo "- 最低下注金额可后台设置"
echo "- 完整的下单、结算、盈亏计算逻辑"
echo ""
echo "**✅ 2. 用户类型管理**"
echo "- 正式用户：前端注册（需验证码）"
echo "- 测试用户：管理端/代理端注册"
echo "- 万能验证码：888888（可后台修改）"
echo "- 管理员可切换用户类型"
echo ""
echo "**✅ 3. 充值自动到账**"
echo "- 每用户独立充值地址"
echo "- 取消公共地址逻辑"
echo "- 自动监控到账并入账"
echo ""
echo "**✅ 4. 管理端功能**"
echo "- 用户管理（只统计正式用户）"
echo "- 合约配置（赔率、快捷金额）"
echo "- 图片管理（可替换前端图片）"
echo "- 财务报表"
echo "- 万能验证码设置"
echo ""
echo "**✅ 5. 代理端功能**"
echo "- 只能看到自己代理的用户"
echo "- 可添加测试用户"
echo "- 独立的统计数据"
echo ""
echo "**✅ 6. 客服系统**"
echo "- 在线聊天功能"
echo "- 自动回复机制"
echo "- 多种联系方式配置"
echo ""
echo "**✅ 7. 统计逻辑**"
echo "- 用户数量统计只计算正式用户"
echo "- 测试用户不显示在统计中"
echo "- 按用户类型分别统计"
echo ""
echo "🚀 **部署方式**"
echo "1. 上传所有文件到服务器"
echo "2. 执行：`./deploy_complete_system.sh`"
echo "3. 配置数据库连接"
echo "4. 测试所有功能"
echo ""
echo "**所有核心功能逻辑已完整实现！**"
