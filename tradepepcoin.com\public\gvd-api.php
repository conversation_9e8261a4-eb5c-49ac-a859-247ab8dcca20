<?php
header('Content-Type: application/json');
$path = $_GET['path'] ?? '';
switch ($path) {
    case 'config/version':
        echo json_encode(['code' => 200, 'message' => 'success', 'data' => ['version' => '1.0.0', 'name' => 'GVD', 'timestamp' => time()]]);
        break;
    case 'config/frontend':
        echo json_encode(['code' => 200, 'message' => 'success', 'data' => ['site_name' => 'GVD', 'site_url' => 'https://tradepepcoin.com']]);
        break;
    case 'config/contract':
        echo json_encode(['code' => 200, 'message' => 'success', 'data' => ['min_amount' => 10, 'max_amount' => 10000]]);
        break;
    default:
        echo json_encode(['code' => 200, 'message' => 'GVD API', 'available' => ['config/version', 'config/frontend', 'config/contract']]);
}
?>
