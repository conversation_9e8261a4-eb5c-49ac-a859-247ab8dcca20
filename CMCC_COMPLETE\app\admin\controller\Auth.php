<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\admin\controller\BaseController;
use app\common\model\Admin;
use think\facade\Request;
use think\facade\Validate;

/**
 * 管理员认证控制器
 */
class Auth extends BaseController
{
    /**
     * 管理员登录
     * POST /admin/auth/login
     */
    public function login()
    {
        if (!Request::isPost()) {
            return $this->error('请求方式错误');
        }

        $username = Request::post('username', '');
        $password = Request::post('password', '');

        if (empty($username) || empty($password)) {
            return $this->error('用户名和密码不能为空');
        }

        $result = Admin::login($username, $password);
        return json($result);
    }

    /**
     * 管理员登出
     * POST /admin/auth/logout
     */
    public function logout()
    {
        return $this->success('登出成功');
    }

    /**
     * 获取管理员信息
     * GET /admin/auth/profile
     */
    public function profile()
    {
        $adminId = $this->getAdminId();
        if (!$adminId) {
            return $this->error('请先登录', 401);
        }

        $admin = Admin::find($adminId);
        if (!$admin) {
            return $this->error('管理员不存在', 401);
        }

        return $this->success('获取成功', $admin->getProfile());
    }

    /**
     * 修改密码
     * POST /admin/auth/change-password
     */
    public function changePassword()
    {
        if (!Request::isPost()) {
            return $this->error('请求方式错误');
        }

        $adminId = $this->getAdminId();
        if (!$adminId) {
            return $this->error('请先登录', 401);
        }

        $admin = Admin::find($adminId);
        if (!$admin) {
            return $this->error('管理员不存在', 401);
        }

        $oldPassword = Request::post('old_password', '');
        $newPassword = Request::post('new_password', '');

        if (empty($oldPassword) || empty($newPassword)) {
            return $this->error('原密码和新密码不能为空');
        }

        if (strlen($newPassword) < 6) {
            return $this->error('新密码长度不能少于6位');
        }

        $result = $admin->changePassword($oldPassword, $newPassword);
        return json($result);
    }

    /**
     * 验证Token
     * POST /admin/auth/verify-token
     */
    public function verifyToken()
    {
        $token = Request::header('Authorization');
        if (!$token) {
            return $this->error('Token不能为空');
        }

        $token = str_replace('Bearer ', '', $token);
        $payload = Admin::verifyToken($token);
        
        if (!$payload) {
            return $this->error('Token无效或已过期', 401);
        }

        return $this->success('Token有效', $payload);
    }

    /**
     * 获取当前管理员ID
     */
    private function getAdminId(): int
    {
        $token = Request::header('Authorization');
        if (!$token) {
            return 0;
        }

        $token = str_replace('Bearer ', '', $token);
        $payload = Admin::verifyToken($token);
        return $payload ? $payload['admin_id'] : 0;
    }
}
