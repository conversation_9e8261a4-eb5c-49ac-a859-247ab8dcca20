<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\BaseController;
use app\common\model\User;
use app\common\model\TradingPair;
use app\common\model\Order;
use think\facade\View;
use think\facade\Db;

/**
 * 管理后台首页控制器
 */
class Index extends BaseController
{
    /**
     * 后台首页
     */
    public function index()
    {
        // 获取统计数据
        $stats = $this->getStats();
        
        // 获取最近订单
        $recentOrders = $this->getRecentOrders();
        
        // 获取用户增长数据
        $userGrowth = $this->getUserGrowthData();
        
        View::assign([
            'stats' => $stats,
            'recentOrders' => $recentOrders,
            'userGrowth' => $userGrowth,
            'title' => '控制台'
        ]);

        return View::fetch();
    }

    /**
     * 获取统计数据
     */
    private function getStats(): array
    {
        $today = date('Y-m-d');
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        
        // 用户统计（只统计正式用户）
        $totalUsers = User::where('user_type', 1)->count();
        $todayUsers = User::where('user_type', 1)
                         ->whereTime('created_at', 'today')
                         ->count();
        $yesterdayUsers = User::where('user_type', 1)
                             ->whereTime('created_at', 'yesterday')
                             ->count();
        
        // 交易统计
        $totalTrades = Db::name('orders')->where('status', 2)->count();
        $todayTrades = Db::name('orders')
                        ->where('status', 2)
                        ->whereTime('created_at', 'today')
                        ->count();
        
        // 交易量统计
        $totalVolume = Db::name('orders')
                        ->where('status', 2)
                        ->sum('filled_total');
        $todayVolume = Db::name('orders')
                        ->where('status', 2)
                        ->whereTime('created_at', 'today')
                        ->sum('filled_total');
        
        // 充值统计
        $totalDeposits = Db::name('deposits')->where('status', 1)->sum('amount');
        $todayDeposits = Db::name('deposits')
                          ->where('status', 1)
                          ->whereTime('created_at', 'today')
                          ->sum('amount');
        
        // 提现统计
        $totalWithdrawals = Db::name('withdrawals')->where('status', 2)->sum('amount');
        $todayWithdrawals = Db::name('withdrawals')
                           ->where('status', 2)
                           ->whereTime('created_at', 'today')
                           ->sum('amount');

        return [
            'users' => [
                'total' => $totalUsers,
                'today' => $todayUsers,
                'growth' => $yesterdayUsers > 0 ? round(($todayUsers - $yesterdayUsers) / $yesterdayUsers * 100, 2) : 0
            ],
            'trades' => [
                'total' => $totalTrades,
                'today' => $todayTrades
            ],
            'volume' => [
                'total' => $totalVolume,
                'today' => $todayVolume
            ],
            'deposits' => [
                'total' => $totalDeposits,
                'today' => $todayDeposits
            ],
            'withdrawals' => [
                'total' => $totalWithdrawals,
                'today' => $todayWithdrawals
            ]
        ];
    }

    /**
     * 获取最近订单
     */
    private function getRecentOrders(): array
    {
        return Db::name('orders')
                ->alias('o')
                ->leftJoin('users u', 'o.user_id = u.id')
                ->field('o.*, u.username')
                ->order('o.created_at', 'desc')
                ->limit(10)
                ->select()
                ->toArray();
    }

    /**
     * 获取用户增长数据（最近30天）
     */
    private function getUserGrowthData(): array
    {
        $data = [];
        $labels = [];
        
        for ($i = 29; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $labels[] = date('m-d', strtotime($date));
            
            $count = User::where('user_type', 1)
                        ->whereTime('created_at', $date)
                        ->count();
            $data[] = $count;
        }
        
        return [
            'labels' => $labels,
            'data' => $data
        ];
    }

    /**
     * 系统信息
     */
    public function systemInfo()
    {
        $info = [
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'mysql_version' => $this->getMysqlVersion(),
            'disk_usage' => $this->getDiskUsage(),
            'memory_usage' => $this->getMemoryUsage(),
            'load_average' => $this->getLoadAverage()
        ];

        return json(['code' => 1, 'data' => $info]);
    }

    /**
     * 获取MySQL版本
     */
    private function getMysqlVersion(): string
    {
        try {
            $result = Db::query('SELECT VERSION() as version');
            return $result[0]['version'] ?? 'Unknown';
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * 获取磁盘使用情况
     */
    private function getDiskUsage(): array
    {
        $total = disk_total_space('.');
        $free = disk_free_space('.');
        $used = $total - $free;
        
        return [
            'total' => $this->formatBytes($total),
            'used' => $this->formatBytes($used),
            'free' => $this->formatBytes($free),
            'percent' => round($used / $total * 100, 2)
        ];
    }

    /**
     * 获取内存使用情况
     */
    private function getMemoryUsage(): array
    {
        $memory = memory_get_usage(true);
        $peak = memory_get_peak_usage(true);
        
        return [
            'current' => $this->formatBytes($memory),
            'peak' => $this->formatBytes($peak)
        ];
    }

    /**
     * 获取系统负载
     */
    private function getLoadAverage(): string
    {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return implode(', ', array_map(function($val) {
                return number_format($val, 2);
            }, $load));
        }
        
        return 'Unknown';
    }

    /**
     * 格式化字节数
     */
    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * 清理缓存
     */
    public function clearCache()
    {
        try {
            // 清理应用缓存
            \think\facade\Cache::clear();
            
            // 清理模板缓存
            $runtimePath = app()->getRuntimePath();
            $this->deleteDir($runtimePath . 'temp');
            $this->deleteDir($runtimePath . 'cache');
            
            return json(['code' => 1, 'msg' => '缓存清理成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '缓存清理失败：' . $e->getMessage()]);
        }
    }

    /**
     * 递归删除目录
     */
    private function deleteDir(string $dir): bool
    {
        if (!is_dir($dir)) {
            return true;
        }
        
        $files = array_diff(scandir($dir), ['.', '..']);
        
        foreach ($files as $file) {
            $path = $dir . DIRECTORY_SEPARATOR . $file;
            is_dir($path) ? $this->deleteDir($path) : unlink($path);
        }
        
        return rmdir($dir);
    }
}
