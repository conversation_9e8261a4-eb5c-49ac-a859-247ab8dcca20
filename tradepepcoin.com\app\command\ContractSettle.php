<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\common\service\ContractService;

/**
 * 合约结算命令 - 兼容老系统的自动结算逻辑
 */
class ContractSettle extends Command
{
    protected function configure()
    {
        $this->setName('contract:settle')
             ->setDescription('执行合约自动结算');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始执行合约结算...');
        
        $contractService = new ContractService();
        
        try {
            $settledCount = $contractService->settleContractOrders();
            
            $output->writeln("结算完成，共结算 {$settledCount} 笔合约");
            
        } catch (\Exception $e) {
            $output->writeln("结算过程中发生错误: " . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
}
