<svg width="120" height="40" viewBox="0 0 120 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- GVD Logo设计 -->
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1890ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00d4aa;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f0b90b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffd700;stop-opacity:1" />
    </linearGradient>
    
    <!-- 发光效果 -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="20" cy="20" r="18" fill="url(#primaryGradient)" opacity="0.1"/>
  
  <!-- 闪电图标 -->
  <path d="M15 8L25 20H20L22 32L12 20H17L15 8Z" fill="url(#goldGradient)" filter="url(#glow)"/>
  
  <!-- GVD文字 -->
  <text x="45" y="16" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="url(#primaryGradient)">GVD</text>
  
  <!-- 副标题 -->
  <text x="45" y="30" font-family="Arial, sans-serif" font-size="8" fill="#848e9c">TRADING</text>
  
  <!-- 装饰线条 -->
  <line x1="45" y1="20" x2="85" y2="20" stroke="url(#primaryGradient)" stroke-width="1" opacity="0.3"/>
</svg>
