<?php
declare (strict_types = 1);

namespace app\admin\controller;

use think\Request;
use think\facade\Db;
use think\facade\Cache;
use app\common\service\RiskControlService;

/**
 * 风控管理控制器
 */
class RiskControl extends BaseController
{
    protected $riskControlService;

    public function __construct()
    {
        parent::__construct();
        $this->riskControlService = new RiskControlService();
    }

    /**
     * 风控概览
     */
    public function index()
    {
        try {
            // 今日风控统计
            $todayStats = [
                'total_events' => Db::name('risk_control_log')->whereTime('created_at', 'today')->count(),
                'blocked_events' => Db::name('risk_control_log')->whereTime('created_at', 'today')->where('action', 'block')->count(),
                'high_risk_events' => Db::name('risk_control_log')->whereTime('created_at', 'today')->where('risk_level', '>=', 3)->count(),
                'unique_users' => Db::name('risk_control_log')->whereTime('created_at', 'today')->group('user_id')->count()
            ];

            // 风险等级分布
            $riskDistribution = Db::name('risk_control_log')
                ->field('risk_level, COUNT(*) as count')
                ->whereTime('created_at', 'today')
                ->group('risk_level')
                ->select()
                ->toArray();

            // 风控动作分布
            $actionDistribution = Db::name('risk_control_log')
                ->field('action, COUNT(*) as count')
                ->whereTime('created_at', 'today')
                ->group('action')
                ->select()
                ->toArray();

            // 最近风控事件
            $recentEvents = Db::name('risk_control_log')
                ->alias('rcl')
                ->join('users u', 'rcl.user_id = u.id')
                ->field('rcl.*, u.username')
                ->order('rcl.id desc')
                ->limit(20)
                ->select()
                ->toArray();

            return $this->success('获取成功', [
                'today_stats' => $todayStats,
                'risk_distribution' => $riskDistribution,
                'action_distribution' => $actionDistribution,
                'recent_events' => $recentEvents
            ]);

        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 风控日志列表
     */
    public function logs()
    {
        try {
            $page = Request::get('page/d', 1);
            $limit = Request::get('limit/d', 20);
            $userId = Request::get('user_id/d', 0);
            $type = Request::get('type', '');
            $riskLevel = Request::get('risk_level/d', 0);
            $action = Request::get('action', '');

            $query = Db::name('risk_control_log')
                ->alias('rcl')
                ->join('users u', 'rcl.user_id = u.id')
                ->field('rcl.*, u.username');

            if ($userId > 0) {
                $query->where('rcl.user_id', $userId);
            }

            if (!empty($type)) {
                $query->where('rcl.type', $type);
            }

            if ($riskLevel > 0) {
                $query->where('rcl.risk_level', $riskLevel);
            }

            if (!empty($action)) {
                $query->where('rcl.action', $action);
            }

            $total = $query->count();
            $logs = $query->order('rcl.id desc')
                ->page($page, $limit)
                ->select()
                ->toArray();

            // 解析风险因素
            foreach ($logs as &$log) {
                $log['risk_factors'] = json_decode($log['risk_factors'], true) ?: [];
            }

            return $this->success('获取成功', [
                'list' => $logs,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]);

        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * IP白名单管理
     */
    public function ipWhitelist()
    {
        if (Request::isPost()) {
            return $this->addIpWhitelist();
        }

        try {
            $page = Request::get('page/d', 1);
            $limit = Request::get('limit/d', 20);

            $total = Db::name('ip_whitelist')->count();
            $list = Db::name('ip_whitelist')
                ->alias('iw')
                ->leftJoin('users u', 'iw.user_id = u.id')
                ->field('iw.*, u.username')
                ->order('iw.id desc')
                ->page($page, $limit)
                ->select()
                ->toArray();

            return $this->success('获取成功', [
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]);

        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 添加IP白名单
     */
    private function addIpWhitelist()
    {
        $data = Request::post();

        if (empty($data['ip'])) {
            return $this->error('IP地址不能为空');
        }

        if (!filter_var($data['ip'], FILTER_VALIDATE_IP)) {
            return $this->error('IP地址格式不正确');
        }

        $result = $this->riskControlService->addIpWhitelist(
            $data['ip'],
            $data['user_id'] ?? 0,
            $data['remark'] ?? ''
        );

        if ($result['code'] === 1) {
            return $this->success($result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * IP黑名单管理
     */
    public function ipBlacklist()
    {
        if (Request::isPost()) {
            return $this->addIpBlacklist();
        }

        try {
            $page = Request::get('page/d', 1);
            $limit = Request::get('limit/d', 20);

            $total = Db::name('ip_blacklist')->count();
            $list = Db::name('ip_blacklist')
                ->order('id desc')
                ->page($page, $limit)
                ->select()
                ->toArray();

            return $this->success('获取成功', [
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]);

        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 添加IP黑名单
     */
    private function addIpBlacklist()
    {
        $data = Request::post();

        if (empty($data['ip'])) {
            return $this->error('IP地址不能为空');
        }

        if (!filter_var($data['ip'], FILTER_VALIDATE_IP)) {
            return $this->error('IP地址格式不正确');
        }

        $result = $this->riskControlService->addIpBlacklist(
            $data['ip'],
            $data['reason'] ?? ''
        );

        if ($result['code'] === 1) {
            return $this->success($result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 删除IP白名单
     */
    public function deleteIpWhitelist()
    {
        $id = Request::post('id/d', 0);
        if (!$id) {
            return $this->error('参数错误');
        }

        try {
            $whitelist = Db::name('ip_whitelist')->find($id);
            if (!$whitelist) {
                return $this->error('记录不存在');
            }

            Db::name('ip_whitelist')->delete($id);
            Cache::delete("ip_whitelist:{$whitelist['ip']}");

            return $this->success('删除成功');

        } catch (\Exception $e) {
            return $this->error('删除失败：' . $e->getMessage());
        }
    }

    /**
     * 删除IP黑名单
     */
    public function deleteIpBlacklist()
    {
        $id = Request::post('id/d', 0);
        if (!$id) {
            return $this->error('参数错误');
        }

        try {
            $blacklist = Db::name('ip_blacklist')->find($id);
            if (!$blacklist) {
                return $this->error('记录不存在');
            }

            Db::name('ip_blacklist')->delete($id);
            Cache::delete("ip_blacklist:{$blacklist['ip']}");

            return $this->success('删除成功');

        } catch (\Exception $e) {
            return $this->error('删除失败：' . $e->getMessage());
        }
    }

    /**
     * 风控规则配置
     */
    public function rules()
    {
        if (Request::isPost()) {
            return $this->updateRules();
        }

        try {
            // 获取当前风控规则配置
            $rules = [
                'login_max_attempts' => Cache::get('risk_rule:login_max_attempts', 10),
                'trade_max_frequency' => Cache::get('risk_rule:trade_max_frequency', 100),
                'withdraw_max_amount' => Cache::get('risk_rule:withdraw_max_amount', 10000),
                'new_ip_risk_score' => Cache::get('risk_rule:new_ip_risk_score', 20),
                'new_device_risk_score' => Cache::get('risk_rule:new_device_risk_score', 15),
                'high_frequency_risk_score' => Cache::get('risk_rule:high_frequency_risk_score', 30),
                'large_amount_risk_score' => Cache::get('risk_rule:large_amount_risk_score', 40),
                'block_threshold' => Cache::get('risk_rule:block_threshold', 80),
                'restrict_threshold' => Cache::get('risk_rule:restrict_threshold', 50),
                'warning_threshold' => Cache::get('risk_rule:warning_threshold', 20)
            ];

            return $this->success('获取成功', $rules);

        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 更新风控规则
     */
    private function updateRules()
    {
        $data = Request::post();

        try {
            $rules = [
                'login_max_attempts',
                'trade_max_frequency',
                'withdraw_max_amount',
                'new_ip_risk_score',
                'new_device_risk_score',
                'high_frequency_risk_score',
                'large_amount_risk_score',
                'block_threshold',
                'restrict_threshold',
                'warning_threshold'
            ];

            foreach ($rules as $rule) {
                if (isset($data[$rule])) {
                    Cache::set("risk_rule:{$rule}", $data[$rule], 86400 * 365); // 1年有效
                }
            }

            return $this->success('规则更新成功');

        } catch (\Exception $e) {
            return $this->error('更新失败：' . $e->getMessage());
        }
    }

    /**
     * 用户风险分析
     */
    public function userRiskAnalysis()
    {
        $userId = Request::get('user_id/d', 0);
        if (!$userId) {
            return $this->error('用户ID不能为空');
        }

        try {
            // 用户基本信息
            $user = Db::name('users')->find($userId);
            if (!$user) {
                return $this->error('用户不存在');
            }

            // 风控事件统计
            $riskStats = [
                'total_events' => Db::name('risk_control_log')->where('user_id', $userId)->count(),
                'blocked_events' => Db::name('risk_control_log')->where('user_id', $userId)->where('action', 'block')->count(),
                'high_risk_events' => Db::name('risk_control_log')->where('user_id', $userId)->where('risk_level', '>=', 3)->count(),
                'recent_events' => Db::name('risk_control_log')->where('user_id', $userId)->where('created_at', '>=', date('Y-m-d H:i:s', time() - 86400 * 7))->count()
            ];

            // 最近风控事件
            $recentEvents = Db::name('risk_control_log')
                ->where('user_id', $userId)
                ->order('id desc')
                ->limit(10)
                ->select()
                ->toArray();

            // 用户行为分析
            $behaviorAnalysis = [
                'login_ips' => Cache::get("user_ips:{$userId}", []),
                'login_devices' => Cache::get("user_devices:{$userId}", []),
                'login_locations' => Cache::get("user_locations:{$userId}", [])
            ];

            return $this->success('获取成功', [
                'user' => $user,
                'risk_stats' => $riskStats,
                'recent_events' => $recentEvents,
                'behavior_analysis' => $behaviorAnalysis
            ]);

        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }
}
