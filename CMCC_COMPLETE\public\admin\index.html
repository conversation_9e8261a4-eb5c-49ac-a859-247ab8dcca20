<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GVD数字货币交易平台 - 管理后台</title>
    <link href="https://cdn.jsdelivr.net/npm/element-plus@2.4.4/dist/index.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@element-plus/icons-vue@2.1.0/dist/index.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f7fa;
        }

        .admin-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .admin-logo {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: bold;
        }

        .admin-logo i {
            margin-right: 10px;
            font-size: 24px;
        }

        .admin-user {
            display: flex;
            align-items: center;
        }

        .admin-main {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        .admin-sidebar {
            width: 250px;
            background: white;
            box-shadow: 2px 0 8px rgba(0,0,0,0.1);
            overflow-y: auto;
        }

        .admin-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background-color: #f5f7fa;
        }

        .sidebar-menu {
            border: none;
        }

        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.3s;
            border-left: 3px solid transparent;
        }

        .menu-item:hover {
            background-color: #f5f7fa;
            border-left-color: #409eff;
        }

        .menu-item.active {
            background-color: #ecf5ff;
            border-left-color: #409eff;
            color: #409eff;
        }

        .menu-item i {
            margin-right: 10px;
            width: 16px;
        }

        .content-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .card-header {
            padding: 20px;
            border-bottom: 1px solid #ebeef5;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
        }

        .card-body {
            padding: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .stat-title {
            color: #606266;
            font-size: 14px;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 8px;
        }

        .stat-change {
            font-size: 12px;
            display: flex;
            align-items: center;
        }

        .stat-change.positive {
            color: #67c23a;
        }

        .stat-change.negative {
            color: #f56c6c;
        }

        .chart-container {
            height: 400px;
            margin-top: 20px;
        }

        .table-container {
            margin-top: 20px;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .breadcrumb {
            margin-bottom: 20px;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .hidden {
            display: none !important;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .admin-sidebar {
                width: 200px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .admin-content {
                padding: 10px;
            }
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body>
    <div id="app" class="admin-container">
        <!-- 顶部导航 -->
        <header class="admin-header">
            <div class="admin-logo">
                <i class="el-icon-s-platform"></i>
                GVD数字货币交易平台
            </div>
            <div class="admin-user">
                <el-dropdown @command="handleUserCommand">
                    <span class="el-dropdown-link">
                        <el-avatar :size="32" :src="userInfo.avatar"></el-avatar>
                        <span style="margin-left: 10px;">{{ userInfo.username }}</span>
                        <i class="el-icon-arrow-down el-icon--right"></i>
                    </span>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item command="profile">个人资料</el-dropdown-item>
                            <el-dropdown-item command="settings">系统设置</el-dropdown-item>
                            <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
        </header>

        <!-- 主体内容 -->
        <main class="admin-main">
            <!-- 侧边栏 -->
            <aside class="admin-sidebar">
                <nav class="sidebar-menu">
                    <div class="menu-item" :class="{active: currentMenu === 'dashboard'}" @click="switchMenu('dashboard')">
                        <i class="el-icon-s-home"></i>
                        仪表板
                    </div>
                    <div class="menu-item" :class="{active: currentMenu === 'users'}" @click="switchMenu('users')">
                        <i class="el-icon-user"></i>
                        用户管理
                    </div>
                    <div class="menu-item" :class="{active: currentMenu === 'trading'}" @click="switchMenu('trading')">
                        <i class="el-icon-s-finance"></i>
                        交易管理
                    </div>
                    <div class="menu-item" :class="{active: currentMenu === 'assets'}" @click="switchMenu('assets')">
                        <i class="el-icon-wallet"></i>
                        资产管理
                    </div>
                    <div class="menu-item" :class="{active: currentMenu === 'risk'}" @click="switchMenu('risk')">
                        <i class="el-icon-warning"></i>
                        风控管理
                    </div>
                    <div class="menu-item" :class="{active: currentMenu === 'agents'}" @click="switchMenu('agents')">
                        <i class="el-icon-s-custom"></i>
                        代理管理
                    </div>
                    <div class="menu-item" :class="{active: currentMenu === 'languages'}" @click="switchMenu('languages')">
                        <i class="el-icon-s-comment"></i>
                        语言管理
                    </div>
                    <div class="menu-item" :class="{active: currentMenu === 'system'}" @click="switchMenu('system')">
                        <i class="el-icon-setting"></i>
                        系统管理
                    </div>
                    <div class="menu-item" :class="{active: currentMenu === 'logs'}" @click="switchMenu('logs')">
                        <i class="el-icon-document"></i>
                        日志管理
                    </div>
                    <div class="menu-item" :class="{active: currentMenu === 'monitoring'}" @click="switchMenu('monitoring')">
                        <i class="el-icon-monitor"></i>
                        系统监控
                    </div>
                </nav>
            </aside>

            <!-- 内容区域 -->
            <section class="admin-content">
                <!-- 面包屑导航 -->
                <el-breadcrumb class="breadcrumb" separator="/">
                    <el-breadcrumb-item>管理后台</el-breadcrumb-item>
                    <el-breadcrumb-item>{{ menuTitles[currentMenu] }}</el-breadcrumb-item>
                </el-breadcrumb>

                <!-- 仪表板 -->
                <div v-show="currentMenu === 'dashboard'">
                    <!-- 统计卡片 -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-header">
                                <span class="stat-title">总用户数</span>
                                <div class="stat-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                    <i class="el-icon-user"></i>
                                </div>
                            </div>
                            <div class="stat-value">{{ dashboardData.totalUsers | number }}</div>
                            <div class="stat-change positive">
                                <i class="el-icon-arrow-up"></i>
                                +{{ dashboardData.userGrowth }}% 较昨日
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <span class="stat-title">24H交易量</span>
                                <div class="stat-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                                    <i class="el-icon-s-finance"></i>
                                </div>
                            </div>
                            <div class="stat-value">${{ dashboardData.tradingVolume | currency }}</div>
                            <div class="stat-change positive">
                                <i class="el-icon-arrow-up"></i>
                                +{{ dashboardData.volumeGrowth }}% 较昨日
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <span class="stat-title">总资产</span>
                                <div class="stat-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                                    <i class="el-icon-wallet"></i>
                                </div>
                            </div>
                            <div class="stat-value">${{ dashboardData.totalAssets | currency }}</div>
                            <div class="stat-change positive">
                                <i class="el-icon-arrow-up"></i>
                                +{{ dashboardData.assetGrowth }}% 较昨日
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <span class="stat-title">活跃用户</span>
                                <div class="stat-icon" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                                    <i class="el-icon-s-custom"></i>
                                </div>
                            </div>
                            <div class="stat-value">{{ dashboardData.activeUsers | number }}</div>
                            <div class="stat-change positive">
                                <i class="el-icon-arrow-up"></i>
                                +{{ dashboardData.activeGrowth }}% 较昨日
                            </div>
                        </div>
                    </div>

                    <!-- 图表区域 -->
                    <div class="content-card">
                        <div class="card-header">
                            <h3 class="card-title">交易量趋势</h3>
                            <el-radio-group v-model="chartPeriod" size="small">
                                <el-radio-button label="24h">24小时</el-radio-button>
                                <el-radio-button label="7d">7天</el-radio-button>
                                <el-radio-button label="30d">30天</el-radio-button>
                            </el-radio-group>
                        </div>
                        <div class="card-body">
                            <div id="tradingChart" class="chart-container"></div>
                        </div>
                    </div>

                    <!-- 最近活动 -->
                    <div class="content-card">
                        <div class="card-header">
                            <h3 class="card-title">最近活动</h3>
                        </div>
                        <div class="card-body">
                            <el-table :data="recentActivities" style="width: 100%">
                                <el-table-column prop="time" label="时间" width="180"></el-table-column>
                                <el-table-column prop="user" label="用户"></el-table-column>
                                <el-table-column prop="action" label="操作"></el-table-column>
                                <el-table-column prop="amount" label="金额"></el-table-column>
                                <el-table-column prop="status" label="状态">
                                    <template #default="scope">
                                        <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
                                            {{ scope.row.status === 'success' ? '成功' : '失败' }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                </div>

                <!-- 其他菜单内容将通过动态加载显示 -->
                <div v-show="currentMenu !== 'dashboard'" id="dynamicContent">
                    <div class="content-card">
                        <div class="card-header">
                            <h3 class="card-title">{{ menuTitles[currentMenu] }}</h3>
                        </div>
                        <div class="card-body">
                            <p>{{ menuTitles[currentMenu] }}功能正在开发中...</p>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- 加载遮罩 -->
        <div v-show="loading" class="loading-overlay">
            <el-loading-spinner></el-loading-spinner>
        </div>
    </div>

    <!-- 引入Vue和Element Plus -->
    <script src="https://cdn.jsdelivr.net/npm/vue@3.3.8/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-plus@2.4.4/dist/index.full.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>

    <script>
        const { createApp } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        createApp({
            data() {
                return {
                    loading: false,
                    currentMenu: 'dashboard',
                    chartPeriod: '24h',
                    userInfo: {
                        username: 'Admin',
                        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
                    },
                    menuTitles: {
                        dashboard: '仪表板',
                        users: '用户管理',
                        trading: '交易管理',
                        assets: '资产管理',
                        risk: '风控管理',
                        agents: '代理管理',
                        languages: '语言管理',
                        system: '系统管理',
                        logs: '日志管理',
                        monitoring: '系统监控'
                    },
                    dashboardData: {
                        totalUsers: 125680,
                        userGrowth: 12.5,
                        tradingVolume: 89650000,
                        volumeGrowth: 8.3,
                        totalAssets: 456780000,
                        assetGrowth: 15.2,
                        activeUsers: 8960,
                        activeGrowth: 6.8
                    },
                    recentActivities: [
                        {
                            time: '2024-01-15 14:30:25',
                            user: 'user123',
                            action: 'BTC买入',
                            amount: '$5,000',
                            status: 'success'
                        },
                        {
                            time: '2024-01-15 14:28:15',
                            user: 'user456',
                            action: 'ETH卖出',
                            amount: '$3,200',
                            status: 'success'
                        },
                        {
                            time: '2024-01-15 14:25:10',
                            user: 'user789',
                            action: 'USDT充值',
                            amount: '$10,000',
                            status: 'success'
                        }
                    ]
                };
            },
            filters: {
                number(value) {
                    return new Intl.NumberFormat().format(value);
                },
                currency(value) {
                    return new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: 'USD',
                        minimumFractionDigits: 0
                    }).format(value);
                }
            },
            mounted() {
                this.initChart();
                this.loadDashboardData();
            },
            methods: {
                switchMenu(menu) {
                    this.currentMenu = menu;
                    if (menu !== 'dashboard') {
                        this.loadMenuContent(menu);
                    }
                },
                handleUserCommand(command) {
                    switch (command) {
                        case 'profile':
                            ElMessage.info('个人资料功能开发中');
                            break;
                        case 'settings':
                            ElMessage.info('系统设置功能开发中');
                            break;
                        case 'logout':
                            this.logout();
                            break;
                    }
                },
                logout() {
                    ElMessageBox.confirm('确定要退出登录吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        // 清除登录信息
                        localStorage.removeItem('admin_token');
                        // 跳转到登录页
                        window.location.href = '/admin/login.html';
                    });
                },
                initChart() {
                    const chart = echarts.init(document.getElementById('tradingChart'));
                    const option = {
                        title: {
                            text: '交易量趋势',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'axis'
                        },
                        xAxis: {
                            type: 'category',
                            data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
                        },
                        yAxis: {
                            type: 'value',
                            name: '交易量 (万USD)'
                        },
                        series: [{
                            data: [820, 932, 901, 934, 1290, 1330, 1320],
                            type: 'line',
                            smooth: true,
                            areaStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
                                    { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
                                ])
                            }
                        }]
                    };
                    chart.setOption(option);

                    // 响应式
                    window.addEventListener('resize', () => {
                        chart.resize();
                    });
                },
                loadDashboardData() {
                    // 模拟加载数据
                    this.loading = true;
                    setTimeout(() => {
                        this.loading = false;
                    }, 1000);
                },
                loadMenuContent(menu) {
                    // 动态加载菜单内容
                    this.loading = true;
                    setTimeout(() => {
                        this.loading = false;
                        ElMessage.success(`${this.menuTitles[menu]}页面加载完成`);
                    }, 500);
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
