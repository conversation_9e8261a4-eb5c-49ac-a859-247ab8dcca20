<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\CustomerService;
use app\common\model\User;
use think\facade\Log;

/**
 * 客服服务类
 */
class CustomerServiceService
{
    /**
     * 发送用户消息
     */
    public function sendUserMessage(int $userId, string $content, int $type = CustomerService::TYPE_TEXT): array
    {
        try {
            // 验证用户
            $user = User::find($userId);
            if (!$user) {
                return ['code' => 0, 'msg' => '用户不存在'];
            }

            // 验证消息内容
            if (empty(trim($content))) {
                return ['code' => 0, 'msg' => '消息内容不能为空'];
            }

            if (mb_strlen($content) > 1000) {
                return ['code' => 0, 'msg' => '消息内容过长'];
            }

            // 发送消息
            $result = CustomerService::sendMessage($userId, $content, $type, false);

            if ($result['code'] == 1) {
                // 记录日志
                Log::info('用户发送客服消息', [
                    'user_id' => $userId,
                    'content' => $content,
                    'type' => $type
                ]);
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('发送用户消息失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '发送失败'];
        }
    }

    /**
     * 发送管理员回复
     */
    public function sendAdminReply(int $userId, string $content, int $adminId): array
    {
        try {
            // 验证用户
            $user = User::find($userId);
            if (!$user) {
                return ['code' => 0, 'msg' => '用户不存在'];
            }

            // 验证消息内容
            if (empty(trim($content))) {
                return ['code' => 0, 'msg' => '回复内容不能为空'];
            }

            // 发送回复
            $result = CustomerService::sendMessage($userId, $content, CustomerService::TYPE_TEXT, true);

            if ($result['code'] == 1) {
                // 记录日志
                Log::info('管理员回复客服消息', [
                    'admin_id' => $adminId,
                    'user_id' => $userId,
                    'content' => $content
                ]);
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('发送管理员回复失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '回复失败'];
        }
    }

    /**
     * 获取用户聊天记录
     */
    public function getUserChatHistory(int $userId, int $page = 1, int $limit = 50): array
    {
        try {
            // 标记管理员消息为已读
            CustomerService::markAsRead($userId, false);

            return CustomerService::getUserMessages($userId, $page, $limit);

        } catch (\Exception $e) {
            Log::error('获取用户聊天记录失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 获取管理员会话列表
     */
    public function getAdminConversations(int $page = 1, int $limit = 20): array
    {
        try {
            return CustomerService::getAllUserLatestMessages($page, $limit);

        } catch (\Exception $e) {
            Log::error('获取管理员会话列表失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 获取指定用户的聊天记录（管理员用）
     */
    public function getAdminUserChat(int $userId, int $page = 1, int $limit = 50): array
    {
        try {
            // 标记用户消息为已读
            CustomerService::markAsRead($userId, true);

            return CustomerService::getUserMessages($userId, $page, $limit);

        } catch (\Exception $e) {
            Log::error('获取管理员用户聊天失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 获取未读消息数量
     */
    public function getUnreadCount(int $userId, bool $isAdmin = false): int
    {
        try {
            return CustomerService::getUnreadCount($userId, $isAdmin);
        } catch (\Exception $e) {
            Log::error('获取未读消息数量失败：' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 初始化用户客服会话
     */
    public function initUserService(int $userId): array
    {
        try {
            // 检查是否已有消息记录
            $hasMessages = CustomerService::where('user_id', $userId)->count() > 0;

            if (!$hasMessages) {
                // 发送欢迎消息
                CustomerService::sendWelcomeMessage($userId);
            }

            return ['code' => 1, 'msg' => '初始化成功'];

        } catch (\Exception $e) {
            Log::error('初始化用户客服失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '初始化失败'];
        }
    }

    /**
     * 获取客服统计
     */
    public function getServiceStats(): array
    {
        try {
            return CustomerService::getServiceStats();
        } catch (\Exception $e) {
            Log::error('获取客服统计失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 搜索消息
     */
    public function searchMessages(string $keyword, int $page = 1, int $limit = 20): array
    {
        try {
            if (empty(trim($keyword))) {
                return ['code' => 0, 'msg' => '搜索关键词不能为空'];
            }

            return CustomerService::searchMessages($keyword, $page, $limit);

        } catch (\Exception $e) {
            Log::error('搜索消息失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '搜索失败'];
        }
    }

    /**
     * 发送图片消息
     */
    public function sendImageMessage(int $userId, string $imageUrl, bool $isAdmin = false): array
    {
        try {
            // 验证图片URL
            if (empty($imageUrl)) {
                return ['code' => 0, 'msg' => '图片地址不能为空'];
            }

            // 发送图片消息
            return CustomerService::sendMessage($userId, $imageUrl, CustomerService::TYPE_IMAGE, $isAdmin);

        } catch (\Exception $e) {
            Log::error('发送图片消息失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '发送失败'];
        }
    }

    /**
     * 发送文件消息
     */
    public function sendFileMessage(int $userId, string $fileUrl, bool $isAdmin = false): array
    {
        try {
            // 验证文件URL
            if (empty($fileUrl)) {
                return ['code' => 0, 'msg' => '文件地址不能为空'];
            }

            // 发送文件消息
            return CustomerService::sendMessage($userId, $fileUrl, CustomerService::TYPE_FILE, $isAdmin);

        } catch (\Exception $e) {
            Log::error('发送文件消息失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '发送失败'];
        }
    }

    /**
     * 获取常用回复模板
     */
    public function getQuickReplies(): array
    {
        return [
            'code' => 1,
            'data' => [
                [
                    'id' => 1,
                    'title' => '问候语',
                    'content' => '您好！很高兴为您服务，请问有什么可以帮助您的吗？'
                ],
                [
                    'id' => 2,
                    'title' => '充值问题',
                    'content' => '关于充值问题，请您提供充值金额和交易哈希，我们会尽快为您处理。'
                ],
                [
                    'id' => 3,
                    'title' => '提现问题',
                    'content' => '提现一般在1-24小时内到账，如超时请联系我们查询处理进度。'
                ],
                [
                    'id' => 4,
                    'title' => '交易问题',
                    'content' => '如遇到交易问题，请提供订单号和具体情况，我们会立即为您核实。'
                ],
                [
                    'id' => 5,
                    'title' => '结束语',
                    'content' => '感谢您的咨询，如还有其他问题请随时联系我们。祝您交易愉快！'
                ]
            ]
        ];
    }

    /**
     * 批量发送系统通知
     */
    public function broadcastSystemMessage(string $content, array $userIds = []): array
    {
        try {
            $successCount = 0;
            $failCount = 0;

            if (empty($userIds)) {
                // 发送给所有用户
                $users = User::where('status', User::STATUS_ACTIVE)->select();
                foreach ($users as $user) {
                    $result = CustomerService::sendSystemMessage($user->id, $content);
                    if ($result['code'] == 1) {
                        $successCount++;
                    } else {
                        $failCount++;
                    }
                }
            } else {
                // 发送给指定用户
                foreach ($userIds as $userId) {
                    $result = CustomerService::sendSystemMessage($userId, $content);
                    if ($result['code'] == 1) {
                        $successCount++;
                    } else {
                        $failCount++;
                    }
                }
            }

            return [
                'code' => 1,
                'msg' => '发送完成',
                'data' => [
                    'success_count' => $successCount,
                    'fail_count' => $failCount
                ]
            ];

        } catch (\Exception $e) {
            Log::error('批量发送系统消息失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '发送失败'];
        }
    }
}
