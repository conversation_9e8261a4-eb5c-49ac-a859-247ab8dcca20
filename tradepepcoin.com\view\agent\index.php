<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GVD - 代理后台</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; }
        .header { background: #2c3e50; color: white; padding: 15px 20px; }
        .container { max-width: 1200px; margin: 20px auto; padding: 0 20px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .nav-menu { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .nav-menu a { display: inline-block; margin: 10px 15px 10px 0; padding: 10px 20px; background: #3498db; color: white; text-decoration: none; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>GVD代理后台</h1>
    </div>
    <div class="container">
        <div class="stats">
            <div class="stat-card">
                <h3>下级用户</h3>
                <p style="font-size: 24px; color: #3498db;">0</p>
            </div>
            <div class="stat-card">
                <h3>今日佣金</h3>
                <p style="font-size: 24px; color: #27ae60;">$0.00</p>
            </div>
            <div class="stat-card">
                <h3>总佣金</h3>
                <p style="font-size: 24px; color: #e74c3c;">$0.00</p>
            </div>
        </div>
        <div class="nav-menu">
            <h3>功能菜单</h3>
            <a href="/agent/users">用户管理</a>
            <a href="/agent/orders">订单管理</a>
            <a href="/agent/commissions">佣金统计</a>
            <a href="/agent/service">客服管理</a>
        </div>
    </div>
</body>
</html>
