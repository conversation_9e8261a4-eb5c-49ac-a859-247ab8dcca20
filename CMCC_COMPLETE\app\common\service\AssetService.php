<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\UserAsset;
use app\common\model\User;
use think\facade\Db;
use think\facade\Log;

/**
 * 资产服务类
 */
class AssetService
{
    /**
     * 获取用户资产列表
     */
    public function getUserAssets(int $userId): array
    {
        try {
            $assets = UserAsset::getUserAssets($userId);
            $assetList = [];
            $totalValue = 0;

            foreach ($assets as $asset) {
                $assetModel = new UserAsset($asset);
                $assetInfo = $assetModel->getAssetInfo();
                $assetList[] = $assetInfo;
                $totalValue += floatval($assetInfo['usdt_value']);
            }

            return [
                'code' => 1,
                'data' => [
                    'assets' => $assetList,
                    'total_value' => number_format($totalValue, 2, '.', ''),
                    'total_count' => count($assetList)
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取用户资产失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 获取用户指定币种资产
     */
    public function getUserAsset(int $userId, string $coinSymbol): array
    {
        try {
            $asset = UserAsset::getUserAsset($userId, $coinSymbol);
            if (!$asset) {
                return ['code' => 0, 'msg' => '资产不存在'];
            }

            return [
                'code' => 1,
                'data' => $asset->getAssetInfo()
            ];

        } catch (\Exception $e) {
            Log::error('获取用户资产失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 充值
     */
    public function deposit(int $userId, string $coinSymbol, float $amount, string $txHash = ''): array
    {
        try {
            Db::startTrans();

            $asset = UserAsset::getUserAsset($userId, $coinSymbol);
            if (!$asset) {
                // 如果资产不存在，创建新的资产记录
                $asset = UserAsset::create([
                    'user_id' => $userId,
                    'coin_symbol' => $coinSymbol,
                    'coin_name' => $coinSymbol,
                    'available' => 0,
                    'frozen' => 0,
                    'total' => 0,
                    'decimals' => 8,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }

            // 增加可用余额
            if (!$asset->addBalance($amount, 'available')) {
                throw new \Exception('充值失败');
            }

            // 记录充值记录
            $this->recordAssetChange($userId, $coinSymbol, $amount, 'deposit', '充值', $txHash);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '充值成功',
                'data' => [
                    'coin_symbol' => $coinSymbol,
                    'amount' => $amount,
                    'balance' => $asset->available,
                    'tx_hash' => $txHash
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('充值失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 提现
     */
    public function withdraw(int $userId, string $coinSymbol, float $amount, string $address, string $payPassword = ''): array
    {
        try {
            Db::startTrans();

            // 验证支付密码
            $user = User::find($userId);
            if ($user->pay_password && !$user->verifyPayPassword($payPassword)) {
                throw new \Exception('支付密码错误');
            }

            $asset = UserAsset::getUserAsset($userId, $coinSymbol);
            if (!$asset) {
                throw new \Exception('资产不存在');
            }

            if ($asset->available < $amount) {
                throw new \Exception('余额不足');
            }

            // 扣除可用余额
            if (!$asset->subBalance($amount, 'available')) {
                throw new \Exception('提现失败');
            }

            // 记录提现记录
            $this->recordAssetChange($userId, $coinSymbol, -$amount, 'withdraw', '提现到：' . $address);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '提现申请已提交',
                'data' => [
                    'coin_symbol' => $coinSymbol,
                    'amount' => $amount,
                    'address' => $address,
                    'balance' => $asset->available
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('提现失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 内部转账
     */
    public function transfer(int $fromUserId, int $toUserId, string $coinSymbol, float $amount, string $payPassword = ''): array
    {
        try {
            Db::startTrans();

            // 验证支付密码
            $fromUser = User::find($fromUserId);
            if ($fromUser->pay_password && !$fromUser->verifyPayPassword($payPassword)) {
                throw new \Exception('支付密码错误');
            }

            // 验证接收用户
            $toUser = User::find($toUserId);
            if (!$toUser) {
                throw new \Exception('接收用户不存在');
            }

            $result = UserAsset::transfer($fromUserId, $toUserId, $coinSymbol, $amount);
            if ($result['code'] != 1) {
                throw new \Exception($result['msg']);
            }

            // 记录转账记录
            $this->recordAssetChange($fromUserId, $coinSymbol, -$amount, 'transfer_out', '转出给：' . $toUser->username);
            $this->recordAssetChange($toUserId, $coinSymbol, $amount, 'transfer_in', '转入来自：' . $fromUser->username);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '转账成功',
                'data' => [
                    'from_user' => $fromUser->username,
                    'to_user' => $toUser->username,
                    'coin_symbol' => $coinSymbol,
                    'amount' => $amount
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('转账失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 冻结资产
     */
    public function freezeAsset(int $userId, string $coinSymbol, float $amount): array
    {
        try {
            $asset = UserAsset::getUserAsset($userId, $coinSymbol);
            if (!$asset) {
                return ['code' => 0, 'msg' => '资产不存在'];
            }

            if (!$asset->freezeBalance($amount)) {
                return ['code' => 0, 'msg' => '冻结失败，余额不足'];
            }

            $this->recordAssetChange($userId, $coinSymbol, 0, 'freeze', '冻结资产：' . $amount);

            return [
                'code' => 1,
                'msg' => '资产冻结成功',
                'data' => [
                    'coin_symbol' => $coinSymbol,
                    'frozen_amount' => $amount,
                    'available' => $asset->available,
                    'frozen' => $asset->frozen
                ]
            ];

        } catch (\Exception $e) {
            Log::error('冻结资产失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '冻结失败'];
        }
    }

    /**
     * 解冻资产
     */
    public function unfreezeAsset(int $userId, string $coinSymbol, float $amount): array
    {
        try {
            $asset = UserAsset::getUserAsset($userId, $coinSymbol);
            if (!$asset) {
                return ['code' => 0, 'msg' => '资产不存在'];
            }

            if (!$asset->unfreezeBalance($amount)) {
                return ['code' => 0, 'msg' => '解冻失败，冻结余额不足'];
            }

            $this->recordAssetChange($userId, $coinSymbol, 0, 'unfreeze', '解冻资产：' . $amount);

            return [
                'code' => 1,
                'msg' => '资产解冻成功',
                'data' => [
                    'coin_symbol' => $coinSymbol,
                    'unfrozen_amount' => $amount,
                    'available' => $asset->available,
                    'frozen' => $asset->frozen
                ]
            ];

        } catch (\Exception $e) {
            Log::error('解冻资产失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '解冻失败'];
        }
    }

    /**
     * 获取资产变动记录
     */
    public function getAssetRecords(int $userId, string $coinSymbol = '', int $page = 1, int $limit = 20): array
    {
        try {
            // 这里应该从资产变动记录表获取数据
            // 暂时返回模拟数据
            $records = [
                [
                    'id' => 1,
                    'coin_symbol' => 'USDT',
                    'amount' => 1000.00,
                    'type' => 'deposit',
                    'description' => '充值',
                    'balance' => 1000.00,
                    'created_at' => date('Y-m-d H:i:s')
                ]
            ];

            return [
                'code' => 1,
                'data' => [
                    'records' => $records,
                    'total' => count($records),
                    'page' => $page,
                    'limit' => $limit
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取资产记录失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 获取充值地址
     */
    public function getDepositAddress(int $userId, string $coinSymbol): array
    {
        try {
            // 这里应该生成或获取用户的充值地址
            // 暂时返回模拟地址
            $addresses = [
                'USDT' => '**********************************',
                'BTC' => '**********************************',
                'ETH' => '******************************************',
                'LTC' => 'LTC**********************************',
                'EOS' => 'eosaccountname',
                'XRP' => 'rXRPAddress123456789'
            ];

            $address = $addresses[$coinSymbol] ?? '';
            if (!$address) {
                return ['code' => 0, 'msg' => '不支持的币种'];
            }

            return [
                'code' => 1,
                'data' => [
                    'coin_symbol' => $coinSymbol,
                    'address' => $address,
                    'qr_code' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取充值地址失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 记录资产变动
     */
    private function recordAssetChange(int $userId, string $coinSymbol, float $amount, string $type, string $description, string $txHash = ''): void
    {
        try {
            // 这里应该记录到资产变动记录表
            Log::info('资产变动记录', [
                'user_id' => $userId,
                'coin_symbol' => $coinSymbol,
                'amount' => $amount,
                'type' => $type,
                'description' => $description,
                'tx_hash' => $txHash,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            // 记录失败不影响主流程
        }
    }

    /**
     * 获取支持的币种列表
     */
    public function getSupportedCoins(): array
    {
        return [
            'code' => 1,
            'data' => [
                ['symbol' => 'USDT', 'name' => 'Tether USD', 'decimals' => 6, 'min_deposit' => 1, 'min_withdraw' => 10],
                ['symbol' => 'BTC', 'name' => 'Bitcoin', 'decimals' => 8, 'min_deposit' => 0.0001, 'min_withdraw' => 0.001],
                ['symbol' => 'ETH', 'name' => 'Ethereum', 'decimals' => 18, 'min_deposit' => 0.001, 'min_withdraw' => 0.01],
                ['symbol' => 'LTC', 'name' => 'Litecoin', 'decimals' => 8, 'min_deposit' => 0.001, 'min_withdraw' => 0.01],
                ['symbol' => 'EOS', 'name' => 'EOS', 'decimals' => 4, 'min_deposit' => 0.1, 'min_withdraw' => 1],
                ['symbol' => 'XRP', 'name' => 'Ripple', 'decimals' => 6, 'min_deposit' => 1, 'min_withdraw' => 10]
            ]
        ];
    }
}
