<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\SystemConfig;
use app\common\model\ContractConfig;
use think\facade\Cache;
use think\facade\Log;
use think\facade\Filesystem;

/**
 * 系统配置服务
 */
class SystemConfigService
{
    // 配置类型
    const CONFIG_TYPE_BASIC = 'basic';           // 基础配置
    const CONFIG_TYPE_TRADE = 'trade';           // 交易配置
    const CONFIG_TYPE_CONTRACT = 'contract';     // 合约配置
    const CONFIG_TYPE_UI = 'ui';                 // 界面配置
    const CONFIG_TYPE_NOTIFICATION = 'notification'; // 通知配置

    /**
     * 获取系统配置
     */
    public function getConfig(string $type = '', string $key = ''): array
    {
        try {
            $query = SystemConfig::where('status', 1);

            if ($type) {
                $query->where('type', $type);
            }

            if ($key) {
                $query->where('key', $key);
                $config = $query->find();
                return $config ? $config->toArray() : [];
            }

            $configs = $query->select();
            $result = [];

            foreach ($configs as $config) {
                if (!isset($result[$config->type])) {
                    $result[$config->type] = [];
                }
                $result[$config->type][$config->key] = [
                    'value' => $config->value,
                    'description' => $config->description
                ];
            }

            return [
                'code' => 1,
                'data' => $type ? ($result[$type] ?? []) : $result
            ];

        } catch (\Exception $e) {
            Log::error('获取系统配置失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取配置失败'];
        }
    }

    /**
     * 更新系统配置
     */
    public function updateConfig(string $type, string $key, $value, int $operatorId): array
    {
        try {
            // 验证操作员权限
            $operator = \app\common\model\User::find($operatorId);
            if (!$operator || $operator->role !== 'admin') {
                return ['code' => 0, 'msg' => '只有管理员可以修改系统配置'];
            }

            $config = SystemConfig::where('type', $type)->where('key', $key)->find();

            if ($config) {
                $oldValue = $config->value;
                $config->value = is_array($value) ? json_encode($value) : $value;
                $config->updated_at = date('Y-m-d H:i:s');
                $config->save();
            } else {
                // 创建新配置
                SystemConfig::create([
                    'type' => $type,
                    'key' => $key,
                    'value' => is_array($value) ? json_encode($value) : $value,
                    'status' => 1,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
                $oldValue = null;
            }

            // 清除缓存
            $this->clearConfigCache($type, $key);

            // 记录操作日志
            Log::info("系统配置更新", [
                'type' => $type,
                'key' => $key,
                'old_value' => $oldValue,
                'new_value' => $value,
                'operator_id' => $operatorId
            ]);

            return [
                'code' => 1,
                'msg' => '配置更新成功'
            ];

        } catch (\Exception $e) {
            Log::error('更新系统配置失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '配置更新失败'];
        }
    }

    /**
     * 上传并替换图片
     */
    public function uploadImage(string $type, string $key, $file, int $operatorId): array
    {
        try {
            // 验证操作员权限
            $operator = \app\common\model\User::find($operatorId);
            if (!$operator || $operator->role !== 'admin') {
                return ['code' => 0, 'msg' => '只有管理员可以上传图片'];
            }

            // 验证文件
            if (!$file || !$file->isValid()) {
                return ['code' => 0, 'msg' => '无效的文件'];
            }

            // 验证文件类型
            $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            $extension = strtolower($file->extension());
            
            if (!in_array($extension, $allowedTypes)) {
                return ['code' => 0, 'msg' => '只支持图片格式：' . implode(', ', $allowedTypes)];
            }

            // 验证文件大小（5MB）
            if ($file->getSize() > 5 * 1024 * 1024) {
                return ['code' => 0, 'msg' => '文件大小不能超过5MB'];
            }

            // 生成文件名
            $filename = $type . '_' . $key . '_' . date('YmdHis') . '.' . $extension;
            
            // 保存文件
            $savePath = Filesystem::disk('public')->putFileAs('images/config', $file, $filename);
            
            if ($savePath) {
                $imageUrl = '/storage/' . $savePath;
                
                // 更新配置
                $result = $this->updateConfig($type, $key, $imageUrl, $operatorId);
                
                if ($result['code']) {
                    return [
                        'code' => 1,
                        'msg' => '图片上传成功',
                        'data' => [
                            'url' => $imageUrl,
                            'filename' => $filename
                        ]
                    ];
                } else {
                    return $result;
                }
            } else {
                return ['code' => 0, 'msg' => '文件保存失败'];
            }

        } catch (\Exception $e) {
            Log::error('上传图片失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '图片上传失败'];
        }
    }

    /**
     * 更新合约配置
     */
    public function updateContractConfig(string $symbol, array $config, int $operatorId): array
    {
        try {
            // 验证操作员权限
            $operator = \app\common\model\User::find($operatorId);
            if (!$operator || $operator->role !== 'admin') {
                return ['code' => 0, 'msg' => '只有管理员可以修改合约配置'];
            }

            $contractConfig = ContractConfig::where('symbol', $symbol)->find();

            if ($contractConfig) {
                // 更新现有配置
                if (isset($config['profit_rates'])) {
                    $contractConfig->profit_rates = json_encode($config['profit_rates']);
                }
                
                if (isset($config['time_periods'])) {
                    $contractConfig->time_periods = json_encode($config['time_periods']);
                }
                
                if (isset($config['min_amount'])) {
                    $contractConfig->min_amount = $config['min_amount'];
                }
                
                if (isset($config['max_amount'])) {
                    $contractConfig->max_amount = $config['max_amount'];
                }
                
                if (isset($config['quick_amounts'])) {
                    $contractConfig->quick_amounts = json_encode($config['quick_amounts']);
                }

                $contractConfig->updated_at = date('Y-m-d H:i:s');
                $contractConfig->save();

            } else {
                // 创建新配置
                ContractConfig::create([
                    'symbol' => $symbol,
                    'profit_rates' => json_encode($config['profit_rates'] ?? [75, 80, 85]),
                    'time_periods' => json_encode($config['time_periods'] ?? [60, 180, 300]),
                    'min_amount' => $config['min_amount'] ?? 10,
                    'max_amount' => $config['max_amount'] ?? 10000,
                    'quick_amounts' => json_encode($config['quick_amounts'] ?? [50, 100, 500, 1000]),
                    'status' => 1,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }

            // 清除缓存
            Cache::delete("contract_config_{$symbol}");

            Log::info("合约配置更新", [
                'symbol' => $symbol,
                'config' => $config,
                'operator_id' => $operatorId
            ]);

            return [
                'code' => 1,
                'msg' => '合约配置更新成功'
            ];

        } catch (\Exception $e) {
            Log::error('更新合约配置失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '合约配置更新失败'];
        }
    }

    /**
     * 获取合约配置
     */
    public function getContractConfig(string $symbol = ''): array
    {
        try {
            if ($symbol) {
                $cacheKey = "contract_config_{$symbol}";
                $config = Cache::get($cacheKey);
                
                if (!$config) {
                    $contractConfig = ContractConfig::where('symbol', $symbol)->find();
                    if ($contractConfig) {
                        $config = $contractConfig->toArray();
                        $config['profit_rates'] = json_decode($config['profit_rates'], true);
                        $config['time_periods'] = json_decode($config['time_periods'], true);
                        $config['quick_amounts'] = json_decode($config['quick_amounts'], true);
                        
                        Cache::set($cacheKey, $config, 3600);
                    }
                }
                
                return [
                    'code' => 1,
                    'data' => $config ?: []
                ];
            } else {
                $configs = ContractConfig::where('status', 1)->select();
                $result = [];
                
                foreach ($configs as $config) {
                    $configData = $config->toArray();
                    $configData['profit_rates'] = json_decode($configData['profit_rates'], true);
                    $configData['time_periods'] = json_decode($configData['time_periods'], true);
                    $configData['quick_amounts'] = json_decode($configData['quick_amounts'], true);
                    $result[] = $configData;
                }
                
                return [
                    'code' => 1,
                    'data' => $result
                ];
            }

        } catch (\Exception $e) {
            Log::error('获取合约配置失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取配置失败'];
        }
    }

    /**
     * 批量更新UI配置
     */
    public function updateUIConfig(array $configs, int $operatorId): array
    {
        try {
            // 验证操作员权限
            $operator = \app\common\model\User::find($operatorId);
            if (!$operator || $operator->role !== 'admin') {
                return ['code' => 0, 'msg' => '只有管理员可以修改UI配置'];
            }

            $successCount = 0;
            $failCount = 0;

            foreach ($configs as $key => $value) {
                $result = $this->updateConfig(self::CONFIG_TYPE_UI, $key, $value, $operatorId);
                if ($result['code']) {
                    $successCount++;
                } else {
                    $failCount++;
                }
            }

            return [
                'code' => 1,
                'msg' => "UI配置更新完成，成功：{$successCount}，失败：{$failCount}",
                'data' => [
                    'success' => $successCount,
                    'fail' => $failCount
                ]
            ];

        } catch (\Exception $e) {
            Log::error('批量更新UI配置失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'UI配置更新失败'];
        }
    }

    /**
     * 获取快捷金额配置
     */
    public function getQuickAmounts(string $symbol = 'default'): array
    {
        try {
            // 先尝试获取特定交易对的配置
            $contractConfig = ContractConfig::where('symbol', $symbol)->find();
            
            if ($contractConfig && !empty($contractConfig->quick_amounts)) {
                $quickAmounts = json_decode($contractConfig->quick_amounts, true);
            } else {
                // 使用默认配置
                $defaultConfig = $this->getConfig(self::CONFIG_TYPE_CONTRACT, 'default_quick_amounts');
                $quickAmounts = $defaultConfig ? json_decode($defaultConfig['value'], true) : [50, 100, 500, 1000];
            }

            return [
                'code' => 1,
                'data' => $quickAmounts
            ];

        } catch (\Exception $e) {
            Log::error('获取快捷金额配置失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取配置失败'];
        }
    }

    /**
     * 清除配置缓存
     */
    private function clearConfigCache(string $type, string $key): void
    {
        try {
            $cacheKeys = [
                "system_config_{$type}_{$key}",
                "system_config_{$type}",
                "system_config_all"
            ];

            foreach ($cacheKeys as $cacheKey) {
                Cache::delete($cacheKey);
            }

        } catch (\Exception $e) {
            Log::error('清除配置缓存失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取所有可配置的图片项
     */
    public function getConfigurableImages(): array
    {
        return [
            'ui' => [
                'logo' => '网站Logo',
                'favicon' => '网站图标',
                'login_bg' => '登录页背景',
                'banner_1' => '首页轮播图1',
                'banner_2' => '首页轮播图2',
                'banner_3' => '首页轮播图3',
                'about_bg' => '关于我们背景',
                'contact_bg' => '联系我们背景'
            ],
            'contract' => [
                'up_icon' => '看涨图标',
                'down_icon' => '看跌图标',
                'win_icon' => '盈利图标',
                'lose_icon' => '亏损图标'
            ]
        ];
    }
}
