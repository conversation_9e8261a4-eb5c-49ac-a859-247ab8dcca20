<!DOCTYPE html>
<html lang="zh-CN" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试用户管理 - 代理端</title>
    <link rel="stylesheet" href="/static/css/modern-theme.css">
    <link rel="stylesheet" href="/static/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <div class="admin-sidebar">
            <div class="sidebar-header">
                <h2>代理端管理</h2>
                <p>测试用户管理</p>
            </div>
            
            <nav class="admin-nav">
                <a href="/agent/dashboard/index" class="nav-item">
                    <span class="nav-icon">📊</span>
                    <span class="nav-text">数据统计</span>
                </a>
                <a href="/agent/dashboard/users" class="nav-item">
                    <span class="nav-icon">👥</span>
                    <span class="nav-text">下级用户</span>
                </a>
                <a href="/agent/dashboard/testUsers" class="nav-item active">
                    <span class="nav-icon">🧪</span>
                    <span class="nav-text">测试用户</span>
                </a>
                <a href="/agent/dashboard/finances" class="nav-item">
                    <span class="nav-icon">💰</span>
                    <span class="nav-text">财务记录</span>
                </a>
                <a href="/agent/commission" class="nav-item">
                    <span class="nav-icon">💎</span>
                    <span class="nav-text">佣金收益</span>
                </a>
            </nav>
        </div>

        <!-- 主要内容 -->
        <div class="admin-main">
            <!-- 页面头部 -->
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1>测试用户管理</h1>
                        <p class="text-secondary">创建和管理测试用户账户</p>
                    </div>
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="showCreateModal()">
                            <span>➕</span> 创建测试用户
                        </button>
                    </div>
                </div>
            </div>

            <!-- 测试用户列表 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">测试用户列表</h3>
                    <div class="card-actions">
                        <button class="btn btn-outline btn-sm" onclick="refreshList()">刷新</button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>用户名</th>
                                    <th>邮箱</th>
                                    <th>USDT余额</th>
                                    <th>创建时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="testUsersTable">
                                {volist name="test_users" id="user"}
                                <tr>
                                    <td>{$user.id}</td>
                                    <td>
                                        <span class="badge badge-warning">TEST</span>
                                        {$user.username}
                                    </td>
                                    <td>{$user.email}</td>
                                    <td>
                                        <span class="balance-amount" data-user-id="{$user.id}">
                                            加载中...
                                        </span>
                                    </td>
                                    <td>{$user.created_at}</td>
                                    <td>
                                        <span class="badge badge-{$user.status == 1 ? 'success' : 'danger'}">
                                            {$user.status == 1 ? '正常' : '禁用'}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline" onclick="showBalanceModal({$user.id}, '{$user.username}')">
                                                调整余额
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="deleteTestUser({$user.id}, '{$user.username}')">
                                                删除
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {/volist}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建测试用户模态框 -->
    <div class="modal" id="createModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>创建测试用户</h3>
                <button class="modal-close" onclick="hideCreateModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="createForm">
                    <div class="form-group">
                        <label class="form-label">用户名 *</label>
                        <input type="text" class="form-control" name="username" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">密码 *</label>
                        <input type="password" class="form-control" name="password" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">邮箱</label>
                        <input type="email" class="form-control" name="email">
                    </div>
                    <div class="form-group">
                        <label class="form-label">初始余额 (USDT)</label>
                        <input type="number" class="form-control" name="initial_balance" value="1000" min="0" step="0.01">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="hideCreateModal()">取消</button>
                <button class="btn btn-primary" onclick="createTestUser()">创建</button>
            </div>
        </div>
    </div>

    <!-- 调整余额模态框 -->
    <div class="modal" id="balanceModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>调整用户余额</h3>
                <button class="modal-close" onclick="hideBalanceModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="balanceForm">
                    <input type="hidden" name="user_id" id="balanceUserId">
                    <div class="form-group">
                        <label class="form-label">用户名</label>
                        <input type="text" class="form-control" id="balanceUsername" readonly>
                    </div>
                    <div class="form-group">
                        <label class="form-label">币种</label>
                        <select class="form-control" name="coin_symbol">
                            <option value="USDT">USDT</option>
                            <option value="BTC">BTC</option>
                            <option value="ETH">ETH</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">调整金额</label>
                        <input type="number" class="form-control" name="amount" step="0.00000001" required>
                        <small class="form-text">正数为增加，负数为减少</small>
                    </div>
                    <div class="form-group">
                        <label class="form-label">备注</label>
                        <input type="text" class="form-control" name="remark" value="代理商调整测试用户余额">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="hideBalanceModal()">取消</button>
                <button class="btn btn-primary" onclick="adjustBalance()">确认调整</button>
            </div>
        </div>
    </div>

    <script src="/static/js/admin.js"></script>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initTheme();
            loadUserBalances();
        });

        // 显示创建模态框
        function showCreateModal() {
            document.getElementById('createModal').style.display = 'flex';
        }

        // 隐藏创建模态框
        function hideCreateModal() {
            document.getElementById('createModal').style.display = 'none';
            document.getElementById('createForm').reset();
        }

        // 创建测试用户
        function createTestUser() {
            const form = document.getElementById('createForm');
            const formData = new FormData(form);
            formData.append('action', 'create');

            fetch('/agent/dashboard/testUsers', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    showNotification('测试用户创建成功', 'success');
                    hideCreateModal();
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification(data.msg || '创建失败', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('网络错误，请重试', 'error');
            });
        }

        // 显示余额调整模态框
        function showBalanceModal(userId, username) {
            document.getElementById('balanceUserId').value = userId;
            document.getElementById('balanceUsername').value = username;
            document.getElementById('balanceModal').style.display = 'flex';
        }

        // 隐藏余额调整模态框
        function hideBalanceModal() {
            document.getElementById('balanceModal').style.display = 'none';
            document.getElementById('balanceForm').reset();
        }

        // 调整余额
        function adjustBalance() {
            const form = document.getElementById('balanceForm');
            const formData = new FormData(form);
            formData.append('action', 'adjust_balance');

            fetch('/agent/dashboard/testUsers', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    showNotification('余额调整成功', 'success');
                    hideBalanceModal();
                    loadUserBalances();
                } else {
                    showNotification(data.msg || '调整失败', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('网络错误，请重试', 'error');
            });
        }

        // 删除测试用户
        function deleteTestUser(userId, username) {
            if (!confirm(`确定要删除测试用户 "${username}" 吗？此操作不可恢复！`)) {
                return;
            }

            const formData = new FormData();
            formData.append('action', 'delete');
            formData.append('user_id', userId);

            fetch('/agent/dashboard/testUsers', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    showNotification('测试用户删除成功', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification(data.msg || '删除失败', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('网络错误，请重试', 'error');
            });
        }

        // 加载用户余额
        function loadUserBalances() {
            const balanceElements = document.querySelectorAll('.balance-amount');
            
            balanceElements.forEach(element => {
                const userId = element.getAttribute('data-user-id');
                
                fetch(`/user/assets/api?user_id=${userId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1 && data.data.USDT) {
                        const balance = parseFloat(data.data.USDT.available || 0);
                        element.textContent = balance.toFixed(2) + ' USDT';
                    } else {
                        element.textContent = '0.00 USDT';
                    }
                })
                .catch(error => {
                    element.textContent = '加载失败';
                });
            });
        }

        // 刷新列表
        function refreshList() {
            location.reload();
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <span>${message}</span>
                <button onclick="this.parentElement.remove()">×</button>
            `;
            
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 16px;
                border-radius: 4px;
                color: white;
                z-index: 10000;
                display: flex;
                align-items: center;
                gap: 8px;
                background: ${type === 'success' ? '#0ECB81' : type === 'error' ? '#F6465D' : '#1890FF'};
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 3000);
        }

        // 主题切换
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
        }

        // 初始化主题
        function initTheme() {
            const savedTheme = localStorage.getItem('theme') || 'dark';
            document.documentElement.setAttribute('data-theme', savedTheme);
        }
    </script>
</body>
</html>
