<?php
declare (strict_types = 1);

namespace app\api\controller;

use think\App;
use think\exception\ValidateException;
use think\Validate;
use think\Response;
use think\facade\Log;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

/**
 * API基础控制器
 */
abstract class BaseController
{
    /**
     * Request实例
     * @var \think\Request
     */
    protected $request;

    /**
     * 应用实例
     * @var \think\App
     */
    protected $app;

    /**
     * 当前用户ID
     * @var int
     */
    protected $userId = 0;

    /**
     * 当前用户信息
     * @var array
     */
    protected $userInfo = [];

    /**
     * 构造方法
     * @access public
     * @param  App  $app  应用对象
     */
    public function __construct(App $app = null)
    {
        $this->app     = $app ?: app();
        $this->request = $this->app->request;

        // 控制器初始化
        $this->initialize();
    }

    // 初始化
    protected function initialize()
    {
        // 验证用户身份
        $this->checkAuth();
    }

    /**
     * 验证用户身份
     */
    protected function checkAuth()
    {
        // 获取不需要验证的方法
        $noAuthMethods = $this->getNoAuthMethods();
        $action = $this->request->action();
        
        if (in_array($action, $noAuthMethods)) {
            return;
        }

        // 获取token
        $token = $this->request->header('Authorization');
        if (empty($token)) {
            $this->error('请先登录', 401);
        }

        if (strpos($token, 'Bearer ') === 0) {
            $token = substr($token, 7);
        }

        try {
            // 验证token
            $decoded = JWT::decode($token, new Key(config('jwt.secret', 'your-secret-key'), 'HS256'));
            
            $this->userId = $decoded->user_id;
            $this->userInfo = [
                'user_id' => $decoded->user_id,
                'username' => $decoded->username ?? ''
            ];

        } catch (\Exception $e) {
            $this->error('登录已过期，请重新登录', 401);
        }
    }

    /**
     * 获取不需要验证的方法
     */
    protected function getNoAuthMethods(): array
    {
        return [];
    }

    /**
     * 获取当前用户ID
     */
    protected function getUserId(): int
    {
        return $this->userId;
    }

    /**
     * 获取当前用户信息
     */
    protected function getUserInfo(): array
    {
        return $this->userInfo;
    }

    /**
     * 验证数据
     * @param  array        $data     数据
     * @param  string|array $validate 验证器名或者验证规则数组
     * @param  array        $message  提示信息
     * @param  bool         $batch    是否批量验证
     * @return array|string|true
     * @throws ValidateException
     */
    protected function validate(array $data, $validate, array $message = [], bool $batch = false)
    {
        if (is_array($validate)) {
            $v = new Validate();
            $v->rule($validate);
        } else {
            if (strpos($validate, '.')) {
                // 支持场景
                [$validate, $scene] = explode('.', $validate);
            }
            $class = false !== strpos($validate, '\\') ? $validate : $this->app->parseClass('validate', $validate);
            $v     = new $class();
            if (!empty($scene)) {
                $v->scene($scene);
            }
        }

        $v->message($message);

        if ($batch || $this->batchValidate) {
            $v->batch(true);
        }

        return $v->failException(true)->check($data);
    }

    /**
     * 返回成功响应
     */
    protected function success($data = [], string $msg = 'success', int $code = 200): Response
    {
        $result = [
            'code' => 1,
            'msg' => $msg,
            'data' => $data,
            'timestamp' => time()
        ];

        return json($result, $code);
    }

    /**
     * 返回错误响应
     */
    protected function error(string $msg = 'error', int $code = 400, $data = []): Response
    {
        $result = [
            'code' => 0,
            'msg' => $msg,
            'data' => $data,
            'timestamp' => time()
        ];

        // 记录错误日志
        Log::error('API错误: ' . $msg, [
            'url' => $this->request->url(),
            'method' => $this->request->method(),
            'params' => $this->request->param(),
            'user_id' => $this->userId
        ]);

        return json($result, $code);
    }

    /**
     * 返回分页数据
     */
    protected function paginate($data, int $total, int $page = 1, int $limit = 20): Response
    {
        $result = [
            'code' => 1,
            'msg' => 'success',
            'data' => [
                'list' => $data,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ],
            'timestamp' => time()
        ];

        return json($result);
    }

    /**
     * 获取请求参数
     */
    protected function getParams(array $rules = []): array
    {
        $params = $this->request->param();
        
        if (!empty($rules)) {
            $filteredParams = [];
            foreach ($rules as $key => $rule) {
                if (is_numeric($key)) {
                    $filteredParams[$rule] = $params[$rule] ?? null;
                } else {
                    $filteredParams[$key] = $params[$key] ?? $rule;
                }
            }
            return $filteredParams;
        }
        
        return $params;
    }

    /**
     * 记录操作日志
     */
    protected function logOperation(string $operation, array $data = []): void
    {
        Log::info('用户操作', [
            'user_id' => $this->userId,
            'operation' => $operation,
            'data' => $data,
            'ip' => $this->request->ip(),
            'user_agent' => $this->request->header('User-Agent'),
            'url' => $this->request->url(),
            'method' => $this->request->method(),
            'timestamp' => time()
        ]);
    }
}
