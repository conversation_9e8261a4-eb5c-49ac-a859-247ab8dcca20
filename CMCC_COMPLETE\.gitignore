# Composer
/vendor/
composer.lock

# Environment files
.env
.env.local
.env.production

# Runtime files
/runtime/
!/runtime/.gitignore
!/runtime/cache/.gitignore
!/runtime/log/.gitignore
!/runtime/session/.gitignore
!/runtime/temp/.gitignore

# Logs
*.log
logs/
log/

# Cache
cache/
*.cache

# Uploads
/public/uploads/
/public/storage/
!/public/uploads/.gitignore
!/public/storage/.gitignore

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js (if using npm for frontend assets)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build files
/build/
/dist/

# Temporary files
*.tmp
*.temp
temp/

# Database
*.sqlite
*.sqlite3
*.db

# Backup files
*.bak
*.backup
*.old

# Configuration files with sensitive data
config/database.php
config/redis.php

# WebSocket logs
/websocket/logs/
!/websocket/logs/.gitignore

# Test files
/tests/coverage/
phpunit.xml

# Documentation build
/docs/build/

# Package files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Compiled files
*.com
*.class
*.dll
*.exe
*.o
*.so

# Editor directories and files
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
