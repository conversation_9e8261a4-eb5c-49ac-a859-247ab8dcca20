<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\UserKyc;
use app\common\model\User;
use think\facade\Log;

/**
 * 简化KYC服务类 - 只包含基础认证功能
 */
class SimpleKycService
{
    // KYC状态
    const STATUS_PENDING = 0;      // 待审核
    const STATUS_APPROVED = 1;     // 已通过
    const STATUS_REJECTED = 2;     // 已拒绝

    // KYC等级
    const LEVEL_BASIC = 1;         // 基础认证
    const LEVEL_ADVANCED = 2;      // 高级认证

    /**
     * 提交基础KYC认证
     */
    public function submitBasicKyc(int $userId, array $data): array
    {
        try {
            // 验证数据
            $validation = $this->validateKycData($data);
            if (!$validation['code']) {
                return $validation;
            }

            // 检查是否已提交
            $existingKyc = UserKyc::where('user_id', $userId)->find();
            if ($existingKyc && $existingKyc->status == self::STATUS_PENDING) {
                return ['code' => 0, 'msg' => '已有待审核的KYC申请'];
            }

            if ($existingKyc && $existingKyc->status == self::STATUS_APPROVED) {
                return ['code' => 0, 'msg' => 'KYC已通过认证'];
            }

            // 创建或更新KYC记录
            $kycData = [
                'user_id' => $userId,
                'real_name' => $data['real_name'],
                'id_number' => $data['id_number'],
                'id_type' => $data['id_type'] ?? 'id_card',
                'nationality' => $data['nationality'] ?? 'CN',
                'id_front_image' => $data['id_front_image'] ?? '',
                'id_back_image' => $data['id_back_image'] ?? '',
                'selfie_image' => $data['selfie_image'] ?? '',
                'level' => self::LEVEL_BASIC,
                'status' => self::STATUS_PENDING,
                'submitted_at' => date('Y-m-d H:i:s')
            ];

            if ($existingKyc) {
                $existingKyc->save($kycData);
                $kyc = $existingKyc;
            } else {
                $kyc = UserKyc::create($kycData);
            }

            Log::info("KYC认证提交", [
                'user_id' => $userId,
                'real_name' => $data['real_name'],
                'id_number' => substr($data['id_number'], 0, 6) . '****'
            ]);

            return [
                'code' => 1,
                'msg' => 'KYC认证提交成功，请等待审核',
                'data' => [
                    'kyc_id' => $kyc->id,
                    'status' => 'pending'
                ]
            ];
        } catch (\Exception $e) {
            Log::error('KYC认证提交失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'KYC认证提交失败'];
        }
    }

    /**
     * 审核KYC申请
     */
    public function reviewKyc(int $kycId, array $reviewData): array
    {
        try {
            $kyc = UserKyc::find($kycId);
            if (!$kyc) {
                return ['code' => 0, 'msg' => 'KYC记录不存在'];
            }

            if ($kyc->status !== self::STATUS_PENDING) {
                return ['code' => 0, 'msg' => 'KYC状态不允许审核'];
            }

            if ($reviewData['action'] === 'approve') {
                // 通过审核
                $kyc->status = self::STATUS_APPROVED;
                $kyc->approved_at = date('Y-m-d H:i:s');
                $kyc->reviewer_id = $reviewData['reviewer_id'] ?? 0;
                $kyc->review_notes = $reviewData['notes'] ?? '';

                // 更新用户KYC等级
                $user = User::find($kyc->user_id);
                if ($user) {
                    $user->kyc_level = $kyc->level;
                    $user->save();
                }

                $message = 'KYC审核通过';
            } else {
                // 拒绝审核
                $kyc->status = self::STATUS_REJECTED;
                $kyc->rejected_at = date('Y-m-d H:i:s');
                $kyc->reviewer_id = $reviewData['reviewer_id'] ?? 0;
                $kyc->reject_reason = $reviewData['reason'] ?? '';

                $message = 'KYC审核拒绝';
            }

            $kyc->save();

            Log::info("KYC审核完成", [
                'kyc_id' => $kycId,
                'user_id' => $kyc->user_id,
                'action' => $reviewData['action'],
                'reviewer_id' => $reviewData['reviewer_id'] ?? 0
            ]);

            return [
                'code' => 1,
                'msg' => $message
            ];
        } catch (\Exception $e) {
            Log::error('KYC审核失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'KYC审核失败'];
        }
    }

    /**
     * 获取用户KYC状态
     */
    public function getUserKycStatus(int $userId): array
    {
        $kyc = UserKyc::where('user_id', $userId)->find();

        if (!$kyc) {
            return [
                'code' => 1,
                'data' => [
                    'status' => 'not_submitted',
                    'level' => 0,
                    'message' => '未提交KYC认证'
                ]
            ];
        }

        $statusTexts = [
            self::STATUS_PENDING => '审核中',
            self::STATUS_APPROVED => '已通过',
            self::STATUS_REJECTED => '已拒绝'
        ];

        return [
            'code' => 1,
            'data' => [
                'status' => $kyc->status,
                'status_text' => $statusTexts[$kyc->status] ?? '未知',
                'level' => $kyc->level,
                'real_name' => $kyc->real_name,
                'submitted_at' => $kyc->submitted_at,
                'approved_at' => $kyc->approved_at,
                'rejected_at' => $kyc->rejected_at,
                'reject_reason' => $kyc->reject_reason
            ]
        ];
    }

    /**
     * 获取待审核的KYC列表
     */
    public function getPendingKycList(int $page = 1, int $limit = 20): array
    {
        $query = UserKyc::where('status', self::STATUS_PENDING)
                        ->with(['user'])
                        ->order('submitted_at', 'asc');

        $kycList = $query->paginate([
            'list_rows' => $limit,
            'page' => $page
        ]);

        return [
            'code' => 1,
            'data' => [
                'items' => $kycList->items(),
                'total' => $kycList->total(),
                'page' => $page,
                'limit' => $limit
            ]
        ];
    }

    /**
     * 获取KYC详情
     */
    public function getKycDetail(int $kycId): array
    {
        $kyc = UserKyc::with(['user'])->find($kycId);

        if (!$kyc) {
            return ['code' => 0, 'msg' => 'KYC记录不存在'];
        }

        return [
            'code' => 1,
            'data' => $kyc->toArray()
        ];
    }

    /**
     * 验证KYC数据
     */
    private function validateKycData(array $data): array
    {
        if (empty($data['real_name'])) {
            return ['code' => 0, 'msg' => '真实姓名不能为空'];
        }

        if (empty($data['id_number'])) {
            return ['code' => 0, 'msg' => '身份证号不能为空'];
        }

        // 简单的身份证号格式验证
        if (!preg_match('/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/', $data['id_number'])) {
            return ['code' => 0, 'msg' => '身份证号格式不正确'];
        }

        return ['code' => 1, 'msg' => '验证通过'];
    }

    /**
     * 获取KYC统计信息
     */
    public function getKycStatistics(): array
    {
        $total = UserKyc::count();
        $pending = UserKyc::where('status', self::STATUS_PENDING)->count();
        $approved = UserKyc::where('status', self::STATUS_APPROVED)->count();
        $rejected = UserKyc::where('status', self::STATUS_REJECTED)->count();

        return [
            'code' => 1,
            'data' => [
                'total' => $total,
                'pending' => $pending,
                'approved' => $approved,
                'rejected' => $rejected,
                'approval_rate' => $total > 0 ? round(($approved / $total) * 100, 2) : 0
            ]
        ];
    }

    /**
     * 批量审核KYC
     */
    public function batchReviewKyc(array $kycIds, array $reviewData): array
    {
        try {
            $successCount = 0;
            $failCount = 0;

            foreach ($kycIds as $kycId) {
                $result = $this->reviewKyc($kycId, $reviewData);
                if ($result['code']) {
                    $successCount++;
                } else {
                    $failCount++;
                }
            }

            return [
                'code' => 1,
                'msg' => "批量审核完成，成功{$successCount}个，失败{$failCount}个",
                'data' => [
                    'success_count' => $successCount,
                    'fail_count' => $failCount
                ]
            ];
        } catch (\Exception $e) {
            Log::error('批量审核KYC失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '批量审核失败'];
        }
    }
}
