<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$title|default='数字货币交易平台'}</title>
    <meta name="description" content="专业的数字货币交易平台，支持币币交易、合约交易、新币认购等多种交易方式">
    <meta name="keywords" content="数字货币,比特币,以太坊,交易平台,区块链">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="/static/css/style.css" rel="stylesheet">
    
    {block name="css"}{/block}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <img src="/static/images/logo.png" alt="Logo" height="32" class="me-2">
                数字货币交易平台
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            交易
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/trade">币币交易</a></li>
                            <li><a class="dropdown-item" href="/contract">合约交易</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ido">新币认购</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            资产
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/wallet">我的钱包</a></li>
                            <li><a class="dropdown-item" href="/wallet/deposit">充值</a></li>
                            <li><a class="dropdown-item" href="/wallet/withdraw">提现</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/help">帮助</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    {if condition="session('user_id')"}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> {$Think.session.username|default='用户'}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/user/profile">个人中心</a></li>
                            <li><a class="dropdown-item" href="/user/assets">资产管理</a></li>
                            <li><a class="dropdown-item" href="/user/orders">我的订单</a></li>
                            <li><a class="dropdown-item" href="/user/invite">邀请管理</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/auth/logout">退出登录</a></li>
                        </ul>
                    </li>
                    {else}
                    <li class="nav-item">
                        <a class="nav-link" href="/auth/login">登录</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/auth/register">注册</a>
                    </li>
                    {/if}
                    
                    <!-- 语言切换 -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-globe"></i>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="?lang=zh-cn">简体中文</a></li>
                            <li><a class="dropdown-item" href="?lang=en">English</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        {block name="content"}{/block}
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-3">
                    <h5>关于我们</h5>
                    <ul class="list-unstyled">
                        <li><a href="/about" class="text-light">公司介绍</a></li>
                        <li><a href="/team" class="text-light">团队介绍</a></li>
                        <li><a href="/contact" class="text-light">联系我们</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>产品服务</h5>
                    <ul class="list-unstyled">
                        <li><a href="/trade" class="text-light">币币交易</a></li>
                        <li><a href="/contract" class="text-light">合约交易</a></li>
                        <li><a href="/ido" class="text-light">新币认购</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>帮助支持</h5>
                    <ul class="list-unstyled">
                        <li><a href="/help" class="text-light">帮助中心</a></li>
                        <li><a href="/api-doc" class="text-light">API文档</a></li>
                        <li><a href="/fee" class="text-light">费率说明</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>关注我们</h5>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter fa-2x"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-telegram fa-2x"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-discord fa-2x"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; 2024 数字货币交易平台. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="/privacy" class="text-light me-3">隐私政策</a>
                    <a href="/terms" class="text-light">服务条款</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- 返回顶部按钮 -->
    <button id="backToTop" class="btn btn-primary position-fixed" style="bottom: 20px; right: 20px; display: none; z-index: 1000;">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- 全局提示框 -->
    <div id="globalToast" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">
        <div id="toast" class="toast" role="alert">
            <div class="toast-header">
                <strong class="me-auto">提示</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body"></div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- 自定义脚本 -->
    <script src="/static/js/common.js"></script>
    
    {block name="js"}{/block}

    <script>
        // 返回顶部功能
        $(window).scroll(function() {
            if ($(this).scrollTop() > 100) {
                $('#backToTop').fadeIn();
            } else {
                $('#backToTop').fadeOut();
            }
        });

        $('#backToTop').click(function() {
            $('html, body').animate({scrollTop: 0}, 800);
            return false;
        });

        // 全局提示函数
        window.showToast = function(message, type = 'info') {
            const toast = $('#toast');
            const toastBody = toast.find('.toast-body');
            
            // 设置消息内容
            toastBody.text(message);
            
            // 设置样式
            toast.removeClass('text-bg-success text-bg-danger text-bg-warning text-bg-info');
            switch(type) {
                case 'success':
                    toast.addClass('text-bg-success');
                    break;
                case 'error':
                case 'danger':
                    toast.addClass('text-bg-danger');
                    break;
                case 'warning':
                    toast.addClass('text-bg-warning');
                    break;
                default:
                    toast.addClass('text-bg-info');
            }
            
            // 显示提示
            const bsToast = new bootstrap.Toast(toast[0]);
            bsToast.show();
        };

        // 全局AJAX错误处理
        $(document).ajaxError(function(event, xhr, settings, thrownError) {
            if (xhr.status === 401) {
                showToast('请先登录', 'warning');
                setTimeout(() => {
                    window.location.href = '/auth/login';
                }, 1500);
            } else if (xhr.status === 403) {
                showToast('权限不足', 'error');
            } else if (xhr.status >= 500) {
                showToast('服务器错误，请稍后重试', 'error');
            }
        });
    </script>
</body>
</html>
