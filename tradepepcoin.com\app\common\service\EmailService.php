<?php
declare (strict_types = 1);

namespace app\common\service;

use PHP<PERSON>ailer\PHPMailer\PHPMailer;
use P<PERSON><PERSON>ailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;
use think\facade\Cache;

/**
 * 邮件服务类
 */
class EmailService
{
    private $mailer;
    
    public function __construct()
    {
        $this->mailer = new PHPMailer(true);
        $this->configureMailer();
    }
    
    /**
     * 配置邮件服务器
     */
    private function configureMailer()
    {
        try {
            // 服务器设置
            $this->mailer->isSMTP();
            $this->mailer->Host = env('EMAIL_SMTP_HOST', 'smtp.gmail.com');
            $this->mailer->SMTPAuth = true;
            $this->mailer->Username = env('EMAIL_SMTP_USERNAME', '<EMAIL>');
            $this->mailer->Password = env('EMAIL_SMTP_PASSWORD', 'asd19910101....');
            $this->mailer->SMTPSecure = env('EMAIL_SMTP_SECURE', 'tls');
            $this->mailer->Port = env('EMAIL_SMTP_PORT', 587);
            $this->mailer->CharSet = 'UTF-8';
            
            // 发件人设置
            $this->mailer->setFrom(
                env('EMAIL_FROM_ADDRESS', '<EMAIL>'),
                env('EMAIL_FROM_NAME', 'GVD')
            );
            
        } catch (Exception $e) {
            throw new \Exception('邮件服务器配置失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 发送验证码邮件
     */
    public function sendVerificationCode(string $email, string $code): array
    {
        try {
            // 检查发送频率限制
            $limitKey = 'email_send_limit_' . $email;
            if (Cache::has($limitKey)) {
                return ['code' => 0, 'msg' => '发送过于频繁，请60秒后再试'];
            }
            
            // 设置收件人
            $this->mailer->addAddress($email);
            
            // 邮件内容
            $this->mailer->isHTML(true);
            $this->mailer->Subject = 'GVD验证码';
            $this->mailer->Body = $this->getVerificationTemplate($code);
            $this->mailer->AltBody = "您的GVD验证码是：{$code}，有效期5分钟。";
            
            // 发送邮件
            $this->mailer->send();
            
            // 设置发送限制
            Cache::set($limitKey, time(), 60);
            
            // 缓存验证码
            Cache::set('email_code_' . $email, $code, 300);
            
            return ['code' => 1, 'msg' => '验证码已发送到您的邮箱'];
            
        } catch (Exception $e) {
            return ['code' => 0, 'msg' => '发送失败: ' . $e->getMessage()];
        } finally {
            // 清除收件人
            $this->mailer->clearAddresses();
        }
    }
    
    /**
     * 发送注册成功邮件
     */
    public function sendWelcomeEmail(string $email, string $username): array
    {
        try {
            $this->mailer->addAddress($email);
            
            $this->mailer->isHTML(true);
            $this->mailer->Subject = '欢迎加入GVD';
            $this->mailer->Body = $this->getWelcomeTemplate($username);
            $this->mailer->AltBody = "欢迎加入GVD，{$username}！";
            
            $this->mailer->send();
            
            return ['code' => 1, 'msg' => '欢迎邮件发送成功'];
            
        } catch (Exception $e) {
            return ['code' => 0, 'msg' => '发送失败: ' . $e->getMessage()];
        } finally {
            $this->mailer->clearAddresses();
        }
    }
    
    /**
     * 发送密码重置邮件
     */
    public function sendPasswordResetEmail(string $email, string $resetLink): array
    {
        try {
            $this->mailer->addAddress($email);
            
            $this->mailer->isHTML(true);
            $this->mailer->Subject = 'GVD密码重置';
            $this->mailer->Body = $this->getPasswordResetTemplate($resetLink);
            $this->mailer->AltBody = "请点击以下链接重置密码：{$resetLink}";
            
            $this->mailer->send();
            
            return ['code' => 1, 'msg' => '密码重置邮件已发送'];
            
        } catch (Exception $e) {
            return ['code' => 0, 'msg' => '发送失败: ' . $e->getMessage()];
        } finally {
            $this->mailer->clearAddresses();
        }
    }
    
    /**
     * 验证码邮件模板
     */
    private function getVerificationTemplate(string $code): string
    {
        return "
        <div style='max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;'>
            <div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;'>
                <h1 style='margin: 0; font-size: 28px;'>GVD</h1>
                <p style='margin: 10px 0 0 0; opacity: 0.9;'>数字货币交易平台</p>
            </div>
            
            <div style='background: white; padding: 40px; border: 1px solid #e9ecef; border-top: none; border-radius: 0 0 10px 10px;'>
                <h2 style='color: #333; margin-bottom: 20px;'>验证码</h2>
                <p style='color: #666; line-height: 1.6; margin-bottom: 30px;'>
                    您正在进行身份验证，请使用以下验证码完成操作：
                </p>
                
                <div style='background: #f8f9fa; border: 2px dashed #667eea; padding: 20px; text-align: center; margin: 30px 0; border-radius: 10px;'>
                    <span style='font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 5px;'>{$code}</span>
                </div>
                
                <p style='color: #999; font-size: 14px; margin-bottom: 20px;'>
                    • 验证码有效期为5分钟<br>
                    • 请勿将验证码告诉他人<br>
                    • 如非本人操作，请忽略此邮件
                </p>
                
                <div style='border-top: 1px solid #e9ecef; padding-top: 20px; margin-top: 30px; text-align: center;'>
                    <p style='color: #999; font-size: 12px; margin: 0;'>
                        此邮件由系统自动发送，请勿回复<br>
                        © 2024 GVD. All rights reserved.
                    </p>
                </div>
            </div>
        </div>";
    }
    
    /**
     * 欢迎邮件模板
     */
    private function getWelcomeTemplate(string $username): string
    {
        return "
        <div style='max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;'>
            <div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;'>
                <h1 style='margin: 0; font-size: 28px;'>欢迎加入GVD</h1>
                <p style='margin: 10px 0 0 0; opacity: 0.9;'>专业的数字货币交易平台</p>
            </div>
            
            <div style='background: white; padding: 40px; border: 1px solid #e9ecef; border-top: none; border-radius: 0 0 10px 10px;'>
                <h2 style='color: #333; margin-bottom: 20px;'>Hi, {$username}!</h2>
                <p style='color: #666; line-height: 1.6; margin-bottom: 30px;'>
                    恭喜您成功注册GVD账户！您现在可以享受我们提供的专业交易服务。
                </p>
                
                <div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>
                    <h3 style='color: #333; margin-top: 0;'>接下来您可以：</h3>
                    <ul style='color: #666; line-height: 1.8;'>
                        <li>完成实名认证，提升账户安全等级</li>
                        <li>充值数字货币，开始交易</li>
                        <li>体验我们的合约交易功能</li>
                        <li>邀请朋友获得佣金奖励</li>
                    </ul>
                </div>
                
                <div style='text-align: center; margin: 30px 0;'>
                    <a href='https://tradepepcoin.com/auth/login' style='background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 25px; display: inline-block;'>
                        立即登录
                    </a>
                </div>
                
                <div style='border-top: 1px solid #e9ecef; padding-top: 20px; margin-top: 30px; text-align: center;'>
                    <p style='color: #999; font-size: 12px; margin: 0;'>
                        如有任何问题，请联系我们的客服团队<br>
                        © 2024 GVD. All rights reserved.
                    </p>
                </div>
            </div>
        </div>";
    }
    
    /**
     * 密码重置邮件模板
     */
    private function getPasswordResetTemplate(string $resetLink): string
    {
        return "
        <div style='max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;'>
            <div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;'>
                <h1 style='margin: 0; font-size: 28px;'>GVD</h1>
                <p style='margin: 10px 0 0 0; opacity: 0.9;'>密码重置</p>
            </div>
            
            <div style='background: white; padding: 40px; border: 1px solid #e9ecef; border-top: none; border-radius: 0 0 10px 10px;'>
                <h2 style='color: #333; margin-bottom: 20px;'>重置您的密码</h2>
                <p style='color: #666; line-height: 1.6; margin-bottom: 30px;'>
                    我们收到了您的密码重置请求。请点击下方按钮重置您的密码：
                </p>
                
                <div style='text-align: center; margin: 30px 0;'>
                    <a href='{$resetLink}' style='background: #667eea; color: white; padding: 15px 40px; text-decoration: none; border-radius: 25px; display: inline-block; font-weight: bold;'>
                        重置密码
                    </a>
                </div>
                
                <p style='color: #999; font-size: 14px; margin-bottom: 20px;'>
                    • 此链接有效期为24小时<br>
                    • 如非本人操作，请忽略此邮件<br>
                    • 为了账户安全，请勿将此链接分享给他人
                </p>
                
                <div style='border-top: 1px solid #e9ecef; padding-top: 20px; margin-top: 30px; text-align: center;'>
                    <p style='color: #999; font-size: 12px; margin: 0;'>
                        此邮件由系统自动发送，请勿回复<br>
                        © 2024 GVD. All rights reserved.
                    </p>
                </div>
            </div>
        </div>";
    }
}
