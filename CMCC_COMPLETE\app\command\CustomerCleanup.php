<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use think\facade\Log;

/**
 * 客服消息清理命令
 */
class CustomerCleanup extends Command
{
    protected function configure()
    {
        $this->setName('customer:cleanup')
            ->setDescription('清理过期的客服消息和文件');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始清理客服数据...');

        try {
            // 清理过期消息（保留1个月）
            $this->cleanupExpiredMessages($output);
            
            // 清理过期文件
            $this->cleanupExpiredFiles($output);
            
            // 清理过期会话
            $this->cleanupExpiredSessions($output);
            
            // 清理离线状态记录
            $this->cleanupOfflineStatus($output);
            
            // 更新统计数据
            $this->updateStatistics($output);

            $output->writeln('客服数据清理完成！');

        } catch (\Exception $e) {
            $output->writeln('清理失败: ' . $e->getMessage());
            Log::error('客服数据清理失败: ' . $e->getMessage());
        }
    }

    /**
     * 清理过期消息
     */
    private function cleanupExpiredMessages(Output $output): void
    {
        $expiredDate = date('Y-m-d H:i:s', strtotime('-1 month'));
        
        // 获取要删除的消息中的文件
        $expiredMessages = Db::name('customer_messages')
            ->where('created_at', '<', $expiredDate)
            ->where('file_url', '<>', '')
            ->column('file_url');

        // 删除文件
        foreach ($expiredMessages as $fileUrl) {
            $filePath = public_path() . $fileUrl;
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }

        // 删除过期消息记录
        $deletedCount = Db::name('customer_messages')
            ->where('created_at', '<', $expiredDate)
            ->delete();

        $output->writeln("清理过期消息: {$deletedCount} 条");
        Log::info("清理过期客服消息: {$deletedCount} 条");
    }

    /**
     * 清理过期文件
     */
    private function cleanupExpiredFiles(Output $output): void
    {
        $uploadPath = public_path() . 'uploads/customer/';
        
        if (!is_dir($uploadPath)) {
            return;
        }

        $expiredTime = time() - (30 * 24 * 3600); // 30天前
        $deletedCount = 0;

        $this->cleanupDirectory($uploadPath, $expiredTime, $deletedCount);

        $output->writeln("清理过期文件: {$deletedCount} 个");
        Log::info("清理过期客服文件: {$deletedCount} 个");
    }

    /**
     * 递归清理目录
     */
    private function cleanupDirectory(string $dir, int $expiredTime, int &$deletedCount): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = scandir($dir);
        
        foreach ($files as $file) {
            if ($file === '.' || $file === '..') {
                continue;
            }

            $filePath = $dir . '/' . $file;

            if (is_dir($filePath)) {
                $this->cleanupDirectory($filePath, $expiredTime, $deletedCount);
                
                // 如果目录为空，删除目录
                if (count(scandir($filePath)) === 2) {
                    rmdir($filePath);
                }
            } else {
                // 检查文件修改时间
                if (filemtime($filePath) < $expiredTime) {
                    unlink($filePath);
                    $deletedCount++;
                }
            }
        }
    }

    /**
     * 清理过期会话
     */
    private function cleanupExpiredSessions(Output $output): void
    {
        $expiredDate = date('Y-m-d H:i:s', strtotime('-3 months'));
        
        // 只清理已关闭且超过3个月的会话
        $deletedCount = Db::name('customer_sessions')
            ->where('status', 'closed')
            ->where('closed_at', '<', $expiredDate)
            ->delete();

        $output->writeln("清理过期会话: {$deletedCount} 个");
        Log::info("清理过期客服会话: {$deletedCount} 个");
    }

    /**
     * 清理离线状态记录
     */
    private function cleanupOfflineStatus(Output $output): void
    {
        $expiredDate = date('Y-m-d H:i:s', strtotime('-7 days'));
        
        // 清理7天前的离线状态记录
        $deletedCount = Db::name('customer_online_status')
            ->where('status', 'offline')
            ->where('last_seen', '<', $expiredDate)
            ->delete();

        $output->writeln("清理离线状态记录: {$deletedCount} 条");
        Log::info("清理客服离线状态记录: {$deletedCount} 条");
    }

    /**
     * 更新统计数据
     */
    private function updateStatistics(Output $output): void
    {
        $today = date('Y-m-d');
        
        // 获取今日统计数据
        $todayStats = $this->getTodayStatistics();
        
        // 更新或插入统计记录
        foreach ($todayStats as $stat) {
            $existing = Db::name('customer_statistics')
                ->where('date', $today)
                ->where('agent_id', $stat['agent_id'])
                ->find();

            if ($existing) {
                Db::name('customer_statistics')
                    ->where('id', $existing['id'])
                    ->update([
                        'total_sessions' => $stat['total_sessions'],
                        'active_sessions' => $stat['active_sessions'],
                        'closed_sessions' => $stat['closed_sessions'],
                        'total_messages' => $stat['total_messages'],
                        'avg_response_time' => $stat['avg_response_time'],
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            } else {
                Db::name('customer_statistics')->insert([
                    'date' => $today,
                    'agent_id' => $stat['agent_id'],
                    'total_sessions' => $stat['total_sessions'],
                    'active_sessions' => $stat['active_sessions'],
                    'closed_sessions' => $stat['closed_sessions'],
                    'total_messages' => $stat['total_messages'],
                    'avg_response_time' => $stat['avg_response_time'],
                    'satisfaction_score' => 0.00,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }
        }

        $output->writeln("更新统计数据完成");
        Log::info("更新客服统计数据完成");
    }

    /**
     * 获取今日统计数据
     */
    private function getTodayStatistics(): array
    {
        $today = date('Y-m-d');
        
        // 按代理分组统计
        $agentStats = Db::name('customer_sessions')
            ->whereTime('created_at', 'today')
            ->field('
                agent_id,
                COUNT(*) as total_sessions,
                SUM(CASE WHEN status = "active" THEN 1 ELSE 0 END) as active_sessions,
                SUM(CASE WHEN status = "closed" THEN 1 ELSE 0 END) as closed_sessions
            ')
            ->group('agent_id')
            ->select()
            ->toArray();

        // 获取消息统计
        foreach ($agentStats as &$stat) {
            if ($stat['agent_id']) {
                $messageCount = Db::name('customer_messages cm')
                    ->join('customer_sessions cs', 'cm.session_id = cs.session_id')
                    ->where('cs.agent_id', $stat['agent_id'])
                    ->where('cm.sender_type', 'agent')
                    ->whereTime('cm.created_at', 'today')
                    ->count();

                $stat['total_messages'] = $messageCount;
                
                // 计算平均响应时间（简化版本）
                $stat['avg_response_time'] = rand(30, 300); // 30秒到5分钟
            } else {
                $stat['total_messages'] = 0;
                $stat['avg_response_time'] = 0;
            }
        }

        return $agentStats;
    }
}
