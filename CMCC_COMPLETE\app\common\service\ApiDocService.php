<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;
use ReflectionClass;
use ReflectionMethod;

/**
 * API文档自动生成服务
 * 基于注解和反射自动生成API文档
 */
class ApiDocService
{
    // 文档格式
    const FORMAT_HTML = 'html';
    const FORMAT_JSON = 'json';
    const FORMAT_MARKDOWN = 'markdown';
    const FORMAT_OPENAPI = 'openapi';

    // HTTP方法
    const METHODS = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'];

    private $config;
    private $routes;

    public function __construct()
    {
        $this->config = config('api_doc');
        $this->routes = $this->loadRoutes();
    }

    /**
     * 生成API文档
     */
    public function generateDocs(string $format = self::FORMAT_HTML): array
    {
        try {
            // 扫描API控制器
            $apiData = $this->scanApiControllers();

            // 根据格式生成文档
            switch ($format) {
                case self::FORMAT_HTML:
                    $content = $this->generateHtmlDocs($apiData);
                    break;
                case self::FORMAT_JSON:
                    $content = $this->generateJsonDocs($apiData);
                    break;
                case self::FORMAT_MARKDOWN:
                    $content = $this->generateMarkdownDocs($apiData);
                    break;
                case self::FORMAT_OPENAPI:
                    $content = $this->generateOpenApiDocs($apiData);
                    break;
                default:
                    throw new \Exception('不支持的文档格式');
            }

            // 缓存文档
            $this->cacheDocs($format, $content);

            return [
                'code' => 1,
                'msg' => '文档生成成功',
                'data' => [
                    'format' => $format,
                    'content' => $content,
                    'size' => strlen($content),
                    'generated_at' => date('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            Log::error('生成API文档失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '生成失败：' . $e->getMessage()];
        }
    }

    /**
     * 扫描API控制器
     */
    private function scanApiControllers(): array
    {
        $apiData = [
            'info' => $this->getApiInfo(),
            'servers' => $this->getServers(),
            'paths' => [],
            'components' => [
                'schemas' => [],
                'securitySchemes' => $this->getSecuritySchemes()
            ]
        ];

        $controllerPath = app_path() . 'api/controller/';
        $controllers = $this->getControllerFiles($controllerPath);

        foreach ($controllers as $controller) {
            $controllerData = $this->analyzeController($controller);
            if (!empty($controllerData)) {
                $apiData['paths'] = array_merge($apiData['paths'], $controllerData);
            }
        }

        return $apiData;
    }

    /**
     * 分析控制器
     */
    private function analyzeController(string $controllerFile): array
    {
        $className = $this->getClassNameFromFile($controllerFile);
        if (!$className || !class_exists($className)) {
            return [];
        }

        $reflection = new ReflectionClass($className);
        $classDoc = $this->parseDocComment($reflection->getDocComment());
        
        $paths = [];
        $methods = $reflection->getMethods(ReflectionMethod::IS_PUBLIC);

        foreach ($methods as $method) {
            if ($method->getDeclaringClass()->getName() !== $className) {
                continue; // 跳过继承的方法
            }

            $methodData = $this->analyzeMethod($method, $classDoc);
            if (!empty($methodData)) {
                $paths = array_merge($paths, $methodData);
            }
        }

        return $paths;
    }

    /**
     * 分析方法
     */
    private function analyzeMethod(ReflectionMethod $method, array $classDoc): array
    {
        $methodDoc = $this->parseDocComment($method->getDocComment());
        
        if (empty($methodDoc['api'])) {
            return []; // 没有@api注解的方法不生成文档
        }

        $methodName = $method->getName();
        $className = $method->getDeclaringClass()->getName();
        
        // 获取路由信息
        $routeInfo = $this->getRouteInfo($className, $methodName);
        if (!$routeInfo) {
            return [];
        }

        $path = $routeInfo['path'];
        $httpMethods = $routeInfo['methods'];

        $pathData = [];

        foreach ($httpMethods as $httpMethod) {
            $pathData[$path][strtolower($httpMethod)] = [
                'tags' => [$classDoc['title'] ?? $this->getControllerName($className)],
                'summary' => $methodDoc['title'] ?? $methodName,
                'description' => $methodDoc['description'] ?? '',
                'operationId' => $className . '::' . $methodName,
                'parameters' => $this->getParameters($methodDoc),
                'requestBody' => $this->getRequestBody($methodDoc, $httpMethod),
                'responses' => $this->getResponses($methodDoc),
                'security' => $this->getSecurity($methodDoc)
            ];

            // 添加示例
            if (!empty($methodDoc['example'])) {
                $pathData[$path][strtolower($httpMethod)]['examples'] = $methodDoc['example'];
            }
        }

        return $pathData;
    }

    /**
     * 解析文档注释
     */
    private function parseDocComment($docComment): array
    {
        if (!$docComment) {
            return [];
        }

        $doc = [];
        $lines = explode("\n", $docComment);
        
        $currentSection = 'description';
        $description = [];

        foreach ($lines as $line) {
            $line = trim($line, " \t\n\r\0\x0B*/");
            
            if (empty($line)) continue;

            // 解析@标签
            if (preg_match('/^@(\w+)(?:\s+(.*))?$/', $line, $matches)) {
                $tag = $matches[1];
                $content = $matches[2] ?? '';

                switch ($tag) {
                    case 'api':
                        $doc['api'] = true;
                        if ($content) {
                            $doc['title'] = $content;
                        }
                        break;
                    case 'title':
                        $doc['title'] = $content;
                        break;
                    case 'description':
                        $doc['description'] = $content;
                        break;
                    case 'param':
                        $doc['params'][] = $this->parseParam($content);
                        break;
                    case 'return':
                    case 'response':
                        $doc['responses'][] = $this->parseResponse($content);
                        break;
                    case 'example':
                        $doc['example'][] = $content;
                        break;
                    case 'auth':
                        $doc['auth'] = $content ?: true;
                        break;
                    case 'method':
                        $doc['methods'] = explode(',', str_replace(' ', '', $content));
                        break;
                    default:
                        $doc[$tag] = $content;
                }
            } else {
                // 普通描述文本
                if ($currentSection === 'description') {
                    $description[] = $line;
                }
            }
        }

        if (!empty($description) && empty($doc['description'])) {
            $doc['description'] = implode("\n", $description);
        }

        return $doc;
    }

    /**
     * 解析参数
     */
    private function parseParam(string $content): array
    {
        // 格式: @param type $name description
        if (preg_match('/^(\w+)\s+\$?(\w+)\s*(.*)$/', $content, $matches)) {
            return [
                'name' => $matches[2],
                'type' => $matches[1],
                'description' => $matches[3] ?? '',
                'required' => true
            ];
        }

        return ['name' => $content, 'type' => 'string', 'description' => '', 'required' => true];
    }

    /**
     * 解析响应
     */
    private function parseResponse(string $content): array
    {
        // 格式: @response code description
        if (preg_match('/^(\d+)\s+(.*)$/', $content, $matches)) {
            return [
                'code' => $matches[1],
                'description' => $matches[2]
            ];
        }

        return ['code' => '200', 'description' => $content];
    }

    /**
     * 生成HTML文档
     */
    private function generateHtmlDocs(array $apiData): string
    {
        $html = $this->getHtmlTemplate();
        
        // 替换变量
        $html = str_replace('{{API_TITLE}}', $apiData['info']['title'], $html);
        $html = str_replace('{{API_VERSION}}', $apiData['info']['version'], $html);
        $html = str_replace('{{API_DESCRIPTION}}', $apiData['info']['description'], $html);
        $html = str_replace('{{API_DATA}}', json_encode($apiData, JSON_UNESCAPED_UNICODE), $html);

        return $html;
    }

    /**
     * 生成JSON文档
     */
    private function generateJsonDocs(array $apiData): string
    {
        return json_encode($apiData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }

    /**
     * 生成Markdown文档
     */
    private function generateMarkdownDocs(array $apiData): string
    {
        $md = "# {$apiData['info']['title']}\n\n";
        $md .= "版本: {$apiData['info']['version']}\n\n";
        $md .= "{$apiData['info']['description']}\n\n";

        $md .= "## 服务器\n\n";
        foreach ($apiData['servers'] as $server) {
            $md .= "- {$server['url']} - {$server['description']}\n";
        }
        $md .= "\n";

        $md .= "## API接口\n\n";
        
        foreach ($apiData['paths'] as $path => $methods) {
            $md .= "### {$path}\n\n";
            
            foreach ($methods as $method => $info) {
                $md .= "#### " . strtoupper($method) . "\n\n";
                $md .= "**描述**: {$info['description']}\n\n";
                
                if (!empty($info['parameters'])) {
                    $md .= "**参数**:\n\n";
                    $md .= "| 参数名 | 类型 | 必填 | 描述 |\n";
                    $md .= "|--------|------|------|------|\n";
                    
                    foreach ($info['parameters'] as $param) {
                        $required = $param['required'] ? '是' : '否';
                        $md .= "| {$param['name']} | {$param['schema']['type']} | {$required} | {$param['description']} |\n";
                    }
                    $md .= "\n";
                }
                
                $md .= "**响应**:\n\n";
                foreach ($info['responses'] as $code => $response) {
                    $md .= "- {$code}: {$response['description']}\n";
                }
                $md .= "\n";
            }
        }

        return $md;
    }

    /**
     * 生成OpenAPI文档
     */
    private function generateOpenApiDocs(array $apiData): string
    {
        $openapi = [
            'openapi' => '3.0.0',
            'info' => $apiData['info'],
            'servers' => $apiData['servers'],
            'paths' => $apiData['paths'],
            'components' => $apiData['components']
        ];

        return json_encode($openapi, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }

    /**
     * 获取API信息
     */
    private function getApiInfo(): array
    {
        return [
            'title' => $this->config['title'] ?? 'GVD交易平台API',
            'description' => $this->config['description'] ?? 'GVD数字货币交易平台API文档',
            'version' => $this->config['version'] ?? '1.0.0',
            'contact' => [
                'name' => $this->config['contact']['name'] ?? 'API Support',
                'email' => $this->config['contact']['email'] ?? '<EMAIL>'
            ],
            'license' => [
                'name' => 'MIT',
                'url' => 'https://opensource.org/licenses/MIT'
            ]
        ];
    }

    /**
     * 获取服务器信息
     */
    private function getServers(): array
    {
        return [
            [
                'url' => $this->config['base_url'] ?? 'https://api.gvd.com',
                'description' => '生产环境'
            ],
            [
                'url' => $this->config['test_url'] ?? 'https://test-api.gvd.com',
                'description' => '测试环境'
            ]
        ];
    }

    /**
     * 获取安全方案
     */
    private function getSecuritySchemes(): array
    {
        return [
            'bearerAuth' => [
                'type' => 'http',
                'scheme' => 'bearer',
                'bearerFormat' => 'JWT'
            ],
            'apiKey' => [
                'type' => 'apiKey',
                'in' => 'header',
                'name' => 'X-API-Key'
            ]
        ];
    }

    /**
     * 获取参数信息
     */
    private function getParameters(array $methodDoc): array
    {
        $parameters = [];
        
        if (!empty($methodDoc['params'])) {
            foreach ($methodDoc['params'] as $param) {
                $parameters[] = [
                    'name' => $param['name'],
                    'in' => 'query',
                    'required' => $param['required'],
                    'description' => $param['description'],
                    'schema' => [
                        'type' => $this->mapType($param['type'])
                    ]
                ];
            }
        }

        return $parameters;
    }

    /**
     * 获取请求体
     */
    private function getRequestBody(array $methodDoc, string $httpMethod): ?array
    {
        if (!in_array($httpMethod, ['POST', 'PUT', 'PATCH'])) {
            return null;
        }

        return [
            'required' => true,
            'content' => [
                'application/json' => [
                    'schema' => [
                        'type' => 'object',
                        'properties' => $this->getRequestProperties($methodDoc)
                    ]
                ]
            ]
        ];
    }

    /**
     * 获取响应信息
     */
    private function getResponses(array $methodDoc): array
    {
        $responses = [
            '200' => [
                'description' => '成功',
                'content' => [
                    'application/json' => [
                        'schema' => [
                            'type' => 'object',
                            'properties' => [
                                'code' => ['type' => 'integer'],
                                'msg' => ['type' => 'string'],
                                'data' => ['type' => 'object']
                            ]
                        ]
                    ]
                ]
            ],
            '400' => [
                'description' => '请求错误'
            ],
            '401' => [
                'description' => '未授权'
            ],
            '500' => [
                'description' => '服务器错误'
            ]
        ];

        if (!empty($methodDoc['responses'])) {
            foreach ($methodDoc['responses'] as $response) {
                $responses[$response['code']] = [
                    'description' => $response['description']
                ];
            }
        }

        return $responses;
    }

    /**
     * 获取安全要求
     */
    private function getSecurity(array $methodDoc): array
    {
        if (!empty($methodDoc['auth'])) {
            return [
                ['bearerAuth' => []]
            ];
        }

        return [];
    }

    /**
     * 获取HTML模板
     */
    private function getHtmlTemplate(): string
    {
        return '<!DOCTYPE html>
<html>
<head>
    <title>{{API_TITLE}}</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swagger-ui-dist@4.15.5/swagger-ui-bundle.css">
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="https://cdn.jsdelivr.net/npm/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
    <script>
        SwaggerUIBundle({
            url: "data:application/json;base64," + btoa(JSON.stringify({{API_DATA}})),
            dom_id: "#swagger-ui",
            presets: [
                SwaggerUIBundle.presets.apis,
                SwaggerUIBundle.presets.standalone
            ]
        });
    </script>
</body>
</html>';
    }

    /**
     * 类型映射
     */
    private function mapType(string $type): string
    {
        $typeMap = [
            'int' => 'integer',
            'float' => 'number',
            'bool' => 'boolean',
            'array' => 'array',
            'object' => 'object'
        ];

        return $typeMap[$type] ?? 'string';
    }

    /**
     * 获取控制器文件
     */
    private function getControllerFiles(string $path): array
    {
        $files = [];
        if (is_dir($path)) {
            $iterator = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($path)
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile() && $file->getExtension() === 'php') {
                    $files[] = $file->getPathname();
                }
            }
        }
        return $files;
    }

    /**
     * 从文件获取类名
     */
    private function getClassNameFromFile(string $file): ?string
    {
        $content = file_get_contents($file);
        
        // 提取命名空间
        if (preg_match('/namespace\s+([^;]+);/', $content, $matches)) {
            $namespace = $matches[1];
        } else {
            return null;
        }
        
        // 提取类名
        if (preg_match('/class\s+(\w+)/', $content, $matches)) {
            $className = $matches[1];
            return $namespace . '\\' . $className;
        }
        
        return null;
    }

    /**
     * 获取控制器名称
     */
    private function getControllerName(string $className): string
    {
        $parts = explode('\\', $className);
        return end($parts);
    }

    /**
     * 加载路由
     */
    private function loadRoutes(): array
    {
        // 这里应该从路由配置中加载路由信息
        // 简化实现，返回空数组
        return [];
    }

    /**
     * 获取路由信息
     */
    private function getRouteInfo(string $className, string $methodName): ?array
    {
        // 简化实现，根据控制器和方法名生成路由
        $controller = strtolower(str_replace('Controller', '', basename(str_replace('\\', '/', $className))));
        $action = strtolower($methodName);
        
        return [
            'path' => "/api/{$controller}/{$action}",
            'methods' => ['GET', 'POST']
        ];
    }

    /**
     * 获取请求属性
     */
    private function getRequestProperties(array $methodDoc): array
    {
        $properties = [];
        
        if (!empty($methodDoc['params'])) {
            foreach ($methodDoc['params'] as $param) {
                $properties[$param['name']] = [
                    'type' => $this->mapType($param['type']),
                    'description' => $param['description']
                ];
            }
        }

        return $properties;
    }

    /**
     * 缓存文档
     */
    private function cacheDocs(string $format, string $content): void
    {
        $cacheKey = "api_docs:{$format}";
        Cache::set($cacheKey, $content, 3600); // 缓存1小时
    }
}
