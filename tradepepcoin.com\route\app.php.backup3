<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

// 首页路由
Route::get('/', 'index/Index/index');

// 用户认证路由
Route::group('auth', function () {
    Route::get('login', 'index/Auth/login');
    Route::post('login', 'index/Auth/login');
    Route::get('register', 'index/Auth/register');
    Route::post('register', 'index/Auth/register');
    Route::get('logout', 'index/Auth/logout');
    Route::get('forgot-password', 'index/Auth/forgotPassword');
    Route::post('forgot-password', 'index/Auth/forgotPassword');
    Route::post('sendEmailCode', 'index/Auth/sendEmailCode');
    Route::get('captcha', 'index/Auth/captcha');
});

// 用户中心路由
Route::group('user', function () {
    Route::get('profile', 'index/User/profile');
    Route::get('assets', 'index/User/assets');
    Route::get('orders', 'index/User/orders');
    Route::get('deposits', 'index/User/deposits');
    Route::get('withdrawals', 'index/User/withdrawals');
    Route::get('commissions', 'index/User/commissions');
    Route::get('invite', 'index/User/invite');
})->middleware('auth');

// 交易路由
Route::group('trade', function () {
    Route::get('/', 'index/Trade/index');
    Route::get('spot', 'index/Trade/spot');
    Route::post('order', 'index/Trade/order');
    Route::post('cancel', 'index/Trade/cancel');
    Route::get('history', 'index/Trade/history');
})->middleware('auth');

// 合约交易路由
Route::group('contract', function () {
    Route::get('/', 'index/Contract/index');
    Route::post('order', 'index/Contract/order');
    Route::get('history', 'index/Contract/history');
    Route::get('config', 'index/Contract/config');
})->middleware('auth');

// 新币认购路由
Route::group('ido', function () {
    Route::get('/', 'index/Ido/index');
    Route::get('detail/<id>', 'index/Ido/detail');
    Route::post('purchase', 'index/Ido/purchase');
    Route::get('orders', 'index/Ido/orders');
})->middleware('auth');

// 资产管理路由
Route::group('wallet', function () {
    Route::get('/', 'index/Wallet/index');
    Route::get('deposit', 'index/Wallet/deposit');
    Route::post('deposit', 'index/Wallet/deposit');
    Route::get('withdraw', 'index/Wallet/withdraw');
    Route::post('withdraw', 'index/Wallet/withdraw');
    Route::get('records', 'index/Wallet/records');
});

// 客服路由
Route::group('service', function () {
    Route::get('/', 'index/Service/index');
    Route::post('send', 'index/Service/send');
    Route::get('messages', 'index/Service/messages');
});

// API路由
Route::group('api', function () {
    Route::get('market-data', 'index/Index/marketData');
    Route::get('kline-data', 'index/Index/klineData');
    Route::get('ticker/<symbol>', 'index/Api/ticker');
    Route::get('depth/<symbol>', 'index/Api/depth');
    Route::get('trades/<symbol>', 'index/Api/trades');
});

// 公告路由
Route::group('notice', function () {
    Route::get('/', 'index/Index/notice');
    Route::get('detail/<id>', 'index/Index/noticeDetail');
});

// 帮助路由
Route::get('help', 'index/Index/help');
Route::get('about', 'index/Index/about');

// 管理后台路由
Route::group('admin', function () {
    // 登录路由
    Route::get('login', 'admin/Auth/login');
    Route::post('login', 'admin/Auth/login');
    Route::get('logout', 'admin/Auth/logout');
    
    // 需要登录的路由
    Route::group('', function () {
        // 首页
        Route::get('/', 'admin/Index/index');
        Route::get('index', 'admin/Index/index');
        Route::get('system-info', 'admin/Index/systemInfo');
        Route::post('clear-cache', 'admin/Index/clearCache');
        
        // 用户管理
        Route::group('user', function () {
            Route::get('/', 'admin/User/index');
            Route::get('list', 'admin/User/index');
            Route::get('add', 'admin/User/add');
            Route::post('add', 'admin/User/add');
            Route::get('edit/<id>', 'admin/User/edit');
            Route::post('edit/<id>', 'admin/User/edit');
            Route::post('delete', 'admin/User/delete');
            Route::post('status', 'admin/User/status');
            Route::get('assets/<id>', 'admin/User/assets');
            Route::post('adjust-asset', 'admin/User/adjustAsset');
        });
        
        // 币种管理
        Route::group('coin', function () {
            Route::get('/', 'admin/Coin/index');
            Route::get('add', 'admin/Coin/add');
            Route::post('add', 'admin/Coin/add');
            Route::get('edit/<id>', 'admin/Coin/edit');
            Route::post('edit/<id>', 'admin/Coin/edit');
            Route::post('delete', 'admin/Coin/delete');
            Route::post('status', 'admin/Coin/status');
        });
        
        // 交易对管理
        Route::group('trading-pair', function () {
            Route::get('/', 'admin/TradingPair/index');
            Route::get('add', 'admin/TradingPair/add');
            Route::post('add', 'admin/TradingPair/add');
            Route::get('edit/<id>', 'admin/TradingPair/edit');
            Route::post('edit/<id>', 'admin/TradingPair/edit');
            Route::post('delete', 'admin/TradingPair/delete');
            Route::post('status', 'admin/TradingPair/status');
        });
        
        // 订单管理
        Route::group('order', function () {
            Route::get('/', 'admin/Order/index');
            Route::get('detail/<id>', 'admin/Order/detail');
            Route::post('cancel', 'admin/Order/cancel');
        });
        
        // 合约管理
        Route::group('contract', function () {
            Route::get('/', 'admin/Contract/index');
            Route::get('config', 'admin/Contract/config');
            Route::post('config', 'admin/Contract/config');
            Route::get('orders', 'admin/Contract/orders');
        });
        
        // 充值提现管理
        Route::group('finance', function () {
            Route::get('deposits', 'admin/Finance/deposits');
            Route::get('withdrawals', 'admin/Finance/withdrawals');
            Route::post('confirm-deposit', 'admin/Finance/confirmDeposit');
            Route::post('confirm-withdrawal', 'admin/Finance/confirmWithdrawal');
            Route::post('reject-withdrawal', 'admin/Finance/rejectWithdrawal');
        });
        
        // 认购管理
        Route::group('ido', function () {
            Route::get('/', 'admin/Ido/index');
            Route::get('add', 'admin/Ido/add');
            Route::post('add', 'admin/Ido/add');
            Route::get('edit/<id>', 'admin/Ido/edit');
            Route::post('edit/<id>', 'admin/Ido/edit');
            Route::post('delete', 'admin/Ido/delete');
            Route::get('orders/<id>', 'admin/Ido/orders');
        });
        
        // 代理管理
        Route::group('agent', function () {
            Route::get('/', 'admin/Agent/index');
            Route::post('set-agent', 'admin/Agent/setAgent');
            Route::get('commissions', 'admin/Agent/commissions');
        });
        
        // 客服管理
        Route::group('service', function () {
            Route::get('/', 'admin/Service/index');
            Route::get('chat/<id>', 'admin/Service/chat');
            Route::post('reply', 'admin/Service/reply');
        });
        
        // 系统配置
        Route::group('config', function () {
            Route::get('/', 'admin/Config/index');
            Route::post('save', 'admin/Config/save');
            Route::post('upload', 'admin/Config/upload');
        });
        
        // 统计报表
        Route::group('report', function () {
            Route::get('/', 'admin/Report/index');
            Route::get('user', 'admin/Report/user');
            Route::get('trade', 'admin/Report/trade');
            Route::get('finance', 'admin/Report/finance');
        });
        
    })->middleware('admin_auth');
});

// 代理后台路由
Route::group('agent', function () {
    // 登录路由
    Route::get('login', 'agent/Auth/login');
    Route::post('login', 'agent/Auth/login');
    Route::get('logout', 'agent/Auth/logout');
    
    // 需要登录的路由
    Route::group('', function () {
        Route::get('/', 'agent/Index/index');
        Route::get('users', 'agent/Index/users');
        Route::get('orders', 'agent/Index/orders');
        Route::get('commissions', 'agent/Index/commissions');
        Route::get('service', 'agent/Index/service');
        Route::post('reply', 'agent/Index/reply');
    })->middleware('agent_auth');
});

// 错误页面
Route::get('404', function () {
    return view('error/404');
});

Route::get('500', function () {
    return view('error/500');
});

// 缺失路由
Route::miss(function() {
    return redirect('/404');
});

// 修复缺失路由处理
Route::miss(function() {
    return response()->view('error/404', [], 404);
});

// 用户端客服路由
Route::group('user', function () {
    Route::post('customer-service/send', 'user/CustomerService/send');
    Route::get('customer-service/messages', 'user/CustomerService/messages');
    Route::get('customer-service/unread-count', 'user/CustomerService/unreadCount');
    Route::post('customer-service/mark-read', 'user/CustomerService/markRead');
    Route::post('customer-service/upload', 'user/CustomerService/upload');
    Route::get('info', 'user/CustomerService/info');
    Route::get('customer-service/service-status', 'user/CustomerService/serviceStatus');
    Route::get('customer-service/quick-replies', 'user/CustomerService/quickReplies');
});

// 代理端扩展路由
Route::group('agent/dashboard', function () {
    Route::get('index', 'agent/Dashboard/index');
    Route::get('customerService', 'agent/Dashboard/customerService');
    Route::get('realtime-stats', 'agent/Dashboard/realtimeStats');
    Route::get('check-new-orders', 'agent/Dashboard/checkNewOrders');
    Route::get('contract-orders', 'agent/Dashboard/contractOrders');
    Route::post('set-contract-result', 'agent/Dashboard/setContractResult');
    Route::post('batch-set-contract-result', 'agent/Dashboard/batchSetContractResult');
    Route::get('withdrawal-requests', 'agent/Dashboard/withdrawalRequests');
    Route::post('handle-withdrawal', 'agent/Dashboard/handleWithdrawal');
    Route::get('finance-records', 'agent/Dashboard/financeRecords');
    Route::get('export-finance', 'agent/Dashboard/exportFinance');
    Route::get('contract-control-stats', 'agent/Dashboard/contractControlStats');
    Route::get('recent-deposits', 'agent/Dashboard/recentDeposits');
    Route::get('get-conversations', 'agent/Dashboard/getConversations');
    Route::get('get-conversation-messages', 'agent/Dashboard/getConversationMessages');
    Route::post('send-service-message', 'agent/Dashboard/sendServiceMessage');
    Route::post('mark-conversation-read', 'agent/Dashboard/markConversationRead');
    Route::get('get-service-stats', 'agent/Dashboard/getServiceStats');
    Route::post('delete-conversation', 'agent/Dashboard/deleteConversation');
})->middleware('agent_auth');

// 管理端客服路由
Route::group('admin/customer-service', function () {
    Route::get('index', 'admin/CustomerService/index');
    Route::get('get-conversation-messages', 'admin/CustomerService/getConversationMessages');
    Route::post('send-service-message', 'admin/CustomerService/sendServiceMessage');
    Route::post('mark-conversation-read', 'admin/CustomerService/markConversationRead');
    Route::get('get-service-stats', 'admin/CustomerService/getServiceStats');
    Route::get('get-conversations', 'admin/CustomerService/getConversations');
    Route::post('delete-conversation', 'admin/CustomerService/deleteConversation');
    Route::get('settings', 'admin/CustomerService/settings');
    Route::post('settings', 'admin/CustomerService/settings');
    Route::get('quick-replies', 'admin/CustomerService/quickReplies');
    Route::post('quick-replies', 'admin/CustomerService/quickReplies');
    Route::get('statistics', 'admin/CustomerService/statistics');
})->middleware('admin_auth');

