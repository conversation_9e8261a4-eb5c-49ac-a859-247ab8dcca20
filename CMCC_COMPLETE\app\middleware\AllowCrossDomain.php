<?php
declare (strict_types = 1);

namespace app\middleware;

use Closure;
use think\Config;
use think\Request;
use think\Response;

/**
 * 跨域请求支持
 */
class AllowCrossDomain
{
    protected $header = [
        'Access-Control-Allow-Credentials' => 'true',
        'Access-Control-Max-Age'           => 1800,
        'Access-Control-Allow-Methods'     => 'GET, POST, PATCH, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers'     => 'Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match, If-Unmodified-Since, X-CSRF-TOKEN, X-Requested-With',
    ];

    /**
     * 处理请求
     *
     * @param Request $request
     * @param Closure $next
     * @param array   $header
     * @return Response
     */
    public function handle(Request $request, Closure $next, ?array $header = []): Response
    {
        $header = !empty($header) ? array_merge($this->header, $header) : $this->header;

        if ($request->method(true) == 'OPTIONS') {
            $response = Response::create('ok')->code(200);
        } else {
            $response = $next($request);
        }

        $origin = $request->header('origin');
        
        if ($origin) {
            $header['Access-Control-Allow-Origin'] = $origin;
        } else {
            $header['Access-Control-Allow-Origin'] = '*';
        }

        return $response->header($header);
    }
}
