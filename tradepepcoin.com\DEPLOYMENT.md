# 数字货币交易平台部署指南

## 📋 部署前准备

### 1. 服务器要求
- **操作系统**: Ubuntu 22.04 64位
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **硬盘**: 20GB以上可用空间
- **带宽**: 10Mbps以上

### 2. 软件环境
- **Nginx**: 1.20.2+
- **MySQL**: 5.7.44+
- **PHP**: 7.4.33+
- **phpMyAdmin**: 5.2+
- **宝塔面板**: 最新版本

## 🚀 快速部署步骤

### 第一步：安装宝塔面板

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装宝塔面板
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh
sudo bash install.sh
```

安装完成后，记录下面板地址、用户名和密码。

### 第二步：安装运行环境

1. 登录宝塔面板
2. 进入"软件商店"
3. 安装以下软件：
   - **Nginx 1.20.2**
   - **MySQL 5.7.44**
   - **PHP 7.4.33**
   - **phpMyAdmin 5.2**

### 第三步：配置PHP环境

1. 点击PHP 7.4 的"设置"
2. 安装必需扩展：
   ```
   - pdo_mysql
   - mbstring
   - openssl
   - curl
   - json
   - fileinfo
   - gd
   - redis (可选)
   ```
3. 修改PHP配置：
   ```ini
   upload_max_filesize = 100M
   post_max_size = 100M
   max_execution_time = 300
   memory_limit = 256M
   ```

### 第四步：创建站点

1. 进入"网站"页面
2. 点击"添加站点"
3. 填写域名信息
4. 选择PHP版本：7.4
5. 创建数据库（记录数据库信息）

### 第五步：上传项目文件

1. 将 `pcwodr` 文件夹中的所有文件上传到站点根目录
2. 或者使用命令行：
   ```bash
   # 压缩项目文件
   cd /path/to/pcwodr
   tar -czf crypto-exchange.tar.gz .
   
   # 上传到服务器并解压
   scp crypto-exchange.tar.gz root@your-server:/www/wwwroot/your-domain/
   cd /www/wwwroot/your-domain/
   tar -xzf crypto-exchange.tar.gz
   rm crypto-exchange.tar.gz
   ```

### 第六步：配置站点

1. 在宝塔面板中点击站点名称
2. 进入"网站目录"设置
3. 将运行目录设置为：`/public`
4. 开启"防跨站攻击"

### 第七步：安装Composer

```bash
# 下载Composer
cd /tmp
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
sudo chmod +x /usr/local/bin/composer

# 验证安装
composer --version
```

### 第八步：安装项目依赖

```bash
cd /www/wwwroot/your-domain
composer install --no-dev --optimize-autoloader
```

### 第九步：配置环境文件

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

修改以下配置：
```ini
# 数据库配置
DATABASE = your_database_name
USERNAME = your_database_user
PASSWORD = your_database_password

# 邮件配置
EMAIL_SMTP_HOST = smtp.qq.com
EMAIL_SMTP_USERNAME = <EMAIL>
EMAIL_SMTP_PASSWORD = your_email_password

# 站点配置
SITE_NAME = 您的交易平台名称
SITE_URL = https://your-domain.com
```

### 第十步：导入数据库

1. 登录phpMyAdmin
2. 选择创建的数据库
3. 点击"导入"
4. 选择 `database/crypto_exchange.sql` 文件
5. 点击"执行"

或使用命令行：
```bash
mysql -u your_user -p your_database < database/crypto_exchange.sql
```

### 第十一步：设置文件权限

```bash
cd /www/wwwroot/your-domain
sudo chown -R www:www .
sudo chmod -R 755 .
sudo chmod -R 777 runtime
sudo chmod -R 777 public/uploads
```

### 第十二步：配置Nginx

在宝塔面板中点击站点设置，添加以下Nginx配置：

```nginx
location / {
    try_files $uri $uri/ /index.php?$query_string;
}

location ~ \.php$ {
    fastcgi_pass unix:/tmp/php-cgi-74.sock;
    fastcgi_index index.php;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    include fastcgi_params;
    fastcgi_read_timeout 300;
}

# 静态文件缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# 安全配置
location ~ /\. {
    deny all;
}

location ~ /(vendor|database|config)/ {
    deny all;
}
```

### 第十三步：设置定时任务

在宝塔面板的"计划任务"中添加：

```bash
# 队列处理（每分钟执行）
* * * * * cd /www/wwwroot/your-domain && php think queue:work >> /dev/null 2>&1

# 定时任务（每小时执行）
0 */1 * * * cd /www/wwwroot/your-domain && php think cron:run >> /dev/null 2>&1
```

### 第十四步：配置SSL证书

1. 在宝塔面板中进入站点设置
2. 点击"SSL"选项卡
3. 选择"Let's Encrypt"免费证书
4. 点击"申请"
5. 开启"强制HTTPS"

## ✅ 部署验证

### 1. 访问测试
- 前端：`https://your-domain.com`
- 管理后台：`https://your-domain.com/admin`
- 代理后台：`https://your-domain.com/agent`

### 2. 默认账号
- **管理员账号**: admin
- **管理员密码**: password

### 3. 功能测试
- [ ] 用户注册登录
- [ ] 邮件发送
- [ ] 文件上传
- [ ] 数据库连接
- [ ] 缓存功能

## 🔧 优化配置

### 1. 性能优化

#### MySQL优化
```sql
# 在MySQL配置文件中添加
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
query_cache_size = 64M
max_connections = 1000
```

#### Redis缓存
```bash
# 安装Redis
sudo apt install redis-server

# 启动Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

在 `.env` 文件中配置：
```ini
[CACHE]
DRIVER = redis

[REDIS]
HOST = 127.0.0.1
PORT = 6379
```

### 2. 安全加固

#### 防火墙配置
```bash
# 开启防火墙
sudo ufw enable

# 允许必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw allow 8888  # 宝塔面板
```

#### 修改默认端口
1. 修改SSH端口
2. 修改宝塔面板端口
3. 修改MySQL端口

### 3. 监控配置

#### 系统监控
在宝塔面板中安装"系统监控"插件

#### 日志监控
```bash
# 设置日志轮转
sudo nano /etc/logrotate.d/crypto-exchange

# 添加配置
/www/wwwroot/your-domain/runtime/log/*.log {
    daily
    missingok
    rotate 30
    compress
    notifempty
    create 644 www www
}
```

## 🔄 备份策略

### 1. 数据库备份
在宝塔面板中设置自动备份：
- 备份周期：每天
- 保留份数：7份
- 备份到云存储

### 2. 文件备份
```bash
# 创建备份脚本
sudo nano /root/backup.sh

#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup"
SITE_DIR="/www/wwwroot/your-domain"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份网站文件
tar -czf $BACKUP_DIR/site_$DATE.tar.gz -C $SITE_DIR .

# 删除7天前的备份
find $BACKUP_DIR -name "site_*.tar.gz" -mtime +7 -delete

# 设置执行权限
sudo chmod +x /root/backup.sh

# 添加到定时任务
0 2 * * * /root/backup.sh
```

## 🚨 故障排除

### 常见问题

#### 1. 500错误
- 检查PHP错误日志
- 检查文件权限
- 检查.env配置

#### 2. 数据库连接失败
- 检查数据库配置
- 检查数据库服务状态
- 检查用户权限

#### 3. 邮件发送失败
- 检查SMTP配置
- 检查邮箱设置
- 检查防火墙

#### 4. 静态资源404
- 检查Nginx配置
- 检查文件权限
- 检查路径设置

### 日志查看
```bash
# PHP错误日志
tail -f /www/server/php/74/var/log/php-fpm.log

# Nginx错误日志
tail -f /www/server/nginx/logs/error.log

# 应用日志
tail -f /www/wwwroot/your-domain/runtime/log/error.log
```

## 📞 技术支持

如遇到部署问题，请提供以下信息：
1. 服务器配置信息
2. 错误日志内容
3. 操作步骤描述
4. 环境配置信息

---

**部署完成后，请及时修改默认密码并做好安全防护！**
