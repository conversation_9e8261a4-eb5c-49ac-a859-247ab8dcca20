<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - GVD</title>
    <script src="/static/js/lang.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: Arial, sans-serif; background: #1a1a1a; color: #fff; 
            min-height: 100vh; display: flex; align-items: center; justify-content: center;
            background-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .container { 
            max-width: 400px; width: 100%; padding: 40px; background: #2d2d2d; 
            border-radius: 10px; border: 1px solid #333; box-shadow: 0 10px 30px rgba(0,0,0,0.5);
        }
        .logo { 
            text-align: center; font-size: 32px; font-weight: bold; margin-bottom: 30px; 
            color: #007bff; 
        }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; color: #ccc; }
        .form-group input { 
            width: 100%; padding: 12px; border: 1px solid #333; background: #1a1a1a; 
            color: #fff; border-radius: 5px; font-size: 16px;
        }
        .btn { 
            width: 100%; padding: 15px; background: #007bff; color: #fff; border: none; 
            border-radius: 5px; font-size: 16px; font-weight: bold; cursor: pointer; 
        }
        .links { text-align: center; margin-top: 20px; }
        .links a { color: #007bff; text-decoration: none; margin: 0 10px; }
        .back-home { text-align: center; margin-bottom: 20px; }
        .back-home a { color: #999; text-decoration: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="back-home">
            <a href="/" data-lang="home">← 返回首页</a>
        </div>
        <div class="logo">🚀 <span data-lang="login">用户登录</span></div>
        <form id="loginForm">
            <div class="form-group">
                <label>邮箱/用户名</label>
                <input type="text" id="username" placeholder="请输入邮箱或用户名" required>
            </div>
            <div class="form-group">
                <label>密码</label>
                <input type="password" id="password" placeholder="请输入密码" required>
            </div>
            <button type="submit" class="btn">立即登录</button>
        </form>
        <div class="links">
            <a href="/auth/register.html">立即注册</a>
            <a href="/auth/forgot.html">忘记密码</a>
        </div>
    </div>
    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            if (!username || !password) {
                alert('请填写完整信息');
                return;
            }
            localStorage.setItem('isLoggedIn', 'true');
            localStorage.setItem('username', username);
            alert('登录成功！');
            window.location.href = '/';
        });
        document.addEventListener('DOMContentLoaded', function() {
            if (localStorage.getItem('isLoggedIn') === 'true') {
                window.location.href = '/';
            }
        });
    </script>
</body>
</html>
