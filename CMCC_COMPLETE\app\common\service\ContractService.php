<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Db;
use think\facade\Cache;
use think\facade\Log;
use app\common\model\ContractOrder;
use app\common\model\UserAsset;
use app\common\model\FinancialRecord;

/**
 * 合约服务类
 */
class ContractService
{
    // 合约状态常量
    const STATUS_ACTIVE = 1;    // 进行中
    const STATUS_SETTLED = 2;   // 已结算
    const STATUS_CANCELLED = 3; // 已取消
    
    // 合约结果常量
    const RESULT_WIN = 1;   // 盈利
    const RESULT_LOSE = 0;  // 亏损

    /**
     * 获取合约配置（从数据库读取，支持后台配置）
     */
    public function getContractConfig(): array
    {
        // 从数据库获取配置，如果没有则使用默认值
        $config = Cache::remember('contract_config', function() {
            $dbConfig = Db::name('contract_config')->find();
            if ($dbConfig) {
                // 解析JSON字段
                $dbConfig['available_times'] = json_decode($dbConfig['available_times'] ?? '[]', true);
                $dbConfig['available_coins'] = json_decode($dbConfig['available_coins'] ?? '[]', true);
                $dbConfig['quick_amounts'] = json_decode($dbConfig['quick_amounts'] ?? '[]', true);
                $dbConfig['profit_rates'] = json_decode($dbConfig['profit_rates'] ?? '{}', true);
            }
            return $dbConfig ?: [];
        }, 300);

        // 默认配置
        $defaultConfig = [
            'min_amount' => 10,      // 最小交易金额
            'max_amount' => 10000,   // 最大交易金额
            'win_rate' => 0.85,      // 胜率85%
            'available_times' => [60, 180, 300, 600], // 可选时长(秒) 1分钟、3分钟、5分钟、10分钟
            'available_coins' => ['BTC', 'ETH', 'LTC', 'EOS', 'XRP'], // 可交易币种
            'quick_amounts' => [50, 100, 500, 1000], // 快捷金额按钮
            // 不同时长的赔率配置
            'profit_rates' => [
                60 => 0.80,   // 60秒 80%赔率
                180 => 0.85,  // 180秒 85%赔率
                300 => 0.90,  // 300秒 90%赔率
                600 => 0.95   // 600秒 95%赔率
            ]
        ];

        // 合并配置
        return array_merge($defaultConfig, $config);
    }

    /**
     * 创建合约订单
     */
    public function createOrder(int $userId, array $data): array
    {
        try {
            Db::startTrans();
            
            // 验证合约配置
            $config = $this->getContractConfig();
            
            // 验证交易金额
            if ($data['amount'] < $config['min_amount'] || $data['amount'] > $config['max_amount']) {
                throw new \Exception("交易金额必须在 {$config['min_amount']} - {$config['max_amount']} USDT 之间");
            }
            
            // 验证币种
            if (!in_array($data['coin_name'], $config['available_coins'])) {
                throw new \Exception('不支持的交易币种');
            }
            
            // 验证时长
            if (!in_array($data['time_length'], $config['available_times'])) {
                throw new \Exception('不支持的合约时长');
            }
            
            // 检查用户余额
            $userAsset = UserAsset::where('user_id', $userId)
                                 ->where('coin_symbol', 'USDT')
                                 ->find();
            
            if (!$userAsset || $userAsset->available < $data['amount']) {
                throw new \Exception('余额不足');
            }
            
            // 获取当前价格
            $currentPrice = $this->getRealTimePrice($data['coin_name']);
            
            // 计算结束时间
            $endTime = date('Y-m-d H:i:s', time() + $data['time_length']);
            
            // 创建合约订单
            $contract = ContractOrder::create([
                'user_id' => $userId,
                'coin_name' => $data['coin_name'],
                'direction' => $data['direction'],
                'amount' => $data['amount'],
                'buy_price' => $currentPrice,
                'time_length' => $data['time_length'],
                'end_time' => $endTime,
                'status' => self::STATUS_ACTIVE,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            // 扣除用户余额
            $userAsset->available -= $data['amount'];
            $userAsset->frozen += $data['amount'];
            $userAsset->save();
            
            // 记录财务流水
            FinancialRecord::create([
                'user_id' => $userId,
                'type' => 'contract_buy',
                'coin_symbol' => 'USDT',
                'amount' => -$data['amount'],
                'balance' => $userAsset->available,
                'description' => '购买' . $data['coin_name'] . '合约',
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            Db::commit();
            
            return [
                'code' => 1,
                'msg' => '合约创建成功',
                'data' => [
                    'contract_id' => $contract->id,
                    'end_time' => $endTime
                ]
            ];
            
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 获取实时价格
     */
    public function getRealTimePrice(string $coinName): float
    {
        // 这里应该从第三方API获取实时价格
        // 暂时返回模拟价格
        $prices = [
            'BTC' => 45000 + mt_rand(-1000, 1000),
            'ETH' => 3000 + mt_rand(-100, 100),
            'LTC' => 150 + mt_rand(-10, 10),
            'EOS' => 5 + mt_rand(-1, 1),
            'XRP' => 0.5 + mt_rand(-10, 10) / 100
        ];
        
        return $prices[$coinName] ?? 1000;
    }

    /**
     * 结算到期合约
     */
    public function settleExpiredContracts(): int
    {
        $settledCount = 0;
        
        try {
            // 获取到期的合约
            $expiredContracts = ContractOrder::where('status', self::STATUS_ACTIVE)
                                           ->where('end_time', '<=', date('Y-m-d H:i:s'))
                                           ->select();
            
            foreach ($expiredContracts as $contract) {
                $this->settleContract($contract);
                $settledCount++;
            }
            
            Log::info("合约结算完成，共结算 {$settledCount} 笔合约");
            
        } catch (\Exception $e) {
            Log::error('合约结算失败：' . $e->getMessage());
        }
        
        return $settledCount;
    }

    /**
     * 结算单个合约
     */
    private function settleContract(ContractOrder $contract): void
    {
        try {
            Db::startTrans();
            
            // 获取结算价格
            $settlePrice = $this->getRealTimePrice($contract->coin_name);
            
            // 判断盈亏
            $isWin = false;
            if ($contract->direction == 1) { // 买涨
                $isWin = $settlePrice > $contract->buy_price;
            } else { // 买跌
                $isWin = $settlePrice < $contract->buy_price;
            }
            
            // 计算盈亏金额
            $profitLoss = 0;
            if ($isWin) {
                $config = $this->getContractConfig();
                $profitRate = $config['profit_rates'][$contract->time_length] ?? 0.80;
                $profitLoss = $contract->amount * $profitRate;
            } else {
                $profitLoss = -$contract->amount;
            }
            
            // 更新合约状态
            $contract->settle_price = $settlePrice;
            $contract->is_win = $isWin ? self::RESULT_WIN : self::RESULT_LOSE;
            $contract->profit_loss = $profitLoss;
            $contract->status = self::STATUS_SETTLED;
            $contract->settle_time = date('Y-m-d H:i:s');
            $contract->save();
            
            // 更新用户资产
            $userAsset = UserAsset::where('user_id', $contract->user_id)
                                 ->where('coin_symbol', 'USDT')
                                 ->find();
            
            if ($userAsset) {
                // 解冻资金
                $userAsset->frozen -= $contract->amount;
                
                if ($isWin) {
                    // 盈利：返还本金 + 收益
                    $userAsset->available += $contract->amount + $profitLoss;
                    
                    // 记录盈利流水
                    FinancialRecord::create([
                        'user_id' => $contract->user_id,
                        'type' => 'contract_win',
                        'coin_symbol' => 'USDT',
                        'amount' => $contract->amount + $profitLoss,
                        'balance' => $userAsset->available,
                        'description' => $contract->coin_name . '合约盈利',
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                } else {
                    // 亏损：本金已在下单时扣除，记录亏损流水
                    FinancialRecord::create([
                        'user_id' => $contract->user_id,
                        'type' => 'contract_lose',
                        'coin_symbol' => 'USDT',
                        'amount' => $profitLoss,
                        'balance' => $userAsset->available,
                        'description' => $contract->coin_name . '合约亏损',
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                }
                
                $userAsset->save();
            }
            
            Db::commit();
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('结算合约失败：' . $e->getMessage());
        }
    }

    /**
     * 获取合约倒计时信息
     */
    public function getContractTimer(ContractOrder $contract): array
    {
        if ($contract->status != self::STATUS_ACTIVE) {
            return ['code' => 0, 'msg' => '合约已结束'];
        }
        
        $endTime = strtotime($contract->end_time);
        $remaining = $endTime - time();
        
        if ($remaining <= 0) {
            return ['code' => 0, 'msg' => '合约已到期'];
        }
        
        return [
            'code' => 1,
            'data' => [
                'remaining_seconds' => $remaining,
                'end_time' => $contract->end_time,
                'current_price' => $this->getRealTimePrice($contract->coin_name)
            ]
        ];
    }

    /**
     * 手动设置合约结果（代理端使用）
     */
    public function setContractResult(int $contractId, bool $isWin, int $operatorId): array
    {
        try {
            Db::startTrans();
            
            $contract = ContractOrder::find($contractId);
            if (!$contract) {
                throw new \Exception('合约不存在');
            }
            
            if ($contract->status != self::STATUS_ACTIVE) {
                throw new \Exception('合约已结算，无法修改');
            }
            
            // 获取当前价格作为结算价格
            $settlePrice = $this->getRealTimePrice($contract->coin_name);
            
            // 计算盈亏金额
            $profitLoss = 0;
            if ($isWin) {
                $config = $this->getContractConfig();
                $profitRate = $config['profit_rates'][$contract->time_length] ?? 0.80;
                $profitLoss = $contract->amount * $profitRate;
            } else {
                $profitLoss = -$contract->amount;
            }
            
            // 更新合约状态
            $contract->settle_price = $settlePrice;
            $contract->is_win = $isWin ? self::RESULT_WIN : self::RESULT_LOSE;
            $contract->profit_loss = $profitLoss;
            $contract->status = self::STATUS_SETTLED;
            $contract->settle_time = date('Y-m-d H:i:s');
            $contract->save();
            
            // 更新用户资产
            $this->updateUserAssetAfterSettle($contract, $isWin, $profitLoss);
            
            // 记录操作日志
            Db::name('admin_logs')->insert([
                'admin_id' => $operatorId,
                'action' => 'set_contract_result',
                'content' => "手动设置合约 {$contractId} 结果为 " . ($isWin ? '盈利' : '亏损'),
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            Db::commit();
            
            return ['code' => 1, 'msg' => '合约结果设置成功'];
            
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 更新用户资产（结算后）
     */
    private function updateUserAssetAfterSettle(ContractOrder $contract, bool $isWin, float $profitLoss): void
    {
        $userAsset = UserAsset::where('user_id', $contract->user_id)
                             ->where('coin_symbol', 'USDT')
                             ->find();
        
        if ($userAsset) {
            // 解冻资金
            $userAsset->frozen -= $contract->amount;
            
            if ($isWin) {
                // 盈利：返还本金 + 收益
                $userAsset->available += $contract->amount + $profitLoss;
                
                // 记录盈利流水
                FinancialRecord::create([
                    'user_id' => $contract->user_id,
                    'type' => 'contract_win',
                    'coin_symbol' => 'USDT',
                    'amount' => $contract->amount + $profitLoss,
                    'balance' => $userAsset->available,
                    'description' => $contract->coin_name . '合约盈利（手动设置）',
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            } else {
                // 亏损：记录亏损流水
                FinancialRecord::create([
                    'user_id' => $contract->user_id,
                    'type' => 'contract_lose',
                    'coin_symbol' => 'USDT',
                    'amount' => $profitLoss,
                    'balance' => $userAsset->available,
                    'description' => $contract->coin_name . '合约亏损（手动设置）',
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }
            
            $userAsset->save();
        }
    }
}
