<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\common\service\CustomerService;
use think\facade\Validate;

/**
 * 管理员客服控制器
 */
class Customer extends BaseController
{
    protected $customerService;

    public function __construct(\think\App $app, CustomerService $customerService)
    {
        parent::__construct($app);
        $this->customerService = $customerService;
    }

    /**
     * 获取所有会话列表
     */
    public function getSessions()
    {
        $params = $this->getPaginationParams();
        $params['status'] = $this->request->param('status');
        $params['agent_id'] = $this->request->param('agent_id/d');
        $params['keyword'] = $this->request->param('keyword');

        $result = $this->customerService->getUserSessions(0, 'admin');
        
        if ($result['code'] == 1) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 发送消息
     */
    public function sendMessage()
    {
        $data = $this->getParams([
            'session_id', 'content', 'message_type'
        ]);

        // 添加发送者信息
        $data['sender_id'] = $this->getAdminId();
        $data['sender_type'] = 'admin';

        // 验证参数
        $validate = Validate::rule([
            'session_id' => 'require|length:32',
            'content' => 'require|max:1000',
            'message_type' => 'in:text,image,emoji'
        ])->message([
            'session_id.require' => '会话ID不能为空',
            'session_id.length' => '会话ID格式错误',
            'content.require' => '消息内容不能为空',
            'content.max' => '消息内容不能超过1000字符',
            'message_type.in' => '消息类型错误'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        $result = $this->customerService->sendMessage($data);
        
        if ($result['code'] == 1) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取会话消息
     */
    public function getMessages()
    {
        $sessionId = $this->request->param('session_id');
        $page = $this->request->param('page/d', 1);
        $limit = $this->request->param('limit/d', 50);

        if (empty($sessionId)) {
            return $this->error('会话ID不能为空');
        }

        $result = $this->customerService->getSessionMessages($sessionId, $page, $limit);
        
        if ($result['code'] == 1) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 标记消息为已读
     */
    public function markAsRead()
    {
        $sessionId = $this->request->param('session_id');
        
        if (empty($sessionId)) {
            return $this->error('会话ID不能为空');
        }

        $result = $this->customerService->markMessagesAsRead(
            $sessionId, 
            $this->getAdminId(), 
            'admin'
        );
        
        if ($result['code'] == 1) {
            return $this->success([], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 关闭会话
     */
    public function closeSession()
    {
        $sessionId = $this->request->param('session_id');
        
        if (empty($sessionId)) {
            return $this->error('会话ID不能为空');
        }

        try {
            \think\facade\Db::name('customer_sessions')
                ->where('session_id', $sessionId)
                ->update([
                    'status' => 'closed',
                    'closed_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            return $this->success([], '会话关闭成功');

        } catch (\Exception $e) {
            return $this->error('关闭会话失败: ' . $e->getMessage());
        }
    }

    /**
     * 转接会话
     */
    public function transferSession()
    {
        $data = $this->getParams(['session_id', 'target_agent_id']);

        // 验证参数
        $validate = Validate::rule([
            'session_id' => 'require|length:32',
            'target_agent_id' => 'require|integer|gt:0'
        ])->message([
            'session_id.require' => '会话ID不能为空',
            'target_agent_id.require' => '目标代理不能为空',
            'target_agent_id.integer' => '代理ID必须为整数',
            'target_agent_id.gt' => '代理ID必须大于0'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        try {
            // 验证目标代理是否存在
            $targetAgent = \think\facade\Db::name('users')
                ->where('id', $data['target_agent_id'])
                ->where('user_type', 2)
                ->where('status', 1)
                ->find();

            if (!$targetAgent) {
                return $this->error('目标代理不存在或已禁用');
            }

            // 更新会话代理
            \think\facade\Db::name('customer_sessions')
                ->where('session_id', $data['session_id'])
                ->update([
                    'agent_id' => $data['target_agent_id'],
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            // 发送转接通知消息
            $this->sendTransferNotification($data['session_id'], $targetAgent['username']);

            return $this->success([], '会话转接成功');

        } catch (\Exception $e) {
            return $this->error('转接会话失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取快捷回复
     */
    public function getQuickReplies()
    {
        $category = $this->request->param('category', '');
        
        $result = $this->customerService->getQuickReplies($category);
        
        return $this->success($result['data'], $result['msg']);
    }

    /**
     * 添加快捷回复
     */
    public function addQuickReply()
    {
        $data = $this->getParams(['title', 'content', 'category']);

        // 验证参数
        $validate = Validate::rule([
            'title' => 'require|max:100',
            'content' => 'require|max:1000',
            'category' => 'require|in:general,trading,account,technical'
        ])->message([
            'title.require' => '标题不能为空',
            'title.max' => '标题不能超过100字符',
            'content.require' => '内容不能为空',
            'content.max' => '内容不能超过1000字符',
            'category.require' => '分类不能为空',
            'category.in' => '分类值错误'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        try {
            $data['created_by'] = $this->getAdminId();
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['updated_at'] = date('Y-m-d H:i:s');

            \think\facade\Db::name('customer_quick_replies')->insert($data);

            return $this->success([], '添加成功');

        } catch (\Exception $e) {
            return $this->error('添加失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新快捷回复
     */
    public function updateQuickReply()
    {
        $id = $this->request->param('id/d');
        $data = $this->getParams(['title', 'content', 'category']);

        if (!$id) {
            return $this->error('ID不能为空');
        }

        // 验证参数
        $validate = Validate::rule([
            'title' => 'require|max:100',
            'content' => 'require|max:1000',
            'category' => 'require|in:general,trading,account,technical'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        try {
            $data['updated_at'] = date('Y-m-d H:i:s');

            \think\facade\Db::name('customer_quick_replies')
                ->where('id', $id)
                ->update($data);

            return $this->success([], '更新成功');

        } catch (\Exception $e) {
            return $this->error('更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除快捷回复
     */
    public function deleteQuickReply()
    {
        $id = $this->request->param('id/d');

        if (!$id) {
            return $this->error('ID不能为空');
        }

        try {
            \think\facade\Db::name('customer_quick_replies')
                ->where('id', $id)
                ->delete();

            return $this->success([], '删除成功');

        } catch (\Exception $e) {
            return $this->error('删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取客服统计
     */
    public function getStatistics()
    {
        $startDate = $this->request->param('start_date', date('Y-m-d', strtotime('-7 days')));
        $endDate = $this->request->param('end_date', date('Y-m-d'));

        try {
            // 会话统计
            $sessionStats = \think\facade\Db::name('customer_sessions')
                ->whereTime('created_at', 'between', [$startDate, $endDate])
                ->field('
                    COUNT(*) as total_sessions,
                    SUM(CASE WHEN status = "active" THEN 1 ELSE 0 END) as active_sessions,
                    SUM(CASE WHEN status = "closed" THEN 1 ELSE 0 END) as closed_sessions
                ')
                ->find();

            // 消息统计
            $messageStats = \think\facade\Db::name('customer_messages')
                ->whereTime('created_at', 'between', [$startDate, $endDate])
                ->field('
                    COUNT(*) as total_messages,
                    COUNT(DISTINCT session_id) as active_sessions_with_messages
                ')
                ->find();

            // 在线状态统计
            $onlineStats = \think\facade\Db::name('customer_online_status')
                ->field('
                    user_type,
                    COUNT(*) as total_count,
                    SUM(CASE WHEN status = "online" THEN 1 ELSE 0 END) as online_count
                ')
                ->group('user_type')
                ->select()
                ->toArray();

            // 代理工作量统计
            $agentStats = \think\facade\Db::name('customer_sessions cs')
                ->join('users u', 'cs.agent_id = u.id')
                ->whereTime('cs.created_at', 'between', [$startDate, $endDate])
                ->field('
                    u.username,
                    COUNT(cs.id) as session_count,
                    AVG(CASE WHEN cs.closed_at IS NOT NULL 
                        THEN TIMESTAMPDIFF(MINUTE, cs.created_at, cs.closed_at) 
                        ELSE NULL END) as avg_session_duration
                ')
                ->group('cs.agent_id')
                ->select()
                ->toArray();

            $statistics = [
                'session_stats' => $sessionStats,
                'message_stats' => $messageStats,
                'online_stats' => $onlineStats,
                'agent_stats' => $agentStats,
                'date_range' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ]
            ];

            return $this->success($statistics, '获取成功');

        } catch (\Exception $e) {
            return $this->error('获取统计失败: ' . $e->getMessage());
        }
    }

    /**
     * 发送转接通知消息
     */
    private function sendTransferNotification(string $sessionId, string $agentName): void
    {
        $messageData = [
            'session_id' => $sessionId,
            'sender_id' => $this->getAdminId(),
            'sender_type' => 'admin',
            'message_type' => 'text',
            'content' => "会话已转接给客服 {$agentName}，请稍等片刻。",
            'is_read' => 0,
            'created_at' => date('Y-m-d H:i:s')
        ];

        \think\facade\Db::name('customer_messages')->insert($messageData);
    }

    /**
     * 获取管理员ID
     */
    private function getAdminId(): int
    {
        // 从JWT token或session中获取管理员ID
        return 1; // 临时返回，实际应该从认证中获取
    }
}
