<?php
declare (strict_types = 1);

namespace app\index\controller;

use app\BaseController;
use think\facade\Session;
use think\facade\Request;
use think\facade\Cookie;

/**
 * 多语言控制器
 */
class Language extends BaseController
{
    /**
     * 支持的语言列表
     */
    private $supportedLanguages = [
        'zh-cn' => '简体中文',
        'en-us' => 'English',
        'ja-jp' => '日本語',
        'ko-kr' => '한국어',
        'fr-fr' => 'Français',
        'de-de' => 'Deutsch',
        'it-it' => 'Italiano',
        'tr-tr' => 'Türkçe'
    ];

    /**
     * 切换语言
     */
    public function switch()
    {
        $lang = Request::get('lang', 'zh-cn');
        
        // 验证语言是否支持
        if (!array_key_exists($lang, $this->supportedLanguages)) {
            $lang = 'zh-cn';
        }
        
        // 设置语言到Session和Cookie
        Session::set('language', $lang);
        Cookie::set('language', $lang, 86400 * 30); // 30天
        
        // 获取返回URL
        $returnUrl = Request::get('return_url', '/');
        
        if (Request::isAjax()) {
            return json([
                'code' => 1,
                'msg' => '语言切换成功',
                'data' => [
                    'language' => $lang,
                    'language_name' => $this->supportedLanguages[$lang]
                ]
            ]);
        }
        
        $this->redirect($returnUrl);
    }

    /**
     * 获取当前语言
     */
    public function current()
    {
        $currentLang = $this->getCurrentLanguage();
        
        return json([
            'code' => 1,
            'data' => [
                'current_language' => $currentLang,
                'current_language_name' => $this->supportedLanguages[$currentLang],
                'supported_languages' => $this->supportedLanguages
            ]
        ]);
    }

    /**
     * 获取语言包
     */
    public function pack()
    {
        $lang = Request::get('lang', $this->getCurrentLanguage());
        
        // 验证语言是否支持
        if (!array_key_exists($lang, $this->supportedLanguages)) {
            $lang = 'zh-cn';
        }
        
        $langPack = $this->getLanguagePack($lang);
        
        return json([
            'code' => 1,
            'data' => [
                'language' => $lang,
                'pack' => $langPack
            ]
        ]);
    }

    /**
     * 获取当前语言
     */
    private function getCurrentLanguage(): string
    {
        // 优先从Session获取
        $lang = Session::get('language');

        // 其次从Cookie获取
        if (!$lang) {
            $lang = Cookie::get('language');
        }

        // 根据IP地区自动识别
        if (!$lang) {
            $lang = $this->getLanguageByIP();
        }

        // 最后从浏览器语言获取
        if (!$lang) {
            $acceptLang = Request::header('accept-language', '');
            if (strpos($acceptLang, 'zh') !== false) {
                $lang = 'zh-cn';
            } elseif (strpos($acceptLang, 'en') !== false) {
                $lang = 'en-us';
            } elseif (strpos($acceptLang, 'ja') !== false) {
                $lang = 'ja-jp';
            } elseif (strpos($acceptLang, 'ko') !== false) {
                $lang = 'ko-kr';
            } elseif (strpos($acceptLang, 'fr') !== false) {
                $lang = 'fr-fr';
            } elseif (strpos($acceptLang, 'de') !== false) {
                $lang = 'de-de';
            } elseif (strpos($acceptLang, 'it') !== false) {
                $lang = 'it-it';
            } elseif (strpos($acceptLang, 'tr') !== false) {
                $lang = 'tr-tr';
            } else {
                $lang = 'en-us'; // 默认英文
            }
        }

        // 验证语言是否支持
        if (!array_key_exists($lang, $this->supportedLanguages)) {
            $lang = 'en-us';
        }

        return $lang;
    }

    /**
     * 根据IP获取语言
     */
    private function getLanguageByIP(): string
    {
        $ip = Request::ip();

        // 这里应该对接IP地理位置API，现在用简单的IP段判断
        // 中国IP段示例
        if ($this->isChineseIP($ip)) {
            return 'zh-cn';
        }

        // 日本IP段
        if ($this->isJapaneseIP($ip)) {
            return 'ja-jp';
        }

        // 韩国IP段
        if ($this->isKoreanIP($ip)) {
            return 'ko-kr';
        }

        // 法国IP段
        if ($this->isFrenchIP($ip)) {
            return 'fr-fr';
        }

        // 德国IP段
        if ($this->isGermanIP($ip)) {
            return 'de-de';
        }

        // 意大利IP段
        if ($this->isItalianIP($ip)) {
            return 'it-it';
        }

        // 土耳其IP段
        if ($this->isTurkishIP($ip)) {
            return 'tr-tr';
        }

        // 默认英文
        return 'en-us';
    }

    private function isChineseIP($ip): bool
    {
        // 简单的中国IP段判断，实际应该使用专业的IP地理位置库
        $chineseRanges = [
            '*******/8', '********/8', '********/8', '********/8',
            '********/8', '********/8', '********/8', '********/8'
        ];

        foreach ($chineseRanges as $range) {
            if ($this->ipInRange($ip, $range)) {
                return true;
            }
        }

        return false;
    }

    private function isJapaneseIP($ip): bool { return false; } // 简化实现
    private function isKoreanIP($ip): bool { return false; }
    private function isFrenchIP($ip): bool { return false; }
    private function isGermanIP($ip): bool { return false; }
    private function isItalianIP($ip): bool { return false; }
    private function isTurkishIP($ip): bool { return false; }

    private function ipInRange($ip, $range): bool
    {
        list($subnet, $bits) = explode('/', $range);
        $ip = ip2long($ip);
        $subnet = ip2long($subnet);
        $mask = -1 << (32 - $bits);
        $subnet &= $mask;
        return ($ip & $mask) == $subnet;
    }

    /**
     * 获取语言包
     */
    private function getLanguagePack(string $lang): array
    {
        $langFile = app()->getAppPath() . 'lang/' . $lang . '.php';
        
        if (file_exists($langFile)) {
            return include $langFile;
        }
        
        // 返回默认中文语言包
        return [
            // 通用
            'home' => '首页',
            'trade' => '交易',
            'contract' => '合约',
            'assets' => '资产',
            'user' => '用户',
            'login' => '登录',
            'register' => '注册',
            'logout' => '退出',
            'submit' => '提交',
            'cancel' => '取消',
            'confirm' => '确认',
            'success' => '成功',
            'error' => '错误',
            'loading' => '加载中...',
            
            // 交易相关
            'buy' => '买入',
            'sell' => '卖出',
            'price' => '价格',
            'amount' => '数量',
            'total' => '总额',
            'available' => '可用',
            'frozen' => '冻结',
            'order_history' => '订单历史',
            'trade_history' => '交易历史',
            
            // 合约相关
            'contract_trade' => '合约交易',
            'buy_up' => '买涨',
            'buy_down' => '买跌',
            'contract_time' => '合约时长',
            'profit_loss' => '盈亏',
            'settlement' => '结算',
            
            // 用户相关
            'profile' => '个人资料',
            'security' => '安全设置',
            'kyc' => '实名认证',
            'deposit' => '充值',
            'withdraw' => '提现',
            'invite' => '邀请',
            
            // 状态
            'pending' => '待处理',
            'processing' => '处理中',
            'completed' => '已完成',
            'failed' => '失败',
            'cancelled' => '已取消'
        ];
    }

    /**
     * 获取支持的语言列表
     */
    public function list()
    {
        return json([
            'code' => 1,
            'data' => $this->supportedLanguages
        ]);
    }
}
