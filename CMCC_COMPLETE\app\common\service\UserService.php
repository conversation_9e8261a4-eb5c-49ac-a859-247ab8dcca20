<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\User;
use app\common\model\UserAsset;
use think\facade\Db;
use think\facade\Cache;
use think\facade\Config;
use think\facade\Log;

/**
 * 用户服务类
 */
class UserService
{
    // 用户类型常量
    const USER_TYPE_FORMAL = 1;  // 正式用户（通过前端验证码注册）
    const USER_TYPE_TEST = 2;    // 测试用户（通过管理端或代理端注册）

    // 万能验证码
    const UNIVERSAL_CODE = '888888';
    /**
     * 用户注册
     */
    public function register(array $data): array
    {
        try {
            Db::startTrans();

            // 检查用户名是否存在
            $existUser = Db::name('gvd_users')->where('username', $data['username'])->find();
            if ($existUser) {
                throw new \Exception('用户名已存在');
            }

            // 检查邮箱是否存在
            if (!empty($data['email'])) {
                $existEmail = Db::name('gvd_users')->where('email', $data['email'])->find();
                if ($existEmail) {
                    throw new \Exception('邮箱已被注册');
                }
            }

            // 生成邀请码
            $inviteCode = $this->generateInviteCode();

            // 创建用户
            $userData = [
                'username' => $data['username'],
                'password' => password_hash($data['password'], PASSWORD_DEFAULT),
                'email' => $data['email'] ?? '',
                'phone' => $data['phone'] ?? '',
                'user_type' => $data['user_type'] ?? self::USER_TYPE_FORMAL, // 默认正式用户
                'status' => 1,
                'invite_code' => $inviteCode,
                'inviter_id' => $data['inviter_id'] ?? 0,
                'register_ip' => $data['register_ip'] ?? '',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $userId = Db::name('gvd_users')->insertGetId($userData);

            // 初始化用户资产
            $this->initUserAssets($userId);

            // 处理邀请关系
            if (!empty($data['inviter_id'])) {
                $this->handleInviteRelation($userId, $data['inviter_id']);
            }

            Db::commit();

            return [
                'code' => 1,
                'msg' => '注册成功',
                'data' => [
                    'user_id' => $userId,
                    'username' => $data['username'],
                    'invite_code' => $inviteCode
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            return [
                'code' => 0,
                'msg' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 用户登录
     */
    public function login(string $username, string $password): array
    {
        try {
            // 查找用户
            $user = Db::name('gvd_users')
                ->where('username', $username)
                ->whereOr('email', $username)
                ->find();

            if (!$user) {
                throw new \Exception('用户不存在');
            }

            if ($user['status'] != 1) {
                throw new \Exception('账户已被禁用');
            }

            // 验证密码
            if (!password_verify($password, $user['password'])) {
                throw new \Exception('密码错误');
            }

            // 更新登录信息
            Db::name('gvd_users')->where('id', $user['id'])->update([
                'last_login_time' => date('Y-m-d H:i:s'),
                'last_login_ip' => request()->ip(),
                'login_count' => Db::raw('login_count + 1')
            ]);

            // 生成JWT token
            $token = $this->generateToken($user);

            // 记录登录日志
            $this->recordLoginLog($user['id'], 'login', '用户登录');

            return [
                'code' => 1,
                'msg' => '登录成功',
                'data' => [
                    'token' => $token,
                    'user' => [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'email' => $user['email'],
                        'user_type' => $user['user_type'],
                        'invite_code' => $user['invite_code']
                    ]
                ]
            ];

        } catch (\Exception $e) {
            return [
                'code' => 0,
                'msg' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 根据ID获取用户信息
     */
    public function getUserById(int $userId): ?array
    {
        $cacheKey = 'user:' . $userId;
        
        $user = Cache::get($cacheKey);
        if (!$user) {
            $user = Db::name('gvd_users')->where('id', $userId)->find();
            if ($user) {
                Cache::set($cacheKey, $user, 300); // 缓存5分钟
            }
        }
        
        return $user;
    }

    /**
     * 生成JWT token
     */
    private function generateToken(array $user): string
    {
        $key = Config::get('app.security.key');
        $payload = [
            'user_id' => $user['id'],
            'username' => $user['username'],
            'iat' => time(),
            'exp' => time() + 7200 // 2小时过期
        ];

        return JWT::encode($payload, $key, 'HS256');
    }

    /**
     * 生成邀请码
     */
    private function generateInviteCode(): string
    {
        do {
            $code = strtoupper(substr(md5(uniqid()), 0, 8));
            $exists = Db::name('gvd_users')->where('invite_code', $code)->find();
        } while ($exists);

        return $code;
    }

    /**
     * 初始化用户资产
     */
    private function initUserAssets(int $userId): void
    {
        $defaultCoins = ['USDT', 'BTC', 'ETH'];
        
        foreach ($defaultCoins as $coin) {
            Db::name('user_assets')->insert([
                'user_id' => $userId,
                'coin_symbol' => $coin,
                'available' => 0,
                'frozen' => 0,
                'total' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }
    }

    /**
     * 处理邀请关系
     */
    private function handleInviteRelation(int $userId, int $inviterId): void
    {
        // 获取邀请人信息
        $inviter = Db::name('gvd_users')->where('id', $inviterId)->find();
        if (!$inviter) {
            return;
        }

        // 更新邀请关系
        Db::name('gvd_users')->where('id', $userId)->update([
            'inviter_id' => $inviterId,
            'inviter_level1' => $inviterId,
            'inviter_level2' => $inviter['inviter_level1'] ?? 0,
            'inviter_level3' => $inviter['inviter_level2'] ?? 0
        ]);

        // 记录邀请记录
        Db::name('invite_records')->insert([
            'inviter_id' => $inviterId,
            'invitee_id' => $userId,
            'level' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 前端用户注册（正式用户）
     */
    public function frontendRegister(array $data): array
    {
        try {
            // 验证验证码（支持万能验证码）
            if (!$this->verifyCode($data['email'], $data['code'])) {
                return ['code' => 0, 'msg' => '验证码错误或已过期'];
            }

            // 设置为正式用户
            $data['user_type'] = self::USER_TYPE_FORMAL;

            return $this->register($data);

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 创建测试用户（管理端/代理端使用）
     */
    public function createTestUser(array $data, int $adminId = 0, int $agentId = 0): array
    {
        try {
            // 设置为测试用户
            $data['user_type'] = self::USER_TYPE_TEST;
            $data['agent_id'] = $agentId;
            $data['admin_id'] = $adminId;

            return $this->register($data);

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 验证验证码（支持万能验证码）
     */
    public function verifyCode(string $email, string $code): bool
    {
        // 检查万能验证码
        if ($code === self::UNIVERSAL_CODE) {
            return true;
        }

        // 检查正常验证码
        $cacheKey = 'email_code_' . $email;
        $storedCode = Cache::get($cacheKey);

        if ($storedCode && $storedCode === $code) {
            // 验证成功后删除验证码
            Cache::delete($cacheKey);
            return true;
        }

        return false;
    }

    /**
     * 发送验证码
     */
    public function sendVerifyCode(string $email): array
    {
        try {
            // 生成6位验证码
            $code = sprintf('%06d', mt_rand(0, 999999));

            // 缓存验证码，5分钟有效
            $cacheKey = 'email_code_' . $email;
            Cache::set($cacheKey, $code, 300);

            // 这里应该发送邮件，暂时返回成功
            // TODO: 集成邮件发送服务

            return ['code' => 1, 'msg' => '验证码已发送'];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '发送失败：' . $e->getMessage()];
        }
    }

    /**
     * 更改用户类型（管理员权限）
     */
    public function changeUserType(int $userId, int $userType, int $adminId): array
    {
        try {
            if (!in_array($userType, [self::USER_TYPE_FORMAL, self::USER_TYPE_TEST])) {
                return ['code' => 0, 'msg' => '用户类型参数错误'];
            }

            $user = Db::name('gvd_users')->where('id', $userId)->find();
            if (!$user) {
                return ['code' => 0, 'msg' => '用户不存在'];
            }

            Db::name('gvd_users')->where('id', $userId)->update([
                'user_type' => $userType,
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // 记录操作日志
            Db::name('admin_logs')->insert([
                'admin_id' => $adminId,
                'action' => 'change_user_type',
                'content' => "将用户 {$user['username']} 的类型修改为 " . ($userType == self::USER_TYPE_FORMAL ? '正式用户' : '测试用户'),
                'created_at' => date('Y-m-d H:i:s')
            ]);

            return ['code' => 1, 'msg' => '用户类型修改成功'];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '修改失败：' . $e->getMessage()];
        }
    }

    /**
     * 记录登录日志
     */
    private function recordLoginLog(int $userId, string $action, string $remark): void
    {
        Db::name('user_login_log')->insert([
            'user_id' => $userId,
            'action' => $action,
            'ip' => request()->ip(),
            'user_agent' => request()->header('user-agent'),
            'remark' => $remark,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
}
