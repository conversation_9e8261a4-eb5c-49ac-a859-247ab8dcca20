<?php
declare (strict_types = 1);

namespace app\agent\controller;

use app\common\service\CustomerService;
use think\facade\Validate;

/**
 * 代理客服控制器
 */
class Customer extends BaseController
{
    protected $customerService;

    public function __construct(\think\App $app, CustomerService $customerService)
    {
        parent::__construct($app);
        $this->customerService = $customerService;
    }

    /**
     * 获取代理的会话列表
     */
    public function getSessions()
    {
        $agentId = $this->getAgentId();
        
        $result = $this->customerService->getUserSessions($agentId, 'agent');
        
        if ($result['code'] == 1) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 发送消息
     */
    public function sendMessage()
    {
        $data = $this->getParams([
            'session_id', 'content', 'message_type'
        ]);

        // 添加发送者信息
        $data['sender_id'] = $this->getAgentId();
        $data['sender_type'] = 'agent';

        // 验证参数
        $validate = Validate::rule([
            'session_id' => 'require|length:32',
            'content' => 'require|max:1000',
            'message_type' => 'in:text,image,emoji'
        ])->message([
            'session_id.require' => '会话ID不能为空',
            'session_id.length' => '会话ID格式错误',
            'content.require' => '消息内容不能为空',
            'content.max' => '消息内容不能超过1000字符',
            'message_type.in' => '消息类型错误'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        // 验证会话权限
        if (!$this->validateSessionAccess($data['session_id'])) {
            return $this->error('无权限访问此会话');
        }

        $result = $this->customerService->sendMessage($data);
        
        if ($result['code'] == 1) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取会话消息
     */
    public function getMessages()
    {
        $sessionId = $this->request->param('session_id');
        $page = $this->request->param('page/d', 1);
        $limit = $this->request->param('limit/d', 50);

        if (empty($sessionId)) {
            return $this->error('会话ID不能为空');
        }

        // 验证会话权限
        if (!$this->validateSessionAccess($sessionId)) {
            return $this->error('无权限访问此会话');
        }

        $result = $this->customerService->getSessionMessages($sessionId, $page, $limit);
        
        if ($result['code'] == 1) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 标记消息为已读
     */
    public function markAsRead()
    {
        $sessionId = $this->request->param('session_id');
        
        if (empty($sessionId)) {
            return $this->error('会话ID不能为空');
        }

        // 验证会话权限
        if (!$this->validateSessionAccess($sessionId)) {
            return $this->error('无权限访问此会话');
        }

        $result = $this->customerService->markMessagesAsRead(
            $sessionId, 
            $this->getAgentId(), 
            'agent'
        );
        
        if ($result['code'] == 1) {
            return $this->success([], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取快捷回复
     */
    public function getQuickReplies()
    {
        $category = $this->request->param('category', '');
        
        $result = $this->customerService->getQuickReplies($category);
        
        return $this->success($result['data'], $result['msg']);
    }

    /**
     * 获取我的客户列表
     */
    public function getMyCustomers()
    {
        $agentId = $this->getAgentId();
        $page = $this->request->param('page/d', 1);
        $limit = $this->request->param('limit/d', 20);
        $keyword = $this->request->param('keyword', '');

        try {
            $query = \think\facade\Db::name('users')
                ->where('agent_id', $agentId)
                ->where('status', 1);

            if ($keyword) {
                $query->where('username|email|phone', 'like', "%{$keyword}%");
            }

            $total = $query->count();
            $customers = $query->page($page, $limit)
                ->field('id, username, email, phone, balance, created_at, last_login_time')
                ->order('created_at desc')
                ->select()
                ->toArray();

            // 获取每个客户的会话状态
            foreach ($customers as &$customer) {
                $session = \think\facade\Db::name('customer_sessions')
                    ->where('user_id', $customer['id'])
                    ->where('status', 'active')
                    ->find();

                $customer['has_active_session'] = !empty($session);
                $customer['session_id'] = $session['session_id'] ?? null;
                
                // 获取未读消息数
                if ($session) {
                    $customer['unread_count'] = $session['unread_count_agent'] ?? 0;
                } else {
                    $customer['unread_count'] = 0;
                }
            }

            return $this->success([
                'customers' => $customers,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ], '获取成功');

        } catch (\Exception $e) {
            return $this->error('获取客户列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 主动联系客户
     */
    public function contactCustomer()
    {
        $userId = $this->request->param('user_id/d');
        
        if (!$userId) {
            return $this->error('用户ID不能为空');
        }

        // 验证客户是否属于当前代理
        $customer = \think\facade\Db::name('users')
            ->where('id', $userId)
            ->where('agent_id', $this->getAgentId())
            ->find();

        if (!$customer) {
            return $this->error('客户不存在或不属于您');
        }

        // 检查是否已有活跃会话
        $existingSession = \think\facade\Db::name('customer_sessions')
            ->where('user_id', $userId)
            ->where('status', 'active')
            ->find();

        if ($existingSession) {
            return $this->success([
                'session_id' => $existingSession['session_id']
            ], '会话已存在');
        }

        // 创建新会话
        $result = $this->customerService->createSession($userId);
        
        if ($result['code'] == 1) {
            return $this->success($result['data'], '会话创建成功');
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取代理工作统计
     */
    public function getWorkStatistics()
    {
        $agentId = $this->getAgentId();
        $startDate = $this->request->param('start_date', date('Y-m-d', strtotime('-7 days')));
        $endDate = $this->request->param('end_date', date('Y-m-d'));

        try {
            // 会话统计
            $sessionStats = \think\facade\Db::name('customer_sessions')
                ->where('agent_id', $agentId)
                ->whereTime('created_at', 'between', [$startDate, $endDate])
                ->field('
                    COUNT(*) as total_sessions,
                    SUM(CASE WHEN status = "active" THEN 1 ELSE 0 END) as active_sessions,
                    SUM(CASE WHEN status = "closed" THEN 1 ELSE 0 END) as closed_sessions,
                    AVG(CASE WHEN closed_at IS NOT NULL 
                        THEN TIMESTAMPDIFF(MINUTE, created_at, closed_at) 
                        ELSE NULL END) as avg_session_duration
                ')
                ->find();

            // 消息统计
            $messageStats = \think\facade\Db::name('customer_messages cm')
                ->join('customer_sessions cs', 'cm.session_id = cs.session_id')
                ->where('cs.agent_id', $agentId)
                ->where('cm.sender_type', 'agent')
                ->whereTime('cm.created_at', 'between', [$startDate, $endDate])
                ->field('
                    COUNT(*) as total_messages,
                    COUNT(DISTINCT cm.session_id) as sessions_with_messages
                ')
                ->find();

            // 客户统计
            $customerStats = \think\facade\Db::name('users')
                ->where('agent_id', $agentId)
                ->field('
                    COUNT(*) as total_customers,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_customers
                ')
                ->find();

            // 今日统计
            $todayStats = \think\facade\Db::name('customer_sessions')
                ->where('agent_id', $agentId)
                ->whereTime('created_at', 'today')
                ->field('
                    COUNT(*) as today_sessions,
                    SUM(unread_count_agent) as unread_messages
                ')
                ->find();

            $statistics = [
                'session_stats' => $sessionStats,
                'message_stats' => $messageStats,
                'customer_stats' => $customerStats,
                'today_stats' => $todayStats,
                'date_range' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ]
            ];

            return $this->success($statistics, '获取成功');

        } catch (\Exception $e) {
            return $this->error('获取统计失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新在线状态
     */
    public function updateOnlineStatus()
    {
        $status = $this->request->param('status', 'online');
        
        if (!in_array($status, ['online', 'offline', 'busy'])) {
            return $this->error('状态值错误');
        }

        $this->customerService->updateOnlineStatus(
            $this->getAgentId(), 
            'agent', 
            $status
        );

        return $this->success([], '状态更新成功');
    }

    /**
     * 验证会话访问权限
     */
    private function validateSessionAccess(string $sessionId): bool
    {
        $session = \think\facade\Db::name('customer_sessions')
            ->where('session_id', $sessionId)
            ->where('agent_id', $this->getAgentId())
            ->find();

        return !empty($session);
    }

    /**
     * 获取代理ID
     */
    private function getAgentId(): int
    {
        // 从JWT token或session中获取代理ID
        return 1; // 临时返回，实际应该从认证中获取
    }
}
