<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Db;
use think\facade\Cache;

/**
 * 管理员服务类
 */
class AdminService
{
    /**
     * 管理员登录
     */
    public function login(string $username, string $password): array
    {
        try {
            // 查找管理员
            $admin = Db::name('admins')
                ->where('username', $username)
                ->find();

            if (!$admin) {
                throw new \Exception('管理员不存在');
            }

            if ($admin['status'] != 1) {
                throw new \Exception('账户已被禁用');
            }

            // 验证密码
            if (!password_verify($password, $admin['password'])) {
                throw new \Exception('密码错误');
            }

            // 更新登录信息
            Db::name('admins')->where('id', $admin['id'])->update([
                'last_login_time' => date('Y-m-d H:i:s'),
                'last_login_ip' => request()->ip(),
                'login_count' => Db::raw('login_count + 1')
            ]);

            // 记录登录日志
            $this->recordLoginLog($admin['id'], 'login', '管理员登录');

            return [
                'code' => 1,
                'msg' => '登录成功',
                'data' => [
                    'admin' => [
                        'id' => $admin['id'],
                        'username' => $admin['username'],
                        'role' => $admin['role'],
                        'permissions' => json_decode($admin['permissions'] ?? '[]', true)
                    ]
                ]
            ];

        } catch (\Exception $e) {
            return [
                'code' => 0,
                'msg' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 根据ID获取管理员信息
     */
    public function getAdminById(int $adminId): ?array
    {
        $cacheKey = 'admin:' . $adminId;
        
        $admin = Cache::get($cacheKey);
        if (!$admin) {
            $admin = Db::name('admins')->where('id', $adminId)->find();
            if ($admin) {
                Cache::set($cacheKey, $admin, 300);
            }
        }
        
        return $admin;
    }

    /**
     * 获取系统统计数据
     */
    public function getSystemStats(): array
    {
        $cacheKey = 'system_stats';
        $stats = Cache::get($cacheKey);
        
        if (!$stats) {
            // 用户统计
            $totalUsers = Db::name('users')->count();
            $todayUsers = Db::name('users')
                ->whereTime('created_at', 'today')
                ->count();
            $activeUsers = Db::name('users')
                ->where('last_login_time', '>=', date('Y-m-d H:i:s', time() - 86400))
                ->count();

            // 交易统计
            $totalOrders = Db::name('leverage_orders')->count();
            $todayOrders = Db::name('leverage_orders')
                ->whereTime('created_at', 'today')
                ->count();
            $openOrders = Db::name('leverage_orders')
                ->where('status', 'open')
                ->count();

            // 资产统计
            $totalAssets = Db::name('user_assets')
                ->where('coin_symbol', 'USDT')
                ->sum('total');
            $frozenAssets = Db::name('user_assets')
                ->where('coin_symbol', 'USDT')
                ->sum('frozen');

            // 财务统计
            $todayDeposits = Db::name('financial_records')
                ->where('type', 'deposit')
                ->whereTime('created_at', 'today')
                ->sum('amount');
            $todayWithdraws = Db::name('financial_records')
                ->where('type', 'withdraw')
                ->whereTime('created_at', 'today')
                ->sum('amount');

            $stats = [
                'users' => [
                    'total' => $totalUsers,
                    'today' => $todayUsers,
                    'active' => $activeUsers
                ],
                'orders' => [
                    'total' => $totalOrders,
                    'today' => $todayOrders,
                    'open' => $openOrders
                ],
                'assets' => [
                    'total' => $totalAssets,
                    'frozen' => $frozenAssets,
                    'available' => $totalAssets - $frozenAssets
                ],
                'finance' => [
                    'today_deposits' => $todayDeposits,
                    'today_withdraws' => $todayWithdraws,
                    'net_flow' => $todayDeposits - $todayWithdraws
                ]
            ];

            Cache::set($cacheKey, $stats, 300); // 缓存5分钟
        }

        return $stats;
    }

    /**
     * 获取用户列表
     */
    public function getUserList(array $params = []): array
    {
        $query = Db::name('users');

        // 用户类型筛选
        if (!empty($params['user_type'])) {
            $query->where('user_type', $params['user_type']);
        }

        // 状态筛选
        if (isset($params['status']) && $params['status'] !== '') {
            $query->where('status', $params['status']);
        }

        // 关键词搜索
        if (!empty($params['keyword'])) {
            $query->where(function($q) use ($params) {
                $q->whereOr('username', 'like', '%' . $params['keyword'] . '%')
                  ->whereOr('email', 'like', '%' . $params['keyword'] . '%')
                  ->whereOr('phone', 'like', '%' . $params['keyword'] . '%');
            });
        }

        // 分页
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 20;
        $offset = ($page - 1) * $limit;

        $total = $query->count();
        $users = $query->order('id desc')
            ->limit($offset, $limit)
            ->select()
            ->toArray();

        // 获取用户资产信息
        foreach ($users as &$user) {
            $assets = Db::name('user_assets')
                ->where('user_id', $user['id'])
                ->where('coin_symbol', 'USDT')
                ->find();
            
            $user['balance'] = $assets ? $assets['available'] : 0;
            $user['frozen'] = $assets ? $assets['frozen'] : 0;
            $user['total_assets'] = $assets ? $assets['total'] : 0;
        }

        return [
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'users' => $users
        ];
    }

    /**
     * 调整用户余额
     */
    public function adjustUserBalance(array $data, int $adminId): array
    {
        try {
            Db::startTrans();

            // 验证用户
            $user = Db::name('users')->where('id', $data['user_id'])->find();
            if (!$user) {
                throw new \Exception('用户不存在');
            }

            // 获取用户资产
            $userAsset = Db::name('user_assets')
                ->where('user_id', $data['user_id'])
                ->where('coin_symbol', $data['coin_symbol'])
                ->find();

            if (!$userAsset) {
                // 创建资产记录
                Db::name('user_assets')->insert([
                    'user_id' => $data['user_id'],
                    'coin_symbol' => $data['coin_symbol'],
                    'available' => 0,
                    'frozen' => 0,
                    'total' => 0,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
                $userAsset = ['available' => 0, 'frozen' => 0, 'total' => 0];
            }

            // 计算新余额
            $newBalance = $userAsset['available'] + $data['amount'];
            if ($newBalance < 0) {
                throw new \Exception('余额不足，无法扣减');
            }

            // 更新资产
            Db::name('user_assets')
                ->where('user_id', $data['user_id'])
                ->where('coin_symbol', $data['coin_symbol'])
                ->update([
                    'available' => $newBalance,
                    'total' => $newBalance + $userAsset['frozen'],
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            // 记录财务流水
            Db::name('financial_records')->insert([
                'user_id' => $data['user_id'],
                'coin_symbol' => $data['coin_symbol'],
                'amount' => $data['amount'],
                'type' => 'admin_adjust',
                'remark' => $data['remark'] ?? '管理员调整余额',
                'created_at' => date('Y-m-d H:i:s')
            ]);

            // 记录管理员操作日志
            Db::name('admin_log')->insert([
                'admin_id' => $adminId,
                'action' => 'adjust_balance',
                'data' => json_encode($data, JSON_UNESCAPED_UNICODE),
                'ip' => request()->ip(),
                'created_at' => date('Y-m-d H:i:s')
            ]);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '余额调整成功',
                'data' => [
                    'old_balance' => $userAsset['available'],
                    'new_balance' => $newBalance,
                    'change_amount' => $data['amount']
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            return [
                'code' => 0,
                'msg' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 控制订单输赢
     */
    public function controlOrder(int $orderId, string $controlType, int $adminId): array
    {
        try {
            Db::startTrans();

            // 获取订单信息
            $order = Db::name('leverage_orders')
                ->where('id', $orderId)
                ->where('status', 'open')
                ->find();

            if (!$order) {
                throw new \Exception('订单不存在或已平仓');
            }

            $currentPrice = $this->getCurrentPrice($order['symbol']);
            $profitLoss = 0;

            // 根据控制类型计算盈亏
            switch ($controlType) {
                case 'win':
                    // 强制盈利
                    $profitLoss = $order['margin'] * 0.1; // 盈利10%
                    break;
                case 'lose':
                    // 强制亏损
                    $profitLoss = -$order['margin'] * 0.1; // 亏损10%
                    break;
                case 'liquidate':
                    // 强制平仓
                    $profitLoss = -$order['margin'] * 0.95; // 亏损95%
                    break;
                default:
                    throw new \Exception('无效的控制类型');
            }

            // 更新订单状态
            Db::name('leverage_orders')->where('id', $orderId)->update([
                'close_price' => $currentPrice,
                'profit_loss' => $profitLoss,
                'status' => $controlType === 'liquidate' ? 'liquidated' : 'closed',
                'close_type' => 'admin_control',
                'closed_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // 结算资产
            $finalAmount = $order['margin'] + $profitLoss;
            
            // 解冻保证金
            Db::name('user_assets')
                ->where('user_id', $order['user_id'])
                ->where('coin_symbol', 'USDT')
                ->dec('frozen', $order['margin']);

            // 添加最终金额
            if ($finalAmount > 0) {
                Db::name('user_assets')
                    ->where('user_id', $order['user_id'])
                    ->where('coin_symbol', 'USDT')
                    ->inc('available', $finalAmount);
            }

            // 记录财务流水
            Db::name('financial_records')->insert([
                'user_id' => $order['user_id'],
                'coin_symbol' => 'USDT',
                'amount' => $profitLoss,
                'type' => 'admin_control',
                'remark' => '管理员控制订单: ' . $controlType,
                'order_id' => $orderId,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            // 记录管理员操作日志
            Db::name('admin_log')->insert([
                'admin_id' => $adminId,
                'action' => 'control_order',
                'data' => json_encode([
                    'order_id' => $orderId,
                    'control_type' => $controlType,
                    'profit_loss' => $profitLoss
                ], JSON_UNESCAPED_UNICODE),
                'ip' => request()->ip(),
                'created_at' => date('Y-m-d H:i:s')
            ]);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '订单控制成功',
                'data' => [
                    'order_id' => $orderId,
                    'control_type' => $controlType,
                    'profit_loss' => $profitLoss,
                    'final_amount' => $finalAmount
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            return [
                'code' => 0,
                'msg' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 获取当前价格
     */
    private function getCurrentPrice(string $symbol): float
    {
        $cacheKey = 'price:' . $symbol;
        $price = Cache::get($cacheKey);
        
        if (!$price) {
            $priceData = Db::name('market_prices')
                ->where('symbol', $symbol)
                ->order('id desc')
                ->find();
            
            $price = $priceData ? $priceData['price'] : 50000;
            Cache::set($cacheKey, $price, 10);
        }
        
        return (float)$price;
    }

    /**
     * 记录登录日志
     */
    private function recordLoginLog(int $adminId, string $action, string $remark): void
    {
        Db::name('admin_login_log')->insert([
            'admin_id' => $adminId,
            'action' => $action,
            'ip' => request()->ip(),
            'user_agent' => request()->header('user-agent'),
            'remark' => $remark,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
}
