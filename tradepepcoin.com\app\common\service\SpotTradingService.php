<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\SpotOrder;
use app\common\model\TradingPair;
use app\common\model\UserAsset;
use app\common\model\Trade;
use app\common\model\FinancialRecord;
use think\facade\Db;
use think\facade\Log;

/**
 * 现货交易服务类
 */
class SpotTradingService
{
    protected $klineService;

    public function __construct()
    {
        $this->klineService = new KlineService();
    }

    /**
     * 创建现货订单
     */
    public function createOrder(array $orderData): array
    {
        try {
            Db::startTrans();

            // 验证交易对
            $tradingPair = TradingPair::getBySymbol($orderData['symbol']);
            if (!$tradingPair || !$tradingPair->isTradeEnabled()) {
                throw new \Exception('交易对不存在或已禁用');
            }

            // 验证订单数据
            $validation = $this->validateOrderData($orderData, $tradingPair);
            if (!$validation['valid']) {
                throw new \Exception($validation['message']);
            }

            // 检查用户资产
            $assetCheck = $this->checkUserAssets($orderData, $tradingPair);
            if (!$assetCheck['valid']) {
                throw new \Exception($assetCheck['message']);
            }

            // 创建订单
            $order = $this->createSpotOrder($orderData, $tradingPair);

            // 冻结用户资产
            $this->freezeUserAssets($order, $tradingPair);

            // 尝试撮合
            if ($order->order_type == SpotOrder::ORDER_TYPE_LIMIT) {
                $this->matchOrder($order, $tradingPair);
            } else {
                // 市价单立即成交
                $this->executeMarketOrder($order, $tradingPair);
            }

            Db::commit();

            return [
                'code' => 1,
                'msg' => '订单创建成功',
                'data' => [
                    'order_id' => $order->order_id,
                    'symbol' => $order->symbol,
                    'type' => $order->type,
                    'price' => $order->price,
                    'amount' => $order->amount,
                    'status' => $order->status
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('创建现货订单失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 验证订单数据
     */
    private function validateOrderData(array $data, TradingPair $tradingPair): array
    {
        // 验证必要字段
        $required = ['user_id', 'symbol', 'type', 'order_type', 'amount'];
        foreach ($required as $field) {
            if (!isset($data[$field]) || $data[$field] === '') {
                return ['valid' => false, 'message' => "字段 {$field} 不能为空"];
            }
        }

        // 验证订单类型
        if (!in_array($data['type'], [SpotOrder::TYPE_BUY, SpotOrder::TYPE_SELL])) {
            return ['valid' => false, 'message' => '无效的订单类型'];
        }

        // 验证订单方式
        if (!in_array($data['order_type'], [SpotOrder::ORDER_TYPE_LIMIT, SpotOrder::ORDER_TYPE_MARKET])) {
            return ['valid' => false, 'message' => '无效的订单方式'];
        }

        // 限价单必须有价格
        if ($data['order_type'] == SpotOrder::ORDER_TYPE_LIMIT && (!isset($data['price']) || $data['price'] <= 0)) {
            return ['valid' => false, 'message' => '限价单必须设置价格'];
        }

        // 验证数量
        $amountValidation = $tradingPair->validateAmount($data['amount']);
        if (!$amountValidation['valid']) {
            return $amountValidation;
        }

        // 验证金额（限价单）
        if ($data['order_type'] == SpotOrder::ORDER_TYPE_LIMIT) {
            $total = $data['amount'] * $data['price'];
            $totalValidation = $tradingPair->validateTotal($total);
            if (!$totalValidation['valid']) {
                return $totalValidation;
            }
        }

        return ['valid' => true];
    }

    /**
     * 检查用户资产
     */
    private function checkUserAssets(array $data, TradingPair $tradingPair): array
    {
        $userId = $data['user_id'];
        
        if ($data['type'] == SpotOrder::TYPE_BUY) {
            // 买单：检查USDT余额
            $asset = UserAsset::where('user_id', $userId)
                             ->where('coin_symbol', 'USDT')
                             ->find();
            
            if (!$asset) {
                return ['valid' => false, 'message' => 'USDT资产不存在'];
            }

            $needAmount = $data['order_type'] == SpotOrder::ORDER_TYPE_LIMIT 
                        ? $data['amount'] * $data['price']
                        : $data['amount']; // 市价单按数量计算

            if ($asset->available < $needAmount) {
                return ['valid' => false, 'message' => 'USDT余额不足'];
            }
        } else {
            // 卖单：检查基础币种余额
            $asset = UserAsset::where('user_id', $userId)
                             ->where('coin_symbol', $tradingPair->base_coin)
                             ->find();
            
            if (!$asset) {
                return ['valid' => false, 'message' => $tradingPair->base_coin . '资产不存在'];
            }

            if ($asset->available < $data['amount']) {
                return ['valid' => false, 'message' => $tradingPair->base_coin . '余额不足'];
            }
        }

        return ['valid' => true];
    }

    /**
     * 创建现货订单
     */
    private function createSpotOrder(array $data, TradingPair $tradingPair): SpotOrder
    {
        $orderData = [
            'user_id' => $data['user_id'],
            'symbol' => $data['symbol'],
            'order_id' => SpotOrder::generateOrderId(),
            'type' => $data['type'],
            'order_type' => $data['order_type'],
            'amount' => $tradingPair->formatAmount($data['amount']),
            'status' => SpotOrder::STATUS_PENDING,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        if ($data['order_type'] == SpotOrder::ORDER_TYPE_LIMIT) {
            $orderData['price'] = $tradingPair->formatPrice($data['price']);
            $orderData['total'] = $orderData['amount'] * $orderData['price'];
        } else {
            // 市价单使用当前市场价格
            $currentPrice = $tradingPair->getLatestPrice();
            $orderData['price'] = $currentPrice;
            $orderData['total'] = $orderData['amount'] * $currentPrice;
        }

        return SpotOrder::create($orderData);
    }

    /**
     * 冻结用户资产
     */
    private function freezeUserAssets(SpotOrder $order, TradingPair $tradingPair): void
    {
        if ($order->type == SpotOrder::TYPE_BUY) {
            // 买单：冻结USDT
            $asset = UserAsset::where('user_id', $order->user_id)
                             ->where('coin_symbol', 'USDT')
                             ->find();
            
            $freezeAmount = $order->total;
            $asset->available -= $freezeAmount;
            $asset->frozen += $freezeAmount;
            $asset->save();
        } else {
            // 卖单：冻结基础币种
            $asset = UserAsset::where('user_id', $order->user_id)
                             ->where('coin_symbol', $tradingPair->base_coin)
                             ->find();
            
            $freezeAmount = $order->amount;
            $asset->available -= $freezeAmount;
            $asset->frozen += $freezeAmount;
            $asset->save();
        }
    }

    /**
     * 撮合订单
     */
    private function matchOrder(SpotOrder $order, TradingPair $tradingPair): void
    {
        if ($order->type == SpotOrder::TYPE_BUY) {
            // 买单：寻找价格小于等于买价的卖单
            $matchOrders = SpotOrder::getMatchableSellOrders($order->symbol, $order->price, 10);
        } else {
            // 卖单：寻找价格大于等于卖价的买单
            $matchOrders = SpotOrder::getMatchableBuyOrders($order->symbol, $order->price, 10);
        }

        foreach ($matchOrders as $matchOrderData) {
            if ($order->filled_amount >= $order->amount) {
                break; // 订单已完全成交
            }

            $matchOrder = SpotOrder::find($matchOrderData['id']);
            if (!$matchOrder || $matchOrder->status == SpotOrder::STATUS_FILLED) {
                continue;
            }

            // 计算成交数量和价格
            $remainAmount = $order->amount - $order->filled_amount;
            $matchRemainAmount = $matchOrder->amount - $matchOrder->filled_amount;
            $tradeAmount = min($remainAmount, $matchRemainAmount);
            $tradePrice = $matchOrder->price; // 使用对手价

            // 执行成交
            $this->executeTrade($order, $matchOrder, $tradeAmount, $tradePrice, $tradingPair);
        }
    }

    /**
     * 执行市价单
     */
    private function executeMarketOrder(SpotOrder $order, TradingPair $tradingPair): void
    {
        // 市价单立即按当前价格成交
        $currentPrice = $tradingPair->getLatestPrice();
        
        $order->price = $currentPrice;
        $order->total = $order->amount * $currentPrice;
        $order->filled_amount = $order->amount;
        $order->filled_total = $order->total;
        $order->avg_price = $currentPrice;
        $order->status = SpotOrder::STATUS_FILLED;
        $order->filled_at = date('Y-m-d H:i:s');
        $order->save();

        // 更新用户资产
        $this->updateUserAssetsAfterTrade($order, $tradingPair);

        // 更新K线数据
        $this->klineService->updateKlineFromTrade([
            'symbol' => $order->symbol,
            'price' => $currentPrice,
            'volume' => $order->amount,
            'timestamp' => time()
        ]);
    }

    /**
     * 执行成交
     */
    private function executeTrade(SpotOrder $buyOrder, SpotOrder $sellOrder, float $amount, float $price, TradingPair $tradingPair): void
    {
        // 更新订单状态
        $buyOrder->partialFill($amount, $price);
        $sellOrder->partialFill($amount, $price);

        // 创建成交记录
        $this->createTradeRecord($buyOrder, $sellOrder, $amount, $price);

        // 更新用户资产
        $this->updateUserAssetsAfterTrade($buyOrder, $tradingPair, $amount, $price);
        $this->updateUserAssetsAfterTrade($sellOrder, $tradingPair, $amount, $price);

        // 更新K线数据
        $this->klineService->updateKlineFromTrade([
            'symbol' => $buyOrder->symbol,
            'price' => $price,
            'volume' => $amount,
            'timestamp' => time()
        ]);
    }

    /**
     * 创建成交记录
     */
    private function createTradeRecord(SpotOrder $buyOrder, SpotOrder $sellOrder, float $amount, float $price): void
    {
        Trade::create([
            'symbol' => $buyOrder->symbol,
            'trade_id' => 'T' . date('YmdHis') . mt_rand(1000, 9999),
            'buy_order_id' => $buyOrder->order_id,
            'sell_order_id' => $sellOrder->order_id,
            'buy_user_id' => $buyOrder->user_id,
            'sell_user_id' => $sellOrder->user_id,
            'price' => $price,
            'amount' => $amount,
            'total' => $amount * $price,
            'buy_fee' => 0, // 暂时不收手续费
            'sell_fee' => 0,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 更新用户资产（成交后）
     */
    private function updateUserAssetsAfterTrade(SpotOrder $order, TradingPair $tradingPair, float $amount = 0, float $price = 0): void
    {
        $tradeAmount = $amount ?: $order->filled_amount;
        $tradePrice = $price ?: $order->avg_price;
        $tradeTotal = $tradeAmount * $tradePrice;

        if ($order->type == SpotOrder::TYPE_BUY) {
            // 买单：减少冻结的USDT，增加基础币种
            $usdtAsset = UserAsset::where('user_id', $order->user_id)
                                 ->where('coin_symbol', 'USDT')
                                 ->find();
            $usdtAsset->frozen -= $tradeTotal;
            $usdtAsset->save();

            $baseAsset = UserAsset::where('user_id', $order->user_id)
                                 ->where('coin_symbol', $tradingPair->base_coin)
                                 ->find();
            if (!$baseAsset) {
                $baseAsset = UserAsset::create([
                    'user_id' => $order->user_id,
                    'coin_symbol' => $tradingPair->base_coin,
                    'available' => 0,
                    'frozen' => 0,
                    'total' => 0
                ]);
            }
            $baseAsset->available += $tradeAmount;
            $baseAsset->total += $tradeAmount;
            $baseAsset->save();
        } else {
            // 卖单：减少冻结的基础币种，增加USDT
            $baseAsset = UserAsset::where('user_id', $order->user_id)
                                 ->where('coin_symbol', $tradingPair->base_coin)
                                 ->find();
            $baseAsset->frozen -= $tradeAmount;
            $baseAsset->save();

            $usdtAsset = UserAsset::where('user_id', $order->user_id)
                                 ->where('coin_symbol', 'USDT')
                                 ->find();
            $usdtAsset->available += $tradeTotal;
            $usdtAsset->total += $tradeTotal;
            $usdtAsset->save();
        }
    }

    /**
     * 取消订单
     */
    public function cancelOrder(int $userId, string $orderId): array
    {
        try {
            $order = SpotOrder::where('user_id', $userId)
                             ->where('order_id', $orderId)
                             ->find();

            if (!$order) {
                return ['code' => 0, 'msg' => '订单不存在'];
            }

            if ($order->cancel()) {
                return ['code' => 1, 'msg' => '订单取消成功'];
            } else {
                return ['code' => 0, 'msg' => '订单无法取消'];
            }

        } catch (\Exception $e) {
            Log::error('取消订单失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '取消失败'];
        }
    }

    /**
     * 获取用户订单
     */
    public function getUserOrders(int $userId, string $symbol = '', string $status = 'active', int $page = 1, int $limit = 20): array
    {
        try {
            if ($status === 'active') {
                $orders = SpotOrder::getUserActiveOrders($userId, $symbol);
                return [
                    'code' => 1,
                    'data' => $orders
                ];
            } else {
                $result = SpotOrder::getUserHistoryOrders($userId, $symbol, $page, $limit);
                return [
                    'code' => 1,
                    'data' => $result['data'],
                    'total' => $result['total'],
                    'page' => $result['page'],
                    'limit' => $result['limit']
                ];
            }

        } catch (\Exception $e) {
            Log::error('获取用户订单失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 获取订单簿
     */
    public function getOrderBook(string $symbol, int $depth = 20): array
    {
        try {
            $orderBook = SpotOrder::getOrderBook($symbol, $depth);
            
            return [
                'code' => 1,
                'data' => [
                    'symbol' => $symbol,
                    'bids' => $orderBook['bids'],
                    'asks' => $orderBook['asks'],
                    'timestamp' => time()
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取订单簿失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }
}
