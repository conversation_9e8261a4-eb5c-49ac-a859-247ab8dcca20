<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单历史 - GVD交易平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }

        .header {
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }

        .nav-menu {
            display: flex;
            gap: 30px;
        }

        .nav-menu a {
            text-decoration: none;
            color: #666;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-menu a:hover,
        .nav-menu a.active {
            color: #667eea;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 30px;
            color: #333;
        }

        .filter-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .filter-tab {
            padding: 10px 20px;
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }

        .filter-tab:hover,
        .filter-tab.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .orders-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .orders-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .orders-header h3 {
            color: #333;
        }

        .search-box {
            display: flex;
            gap: 10px;
        }

        .search-input {
            padding: 8px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .search-btn {
            padding: 8px 15px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        .orders-table {
            width: 100%;
            border-collapse: collapse;
        }

        .orders-table th,
        .orders-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .orders-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .order-symbol {
            font-weight: 600;
            color: #333;
        }

        .order-side {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .side-buy {
            background: rgba(46, 204, 113, 0.2);
            color: #27ae60;
        }

        .side-sell {
            background: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
        }

        .order-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-pending {
            background: rgba(241, 196, 15, 0.2);
            color: #f39c12;
        }

        .status-filled {
            background: rgba(46, 204, 113, 0.2);
            color: #27ae60;
        }

        .status-cancelled {
            background: rgba(149, 165, 166, 0.2);
            color: #95a5a6;
        }

        .status-partial {
            background: rgba(52, 152, 219, 0.2);
            color: #3498db;
        }

        .order-actions {
            display: flex;
            gap: 5px;
        }

        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }

        .btn-cancel {
            background: #e74c3c;
            color: white;
        }

        .btn-view {
            background: #3498db;
            color: white;
        }

        .btn-cancel:hover {
            background: #c0392b;
        }

        .btn-view:hover {
            background: #2980b9;
        }

        .pagination {
            padding: 20px;
            display: flex;
            justify-content: center;
            gap: 10px;
        }

        .page-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .page-btn:hover,
        .page-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .loading {
            text-align: center;
            padding: 60px;
            color: #666;
        }

        .empty-state {
            text-align: center;
            padding: 60px;
            color: #666;
        }

        .empty-state img {
            width: 100px;
            opacity: 0.5;
            margin-bottom: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .orders-table {
                font-size: 14px;
            }
            
            .orders-table th,
            .orders-table td {
                padding: 10px 8px;
            }
            
            .search-box {
                flex-direction: column;
            }
            
            .orders-header {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">GVD交易平台</div>
            <nav class="nav-menu">
                <a href="/trade/index.html">交易</a>
                <a href="/ido/index.html">IDO</a>
                <a href="/user/dashboard.html">资产</a>
                <a href="/user/orders.html" class="active">订单</a>
            </nav>
            <div class="user-info">
                <span id="username">用户</span>
                <button class="btn btn-outline" onclick="logout()">退出</button>
            </div>
        </div>
    </div>

    <div class="container">
        <h1 class="page-title">订单历史</h1>
        
        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <div class="stat-value" id="totalOrders">0</div>
                <div class="stat-label">总订单数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="filledOrders">0</div>
                <div class="stat-label">已成交</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="cancelledOrders">0</div>
                <div class="stat-label">已取消</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalVolume">$0</div>
                <div class="stat-label">总交易额</div>
            </div>
        </div>

        <div class="filter-tabs">
            <div class="filter-tab active" data-type="all" onclick="filterOrders('all')">全部订单</div>
            <div class="filter-tab" data-type="spot" onclick="filterOrders('spot')">现货订单</div>
            <div class="filter-tab" data-type="ido" onclick="filterOrders('ido')">IDO订单</div>
            <div class="filter-tab" data-status="pending" onclick="filterOrders('', 'pending')">待成交</div>
            <div class="filter-tab" data-status="filled" onclick="filterOrders('', 'filled')">已成交</div>
            <div class="filter-tab" data-status="cancelled" onclick="filterOrders('', 'cancelled')">已取消</div>
        </div>

        <div class="orders-container">
            <div class="orders-header">
                <h3>订单列表</h3>
                <div class="search-box">
                    <input type="text" class="search-input" id="searchInput" placeholder="搜索交易对...">
                    <button class="search-btn" onclick="searchOrders()">搜索</button>
                </div>
            </div>
            
            <div class="orders-content" id="ordersContent">
                <div class="loading">加载中...</div>
            </div>
            
            <div class="pagination" id="pagination" style="display: none;">
                <!-- 分页按钮将在这里动态生成 -->
            </div>
        </div>
    </div>

    <script>
        class OrdersPage {
            constructor() {
                this.token = localStorage.getItem('token');
                this.userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
                this.currentPage = 1;
                this.currentType = 'all';
                this.currentStatus = '';
                this.searchKeyword = '';
                
                if (!this.token) {
                    window.location.href = '/auth/login.html';
                    return;
                }
                
                this.init();
            }
            
            async init() {
                this.updateUserInfo();
                await this.loadOrderStats();
                await this.loadOrders();
                this.bindEvents();
            }
            
            bindEvents() {
                document.getElementById('searchInput').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.searchOrders();
                    }
                });
            }
            
            updateUserInfo() {
                if (this.userInfo.username) {
                    document.getElementById('username').textContent = this.userInfo.username;
                }
            }
            
            async loadOrderStats() {
                try {
                    // 加载现货订单统计
                    const spotResponse = await this.apiRequest('/api/spot/user-trades');
                    // 加载IDO订单统计
                    const idoResponse = await this.apiRequest('/api/ido/my-stats');
                    
                    let totalOrders = 0;
                    let filledOrders = 0;
                    let cancelledOrders = 0;
                    let totalVolume = 0;
                    
                    if (spotResponse.code === 1) {
                        // 处理现货统计
                        totalOrders += spotResponse.data.total || 0;
                        totalVolume += parseFloat(spotResponse.data.total_volume || 0);
                    }
                    
                    if (idoResponse.code === 1) {
                        totalOrders += idoResponse.data.total_orders || 0;
                        filledOrders += idoResponse.data.success_orders || 0;
                        totalVolume += parseFloat(idoResponse.data.total_amount || 0);
                    }
                    
                    document.getElementById('totalOrders').textContent = totalOrders;
                    document.getElementById('filledOrders').textContent = filledOrders;
                    document.getElementById('cancelledOrders').textContent = cancelledOrders;
                    document.getElementById('totalVolume').textContent = `$${this.formatNumber(totalVolume)}`;
                    
                } catch (error) {
                    console.error('加载订单统计失败:', error);
                }
            }
            
            async loadOrders() {
                try {
                    const params = new URLSearchParams();
                    params.append('page', this.currentPage.toString());
                    params.append('limit', '20');
                    
                    if (this.currentStatus) {
                        params.append('status', this.currentStatus);
                    }
                    
                    if (this.searchKeyword) {
                        params.append('symbol', this.searchKeyword);
                    }
                    
                    let orders = [];
                    
                    if (this.currentType === 'all' || this.currentType === 'spot') {
                        const spotResponse = await this.apiRequest(`/api/spot/orders?${params.toString()}`);
                        if (spotResponse.code === 1) {
                            orders = orders.concat(spotResponse.data.orders.map(order => ({
                                ...order,
                                type: 'spot'
                            })));
                        }
                    }
                    
                    if (this.currentType === 'all' || this.currentType === 'ido') {
                        const idoResponse = await this.apiRequest(`/api/ido/my-orders?${params.toString()}`);
                        if (idoResponse.code === 1) {
                            orders = orders.concat(idoResponse.data.list.map(order => ({
                                ...order,
                                type: 'ido'
                            })));
                        }
                    }
                    
                    this.renderOrders(orders);
                    
                } catch (error) {
                    console.error('加载订单失败:', error);
                    document.getElementById('ordersContent').innerHTML = '<div class="empty-state">加载失败</div>';
                }
            }
            
            renderOrders(orders) {
                const ordersContent = document.getElementById('ordersContent');
                
                if (orders.length === 0) {
                    ordersContent.innerHTML = `
                        <div class="empty-state">
                            <p>暂无订单记录</p>
                        </div>
                    `;
                    return;
                }
                
                const tableHTML = `
                    <table class="orders-table">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>类型</th>
                                <th>交易对</th>
                                <th>方向</th>
                                <th>价格</th>
                                <th>数量</th>
                                <th>成交额</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${orders.map(order => this.renderOrderRow(order)).join('')}
                        </tbody>
                    </table>
                `;
                
                ordersContent.innerHTML = tableHTML;
            }
            
            renderOrderRow(order) {
                const time = new Date(order.created_at).toLocaleString();
                const type = order.type === 'spot' ? '现货' : 'IDO';
                const symbol = order.symbol || (order.project_symbol ? `${order.project_symbol}/USDT` : '-');
                const side = this.getOrderSide(order);
                const price = order.price || order.token_price || '-';
                const amount = order.amount || order.token_amount || '-';
                const total = order.total || (order.amount * (order.price || order.token_price)) || '-';
                const status = this.getOrderStatus(order);
                const actions = this.getOrderActions(order);
                
                return `
                    <tr>
                        <td>${time}</td>
                        <td>${type}</td>
                        <td class="order-symbol">${symbol}</td>
                        <td>${side}</td>
                        <td>${price}</td>
                        <td>${amount}</td>
                        <td>${total}</td>
                        <td>${status}</td>
                        <td>${actions}</td>
                    </tr>
                `;
            }
            
            getOrderSide(order) {
                if (order.type === 'ido') {
                    return '<span class="order-side side-buy">认购</span>';
                }
                
                const sideClass = order.side === 'buy' ? 'side-buy' : 'side-sell';
                const sideText = order.side === 'buy' ? '买入' : '卖出';
                return `<span class="order-side ${sideClass}">${sideText}</span>`;
            }
            
            getOrderStatus(order) {
                let statusClass = '';
                let statusText = '';
                
                if (order.type === 'ido') {
                    switch (order.status) {
                        case 1:
                            statusClass = 'status-pending';
                            statusText = '待处理';
                            break;
                        case 2:
                            statusClass = 'status-filled';
                            statusText = '成功';
                            break;
                        case 3:
                            statusClass = 'status-cancelled';
                            statusText = '失败';
                            break;
                        default:
                            statusClass = 'status-pending';
                            statusText = '未知';
                    }
                } else {
                    switch (order.status) {
                        case 'pending':
                            statusClass = 'status-pending';
                            statusText = '待成交';
                            break;
                        case 'filled':
                            statusClass = 'status-filled';
                            statusText = '已成交';
                            break;
                        case 'cancelled':
                            statusClass = 'status-cancelled';
                            statusText = '已取消';
                            break;
                        case 'partial':
                            statusClass = 'status-partial';
                            statusText = '部分成交';
                            break;
                        default:
                            statusClass = 'status-pending';
                            statusText = '未知';
                    }
                }
                
                return `<span class="order-status ${statusClass}">${statusText}</span>`;
            }
            
            getOrderActions(order) {
                const actions = [];
                
                if (order.type === 'spot' && order.status === 'pending') {
                    actions.push(`<button class="action-btn btn-cancel" onclick="cancelOrder('${order.order_id}')">取消</button>`);
                }
                
                actions.push(`<button class="action-btn btn-view" onclick="viewOrder('${order.order_id}', '${order.type}')">详情</button>`);
                
                return `<div class="order-actions">${actions.join('')}</div>`;
            }
            
            formatNumber(num) {
                if (num >= 1000000) {
                    return (num / 1000000).toFixed(1) + 'M';
                } else if (num >= 1000) {
                    return (num / 1000).toFixed(1) + 'K';
                }
                return num.toFixed(2);
            }
            
            async apiRequest(url, options = {}) {
                const defaultOptions = {
                    headers: {
                        'Authorization': `Bearer ${this.token}`,
                        'Content-Type': 'application/json'
                    }
                };
                
                const response = await fetch(url, { ...defaultOptions, ...options });
                const result = await response.json();
                
                if (response.status === 401) {
                    localStorage.removeItem('token');
                    localStorage.removeItem('user_info');
                    window.location.href = '/auth/login.html';
                    return;
                }
                
                return result;
            }
        }
        
        function filterOrders(type = '', status = '') {
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            ordersPage.currentType = type || ordersPage.currentType;
            ordersPage.currentStatus = status;
            ordersPage.currentPage = 1;
            ordersPage.loadOrders();
        }
        
        function searchOrders() {
            ordersPage.searchKeyword = document.getElementById('searchInput').value.trim();
            ordersPage.currentPage = 1;
            ordersPage.loadOrders();
        }
        
        async function cancelOrder(orderId) {
            if (!confirm('确定要取消这个订单吗？')) {
                return;
            }
            
            try {
                const response = await ordersPage.apiRequest('/api/spot/cancel', {
                    method: 'POST',
                    body: JSON.stringify({ order_id: orderId })
                });
                
                if (response.code === 1) {
                    alert('订单取消成功');
                    ordersPage.loadOrders();
                } else {
                    alert(response.msg || '取消失败');
                }
            } catch (error) {
                console.error('取消订单失败:', error);
                alert('网络错误，请稍后重试');
            }
        }
        
        function viewOrder(orderId, type) {
            // 这里可以打开订单详情弹窗或跳转到详情页面
            alert(`查看${type === 'spot' ? '现货' : 'IDO'}订单详情: ${orderId}`);
        }
        
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('token');
                localStorage.removeItem('user_info');
                window.location.href = '/auth/login.html';
            }
        }
        
        let ordersPage;
        document.addEventListener('DOMContentLoaded', () => {
            ordersPage = new OrdersPage();
        });
    </script>
</body>
</html>
