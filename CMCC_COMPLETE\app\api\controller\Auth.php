<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\common\service\UserService;
use app\common\model\User;
use think\facade\Request;
use think\facade\Validate;

/**
 * 用户认证控制器
 */
class Auth extends BaseController
{
    protected $userService;

    public function __construct(\think\App $app, UserService $userService)
    {
        parent::__construct($app);
        $this->userService = $userService;
    }

    /**
     * 发送验证码
     */
    public function sendCode()
    {
        $data = $this->getParams(['email']);

        if (!$data['email']) {
            return $this->error('请输入邮箱地址');
        }

        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            return $this->error('邮箱格式不正确');
        }

        // 检查邮箱是否已注册
        $exists = \app\common\model\User::where('email', $data['email'])->find();
        if ($exists) {
            return $this->error('邮箱已被注册');
        }

        $result = $this->userService->sendVerifyCode($data['email']);

        if ($result['code'] == 1) {
            return $this->success([], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 用户注册（前端注册，正式用户）
     */
    public function register()
    {
        $data = $this->getParams([
            'username', 'password', 'confirm_password',
            'email', 'code', 'invite_code'
        ]);

        // 验证参数
        $validate = Validate::rule([
            'username' => 'require|length:3,20|alphaNum',
            'password' => 'require|length:6,20',
            'confirm_password' => 'require|confirm:password',
            'email' => 'require|email',
            'code' => 'require|length:6'
        ])->message([
            'username.require' => '用户名不能为空',
            'username.length' => '用户名长度为3-20位',
            'username.alphaNum' => '用户名只能包含字母和数字',
            'password.require' => '密码不能为空',
            'password.length' => '密码长度为6-20位',
            'confirm_password.require' => '确认密码不能为空',
            'confirm_password.confirm' => '两次密码输入不一致',
            'email.require' => '邮箱不能为空',
            'email.email' => '邮箱格式不正确',
            'code.require' => '验证码不能为空',
            'code.length' => '验证码长度为6位'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        // 检查用户名是否已存在
        $usernameExists = \app\common\model\User::where('username', $data['username'])->find();
        if ($usernameExists) {
            return $this->error('用户名已存在');
        }

        // 检查邮箱是否已注册
        $emailExists = \app\common\model\User::where('email', $data['email'])->find();
        if ($emailExists) {
            return $this->error('邮箱已被注册');
        }

        // 处理邀请码
        $inviterId = 0;
        if (!empty($data['invite_code'])) {
            $inviterId = $this->getInviterByCode($data['invite_code']);
            if (!$inviterId) {
                return $this->error('邀请码无效');
            }
        }

        // 注册用户（前端注册为正式用户）
        $registerData = [
            'username' => $data['username'],
            'password' => $data['password'],
            'email' => $data['email'],
            'code' => $data['code'],
            'inviter_id' => $inviterId,
            'register_ip' => $this->getClientIp()
        ];

        $result = $this->userService->frontendRegister($registerData);

        if ($result['code'] == 1) {
            return $this->success($result['data'], '注册成功');
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 用户登录
     */
    public function login()
    {
        $data = $this->getParams(['username', 'password']);

        // 验证参数
        $validate = Validate::rule([
            'username' => 'require',
            'password' => 'require'
        ])->message([
            'username.require' => '用户名不能为空',
            'password.require' => '密码不能为空'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        $result = $this->userService->login($data['username'], $data['password']);
        
        if ($result['code'] == 1) {
            return $this->success($result['data'], '登录成功');
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 用户登出
     */
    public function logout()
    {
        // JWT token无状态，客户端删除即可
        return $this->success([], '登出成功');
    }

    /**
     * 发送短信验证码
     */
    public function sendSms()
    {
        $phone = $this->request->param('phone');
        $type = $this->request->param('type', 'register'); // register/login/reset

        if (!$phone || !preg_match('/^1[3-9]\d{9}$/', $phone)) {
            return $this->error('手机号格式不正确');
        }

        // 检查发送频率
        $cacheKey = 'sms_limit:' . $phone;
        if (cache($cacheKey)) {
            return $this->error('发送过于频繁，请稍后再试');
        }

        // 生成验证码
        $code = sprintf('%06d', mt_rand(0, 999999));
        
        // 保存验证码
        cache('sms_code:' . $phone, $code, 300); // 5分钟有效
        cache($cacheKey, 1, 60); // 1分钟内不能重复发送

        // TODO: 调用短信服务发送验证码
        // $smsService->send($phone, $code, $type);

        return $this->success(['code' => $code], '验证码发送成功'); // 开发环境返回验证码
    }

    /**
     * 发送邮箱验证码
     */
    public function sendEmail()
    {
        $email = $this->request->param('email');
        $type = $this->request->param('type', 'register');

        if (!$email || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return $this->error('邮箱格式不正确');
        }

        // 检查发送频率
        $cacheKey = 'email_limit:' . $email;
        if (cache($cacheKey)) {
            return $this->error('发送过于频繁，请稍后再试');
        }

        // 生成验证码
        $code = sprintf('%06d', mt_rand(0, 999999));
        
        // 保存验证码
        cache('email_code:' . $email, $code, 300);
        cache($cacheKey, 1, 60);

        // TODO: 调用邮件服务发送验证码
        // $emailService->send($email, $code, $type);

        return $this->success(['code' => $code], '验证码发送成功');
    }

    /**
     * 获取图形验证码
     */
    public function captcha()
    {
        $captcha = new \think\captcha\Captcha();
        return $captcha->entry();
    }

    /**
     * 刷新Token
     */
    public function refreshToken()
    {
        $token = $this->request->header('Authorization');
        
        if (empty($token)) {
            return $this->error('Token不能为空', 401);
        }

        // 移除Bearer前缀
        if (strpos($token, 'Bearer ') === 0) {
            $token = substr($token, 7);
        }

        try {
            // 验证并刷新Token
            $newToken = $this->userService->refreshToken($token);
            
            return $this->success(['token' => $newToken], 'Token刷新成功');
        } catch (\Exception $e) {
            return $this->error('Token刷新失败', 401);
        }
    }

    /**
     * 忘记密码
     */
    public function forgotPassword()
    {
        $data = $this->getParams(['email', 'code']);

        // 验证邮箱验证码
        $savedCode = cache('email_code:' . $data['email']);
        if (!$savedCode || $savedCode != $data['code']) {
            return $this->error('验证码错误或已过期');
        }

        // 生成重置Token
        $resetToken = md5($data['email'] . time() . uniqid());
        cache('reset_token:' . $resetToken, $data['email'], 1800); // 30分钟有效

        // TODO: 发送重置密码邮件
        // $emailService->sendResetPassword($data['email'], $resetToken);

        return $this->success(['reset_token' => $resetToken], '重置链接已发送到您的邮箱');
    }

    /**
     * 重置密码
     */
    public function resetPassword()
    {
        $data = $this->getParams(['reset_token', 'password', 'confirm_password']);

        // 验证参数
        $validate = Validate::rule([
            'reset_token' => 'require',
            'password' => 'require|length:6,20',
            'confirm_password' => 'require|confirm:password'
        ])->message([
            'reset_token.require' => '重置令牌不能为空',
            'password.require' => '密码不能为空',
            'password.length' => '密码长度为6-20位',
            'confirm_password.require' => '确认密码不能为空',
            'confirm_password.confirm' => '两次密码输入不一致'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        // 验证重置Token
        $email = cache('reset_token:' . $data['reset_token']);
        if (!$email) {
            return $this->error('重置令牌无效或已过期');
        }

        // 重置密码
        $result = $this->userService->resetPassword($email, $data['password']);
        
        if ($result['code'] == 1) {
            // 删除重置Token
            cache('reset_token:' . $data['reset_token'], null);
            return $this->success([], '密码重置成功');
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 根据邀请码获取邀请人ID
     */
    private function getInviterByCode(string $inviteCode): int
    {
        $user = \think\facade\Db::name('users')
            ->where('invite_code', $inviteCode)
            ->where('status', 1)
            ->find();
        
        return $user ? $user['id'] : 0;
    }
}
