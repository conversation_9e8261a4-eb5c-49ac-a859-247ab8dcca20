{extend name="index/layout" /}

{block name="css"}
<link rel="stylesheet" href="/static/css/trading.css">
<style>
.trading-container {
    padding: 0;
    background: #1a1a1a;
    min-height: 100vh;
    color: #fff;
}

.trading-header {
    background: #2d2d2d;
    padding: 10px 20px;
    border-bottom: 1px solid #333;
}

.symbol-selector {
    display: flex;
    align-items: center;
    gap: 20px;
}

.symbol-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.symbol-name {
    font-size: 24px;
    font-weight: bold;
    color: #fff;
}

.symbol-price {
    font-size: 20px;
    font-weight: bold;
    color: #00d4aa;
}

.symbol-change {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
}

.symbol-change.up {
    background: #00d4aa;
    color: #fff;
}

.symbol-change.down {
    background: #ff6b6b;
    color: #fff;
}

.trading-main {
    display: grid;
    grid-template-columns: 1fr 300px;
    height: calc(100vh - 60px);
}

.chart-section {
    background: #1e1e1e;
    border-right: 1px solid #333;
}

.chart-header {
    background: #2d2d2d;
    padding: 10px 20px;
    border-bottom: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-tabs {
    display: flex;
    gap: 20px;
}

.chart-tab {
    padding: 8px 16px;
    background: transparent;
    border: none;
    color: #999;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s;
}

.chart-tab.active {
    background: #667eea;
    color: #fff;
}

.timeframe-buttons {
    display: flex;
    gap: 5px;
}

.timeframe-btn {
    padding: 6px 12px;
    background: #333;
    border: none;
    color: #999;
    cursor: pointer;
    border-radius: 4px;
    font-size: 12px;
    transition: all 0.3s;
}

.timeframe-btn.active {
    background: #667eea;
    color: #fff;
}

.chart-container {
    height: 500px;
    background: #1e1e1e;
}

.market-data {
    background: #1e1e1e;
    padding: 20px;
}

.market-tabs {
    display: flex;
    border-bottom: 1px solid #333;
    margin-bottom: 20px;
}

.market-tab {
    padding: 10px 20px;
    background: transparent;
    border: none;
    color: #999;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s;
}

.market-tab.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

.depth-chart {
    height: 200px;
    margin-bottom: 20px;
}

.order-book {
    margin-bottom: 20px;
}

.order-book-header {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    padding: 10px 0;
    border-bottom: 1px solid #333;
    font-size: 12px;
    color: #999;
}

.order-book-item {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    padding: 4px 0;
    font-size: 12px;
    cursor: pointer;
    transition: background 0.3s;
}

.order-book-item:hover {
    background: rgba(255,255,255,0.05);
}

.order-book-item.buy {
    color: #00d4aa;
}

.order-book-item.sell {
    color: #ff6b6b;
}

.recent-trades {
    margin-bottom: 20px;
}

.trade-item {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    padding: 4px 0;
    font-size: 12px;
}

.trade-item.buy {
    color: #00d4aa;
}

.trade-item.sell {
    color: #ff6b6b;
}

.trading-form {
    background: #2d2d2d;
    padding: 20px;
    border-top: 1px solid #333;
}

.form-tabs {
    display: flex;
    margin-bottom: 20px;
}

.form-tab {
    flex: 1;
    padding: 12px;
    background: #333;
    border: none;
    color: #999;
    cursor: pointer;
    transition: all 0.3s;
}

.form-tab.active.buy {
    background: #00d4aa;
    color: #fff;
}

.form-tab.active.sell {
    background: #ff6b6b;
    color: #fff;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-size: 12px;
    color: #999;
}

.form-control {
    width: 100%;
    padding: 10px;
    background: #1e1e1e;
    border: 1px solid #333;
    color: #fff;
    border-radius: 4px;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
}

.balance-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 12px;
    color: #999;
}

.submit-btn {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 4px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s;
}

.submit-btn.buy {
    background: #00d4aa;
    color: #fff;
}

.submit-btn.sell {
    background: #ff6b6b;
    color: #fff;
}

.submit-btn:hover {
    opacity: 0.9;
}

.submit-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.active-orders {
    max-height: 200px;
    overflow-y: auto;
}

.order-item {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr auto;
    padding: 8px 0;
    border-bottom: 1px solid #333;
    font-size: 12px;
    align-items: center;
}

.cancel-btn {
    padding: 4px 8px;
    background: #ff6b6b;
    border: none;
    color: #fff;
    border-radius: 4px;
    cursor: pointer;
    font-size: 10px;
}
</style>
{/block}

{block name="content"}
<div class="trading-container">
    <!-- 交易头部 -->
    <div class="trading-header">
        <div class="symbol-selector">
            <div class="symbol-info">
                <span class="symbol-name" id="current-symbol">{$symbol|default='BTC/USDT'}</span>
                <span class="symbol-price" id="current-price">$0.00</span>
                <span class="symbol-change up" id="price-change">+0.00%</span>
            </div>
            
            <select class="form-control" id="symbol-select" style="width: 200px;">
                {volist name="trading_pairs" id="pair"}
                <option value="{$pair.symbol}" {if condition="$pair.symbol == $symbol"}selected{/if}>
                    {$pair.symbol}
                </option>
                {/volist}
            </select>
        </div>
    </div>

    <!-- 交易主体 -->
    <div class="trading-main">
        <!-- 图表区域 -->
        <div class="chart-section">
            <div class="chart-header">
                <div class="chart-tabs">
                    <button class="chart-tab active" data-tab="kline">K线图</button>
                    <button class="chart-tab" data-tab="depth">深度图</button>
                </div>
                
                <div class="timeframe-buttons">
                    <button class="timeframe-btn" data-interval="1m">1分</button>
                    <button class="timeframe-btn" data-interval="5m">5分</button>
                    <button class="timeframe-btn active" data-interval="15m">15分</button>
                    <button class="timeframe-btn" data-interval="1h">1小时</button>
                    <button class="timeframe-btn" data-interval="4h">4小时</button>
                    <button class="timeframe-btn" data-interval="1d">1天</button>
                </div>
            </div>
            
            <div class="chart-container">
                <div id="tradingview-chart"></div>
            </div>
        </div>

        <!-- 市场数据和交易表单 -->
        <div class="market-data">
            <!-- 市场数据标签 -->
            <div class="market-tabs">
                <button class="market-tab active" data-tab="orderbook">订单簿</button>
                <button class="market-tab" data-tab="trades">最新成交</button>
            </div>

            <!-- 订单簿 -->
            <div class="order-book" id="orderbook-section">
                <div class="order-book-header">
                    <span>价格</span>
                    <span>数量</span>
                    <span>累计</span>
                </div>
                <div id="sell-orders"></div>
                <div id="buy-orders"></div>
            </div>

            <!-- 最新成交 -->
            <div class="recent-trades" id="trades-section" style="display: none;">
                <div class="order-book-header">
                    <span>价格</span>
                    <span>数量</span>
                    <span>时间</span>
                </div>
                <div id="recent-trades-list"></div>
            </div>

            <!-- 交易表单 -->
            {if condition="$user_id"}
            <div class="trading-form">
                <div class="form-tabs">
                    <button class="form-tab buy active" data-type="buy">买入</button>
                    <button class="form-tab sell" data-type="sell">卖出</button>
                </div>

                <form id="trading-form">
                    <input type="hidden" name="symbol" value="{$symbol}">
                    <input type="hidden" name="type" id="trade-type" value="buy">

                    <div class="balance-info">
                        <span>可用:</span>
                        <span id="available-balance">0.00 USDT</span>
                    </div>

                    <div class="form-group">
                        <label>价格</label>
                        <input type="number" class="form-control" name="price" id="trade-price" step="0.01" placeholder="0.00">
                    </div>

                    <div class="form-group">
                        <label>数量</label>
                        <input type="number" class="form-control" name="amount" id="trade-amount" step="0.001" placeholder="0.000">
                    </div>

                    <div class="form-group">
                        <label>总额</label>
                        <input type="number" class="form-control" id="trade-total" readonly placeholder="0.00">
                    </div>

                    <button type="submit" class="submit-btn buy" id="submit-btn">
                        买入 {$symbol|substr=0,3}
                    </button>
                </form>

                <!-- 当前委托 -->
                {if condition="$active_orders"}
                <div class="active-orders">
                    <h6 style="margin: 20px 0 10px 0; color: #999;">当前委托</h6>
                    <div id="active-orders-list">
                        {volist name="active_orders" id="order"}
                        <div class="order-item" data-id="{$order.id}">
                            <span class="{$order.type == 'buy' ? 'buy' : 'sell'}">{$order.type == 'buy' ? '买入' : '卖出'}</span>
                            <span>{$order.price}</span>
                            <span>{$order.amount}</span>
                            <button class="cancel-btn" onclick="cancelOrder({$order.id})">取消</button>
                        </div>
                        {/volist}
                    </div>
                </div>
                {/if}
            </div>
            {else}
            <div class="trading-form">
                <div style="text-align: center; padding: 40px 20px;">
                    <p style="color: #999; margin-bottom: 20px;">请登录后进行交易</p>
                    <a href="/auth/login" class="submit-btn buy" style="display: inline-block; text-decoration: none;">
                        立即登录
                    </a>
                </div>
            </div>
            {/if}
        </div>
    </div>
</div>
{/block}

{block name="js"}
<!-- TradingView图表库 -->
<script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
<script>
$(document).ready(function() {
    let currentSymbol = '{$symbol|default="BTC/USDT"}';
    let currentInterval = '15m';
    let chart = null;
    let candlestickSeries = null;
    
    // 初始化图表
    function initChart() {
        const chartContainer = document.getElementById('tradingview-chart');
        
        chart = LightweightCharts.createChart(chartContainer, {
            width: chartContainer.clientWidth,
            height: 500,
            layout: {
                backgroundColor: '#1e1e1e',
                textColor: '#d1d4dc',
            },
            grid: {
                vertLines: {
                    color: '#2B2B43',
                },
                horzLines: {
                    color: '#2B2B43',
                },
            },
            crosshair: {
                mode: LightweightCharts.CrosshairMode.Normal,
            },
            priceScale: {
                borderColor: '#485c7b',
            },
            timeScale: {
                borderColor: '#485c7b',
                timeVisible: true,
                secondsVisible: false,
            },
        });

        candlestickSeries = chart.addCandlestickSeries({
            upColor: '#00d4aa',
            downColor: '#ff6b6b',
            borderDownColor: '#ff6b6b',
            borderUpColor: '#00d4aa',
            wickDownColor: '#ff6b6b',
            wickUpColor: '#00d4aa',
        });
        
        loadKlineData();
    }
    
    // 加载K线数据
    function loadKlineData() {
        $.get('/trade/klineData', {
            symbol: currentSymbol,
            interval: currentInterval,
            limit: 500
        }, function(response) {
            if (response.code === 1 && response.data) {
                candlestickSeries.setData(response.data);
            }
        });
    }
    
    // 更新市场数据
    function updateMarketData() {
        // 更新价格
        $.get('/contract/price', {coin_name: currentSymbol.split('/')[0]}, function(response) {
            if (response.code === 1) {
                $('#current-price').text('$' + response.data.price.toFixed(2));
                $('#trade-price').val(response.data.price.toFixed(2));
            }
        });
        
        // 更新深度数据
        $.get('/trade/depthData', {symbol: currentSymbol}, function(response) {
            if (response.code === 1) {
                updateOrderBook(response.data);
            }
        });
        
        // 更新最新成交
        $.get('/trade/recentTrades', {symbol: currentSymbol}, function(response) {
            if (response.code === 1) {
                updateRecentTrades(response.data);
            }
        });
    }
    
    // 更新订单簿
    function updateOrderBook(data) {
        const sellOrders = $('#sell-orders');
        const buyOrders = $('#buy-orders');
        
        sellOrders.empty();
        buyOrders.empty();
        
        // 卖单（红色）
        if (data.asks) {
            data.asks.slice(0, 10).forEach(function(order) {
                sellOrders.append(`
                    <div class="order-book-item sell" onclick="setPrice(${order[0]})">
                        <span>${parseFloat(order[0]).toFixed(2)}</span>
                        <span>${parseFloat(order[1]).toFixed(4)}</span>
                        <span>${(order[0] * order[1]).toFixed(2)}</span>
                    </div>
                `);
            });
        }
        
        // 买单（绿色）
        if (data.bids) {
            data.bids.slice(0, 10).forEach(function(order) {
                buyOrders.append(`
                    <div class="order-book-item buy" onclick="setPrice(${order[0]})">
                        <span>${parseFloat(order[0]).toFixed(2)}</span>
                        <span>${parseFloat(order[1]).toFixed(4)}</span>
                        <span>${(order[0] * order[1]).toFixed(2)}</span>
                    </div>
                `);
            });
        }
    }
    
    // 更新最新成交
    function updateRecentTrades(data) {
        const tradesList = $('#recent-trades-list');
        tradesList.empty();
        
        if (data && data.length > 0) {
            data.slice(0, 20).forEach(function(trade) {
                const time = new Date(trade.timestamp * 1000).toLocaleTimeString();
                tradesList.append(`
                    <div class="trade-item ${trade.side}">
                        <span>${parseFloat(trade.price).toFixed(2)}</span>
                        <span>${parseFloat(trade.amount).toFixed(4)}</span>
                        <span>${time}</span>
                    </div>
                `);
            });
        }
    }
    
    // 设置价格
    window.setPrice = function(price) {
        $('#trade-price').val(parseFloat(price).toFixed(2));
        calculateTotal();
    };
    
    // 计算总额
    function calculateTotal() {
        const price = parseFloat($('#trade-price').val()) || 0;
        const amount = parseFloat($('#trade-amount').val()) || 0;
        const total = price * amount;
        $('#trade-total').val(total.toFixed(2));
    }
    
    // 事件绑定
    $('#trade-price, #trade-amount').on('input', calculateTotal);
    
    // 交易类型切换
    $('.form-tab').click(function() {
        $('.form-tab').removeClass('active');
        $(this).addClass('active');
        
        const type = $(this).data('type');
        $('#trade-type').val(type);
        
        $('#submit-btn').removeClass('buy sell').addClass(type);
        $('#submit-btn').text((type === 'buy' ? '买入 ' : '卖出 ') + currentSymbol.split('/')[0]);
    });
    
    // 市场数据标签切换
    $('.market-tab').click(function() {
        $('.market-tab').removeClass('active');
        $(this).addClass('active');
        
        const tab = $(this).data('tab');
        if (tab === 'orderbook') {
            $('#orderbook-section').show();
            $('#trades-section').hide();
        } else {
            $('#orderbook-section').hide();
            $('#trades-section').show();
        }
    });
    
    // 时间周期切换
    $('.timeframe-btn').click(function() {
        $('.timeframe-btn').removeClass('active');
        $(this).addClass('active');
        
        currentInterval = $(this).data('interval');
        loadKlineData();
    });
    
    // 交易对切换
    $('#symbol-select').change(function() {
        currentSymbol = $(this).val();
        $('#current-symbol').text(currentSymbol);
        $('input[name="symbol"]').val(currentSymbol);
        
        loadKlineData();
        updateMarketData();
        
        // 更新URL
        window.history.pushState({}, '', '/trade/?symbol=' + currentSymbol);
    });
    
    // 提交交易订单
    $('#trading-form').submit(function(e) {
        e.preventDefault();
        
        const formData = {
            symbol: currentSymbol,
            type: $('#trade-type').val(),
            price: parseFloat($('#trade-price').val()),
            amount: parseFloat($('#trade-amount').val())
        };
        
        if (!formData.price || !formData.amount) {
            alert('请输入价格和数量');
            return;
        }
        
        $.post('/trade/createOrder', formData, function(response) {
            if (response.code === 1) {
                alert('订单创建成功');
                location.reload();
            } else {
                alert(response.msg);
            }
        });
    });
    
    // 取消订单
    window.cancelOrder = function(orderId) {
        if (confirm('确定要取消这个订单吗？')) {
            $.post('/trade/cancelOrder', {order_id: orderId}, function(response) {
                if (response.code === 1) {
                    alert('订单已取消');
                    location.reload();
                } else {
                    alert(response.msg);
                }
            });
        }
    };
    
    // 初始化
    initChart();
    updateMarketData();
    
    // 定时更新
    setInterval(updateMarketData, 5000);
    setInterval(loadKlineData, 30000);
    
    // 窗口大小调整
    window.addEventListener('resize', function() {
        if (chart) {
            chart.applyOptions({
                width: document.getElementById('tradingview-chart').clientWidth
            });
        }
    });
});
</script>
{/block}
