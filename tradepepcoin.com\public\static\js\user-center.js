/* 用户中心JavaScript功能 */

// 初始化用户中心
function initUserCenter() {
    // 设置导航高亮
    setActiveNavigation();
    
    // 初始化事件监听
    setupEventListeners();
}

// 设置导航高亮
function setActiveNavigation() {
    const currentPath = window.location.pathname;
    const navItems = document.querySelectorAll('.nav-item');
    
    navItems.forEach(item => {
        item.classList.remove('active');
        if (item.getAttribute('href') === currentPath) {
            item.classList.add('active');
        }
    });
}

// 设置事件监听器
function setupEventListeners() {
    // 导航点击事件
    document.querySelectorAll('.nav-item').forEach(item => {
        item.addEventListener('click', function(e) {
            // 移除所有active类
            document.querySelectorAll('.nav-item').forEach(nav => {
                nav.classList.remove('active');
            });
            // 添加active类到当前项
            this.classList.add('active');
        });
    });
}

// 加载用户数据
function loadUserData() {
    fetch('/user/stats')
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            updateUserStats(data.data);
        }
    })
    .catch(error => {
        console.error('加载用户数据失败:', error);
    });
}

// 更新用户统计数据
function updateUserStats(stats) {
    // 更新总资产
    const totalAssetsElement = document.getElementById('totalAssets');
    if (totalAssetsElement) {
        totalAssetsElement.textContent = (stats.total_assets || 0).toFixed(2);
    }
    
    // 更新今日盈亏
    const todayProfitElement = document.getElementById('todayProfit');
    if (todayProfitElement) {
        const profit = stats.today_profit || 0;
        todayProfitElement.textContent = (profit >= 0 ? '+' : '') + profit.toFixed(2);
        todayProfitElement.className = 'stat-value ' + (profit >= 0 ? 'text-success' : 'text-danger');
    }
    
    // 更新总交易次数
    const totalOrdersElement = document.getElementById('totalOrders');
    if (totalOrdersElement) {
        totalOrdersElement.textContent = stats.total_orders || 0;
    }
    
    // 更新胜率
    const winRateElement = document.getElementById('winRate');
    if (winRateElement) {
        winRateElement.textContent = (stats.win_rate || 0) + '%';
    }
}

// 加载资产数据
function loadAssetData() {
    fetch('/user/assets/api')
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            updateAssetList(data.data);
        }
    })
    .catch(error => {
        console.error('加载资产数据失败:', error);
    });
}

// 更新资产列表
function updateAssetList(assets) {
    const assetListElement = document.getElementById('assetList');
    if (!assetListElement) return;
    
    assetListElement.innerHTML = '';
    
    // 主要币种列表
    const mainCoins = ['USDT', 'BTC', 'ETH', 'BNB'];
    
    mainCoins.forEach(coin => {
        const asset = assets[coin] || { available: 0, frozen: 0 };
        const total = parseFloat(asset.available) + parseFloat(asset.frozen);
        
        if (total > 0 || coin === 'USDT') {
            const assetItem = document.createElement('div');
            assetItem.className = 'asset-item';
            
            // 计算USDT价值（简化计算）
            let usdtValue = total;
            if (coin === 'BTC') usdtValue = total * 45000;
            else if (coin === 'ETH') usdtValue = total * 2800;
            else if (coin === 'BNB') usdtValue = total * 300;
            
            assetItem.innerHTML = `
                <div class="asset-info">
                    <div class="asset-icon">${coin.substring(0, 2)}</div>
                    <div class="asset-details">
                        <h4>${coin}</h4>
                        <p>可用: ${parseFloat(asset.available).toFixed(8)}</p>
                    </div>
                </div>
                <div class="asset-balance">
                    <div class="balance-amount">${total.toFixed(8)}</div>
                    <div class="balance-value">≈ ${usdtValue.toFixed(2)} USDT</div>
                </div>
            `;
            
            assetListElement.appendChild(assetItem);
        }
    });
    
    // 如果没有资产，显示空状态
    if (assetListElement.children.length === 0) {
        assetListElement.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">💰</div>
                <div class="empty-text">暂无资产</div>
            </div>
        `;
    }
}

// 加载最近交易
function loadRecentTrades() {
    fetch('/user/recent-trades')
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            updateRecentTrades(data.data);
        }
    })
    .catch(error => {
        console.error('加载最近交易失败:', error);
        // 显示模拟数据
        updateRecentTrades([]);
    });
}

// 更新最近交易列表
function updateRecentTrades(trades) {
    const recentTradesElement = document.getElementById('recentTrades');
    if (!recentTradesElement) return;
    
    recentTradesElement.innerHTML = '';
    
    if (trades.length === 0) {
        // 显示模拟数据
        const mockTrades = [
            {
                symbol: 'BTC/USDT',
                type: 'buy',
                amount: '0.001',
                price: '45280.50',
                time: '2小时前',
                profit: '+12.50'
            },
            {
                symbol: 'ETH/USDT',
                type: 'sell',
                amount: '0.1',
                price: '2850.00',
                time: '5小时前',
                profit: '-8.30'
            }
        ];
        trades = mockTrades;
    }
    
    trades.slice(0, 5).forEach(trade => {
        const tradeItem = document.createElement('div');
        tradeItem.className = 'trade-item';
        
        const isProfit = parseFloat(trade.profit) >= 0;
        
        tradeItem.innerHTML = `
            <div class="trade-info">
                <h4>${trade.symbol} ${trade.type === 'buy' ? '买入' : '卖出'}</h4>
                <p>${trade.amount} @ ${trade.price}</p>
            </div>
            <div class="trade-result">
                <div class="trade-amount ${isProfit ? 'text-success' : 'text-danger'}">
                    ${trade.profit} USDT
                </div>
                <div class="trade-time">${trade.time}</div>
            </div>
        `;
        
        recentTradesElement.appendChild(tradeItem);
    });
    
    if (trades.length === 0) {
        recentTradesElement.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">📊</div>
                <div class="empty-text">暂无交易记录</div>
            </div>
        `;
    }
}

// 加载通知
function loadNotifications() {
    fetch('/user/notifications/api?limit=5')
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            updateNotifications(data.data);
        }
    })
    .catch(error => {
        console.error('加载通知失败:', error);
        // 显示模拟数据
        updateNotifications([]);
    });
}

// 更新通知列表
function updateNotifications(notifications) {
    const notificationListElement = document.getElementById('notificationList');
    if (!notificationListElement) return;
    
    notificationListElement.innerHTML = '';
    
    if (notifications.length === 0) {
        // 显示模拟数据
        const mockNotifications = [
            {
                title: '交易成功通知',
                content: '您的BTC/USDT买入订单已成功成交',
                time: '2小时前',
                is_read: 0
            },
            {
                title: '安全提醒',
                content: '检测到您的账户在新设备登录',
                time: '1天前',
                is_read: 1
            }
        ];
        notifications = mockNotifications;
    }
    
    notifications.forEach(notification => {
        const notificationItem = document.createElement('div');
        notificationItem.className = `notification-item ${notification.is_read ? '' : 'unread'}`;
        
        notificationItem.innerHTML = `
            <div class="notification-title">${notification.title}</div>
            <div class="notification-content">${notification.content}</div>
            <div class="notification-time">${notification.time}</div>
        `;
        
        notificationListElement.appendChild(notificationItem);
    });
    
    if (notifications.length === 0) {
        notificationListElement.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">🔔</div>
                <div class="empty-text">暂无通知</div>
            </div>
        `;
    }
}

// 加载公告
function loadAnnouncements() {
    fetch('/announcements/api?limit=5')
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            updateAnnouncements(data.data);
        }
    })
    .catch(error => {
        console.error('加载公告失败:', error);
        // 显示模拟数据
        updateAnnouncements([]);
    });
}

// 更新公告列表
function updateAnnouncements(announcements) {
    const announcementListElement = document.getElementById('announcementList');
    if (!announcementListElement) return;
    
    announcementListElement.innerHTML = '';
    
    if (announcements.length === 0) {
        // 显示模拟数据
        const mockAnnouncements = [
            {
                title: '系统维护通知',
                content: '平台将于今晚23:00-01:00进行系统维护升级',
                time: '3小时前'
            },
            {
                title: '新币种上线',
                content: 'DOGE/USDT交易对现已上线，欢迎体验',
                time: '2天前'
            }
        ];
        announcements = mockAnnouncements;
    }
    
    announcements.forEach(announcement => {
        const announcementItem = document.createElement('div');
        announcementItem.className = 'announcement-item';
        
        announcementItem.innerHTML = `
            <div class="announcement-title">${announcement.title}</div>
            <div class="announcement-content">${announcement.content}</div>
            <div class="announcement-time">${announcement.time}</div>
        `;
        
        announcementListElement.appendChild(announcementItem);
    });
    
    if (announcements.length === 0) {
        announcementListElement.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">📢</div>
                <div class="empty-text">暂无公告</div>
            </div>
        `;
    }
}

// 主题切换（复用之前的函数）
function toggleTheme() {
    const html = document.documentElement;
    const currentTheme = html.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    
    html.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
    
    const themeIcon = document.getElementById('themeIcon');
    if (themeIcon) {
        themeIcon.textContent = newTheme === 'dark' ? '🌙' : '☀️';
    }
}

// 初始化主题（复用之前的函数）
function initTheme() {
    const savedTheme = localStorage.getItem('theme') || 'dark';
    document.documentElement.setAttribute('data-theme', savedTheme);
    
    const themeIcon = document.getElementById('themeIcon');
    if (themeIcon) {
        themeIcon.textContent = savedTheme === 'dark' ? '🌙' : '☀️';
    }
}

// 格式化时间
function formatTime(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diff = now - time;
    
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;
    
    return time.toLocaleDateString();
}

// 显示通知（复用之前的函数）
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">×</button>
    `;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 16px;
        border-radius: 4px;
        color: white;
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 8px;
        background: ${type === 'success' ? '#0ECB81' : type === 'error' ? '#F6465D' : '#1890FF'};
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}
