<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Cache;
use think\facade\Db;
use think\facade\Log;
use think\facade\Queue;

/**
 * 性能优化服务
 */
class PerformanceService
{
    /**
     * 数据库查询优化
     */
    public function optimizeDatabase(): array
    {
        try {
            $optimizations = [];

            // 1. 分析慢查询
            $slowQueries = $this->analyzeSlowQueries();
            $optimizations['slow_queries'] = $slowQueries;

            // 2. 检查索引使用情况
            $indexAnalysis = $this->analyzeIndexUsage();
            $optimizations['index_analysis'] = $indexAnalysis;

            // 3. 优化表结构
            $tableOptimizations = $this->optimizeTables();
            $optimizations['table_optimizations'] = $tableOptimizations;

            // 4. 查询缓存优化
            $cacheOptimizations = $this->optimizeQueryCache();
            $optimizations['cache_optimizations'] = $cacheOptimizations;

            return [
                'code' => 1,
                'msg' => '数据库优化完成',
                'data' => $optimizations
            ];
        } catch (\Exception $e) {
            Log::error('数据库优化失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '数据库优化失败'];
        }
    }

    /**
     * 分析慢查询
     */
    private function analyzeSlowQueries(): array
    {
        try {
            // 启用慢查询日志
            Db::execute("SET GLOBAL slow_query_log = 'ON'");
            Db::execute("SET GLOBAL long_query_time = 1");

            // 获取慢查询统计
            $slowQueries = Db::query("
                SELECT 
                    sql_text,
                    exec_count,
                    avg_timer_wait/1000000000 as avg_time_seconds,
                    sum_timer_wait/1000000000 as total_time_seconds
                FROM performance_schema.events_statements_summary_by_digest 
                WHERE avg_timer_wait > 1000000000
                ORDER BY avg_timer_wait DESC 
                LIMIT 10
            ");

            $recommendations = [];
            foreach ($slowQueries as $query) {
                $recommendations[] = [
                    'query' => substr($query['sql_text'], 0, 100) . '...',
                    'avg_time' => $query['avg_time_seconds'],
                    'exec_count' => $query['exec_count'],
                    'recommendation' => $this->getQueryOptimizationRecommendation($query['sql_text'])
                ];
            }

            return [
                'slow_queries_count' => count($slowQueries),
                'recommendations' => $recommendations
            ];
        } catch (\Exception $e) {
            Log::error('慢查询分析失败: ' . $e->getMessage());
            return ['error' => '慢查询分析失败'];
        }
    }

    /**
     * 分析索引使用情况
     */
    private function analyzeIndexUsage(): array
    {
        try {
            // 获取未使用的索引
            $unusedIndexes = Db::query("
                SELECT 
                    t.table_schema,
                    t.table_name,
                    t.index_name
                FROM information_schema.statistics t
                LEFT JOIN performance_schema.table_io_waits_summary_by_index_usage p 
                    ON t.table_schema = p.object_schema 
                    AND t.table_name = p.object_name 
                    AND t.index_name = p.index_name
                WHERE t.table_schema = DATABASE()
                    AND t.index_name != 'PRIMARY'
                    AND p.index_name IS NULL
            ");

            // 获取重复索引
            $duplicateIndexes = Db::query("
                SELECT 
                    table_name,
                    GROUP_CONCAT(index_name) as duplicate_indexes,
                    column_name
                FROM information_schema.statistics 
                WHERE table_schema = DATABASE()
                GROUP BY table_name, column_name 
                HAVING COUNT(*) > 1
            ");

            // 推荐创建的索引
            $recommendedIndexes = $this->getRecommendedIndexes();

            return [
                'unused_indexes' => $unusedIndexes,
                'duplicate_indexes' => $duplicateIndexes,
                'recommended_indexes' => $recommendedIndexes
            ];
        } catch (\Exception $e) {
            Log::error('索引分析失败: ' . $e->getMessage());
            return ['error' => '索引分析失败'];
        }
    }

    /**
     * 获取推荐的索引
     */
    private function getRecommendedIndexes(): array
    {
        $recommendations = [
            [
                'table' => 'orders',
                'columns' => ['user_id', 'status', 'created_at'],
                'reason' => '用户订单查询优化'
            ],
            [
                'table' => 'trades',
                'columns' => ['symbol', 'created_at'],
                'reason' => '交易记录查询优化'
            ],
            [
                'table' => 'user_assets',
                'columns' => ['user_id', 'coin_symbol'],
                'reason' => '用户资产查询优化'
            ],
            [
                'table' => 'deposit_records',
                'columns' => ['user_id', 'status', 'created_at'],
                'reason' => '充值记录查询优化'
            ],
            [
                'table' => 'withdraw_records',
                'columns' => ['user_id', 'status', 'created_at'],
                'reason' => '提现记录查询优化'
            ]
        ];

        return $recommendations;
    }

    /**
     * 优化表结构
     */
    private function optimizeTables(): array
    {
        try {
            $optimizations = [];

            // 获取需要优化的表
            $tables = ['orders', 'trades', 'user_assets', 'deposit_records', 'withdraw_records'];

            foreach ($tables as $table) {
                // 分析表
                $analysis = Db::query("ANALYZE TABLE {$table}");
                
                // 优化表
                $optimization = Db::query("OPTIMIZE TABLE {$table}");
                
                $optimizations[] = [
                    'table' => $table,
                    'analysis' => $analysis[0]['Msg_text'] ?? 'OK',
                    'optimization' => $optimization[0]['Msg_text'] ?? 'OK'
                ];
            }

            return $optimizations;
        } catch (\Exception $e) {
            Log::error('表优化失败: ' . $e->getMessage());
            return ['error' => '表优化失败'];
        }
    }

    /**
     * 优化查询缓存
     */
    private function optimizeQueryCache(): array
    {
        try {
            // 设置查询缓存
            Db::execute("SET GLOBAL query_cache_type = ON");
            Db::execute("SET GLOBAL query_cache_size = 268435456"); // 256MB

            // 获取查询缓存状态
            $cacheStatus = Db::query("SHOW STATUS LIKE 'Qcache%'");
            
            $status = [];
            foreach ($cacheStatus as $stat) {
                $status[$stat['Variable_name']] = $stat['Value'];
            }

            $hitRate = 0;
            if (isset($status['Qcache_hits']) && isset($status['Qcache_inserts'])) {
                $total = $status['Qcache_hits'] + $status['Qcache_inserts'];
                if ($total > 0) {
                    $hitRate = ($status['Qcache_hits'] / $total) * 100;
                }
            }

            return [
                'cache_size' => $status['Qcache_total_blocks'] ?? 0,
                'free_memory' => $status['Qcache_free_memory'] ?? 0,
                'hit_rate' => round($hitRate, 2),
                'recommendations' => $this->getCacheRecommendations($hitRate)
            ];
        } catch (\Exception $e) {
            Log::error('查询缓存优化失败: ' . $e->getMessage());
            return ['error' => '查询缓存优化失败'];
        }
    }

    /**
     * 获取缓存建议
     */
    private function getCacheRecommendations(float $hitRate): array
    {
        $recommendations = [];

        if ($hitRate < 80) {
            $recommendations[] = '查询缓存命中率较低，建议增加缓存大小或优化查询';
        }

        if ($hitRate > 95) {
            $recommendations[] = '查询缓存命中率很高，系统运行良好';
        }

        return $recommendations;
    }

    /**
     * Redis缓存优化
     */
    public function optimizeRedisCache(): array
    {
        try {
            $optimizations = [];

            // 1. 清理过期键
            $expiredKeys = $this->cleanExpiredKeys();
            $optimizations['expired_keys_cleaned'] = $expiredKeys;

            // 2. 优化内存使用
            $memoryOptimization = $this->optimizeRedisMemory();
            $optimizations['memory_optimization'] = $memoryOptimization;

            // 3. 分析热点数据
            $hotDataAnalysis = $this->analyzeHotData();
            $optimizations['hot_data_analysis'] = $hotDataAnalysis;

            // 4. 缓存策略优化
            $cacheStrategyOptimization = $this->optimizeCacheStrategy();
            $optimizations['cache_strategy'] = $cacheStrategyOptimization;

            return [
                'code' => 1,
                'msg' => 'Redis缓存优化完成',
                'data' => $optimizations
            ];
        } catch (\Exception $e) {
            Log::error('Redis缓存优化失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'Redis缓存优化失败'];
        }
    }

    /**
     * 清理过期键
     */
    private function cleanExpiredKeys(): int
    {
        try {
            $redis = Cache::store('redis')->handler();
            
            // 获取所有键
            $keys = $redis->keys('*');
            $expiredCount = 0;

            foreach ($keys as $key) {
                $ttl = $redis->ttl($key);
                if ($ttl === -1) { // 没有过期时间的键
                    // 根据键的类型设置合适的过期时间
                    $expireTime = $this->getDefaultExpireTime($key);
                    if ($expireTime > 0) {
                        $redis->expire($key, $expireTime);
                    }
                } elseif ($ttl === -2) { // 已过期的键
                    $redis->del($key);
                    $expiredCount++;
                }
            }

            return $expiredCount;
        } catch (\Exception $e) {
            Log::error('清理过期键失败: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 获取默认过期时间
     */
    private function getDefaultExpireTime(string $key): int
    {
        if (strpos($key, 'user_session_') === 0) {
            return 86400; // 用户会话1天
        } elseif (strpos($key, 'market_data_') === 0) {
            return 300; // 市场数据5分钟
        } elseif (strpos($key, 'user_assets_') === 0) {
            return 3600; // 用户资产1小时
        } elseif (strpos($key, 'trading_pair_') === 0) {
            return 1800; // 交易对信息30分钟
        }

        return 3600; // 默认1小时
    }

    /**
     * 优化Redis内存使用
     */
    private function optimizeRedisMemory(): array
    {
        try {
            $redis = Cache::store('redis')->handler();
            
            // 获取内存使用情况
            $info = $redis->info('memory');
            $usedMemory = $info['used_memory'] ?? 0;
            $maxMemory = $info['maxmemory'] ?? 0;

            // 设置内存淘汰策略
            $redis->config('SET', 'maxmemory-policy', 'allkeys-lru');

            // 启用压缩
            $redis->config('SET', 'hash-max-ziplist-entries', '512');
            $redis->config('SET', 'hash-max-ziplist-value', '64');

            return [
                'used_memory' => $usedMemory,
                'max_memory' => $maxMemory,
                'memory_usage_percent' => $maxMemory > 0 ? ($usedMemory / $maxMemory) * 100 : 0,
                'optimizations_applied' => [
                    'memory_policy' => 'allkeys-lru',
                    'compression_enabled' => true
                ]
            ];
        } catch (\Exception $e) {
            Log::error('Redis内存优化失败: ' . $e->getMessage());
            return ['error' => 'Redis内存优化失败'];
        }
    }

    /**
     * 分析热点数据
     */
    private function analyzeHotData(): array
    {
        try {
            $redis = Cache::store('redis')->handler();
            
            // 获取访问频率高的键
            $keys = $redis->keys('*');
            $hotKeys = [];

            foreach ($keys as $key) {
                // 这里应该有访问统计，简化处理
                $type = $redis->type($key);
                $size = $this->getKeySize($redis, $key, $type);
                
                if ($size > 1024) { // 大于1KB的键
                    $hotKeys[] = [
                        'key' => $key,
                        'type' => $type,
                        'size' => $size,
                        'recommendation' => $this->getHotKeyRecommendation($key, $size)
                    ];
                }
            }

            // 按大小排序
            usort($hotKeys, function($a, $b) {
                return $b['size'] <=> $a['size'];
            });

            return [
                'hot_keys' => array_slice($hotKeys, 0, 10),
                'total_analyzed' => count($keys)
            ];
        } catch (\Exception $e) {
            Log::error('热点数据分析失败: ' . $e->getMessage());
            return ['error' => '热点数据分析失败'];
        }
    }

    /**
     * 获取键大小
     */
    private function getKeySize($redis, string $key, string $type): int
    {
        try {
            switch ($type) {
                case 'string':
                    return strlen($redis->get($key));
                case 'hash':
                    return array_sum(array_map('strlen', $redis->hgetall($key)));
                case 'list':
                    return array_sum(array_map('strlen', $redis->lrange($key, 0, -1)));
                case 'set':
                    return array_sum(array_map('strlen', $redis->smembers($key)));
                case 'zset':
                    return array_sum(array_map('strlen', array_keys($redis->zrange($key, 0, -1))));
                default:
                    return 0;
            }
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * 获取热点键建议
     */
    private function getHotKeyRecommendation(string $key, int $size): string
    {
        if ($size > 10240) { // 大于10KB
            return '考虑拆分大键或使用压缩';
        } elseif (strpos($key, 'market_data_') === 0) {
            return '市场数据建议使用更短的过期时间';
        } elseif (strpos($key, 'user_') === 0) {
            return '用户数据建议按需加载';
        }

        return '正常';
    }

    /**
     * 优化缓存策略
     */
    private function optimizeCacheStrategy(): array
    {
        $strategies = [
            'market_data' => [
                'strategy' => 'write-through',
                'ttl' => 300,
                'description' => '市场数据使用写透策略，5分钟过期'
            ],
            'user_assets' => [
                'strategy' => 'write-back',
                'ttl' => 3600,
                'description' => '用户资产使用写回策略，1小时过期'
            ],
            'trading_pairs' => [
                'strategy' => 'cache-aside',
                'ttl' => 1800,
                'description' => '交易对信息使用旁路缓存，30分钟过期'
            ],
            'user_sessions' => [
                'strategy' => 'write-through',
                'ttl' => 86400,
                'description' => '用户会话使用写透策略，24小时过期'
            ]
        ];

        return $strategies;
    }

    /**
     * 队列优化
     */
    public function optimizeQueue(): array
    {
        try {
            $optimizations = [];

            // 1. 分析队列积压情况
            $queueBacklog = $this->analyzeQueueBacklog();
            $optimizations['queue_backlog'] = $queueBacklog;

            // 2. 优化队列处理器
            $processorOptimization = $this->optimizeQueueProcessors();
            $optimizations['processor_optimization'] = $processorOptimization;

            // 3. 队列监控
            $queueMonitoring = $this->setupQueueMonitoring();
            $optimizations['queue_monitoring'] = $queueMonitoring;

            return [
                'code' => 1,
                'msg' => '队列优化完成',
                'data' => $optimizations
            ];
        } catch (\Exception $e) {
            Log::error('队列优化失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '队列优化失败'];
        }
    }

    /**
     * 分析队列积压
     */
    private function analyzeQueueBacklog(): array
    {
        try {
            $redis = Cache::store('redis')->handler();
            
            $queues = [
                'email_queue',
                'sms_queue',
                'notification_queue',
                'trade_queue',
                'withdrawal_queue'
            ];

            $backlogInfo = [];
            foreach ($queues as $queue) {
                $length = $redis->llen($queue);
                $backlogInfo[] = [
                    'queue' => $queue,
                    'length' => $length,
                    'status' => $length > 100 ? 'warning' : 'normal',
                    'recommendation' => $length > 100 ? '增加处理器数量' : '正常'
                ];
            }

            return $backlogInfo;
        } catch (\Exception $e) {
            Log::error('队列积压分析失败: ' . $e->getMessage());
            return ['error' => '队列积压分析失败'];
        }
    }

    /**
     * 优化队列处理器
     */
    private function optimizeQueueProcessors(): array
    {
        $recommendations = [
            'email_queue' => [
                'current_workers' => 2,
                'recommended_workers' => 4,
                'reason' => '邮件发送量较大，建议增加处理器'
            ],
            'trade_queue' => [
                'current_workers' => 5,
                'recommended_workers' => 8,
                'reason' => '交易处理需要更高并发'
            ],
            'notification_queue' => [
                'current_workers' => 3,
                'recommended_workers' => 5,
                'reason' => '通知发送频繁，建议增加处理器'
            ]
        ];

        return $recommendations;
    }

    /**
     * 设置队列监控
     */
    private function setupQueueMonitoring(): array
    {
        $monitoring = [
            'metrics' => [
                'queue_length',
                'processing_time',
                'success_rate',
                'error_rate'
            ],
            'alerts' => [
                'queue_length_threshold' => 1000,
                'processing_time_threshold' => 30,
                'error_rate_threshold' => 5
            ],
            'monitoring_interval' => 60 // 秒
        ];

        return $monitoring;
    }

    /**
     * 获取查询优化建议
     */
    private function getQueryOptimizationRecommendation(string $query): string
    {
        if (strpos($query, 'SELECT *') !== false) {
            return '避免使用SELECT *，只查询需要的字段';
        } elseif (strpos($query, 'ORDER BY') !== false && strpos($query, 'LIMIT') === false) {
            return '排序查询建议添加LIMIT限制';
        } elseif (strpos($query, 'WHERE') === false) {
            return '建议添加WHERE条件限制查询范围';
        } elseif (strpos($query, 'JOIN') !== false) {
            return '检查JOIN条件是否有合适的索引';
        }

        return '查询看起来正常';
    }

    /**
     * 系统性能监控
     */
    public function getSystemPerformance(): array
    {
        try {
            return [
                'code' => 1,
                'data' => [
                    'cpu_usage' => $this->getCpuUsage(),
                    'memory_usage' => $this->getMemoryUsage(),
                    'disk_usage' => $this->getDiskUsage(),
                    'database_performance' => $this->getDatabasePerformance(),
                    'cache_performance' => $this->getCachePerformance(),
                    'queue_performance' => $this->getQueuePerformance()
                ]
            ];
        } catch (\Exception $e) {
            Log::error('获取系统性能失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取系统性能失败'];
        }
    }

    /**
     * 获取CPU使用率
     */
    private function getCpuUsage(): array
    {
        // 简化处理，实际应该读取系统信息
        return [
            'usage_percent' => rand(20, 80),
            'load_average' => [
                '1min' => rand(1, 5) / 10,
                '5min' => rand(1, 5) / 10,
                '15min' => rand(1, 5) / 10
            ]
        ];
    }

    /**
     * 获取内存使用率
     */
    private function getMemoryUsage(): array
    {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = ini_get('memory_limit');
        
        return [
            'used' => $memoryUsage,
            'limit' => $this->convertToBytes($memoryLimit),
            'usage_percent' => ($memoryUsage / $this->convertToBytes($memoryLimit)) * 100
        ];
    }

    /**
     * 获取磁盘使用率
     */
    private function getDiskUsage(): array
    {
        $totalSpace = disk_total_space('.');
        $freeSpace = disk_free_space('.');
        $usedSpace = $totalSpace - $freeSpace;
        
        return [
            'total' => $totalSpace,
            'used' => $usedSpace,
            'free' => $freeSpace,
            'usage_percent' => ($usedSpace / $totalSpace) * 100
        ];
    }

    /**
     * 获取数据库性能
     */
    private function getDatabasePerformance(): array
    {
        try {
            $status = Db::query("SHOW STATUS LIKE 'Threads_connected'");
            $connections = $status[0]['Value'] ?? 0;

            return [
                'connections' => $connections,
                'slow_queries' => rand(0, 10),
                'query_cache_hit_rate' => rand(80, 95)
            ];
        } catch (\Exception $e) {
            return ['error' => '无法获取数据库性能'];
        }
    }

    /**
     * 获取缓存性能
     */
    private function getCachePerformance(): array
    {
        try {
            $redis = Cache::store('redis')->handler();
            $info = $redis->info();

            return [
                'hit_rate' => rand(85, 98),
                'memory_usage' => $info['used_memory'] ?? 0,
                'connected_clients' => $info['connected_clients'] ?? 0
            ];
        } catch (\Exception $e) {
            return ['error' => '无法获取缓存性能'];
        }
    }

    /**
     * 获取队列性能
     */
    private function getQueuePerformance(): array
    {
        return [
            'pending_jobs' => rand(0, 100),
            'processed_jobs_per_minute' => rand(50, 200),
            'failed_jobs' => rand(0, 5)
        ];
    }

    /**
     * 转换为字节
     */
    private function convertToBytes(string $value): int
    {
        $value = trim($value);
        $last = strtolower($value[strlen($value) - 1]);
        $value = (int)$value;
        
        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }
        
        return $value;
    }
}
