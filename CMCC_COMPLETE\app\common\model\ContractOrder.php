<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 合约订单模型
 */
class ContractOrder extends Model
{
    protected $name = 'gvd_futures_orders';
    
    protected $type = [
        'amount' => 'float',
        'open_price' => 'float',
        'close_price' => 'float',
        'pnl' => 'float',
        'margin' => 'float',
        'leverage' => 'integer',
        'open_time' => 'timestamp',
        'close_time' => 'timestamp'
    ];

    // 订单状态
    const STATUS_OPEN = 'open';        // 持仓中
    const STATUS_CLOSED = 'closed';    // 已平仓
    const STATUS_CANCELLED = 'cancelled'; // 已取消

    // 订单方向
    const SIDE_BUY = 'buy';   // 买入/做多
    const SIDE_SELL = 'sell'; // 卖出/做空

    // 订单类型
    const TYPE_LIMIT = 'limit';   // 限价单
    const TYPE_MARKET = 'market'; // 市价单

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 获取订单状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = [
            self::STATUS_OPEN => '持仓中',
            self::STATUS_CLOSED => '已平仓',
            self::STATUS_CANCELLED => '已取消'
        ];
        
        return $statusMap[$data['status']] ?? '未知';
    }

    /**
     * 获取方向文本
     */
    public function getSideTextAttr($value, $data)
    {
        $sideMap = [
            self::SIDE_BUY => '做多',
            self::SIDE_SELL => '做空'
        ];
        
        return $sideMap[$data['side']] ?? '未知';
    }

    /**
     * 计算盈亏
     */
    public function calculatePnl($currentPrice = null)
    {
        if ($this->status !== self::STATUS_OPEN) {
            return $this->pnl;
        }

        if (!$currentPrice) {
            return 0;
        }

        $openPrice = $this->open_price;
        $amount = $this->amount;
        $side = $this->side;

        if ($side === self::SIDE_BUY) {
            return ($currentPrice - $openPrice) * $amount;
        } else {
            return ($openPrice - $currentPrice) * $amount;
        }
    }

    /**
     * 计算保证金率
     */
    public function calculateMarginRate($currentPrice = null)
    {
        if (!$currentPrice || $this->margin <= 0) {
            return 0;
        }

        $unrealizedPnl = $this->calculatePnl($currentPrice);
        $equity = $this->margin + $unrealizedPnl;
        
        return ($equity / $this->margin) * 100;
    }

    /**
     * 是否需要强制平仓
     */
    public function needLiquidation($currentPrice = null, $liquidationRate = 20)
    {
        if ($this->status !== self::STATUS_OPEN) {
            return false;
        }

        $marginRate = $this->calculateMarginRate($currentPrice);
        return $marginRate <= $liquidationRate;
    }

    /**
     * 获取订单详情
     */
    public static function getOrderDetail($orderId)
    {
        return self::with(['user'])
            ->where('id', $orderId)
            ->find();
    }

    /**
     * 获取用户持仓列表
     */
    public static function getUserPositions($userId, $status = self::STATUS_OPEN)
    {
        return self::where('user_id', $userId)
            ->where('status', $status)
            ->order('id desc')
            ->select();
    }

    /**
     * 获取交易对的所有持仓
     */
    public static function getSymbolPositions($symbol, $status = self::STATUS_OPEN)
    {
        return self::where('symbol', $symbol)
            ->where('status', $status)
            ->select();
    }

    /**
     * 平仓
     */
    public function closePosition($closePrice, $pnl = null)
    {
        if ($pnl === null) {
            $pnl = $this->calculatePnl($closePrice);
        }

        $this->close_price = $closePrice;
        $this->pnl = $pnl;
        $this->status = self::STATUS_CLOSED;
        $this->close_time = time();
        $this->updated_at = date('Y-m-d H:i:s');
        
        return $this->save();
    }

    /**
     * 取消订单
     */
    public function cancelOrder()
    {
        $this->status = self::STATUS_CANCELLED;
        $this->updated_at = date('Y-m-d H:i:s');
        
        return $this->save();
    }

    /**
     * 搜索器 - 按用户ID
     */
    public function searchUserIdAttr($query, $value)
    {
        $query->where('user_id', $value);
    }

    /**
     * 搜索器 - 按交易对
     */
    public function searchSymbolAttr($query, $value)
    {
        $query->where('symbol', 'like', '%' . $value . '%');
    }

    /**
     * 搜索器 - 按状态
     */
    public function searchStatusAttr($query, $value)
    {
        $query->where('status', $value);
    }

    /**
     * 搜索器 - 按时间范围
     */
    public function searchTimeRangeAttr($query, $value)
    {
        if (is_array($value) && count($value) === 2) {
            $query->whereBetweenTime('created_at', $value[0], $value[1]);
        }
    }
}
