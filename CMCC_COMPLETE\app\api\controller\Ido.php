<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\BaseController;
use app\common\service\IdoService;
use app\common\model\User;
use think\facade\Request;
use think\facade\Validate;

/**
 * IDO控制器
 */
class Ido extends BaseController
{
    protected $idoService;

    public function initialize()
    {
        parent::initialize();
        $this->idoService = new IdoService();
    }

    /**
     * 获取IDO项目列表
     * GET /api/ido/projects
     */
    public function getProjects()
    {
        $page = Request::get('page/d', 1);
        $limit = Request::get('limit/d', 20);
        $status = Request::get('status', '');

        $params = [
            'page' => $page,
            'limit' => $limit,
            'status' => $status
        ];

        $result = $this->idoService->getProjectList($params);
        return json($result);
    }

    /**
     * 获取项目详情
     * GET /api/ido/project-detail
     */
    public function getProjectDetail()
    {
        $projectId = Request::get('project_id/d', 0);
        if (!$projectId) {
            return $this->error('项目ID不能为空');
        }

        $userId = $this->getUserId();
        $result = $this->idoService->getProjectDetail($projectId, $userId);
        
        return json($result);
    }

    /**
     * 参与IDO认购
     * POST /api/ido/purchase
     */
    public function purchase()
    {
        if (!Request::isPost()) {
            return $this->error('请求方式错误');
        }

        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $data = Request::post();

        $validate = Validate::rule([
            'project_id' => 'require|integer',
            'amount' => 'require|float|gt:0',
            'pay_password' => 'require'
        ])->message([
            'project_id.require' => '项目ID不能为空',
            'amount.require' => '认购金额不能为空',
            'amount.gt' => '认购金额必须大于0',
            'pay_password.require' => '支付密码不能为空'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        $result = $this->idoService->purchaseProject(
            $userId,
            $data['project_id'],
            $data['amount'],
            $data['pay_password']
        );

        return json($result);
    }

    /**
     * 获取用户认购记录
     * GET /api/ido/my-orders
     */
    public function getMyOrders()
    {
        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $page = Request::get('page/d', 1);
        $limit = Request::get('limit/d', 20);

        $params = [
            'page' => $page,
            'limit' => $limit
        ];

        $result = $this->idoService->getUserOrders($userId, $params);
        return json($result);
    }

    /**
     * 获取项目认购记录
     * GET /api/ido/project-orders
     */
    public function getProjectOrders()
    {
        $projectId = Request::get('project_id/d', 0);
        if (!$projectId) {
            return $this->error('项目ID不能为空');
        }

        $page = Request::get('page/d', 1);
        $limit = Request::get('limit/d', 20);

        $params = [
            'page' => $page,
            'limit' => $limit
        ];

        $result = $this->idoService->getProjectOrders($projectId, $params);
        return json($result);
    }

    /**
     * 获取热门项目
     * GET /api/ido/hot-projects
     */
    public function getHotProjects()
    {
        $limit = Request::get('limit/d', 6);
        $result = $this->idoService->getHotProjects($limit);
        
        return json($result);
    }

    /**
     * 获取IDO统计
     * GET /api/ido/stats
     */
    public function getStats()
    {
        $result = $this->idoService->getIdoStats();
        return json($result);
    }

    /**
     * 获取用户IDO统计
     * GET /api/ido/my-stats
     */
    public function getMyStats()
    {
        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $result = $this->idoService->getUserIdoStats($userId);
        return json($result);
    }

    /**
     * 检查项目状态
     * GET /api/ido/check-project
     */
    public function checkProject()
    {
        $projectId = Request::get('project_id/d', 0);
        if (!$projectId) {
            return $this->error('项目ID不能为空');
        }

        $result = $this->idoService->checkProjectPurchasable($projectId);
        return json($result);
    }

    /**
     * 获取当前用户ID
     */
    private function getUserId(): int
    {
        $token = Request::header('Authorization');
        if (!$token) {
            return 0;
        }

        $token = str_replace('Bearer ', '', $token);
        $payload = User::verifyToken($token);
        return $payload ? $payload['user_id'] : 0;
    }
}
