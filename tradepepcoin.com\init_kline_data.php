<?php
/**
 * K线数据初始化脚本
 * 用于生成测试用的K线数据
 */

require_once __DIR__ . '/vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;

// 初始化ThinkPHP
$app = new think\App();
$app->initialize();

echo "=== K线数据初始化脚本 ===\n\n";

// 数据库配置
$database = Config::get('database');
echo "数据库配置检查...\n";
echo "主机: " . $database['default']['hostname'] . "\n";
echo "数据库: " . $database['default']['database'] . "\n\n";

try {
    // 检查数据库连接
    $connection = Db::connect();
    echo "✅ 数据库连接成功\n\n";
    
    // 检查必要的表是否存在
    $tables = ['gvd_klines', 'gvd_trading_pairs'];
    foreach ($tables as $table) {
        $exists = Db::query("SHOW TABLES LIKE '{$table}'");
        if (empty($exists)) {
            echo "❌ 表 {$table} 不存在，请先执行数据库初始化脚本\n";
            exit(1);
        }
        echo "✅ 表 {$table} 存在\n";
    }
    echo "\n";
    
    // 获取交易对列表
    $tradingPairs = Db::name('trading_pairs')->where('is_active', 1)->column('symbol');
    if (empty($tradingPairs)) {
        echo "⚠️  没有找到活跃的交易对，使用默认交易对\n";
        $tradingPairs = ['BTCUSDT', 'ETHUSDT', 'LTCUSDT', 'EOSUSDT', 'XRPUSDT'];
    }
    
    echo "找到交易对: " . implode(', ', $tradingPairs) . "\n\n";
    
    // 支持的时间周期
    $intervals = [
        '1m' => 60,
        '5m' => 300,
        '15m' => 900,
        '1h' => 3600,
        '4h' => 14400,
        '1d' => 86400
    ];
    
    // 基础价格配置
    $basePrices = [
        'BTCUSDT' => 45000,
        'ETHUSDT' => 3000,
        'LTCUSDT' => 150,
        'EOSUSDT' => 5,
        'XRPUSDT' => 0.6
    ];
    
    echo "开始生成K线数据...\n\n";
    
    foreach ($tradingPairs as $symbol) {
        echo "处理交易对: {$symbol}\n";
        
        $basePrice = $basePrices[$symbol] ?? 100;
        
        foreach ($intervals as $interval => $seconds) {
            echo "  生成 {$interval} 周期数据...";
            
            // 检查是否已有数据
            $existingCount = Db::name('klines')
                              ->where('symbol', $symbol)
                              ->where('interval', $interval)
                              ->count();
            
            if ($existingCount > 0) {
                echo " 跳过（已有 {$existingCount} 条数据）\n";
                continue;
            }
            
            // 生成数据
            $count = generateKlineData($symbol, $interval, $seconds, $basePrice, 200);
            echo " 完成（生成 {$count} 条数据）\n";
        }
        echo "\n";
    }
    
    echo "🎉 K线数据初始化完成！\n\n";
    echo "您现在可以访问以下页面查看图表：\n";
    echo "- K线图表: http://your-domain/trade/chart.html\n";
    echo "- API测试: http://your-domain/api/kline/data?symbol=BTCUSDT&interval=1h\n\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    exit(1);
}

/**
 * 生成K线数据
 */
function generateKlineData($symbol, $interval, $intervalSeconds, $basePrice, $count)
{
    $currentTime = time();
    $data = [];
    $currentPrice = $basePrice;
    
    for ($i = $count; $i > 0; $i--) {
        $openTime = $currentTime - ($i * $intervalSeconds);
        $closeTime = $openTime + $intervalSeconds - 1;
        
        // 生成价格数据（随机波动）
        $volatility = 0.02; // 2%波动率
        $change = (mt_rand(-100, 100) / 100) * $volatility;
        
        $open = $currentPrice;
        $close = $open * (1 + $change);
        $high = max($open, $close) * (1 + mt_rand(0, 50) / 10000);
        $low = min($open, $close) * (1 - mt_rand(0, 50) / 10000);
        
        // 生成成交量（随机）
        $volume = mt_rand(100, 10000) / 100;
        $amount = $volume * (($open + $close) / 2);
        
        $data[] = [
            'symbol' => $symbol,
            'interval' => $interval,
            'open_time' => $openTime,
            'close_time' => $closeTime,
            'open_price' => round($open, 8),
            'high_price' => round($high, 8),
            'low_price' => round($low, 8),
            'close_price' => round($close, 8),
            'volume' => round($volume, 8),
            'amount' => round($amount, 8),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $currentPrice = $close; // 下一根K线的起始价格
        
        // 批量插入（每100条）
        if (count($data) >= 100) {
            Db::name('klines')->insertAll($data);
            $data = [];
        }
    }
    
    // 插入剩余数据
    if (!empty($data)) {
        Db::name('klines')->insertAll($data);
    }
    
    return $count;
}

/**
 * 清理旧数据
 */
function cleanOldData()
{
    echo "清理旧的K线数据...\n";
    $deleted = Db::name('klines')->delete(true);
    echo "删除了 {$deleted} 条旧数据\n\n";
}

// 如果传入 clean 参数，先清理旧数据
if (isset($argv[1]) && $argv[1] === 'clean') {
    cleanOldData();
}
?>
