<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\common\service\ContractService;
use app\common\service\SystemConfigService;
use think\Request;

/**
 * 合约交易控制器
 */
class Contract extends BaseController
{
    protected $contractService;
    protected $configService;

    public function __construct()
    {
        parent::__construct();
        $this->contractService = new ContractService();
        $this->configService = new SystemConfigService();
    }

    /**
     * 创建秒合约订单
     */
    public function createSecondContract(Request $request)
    {
        $data = $request->post();
        
        // 验证必填字段
        $validate = [
            'symbol' => 'require',
            'direction' => 'require|in:up,down',
            'amount' => 'require|float|gt:0',
            'time_period' => 'require|integer|gt:0'
        ];

        $result = $this->validate($data, $validate);
        if ($result !== true) {
            return $this->error($result);
        }

        $userId = $this->getUserId();
        $result = $this->contractService->createSecondContract($userId, $data);

        if ($result['code']) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取合约配置
     */
    public function getConfig(Request $request)
    {
        $symbol = $request->get('symbol', '');
        
        $result = $this->configService->getContractConfig($symbol);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取用户合约订单
     */
    public function getUserOrders(Request $request)
    {
        $userId = $this->getUserId();
        $filters = [
            'symbol' => $request->get('symbol', ''),
            'type' => $request->get('type', ''),
            'status' => $request->get('status', ''),
            'limit' => (int)$request->get('limit', 50)
        ];

        $result = $this->contractService->getUserOrders($userId, $filters);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取合约订单详情
     */
    public function getOrderDetail(Request $request)
    {
        $orderId = $request->get('order_id');
        if (empty($orderId)) {
            return $this->error('订单ID不能为空');
        }

        $userId = $this->getUserId();
        $result = $this->contractService->getOrderDetail($orderId, $userId);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取快捷金额配置
     */
    public function getQuickAmounts(Request $request)
    {
        $symbol = $request->get('symbol', 'default');
        
        $result = $this->configService->getQuickAmounts($symbol);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取用户合约统计
     */
    public function getUserStats(Request $request)
    {
        $userId = $this->getUserId();
        $symbol = $request->get('symbol', '');
        
        $result = $this->contractService->getUserStats($userId, $symbol);
        
        return $this->success($result);
    }

    /**
     * 获取今日统计
     */
    public function getTodayStats(Request $request)
    {
        $userId = $this->getUserId();
        
        $result = $this->contractService->getUserTodayStats($userId);
        
        return $this->success($result);
    }

    /**
     * 获取排行榜
     */
    public function getRanking(Request $request)
    {
        $type = $request->get('type', 'profit'); // profit, win_rate, volume
        $period = $request->get('period', 'today'); // today, week, month
        $limit = (int)$request->get('limit', 10);
        
        $result = $this->contractService->getRanking($type, $period, $limit);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取价格历史
     */
    public function getPriceHistory(Request $request)
    {
        $symbol = $request->get('symbol');
        $period = (int)$request->get('period', 60); // 秒
        $limit = (int)$request->get('limit', 100);
        
        if (empty($symbol)) {
            return $this->error('交易对不能为空');
        }
        
        $result = $this->contractService->getPriceHistory($symbol, $period, $limit);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取实时价格
     */
    public function getCurrentPrice(Request $request)
    {
        $symbol = $request->get('symbol');
        
        if (empty($symbol)) {
            return $this->error('交易对不能为空');
        }
        
        $result = $this->contractService->getCurrentPrice($symbol);
        
        if ($result['code']) {
            return $this->success($result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 手动结算订单（测试用）
     */
    public function settleOrder(Request $request)
    {
        $orderId = $request->post('order_id');
        
        if (empty($orderId)) {
            return $this->error('订单ID不能为空');
        }
        
        $result = $this->contractService->settleSecondContract($orderId);
        
        if ($result['code']) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }
}
