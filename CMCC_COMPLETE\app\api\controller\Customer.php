<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\common\service\CustomerService;
use think\facade\Validate;

/**
 * 客服API控制器
 */
class Customer extends BaseController
{
    protected $customerService;

    public function __construct(\think\App $app, CustomerService $customerService)
    {
        parent::__construct($app);
        $this->customerService = $customerService;
    }

    /**
     * 创建客服会话
     */
    public function createSession()
    {
        $userId = $this->getUserId();
        
        $result = $this->customerService->createSession($userId);
        
        if ($result['code'] == 1) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 发送消息
     */
    public function sendMessage()
    {
        $data = $this->getParams([
            'session_id', 'content', 'message_type'
        ]);

        // 添加发送者信息
        $data['sender_id'] = $this->getUserId();
        $data['sender_type'] = 'user';

        // 验证参数
        $validate = Validate::rule([
            'session_id' => 'require|length:32',
            'content' => 'require|max:1000',
            'message_type' => 'in:text,image,emoji'
        ])->message([
            'session_id.require' => '会话ID不能为空',
            'session_id.length' => '会话ID格式错误',
            'content.require' => '消息内容不能为空',
            'content.max' => '消息内容不能超过1000字符',
            'message_type.in' => '消息类型错误'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        // 处理图片上传
        if ($data['message_type'] === 'image') {
            $file = $this->request->file('image');
            if ($file) {
                $data['file'] = $file;
            } else {
                return $this->error('请上传图片文件');
            }
        }

        $result = $this->customerService->sendMessage($data);
        
        if ($result['code'] == 1) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取会话消息列表
     */
    public function getMessages()
    {
        $sessionId = $this->request->param('session_id');
        $page = $this->request->param('page/d', 1);
        $limit = $this->request->param('limit/d', 50);

        if (empty($sessionId)) {
            return $this->error('会话ID不能为空');
        }

        // 验证会话权限
        if (!$this->validateSessionAccess($sessionId)) {
            return $this->error('无权限访问此会话');
        }

        $result = $this->customerService->getSessionMessages($sessionId, $page, $limit);
        
        if ($result['code'] == 1) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取用户会话列表
     */
    public function getSessions()
    {
        $userId = $this->getUserId();
        
        $result = $this->customerService->getUserSessions($userId, 'user');
        
        if ($result['code'] == 1) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 标记消息为已读
     */
    public function markAsRead()
    {
        $sessionId = $this->request->param('session_id');
        
        if (empty($sessionId)) {
            return $this->error('会话ID不能为空');
        }

        // 验证会话权限
        if (!$this->validateSessionAccess($sessionId)) {
            return $this->error('无权限访问此会话');
        }

        $result = $this->customerService->markMessagesAsRead(
            $sessionId, 
            $this->getUserId(), 
            'user'
        );
        
        if ($result['code'] == 1) {
            return $this->success([], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 上传图片
     */
    public function uploadImage()
    {
        $file = $this->request->file('image');
        
        if (!$file) {
            return $this->error('请选择图片文件');
        }

        // 验证文件类型
        $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $extension = strtolower($file->getOriginalExtension());
        
        if (!in_array($extension, $allowedTypes)) {
            return $this->error('只支持JPG、PNG、GIF、WebP格式的图片');
        }

        // 验证文件大小 (5MB)
        if ($file->getSize() > 5 * 1024 * 1024) {
            return $this->error('图片大小不能超过5MB');
        }

        try {
            // 生成文件名
            $fileName = date('YmdHis') . '_' . uniqid() . '.' . $extension;
            $uploadPath = 'uploads/customer/images/' . date('Y/m/d') . '/';
            
            // 创建目录
            $fullPath = public_path() . $uploadPath;
            if (!is_dir($fullPath)) {
                mkdir($fullPath, 0755, true);
            }

            // 保存文件
            $file->move($fullPath, $fileName);
            
            $fileUrl = '/' . $uploadPath . $fileName;
            
            return $this->success([
                'url' => $fileUrl,
                'name' => $file->getOriginalName(),
                'size' => $file->getSize()
            ], '图片上传成功');

        } catch (\Exception $e) {
            return $this->error('图片上传失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取表情包列表
     */
    public function getEmojis()
    {
        // 系统emoji分类
        $emojiCategories = [
            'smileys' => [
                'name' => '笑脸',
                'emojis' => ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳']
            ],
            'emotions' => [
                'name' => '情感',
                'emojis' => ['😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔']
            ],
            'gestures' => [
                'name' => '手势',
                'emojis' => ['👍', '👎', '👌', '🤌', '🤏', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '☝️', '👋', '🤚', '🖐️', '✋', '🖖', '👏', '🙌', '🤲', '🤝', '🙏', '✍️', '💪', '🦾', '🦿']
            ],
            'objects' => [
                'name' => '物品',
                'emojis' => ['💰', '💎', '💳', '💸', '📱', '💻', '⌨️', '🖥️', '🖨️', '📊', '📈', '📉', '💹', '🔔', '🔕', '📢', '📣', '📯', '🎯', '🎪', '🎭', '🎨', '🎬', '🎤', '🎧', '🎼', '🎵', '🎶', '🎹', '🥁']
            ]
        ];

        return $this->success($emojiCategories, '获取成功');
    }

    /**
     * 获取在线状态
     */
    public function getOnlineStatus()
    {
        $sessionId = $this->request->param('session_id');
        
        if (empty($sessionId)) {
            return $this->error('会话ID不能为空');
        }

        // 获取会话信息
        $session = \think\facade\Db::name('customer_sessions')
            ->where('session_id', $sessionId)
            ->find();

        if (!$session) {
            return $this->error('会话不存在');
        }

        $onlineStatus = [];

        // 获取代理在线状态
        if ($session['agent_id']) {
            $agentStatus = \think\facade\Db::name('customer_online_status')
                ->where('user_id', $session['agent_id'])
                ->where('user_type', 'agent')
                ->find();

            $onlineStatus['agent'] = [
                'user_id' => $session['agent_id'],
                'status' => $agentStatus['status'] ?? 'offline',
                'last_seen' => $agentStatus['last_seen'] ?? null
            ];
        }

        // 获取管理员在线状态
        $adminCount = \think\facade\Db::name('customer_online_status')
            ->where('user_type', 'admin')
            ->where('status', 'online')
            ->count();

        $onlineStatus['admin_count'] = $adminCount;

        return $this->success($onlineStatus, '获取成功');
    }

    /**
     * 验证会话访问权限
     */
    private function validateSessionAccess(string $sessionId): bool
    {
        $session = \think\facade\Db::name('customer_sessions')
            ->where('session_id', $sessionId)
            ->where('user_id', $this->getUserId())
            ->find();

        return !empty($session);
    }
}
