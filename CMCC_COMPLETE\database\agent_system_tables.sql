-- 代理系统相关表结构
-- 需要添加到主数据库中

-- 代理佣金记录表
CREATE TABLE `gvd_agent_commission` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `agent_id` int(11) NOT NULL COMMENT '代理ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `order_id` int(11) DEFAULT NULL COMMENT '订单ID',
  `order_type` varchar(20) NOT NULL DEFAULT 'futures' COMMENT '订单类型:futures,spot,leverage',
  `commission_type` varchar(20) NOT NULL COMMENT '佣金类型:trade,deposit,withdraw',
  `amount` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '佣金金额',
  `commission_rate` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '佣金比例',
  `original_amount` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '原始金额',
  `coin_symbol` varchar(10) NOT NULL DEFAULT 'USDT' COMMENT '币种',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0=待发放,1=已发放,2=已取消',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代理佣金记录表';

-- 代理操作日志表
CREATE TABLE `gvd_agent_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `agent_id` int(11) NOT NULL COMMENT '代理ID',
  `action` varchar(50) NOT NULL COMMENT '操作类型',
  `target_type` varchar(20) DEFAULT NULL COMMENT '目标类型:user,order,commission',
  `target_id` int(11) DEFAULT NULL COMMENT '目标ID',
  `content` text COMMENT '操作内容',
  `data` text COMMENT '操作数据JSON',
  `ip` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代理操作日志表';

-- 代理配置表
CREATE TABLE `gvd_agent_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `agent_id` int(11) NOT NULL COMMENT '代理ID',
  `config_key` varchar(50) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agent_key` (`agent_id`,`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代理配置表';

-- 代理团队统计表（缓存表）
CREATE TABLE `gvd_agent_team_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `agent_id` int(11) NOT NULL COMMENT '代理ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `total_users` int(11) NOT NULL DEFAULT '0' COMMENT '总用户数',
  `new_users` int(11) NOT NULL DEFAULT '0' COMMENT '新增用户数',
  `active_users` int(11) NOT NULL DEFAULT '0' COMMENT '活跃用户数',
  `total_orders` int(11) NOT NULL DEFAULT '0' COMMENT '总订单数',
  `total_volume` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '总交易量',
  `total_commission` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '总佣金',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agent_date` (`agent_id`,`stat_date`),
  KEY `idx_stat_date` (`stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代理团队统计表';

-- 代理提现记录表
CREATE TABLE `gvd_agent_withdraws` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `agent_id` int(11) NOT NULL COMMENT '代理ID',
  `amount` decimal(20,8) NOT NULL COMMENT '提现金额',
  `coin_symbol` varchar(10) NOT NULL DEFAULT 'USDT' COMMENT '币种',
  `withdraw_address` varchar(255) NOT NULL COMMENT '提现地址',
  `fee` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '手续费',
  `actual_amount` decimal(20,8) NOT NULL COMMENT '实际到账金额',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态:0=待审核,1=处理中,2=已完成,3=已拒绝',
  `tx_hash` varchar(100) DEFAULT NULL COMMENT '交易哈希',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `admin_id` int(11) DEFAULT NULL COMMENT '处理管理员ID',
  `processed_at` datetime DEFAULT NULL COMMENT '处理时间',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代理提现记录表';

-- 插入默认代理配置
INSERT INTO `gvd_agent_config` (`agent_id`, `config_key`, `config_value`, `description`, `created_at`) VALUES
(0, 'default_commission_rate', '0.0010', '默认佣金比例', NOW()),
(0, 'min_withdraw_amount', '100.00000000', '最小提现金额', NOW()),
(0, 'withdraw_fee_rate', '0.0050', '提现手续费比例', NOW()),
(0, 'max_control_orders', '10', '最大可控制订单数', NOW());
