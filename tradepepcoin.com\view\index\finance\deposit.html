{extend name="index/layout" /}

{block name="css"}
<style>
.deposit-container {
    padding: 20px 0;
    background: #f8f9fa;
    min-height: 100vh;
}

.deposit-header {
    background: white;
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.coin-selector {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.coin-option {
    padding: 12px 20px;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s;
    background: white;
}

.coin-option.active {
    border-color: #667eea;
    background: #667eea;
    color: white;
}

.deposit-main {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.deposit-address {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
}

.qr-code {
    width: 200px;
    height: 200px;
    margin: 20px auto;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
}

.address-text {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    word-break: break-all;
    font-family: monospace;
    margin: 20px 0;
    position: relative;
}

.copy-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 5px 10px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
}

.deposit-info {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #f8f9fa;
}

.info-label {
    color: #666;
}

.info-value {
    font-weight: 600;
}

.deposit-notice {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}

.notice-title {
    font-weight: bold;
    color: #856404;
    margin-bottom: 10px;
}

.notice-list {
    color: #856404;
    margin: 0;
    padding-left: 20px;
}

.deposit-records {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.record-item {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr auto;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #f8f9fa;
    align-items: center;
}

.record-header {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr auto;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 2px solid #e9ecef;
    font-weight: 600;
    color: #333;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-failed {
    background: #f8d7da;
    color: #721c24;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
    margin-top: 5px;
}

.progress-fill {
    height: 100%;
    background: #28a745;
    transition: width 0.3s;
}

@media (max-width: 768px) {
    .deposit-main {
        grid-template-columns: 1fr;
    }
    
    .record-item,
    .record-header {
        grid-template-columns: 1fr;
        gap: 5px;
    }
}
</style>
{/block}

{block name="content"}
<div class="deposit-container">
    <div class="container">
        <!-- 充值头部 -->
        <div class="deposit-header">
            <h3>充值 {$coin_symbol}</h3>
            <p class="text-muted">选择要充值的币种，获取充值地址</p>
            
            <div class="coin-selector">
                <div class="coin-option {if condition='$coin_symbol == "USDT"'}active{/if}" data-coin="USDT">
                    USDT
                </div>
                <div class="coin-option {if condition='$coin_symbol == "BTC"'}active{/if}" data-coin="BTC">
                    BTC
                </div>
                <div class="coin-option {if condition='$coin_symbol == "ETH"'}active{/if}" data-coin="ETH">
                    ETH
                </div>
            </div>
        </div>

        <!-- 充值主体 -->
        <div class="deposit-main">
            <!-- 充值地址 -->
            <div class="deposit-address">
                <h5>充值地址</h5>
                <p class="text-muted">扫描二维码或复制地址进行充值</p>
                
                <div class="qr-code">
                    <img src="{$deposit_address.qr_code|default='/static/images/qr-placeholder.png'}" 
                         alt="充值二维码" style="max-width: 100%; max-height: 100%;">
                </div>
                
                <div class="address-text">
                    {$deposit_address.address|default='地址生成中...'}
                    <button class="copy-btn" onclick="copyAddress()">
                        <i class="fas fa-copy"></i> 复制
                    </button>
                </div>
                
                {if condition="$deposit_address.memo"}
                <div class="memo-text">
                    <strong>Memo:</strong> {$deposit_address.memo}
                    <button class="copy-btn" onclick="copyMemo()">
                        <i class="fas fa-copy"></i> 复制
                    </button>
                </div>
                {/if}
            </div>

            <!-- 充值信息 -->
            <div class="deposit-info">
                <h5>充值信息</h5>
                
                <div class="info-item">
                    <span class="info-label">币种</span>
                    <span class="info-value">{$coin_symbol}</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">网络</span>
                    <span class="info-value">{$deposit_config.network|default='主网'}</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">最小充值金额</span>
                    <span class="info-value">{$deposit_config.min_amount|default=0} {$coin_symbol}</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">确认数</span>
                    <span class="info-value">{$deposit_config.confirmations|default=12} 个确认</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">充值手续费</span>
                    <span class="info-value">{$deposit_config.fee|default=0} {$coin_symbol}</span>
                </div>
            </div>
        </div>

        <!-- 充值须知 -->
        <div class="deposit-notice">
            <div class="notice-title">
                <i class="fas fa-exclamation-triangle"></i> 充值须知
            </div>
            <ul class="notice-list">
                <li>请确保充值地址正确，错误地址将导致资产丢失</li>
                <li>最小充值金额为 {$deposit_config.min_amount|default=0} {$coin_symbol}，低于此金额将不会到账</li>
                <li>充值需要 {$deposit_config.confirmations|default=12} 个网络确认，请耐心等待</li>
                <li>请勿向该地址充值其他币种，否则资产将无法找回</li>
                <li>充值地址仅支持 {$deposit_config.network|default='主网'} 网络</li>
            </ul>
        </div>

        <!-- 充值记录 -->
        <div class="deposit-records">
            <h5 class="mb-4">最近充值记录</h5>
            
            <div class="record-header">
                <span>金额</span>
                <span>状态</span>
                <span>确认进度</span>
                <span>时间</span>
                <span>操作</span>
            </div>
            
            {volist name="deposit_records" id="record"}
            <div class="record-item">
                <span>{$record.amount|number_format:6} {$record.coin_symbol}</span>
                <span>
                    <span class="status-badge status-{$record.status}">{$record.status_text}</span>
                </span>
                <span>
                    {$record.confirmations}/{$record.required_confirmations}
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {$record.confirm_progress}%"></div>
                    </div>
                </span>
                <span>{$record.created_at|date='m-d H:i'}</span>
                <span>
                    {if condition="$record.txid"}
                    <a href="javascript:void(0)" onclick="viewTx('{$record.txid}')" class="btn btn-sm btn-outline-primary">
                        查看
                    </a>
                    {/if}
                </span>
            </div>
            {/volist}
            
            {if condition="count($deposit_records) == 0"}
            <div class="text-center py-4 text-muted">
                暂无充值记录
            </div>
            {/if}
        </div>
    </div>
</div>
{/block}

{block name="js"}
<script>
$(document).ready(function() {
    // 币种切换
    $('.coin-option').click(function() {
        const coin = $(this).data('coin');
        window.location.href = '/finance/deposit?coin=' + coin;
    });
    
    // 定时刷新充值状态
    setInterval(function() {
        location.reload();
    }, 30000); // 30秒刷新一次
});

// 复制地址
function copyAddress() {
    const address = '{$deposit_address.address|default=""}';
    if (address) {
        navigator.clipboard.writeText(address).then(function() {
            alert('地址已复制到剪贴板');
        });
    }
}

// 复制Memo
function copyMemo() {
    const memo = '{$deposit_address.memo|default=""}';
    if (memo) {
        navigator.clipboard.writeText(memo).then(function() {
            alert('Memo已复制到剪贴板');
        });
    }
}

// 查看交易
function viewTx(txid) {
    // 这里可以打开区块链浏览器
    const url = 'https://tronscan.org/#/transaction/' + txid;
    window.open(url, '_blank');
}
</script>
{/block}
