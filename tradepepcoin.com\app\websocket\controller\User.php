<?php
declare (strict_types = 1);

namespace app\websocket\controller;

use think\swoole\websocket\Room;
use app\common\model\User as UserModel;
use app\common\model\Order;
use app\common\model\UserAsset;

/**
 * WebSocket用户数据推送控制器
 */
class User
{
    /**
     * 用户连接映射
     */
    private static $userConnections = [];

    /**
     * 用户认证
     */
    public function authenticate($server, $fd, $data)
    {
        if (!isset($data['token'])) {
            $this->sendError($server, $fd, 'Token required');
            return false;
        }

        // 验证token（这里简化处理）
        $userId = $this->validateToken($data['token']);
        if (!$userId) {
            $this->sendError($server, $fd, 'Invalid token');
            return false;
        }

        // 建立用户连接映射
        self::$userConnections[$fd] = $userId;
        
        // 加入用户专属房间
        Room::add($fd, "user_{$userId}");

        $this->sendResponse($server, $fd, [
            'event' => 'authenticated',
            'data' => [
                'user_id' => $userId,
                'message' => '认证成功'
            ]
        ]);

        // 发送用户初始数据
        $this->sendUserInitialData($server, $fd, $userId);

        return true;
    }

    /**
     * 订阅用户数据
     */
    public function subscribe($server, $fd, $data)
    {
        $userId = self::$userConnections[$fd] ?? null;
        if (!$userId) {
            $this->sendError($server, $fd, 'Not authenticated');
            return;
        }

        $streams = $data['streams'] ?? [];
        $subscribed = [];

        foreach ($streams as $stream) {
            if ($this->isValidUserStream($stream)) {
                Room::add($fd, "user_{$userId}_{$stream}");
                $subscribed[] = $stream;
            }
        }

        $this->sendResponse($server, $fd, [
            'event' => 'subscribed',
            'data' => [
                'streams' => $subscribed
            ]
        ]);
    }

    /**
     * 发送用户初始数据
     */
    private function sendUserInitialData($server, $fd, $userId)
    {
        // 发送用户资产
        $this->sendUserAssets($server, $fd, $userId);
        
        // 发送用户订单
        $this->sendUserOrders($server, $fd, $userId);
    }

    /**
     * 发送用户资产
     */
    private function sendUserAssets($server, $fd, $userId)
    {
        $assets = UserAsset::where('user_id', $userId)
                          ->where('total', '>', 0)
                          ->with('coin')
                          ->select();

        $data = [];
        foreach ($assets as $asset) {
            $data[] = [
                'coin' => $asset->coin_symbol,
                'available' => $asset->available,
                'frozen' => $asset->frozen,
                'total' => $asset->total
            ];
        }

        $server->push($fd, json_encode([
            'event' => 'user_assets',
            'data' => $data
        ]));
    }

    /**
     * 发送用户订单
     */
    private function sendUserOrders($server, $fd, $userId)
    {
        $orders = Order::where('user_id', $userId)
                      ->where('status', 'in', [0, 1])
                      ->order('created_at', 'desc')
                      ->limit(50)
                      ->select();

        $data = [];
        foreach ($orders as $order) {
            $data[] = [
                'order_id' => $order->order_id,
                'symbol' => $order->symbol,
                'type' => $order->type,
                'order_type' => $order->order_type,
                'amount' => $order->amount,
                'price' => $order->price,
                'filled_amount' => $order->filled_amount,
                'status' => $order->status,
                'created_at' => $order->created_at
            ];
        }

        $server->push($fd, json_encode([
            'event' => 'user_orders',
            'data' => $data
        ]));
    }

    /**
     * 验证用户流
     */
    private function isValidUserStream($stream)
    {
        $validStreams = [
            'assets',      // 资产变动
            'orders',      // 订单状态
            'trades',      // 成交记录
            'deposits',    // 充值记录
            'withdrawals', // 提现记录
            'commissions'  // 佣金记录
        ];

        return in_array($stream, $validStreams);
    }

    /**
     * 验证token
     */
    private function validateToken($token)
    {
        // 这里应该实现真正的token验证逻辑
        // 简化处理，直接返回用户ID
        if ($token === 'demo_token') {
            return 1;
        }
        
        return false;
    }

    /**
     * 发送响应
     */
    private function sendResponse($server, $fd, $data)
    {
        $server->push($fd, json_encode($data));
    }

    /**
     * 发送错误
     */
    private function sendError($server, $fd, $message, $code = -1)
    {
        $server->push($fd, json_encode([
            'error' => [
                'code' => $code,
                'msg' => $message
            ]
        ]));
    }

    /**
     * 广播用户资产更新
     */
    public static function broadcastAssetUpdate($userId, $coinSymbol, $asset)
    {
        $data = [
            'event' => 'asset_update',
            'data' => [
                'coin' => $coinSymbol,
                'available' => $asset->available,
                'frozen' => $asset->frozen,
                'total' => $asset->total,
                'timestamp' => time() * 1000
            ]
        ];

        Room::broadcast("user_{$userId}", json_encode($data));
        Room::broadcast("user_{$userId}_assets", json_encode($data));
    }

    /**
     * 广播订单状态更新
     */
    public static function broadcastOrderUpdate($userId, $order)
    {
        $data = [
            'event' => 'order_update',
            'data' => [
                'order_id' => $order->order_id,
                'symbol' => $order->symbol,
                'type' => $order->type,
                'order_type' => $order->order_type,
                'amount' => $order->amount,
                'price' => $order->price,
                'filled_amount' => $order->filled_amount,
                'filled_total' => $order->filled_total,
                'avg_price' => $order->avg_price,
                'status' => $order->status,
                'updated_at' => $order->updated_at,
                'timestamp' => time() * 1000
            ]
        ];

        Room::broadcast("user_{$userId}", json_encode($data));
        Room::broadcast("user_{$userId}_orders", json_encode($data));
    }

    /**
     * 广播成交记录
     */
    public static function broadcastTradeUpdate($userId, $trade)
    {
        $data = [
            'event' => 'trade_update',
            'data' => [
                'trade_id' => $trade->trade_id,
                'order_id' => $trade->order_id,
                'symbol' => $trade->symbol,
                'type' => $trade->type,
                'amount' => $trade->amount,
                'price' => $trade->price,
                'total' => $trade->total,
                'fee' => $trade->fee,
                'fee_coin' => $trade->fee_coin,
                'created_at' => $trade->created_at,
                'timestamp' => time() * 1000
            ]
        ];

        Room::broadcast("user_{$userId}", json_encode($data));
        Room::broadcast("user_{$userId}_trades", json_encode($data));
    }

    /**
     * 广播充值状态更新
     */
    public static function broadcastDepositUpdate($userId, $deposit)
    {
        $data = [
            'event' => 'deposit_update',
            'data' => [
                'id' => $deposit->id,
                'coin_symbol' => $deposit->coin_symbol,
                'amount' => $deposit->amount,
                'txid' => $deposit->txid,
                'confirmations' => $deposit->confirmations,
                'required_confirmations' => $deposit->required_confirmations,
                'status' => $deposit->status,
                'created_at' => $deposit->created_at,
                'confirmed_at' => $deposit->confirmed_at,
                'timestamp' => time() * 1000
            ]
        ];

        Room::broadcast("user_{$userId}", json_encode($data));
        Room::broadcast("user_{$userId}_deposits", json_encode($data));
    }

    /**
     * 广播提现状态更新
     */
    public static function broadcastWithdrawUpdate($userId, $withdraw)
    {
        $data = [
            'event' => 'withdraw_update',
            'data' => [
                'id' => $withdraw->id,
                'coin_symbol' => $withdraw->coin_symbol,
                'amount' => $withdraw->amount,
                'fee' => $withdraw->fee,
                'actual_amount' => $withdraw->actual_amount,
                'address' => $withdraw->address,
                'txid' => $withdraw->txid,
                'status' => $withdraw->status,
                'created_at' => $withdraw->created_at,
                'processed_at' => $withdraw->processed_at,
                'timestamp' => time() * 1000
            ]
        ];

        Room::broadcast("user_{$userId}", json_encode($data));
        Room::broadcast("user_{$userId}_withdrawals", json_encode($data));
    }

    /**
     * 广播佣金记录
     */
    public static function broadcastCommissionUpdate($userId, $commission)
    {
        $data = [
            'event' => 'commission_update',
            'data' => [
                'id' => $commission->id,
                'type' => $commission->type,
                'level' => $commission->level,
                'rate' => $commission->rate,
                'amount' => $commission->amount,
                'coin_symbol' => $commission->coin_symbol,
                'status' => $commission->status,
                'created_at' => $commission->created_at,
                'timestamp' => time() * 1000
            ]
        ];

        Room::broadcast("user_{$userId}", json_encode($data));
        Room::broadcast("user_{$userId}_commissions", json_encode($data));
    }

    /**
     * 连接关闭时清理
     */
    public static function onClose($fd)
    {
        if (isset(self::$userConnections[$fd])) {
            $userId = self::$userConnections[$fd];
            
            // 清理用户房间
            Room::del($fd, "user_{$userId}");
            
            // 清理用户流订阅
            $streams = ['assets', 'orders', 'trades', 'deposits', 'withdrawals', 'commissions'];
            foreach ($streams as $stream) {
                Room::del($fd, "user_{$userId}_{$stream}");
            }
            
            // 移除连接映射
            unset(self::$userConnections[$fd]);
        }
    }
}
