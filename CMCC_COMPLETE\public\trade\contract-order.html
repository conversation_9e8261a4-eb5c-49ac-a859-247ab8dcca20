<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合约交易下单 - GVD</title>
    <link href="https://cdn.jsdelivr.net/npm/element-plus@2.4.4/dist/index.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #0b0e11;
            color: #ffffff;
        }

        .trading-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 20px;
        }

        .chart-section {
            background: #1e2329;
            border-radius: 8px;
            padding: 20px;
            min-height: 600px;
        }

        .order-panel {
            background: #1e2329;
            border-radius: 8px;
            padding: 20px;
        }

        .panel-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #f0b90b;
        }

        .price-display {
            background: #2b3139;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .current-price {
            font-size: 24px;
            font-weight: bold;
            color: #02c076;
            margin-bottom: 5px;
        }

        .price-change {
            font-size: 14px;
            color: #848e9c;
        }

        .order-form {
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            color: #b7bdc6;
            font-size: 14px;
        }

        .form-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #2b3139;
            border-radius: 4px;
            background: #2b3139;
            color: #ffffff;
            font-size: 14px;
        }

        .form-input:focus {
            outline: none;
            border-color: #f0b90b;
        }

        .direction-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 20px;
        }

        .direction-btn {
            padding: 12px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .direction-btn.buy {
            background: #02c076;
            color: white;
        }

        .direction-btn.sell {
            background: #f84960;
            color: white;
        }

        .direction-btn:hover {
            opacity: 0.8;
        }

        .direction-btn.active {
            box-shadow: 0 0 10px rgba(240, 185, 11, 0.5);
        }

        .submit-btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 4px;
            background: #f0b90b;
            color: #000;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .submit-btn:hover {
            background: #fcd535;
        }

        .submit-btn:disabled {
            background: #2b3139;
            color: #848e9c;
            cursor: not-allowed;
        }

        /* 订单详情弹窗 */
        .order-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .order-modal.hidden {
            display: none;
        }

        .modal-content {
            background: #1e2329;
            border-radius: 12px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            position: relative;
            border: 2px solid #f0b90b;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .modal-title {
            font-size: 20px;
            font-weight: bold;
            color: #f0b90b;
        }

        .close-btn {
            background: none;
            border: none;
            color: #848e9c;
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .close-btn:hover {
            background: #2b3139;
            color: #ffffff;
        }

        .order-info {
            margin-bottom: 25px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #2b3139;
        }

        .info-label {
            color: #b7bdc6;
            font-size: 14px;
        }

        .info-value {
            color: #ffffff;
            font-weight: bold;
        }

        .countdown-section {
            text-align: center;
            margin-bottom: 25px;
        }

        .countdown-timer {
            font-size: 48px;
            font-weight: bold;
            color: #f0b90b;
            margin-bottom: 10px;
            font-family: 'Courier New', monospace;
        }

        .countdown-label {
            color: #b7bdc6;
            font-size: 14px;
        }

        .price-section {
            background: #2b3139;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .price-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .price-row:last-child {
            margin-bottom: 0;
        }

        .price-label {
            color: #b7bdc6;
            font-size: 14px;
        }

        .price-value {
            font-size: 18px;
            font-weight: bold;
        }

        .open-price {
            color: #409eff;
        }

        .close-price {
            color: #848e9c;
        }

        .close-price.win {
            color: #02c076;
        }

        .close-price.lose {
            color: #f84960;
        }

        .result-section {
            text-align: center;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .result-section.win {
            background: linear-gradient(135deg, rgba(2, 192, 118, 0.1), rgba(2, 192, 118, 0.2));
            border: 1px solid #02c076;
        }

        .result-section.lose {
            background: linear-gradient(135deg, rgba(248, 73, 96, 0.1), rgba(248, 73, 96, 0.2));
            border: 1px solid #f84960;
        }

        .result-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }

        .result-text {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .result-amount {
            font-size: 18px;
        }

        .result-section.win .result-text,
        .result-section.win .result-amount {
            color: #02c076;
        }

        .result-section.lose .result-text,
        .result-section.lose .result-amount {
            color: #f84960;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-waiting {
            background: #f0b90b;
            animation: pulse 2s infinite;
        }

        .status-finished {
            background: #02c076;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .hidden {
            display: none !important;
        }

        @media (max-width: 768px) {
            .trading-container {
                grid-template-columns: 1fr;
                padding: 10px;
            }

            .modal-content {
                padding: 20px;
                margin: 20px;
            }

            .countdown-timer {
                font-size: 36px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="trading-container">
            <!-- K线图区域 -->
            <div class="chart-section">
                <h3 class="panel-title">📈 BTC/USDT 实时K线</h3>
                <div id="kline-chart" style="width: 100%; height: 500px; background: #0b0e11; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #848e9c;">
                    K线图加载中...
                </div>
            </div>

            <!-- 下单面板 -->
            <div class="order-panel">
                <h3 class="panel-title">⚡ 合约交易</h3>
                
                <!-- 当前价格显示 -->
                <div class="price-display">
                    <div class="current-price">${{ currentPrice.toFixed(2) }}</div>
                    <div class="price-change" :class="priceChange >= 0 ? 'text-green' : 'text-red'">
                        {{ priceChange >= 0 ? '+' : '' }}{{ priceChange.toFixed(2) }}%
                    </div>
                </div>

                <!-- 下单表单 -->
                <div class="order-form">
                    <div class="form-group">
                        <label class="form-label">交易对</label>
                        <select class="form-input" v-model="orderForm.symbol">
                            <option value="BTC/USDT">BTC/USDT</option>
                            <option value="ETH/USDT">ETH/USDT</option>
                            <option value="BNB/USDT">BNB/USDT</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">下单金额 (USDT)</label>
                        <input type="number" class="form-input" v-model="orderForm.amount" placeholder="输入下单金额" min="10" step="1">
                    </div>

                    <div class="form-group">
                        <label class="form-label">交易时长</label>
                        <select class="form-input" v-model="orderForm.duration">
                            <option value="60">1分钟</option>
                            <option value="300">5分钟</option>
                            <option value="900">15分钟</option>
                            <option value="1800">30分钟</option>
                        </select>
                    </div>

                    <!-- 方向选择 -->
                    <div class="direction-buttons">
                        <button 
                            class="direction-btn buy" 
                            :class="{ active: orderForm.direction === 'buy' }"
                            @click="orderForm.direction = 'buy'">
                            📈 买涨
                        </button>
                        <button 
                            class="direction-btn sell" 
                            :class="{ active: orderForm.direction === 'sell' }"
                            @click="orderForm.direction = 'sell'">
                            📉 买跌
                        </button>
                    </div>

                    <!-- 提交按钮 -->
                    <button 
                        class="submit-btn" 
                        @click="submitOrder" 
                        :disabled="!canSubmit || submitting">
                        {{ submitting ? '下单中...' : '确认下单' }}
                    </button>
                </div>

                <!-- 账户余额 -->
                <div class="price-display">
                    <div style="font-size: 14px; color: #b7bdc6; margin-bottom: 5px;">可用余额</div>
                    <div style="font-size: 18px; font-weight: bold;">{{ userBalance.toFixed(2) }} USDT</div>
                </div>
            </div>
        </div>

        <!-- 订单详情弹窗 -->
        <div class="order-modal" :class="{ hidden: !showOrderModal }">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">
                        <span class="status-indicator" :class="orderStatus === 'waiting' ? 'status-waiting' : 'status-finished'"></span>
                        订单详情 #{{ currentOrder.id }}
                    </h3>
                    <button class="close-btn" @click="closeOrderModal">×</button>
                </div>

                <!-- 订单基本信息 -->
                <div class="order-info">
                    <div class="info-row">
                        <span class="info-label">交易对</span>
                        <span class="info-value">{{ currentOrder.symbol }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">方向</span>
                        <span class="info-value" :style="{ color: currentOrder.direction === 'buy' ? '#02c076' : '#f84960' }">
                            {{ currentOrder.direction === 'buy' ? '📈 买涨' : '📉 买跌' }}
                        </span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">下单金额</span>
                        <span class="info-value">{{ currentOrder.amount }} USDT</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">交易时长</span>
                        <span class="info-value">{{ getDurationText(currentOrder.duration) }}</span>
                    </div>
                </div>

                <!-- 倒计时 -->
                <div class="countdown-section" v-if="orderStatus === 'waiting'">
                    <div class="countdown-timer">{{ formatTime(remainingTime) }}</div>
                    <div class="countdown-label">距离结算还有</div>
                </div>

                <!-- 价格信息 -->
                <div class="price-section">
                    <div class="price-row">
                        <span class="price-label">开盘价</span>
                        <span class="price-value open-price">${{ currentOrder.openPrice.toFixed(2) }}</span>
                    </div>
                    <div class="price-row">
                        <span class="price-label">收盘价</span>
                        <span class="price-value close-price" :class="getClosePriceClass()">
                            {{ orderStatus === 'finished' ? '$' + currentOrder.closePrice.toFixed(2) : '等待结算...' }}
                        </span>
                    </div>
                </div>

                <!-- 结算结果 -->
                <div v-if="orderStatus === 'finished'" class="result-section" :class="orderResult">
                    <div class="result-icon">{{ orderResult === 'win' ? '🎉' : '😔' }}</div>
                    <div class="result-text">{{ orderResult === 'win' ? '恭喜您！交易成功' : '很遗憾，交易失败' }}</div>
                    <div class="result-amount">
                        {{ orderResult === 'win' ? '+' : '-' }}{{ Math.abs(currentOrder.pnl).toFixed(2) }} USDT
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入Vue -->
    <script src="https://cdn.jsdelivr.net/npm/vue@3.3.8/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-plus@2.4.4/dist/index.full.js"></script>

    <script>
        const { createApp } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            data() {
                return {
                    // 当前价格
                    currentPrice: 43250.50,
                    priceChange: 2.45,
                    
                    // 用户余额
                    userBalance: 1000.00,
                    
                    // 下单表单
                    orderForm: {
                        symbol: 'BTC/USDT',
                        amount: 100,
                        direction: 'buy',
                        duration: 60
                    },
                    
                    // 提交状态
                    submitting: false,
                    
                    // 订单弹窗
                    showOrderModal: false,
                    orderStatus: 'waiting', // waiting, finished
                    remainingTime: 0,
                    countdownTimer: null,
                    
                    // 当前订单
                    currentOrder: {
                        id: '',
                        symbol: '',
                        direction: '',
                        amount: 0,
                        duration: 0,
                        openPrice: 0,
                        closePrice: 0,
                        pnl: 0,
                        createdAt: ''
                    },
                    
                    // 订单结果
                    orderResult: 'win' // win, lose
                };
            },
            computed: {
                canSubmit() {
                    return this.orderForm.amount >= 10 && 
                           this.orderForm.amount <= this.userBalance && 
                           this.orderForm.direction;
                }
            },
            mounted() {
                this.startPriceUpdate();
                this.initKlineChart();
            },
            beforeUnmount() {
                if (this.countdownTimer) {
                    clearInterval(this.countdownTimer);
                }
            },
            methods: {
                // 提交订单
                async submitOrder() {
                    if (!this.canSubmit) {
                        ElMessage.error('请检查下单信息');
                        return;
                    }

                    this.submitting = true;
                    
                    try {
                        // 模拟API调用
                        await this.mockApiCall(1000);
                        
                        // 创建订单
                        this.currentOrder = {
                            id: 'ORD' + Date.now(),
                            symbol: this.orderForm.symbol,
                            direction: this.orderForm.direction,
                            amount: this.orderForm.amount,
                            duration: this.orderForm.duration,
                            openPrice: this.currentPrice,
                            closePrice: 0,
                            pnl: 0,
                            createdAt: new Date().toLocaleString()
                        };
                        
                        // 扣除余额
                        this.userBalance -= this.orderForm.amount;
                        
                        // 显示订单弹窗
                        this.showOrderModal = true;
                        this.orderStatus = 'waiting';
                        this.remainingTime = this.orderForm.duration;
                        
                        // 开始倒计时
                        this.startCountdown();
                        
                        // 通知代理端（实际项目中通过WebSocket）
                        this.notifyAgent();
                        
                        ElMessage.success('下单成功！');
                        
                    } catch (error) {
                        ElMessage.error('下单失败：' + error.message);
                    } finally {
                        this.submitting = false;
                    }
                },
                
                // 开始倒计时
                startCountdown() {
                    this.countdownTimer = setInterval(() => {
                        this.remainingTime--;
                        
                        if (this.remainingTime <= 0) {
                            this.finishOrder();
                        }
                    }, 1000);
                },
                
                // 完成订单
                async finishOrder() {
                    if (this.countdownTimer) {
                        clearInterval(this.countdownTimer);
                        this.countdownTimer = null;
                    }
                    
                    try {
                        // 调用结算API
                        const result = await this.settleOrder(this.currentOrder.id);
                        
                        // 更新订单信息
                        this.currentOrder.closePrice = result.closePrice;
                        this.currentOrder.pnl = result.pnl;
                        this.orderResult = result.pnl >= 0 ? 'win' : 'lose';
                        this.orderStatus = 'finished';
                        
                        // 更新用户余额
                        if (result.pnl > 0) {
                            this.userBalance += this.currentOrder.amount + result.pnl;
                        }
                        // 如果亏损，钱已经在下单时扣除了
                        
                        ElMessage.success('订单已结算');
                        
                    } catch (error) {
                        ElMessage.error('结算失败：' + error.message);
                    }
                },
                
                // 结算订单（调用后端API）
                async settleOrder(orderId) {
                    // 模拟API调用
                    await this.mockApiCall(500);
                    
                    // 模拟结算结果（实际由后端控制系统决定）
                    const isWin = Math.random() > 0.5; // 这里实际由代理端控制
                    const openPrice = this.currentOrder.openPrice;
                    const direction = this.currentOrder.direction;
                    
                    let closePrice;
                    let pnl;
                    
                    if (isWin) {
                        // 如果控制为赢
                        if (direction === 'buy') {
                            closePrice = openPrice + (openPrice * 0.001 * (1 + Math.random())); // 涨0.1%-0.2%
                        } else {
                            closePrice = openPrice - (openPrice * 0.001 * (1 + Math.random())); // 跌0.1%-0.2%
                        }
                        pnl = this.currentOrder.amount * 0.8; // 盈利80%
                    } else {
                        // 如果控制为输
                        if (direction === 'buy') {
                            closePrice = openPrice - (openPrice * 0.001 * (1 + Math.random())); // 跌0.1%-0.2%
                        } else {
                            closePrice = openPrice + (openPrice * 0.001 * (1 + Math.random())); // 涨0.1%-0.2%
                        }
                        pnl = -this.currentOrder.amount; // 亏损本金
                    }
                    
                    return {
                        closePrice: closePrice,
                        pnl: pnl
                    };
                },
                
                // 关闭订单弹窗
                closeOrderModal() {
                    this.showOrderModal = false;
                    // 注意：这里只是关闭弹窗，不取消订单
                    // 订单继续在后台执行
                },
                
                // 通知代理端
                notifyAgent() {
                    // 实际项目中通过WebSocket发送给代理端
                    console.log('通知代理端新订单：', this.currentOrder);
                },
                
                // 格式化时间
                formatTime(seconds) {
                    const mins = Math.floor(seconds / 60);
                    const secs = seconds % 60;
                    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
                },
                
                // 获取时长文本
                getDurationText(duration) {
                    if (duration < 60) return duration + '秒';
                    if (duration < 3600) return (duration / 60) + '分钟';
                    return (duration / 3600) + '小时';
                },
                
                // 获取收盘价样式
                getClosePriceClass() {
                    if (this.orderStatus !== 'finished') return '';
                    return this.orderResult;
                },
                
                // 开始价格更新
                startPriceUpdate() {
                    setInterval(() => {
                        const change = (Math.random() - 0.5) * 100; // -50 到 +50
                        this.currentPrice += change;
                        this.priceChange = (Math.random() - 0.5) * 10; // -5% 到 +5%
                    }, 3000);
                },
                
                // 初始化K线图
                initKlineChart() {
                    // 这里应该集成TradingView或其他K线图库
                    document.getElementById('kline-chart').innerHTML = '📊 K线图已加载（集成TradingView）';
                },
                
                // 模拟API调用
                mockApiCall(delay = 1000) {
                    return new Promise((resolve) => {
                        setTimeout(resolve, delay);
                    });
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
