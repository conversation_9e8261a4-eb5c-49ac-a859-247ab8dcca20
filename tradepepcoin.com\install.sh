#!/bin/bash

# 数字货币交易平台安装脚本
# 适用于 Ubuntu 22.04 + 宝塔面板

echo "=========================================="
echo "数字货币交易平台安装脚本"
echo "版本: 1.0"
echo "适用环境: Ubuntu 22.04 + 宝塔面板"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "请使用root用户运行此脚本"
        exit 1
    fi
}

# 检查系统版本
check_system() {
    log_step "检查系统环境..."
    
    if [[ ! -f /etc/os-release ]]; then
        log_error "无法检测系统版本"
        exit 1
    fi
    
    . /etc/os-release
    
    if [[ "$ID" != "ubuntu" ]] || [[ "$VERSION_ID" != "22.04" ]]; then
        log_warn "建议使用 Ubuntu 22.04 系统"
    fi
    
    log_info "系统检查完成: $PRETTY_NAME"
}

# 检查宝塔面板
check_bt_panel() {
    log_step "检查宝塔面板..."
    
    if [[ ! -f /www/server/panel/BT-Panel ]]; then
        log_error "未检测到宝塔面板，请先安装宝塔面板"
        echo "安装命令: wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh"
        exit 1
    fi
    
    log_info "宝塔面板检查完成"
}

# 检查PHP版本
check_php() {
    log_step "检查PHP环境..."
    
    if ! command -v php &> /dev/null; then
        log_error "PHP未安装，请在宝塔面板中安装PHP 7.4"
        exit 1
    fi
    
    PHP_VERSION=$(php -r "echo PHP_VERSION;")
    log_info "PHP版本: $PHP_VERSION"
    
    # 检查必需的PHP扩展
    REQUIRED_EXTENSIONS=("pdo" "pdo_mysql" "mbstring" "openssl" "curl" "json" "fileinfo" "gd")
    
    for ext in "${REQUIRED_EXTENSIONS[@]}"; do
        if ! php -m | grep -q "$ext"; then
            log_error "PHP扩展 $ext 未安装"
            exit 1
        fi
    done
    
    log_info "PHP环境检查完成"
}

# 检查MySQL
check_mysql() {
    log_step "检查MySQL环境..."
    
    if ! command -v mysql &> /dev/null; then
        log_error "MySQL未安装，请在宝塔面板中安装MySQL 5.7"
        exit 1
    fi
    
    MYSQL_VERSION=$(mysql --version)
    log_info "MySQL版本: $MYSQL_VERSION"
}

# 检查Nginx
check_nginx() {
    log_step "检查Nginx环境..."
    
    if ! command -v nginx &> /dev/null; then
        log_error "Nginx未安装，请在宝塔面板中安装Nginx"
        exit 1
    fi
    
    NGINX_VERSION=$(nginx -v 2>&1)
    log_info "Nginx版本: $NGINX_VERSION"
}

# 安装Composer
install_composer() {
    log_step "安装Composer..."
    
    if command -v composer &> /dev/null; then
        log_info "Composer已安装"
        return
    fi
    
    cd /tmp
    curl -sS https://getcomposer.org/installer | php
    mv composer.phar /usr/local/bin/composer
    chmod +x /usr/local/bin/composer
    
    log_info "Composer安装完成"
}

# 创建数据库
create_database() {
    log_step "创建数据库..."
    
    read -p "请输入MySQL root密码: " -s MYSQL_ROOT_PASSWORD
    echo
    
    read -p "请输入数据库名称 [crypto_exchange]: " DB_NAME
    DB_NAME=${DB_NAME:-crypto_exchange}
    
    read -p "请输入数据库用户名 [crypto_user]: " DB_USER
    DB_USER=${DB_USER:-crypto_user}
    
    read -p "请输入数据库密码: " -s DB_PASSWORD
    echo
    
    # 创建数据库和用户
    mysql -uroot -p"$MYSQL_ROOT_PASSWORD" <<EOF
CREATE DATABASE IF NOT EXISTS \`$DB_NAME\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASSWORD';
GRANT ALL PRIVILEGES ON \`$DB_NAME\`.* TO '$DB_USER'@'localhost';
FLUSH PRIVILEGES;
EOF
    
    if [[ $? -eq 0 ]]; then
        log_info "数据库创建成功"
    else
        log_error "数据库创建失败"
        exit 1
    fi
}

# 配置项目
configure_project() {
    log_step "配置项目..."
    
    # 获取项目路径
    read -p "请输入项目安装路径 [/www/wwwroot/crypto-exchange]: " PROJECT_PATH
    PROJECT_PATH=${PROJECT_PATH:-/www/wwwroot/crypto-exchange}
    
    # 创建项目目录
    mkdir -p "$PROJECT_PATH"
    
    # 复制项目文件
    if [[ -d "$(pwd)" ]]; then
        cp -r . "$PROJECT_PATH/"
        cd "$PROJECT_PATH"
    else
        log_error "项目文件不存在"
        exit 1
    fi
    
    # 设置权限
    chown -R www:www "$PROJECT_PATH"
    chmod -R 755 "$PROJECT_PATH"
    chmod -R 777 "$PROJECT_PATH/runtime"
    
    # 配置环境文件
    if [[ -f .env.example ]]; then
        cp .env.example .env
    fi
    
    # 更新数据库配置
    sed -i "s/DATABASE = .*/DATABASE = $DB_NAME/" .env
    sed -i "s/USERNAME = .*/USERNAME = $DB_USER/" .env
    sed -i "s/PASSWORD = .*/PASSWORD = $DB_PASSWORD/" .env
    
    log_info "项目配置完成"
}

# 安装依赖
install_dependencies() {
    log_step "安装项目依赖..."
    
    cd "$PROJECT_PATH"
    
    # 安装Composer依赖
    composer install --no-dev --optimize-autoloader
    
    if [[ $? -eq 0 ]]; then
        log_info "依赖安装完成"
    else
        log_error "依赖安装失败"
        exit 1
    fi
}

# 导入数据库
import_database() {
    log_step "导入数据库结构..."
    
    if [[ -f "$PROJECT_PATH/database/crypto_exchange.sql" ]]; then
        mysql -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$PROJECT_PATH/database/crypto_exchange.sql"
        
        if [[ $? -eq 0 ]]; then
            log_info "数据库导入完成"
        else
            log_error "数据库导入失败"
            exit 1
        fi
    else
        log_error "数据库文件不存在"
        exit 1
    fi
}

# 配置Nginx
configure_nginx() {
    log_step "配置Nginx..."
    
    read -p "请输入域名 [crypto-exchange.local]: " DOMAIN
    DOMAIN=${DOMAIN:-crypto-exchange.local}
    
    # 创建Nginx配置文件
    cat > "/www/server/panel/vhost/nginx/$DOMAIN.conf" <<EOF
server {
    listen 80;
    server_name $DOMAIN;
    root $PROJECT_PATH/public;
    index index.php index.html;
    
    # 安全配置
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";
    add_header X-XSS-Protection "1; mode=block";
    
    # 日志配置
    access_log /www/wwwroot/$DOMAIN/access.log;
    error_log /www/wwwroot/$DOMAIN/error.log;
    
    # 主要配置
    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }
    
    # PHP配置
    location ~ \.php$ {
        fastcgi_pass unix:/tmp/php-cgi-74.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME \$document_root\$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_read_timeout 300;
    }
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 安全配置
    location ~ /\. {
        deny all;
    }
    
    location ~ /(vendor|database|config)/ {
        deny all;
    }
}
EOF
    
    # 重载Nginx配置
    nginx -t && nginx -s reload
    
    log_info "Nginx配置完成"
    log_info "访问地址: http://$DOMAIN"
}

# 设置定时任务
setup_cron() {
    log_step "设置定时任务..."
    
    # 添加定时任务
    (crontab -l 2>/dev/null; echo "* * * * * cd $PROJECT_PATH && php think queue:work >> /dev/null 2>&1") | crontab -
    (crontab -l 2>/dev/null; echo "0 */1 * * * cd $PROJECT_PATH && php think cron:run >> /dev/null 2>&1") | crontab -
    
    log_info "定时任务设置完成"
}

# 主安装流程
main() {
    log_info "开始安装数字货币交易平台..."
    
    check_root
    check_system
    check_bt_panel
    check_php
    check_mysql
    check_nginx
    install_composer
    create_database
    configure_project
    install_dependencies
    import_database
    configure_nginx
    setup_cron
    
    echo
    echo "=========================================="
    log_info "安装完成！"
    echo "=========================================="
    echo "访问地址: http://$DOMAIN"
    echo "管理后台: http://$DOMAIN/admin"
    echo "默认管理员账号: admin"
    echo "默认管理员密码: password"
    echo "=========================================="
    echo "请及时修改默认密码！"
    echo "=========================================="
}

# 运行主程序
main "$@"
