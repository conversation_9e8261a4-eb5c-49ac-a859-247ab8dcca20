<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Cache;
use think\facade\Log;

/**
 * 多语言服务类
 */
class LanguageService
{
    // 支持的语言
    const SUPPORTED_LANGUAGES = [
        'zh-CN' => '简体中文',
        'zh-TW' => '繁體中文',
        'en-US' => 'English',
        'ja-JP' => '日本語',
        'ko-KR' => '한국어',
        'es-ES' => 'Español',
        'fr-FR' => 'Français',
        'de-DE' => 'Deutsch',
        'ru-RU' => 'Русский',
        'ar-SA' => 'العربية'
    ];

    // 默认语言
    const DEFAULT_LANGUAGE = 'zh-CN';

    // 语言包缓存前缀
    const CACHE_PREFIX = 'lang_pack_';

    /**
     * 获取支持的语言列表
     */
    public function getSupportedLanguages(): array
    {
        return self::SUPPORTED_LANGUAGES;
    }

    /**
     * 检测用户语言
     */
    public function detectUserLanguage(): string
    {
        // 1. 从用户设置中获取
        $userLang = session('user_language');
        if ($userLang && isset(self::SUPPORTED_LANGUAGES[$userLang])) {
            return $userLang;
        }

        // 2. 从浏览器Accept-Language头获取
        $acceptLanguage = $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '';
        $browserLang = $this->parseBrowserLanguage($acceptLanguage);
        if ($browserLang && isset(self::SUPPORTED_LANGUAGES[$browserLang])) {
            return $browserLang;
        }

        // 3. 返回默认语言
        return self::DEFAULT_LANGUAGE;
    }

    /**
     * 解析浏览器语言
     */
    private function parseBrowserLanguage(string $acceptLanguage): string
    {
        if (empty($acceptLanguage)) {
            return '';
        }

        // 解析Accept-Language头
        $languages = explode(',', $acceptLanguage);
        foreach ($languages as $language) {
            $lang = trim(explode(';', $language)[0]);
            
            // 精确匹配
            if (isset(self::SUPPORTED_LANGUAGES[$lang])) {
                return $lang;
            }
            
            // 模糊匹配（如en匹配en-US）
            foreach (self::SUPPORTED_LANGUAGES as $supportedLang => $name) {
                if (strpos($supportedLang, substr($lang, 0, 2)) === 0) {
                    return $supportedLang;
                }
            }
        }

        return '';
    }

    /**
     * 获取语言包
     */
    public function getLanguagePack(string $language = ''): array
    {
        if (empty($language)) {
            $language = $this->detectUserLanguage();
        }

        // 从缓存获取
        $cacheKey = self::CACHE_PREFIX . $language;
        $languagePack = Cache::get($cacheKey);

        if ($languagePack === null) {
            $languagePack = $this->loadLanguagePack($language);
            // 缓存1小时
            Cache::set($cacheKey, $languagePack, 3600);
        }

        return $languagePack;
    }

    /**
     * 加载语言包
     */
    private function loadLanguagePack(string $language): array
    {
        $languageFile = app()->getRootPath() . "lang/{$language}.php";
        
        if (!file_exists($languageFile)) {
            // 如果语言文件不存在，使用默认语言
            $languageFile = app()->getRootPath() . "lang/" . self::DEFAULT_LANGUAGE . ".php";
        }

        if (file_exists($languageFile)) {
            return include $languageFile;
        }

        // 返回空数组作为后备
        return [];
    }

    /**
     * 翻译文本
     */
    public function translate(string $key, array $params = [], string $language = ''): string
    {
        $languagePack = $this->getLanguagePack($language);
        
        // 支持点号分隔的键名，如 'user.login.title'
        $keys = explode('.', $key);
        $value = $languagePack;
        
        foreach ($keys as $k) {
            if (isset($value[$k])) {
                $value = $value[$k];
            } else {
                // 如果找不到翻译，返回原键名
                return $key;
            }
        }

        // 如果不是字符串，返回原键名
        if (!is_string($value)) {
            return $key;
        }

        // 替换参数
        if (!empty($params)) {
            foreach ($params as $param => $replacement) {
                $value = str_replace("{{$param}}", $replacement, $value);
            }
        }

        return $value;
    }

    /**
     * 设置用户语言
     */
    public function setUserLanguage(string $language): bool
    {
        if (!isset(self::SUPPORTED_LANGUAGES[$language])) {
            return false;
        }

        session('user_language', $language);
        return true;
    }

    /**
     * 格式化数字（本地化）
     */
    public function formatNumber(float $number, int $decimals = 2, string $language = ''): string
    {
        if (empty($language)) {
            $language = $this->detectUserLanguage();
        }

        $localeMap = [
            'zh-CN' => 'zh_CN',
            'zh-TW' => 'zh_TW',
            'en-US' => 'en_US',
            'ja-JP' => 'ja_JP',
            'ko-KR' => 'ko_KR',
            'es-ES' => 'es_ES',
            'fr-FR' => 'fr_FR',
            'de-DE' => 'de_DE',
            'ru-RU' => 'ru_RU',
            'ar-SA' => 'ar_SA'
        ];

        $locale = $localeMap[$language] ?? 'en_US';

        // 使用NumberFormatter进行本地化格式化
        if (class_exists('NumberFormatter')) {
            $formatter = new \NumberFormatter($locale, \NumberFormatter::DECIMAL);
            $formatter->setAttribute(\NumberFormatter::FRACTION_DIGITS, $decimals);
            return $formatter->format($number);
        }

        // 降级方案
        return number_format($number, $decimals);
    }

    /**
     * 格式化货币（本地化）
     */
    public function formatCurrency(float $amount, string $currency = 'USD', string $language = ''): string
    {
        if (empty($language)) {
            $language = $this->detectUserLanguage();
        }

        $localeMap = [
            'zh-CN' => 'zh_CN',
            'zh-TW' => 'zh_TW',
            'en-US' => 'en_US',
            'ja-JP' => 'ja_JP',
            'ko-KR' => 'ko_KR',
            'es-ES' => 'es_ES',
            'fr-FR' => 'fr_FR',
            'de-DE' => 'de_DE',
            'ru-RU' => 'ru_RU',
            'ar-SA' => 'ar_SA'
        ];

        $locale = $localeMap[$language] ?? 'en_US';

        // 使用NumberFormatter进行货币格式化
        if (class_exists('NumberFormatter')) {
            $formatter = new \NumberFormatter($locale, \NumberFormatter::CURRENCY);
            return $formatter->formatCurrency($amount, $currency);
        }

        // 降级方案
        $currencySymbols = [
            'USD' => '$',
            'EUR' => '€',
            'JPY' => '¥',
            'CNY' => '¥',
            'KRW' => '₩',
            'GBP' => '£'
        ];

        $symbol = $currencySymbols[$currency] ?? $currency;
        return $symbol . number_format($amount, 2);
    }

    /**
     * 格式化日期（本地化）
     */
    public function formatDate($date, string $format = 'medium', string $language = ''): string
    {
        if (empty($language)) {
            $language = $this->detectUserLanguage();
        }

        $timestamp = is_numeric($date) ? $date : strtotime($date);
        
        $formatMap = [
            'zh-CN' => [
                'short' => 'Y/m/d',
                'medium' => 'Y年m月d日',
                'long' => 'Y年m月d日 H:i',
                'full' => 'Y年m月d日 H:i:s'
            ],
            'en-US' => [
                'short' => 'm/d/Y',
                'medium' => 'M d, Y',
                'long' => 'M d, Y H:i',
                'full' => 'F d, Y H:i:s'
            ],
            'ja-JP' => [
                'short' => 'Y/m/d',
                'medium' => 'Y年m月d日',
                'long' => 'Y年m月d日 H:i',
                'full' => 'Y年m月d日 H:i:s'
            ]
        ];

        $formats = $formatMap[$language] ?? $formatMap['en-US'];
        $dateFormat = $formats[$format] ?? $formats['medium'];

        return date($dateFormat, $timestamp);
    }

    /**
     * 获取RTL语言列表
     */
    public function getRTLLanguages(): array
    {
        return ['ar-SA', 'he-IL', 'fa-IR'];
    }

    /**
     * 检查是否为RTL语言
     */
    public function isRTLLanguage(string $language = ''): bool
    {
        if (empty($language)) {
            $language = $this->detectUserLanguage();
        }

        return in_array($language, $this->getRTLLanguages());
    }

    /**
     * 清除语言包缓存
     */
    public function clearLanguageCache(string $language = ''): bool
    {
        try {
            if (empty($language)) {
                // 清除所有语言包缓存
                foreach (array_keys(self::SUPPORTED_LANGUAGES) as $lang) {
                    Cache::delete(self::CACHE_PREFIX . $lang);
                }
            } else {
                // 清除指定语言包缓存
                Cache::delete(self::CACHE_PREFIX . $language);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('清除语言包缓存失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取语言包统计信息
     */
    public function getLanguagePackStats(): array
    {
        $stats = [];

        foreach (self::SUPPORTED_LANGUAGES as $langCode => $langName) {
            $languagePack = $this->getLanguagePack($langCode);
            $stats[$langCode] = [
                'name' => $langName,
                'code' => $langCode,
                'keys_count' => $this->countLanguageKeys($languagePack),
                'file_exists' => file_exists(app()->getRootPath() . "lang/{$langCode}.php"),
                'is_rtl' => $this->isRTLLanguage($langCode)
            ];
        }

        return $stats;
    }

    /**
     * 递归计算语言包键数量
     */
    private function countLanguageKeys(array $languagePack): int
    {
        $count = 0;
        foreach ($languagePack as $value) {
            if (is_array($value)) {
                $count += $this->countLanguageKeys($value);
            } else {
                $count++;
            }
        }
        return $count;
    }
}
