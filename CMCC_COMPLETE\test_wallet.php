<?php
/**
 * 钱包功能测试脚本
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/vendor/autoload.php';

use think\facade\Db;
use app\common\service\WalletService;

// 初始化应用
$app = new \think\App();
$app->initialize();

echo "=== 钱包功能测试 ===\n\n";

try {
    $walletService = new WalletService();
    
    // 测试用户ID（使用数据库中的测试用户）
    $testUserId = 2; // test用户
    
    echo "1. 测试获取用户余额\n";
    echo "-------------------\n";
    
    // 测试获取USDT余额
    $result = $walletService->getUserBalance($testUserId, 'USDT');
    echo "获取USDT余额结果：\n";
    print_r($result);
    echo "\n";
    
    // 测试获取BTC余额
    $result = $walletService->getUserBalance($testUserId, 'BTC');
    echo "获取BTC余额结果：\n";
    print_r($result);
    echo "\n";
    
    echo "2. 测试增加用户余额\n";
    echo "-------------------\n";
    
    // 测试增加100 USDT
    $result = $walletService->addBalance($testUserId, 'USDT', 100.0, 'admin', '测试充值100 USDT');
    echo "增加100 USDT结果：\n";
    print_r($result);
    echo "\n";
    
    // 再次查询余额确认
    $result = $walletService->getUserBalance($testUserId, 'USDT');
    echo "增加后的USDT余额：\n";
    print_r($result);
    echo "\n";
    
    echo "3. 测试冻结余额\n";
    echo "---------------\n";
    
    // 测试冻结50 USDT
    $result = $walletService->freezeBalance($testUserId, 'USDT', 50.0);
    echo "冻结50 USDT结果：\n";
    print_r($result);
    echo "\n";
    
    // 查询冻结后的余额
    $result = $walletService->getUserBalance($testUserId, 'USDT');
    echo "冻结后的USDT余额：\n";
    print_r($result);
    echo "\n";
    
    echo "4. 测试解冻余额\n";
    echo "---------------\n";
    
    // 测试解冻30 USDT
    $result = $walletService->unfreezeBalance($testUserId, 'USDT', 30.0);
    echo "解冻30 USDT结果：\n";
    print_r($result);
    echo "\n";
    
    // 查询解冻后的余额
    $result = $walletService->getUserBalance($testUserId, 'USDT');
    echo "解冻后的USDT余额：\n";
    print_r($result);
    echo "\n";
    
    echo "5. 查询资产变动记录\n";
    echo "-------------------\n";
    
    // 查询最近的资产变动记录
    $records = Db::name('gvd_asset_records')
        ->where('user_id', $testUserId)
        ->order('id desc')
        ->limit(5)
        ->select()
        ->toArray();
    
    echo "最近5条资产变动记录：\n";
    foreach ($records as $record) {
        echo sprintf(
            "ID: %d, 币种: %s, 金额: %s, 类型: %s, 描述: %s, 时间: %s\n",
            $record['id'],
            $record['coin_symbol'],
            $record['amount'],
            $record['type'],
            $record['description'],
            $record['created_at']
        );
    }
    echo "\n";
    
    echo "=== 钱包功能测试完成 ===\n";
    
} catch (\Exception $e) {
    echo "测试失败：" . $e->getMessage() . "\n";
    echo "错误文件：" . $e->getFile() . "\n";
    echo "错误行号：" . $e->getLine() . "\n";
    echo "错误堆栈：\n" . $e->getTraceAsString() . "\n";
}
