<?php
declare (strict_types = 1);

namespace app\agent\controller;

use think\App;
use think\facade\View;
use think\facade\Session;
use think\facade\Db;

/**
 * 代理端基础控制器
 */
abstract class BaseController
{
    /**
     * Request实例
     * @var \think\Request
     */
    protected $request;

    /**
     * 应用实例
     * @var \think\App
     */
    protected $app;

    /**
     * 当前代理ID
     * @var int
     */
    protected $agentId = 0;

    /**
     * 当前代理信息
     * @var array
     */
    protected $agentInfo = [];

    /**
     * 不需要验证登录的方法
     * @var array
     */
    protected $noAuthMethods = ['login', 'logout', 'captcha'];

    /**
     * 构造方法
     */
    public function __construct(App $app)
    {
        $this->app     = $app;
        $this->request = $this->app->request;

        // 控制器初始化
        $this->initialize();
    }

    // 初始化
    protected function initialize()
    {
        // 获取代理信息
        if (isset($this->request->agent_id)) {
            $this->agentId = $this->request->agent_id;
            $this->agentInfo = $this->request->agent;
        }

        // 设置视图变量
        View::assign([
            'agent' => $this->agentInfo,
            'agent_id' => $this->agentId,
            'request' => $this->request,
        ]);
    }

    /**
     * 获取当前代理ID
     */
    protected function getAgentId(): int
    {
        return $this->agentId;
    }

    /**
     * 获取当前代理信息
     */
    protected function getAgent(): array
    {
        return $this->agentInfo;
    }

    /**
     * 成功响应
     */
    protected function success($data = [], string $message = '操作成功', string $url = '')
    {
        $result = [
            'code' => 1,
            'msg' => $message,
            'data' => $data,
            'timestamp' => time()
        ];

        if ($url) {
            $result['url'] = $url;
        }

        return json($result);
    }

    /**
     * 错误响应
     */
    protected function error(string $message = '操作失败', string $url = '', $data = [])
    {
        $result = [
            'code' => 0,
            'msg' => $message,
            'data' => $data,
            'timestamp' => time()
        ];

        if ($url) {
            $result['url'] = $url;
        }

        return json($result);
    }

    /**
     * 记录操作日志
     */
    protected function writeLog(string $action, array $data = [])
    {
        try {
            Db::name('agent_log')->insert([
                'agent_id' => $this->agentId,
                'agent_name' => $this->agentInfo['username'] ?? '',
                'action' => $action,
                'data' => json_encode($data, JSON_UNESCAPED_UNICODE),
                'ip' => $this->request->ip(),
                'user_agent' => $this->request->header('user-agent'),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            // 日志记录失败不影响主流程
        }
    }

    /**
     * 获取客户端IP
     */
    protected function getClientIp(): string
    {
        return $this->request->ip();
    }

    /**
     * 分页参数处理
     */
    protected function getPaginationParams(): array
    {
        $page = $this->request->param('page/d', 1);
        $limit = $this->request->param('limit/d', 20);
        
        // 限制每页最大数量
        $limit = min($limit, 100);
        
        return [
            'page' => max(1, $page),
            'limit' => max(1, $limit),
            'offset' => ($page - 1) * $limit
        ];
    }

    /**
     * 获取团队用户列表
     */
    protected function getTeamUsers(): array
    {
        return Db::name('users')
            ->where('agent_id', $this->agentId)
            ->where('status', 1)
            ->field('id,username,email,user_type,created_at')
            ->select()
            ->toArray();
    }
}
