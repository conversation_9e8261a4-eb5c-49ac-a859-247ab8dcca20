<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\IdoProject;
use app\common\model\IdoOrder;
use app\common\model\User;
use think\facade\Log;

/**
 * IDO服务类
 */
class IdoService
{
    /**
     * 获取项目列表
     */
    public function getProjectList(array $params = []): array
    {
        try {
            // 更新项目状态
            $this->updateProjectStatus();
            
            return IdoProject::getProjectList($params);
        } catch (\Exception $e) {
            Log::error('获取IDO项目列表失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 获取项目详情
     */
    public function getProjectDetail(int $projectId, int $userId = 0): array
    {
        try {
            $project = IdoProject::find($projectId);
            if (!$project) {
                return ['code' => 0, 'msg' => '项目不存在'];
            }

            // 更新项目状态
            $project->updateStatus();

            $projectInfo = $project->getProjectInfo();
            
            // 添加额外信息
            $projectInfo['participant_count'] = $project->getParticipantCount();
            
            if ($userId > 0) {
                $projectInfo['user_purchased'] = $project->hasUserPurchased($userId);
                $projectInfo['user_purchase_amount'] = $project->getUserPurchaseAmount($userId);
            }

            return [
                'code' => 1,
                'data' => $projectInfo
            ];

        } catch (\Exception $e) {
            Log::error('获取IDO项目详情失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 参与IDO认购
     */
    public function purchaseProject(int $userId, int $projectId, float $amount, string $payPassword = ''): array
    {
        try {
            // 验证支付密码
            $user = User::find($userId);
            if ($user->pay_password && !$user->verifyPayPassword($payPassword)) {
                return ['code' => 0, 'msg' => '支付密码错误'];
            }

            return IdoOrder::createOrder($userId, $projectId, $amount);

        } catch (\Exception $e) {
            Log::error('IDO认购失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '认购失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取用户认购记录
     */
    public function getUserOrders(int $userId, array $params = []): array
    {
        try {
            return IdoOrder::getUserOrders($userId, $params);
        } catch (\Exception $e) {
            Log::error('获取用户IDO订单失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 获取项目认购记录
     */
    public function getProjectOrders(int $projectId, array $params = []): array
    {
        try {
            return IdoOrder::getProjectOrders($projectId, $params);
        } catch (\Exception $e) {
            Log::error('获取项目IDO订单失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 获取热门项目
     */
    public function getHotProjects(int $limit = 6): array
    {
        try {
            // 更新项目状态
            $this->updateProjectStatus();
            
            return IdoProject::getHotProjects($limit);
        } catch (\Exception $e) {
            Log::error('获取热门IDO项目失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 获取IDO统计信息
     */
    public function getIdoStats(): array
    {
        try {
            $projectStats = IdoProject::getProjectStats();
            $orderStats = IdoOrder::getOrderStats();

            if ($projectStats['code'] == 1 && $orderStats['code'] == 1) {
                return [
                    'code' => 1,
                    'data' => array_merge($projectStats['data'], $orderStats['data'])
                ];
            }

            return ['code' => 0, 'msg' => '获取统计失败'];

        } catch (\Exception $e) {
            Log::error('获取IDO统计失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 创建IDO项目（管理员功能）
     */
    public function createProject(array $data): array
    {
        try {
            // 验证数据
            $validation = $this->validateProjectData($data);
            if (!$validation['valid']) {
                return ['code' => 0, 'msg' => $validation['message']];
            }

            return IdoProject::createProject($data);

        } catch (\Exception $e) {
            Log::error('创建IDO项目失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '创建失败'];
        }
    }

    /**
     * 更新项目状态
     */
    private function updateProjectStatus(): void
    {
        try {
            $projects = IdoProject::where('status', 'in', [
                IdoProject::STATUS_UPCOMING,
                IdoProject::STATUS_ACTIVE
            ])->select();

            foreach ($projects as $project) {
                $project->updateStatus();
            }
        } catch (\Exception $e) {
            Log::error('更新IDO项目状态失败：' . $e->getMessage());
        }
    }

    /**
     * 验证项目数据
     */
    private function validateProjectData(array $data): array
    {
        if (empty($data['name'])) {
            return ['valid' => false, 'message' => '项目名称不能为空'];
        }

        if (empty($data['symbol'])) {
            return ['valid' => false, 'message' => '代币符号不能为空'];
        }

        if (!isset($data['target_amount']) || $data['target_amount'] <= 0) {
            return ['valid' => false, 'message' => '目标金额必须大于0'];
        }

        if (!isset($data['token_price']) || $data['token_price'] <= 0) {
            return ['valid' => false, 'message' => '代币价格必须大于0'];
        }

        if (empty($data['start_time'])) {
            return ['valid' => false, 'message' => '开始时间不能为空'];
        }

        if (empty($data['end_time'])) {
            return ['valid' => false, 'message' => '结束时间不能为空'];
        }

        $startTime = strtotime($data['start_time']);
        $endTime = strtotime($data['end_time']);

        if ($startTime >= $endTime) {
            return ['valid' => false, 'message' => '结束时间必须晚于开始时间'];
        }

        if ($startTime <= time()) {
            return ['valid' => false, 'message' => '开始时间必须晚于当前时间'];
        }

        return ['valid' => true];
    }

    /**
     * 获取用户IDO统计
     */
    public function getUserIdoStats(int $userId): array
    {
        try {
            $orderStats = IdoOrder::getOrderStats($userId);
            
            // 获取用户参与的项目数
            $participatedProjects = IdoOrder::where('user_id', $userId)
                                           ->where('status', IdoOrder::STATUS_SUCCESS)
                                           ->group('project_id')
                                           ->count();

            if ($orderStats['code'] == 1) {
                $orderStats['data']['participated_projects'] = $participatedProjects;
                return $orderStats;
            }

            return ['code' => 0, 'msg' => '获取统计失败'];

        } catch (\Exception $e) {
            Log::error('获取用户IDO统计失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 检查项目是否可以认购
     */
    public function checkProjectPurchasable(int $projectId): array
    {
        try {
            $project = IdoProject::find($projectId);
            if (!$project) {
                return ['code' => 0, 'msg' => '项目不存在'];
            }

            $project->updateStatus();

            return [
                'code' => 1,
                'data' => [
                    'can_purchase' => $project->canPurchase(),
                    'status' => $project->status,
                    'status_text' => $project->getStatusText(),
                    'remaining_time' => $project->getRemainingTime(),
                    'remaining_amount' => $project->target_amount - $project->raised_amount
                ]
            ];

        } catch (\Exception $e) {
            Log::error('检查项目状态失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '检查失败'];
        }
    }
}
