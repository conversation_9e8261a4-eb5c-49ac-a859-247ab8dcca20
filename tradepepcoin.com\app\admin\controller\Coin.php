<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\BaseController;
use app\common\model\Coin as CoinModel;
use think\facade\View;

/**
 * 管理后台币种管理控制器
 */
class Coin extends BaseController
{
    /**
     * 币种列表
     */
    public function index()
    {
        $keyword = input('keyword', '');
        $status = input('status', '');
        $page = input('page/d', 1);
        $limit = input('limit/d', 20);

        $where = [];
        
        if (!empty($keyword)) {
            $where[] = ['symbol|name', 'like', "%{$keyword}%"];
        }
        
        if ($status !== '') {
            $where['status'] = $status;
        }

        $coins = CoinModel::where($where)
                         ->order('sort', 'asc')
                         ->order('created_at', 'desc')
                         ->paginate([
                             'list_rows' => $limit,
                             'page' => $page
                         ]);

        if ($this->request->isAjax()) {
            return json([
                'code' => 1,
                'data' => $coins->items(),
                'total' => $coins->total(),
                'page' => $page,
                'limit' => $limit
            ]);
        }

        View::assign([
            'coins' => $coins,
            'keyword' => $keyword,
            'status' => $status,
            'title' => '币种管理'
        ]);

        return View::fetch();
    }

    /**
     * 添加币种
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();
            
            // 验证数据
            $validate = [
                'symbol' => 'require|length:2,10',
                'name' => 'require|length:2,50',
                'decimals' => 'require|integer|between:0,18',
                'min_deposit' => 'require|float|egt:0',
                'min_withdraw' => 'require|float|egt:0',
                'withdraw_fee' => 'require|float|egt:0',
                'withdraw_fee_type' => 'require|in:1,2'
            ];

            $result = $this->validate($data, $validate);
            if ($result !== true) {
                return json(['code' => 0, 'msg' => $result]);
            }

            // 检查币种符号是否存在
            if (CoinModel::where('symbol', $data['symbol'])->count() > 0) {
                return json(['code' => 0, 'msg' => '币种符号已存在']);
            }

            // 创建币种
            $coin = new CoinModel();
            $coin->symbol = strtoupper($data['symbol']);
            $coin->name = $data['name'];
            $coin->icon = $data['icon'] ?? '';
            $coin->decimals = $data['decimals'];
            $coin->is_deposit = $data['is_deposit'] ?? 0;
            $coin->is_withdraw = $data['is_withdraw'] ?? 0;
            $coin->is_trade = $data['is_trade'] ?? 0;
            $coin->min_deposit = $data['min_deposit'];
            $coin->min_withdraw = $data['min_withdraw'];
            $coin->withdraw_fee = $data['withdraw_fee'];
            $coin->withdraw_fee_type = $data['withdraw_fee_type'];
            $coin->deposit_address = $data['deposit_address'] ?? '';
            $coin->contract_address = $data['contract_address'] ?? '';
            $coin->sort = $data['sort'] ?? 0;
            $coin->status = $data['status'] ?? 1;

            if ($coin->save()) {
                return json(['code' => 1, 'msg' => '币种添加成功']);
            } else {
                return json(['code' => 0, 'msg' => '币种添加失败']);
            }
        }

        View::assign([
            'title' => '添加币种'
        ]);

        return View::fetch();
    }

    /**
     * 编辑币种
     */
    public function edit()
    {
        $id = input('id/d', 0);
        $coin = CoinModel::find($id);
        
        if (!$coin) {
            $this->error('币种不存在');
        }

        if ($this->request->isPost()) {
            $data = $this->request->post();
            
            // 验证数据
            $validate = [
                'name' => 'require|length:2,50',
                'decimals' => 'require|integer|between:0,18',
                'min_deposit' => 'require|float|egt:0',
                'min_withdraw' => 'require|float|egt:0',
                'withdraw_fee' => 'require|float|egt:0',
                'withdraw_fee_type' => 'require|in:1,2'
            ];

            $result = $this->validate($data, $validate);
            if ($result !== true) {
                return json(['code' => 0, 'msg' => $result]);
            }

            // 更新币种信息
            $coin->name = $data['name'];
            $coin->icon = $data['icon'] ?? '';
            $coin->decimals = $data['decimals'];
            $coin->is_deposit = $data['is_deposit'] ?? 0;
            $coin->is_withdraw = $data['is_withdraw'] ?? 0;
            $coin->is_trade = $data['is_trade'] ?? 0;
            $coin->min_deposit = $data['min_deposit'];
            $coin->min_withdraw = $data['min_withdraw'];
            $coin->withdraw_fee = $data['withdraw_fee'];
            $coin->withdraw_fee_type = $data['withdraw_fee_type'];
            $coin->deposit_address = $data['deposit_address'] ?? '';
            $coin->contract_address = $data['contract_address'] ?? '';
            $coin->sort = $data['sort'] ?? 0;
            $coin->status = $data['status'] ?? 1;

            if ($coin->save()) {
                return json(['code' => 1, 'msg' => '币种更新成功']);
            } else {
                return json(['code' => 0, 'msg' => '币种更新失败']);
            }
        }

        View::assign([
            'coin' => $coin,
            'title' => '编辑币种'
        ]);

        return View::fetch();
    }

    /**
     * 删除币种
     */
    public function delete()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $id = input('id/d', 0);
        $coin = CoinModel::find($id);
        
        if (!$coin) {
            return json(['code' => 0, 'msg' => '币种不存在']);
        }

        // 检查是否有用户持有该币种
        $hasAssets = \app\common\model\UserAsset::where('coin_symbol', $coin->symbol)
                                               ->where('total', '>', 0)
                                               ->count() > 0;
        if ($hasAssets) {
            return json(['code' => 0, 'msg' => '有用户持有该币种，无法删除']);
        }

        // 检查是否有交易对使用该币种
        $hasTradingPairs = \app\common\model\TradingPair::where('base_coin', $coin->symbol)
                                                       ->whereOr('quote_coin', $coin->symbol)
                                                       ->count() > 0;
        if ($hasTradingPairs) {
            return json(['code' => 0, 'msg' => '有交易对使用该币种，无法删除']);
        }

        if ($coin->delete()) {
            return json(['code' => 1, 'msg' => '币种删除成功']);
        } else {
            return json(['code' => 0, 'msg' => '币种删除失败']);
        }
    }

    /**
     * 修改币种状态
     */
    public function status()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $id = input('id/d', 0);
        $status = input('status/d', 1);
        
        $coin = CoinModel::find($id);
        if (!$coin) {
            return json(['code' => 0, 'msg' => '币种不存在']);
        }

        $coin->status = $status;
        if ($coin->save()) {
            $action = $status ? '启用' : '禁用';
            return json(['code' => 1, 'msg' => "币种{$action}成功"]);
        } else {
            return json(['code' => 0, 'msg' => '操作失败']);
        }
    }

    /**
     * 上传币种图标
     */
    public function uploadIcon()
    {
        $file = request()->file('icon');
        
        if (!$file) {
            return json(['code' => 0, 'msg' => '请选择文件']);
        }

        // 验证文件
        $validate = [
            'size' => 2 * 1024 * 1024, // 2MB
            'ext' => 'jpg,jpeg,png,gif'
        ];

        if (!$file->check($validate)) {
            return json(['code' => 0, 'msg' => $file->getError()]);
        }

        // 保存文件
        $saveName = \think\facade\Filesystem::disk('public')->putFile('coins', $file);
        
        if ($saveName) {
            $url = '/uploads/' . $saveName;
            return json(['code' => 1, 'msg' => '上传成功', 'data' => ['url' => $url]]);
        } else {
            return json(['code' => 0, 'msg' => '上传失败']);
        }
    }

    /**
     * 批量更新排序
     */
    public function updateSort()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $data = input('data/a', []);
        
        if (empty($data)) {
            return json(['code' => 0, 'msg' => '数据不能为空']);
        }

        try {
            foreach ($data as $item) {
                if (isset($item['id']) && isset($item['sort'])) {
                    CoinModel::where('id', $item['id'])->update(['sort' => $item['sort']]);
                }
            }
            
            return json(['code' => 1, 'msg' => '排序更新成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '排序更新失败：' . $e->getMessage()]);
        }
    }
}
