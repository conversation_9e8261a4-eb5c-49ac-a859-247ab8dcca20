<?php
declare (strict_types = 1);

namespace app\middleware;

use app\common\service\SecurityService;
use think\facade\Request;
use think\facade\Log;

/**
 * 安全中间件
 */
class SecurityMiddleware
{
    protected $securityService;

    public function __construct()
    {
        $this->securityService = new SecurityService();
    }

    /**
     * 处理请求
     */
    public function handle($request, \Closure $next)
    {
        $ip = $request->ip();
        
        // 检查IP黑名单
        if ($this->securityService->isIpBlacklisted($ip)) {
            Log::warning("Blocked IP access: {$ip}");
            return json(['code' => 403, 'msg' => 'Access denied'], 403);
        }

        // 检查IP白名单（如果配置了）
        if (!$this->securityService->isIpWhitelisted($ip)) {
            Log::warning("IP not in whitelist: {$ip}");
            return json(['code' => 403, 'msg' => 'Access denied'], 403);
        }

        // 防刷检查
        $rateLimitKey = $ip . ':' . $request->pathinfo();
        if (!$this->securityService->checkRateLimit($rateLimitKey, 100, 60)) {
            Log::warning("Rate limit exceeded: {$ip} - {$request->pathinfo()}");
            return json(['code' => 429, 'msg' => 'Too many requests'], 429);
        }

        // 检查请求头
        if (!$this->validateRequestHeaders($request)) {
            Log::warning("Invalid request headers: {$ip}");
            return json(['code' => 400, 'msg' => 'Invalid request'], 400);
        }

        // 检查请求大小
        if (!$this->validateRequestSize($request)) {
            Log::warning("Request too large: {$ip}");
            return json(['code' => 413, 'msg' => 'Request too large'], 413);
        }

        // SQL注入检查
        if ($this->detectSqlInjection($request)) {
            Log::warning("SQL injection attempt detected: {$ip}");
            return json(['code' => 400, 'msg' => 'Invalid request'], 400);
        }

        // XSS检查
        if ($this->detectXss($request)) {
            Log::warning("XSS attempt detected: {$ip}");
            return json(['code' => 400, 'msg' => 'Invalid request'], 400);
        }

        return $next($request);
    }

    /**
     * 验证请求头
     */
    private function validateRequestHeaders($request): bool
    {
        $userAgent = $request->header('user-agent', '');
        
        // 检查是否有User-Agent
        if (empty($userAgent)) {
            return false;
        }

        // 检查可疑的User-Agent
        $suspiciousPatterns = [
            'sqlmap',
            'nikto',
            'nmap',
            'masscan',
            'python-requests',
            'curl',
            'wget'
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (stripos($userAgent, $pattern) !== false) {
                return false;
            }
        }

        return true;
    }

    /**
     * 验证请求大小
     */
    private function validateRequestSize($request): bool
    {
        $maxSize = 10 * 1024 * 1024; // 10MB
        $contentLength = $request->header('content-length', 0);
        
        return $contentLength <= $maxSize;
    }

    /**
     * 检测SQL注入
     */
    private function detectSqlInjection($request): bool
    {
        $params = array_merge($request->param(), $request->header());
        
        $sqlPatterns = [
            '/(\bunion\b.*\bselect\b)/i',
            '/(\bselect\b.*\bfrom\b)/i',
            '/(\binsert\b.*\binto\b)/i',
            '/(\bupdate\b.*\bset\b)/i',
            '/(\bdelete\b.*\bfrom\b)/i',
            '/(\bdrop\b.*\btable\b)/i',
            '/(\bor\b.*\b1\s*=\s*1\b)/i',
            '/(\band\b.*\b1\s*=\s*1\b)/i',
            '/(\'.*\bor\b.*\')/i',
            '/(\".*\bor\b.*\")/i',
            '/(\bexec\b.*\bxp_)/i',
            '/(\bsp_executesql\b)/i'
        ];

        foreach ($params as $value) {
            if (is_string($value)) {
                foreach ($sqlPatterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * 检测XSS攻击
     */
    private function detectXss($request): bool
    {
        $params = array_merge($request->param(), $request->header());
        
        $xssPatterns = [
            '/<script[^>]*>.*?<\/script>/is',
            '/<iframe[^>]*>.*?<\/iframe>/is',
            '/<object[^>]*>.*?<\/object>/is',
            '/<embed[^>]*>.*?<\/embed>/is',
            '/javascript:/i',
            '/vbscript:/i',
            '/onload\s*=/i',
            '/onclick\s*=/i',
            '/onerror\s*=/i',
            '/onmouseover\s*=/i',
            '/onfocus\s*=/i',
            '/onblur\s*=/i'
        ];

        foreach ($params as $value) {
            if (is_string($value)) {
                foreach ($xssPatterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }
}

/**
 * API安全中间件
 */
class ApiSecurityMiddleware
{
    protected $securityService;

    public function __construct()
    {
        $this->securityService = new SecurityService();
    }

    /**
     * 处理API请求
     */
    public function handle($request, \Closure $next)
    {
        $apiKey = $request->header('X-API-KEY');
        $signature = $request->header('X-SIGNATURE');
        $timestamp = $request->header('X-TIMESTAMP');
        $nonce = $request->header('X-NONCE');

        // 检查必需的头部
        if (empty($apiKey) || empty($signature) || empty($timestamp) || empty($nonce)) {
            return json(['code' => 401, 'msg' => 'Missing required headers'], 401);
        }

        // 检查时间戳（防重放攻击）
        $currentTime = time();
        if (abs($currentTime - $timestamp) > 300) { // 5分钟内有效
            return json(['code' => 401, 'msg' => 'Request expired'], 401);
        }

        // 检查nonce（防重放攻击）
        $nonceKey = "api_nonce:{$nonce}";
        if (cache($nonceKey)) {
            return json(['code' => 401, 'msg' => 'Duplicate request'], 401);
        }
        cache($nonceKey, true, 300); // 缓存5分钟

        // 验证API密钥和签名
        $user = $this->validateApiKey($apiKey);
        if (!$user) {
            return json(['code' => 401, 'msg' => 'Invalid API key'], 401);
        }

        $params = array_merge($request->param(), [
            'timestamp' => $timestamp,
            'nonce' => $nonce
        ]);

        if (!$this->securityService->verifyRequestSignature($params, $signature, $user['secret_key'])) {
            return json(['code' => 401, 'msg' => 'Invalid signature'], 401);
        }

        // API频率限制
        $rateLimitKey = "api_rate_limit:{$user['id']}";
        if (!$this->securityService->checkRateLimit($rateLimitKey, 1000, 60)) {
            return json(['code' => 429, 'msg' => 'API rate limit exceeded'], 429);
        }

        // 将用户信息添加到请求中
        $request->user = $user;

        return $next($request);
    }

    /**
     * 验证API密钥
     */
    private function validateApiKey($apiKey)
    {
        // 这里应该从数据库查询API密钥对应的用户信息
        // 简化处理，返回模拟数据
        if ($apiKey === 'demo_api_key') {
            return [
                'id' => 1,
                'username' => 'demo',
                'secret_key' => 'demo_secret_key'
            ];
        }

        return null;
    }
}

/**
 * 登录安全中间件
 */
class LoginSecurityMiddleware
{
    protected $securityService;

    public function __construct()
    {
        $this->securityService = new SecurityService();
    }

    /**
     * 处理登录请求
     */
    public function handle($request, \Closure $next)
    {
        $ip = $request->ip();
        $email = $request->param('email', '');

        // 检查IP登录失败次数
        if (!$this->securityService->checkLoginAttempts($ip, 10, 3600)) {
            return json(['code' => 429, 'msg' => 'Too many login attempts from this IP'], 429);
        }

        // 检查邮箱登录失败次数
        if (!empty($email) && !$this->securityService->checkLoginAttempts($email, 5, 1800)) {
            return json(['code' => 429, 'msg' => 'Account temporarily locked'], 429);
        }

        // 设备指纹检查
        $fingerprint = $this->securityService->generateDeviceFingerprint();
        $request->fingerprint = $fingerprint;

        return $next($request);
    }
}
