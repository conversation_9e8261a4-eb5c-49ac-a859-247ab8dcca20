<?php
declare (strict_types = 1);

namespace app\index\controller;

use app\BaseController;
use app\common\model\TradingPair;
use app\common\model\Coin;
use think\facade\View;

/**
 * 首页控制器
 */
class Index extends BaseController
{
    /**
     * 首页
     */
    public function index()
    {
        // 获取交易对数据
        $tradingPairs = TradingPair::where('status', 1)
                                  ->order('sort', 'asc')
                                  ->limit(10)
                                  ->select();

        // 获取热门币种
        $hotCoins = Coin::where('status', 1)
                       ->where('is_trade', 1)
                       ->order('sort', 'asc')
                       ->limit(8)
                       ->select();

        // 模拟市场数据（实际项目中应该从API获取）
        $marketData = [
            'BTC/USDT' => [
                'price' => '45,280.50',
                'change' => '+2.45%',
                'volume' => '1,234.56 BTC',
                'trend' => 'up'
            ],
            'ETH/USDT' => [
                'price' => '2,845.30',
                'change' => '+1.85%',
                'volume' => '5,678.90 ETH',
                'trend' => 'up'
            ],
            'BNB/USDT' => [
                'price' => '315.80',
                'change' => '-0.65%',
                'volume' => '2,345.67 BNB',
                'trend' => 'down'
            ],
            'ADA/USDT' => [
                'price' => '0.4520',
                'change' => '+3.25%',
                'volume' => '12,345.67 ADA',
                'trend' => 'up'
            ]
        ];

        View::assign([
            'tradingPairs' => $tradingPairs,
            'hotCoins' => $hotCoins,
            'marketData' => $marketData,
            'title' => '首页'
        ]);

        return View::fetch();
    }

    /**
     * 关于我们
     */
    public function about()
    {
        View::assign([
            'title' => '关于我们'
        ]);

        return View::fetch();
    }

    /**
     * 帮助中心
     */
    public function help()
    {
        View::assign([
            'title' => '帮助中心'
        ]);

        return View::fetch();
    }

    /**
     * 公告列表
     */
    public function notice()
    {
        // 模拟公告数据
        $notices = [
            [
                'id' => 1,
                'title' => '平台升级维护公告',
                'content' => '为了给用户提供更好的服务体验，平台将于...',
                'created_at' => '2024-08-01 10:00:00',
                'is_important' => 1
            ],
            [
                'id' => 2,
                'title' => '新币种上线通知',
                'content' => '平台即将上线新的数字货币交易对...',
                'created_at' => '2024-07-30 15:30:00',
                'is_important' => 0
            ],
            [
                'id' => 3,
                'title' => '交易手续费调整公告',
                'content' => '为了提升平台竞争力，现对部分交易对手续费进行调整...',
                'created_at' => '2024-07-28 09:15:00',
                'is_important' => 0
            ]
        ];

        View::assign([
            'notices' => $notices,
            'title' => '平台公告'
        ]);

        return View::fetch();
    }

    /**
     * 公告详情
     */
    public function noticeDetail()
    {
        $id = input('id/d', 0);
        
        // 模拟公告详情数据
        $notice = [
            'id' => $id,
            'title' => '平台升级维护公告',
            'content' => '
                <p>尊敬的用户：</p>
                <p>为了给用户提供更好的服务体验，平台将于2024年8月2日02:00-06:00进行系统升级维护。</p>
                <p><strong>维护期间影响：</strong></p>
                <ul>
                    <li>网站和APP将暂时无法访问</li>
                    <li>所有交易将暂停</li>
                    <li>充值提现功能暂停</li>
                </ul>
                <p><strong>维护内容：</strong></p>
                <ul>
                    <li>系统性能优化</li>
                    <li>新功能上线</li>
                    <li>安全性升级</li>
                </ul>
                <p>维护完成后，所有功能将恢复正常。给您带来的不便，敬请谅解！</p>
                <p>如有疑问，请联系客服。</p>
            ',
            'created_at' => '2024-08-01 10:00:00',
            'is_important' => 1
        ];

        View::assign([
            'notice' => $notice,
            'title' => $notice['title']
        ]);

        return View::fetch();
    }

    /**
     * 获取市场数据API
     */
    public function marketData()
    {
        // 模拟实时市场数据
        $data = [
            'BTC/USDT' => [
                'symbol' => 'BTC/USDT',
                'price' => mt_rand(44000, 46000) + mt_rand(0, 99) / 100,
                'change_24h' => mt_rand(-500, 500) / 100,
                'volume_24h' => mt_rand(1000, 2000) + mt_rand(0, 99) / 100,
                'high_24h' => mt_rand(45000, 47000) + mt_rand(0, 99) / 100,
                'low_24h' => mt_rand(43000, 45000) + mt_rand(0, 99) / 100
            ],
            'ETH/USDT' => [
                'symbol' => 'ETH/USDT',
                'price' => mt_rand(2800, 3000) + mt_rand(0, 99) / 100,
                'change_24h' => mt_rand(-100, 100) / 100,
                'volume_24h' => mt_rand(5000, 8000) + mt_rand(0, 99) / 100,
                'high_24h' => mt_rand(2900, 3100) + mt_rand(0, 99) / 100,
                'low_24h' => mt_rand(2700, 2900) + mt_rand(0, 99) / 100
            ]
        ];

        return json($data);
    }

    /**
     * 获取K线数据API
     */
    public function klineData()
    {
        $symbol = input('symbol', 'BTC/USDT');
        $interval = input('interval', '1h');
        $limit = input('limit/d', 100);

        // 模拟K线数据
        $klineData = [];
        $basePrice = $symbol == 'BTC/USDT' ? 45000 : 2850;
        $timestamp = time() - ($limit * 3600); // 1小时间隔

        for ($i = 0; $i < $limit; $i++) {
            $open = $basePrice + mt_rand(-1000, 1000);
            $close = $open + mt_rand(-500, 500);
            $high = max($open, $close) + mt_rand(0, 200);
            $low = min($open, $close) - mt_rand(0, 200);
            $volume = mt_rand(100, 1000) + mt_rand(0, 99) / 100;

            $klineData[] = [
                $timestamp * 1000, // 时间戳（毫秒）
                $open,             // 开盘价
                $high,             // 最高价
                $low,              // 最低价
                $close,            // 收盘价
                $volume            // 成交量
            ];

            $timestamp += 3600; // 增加1小时
            $basePrice = $close; // 下一根K线的基准价格
        }

        return json($klineData);
    }
}

    public function authtest()
    {
        return "Auth test from Index controller";
    }
