<?php
/**
 * 数据库配置检查脚本
 */

// 加载环境变量
if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    $lines = explode("\n", $envContent);
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '#') === 0) {
            continue;
        }
        
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

echo "=== GVD数据库配置检查 ===\n\n";

// 检查数据库配置
$dbConfig = [
    'type' => $_ENV['DATABASE_TYPE'] ?? 'mysql',
    'hostname' => $_ENV['DATABASE_HOSTNAME'] ?? '127.0.0.1',
    'database' => $_ENV['DATABASE_DATABASE'] ?? 'crypto_exchange',
    'username' => $_ENV['DATABASE_USERNAME'] ?? 'crypto_exchange',
    'password' => $_ENV['DATABASE_PASSWORD'] ?? '',
    'port' => $_ENV['DATABASE_PORT'] ?? '3306',
    'prefix' => $_ENV['DATABASE_PREFIX'] ?? 'ce_',
    'charset' => $_ENV['DATABASE_CHARSET'] ?? 'utf8mb4'
];

echo "当前数据库配置：\n";
echo "- 类型: {$dbConfig['type']}\n";
echo "- 主机: {$dbConfig['hostname']}\n";
echo "- 端口: {$dbConfig['port']}\n";
echo "- 数据库: {$dbConfig['database']}\n";
echo "- 用户名: {$dbConfig['username']}\n";
echo "- 密码: " . (empty($dbConfig['password']) ? '未设置' : '已设置') . "\n";
echo "- 表前缀: {$dbConfig['prefix']}\n";
echo "- 字符集: {$dbConfig['charset']}\n\n";

// 测试数据库连接
echo "测试数据库连接...\n";

try {
    $dsn = "{$dbConfig['type']}:host={$dbConfig['hostname']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ 数据库连接成功！\n\n";
    
    // 检查必要的表是否存在
    echo "检查数据库表结构...\n";
    
    $requiredTables = [
        'ce_users' => '用户表',
        'ce_contract_orders' => '合约订单表',
        'ce_deposit_records' => '充值记录表',
        'ce_withdraw_records' => '提现记录表',
        'ce_user_assets' => '用户资产表',
        'ce_trading_pairs' => '交易对表',
        'ce_deposit_addresses' => '充值地址表',
        'ce_financial_records' => '财务记录表'
    ];
    
    foreach ($requiredTables as $table => $desc) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        
        if ($stmt->rowCount() > 0) {
            echo "✅ {$desc} ({$table}) - 存在\n";
        } else {
            echo "❌ {$desc} ({$table}) - 不存在\n";
        }
    }
    
    echo "\n";
    
    // 检查新增的表
    echo "检查新增表结构...\n";
    
    $newTables = [
        'ce_contract_config' => '合约配置表',
        'ce_system_config' => '系统配置表',
        'ce_system_images' => '系统图片表',
        'ce_customer_messages' => '客服消息表',
        'ce_admins' => '管理员表',
        'ce_agents' => '代理表'
    ];
    
    foreach ($newTables as $table => $desc) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        
        if ($stmt->rowCount() > 0) {
            echo "✅ {$desc} ({$table}) - 存在\n";
        } else {
            echo "❌ {$desc} ({$table}) - 需要创建\n";
        }
    }
    
    echo "\n";
    
    // 检查用户表新字段
    echo "检查用户表新字段...\n";
    
    $stmt = $pdo->prepare("DESCRIBE ce_users");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $requiredColumns = ['user_type', 'agent_id'];
    
    foreach ($requiredColumns as $column) {
        if (in_array($column, $columns)) {
            echo "✅ 用户表字段 {$column} - 存在\n";
        } else {
            echo "❌ 用户表字段 {$column} - 需要添加\n";
        }
    }
    
    echo "\n";
    
    // 检查系统配置
    echo "检查系统配置...\n";
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM ce_system_config WHERE `key` = 'universal_verification_code'");
    $stmt->execute();
    
    if ($stmt->fetchColumn() > 0) {
        echo "✅ 万能验证码配置 - 已设置\n";
    } else {
        echo "❌ 万能验证码配置 - 需要设置\n";
    }
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM ce_contract_config");
    $stmt->execute();
    
    if ($stmt->fetchColumn() > 0) {
        echo "✅ 合约配置 - 已设置\n";
    } else {
        echo "❌ 合约配置 - 需要设置\n";
    }
    
} catch (PDOException $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
    echo "\n请检查以下配置：\n";
    echo "1. 数据库服务是否启动\n";
    echo "2. 数据库名称是否正确\n";
    echo "3. 用户名和密码是否正确\n";
    echo "4. 主机地址和端口是否正确\n";
}

echo "\n=== 检查完成 ===\n";

// 显示需要执行的SQL
echo "\n如果有缺失的表或字段，请执行以下SQL：\n";
echo "mysql -u {$dbConfig['username']} -p {$dbConfig['database']} < database_structure.sql\n";
?>
