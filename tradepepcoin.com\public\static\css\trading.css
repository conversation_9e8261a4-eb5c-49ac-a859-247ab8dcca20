/* 交易界面专用样式 - 火币风格 */

/* ==================== 交易页面布局 ==================== */
.trading-layout {
  display: grid;
  grid-template-columns: 300px 1fr 300px;
  grid-template-rows: 60px 1fr;
  grid-template-areas: 
    "header header header"
    "sidebar main orderbook";
  height: 100vh;
  gap: 1px;
  background: var(--border-color);
}

.trading-header {
  grid-area: header;
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  padding: 0 20px;
  border-bottom: 1px solid var(--border-color);
}

.trading-sidebar {
  grid-area: sidebar;
  background: var(--bg-card);
  overflow-y: auto;
}

.trading-main {
  grid-area: main;
  background: var(--bg-card);
  display: grid;
  grid-template-rows: 300px 1fr;
  gap: 1px;
}

.trading-orderbook {
  grid-area: orderbook;
  background: var(--bg-card);
  overflow-y: auto;
}

/* ==================== 交易对选择器 ==================== */
.pair-selector {
  display: flex;
  align-items: center;
  gap: 16px;
}

.current-pair {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: var(--border-radius);
  transition: background-color 0.2s ease;
}

.current-pair:hover {
  background: var(--bg-hover);
}

.pair-symbol {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.pair-price {
  font-size: 16px;
  font-weight: 600;
}

.pair-change {
  font-size: 14px;
  padding: 2px 6px;
  border-radius: 2px;
  font-weight: 500;
}

.pair-change.positive {
  color: var(--color-success);
  background: rgba(14, 203, 129, 0.1);
}

.pair-change.negative {
  color: var(--color-danger);
  background: rgba(246, 70, 93, 0.1);
}

/* ==================== K线图区域 ==================== */
.chart-container {
  position: relative;
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-color);
}

.chart-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.chart-intervals {
  display: flex;
  gap: 4px;
}

.interval-btn {
  padding: 4px 8px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  border-radius: 2px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.interval-btn:hover,
.interval-btn.active {
  background: var(--color-primary);
  color: #000;
}

.chart-content {
  height: 250px;
  position: relative;
}

/* ==================== 订单簿 ==================== */
.orderbook {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.orderbook-header {
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.orderbook-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.orderbook-tabs {
  display: flex;
  gap: 16px;
}

.orderbook-tab {
  font-size: 12px;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px 0;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.orderbook-tab.active {
  color: var(--text-primary);
  border-bottom-color: var(--color-primary);
}

.orderbook-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.orderbook-table {
  flex: 1;
  overflow-y: auto;
}

.orderbook-table table {
  width: 100%;
  border-collapse: collapse;
}

.orderbook-table th {
  padding: 8px 12px;
  font-size: 11px;
  color: var(--text-secondary);
  text-align: right;
  background: var(--bg-secondary);
  position: sticky;
  top: 0;
}

.orderbook-table td {
  padding: 2px 12px;
  font-size: 12px;
  text-align: right;
  font-family: 'Monaco', 'Menlo', monospace;
}

.orderbook-sell {
  color: var(--color-danger);
}

.orderbook-buy {
  color: var(--color-success);
}

.orderbook-row {
  position: relative;
  cursor: pointer;
  transition: background-color 0.1s ease;
}

.orderbook-row:hover {
  background: var(--bg-hover);
}

.orderbook-row::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  background: currentColor;
  opacity: 0.1;
  transition: width 0.3s ease;
}

/* ==================== 交易面板 ==================== */
.trading-panel {
  padding: 16px;
}

.trading-tabs {
  display: flex;
  margin-bottom: 16px;
  border-bottom: 1px solid var(--border-color);
}

.trading-tab {
  flex: 1;
  padding: 12px;
  text-align: center;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
}

.trading-tab.active {
  color: var(--text-primary);
}

.trading-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--color-primary);
}

.trading-tab.buy.active {
  color: var(--color-success);
}

.trading-tab.buy.active::after {
  background: var(--color-success);
}

.trading-tab.sell.active {
  color: var(--color-danger);
}

.trading-tab.sell.active::after {
  background: var(--color-danger);
}

.trading-form {
  display: none;
}

.trading-form.active {
  display: block;
}

.order-type-selector {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.order-type-btn {
  flex: 1;
  padding: 8px;
  border: 1px solid var(--border-color);
  background: transparent;
  color: var(--text-secondary);
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.order-type-btn.active {
  border-color: var(--color-primary);
  color: var(--color-primary);
  background: rgba(240, 185, 11, 0.1);
}

.price-input-group {
  position: relative;
  margin-bottom: 12px;
}

.price-input {
  width: 100%;
  padding: 8px 40px 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 14px;
}

.price-unit {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 12px;
}

.amount-slider {
  margin: 12px 0;
}

.slider {
  width: 100%;
  height: 4px;
  border-radius: 2px;
  background: var(--border-color);
  outline: none;
  -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--color-primary);
  cursor: pointer;
}

.percentage-buttons {
  display: flex;
  gap: 4px;
  margin-bottom: 12px;
}

.percentage-btn {
  flex: 1;
  padding: 4px;
  border: 1px solid var(--border-color);
  background: transparent;
  color: var(--text-secondary);
  border-radius: 2px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
}

.percentage-btn:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.order-summary {
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  padding: 12px;
  margin-bottom: 16px;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
}

.summary-label {
  color: var(--text-secondary);
}

.summary-value {
  color: var(--text-primary);
  font-family: 'Monaco', 'Menlo', monospace;
}

.submit-order-btn {
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submit-order-btn.buy {
  background: var(--color-success);
  color: #fff;
}

.submit-order-btn.sell {
  background: var(--color-danger);
  color: #fff;
}

.submit-order-btn:hover:not(:disabled) {
  opacity: 0.9;
}

.submit-order-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* ==================== 资产信息 ==================== */
.asset-info {
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
}

.asset-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.asset-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.asset-value {
  font-size: 12px;
  color: var(--text-primary);
  font-family: 'Monaco', 'Menlo', monospace;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 1200px) {
  .trading-layout {
    grid-template-columns: 280px 1fr 280px;
  }
}

@media (max-width: 992px) {
  .trading-layout {
    grid-template-columns: 1fr;
    grid-template-rows: 60px 300px 1fr;
    grid-template-areas: 
      "header"
      "main"
      "sidebar";
  }
  
  .trading-orderbook {
    display: none;
  }
}

@media (max-width: 768px) {
  .trading-layout {
    grid-template-rows: 60px 250px 1fr;
  }
  
  .chart-content {
    height: 200px;
  }
}
