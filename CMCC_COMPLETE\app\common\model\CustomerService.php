<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 客服聊天模型
 */
class CustomerService extends Model
{
    protected $name = 'gvd_customer_service';
    
    protected $type = [
        'is_read' => 'boolean',
        'is_admin' => 'boolean'
    ];

    // 消息类型
    const TYPE_TEXT = 1;        // 文本消息
    const TYPE_IMAGE = 2;       // 图片消息
    const TYPE_FILE = 3;        // 文件消息
    const TYPE_SYSTEM = 4;      // 系统消息

    // 会话状态
    const STATUS_ACTIVE = 1;    // 活跃
    const STATUS_CLOSED = 2;    // 已关闭
    const STATUS_WAITING = 3;   // 等待回复

    /**
     * 获取消息类型文本
     */
    public function getTypeText(): string
    {
        $typeMap = [
            self::TYPE_TEXT => '文本',
            self::TYPE_IMAGE => '图片',
            self::TYPE_FILE => '文件',
            self::TYPE_SYSTEM => '系统'
        ];

        return $typeMap[$this->type] ?? '未知';
    }

    /**
     * 发送消息
     */
    public static function sendMessage(int $userId, string $content, int $type = self::TYPE_TEXT, bool $isAdmin = false): array
    {
        try {
            $messageData = [
                'user_id' => $userId,
                'content' => $content,
                'type' => $type,
                'is_admin' => $isAdmin,
                'is_read' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $message = self::create($messageData);

            return [
                'code' => 1,
                'msg' => '消息发送成功',
                'data' => $message->getMessageInfo()
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '发送失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取消息信息
     */
    public function getMessageInfo(): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'content' => $this->content,
            'type' => $this->type,
            'type_text' => $this->getTypeText(),
            'is_admin' => $this->is_admin,
            'is_read' => $this->is_read,
            'created_at' => $this->created_at,
            'time_ago' => $this->getTimeAgo()
        ];
    }

    /**
     * 获取时间差
     */
    private function getTimeAgo(): string
    {
        $time = strtotime($this->created_at);
        $diff = time() - $time;

        if ($diff < 60) {
            return '刚刚';
        } elseif ($diff < 3600) {
            return floor($diff / 60) . '分钟前';
        } elseif ($diff < 86400) {
            return floor($diff / 3600) . '小时前';
        } else {
            return date('m-d H:i', $time);
        }
    }

    /**
     * 获取用户聊天记录
     */
    public static function getUserMessages(int $userId, int $page = 1, int $limit = 50): array
    {
        try {
            $messages = self::where('user_id', $userId)
                           ->order('created_at', 'desc')
                           ->paginate([
                               'list_rows' => $limit,
                               'page' => $page
                           ]);

            $list = [];
            foreach ($messages->items() as $message) {
                $list[] = $message->getMessageInfo();
            }

            // 反转数组，让最新消息在底部
            $list = array_reverse($list);

            return [
                'code' => 1,
                'data' => [
                    'messages' => $list,
                    'total' => $messages->total(),
                    'page' => $page,
                    'limit' => $limit
                ]
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '获取失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取所有用户的最新消息（管理员用）
     */
    public static function getAllUserLatestMessages(int $page = 1, int $limit = 20): array
    {
        try {
            // 获取每个用户的最新消息
            $sql = "SELECT cs.*, u.username, u.email 
                    FROM gvd_customer_service cs 
                    INNER JOIN gvd_users u ON cs.user_id = u.id 
                    WHERE cs.id IN (
                        SELECT MAX(id) FROM gvd_customer_service GROUP BY user_id
                    ) 
                    ORDER BY cs.created_at DESC 
                    LIMIT " . (($page - 1) * $limit) . ", " . $limit;

            $messages = \think\facade\Db::query($sql);

            $list = [];
            foreach ($messages as $message) {
                $messageModel = new self($message);
                $messageInfo = $messageModel->getMessageInfo();
                $messageInfo['username'] = $message['username'];
                $messageInfo['email'] = $message['email'];
                $messageInfo['unread_count'] = self::getUnreadCount($message['user_id']);
                $list[] = $messageInfo;
            }

            // 获取总用户数
            $totalUsers = self::group('user_id')->count();

            return [
                'code' => 1,
                'data' => [
                    'conversations' => $list,
                    'total' => $totalUsers,
                    'page' => $page,
                    'limit' => $limit
                ]
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '获取失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取未读消息数量
     */
    public static function getUnreadCount(int $userId, bool $isAdmin = false): int
    {
        try {
            $query = self::where('user_id', $userId)
                        ->where('is_read', false);

            if ($isAdmin) {
                // 管理员查看用户发送的未读消息
                $query->where('is_admin', false);
            } else {
                // 用户查看管理员发送的未读消息
                $query->where('is_admin', true);
            }

            return $query->count();

        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * 标记消息为已读
     */
    public static function markAsRead(int $userId, bool $isAdmin = false): bool
    {
        try {
            $query = self::where('user_id', $userId)
                        ->where('is_read', false);

            if ($isAdmin) {
                // 管理员标记用户消息为已读
                $query->where('is_admin', false);
            } else {
                // 用户标记管理员消息为已读
                $query->where('is_admin', true);
            }

            $query->update(['is_read' => true, 'updated_at' => date('Y-m-d H:i:s')]);

            return true;

        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 发送系统消息
     */
    public static function sendSystemMessage(int $userId, string $content): array
    {
        return self::sendMessage($userId, $content, self::TYPE_SYSTEM, true);
    }

    /**
     * 发送欢迎消息
     */
    public static function sendWelcomeMessage(int $userId): array
    {
        $welcomeText = "您好！欢迎使用GVD交易平台客服系统。\n\n我们的服务时间：\n周一至周日 9:00-21:00\n\n如有任何问题，请随时联系我们！";
        
        return self::sendSystemMessage($userId, $welcomeText);
    }

    /**
     * 获取客服统计
     */
    public static function getServiceStats(): array
    {
        try {
            $totalMessages = self::count();
            $todayMessages = self::whereTime('created_at', 'today')->count();
            $totalUsers = self::group('user_id')->count();
            $unreadMessages = self::where('is_read', false)->where('is_admin', false)->count();

            return [
                'code' => 1,
                'data' => [
                    'total_messages' => $totalMessages,
                    'today_messages' => $todayMessages,
                    'total_users' => $totalUsers,
                    'unread_messages' => $unreadMessages
                ]
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '获取失败：' . $e->getMessage()];
        }
    }

    /**
     * 搜索消息
     */
    public static function searchMessages(string $keyword, int $page = 1, int $limit = 20): array
    {
        try {
            $messages = self::where('content', 'like', "%{$keyword}%")
                           ->with(['user'])
                           ->order('created_at', 'desc')
                           ->paginate([
                               'list_rows' => $limit,
                               'page' => $page
                           ]);

            $list = [];
            foreach ($messages->items() as $message) {
                $messageInfo = $message->getMessageInfo();
                if ($message->user) {
                    $messageInfo['username'] = $message->user->username;
                }
                $list[] = $messageInfo;
            }

            return [
                'code' => 1,
                'data' => [
                    'messages' => $list,
                    'total' => $messages->total(),
                    'page' => $page,
                    'limit' => $limit
                ]
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '搜索失败：' . $e->getMessage()];
        }
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
