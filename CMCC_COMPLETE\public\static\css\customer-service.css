/* GVD客服系统样式 - WhatsApp风格 */

/* 悬浮按钮 */
.gvd-customer-btn {
    position: fixed;
    right: 20px;
    bottom: 20px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #1890ff 0%, #00d4aa 100%);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(24, 144, 255, 0.4);
    z-index: 9998;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    user-select: none;
}

.gvd-customer-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(24, 144, 255, 0.6);
}

.gvd-customer-btn.active {
    background: linear-gradient(135deg, #f84960 0%, #ff6b7a 100%);
}

.gvd-customer-btn .btn-icon {
    color: white;
    font-size: 24px;
    transition: transform 0.3s ease;
}

.gvd-customer-btn.active .btn-icon {
    transform: rotate(180deg);
}

.gvd-customer-btn .btn-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #f84960;
    color: white;
    border-radius: 12px;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: 600;
    min-width: 18px;
    text-align: center;
    border: 2px solid white;
}

.gvd-customer-btn .btn-pulse {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: inherit;
    animation: pulse 2s infinite;
    opacity: 0.6;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.6;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.3;
    }
    100% {
        transform: scale(1.4);
        opacity: 0;
    }
}

/* 聊天窗口 */
.gvd-chat-window {
    position: fixed;
    right: 20px;
    bottom: 90px;
    width: 380px;
    height: 600px;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    display: flex;
    flex-direction: column;
    transform: translateY(100%) scale(0.8);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.gvd-chat-window.open {
    transform: translateY(0) scale(1);
    opacity: 1;
}

/* 聊天头部 */
.chat-header {
    background: linear-gradient(135deg, #1890ff 0%, #00d4aa 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 16px 16px 0 0;
}

.header-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-info .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    position: relative;
    overflow: hidden;
}

.header-info .avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.header-info .avatar .avatar-fallback {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.header-info .info .name {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 2px;
}

.header-info .info .status {
    font-size: 12px;
    opacity: 0.8;
}

.header-info .info .status.online {
    color: #02c076;
}

.header-info .info .status.offline {
    color: #848e9c;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s ease;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* 聊天主体 */
.chat-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;
    position: relative;
}

.messages-container {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    scroll-behavior: smooth;
}

.messages-container::-webkit-scrollbar {
    width: 4px;
}

.messages-container::-webkit-scrollbar-track {
    background: transparent;
}

.messages-container::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 2px;
}

/* 欢迎消息 */
.welcome-message {
    text-align: center;
    padding: 40px 20px;
    color: #848e9c;
}

.welcome-message .welcome-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.welcome-message h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1890ff;
    margin-bottom: 8px;
}

.welcome-message p {
    font-size: 14px;
    line-height: 1.5;
}

/* 消息样式 */
.message {
    display: flex;
    margin-bottom: 16px;
    animation: messageSlideIn 0.3s ease;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.own {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #1890ff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    flex-shrink: 0;
    margin: 0 8px;
}

.message.own .message-avatar {
    background: #00d4aa;
}

.message-content {
    max-width: 70%;
    display: flex;
    flex-direction: column;
}

.message.own .message-content {
    align-items: flex-end;
}

.message-bubble {
    background: white;
    padding: 12px 16px;
    border-radius: 18px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    position: relative;
    word-wrap: break-word;
}

.message.own .message-bubble {
    background: #1890ff;
    color: white;
}

.message-bubble::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border: 6px solid transparent;
}

.message:not(.own) .message-bubble::before {
    left: -12px;
    top: 12px;
    border-right-color: white;
}

.message.own .message-bubble::before {
    right: -12px;
    top: 12px;
    border-left-color: #1890ff;
}

.message-text {
    font-size: 14px;
    line-height: 1.4;
}

.message-image img {
    max-width: 200px;
    max-height: 200px;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.message-image img:hover {
    transform: scale(1.02);
}

.message-emoji {
    font-size: 32px;
    line-height: 1;
}

.message-time {
    font-size: 11px;
    color: #848e9c;
    margin-top: 4px;
    padding: 0 4px;
}

.message.sending .message-bubble {
    opacity: 0.6;
    position: relative;
}

.message.sending .message-bubble::after {
    content: '';
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    border: 2px solid currentColor;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: translateY(-50%) rotate(360deg);
    }
}

.message.failed .message-bubble {
    background: #f84960;
    color: white;
}

/* 输入指示器 */
.typing-indicator {
    display: flex;
    align-items: center;
    padding: 8px 20px;
    gap: 8px;
}

.typing-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #1890ff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 600;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #848e9c;
    animation: typingDots 1.4s infinite;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typingDots {
    0%, 60%, 100% {
        transform: scale(1);
        opacity: 0.5;
    }
    30% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* 聊天底部 */
.chat-footer {
    background: white;
    border-top: 1px solid #e8e8e8;
    position: relative;
}

/* 表情面板 */
.emoji-panel {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e8e8e8;
    border-bottom: none;
    max-height: 200px;
    display: none;
}

.emoji-tabs {
    display: flex;
    border-bottom: 1px solid #e8e8e8;
    background: #f8f9fa;
}

.emoji-tab {
    flex: 1;
    padding: 12px;
    border: none;
    background: transparent;
    font-size: 18px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.emoji-tab:hover {
    background: #e8e8e8;
}

.emoji-tab.active {
    background: white;
    border-bottom: 2px solid #1890ff;
}

.emoji-content {
    padding: 16px;
    max-height: 140px;
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 8px;
}

.emoji-item {
    font-size: 20px;
    padding: 8px;
    border-radius: 6px;
    cursor: pointer;
    text-align: center;
    transition: background 0.2s ease;
    user-select: none;
}

.emoji-item:hover {
    background: #f0f0f0;
    transform: scale(1.1);
}

/* 输入区域 */
.input-area {
    display: flex;
    align-items: flex-end;
    padding: 16px;
    gap: 12px;
}

.tool-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: transparent;
    color: #848e9c;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.tool-btn:hover {
    background: #f0f0f0;
    color: #1890ff;
}

.input-wrapper {
    flex: 1;
    position: relative;
    background: #f8f9fa;
    border-radius: 20px;
    border: 1px solid #e8e8e8;
    transition: border-color 0.2s ease;
}

.input-wrapper:focus-within {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.input-wrapper textarea {
    width: 100%;
    border: none;
    background: transparent;
    padding: 12px 16px;
    font-size: 14px;
    line-height: 1.4;
    resize: none;
    outline: none;
    font-family: inherit;
    max-height: 120px;
    min-height: 20px;
}

.input-wrapper textarea::placeholder {
    color: #848e9c;
}

.input-counter {
    position: absolute;
    bottom: 4px;
    right: 8px;
    font-size: 10px;
    color: #848e9c;
    pointer-events: none;
}

.send-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: #e8e8e8;
    color: #848e9c;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.send-btn:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

.send-btn.active {
    background: #1890ff;
    color: white;
    transform: scale(1.05);
}

.send-btn.active:hover {
    background: #0f7ae5;
}

/* 通知样式 */
.gvd-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 16px 20px;
    max-width: 300px;
    z-index: 10000;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    animation: notificationSlideIn 0.3s ease;
}

@keyframes notificationSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.gvd-notification.error {
    border-left: 4px solid #f84960;
}

.gvd-notification.warning {
    border-left: 4px solid #f0b90b;
}

.gvd-notification.success {
    border-left: 4px solid #02c076;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 4px;
    color: #1a1a1a;
}

.notification-message {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}

.notification-actions {
    margin-top: 12px;
    display: flex;
    gap: 8px;
}

.notification-btn {
    padding: 6px 12px;
    border: 1px solid #e8e8e8;
    background: white;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.notification-btn:hover {
    background: #f8f9fa;
    border-color: #1890ff;
    color: #1890ff;
}

.notification-close {
    width: 20px;
    height: 20px;
    border: none;
    background: transparent;
    color: #848e9c;
    cursor: pointer;
    font-size: 16px;
    line-height: 1;
    flex-shrink: 0;
}

.notification-close:hover {
    color: #f84960;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .gvd-customer-btn {
        right: 16px;
        bottom: 16px;
        width: 56px;
        height: 56px;
    }

    .gvd-chat-window {
        right: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
        border-radius: 0;
    }

    .gvd-chat-window.open {
        transform: translateY(0) scale(1);
    }

    .chat-header {
        border-radius: 0;
        padding: 16px;
    }

    .input-area {
        padding: 12px 16px;
    }

    .emoji-content {
        grid-template-columns: repeat(6, 1fr);
    }

    .gvd-notification {
        right: 16px;
        top: 16px;
        max-width: calc(100% - 32px);
    }
}

@media (max-width: 480px) {
    .emoji-content {
        grid-template-columns: repeat(5, 1fr);
    }

    .input-area {
        gap: 8px;
    }

    .tool-btn {
        width: 32px;
        height: 32px;
    }

    .send-btn {
        width: 32px;
        height: 32px;
    }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
    .gvd-chat-window {
        background: #1e2329;
    }

    .chat-body {
        background: #0b1426;
    }

    .message-bubble {
        background: #2b3139;
        color: #ffffff;
    }

    .message.own .message-bubble {
        background: #1890ff;
    }

    .chat-footer {
        background: #1e2329;
        border-top-color: #2b3139;
    }

    .input-wrapper {
        background: #2b3139;
        border-color: #3c4043;
        color: #ffffff;
    }

    .input-wrapper textarea {
        color: #ffffff;
    }

    .input-wrapper textarea::placeholder {
        color: #848e9c;
    }

    .emoji-panel {
        background: #1e2329;
        border-color: #2b3139;
    }

    .emoji-tabs {
        background: #2b3139;
        border-bottom-color: #3c4043;
    }

    .emoji-tab.active {
        background: #1e2329;
    }

    .emoji-item:hover {
        background: #2b3139;
    }

    .gvd-notification {
        background: #1e2329;
        color: #ffffff;
    }

    .notification-title {
        color: #ffffff;
    }

    .notification-message {
        color: #848e9c;
    }
}

/* 搜索面板 */
.search-panel {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    display: flex;
    flex-direction: column;
}

.search-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    gap: 12px;
}

.search-header input {
    flex: 1;
    border: 1px solid #e8e8e8;
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 14px;
    outline: none;
}

.search-header input:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.search-close {
    width: 32px;
    height: 32px;
    border: none;
    background: #f0f0f0;
    border-radius: 50%;
    cursor: pointer;
    font-size: 16px;
    color: #666;
}

.search-close:hover {
    background: #e8e8e8;
}

.search-results {
    flex: 1;
    overflow-y: auto;
    padding: 16px 20px;
}

.search-tip {
    text-align: center;
    color: #848e9c;
    font-size: 14px;
    padding: 40px 20px;
}

.search-result-item {
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.2s ease;
    border-bottom: 1px solid #f0f0f0;
}

.search-result-item:hover {
    background: #f8f9fa;
}

.result-content {
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 6px;
    color: #1a1a1a;
}

.result-content mark {
    background: #fff3cd;
    color: #856404;
    padding: 1px 2px;
    border-radius: 2px;
}

.result-meta {
    display: flex;
    justify-content: space-between;
    font-size: 11px;
    color: #848e9c;
}

/* 消息右键菜单 */
.message-context-menu {
    background: white;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 8px 0;
    min-width: 150px;
}

.context-menu-item {
    padding: 8px 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #1a1a1a;
    transition: background 0.2s ease;
}

.context-menu-item:hover {
    background: #f8f9fa;
}

.context-menu-item .icon {
    font-size: 16px;
}

/* 消息高亮 */
.message.highlight {
    animation: messageHighlight 2s ease;
}

@keyframes messageHighlight {
    0%, 100% {
        background: transparent;
    }
    50% {
        background: rgba(24, 144, 255, 0.1);
    }
}

/* 消息状态 */
.message-status {
    font-size: 10px;
    margin-left: 4px;
    opacity: 0.7;
}

.message-status.sent {
    color: #848e9c;
}

.message-status.delivered {
    color: #1890ff;
}

.message-status.read {
    color: #02c076;
}

.message-status.failed {
    color: #f84960;
}

/* 拖拽状态 */
.gvd-customer-btn.dragging {
    opacity: 0.8;
    transform: scale(1.1);
    cursor: grabbing;
}

/* 加载状态 */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 连接状态指示器 */
.connection-status {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #02c076;
}

.connection-status.connecting {
    background: #f0b90b;
    animation: pulse 1s infinite;
}

.connection-status.disconnected {
    background: #f84960;
}

/* 快捷键提示 */
.keyboard-shortcut {
    position: absolute;
    bottom: 8px;
    left: 8px;
    font-size: 10px;
    color: #848e9c;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gvd-chat-window:hover .keyboard-shortcut {
    opacity: 1;
}

/* 消息引用样式 */
.message-reply {
    background: rgba(24, 144, 255, 0.1);
    border-left: 3px solid #1890ff;
    padding: 8px 12px;
    margin-bottom: 8px;
    border-radius: 4px;
    font-size: 12px;
    color: #666;
}

.message-reply .reply-author {
    font-weight: 600;
    color: #1890ff;
}

.message-reply .reply-content {
    margin-top: 2px;
    font-style: italic;
}

/* 文件上传进度 */
.upload-progress {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    padding: 12px;
    margin: 8px 0;
    border: 1px solid #e8e8e8;
}

.upload-progress-bar {
    width: 100%;
    height: 4px;
    background: #f0f0f0;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 8px;
}

.upload-progress-fill {
    height: 100%;
    background: #1890ff;
    transition: width 0.3s ease;
}

/* 表情面板增强 */
.emoji-search {
    padding: 12px 16px;
    border-bottom: 1px solid #e8e8e8;
}

.emoji-search input {
    width: 100%;
    border: 1px solid #e8e8e8;
    border-radius: 16px;
    padding: 6px 12px;
    font-size: 12px;
    outline: none;
}

.emoji-search input:focus {
    border-color: #1890ff;
}

.emoji-recent {
    padding: 8px 16px;
    border-bottom: 1px solid #e8e8e8;
}

.emoji-recent-title {
    font-size: 11px;
    color: #848e9c;
    margin-bottom: 8px;
    font-weight: 500;
}

.emoji-recent-list {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

.emoji-recent-item {
    font-size: 16px;
    padding: 4px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.emoji-recent-item:hover {
    background: #f0f0f0;
}

/* 暗色主题增强 */
@media (prefers-color-scheme: dark) {
    .search-panel {
        background: rgba(30, 35, 41, 0.95);
    }

    .search-header {
        border-bottom-color: #2b3139;
    }

    .search-header input {
        background: #2b3139;
        border-color: #3c4043;
        color: #ffffff;
    }

    .search-close {
        background: #2b3139;
        color: #ffffff;
    }

    .search-close:hover {
        background: #3c4043;
    }

    .search-result-item {
        border-bottom-color: #2b3139;
    }

    .search-result-item:hover {
        background: #2b3139;
    }

    .result-content {
        color: #ffffff;
    }

    .message-context-menu {
        background: #1e2329;
        border-color: #2b3139;
    }

    .context-menu-item {
        color: #ffffff;
    }

    .context-menu-item:hover {
        background: #2b3139;
    }

    .upload-progress {
        background: rgba(30, 35, 41, 0.9);
        border-color: #2b3139;
    }

    .emoji-search {
        border-bottom-color: #2b3139;
    }

    .emoji-search input {
        background: #2b3139;
        border-color: #3c4043;
        color: #ffffff;
    }

    .emoji-recent {
        border-bottom-color: #2b3139;
    }
}
