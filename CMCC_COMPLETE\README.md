# 🚀 GVD数字货币交易平台

[![PHP Version](https://img.shields.io/badge/PHP-8.1+-blue.svg)](https://php.net)
[![ThinkPHP](https://img.shields.io/badge/ThinkPHP-6.1-green.svg)](https://thinkphp.cn)
[![MySQL](https://img.shields.io/badge/MySQL-8.0+-orange.svg)](https://mysql.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

基于ThinkPHP 6.1开发的专业数字货币交易平台，支持杠杆交易、期货合约、用户管理等核心功能。

## ✨ 核心功能

### 🎯 交易功能
- **杠杆交易** - 支持1-100倍杠杆，自动强制平仓
- **期货合约** - 永续合约和定期合约交易
- **止盈止损** - 智能风险控制机制
- **实时行情** - 集成TradingView图表
- **自动撮合** - 高性能订单撮合引擎

### 👥 用户系统
- **多级代理** - 三级代理分佣体系
- **KYC认证** - 身份验证和风控
- **资产管理** - 多币种钱包系统
- **邀请奖励** - 推广返佣机制

### 🛡️ 安全特性
- **JWT认证** - 安全的API访问控制
- **API限流** - 防止恶意攻击
- **数据加密** - 敏感信息加密存储
- **操作日志** - 完整的审计追踪

### 📊 管理后台
- **用户管理** - 用户信息和资产管理
- **交易控制** - 订单监控和风险控制
- **财务管理** - 充值提现审核
- **数据统计** - 实时业务数据分析

## 🔧 技术栈

- **后端框架**: ThinkPHP 6.1
- **数据库**: MySQL 8.0
- **缓存**: Redis 6.0
- **Web服务器**: Nginx 1.20+
- **PHP版本**: 8.1+
- **前端**: HTML5 + CSS3 + JavaScript ES6
- **图表**: TradingView Widget
- **WebSocket**: Workerman

## 📋 系统要求

### 服务器环境
- **操作系统**: Linux (推荐 Ubuntu 20.04+)
- **PHP**: 8.1+ 
- **MySQL**: 8.0+
- **Redis**: 6.0+
- **Nginx**: 1.20+

### PHP扩展
```
- pdo
- pdo_mysql
- mbstring
- openssl
- tokenizer
- xml
- ctype
- json
- bcmath
- curl
- fileinfo
- gd
- redis
```

## 🚀 快速安装

### 1. 下载项目
```bash
git clone https://github.com/gvd-team/trading-platform.git
cd trading-platform
```

### 2. 安装依赖
```bash
composer install --no-dev --optimize-autoloader
```

### 3. 配置环境
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库等信息
```

### 4. 创建数据库
```bash
mysql -u root -p -e "CREATE DATABASE gvd_trading CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
```

### 5. 导入数据库
```bash
mysql -u root -p gvd_trading < database/gvd_trading.sql
```

### 6. 设置权限
```bash
chmod -R 755 .
chmod -R 777 runtime/
chmod -R 777 public/uploads/
```

### 7. 配置Nginx
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/project/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

### 8. 启动服务
```bash
# 启动定时任务
crontab -e
# 添加以下内容：
* * * * * cd /path/to/project && php think trading:settle
* * * * * cd /path/to/project && php think trading:price-update

# 启动WebSocket服务
php think websocket:start
```

## 🎮 使用说明

### 默认账号
- **管理员**: admin / admin123
- **代理**: agent / agent123

### 访问地址
- **前台**: http://your-domain.com/
- **管理后台**: http://your-domain.com/admin
- **代理后台**: http://your-domain.com/agent

### API文档
- **接口地址**: http://your-domain.com/api
- **认证方式**: Bearer Token (JWT)
- **数据格式**: JSON

## 📁 目录结构

```
CMCC_COMPLETE/
├── app/                    # 应用目录
│   ├── api/               # API控制器
│   ├── admin/             # 管理后台
│   ├── agent/             # 代理后台
│   ├── common/            # 公共文件
│   ├── middleware/        # 中间件
│   └── command/           # 命令行工具
├── config/                # 配置文件
├── database/              # 数据库文件
├── public/                # 公共资源
│   ├── static/           # 静态文件
│   └── index.php         # 入口文件
├── route/                 # 路由配置
├── runtime/               # 运行时文件
├── vendor/                # Composer依赖
├── .env                   # 环境配置
├── composer.json          # 依赖配置
├── think                  # 命令行工具
└── README.md             # 说明文档
```

## 🔧 配置说明

### 数据库配置
```env
DATABASE_TYPE = mysql
DATABASE_HOSTNAME = 127.0.0.1
DATABASE_DATABASE = gvd_trading
DATABASE_USERNAME = root
DATABASE_PASSWORD = your_password
DATABASE_HOSTPORT = 3306
DATABASE_PREFIX = gvd_
```

### Redis配置
```env
REDIS_HOSTNAME = 127.0.0.1
REDIS_PORT = 6379
REDIS_PASSWORD = 
REDIS_SELECT = 0
```

### 交易配置
```env
TRADING_FEE_RATE = 0.002
LEVERAGE_MAX = 100
FUTURES_MARGIN_RATE = 0.1
```

## 🛠️ 开发指南

### 命令行工具
```bash
# 查看所有命令
php think

# 交易结算
php think trading:settle

# 价格更新
php think trading:price-update

# 系统健康检查
php think system:health-check

# 数据备份
php think system:backup
```

### API开发
```php
// 创建新的API控制器
class ExampleController extends BaseController
{
    public function index()
    {
        return $this->success($data, '操作成功');
    }
}
```

## 🔒 安全建议

1. **修改默认密码** - 立即修改所有默认账号密码
2. **启用HTTPS** - 配置SSL证书
3. **防火墙设置** - 限制不必要的端口访问
4. **定期备份** - 设置自动数据备份
5. **监控日志** - 定期检查系统日志
6. **更新维护** - 及时更新系统和依赖

## 📞 技术支持

- **官方网站**: https://gvd-trading.com
- **技术文档**: https://docs.gvd-trading.com
- **问题反馈**: https://github.com/gvd-team/trading-platform/issues
- **QQ群**: 123456789
- **微信群**: 扫描二维码加入

## 📄 开源协议

本项目基于 [MIT License](LICENSE) 开源协议。

## 🙏 致谢

感谢以下开源项目的支持：
- [ThinkPHP](https://thinkphp.cn)
- [TradingView](https://tradingview.com)
- [Workerman](https://workerman.net)

---

**⚠️ 免责声明**: 本项目仅供学习交流使用，请遵守当地法律法规。
