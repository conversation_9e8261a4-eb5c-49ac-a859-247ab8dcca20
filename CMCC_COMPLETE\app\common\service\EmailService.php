<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Log;
use think\facade\Cache;

/**
 * 邮件服务类
 */
class EmailService
{
    private $smtpHost = 'smtp.gmail.com';
    private $smtpPort = 587;
    private $smtpUsername = '<EMAIL>';
    private $smtpPassword = 'your-app-password';
    private $fromEmail = '<EMAIL>';
    private $fromName = 'GVD交易平台';

    /**
     * 发送验证码邮件
     */
    public function sendVerificationCode(string $email, string $type = 'register'): array
    {
        try {
            // 生成6位数字验证码
            $code = sprintf('%06d', mt_rand(0, 999999));
            
            // 缓存验证码，5分钟有效
            $cacheKey = "email_code_{$type}_{$email}";
            Cache::set($cacheKey, $code, 300);
            
            // 邮件主题和内容
            $subject = $this->getCodeEmailSubject($type);
            $content = $this->getCodeEmailContent($code, $type);
            
            // 发送邮件
            $result = $this->sendEmail($email, $subject, $content);
            
            if ($result) {
                Log::info('验证码邮件发送成功', [
                    'email' => $email,
                    'type' => $type,
                    'code' => $code // 开发环境记录验证码
                ]);
                
                return [
                    'code' => 1,
                    'msg' => '验证码发送成功',
                    'data' => [
                        'code' => $code, // 开发环境返回验证码
                        'expires_in' => 300
                    ]
                ];
            } else {
                return ['code' => 0, 'msg' => '邮件发送失败'];
            }
            
        } catch (\Exception $e) {
            Log::error('发送验证码邮件失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '发送失败'];
        }
    }

    /**
     * 验证邮件验证码
     */
    public function verifyEmailCode(string $email, string $code, string $type = 'register'): bool
    {
        try {
            $cacheKey = "email_code_{$type}_{$email}";
            $cachedCode = Cache::get($cacheKey);
            
            if ($cachedCode && $cachedCode === $code) {
                // 验证成功后删除缓存
                Cache::delete($cacheKey);
                return true;
            }
            
            return false;
            
        } catch (\Exception $e) {
            Log::error('验证邮件验证码失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 发送交易通知邮件
     */
    public function sendTradeNotification(string $email, array $tradeData): array
    {
        try {
            $subject = '交易通知 - GVD交易平台';
            $content = $this->getTradeNotificationContent($tradeData);
            
            $result = $this->sendEmail($email, $subject, $content);
            
            if ($result) {
                return ['code' => 1, 'msg' => '通知邮件发送成功'];
            } else {
                return ['code' => 0, 'msg' => '邮件发送失败'];
            }
            
        } catch (\Exception $e) {
            Log::error('发送交易通知邮件失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '发送失败'];
        }
    }

    /**
     * 发送充值通知邮件
     */
    public function sendDepositNotification(string $email, array $depositData): array
    {
        try {
            $subject = '充值通知 - GVD交易平台';
            $content = $this->getDepositNotificationContent($depositData);
            
            $result = $this->sendEmail($email, $subject, $content);
            
            if ($result) {
                return ['code' => 1, 'msg' => '通知邮件发送成功'];
            } else {
                return ['code' => 0, 'msg' => '邮件发送失败'];
            }
            
        } catch (\Exception $e) {
            Log::error('发送充值通知邮件失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '发送失败'];
        }
    }

    /**
     * 发送提现通知邮件
     */
    public function sendWithdrawNotification(string $email, array $withdrawData): array
    {
        try {
            $subject = '提现通知 - GVD交易平台';
            $content = $this->getWithdrawNotificationContent($withdrawData);
            
            $result = $this->sendEmail($email, $subject, $content);
            
            if ($result) {
                return ['code' => 1, 'msg' => '通知邮件发送成功'];
            } else {
                return ['code' => 0, 'msg' => '邮件发送失败'];
            }
            
        } catch (\Exception $e) {
            Log::error('发送提现通知邮件失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '发送失败'];
        }
    }

    /**
     * 发送IDO认购通知邮件
     */
    public function sendIdoNotification(string $email, array $idoData): array
    {
        try {
            $subject = 'IDO认购通知 - GVD交易平台';
            $content = $this->getIdoNotificationContent($idoData);
            
            $result = $this->sendEmail($email, $subject, $content);
            
            if ($result) {
                return ['code' => 1, 'msg' => '通知邮件发送成功'];
            } else {
                return ['code' => 0, 'msg' => '邮件发送失败'];
            }
            
        } catch (\Exception $e) {
            Log::error('发送IDO通知邮件失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '发送失败'];
        }
    }

    /**
     * 发送邮件
     */
    private function sendEmail(string $to, string $subject, string $content): bool
    {
        try {
            // 在实际环境中，这里应该使用真实的SMTP发送
            // 这里使用模拟发送，实际项目中需要配置真实的SMTP服务
            
            // 模拟发送成功
            Log::info('邮件发送模拟', [
                'to' => $to,
                'subject' => $subject,
                'content' => $content
            ]);
            
            return true;
            
            // 真实SMTP发送代码示例：
            /*
            $mail = new \PHPMailer\PHPMailer\PHPMailer(true);
            
            $mail->isSMTP();
            $mail->Host = $this->smtpHost;
            $mail->SMTPAuth = true;
            $mail->Username = $this->smtpUsername;
            $mail->Password = $this->smtpPassword;
            $mail->SMTPSecure = 'tls';
            $mail->Port = $this->smtpPort;
            $mail->CharSet = 'UTF-8';
            
            $mail->setFrom($this->fromEmail, $this->fromName);
            $mail->addAddress($to);
            
            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $content;
            
            return $mail->send();
            */
            
        } catch (\Exception $e) {
            Log::error('邮件发送失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取验证码邮件主题
     */
    private function getCodeEmailSubject(string $type): string
    {
        $subjects = [
            'register' => '注册验证码 - GVD交易平台',
            'login' => '登录验证码 - GVD交易平台',
            'reset_password' => '重置密码验证码 - GVD交易平台',
            'change_email' => '更换邮箱验证码 - GVD交易平台'
        ];

        return $subjects[$type] ?? '验证码 - GVD交易平台';
    }

    /**
     * 获取验证码邮件内容
     */
    private function getCodeEmailContent(string $code, string $type): string
    {
        $actions = [
            'register' => '注册账号',
            'login' => '登录账号',
            'reset_password' => '重置密码',
            'change_email' => '更换邮箱'
        ];

        $action = $actions[$type] ?? '验证操作';

        return "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <div style='background: #667eea; color: white; padding: 20px; text-align: center;'>
                <h1>GVD交易平台</h1>
            </div>
            <div style='padding: 30px; background: #f9f9f9;'>
                <h2>您的验证码</h2>
                <p>您正在进行{$action}操作，验证码为：</p>
                <div style='background: white; padding: 20px; text-align: center; margin: 20px 0; border-radius: 5px;'>
                    <span style='font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 5px;'>{$code}</span>
                </div>
                <p style='color: #666;'>验证码有效期为5分钟，请及时使用。</p>
                <p style='color: #666;'>如果这不是您的操作，请忽略此邮件。</p>
            </div>
            <div style='background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;'>
                <p>© 2024 GVD交易平台 版权所有</p>
            </div>
        </div>
        ";
    }

    /**
     * 获取交易通知邮件内容
     */
    private function getTradeNotificationContent(array $data): string
    {
        return "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <div style='background: #667eea; color: white; padding: 20px; text-align: center;'>
                <h1>交易通知</h1>
            </div>
            <div style='padding: 30px; background: #f9f9f9;'>
                <h2>交易成功</h2>
                <p>您的交易已成功执行：</p>
                <table style='width: 100%; border-collapse: collapse; margin: 20px 0;'>
                    <tr><td style='padding: 10px; border-bottom: 1px solid #ddd;'><strong>交易对：</strong></td><td style='padding: 10px; border-bottom: 1px solid #ddd;'>{$data['symbol']}</td></tr>
                    <tr><td style='padding: 10px; border-bottom: 1px solid #ddd;'><strong>类型：</strong></td><td style='padding: 10px; border-bottom: 1px solid #ddd;'>{$data['side']}</td></tr>
                    <tr><td style='padding: 10px; border-bottom: 1px solid #ddd;'><strong>价格：</strong></td><td style='padding: 10px; border-bottom: 1px solid #ddd;'>{$data['price']}</td></tr>
                    <tr><td style='padding: 10px; border-bottom: 1px solid #ddd;'><strong>数量：</strong></td><td style='padding: 10px; border-bottom: 1px solid #ddd;'>{$data['amount']}</td></tr>
                    <tr><td style='padding: 10px; border-bottom: 1px solid #ddd;'><strong>时间：</strong></td><td style='padding: 10px; border-bottom: 1px solid #ddd;'>{$data['time']}</td></tr>
                </table>
            </div>
        </div>
        ";
    }

    /**
     * 获取充值通知邮件内容
     */
    private function getDepositNotificationContent(array $data): string
    {
        return "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <div style='background: #2ecc71; color: white; padding: 20px; text-align: center;'>
                <h1>充值通知</h1>
            </div>
            <div style='padding: 30px; background: #f9f9f9;'>
                <h2>充值成功</h2>
                <p>您的充值已到账：</p>
                <table style='width: 100%; border-collapse: collapse; margin: 20px 0;'>
                    <tr><td style='padding: 10px; border-bottom: 1px solid #ddd;'><strong>币种：</strong></td><td style='padding: 10px; border-bottom: 1px solid #ddd;'>{$data['coin_symbol']}</td></tr>
                    <tr><td style='padding: 10px; border-bottom: 1px solid #ddd;'><strong>金额：</strong></td><td style='padding: 10px; border-bottom: 1px solid #ddd;'>{$data['amount']}</td></tr>
                    <tr><td style='padding: 10px; border-bottom: 1px solid #ddd;'><strong>时间：</strong></td><td style='padding: 10px; border-bottom: 1px solid #ddd;'>{$data['time']}</td></tr>
                </table>
            </div>
        </div>
        ";
    }

    /**
     * 获取提现通知邮件内容
     */
    private function getWithdrawNotificationContent(array $data): string
    {
        return "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <div style='background: #f39c12; color: white; padding: 20px; text-align: center;'>
                <h1>提现通知</h1>
            </div>
            <div style='padding: 30px; background: #f9f9f9;'>
                <h2>提现申请已提交</h2>
                <p>您的提现申请正在处理中：</p>
                <table style='width: 100%; border-collapse: collapse; margin: 20px 0;'>
                    <tr><td style='padding: 10px; border-bottom: 1px solid #ddd;'><strong>币种：</strong></td><td style='padding: 10px; border-bottom: 1px solid #ddd;'>{$data['coin_symbol']}</td></tr>
                    <tr><td style='padding: 10px; border-bottom: 1px solid #ddd;'><strong>金额：</strong></td><td style='padding: 10px; border-bottom: 1px solid #ddd;'>{$data['amount']}</td></tr>
                    <tr><td style='padding: 10px; border-bottom: 1px solid #ddd;'><strong>地址：</strong></td><td style='padding: 10px; border-bottom: 1px solid #ddd;'>{$data['address']}</td></tr>
                    <tr><td style='padding: 10px; border-bottom: 1px solid #ddd;'><strong>时间：</strong></td><td style='padding: 10px; border-bottom: 1px solid #ddd;'>{$data['time']}</td></tr>
                </table>
                <p style='color: #666;'>预计1-24小时内到账，请耐心等待。</p>
            </div>
        </div>
        ";
    }

    /**
     * 获取IDO通知邮件内容
     */
    private function getIdoNotificationContent(array $data): string
    {
        return "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <div style='background: #9b59b6; color: white; padding: 20px; text-align: center;'>
                <h1>IDO认购通知</h1>
            </div>
            <div style='padding: 30px; background: #f9f9f9;'>
                <h2>认购成功</h2>
                <p>您已成功参与IDO认购：</p>
                <table style='width: 100%; border-collapse: collapse; margin: 20px 0;'>
                    <tr><td style='padding: 10px; border-bottom: 1px solid #ddd;'><strong>项目：</strong></td><td style='padding: 10px; border-bottom: 1px solid #ddd;'>{$data['project_name']}</td></tr>
                    <tr><td style='padding: 10px; border-bottom: 1px solid #ddd;'><strong>认购金额：</strong></td><td style='padding: 10px; border-bottom: 1px solid #ddd;'>{$data['amount']} USDT</td></tr>
                    <tr><td style='padding: 10px; border-bottom: 1px solid #ddd;'><strong>获得代币：</strong></td><td style='padding: 10px; border-bottom: 1px solid #ddd;'>{$data['token_amount']} {$data['token_symbol']}</td></tr>
                    <tr><td style='padding: 10px; border-bottom: 1px solid #ddd;'><strong>时间：</strong></td><td style='padding: 10px; border-bottom: 1px solid #ddd;'>{$data['time']}</td></tr>
                </table>
            </div>
        </div>
        ";
    }
}
