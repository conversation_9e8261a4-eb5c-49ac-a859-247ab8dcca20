-- MySQL dump 10.13  Distrib 5.7.44, for Linux (x86_64)
--
-- Host: localhost    Database: crypto_exchange
-- ------------------------------------------------------
-- Server version	5.7.44-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `ce_admins`
--

DROP TABLE IF EXISTS `ce_admins`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_admins` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `real_name` varchar(50) DEFAULT '' COMMENT '真实姓名',
  `email` varchar(100) DEFAULT '' COMMENT '邮箱',
  `phone` varchar(20) DEFAULT '' COMMENT '手机号',
  `avatar` varchar(255) DEFAULT '' COMMENT '头像',
  `role` varchar(20) DEFAULT 'admin' COMMENT '角色',
  `permissions` text COMMENT '权限列表(JSON)',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT '' COMMENT '最后登录IP',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_admins`
--

LOCK TABLES `ce_admins` WRITE;
/*!40000 ALTER TABLE `ce_admins` DISABLE KEYS */;
INSERT INTO `ce_admins` VALUES (1,'admin','$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi','超级管理员','','','','super_admin',NULL,NULL,'',1,'2025-08-03 15:33:55','2025-08-03 15:33:55');
/*!40000 ALTER TABLE `ce_admins` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_ai_predictions`
--

DROP TABLE IF EXISTS `ce_ai_predictions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_ai_predictions` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `prediction_id` varchar(32) NOT NULL COMMENT '预测ID',
  `symbol` varchar(20) NOT NULL COMMENT '交易对',
  `prediction_type` varchar(50) NOT NULL COMMENT '预测类型',
  `model_used` varchar(50) NOT NULL COMMENT '使用的模型',
  `predicted_value` decimal(20,8) NOT NULL COMMENT '预测值',
  `confidence_score` decimal(5,4) NOT NULL COMMENT '置信度',
  `time_horizon` varchar(20) NOT NULL COMMENT '时间范围',
  `features_used` json DEFAULT NULL COMMENT '使用的特征',
  `actual_value` decimal(20,8) DEFAULT NULL COMMENT '实际值',
  `accuracy` decimal(5,4) DEFAULT NULL COMMENT '准确度',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `prediction_id` (`prediction_id`),
  KEY `symbol` (`symbol`),
  KEY `prediction_type` (`prediction_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI预测表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_ai_predictions`
--

LOCK TABLES `ce_ai_predictions` WRITE;
/*!40000 ALTER TABLE `ce_ai_predictions` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_ai_predictions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_ai_strategies`
--

DROP TABLE IF EXISTS `ce_ai_strategies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_ai_strategies` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `strategy_id` varchar(32) NOT NULL COMMENT '策略ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `name` varchar(100) NOT NULL COMMENT '策略名称',
  `description` text COMMENT '策略描述',
  `type` varchar(50) NOT NULL COMMENT '策略类型',
  `symbols` json DEFAULT NULL COMMENT '交易对列表',
  `risk_level` tinyint(1) DEFAULT '2' COMMENT '风险等级',
  `investment_amount` decimal(20,8) DEFAULT '1000.********' COMMENT '投资金额',
  `parameters` json DEFAULT NULL COMMENT '策略参数',
  `ml_model` varchar(50) DEFAULT 'ensemble' COMMENT '机器学习模型',
  `performance_metrics` json DEFAULT NULL COMMENT '性能指标',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `strategy_id` (`strategy_id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI策略表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_ai_strategies`
--

LOCK TABLES `ce_ai_strategies` WRITE;
/*!40000 ALTER TABLE `ce_ai_strategies` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_ai_strategies` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_coins`
--

DROP TABLE IF EXISTS `ce_coins`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_coins` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `symbol` varchar(20) NOT NULL COMMENT '币种符号',
  `name` varchar(50) NOT NULL COMMENT '币种名称',
  `icon` varchar(255) DEFAULT '' COMMENT '币种图标',
  `decimals` int(2) DEFAULT '8' COMMENT '小数位数',
  `is_deposit` tinyint(1) DEFAULT '1' COMMENT '是否支持充值',
  `is_withdraw` tinyint(1) DEFAULT '1' COMMENT '是否支持提现',
  `is_trade` tinyint(1) DEFAULT '1' COMMENT '是否支持交易',
  `min_deposit` decimal(20,8) DEFAULT '0.********' COMMENT '最小充值金额',
  `min_withdraw` decimal(20,8) DEFAULT '0.********' COMMENT '最小提现金额',
  `withdraw_fee` decimal(20,8) DEFAULT '0.********' COMMENT '提现手续费',
  `withdraw_fee_type` tinyint(1) DEFAULT '1' COMMENT '手续费类型:1固定,2比例',
  `deposit_address` varchar(255) DEFAULT '' COMMENT '充值地址',
  `contract_address` varchar(255) DEFAULT '' COMMENT '合约地址',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `symbol` (`symbol`),
  KEY `status` (`status`),
  KEY `sort` (`sort`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='币种配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_coins`
--

LOCK TABLES `ce_coins` WRITE;
/*!40000 ALTER TABLE `ce_coins` DISABLE KEYS */;
INSERT INTO `ce_coins` VALUES (1,'USDT','Tether USD','',6,1,1,1,10.********,10.********,5.********,1,'','',1,1,'2025-08-03 15:33:55','2025-08-03 15:33:55'),(2,'BTC','Bitcoin','',8,1,1,1,0.00100000,0.00100000,0.00050000,1,'','',2,1,'2025-08-03 15:33:55','2025-08-03 15:33:55'),(3,'ETH','Ethereum','',8,1,1,1,0.01000000,0.01000000,0.00500000,1,'','',3,1,'2025-08-03 15:33:55','2025-08-03 15:33:55');
/*!40000 ALTER TABLE `ce_coins` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_commissions`
--

DROP TABLE IF EXISTS `ce_commissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_commissions` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '获得佣金的用户ID',
  `from_user_id` int(11) NOT NULL COMMENT '产生佣金的用户ID',
  `type` varchar(20) NOT NULL COMMENT '佣金类型:trade,contract,ido',
  `level` tinyint(1) NOT NULL COMMENT '佣金级别:1,2,3',
  `rate` decimal(8,6) NOT NULL COMMENT '佣金比例',
  `amount` decimal(20,8) NOT NULL COMMENT '佣金金额',
  `coin_symbol` varchar(20) NOT NULL COMMENT '佣金币种',
  `related_id` varchar(32) DEFAULT '' COMMENT '关联订单ID',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态:1已发放,0待发放',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `from_user_id` (`from_user_id`),
  KEY `type` (`type`),
  KEY `level` (`level`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='佣金记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_commissions`
--

LOCK TABLES `ce_commissions` WRITE;
/*!40000 ALTER TABLE `ce_commissions` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_commissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_contract_config`
--

DROP TABLE IF EXISTS `ce_contract_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_contract_config` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `symbol` varchar(20) NOT NULL COMMENT '交易对',
  `time_periods` text NOT NULL COMMENT '时间周期配置(JSON)',
  `profit_rates` text NOT NULL COMMENT '盈利比例配置(JSON)',
  `quick_amounts` text NOT NULL COMMENT '快捷金额配置(JSON)',
  `min_amount` decimal(20,8) DEFAULT '10.********' COMMENT '最小投注金额',
  `max_amount` decimal(20,8) DEFAULT '10000.********' COMMENT '最大投注金额',
  `fee_rate` decimal(8,6) DEFAULT '0.020000' COMMENT '手续费比例',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `symbol` (`symbol`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='秒合约配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_contract_config`
--

LOCK TABLES `ce_contract_config` WRITE;
/*!40000 ALTER TABLE `ce_contract_config` DISABLE KEYS */;
INSERT INTO `ce_contract_config` VALUES (1,'BTC/USDT','[60,180,300,1800]','[75,77,80,85]','[50,100,500,1000]',10.********,10000.********,0.020000,1,'2025-08-03 15:33:55','2025-08-03 15:33:55'),(2,'ETH/USDT','[60,180,300,1800]','[75,77,80,85]','[50,100,500,1000]',10.********,10000.********,0.020000,1,'2025-08-03 15:33:55','2025-08-03 15:33:55');
/*!40000 ALTER TABLE `ce_contract_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_contract_orders`
--

DROP TABLE IF EXISTS `ce_contract_orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_contract_orders` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` varchar(32) NOT NULL COMMENT '订单ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `symbol` varchar(20) NOT NULL COMMENT '交易对',
  `type` varchar(20) DEFAULT 'second' COMMENT '合约类型',
  `direction` varchar(10) NOT NULL COMMENT '方向:up,down',
  `amount` decimal(20,8) NOT NULL COMMENT '投注金额',
  `time_period` int(11) DEFAULT '60' COMMENT '时间周期(秒)',
  `fee` decimal(20,8) DEFAULT '0.********' COMMENT '手续费',
  `period` int(11) NOT NULL COMMENT '合约周期(秒)',
  `profit_rate` decimal(8,4) NOT NULL COMMENT '盈利比例',
  `open_price` decimal(20,8) DEFAULT '0.********' COMMENT '开仓价格',
  `close_price` decimal(20,8) DEFAULT '0.********' COMMENT '平仓价格',
  `settle_price` decimal(20,8) DEFAULT '0.********' COMMENT '结算价格',
  `profit_loss` decimal(20,8) DEFAULT '0.********' COMMENT '盈亏金额',
  `profit_amount` decimal(20,8) DEFAULT '0.********' COMMENT '盈利金额',
  `total_amount` decimal(20,8) DEFAULT '0.********' COMMENT '总金额',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态:0进行中,1盈利,2亏损',
  `open_time` datetime NOT NULL COMMENT '开仓时间',
  `close_time` datetime DEFAULT NULL COMMENT '平仓时间',
  `settled_at` datetime DEFAULT NULL COMMENT '结算时间',
  `settle_time` datetime DEFAULT NULL COMMENT '结算时间',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_id` (`order_id`),
  KEY `user_id` (`user_id`),
  KEY `symbol` (`symbol`),
  KEY `status` (`status`),
  KEY `open_time` (`open_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒合约订单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_contract_orders`
--

LOCK TABLES `ce_contract_orders` WRITE;
/*!40000 ALTER TABLE `ce_contract_orders` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_contract_orders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_copy_trades`
--

DROP TABLE IF EXISTS `ce_copy_trades`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_copy_trades` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `copy_trade_id` varchar(32) NOT NULL COMMENT '跟单ID',
  `original_order_id` varchar(32) NOT NULL COMMENT '原始订单ID',
  `copy_order_id` varchar(32) NOT NULL COMMENT '跟单订单ID',
  `subscription_id` varchar(32) NOT NULL COMMENT '订阅ID',
  `user_id` int(11) NOT NULL COMMENT '跟单用户ID',
  `strategy_id` varchar(32) NOT NULL COMMENT '策略ID',
  `copy_amount` decimal(20,8) NOT NULL COMMENT '跟单数量',
  `copy_ratio` decimal(5,4) NOT NULL COMMENT '跟单比例',
  `profit_loss` decimal(20,8) DEFAULT '0.********' COMMENT '盈亏',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `copy_trade_id` (`copy_trade_id`),
  KEY `user_id` (`user_id`),
  KEY `strategy_id` (`strategy_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='跟单记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_copy_trades`
--

LOCK TABLES `ce_copy_trades` WRITE;
/*!40000 ALTER TABLE `ce_copy_trades` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_copy_trades` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_customer_conversations`
--

DROP TABLE IF EXISTS `ce_customer_conversations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_customer_conversations` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '会话ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `agent_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '代理商ID',
  `last_message_id` int(11) unsigned DEFAULT NULL COMMENT '最后一条消息ID',
  `last_message_time` datetime DEFAULT NULL COMMENT '最后消息时间',
  `unread_count_user` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户未读数量',
  `unread_count_agent` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '代理商未读数量',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '会话状态：1正常 2已删除',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_agent` (`user_id`,`agent_id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_last_message_time` (`last_message_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客服会话表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_customer_conversations`
--

LOCK TABLES `ce_customer_conversations` WRITE;
/*!40000 ALTER TABLE `ce_customer_conversations` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_customer_conversations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_customer_quick_replies`
--

DROP TABLE IF EXISTS `ce_customer_quick_replies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_customer_quick_replies` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `agent_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '代理商ID，0表示全局模板',
  `category` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'common' COMMENT '分类：common通用 greeting问候 help帮助 closing结束',
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '回复内容',
  `sort_order` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1启用 0禁用',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_agent_category` (`agent_id`,`category`),
  KEY `idx_status_sort` (`status`,`sort_order`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客服快速回复模板表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_customer_quick_replies`
--

LOCK TABLES `ce_customer_quick_replies` WRITE;
/*!40000 ALTER TABLE `ce_customer_quick_replies` DISABLE KEYS */;
INSERT INTO `ce_customer_quick_replies` VALUES (1,0,'greeting','您好，有什么可以帮助您的吗？',1,1,'2025-08-03 15:34:24',NULL),(2,0,'greeting','欢迎使用我们的服务！',2,1,'2025-08-03 15:34:24',NULL),(3,0,'common','请稍等，我来为您查询一下',3,1,'2025-08-03 15:34:24',NULL),(4,0,'common','感谢您的耐心等待',4,1,'2025-08-03 15:34:24',NULL),(5,0,'common','我正在为您处理，请稍候',5,1,'2025-08-03 15:34:24',NULL),(6,0,'help','如果您遇到充值问题，请提供交易截图',6,1,'2025-08-03 15:34:24',NULL),(7,0,'help','如果您遇到提现问题，请检查地址是否正确',7,1,'2025-08-03 15:34:24',NULL),(8,0,'help','如果您遇到交易问题，请联系技术支持',8,1,'2025-08-03 15:34:24',NULL),(9,0,'help','请提供您的用户ID，我来帮您查询',9,1,'2025-08-03 15:34:24',NULL),(10,0,'closing','如果还有其他问题，请随时联系我们',10,1,'2025-08-03 15:34:24',NULL),(11,0,'closing','祝您使用愉快！',11,1,'2025-08-03 15:34:24',NULL),(12,0,'closing','感谢您的咨询，祝您投资顺利！',12,1,'2025-08-03 15:34:24',NULL);
/*!40000 ALTER TABLE `ce_customer_quick_replies` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_customer_service`
--

DROP TABLE IF EXISTS `ce_customer_service`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_customer_service` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `agent_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '代理商ID',
  `sender_id` int(11) unsigned NOT NULL COMMENT '发送者ID',
  `sender_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '发送者类型：1用户 2代理商 3管理员 4系统',
  `type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '消息类型：1文本 2图片 3视频 4表情 5文件 6系统',
  `content` text COLLATE utf8mb4_unicode_ci COMMENT '消息内容',
  `media_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '媒体文件URL',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '消息状态：0未读 1已读 2已删除',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_sender` (`sender_id`,`sender_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_status` (`user_id`,`status`),
  KEY `idx_agent_status` (`agent_id`,`status`),
  KEY `idx_user_created` (`user_id`,`created_at`),
  KEY `idx_agent_created` (`agent_id`,`created_at`),
  KEY `idx_sender_created` (`sender_id`,`sender_type`,`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客服消息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_customer_service`
--

LOCK TABLES `ce_customer_service` WRITE;
/*!40000 ALTER TABLE `ce_customer_service` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_customer_service` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_customer_service_messages`
--

DROP TABLE IF EXISTS `ce_customer_service_messages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_customer_service_messages` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `ticket_id` varchar(32) NOT NULL COMMENT '工单ID',
  `user_id` int(11) NOT NULL COMMENT '发送者ID',
  `type` enum('user','service','system') NOT NULL COMMENT '消息类型',
  `content` text NOT NULL COMMENT '消息内容',
  `is_read` tinyint(1) DEFAULT '0' COMMENT '是否已读',
  `read_at` datetime DEFAULT NULL COMMENT '阅读时间',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `ticket_id` (`ticket_id`),
  KEY `user_id` (`user_id`),
  KEY `type` (`type`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服消息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_customer_service_messages`
--

LOCK TABLES `ce_customer_service_messages` WRITE;
/*!40000 ALTER TABLE `ce_customer_service_messages` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_customer_service_messages` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_customer_service_online`
--

DROP TABLE IF EXISTS `ce_customer_service_online`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_customer_service_online` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `user_type` tinyint(1) unsigned NOT NULL COMMENT '用户类型：1普通用户 2代理商 3管理员',
  `last_active_time` datetime NOT NULL COMMENT '最后活跃时间',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1在线 0离线',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user` (`user_id`,`user_type`),
  KEY `idx_last_active` (`last_active_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客服在线状态表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_customer_service_online`
--

LOCK TABLES `ce_customer_service_online` WRITE;
/*!40000 ALTER TABLE `ce_customer_service_online` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_customer_service_online` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_customer_service_stats`
--

DROP TABLE IF EXISTS `ce_customer_service_stats`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_customer_service_stats` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `date` date NOT NULL COMMENT '统计日期',
  `agent_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '代理商ID，0表示全局统计',
  `total_messages` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '总消息数',
  `total_conversations` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '总对话数',
  `active_users` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '活跃用户数',
  `response_time_avg` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '平均响应时间（秒）',
  `satisfaction_score` decimal(3,2) DEFAULT NULL COMMENT '满意度评分',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_date_agent` (`date`,`agent_id`),
  KEY `idx_date` (`date`),
  KEY `idx_agent_id` (`agent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客服统计表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_customer_service_stats`
--

LOCK TABLES `ce_customer_service_stats` WRITE;
/*!40000 ALTER TABLE `ce_customer_service_stats` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_customer_service_stats` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_customer_service_tickets`
--

DROP TABLE IF EXISTS `ce_customer_service_tickets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_customer_service_tickets` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `ticket_id` varchar(32) NOT NULL COMMENT '工单ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `service_id` int(11) DEFAULT '0' COMMENT '客服ID',
  `subject` varchar(200) NOT NULL COMMENT '主题',
  `category` varchar(50) DEFAULT 'general' COMMENT '分类',
  `priority` tinyint(1) DEFAULT '2' COMMENT '优先级:1低,2普通,3高,4紧急',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态:1开启,2等待回复,3已解决,4已关闭',
  `last_reply_at` datetime DEFAULT NULL COMMENT '最后回复时间',
  `closed_at` datetime DEFAULT NULL COMMENT '关闭时间',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ticket_id` (`ticket_id`),
  KEY `user_id` (`user_id`),
  KEY `service_id` (`service_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服工单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_customer_service_tickets`
--

LOCK TABLES `ce_customer_service_tickets` WRITE;
/*!40000 ALTER TABLE `ce_customer_service_tickets` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_customer_service_tickets` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_deposit_addresses`
--

DROP TABLE IF EXISTS `ce_deposit_addresses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_deposit_addresses` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `coin_symbol` varchar(20) NOT NULL COMMENT '币种符号',
  `address` varchar(255) NOT NULL COMMENT '充值地址',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_coin` (`user_id`,`coin_symbol`),
  UNIQUE KEY `address` (`address`),
  KEY `user_id` (`user_id`),
  KEY `coin_symbol` (`coin_symbol`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值地址表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_deposit_addresses`
--

LOCK TABLES `ce_deposit_addresses` WRITE;
/*!40000 ALTER TABLE `ce_deposit_addresses` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_deposit_addresses` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_deposits`
--

DROP TABLE IF EXISTS `ce_deposits`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_deposits` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `deposit_id` varchar(32) NOT NULL COMMENT '充值ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `coin_symbol` varchar(20) NOT NULL COMMENT '币种',
  `amount` decimal(20,8) NOT NULL COMMENT '充值金额',
  `to_address` varchar(255) NOT NULL COMMENT '充值地址',
  `from_address` varchar(255) DEFAULT '' COMMENT '来源地址',
  `txid` varchar(255) DEFAULT '' COMMENT '交易哈希',
  `confirmations` int(11) DEFAULT '0' COMMENT '确认数',
  `required_confirmations` int(11) DEFAULT '6' COMMENT '需要确认数',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态:0待确认,1已确认,2失败',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `deposit_id` (`deposit_id`),
  KEY `user_id` (`user_id`),
  KEY `coin_symbol` (`coin_symbol`),
  KEY `status` (`status`),
  KEY `txid` (`txid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_deposits`
--

LOCK TABLES `ce_deposits` WRITE;
/*!40000 ALTER TABLE `ce_deposits` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_deposits` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_financial_records`
--

DROP TABLE IF EXISTS `ce_financial_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_financial_records` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `coin_symbol` varchar(20) NOT NULL COMMENT '币种',
  `type` varchar(20) NOT NULL COMMENT '类型:deposit,withdraw,trade,contract,commission',
  `amount` decimal(20,8) NOT NULL COMMENT '变动金额',
  `balance_before` decimal(20,8) NOT NULL COMMENT '变动前余额',
  `balance_after` decimal(20,8) NOT NULL COMMENT '变动后余额',
  `related_id` varchar(32) DEFAULT '' COMMENT '关联ID',
  `description` varchar(255) DEFAULT '' COMMENT '描述',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `coin_symbol` (`coin_symbol`),
  KEY `type` (`type`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_financial_records`
--

LOCK TABLES `ce_financial_records` WRITE;
/*!40000 ALTER TABLE `ce_financial_records` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_financial_records` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_follow_relations`
--

DROP TABLE IF EXISTS `ce_follow_relations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_follow_relations` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `follower_id` int(11) NOT NULL COMMENT '关注者ID',
  `following_id` int(11) NOT NULL COMMENT '被关注者ID',
  `followed_at` datetime NOT NULL COMMENT '关注时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `follow_unique` (`follower_id`,`following_id`),
  KEY `follower_id` (`follower_id`),
  KEY `following_id` (`following_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='关注关系表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_follow_relations`
--

LOCK TABLES `ce_follow_relations` WRITE;
/*!40000 ALTER TABLE `ce_follow_relations` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_follow_relations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_ido_orders`
--

DROP TABLE IF EXISTS `ce_ido_orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_ido_orders` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` varchar(32) NOT NULL COMMENT '订单ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `project_id` int(11) NOT NULL COMMENT '项目ID',
  `amount` decimal(20,8) NOT NULL COMMENT '认购数量',
  `price` decimal(20,8) NOT NULL COMMENT '认购价格',
  `total_payment` decimal(20,8) NOT NULL COMMENT '支付总额',
  `payment_coin` varchar(20) NOT NULL COMMENT '支付币种',
  `released_amount` decimal(20,8) DEFAULT '0.********' COMMENT '已释放数量',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态:1已支付,2已取消',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_id` (`order_id`),
  KEY `user_id` (`user_id`),
  KEY `project_id` (`project_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='认购订单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_ido_orders`
--

LOCK TABLES `ce_ido_orders` WRITE;
/*!40000 ALTER TABLE `ce_ido_orders` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_ido_orders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_ido_projects`
--

DROP TABLE IF EXISTS `ce_ido_projects`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_ido_projects` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '项目名称',
  `symbol` varchar(20) NOT NULL COMMENT '代币符号',
  `icon` varchar(255) DEFAULT '' COMMENT '项目图标',
  `description` text COMMENT '项目描述',
  `total_supply` decimal(20,8) NOT NULL COMMENT '总发行量',
  `sale_amount` decimal(20,8) NOT NULL COMMENT '销售数量',
  `price` decimal(20,8) NOT NULL COMMENT '认购价格',
  `payment_coin` varchar(20) NOT NULL COMMENT '支付币种',
  `min_purchase` decimal(20,8) DEFAULT '0.********' COMMENT '最小认购数量',
  `max_purchase` decimal(20,8) DEFAULT '0.********' COMMENT '最大认购数量',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `lock_period` int(11) DEFAULT '0' COMMENT '锁仓期(天)',
  `daily_release_rate` decimal(8,6) DEFAULT '0.000000' COMMENT '每日释放比例',
  `commission_level1` decimal(8,4) DEFAULT '0.0000' COMMENT '一级佣金比例',
  `commission_level2` decimal(8,4) DEFAULT '0.0000' COMMENT '二级佣金比例',
  `commission_level3` decimal(8,4) DEFAULT '0.0000' COMMENT '三级佣金比例',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `symbol` (`symbol`),
  KEY `status` (`status`),
  KEY `start_time` (`start_time`),
  KEY `end_time` (`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='认购项目表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_ido_projects`
--

LOCK TABLES `ce_ido_projects` WRITE;
/*!40000 ALTER TABLE `ce_ido_projects` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_ido_projects` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_institutional_accounts`
--

DROP TABLE IF EXISTS `ce_institutional_accounts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_institutional_accounts` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `account_id` varchar(32) NOT NULL COMMENT '账户ID',
  `company_name` varchar(200) NOT NULL COMMENT '公司名称',
  `company_type` varchar(50) NOT NULL COMMENT '公司类型',
  `registration_number` varchar(100) NOT NULL COMMENT '注册号',
  `registration_country` varchar(10) NOT NULL COMMENT '注册国家',
  `business_license` varchar(255) DEFAULT NULL COMMENT '营业执照',
  `contact_person` varchar(100) NOT NULL COMMENT '联系人',
  `contact_email` varchar(100) NOT NULL COMMENT '联系邮箱',
  `contact_phone` varchar(20) NOT NULL COMMENT '联系电话',
  `address` text COMMENT '地址',
  `aum` decimal(20,2) DEFAULT '0.00' COMMENT '管理资产规模',
  `trading_volume_requirement` decimal(20,2) DEFAULT '0.00' COMMENT '交易量要求',
  `fee_tier` varchar(20) DEFAULT 'Standard' COMMENT '费率等级',
  `api_key` varchar(64) DEFAULT NULL COMMENT 'API密钥',
  `api_secret` varchar(128) DEFAULT NULL COMMENT 'API密钥',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态',
  `reviewer_id` int(11) DEFAULT '0' COMMENT '审核员ID',
  `review_notes` text COMMENT '审核备注',
  `reject_reason` varchar(255) DEFAULT NULL COMMENT '拒绝原因',
  `approved_at` datetime DEFAULT NULL COMMENT '通过时间',
  `rejected_at` datetime DEFAULT NULL COMMENT '拒绝时间',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_id` (`account_id`),
  KEY `status` (`status`),
  KEY `company_type` (`company_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机构账户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_institutional_accounts`
--

LOCK TABLES `ce_institutional_accounts` WRITE;
/*!40000 ALTER TABLE `ce_institutional_accounts` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_institutional_accounts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_orders`
--

DROP TABLE IF EXISTS `ce_orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_orders` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` varchar(32) NOT NULL COMMENT '订单ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `symbol` varchar(20) NOT NULL COMMENT '交易对',
  `type` varchar(10) NOT NULL COMMENT '订单类型:buy,sell',
  `order_type` varchar(10) NOT NULL COMMENT '订单方式:market,limit',
  `price` decimal(20,8) DEFAULT '0.********' COMMENT '委托价格',
  `amount` decimal(20,8) NOT NULL COMMENT '委托数量',
  `total` decimal(20,8) DEFAULT '0.********' COMMENT '委托总额',
  `filled_amount` decimal(20,8) DEFAULT '0.********' COMMENT '已成交数量',
  `filled_total` decimal(20,8) DEFAULT '0.********' COMMENT '已成交总额',
  `avg_price` decimal(20,8) DEFAULT '0.********' COMMENT '平均成交价',
  `fee` decimal(20,8) DEFAULT '0.********' COMMENT '手续费',
  `fee_coin` varchar(20) DEFAULT '' COMMENT '手续费币种',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态:0待成交,1部分成交,2完全成交,3已取消',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_id` (`order_id`),
  KEY `user_id` (`user_id`),
  KEY `symbol` (`symbol`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_orders`
--

LOCK TABLES `ce_orders` WRITE;
/*!40000 ALTER TABLE `ce_orders` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_orders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_otc_orders`
--

DROP TABLE IF EXISTS `ce_otc_orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_otc_orders` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` varchar(32) NOT NULL COMMENT '订单ID',
  `account_id` varchar(32) NOT NULL COMMENT '机构账户ID',
  `type` enum('buy','sell') NOT NULL COMMENT '订单类型',
  `coin_symbol` varchar(20) NOT NULL COMMENT '币种符号',
  `amount` decimal(20,8) NOT NULL COMMENT '数量',
  `price` decimal(20,8) DEFAULT '0.********' COMMENT '价格',
  `total` decimal(20,8) DEFAULT '0.********' COMMENT '总额',
  `min_amount` decimal(20,8) DEFAULT '0.********' COMMENT '最小数量',
  `max_amount` decimal(20,8) DEFAULT '0.********' COMMENT '最大数量',
  `payment_methods` json DEFAULT NULL COMMENT '支付方式',
  `settlement_time` varchar(20) DEFAULT 'T+1' COMMENT '结算时间',
  `remarks` text COMMENT '备注',
  `counterparty_id` varchar(32) DEFAULT NULL COMMENT '对手方ID',
  `matched_price` decimal(20,8) DEFAULT '0.********' COMMENT '匹配价格',
  `matched_amount` decimal(20,8) DEFAULT '0.********' COMMENT '匹配数量',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态',
  `matched_at` datetime DEFAULT NULL COMMENT '匹配时间',
  `expires_at` datetime DEFAULT NULL COMMENT '过期时间',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_id` (`order_id`),
  KEY `account_id` (`account_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='OTC订单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_otc_orders`
--

LOCK TABLES `ce_otc_orders` WRITE;
/*!40000 ALTER TABLE `ce_otc_orders` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_otc_orders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_quant_backtests`
--

DROP TABLE IF EXISTS `ce_quant_backtests`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_quant_backtests` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `backtest_id` varchar(32) NOT NULL COMMENT '回测ID',
  `strategy_id` varchar(32) NOT NULL COMMENT '策略ID',
  `start_date` date NOT NULL COMMENT '开始日期',
  `end_date` date NOT NULL COMMENT '结束日期',
  `initial_capital` decimal(20,8) NOT NULL COMMENT '初始资金',
  `final_capital` decimal(20,8) NOT NULL COMMENT '最终资金',
  `total_return` decimal(10,6) NOT NULL COMMENT '总收益率',
  `annual_return` decimal(10,6) NOT NULL COMMENT '年化收益率',
  `max_drawdown` decimal(10,6) NOT NULL COMMENT '最大回撤',
  `sharpe_ratio` decimal(10,6) NOT NULL COMMENT '夏普比率',
  `win_rate` decimal(5,4) NOT NULL COMMENT '胜率',
  `total_trades` int(11) NOT NULL COMMENT '总交易次数',
  `profit_trades` int(11) NOT NULL COMMENT '盈利交易次数',
  `loss_trades` int(11) NOT NULL COMMENT '亏损交易次数',
  `avg_profit` decimal(20,8) NOT NULL COMMENT '平均盈利',
  `avg_loss` decimal(20,8) NOT NULL COMMENT '平均亏损',
  `profit_factor` decimal(10,6) NOT NULL COMMENT '盈亏比',
  `trade_details` json DEFAULT NULL COMMENT '交易详情',
  `equity_curve` json DEFAULT NULL COMMENT '资金曲线',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `backtest_id` (`backtest_id`),
  KEY `strategy_id` (`strategy_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='量化回测结果表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_quant_backtests`
--

LOCK TABLES `ce_quant_backtests` WRITE;
/*!40000 ALTER TABLE `ce_quant_backtests` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_quant_backtests` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_quant_signals`
--

DROP TABLE IF EXISTS `ce_quant_signals`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_quant_signals` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `signal_id` varchar(32) NOT NULL COMMENT '信号ID',
  `strategy_id` varchar(32) NOT NULL COMMENT '策略ID',
  `symbol` varchar(20) NOT NULL COMMENT '交易对',
  `signal_type` enum('buy','sell','hold') NOT NULL COMMENT '信号类型',
  `price` decimal(20,8) NOT NULL COMMENT '信号价格',
  `amount` decimal(20,8) NOT NULL COMMENT '建议数量',
  `confidence` decimal(5,4) NOT NULL COMMENT '置信度',
  `reason` text COMMENT '信号原因',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `signal_id` (`signal_id`),
  KEY `strategy_id` (`strategy_id`),
  KEY `symbol` (`symbol`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='量化交易信号表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_quant_signals`
--

LOCK TABLES `ce_quant_signals` WRITE;
/*!40000 ALTER TABLE `ce_quant_signals` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_quant_signals` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_quant_strategies`
--

DROP TABLE IF EXISTS `ce_quant_strategies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_quant_strategies` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `strategy_id` varchar(32) NOT NULL COMMENT '策略ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `name` varchar(100) NOT NULL COMMENT '策略名称',
  `description` text COMMENT '策略描述',
  `type` varchar(50) NOT NULL COMMENT '策略类型',
  `symbols` json DEFAULT NULL COMMENT '交易对列表',
  `parameters` json DEFAULT NULL COMMENT '策略参数',
  `risk_level` tinyint(1) DEFAULT '2' COMMENT '风险等级',
  `max_drawdown` decimal(5,4) DEFAULT '0.1000' COMMENT '最大回撤',
  `investment_amount` decimal(20,8) DEFAULT '1000.********' COMMENT '投资金额',
  `ml_model` varchar(50) DEFAULT 'ensemble' COMMENT '机器学习模型',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态',
  `backtest_result` json DEFAULT NULL COMMENT '回测结果',
  `started_at` datetime DEFAULT NULL COMMENT '启动时间',
  `stopped_at` datetime DEFAULT NULL COMMENT '停止时间',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `strategy_id` (`strategy_id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='量化交易策略表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_quant_strategies`
--

LOCK TABLES `ce_quant_strategies` WRITE;
/*!40000 ALTER TABLE `ce_quant_strategies` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_quant_strategies` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_social_posts`
--

DROP TABLE IF EXISTS `ce_social_posts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_social_posts` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `post_id` varchar(32) NOT NULL COMMENT '帖子ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `type` varchar(20) NOT NULL COMMENT '帖子类型',
  `title` varchar(200) DEFAULT '' COMMENT '标题',
  `content` text NOT NULL COMMENT '内容',
  `images` json DEFAULT NULL COMMENT '图片列表',
  `symbols` json DEFAULT NULL COMMENT '相关交易对',
  `tags` json DEFAULT NULL COMMENT '标签',
  `is_premium` tinyint(1) DEFAULT '0' COMMENT '是否付费内容',
  `view_count` int(11) DEFAULT '0' COMMENT '浏览次数',
  `like_count` int(11) DEFAULT '0' COMMENT '点赞次数',
  `comment_count` int(11) DEFAULT '0' COMMENT '评论次数',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `post_id` (`post_id`),
  KEY `user_id` (`user_id`),
  KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='社交帖子表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_social_posts`
--

LOCK TABLES `ce_social_posts` WRITE;
/*!40000 ALTER TABLE `ce_social_posts` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_social_posts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_strategy_subscriptions`
--

DROP TABLE IF EXISTS `ce_strategy_subscriptions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_strategy_subscriptions` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `subscription_id` varchar(32) NOT NULL COMMENT '订阅ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `strategy_id` varchar(32) NOT NULL COMMENT '策略ID',
  `investment_amount` decimal(20,8) NOT NULL COMMENT '投资金额',
  `copy_mode` varchar(20) DEFAULT 'fixed_amount' COMMENT '跟单模式',
  `copy_ratio` decimal(5,4) DEFAULT '1.0000' COMMENT '跟单比例',
  `stop_loss` decimal(10,6) DEFAULT '0.000000' COMMENT '止损',
  `take_profit` decimal(10,6) DEFAULT '0.000000' COMMENT '止盈',
  `current_value` decimal(20,8) DEFAULT '0.********' COMMENT '当前价值',
  `profit_loss` decimal(20,8) DEFAULT '0.********' COMMENT '盈亏',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态',
  `subscribed_at` datetime NOT NULL COMMENT '订阅时间',
  `cancelled_at` datetime DEFAULT NULL COMMENT '取消时间',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `subscription_id` (`subscription_id`),
  KEY `user_id` (`user_id`),
  KEY `strategy_id` (`strategy_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='策略订阅表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_strategy_subscriptions`
--

LOCK TABLES `ce_strategy_subscriptions` WRITE;
/*!40000 ALTER TABLE `ce_strategy_subscriptions` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_strategy_subscriptions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_system_config`
--

DROP TABLE IF EXISTS `ce_system_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_system_config` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `config_key` varchar(50) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_type` varchar(20) DEFAULT 'string' COMMENT '配置类型',
  `description` varchar(255) DEFAULT '' COMMENT '配置描述',
  `group_name` varchar(50) DEFAULT 'system' COMMENT '配置分组',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`),
  KEY `group_name` (`group_name`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_system_config`
--

LOCK TABLES `ce_system_config` WRITE;
/*!40000 ALTER TABLE `ce_system_config` DISABLE KEYS */;
INSERT INTO `ce_system_config` VALUES (1,'site_name','数字货币交易平台','string','网站名称','basic',0,'2025-08-03 15:33:55','2025-08-03 15:33:55'),(2,'site_logo','','string','网站Logo','basic',0,'2025-08-03 15:33:55','2025-08-03 15:33:55'),(3,'universal_code','888888','string','万能验证码','security',0,'2025-08-03 15:33:55','2025-08-03 15:33:55'),(4,'email_smtp_host','smtp.qq.com','string','SMTP服务器','email',0,'2025-08-03 15:33:55','2025-08-03 15:33:55'),(5,'email_smtp_port','587','string','SMTP端口','email',0,'2025-08-03 15:33:55','2025-08-03 15:33:55'),(6,'email_smtp_username','','string','SMTP用户名','email',0,'2025-08-03 15:33:55','2025-08-03 15:33:55'),(7,'email_smtp_password','','string','SMTP密码','email',0,'2025-08-03 15:33:55','2025-08-03 15:33:55');
/*!40000 ALTER TABLE `ce_system_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_system_configs`
--

DROP TABLE IF EXISTS `ce_system_configs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_system_configs` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `type` varchar(50) NOT NULL COMMENT '配置类型',
  `key` varchar(100) NOT NULL COMMENT '配置键',
  `value` text COMMENT '配置值',
  `description` varchar(255) DEFAULT '' COMMENT '配置描述',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `type_key` (`type`,`key`),
  KEY `type` (`type`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_system_configs`
--

LOCK TABLES `ce_system_configs` WRITE;
/*!40000 ALTER TABLE `ce_system_configs` DISABLE KEYS */;
INSERT INTO `ce_system_configs` VALUES (1,'basic','site_name','数字货币交易平台','网站名称',1,'2025-08-03 15:33:56','2025-08-03 15:33:56'),(2,'basic','site_logo','/static/images/logo.png','网站Logo',1,'2025-08-03 15:33:56','2025-08-03 15:33:56'),(3,'basic','site_favicon','/static/images/favicon.ico','网站图标',1,'2025-08-03 15:33:56','2025-08-03 15:33:56'),(4,'security','universal_code','888888','万能验证码',1,'2025-08-03 15:33:56','2025-08-03 15:33:56'),(5,'ui','login_bg','/static/images/login_bg.jpg','登录页背景',1,'2025-08-03 15:33:56','2025-08-03 15:33:56'),(6,'ui','banner_1','/static/images/banner1.jpg','首页轮播图1',1,'2025-08-03 15:33:56','2025-08-03 15:33:56'),(7,'ui','banner_2','/static/images/banner2.jpg','首页轮播图2',1,'2025-08-03 15:33:56','2025-08-03 15:33:56'),(8,'ui','banner_3','/static/images/banner3.jpg','首页轮播图3',1,'2025-08-03 15:33:56','2025-08-03 15:33:56'),(9,'contract','up_icon','/static/images/up_icon.png','看涨图标',1,'2025-08-03 15:33:56','2025-08-03 15:33:56'),(10,'contract','down_icon','/static/images/down_icon.png','看跌图标',1,'2025-08-03 15:33:56','2025-08-03 15:33:56'),(11,'contract','win_icon','/static/images/win_icon.png','盈利图标',1,'2025-08-03 15:33:56','2025-08-03 15:33:56'),(12,'contract','lose_icon','/static/images/lose_icon.png','亏损图标',1,'2025-08-03 15:33:56','2025-08-03 15:33:56'),(13,'contract','default_quick_amounts','[50,100,500,1000]','默认快捷金额',1,'2025-08-03 15:33:56','2025-08-03 15:33:56');
/*!40000 ALTER TABLE `ce_system_configs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_trader_rankings`
--

DROP TABLE IF EXISTS `ce_trader_rankings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_trader_rankings` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `period` varchar(20) NOT NULL COMMENT '统计周期',
  `return_rate` decimal(10,6) NOT NULL COMMENT '收益率',
  `total_trades` int(11) DEFAULT '0' COMMENT '总交易次数',
  `win_rate` decimal(5,4) DEFAULT '0.0000' COMMENT '胜率',
  `max_drawdown` decimal(10,6) DEFAULT '0.000000' COMMENT '最大回撤',
  `followers_count` int(11) DEFAULT '0' COMMENT '关注者数量',
  `aum` decimal(20,8) DEFAULT '0.********' COMMENT '管理资产',
  `ranking` int(11) DEFAULT '0' COMMENT '排名',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_period` (`user_id`,`period`),
  KEY `period` (`period`),
  KEY `ranking` (`ranking`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='交易员排行榜表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_trader_rankings`
--

LOCK TABLES `ce_trader_rankings` WRITE;
/*!40000 ALTER TABLE `ce_trader_rankings` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_trader_rankings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_trades`
--

DROP TABLE IF EXISTS `ce_trades`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_trades` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `trade_id` varchar(32) NOT NULL COMMENT '成交ID',
  `order_id` varchar(32) NOT NULL COMMENT '订单ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `symbol` varchar(20) NOT NULL COMMENT '交易对',
  `type` varchar(10) NOT NULL COMMENT '交易类型:buy,sell',
  `price` decimal(20,8) NOT NULL COMMENT '成交价格',
  `amount` decimal(20,8) NOT NULL COMMENT '成交数量',
  `total` decimal(20,8) NOT NULL COMMENT '成交总额',
  `fee` decimal(20,8) DEFAULT '0.********' COMMENT '手续费',
  `fee_coin` varchar(20) DEFAULT '' COMMENT '手续费币种',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `trade_id` (`trade_id`),
  KEY `order_id` (`order_id`),
  KEY `user_id` (`user_id`),
  KEY `symbol` (`symbol`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成交记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_trades`
--

LOCK TABLES `ce_trades` WRITE;
/*!40000 ALTER TABLE `ce_trades` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_trades` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_trading_pairs`
--

DROP TABLE IF EXISTS `ce_trading_pairs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_trading_pairs` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `symbol` varchar(20) NOT NULL COMMENT '交易对符号',
  `base_coin` varchar(20) NOT NULL COMMENT '基础币种',
  `quote_coin` varchar(20) NOT NULL COMMENT '计价币种',
  `price_precision` int(2) DEFAULT '8' COMMENT '价格精度',
  `amount_precision` int(2) DEFAULT '8' COMMENT '数量精度',
  `min_amount` decimal(20,8) DEFAULT '0.********' COMMENT '最小交易数量',
  `max_amount` decimal(20,8) DEFAULT '0.********' COMMENT '最大交易数量',
  `min_total` decimal(20,8) DEFAULT '0.********' COMMENT '最小交易额',
  `maker_fee` decimal(8,6) DEFAULT '0.001000' COMMENT 'Maker手续费',
  `taker_fee` decimal(8,6) DEFAULT '0.001000' COMMENT 'Taker手续费',
  `current_price` decimal(20,8) DEFAULT '0.********' COMMENT '当前价格',
  `change_24h` decimal(8,4) DEFAULT '0.0000' COMMENT '24小时涨跌幅',
  `volume_24h` decimal(20,8) DEFAULT '0.********' COMMENT '24小时成交量',
  `high_24h` decimal(20,8) DEFAULT '0.********' COMMENT '24小时最高价',
  `low_24h` decimal(20,8) DEFAULT '0.********' COMMENT '24小时最低价',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `symbol` (`symbol`),
  KEY `base_coin` (`base_coin`),
  KEY `quote_coin` (`quote_coin`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='交易对表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_trading_pairs`
--

LOCK TABLES `ce_trading_pairs` WRITE;
/*!40000 ALTER TABLE `ce_trading_pairs` DISABLE KEYS */;
INSERT INTO `ce_trading_pairs` VALUES (1,'BTC/USDT','BTC','USDT',2,6,0.00010000,0.********,10.********,0.001000,0.001000,45000.********,0.0000,0.********,0.********,0.********,1,1,'2025-08-03 15:33:55','2025-08-03 15:33:55'),(2,'ETH/USDT','ETH','USDT',2,4,0.00100000,0.********,10.********,0.001000,0.001000,2500.********,0.0000,0.********,0.********,0.********,2,1,'2025-08-03 15:33:55','2025-08-03 15:33:55');
/*!40000 ALTER TABLE `ce_trading_pairs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_trading_strategies`
--

DROP TABLE IF EXISTS `ce_trading_strategies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_trading_strategies` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `strategy_id` varchar(32) NOT NULL COMMENT '策略ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `title` varchar(200) NOT NULL COMMENT '策略标题',
  `description` text COMMENT '策略描述',
  `strategy_type` varchar(50) NOT NULL COMMENT '策略类型',
  `risk_level` tinyint(1) DEFAULT '2' COMMENT '风险等级',
  `min_investment` decimal(20,8) DEFAULT '100.********' COMMENT '最小投资',
  `max_investment` decimal(20,8) DEFAULT '10000.********' COMMENT '最大投资',
  `performance_fee` decimal(5,4) DEFAULT '0.2000' COMMENT '业绩费',
  `management_fee` decimal(5,4) DEFAULT '0.0200' COMMENT '管理费',
  `symbols` json DEFAULT NULL COMMENT '交易对列表',
  `parameters` json DEFAULT NULL COMMENT '策略参数',
  `subscribers_count` int(11) DEFAULT '0' COMMENT '订阅人数',
  `total_investment` decimal(20,8) DEFAULT '0.********' COMMENT '总投资额',
  `return_rate` decimal(10,6) DEFAULT '0.000000' COMMENT '收益率',
  `max_drawdown` decimal(10,6) DEFAULT '0.000000' COMMENT '最大回撤',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态',
  `published_at` datetime DEFAULT NULL COMMENT '发布时间',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `strategy_id` (`strategy_id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='社交交易策略表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_trading_strategies`
--

LOCK TABLES `ce_trading_strategies` WRITE;
/*!40000 ALTER TABLE `ce_trading_strategies` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_trading_strategies` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_user_assets`
--

DROP TABLE IF EXISTS `ce_user_assets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_user_assets` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `coin_symbol` varchar(20) NOT NULL COMMENT '币种符号',
  `available` decimal(20,8) DEFAULT '0.********' COMMENT '可用余额',
  `frozen` decimal(20,8) DEFAULT '0.********' COMMENT '冻结余额',
  `total` decimal(20,8) DEFAULT '0.********' COMMENT '总余额',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_coin` (`user_id`,`coin_symbol`),
  KEY `user_id` (`user_id`),
  KEY `coin_symbol` (`coin_symbol`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户资产表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_user_assets`
--

LOCK TABLES `ce_user_assets` WRITE;
/*!40000 ALTER TABLE `ce_user_assets` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_user_assets` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_users`
--

DROP TABLE IF EXISTS `ce_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_users` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名/邮箱',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `user_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '用户类型:1正式用户,2测试用户',
  `role` varchar(20) DEFAULT 'user' COMMENT '角色:user,agent,admin',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `invite_code` varchar(20) NOT NULL COMMENT '邀请码',
  `inviter_id` int(11) DEFAULT '0' COMMENT '邀请人ID',
  `inviter_level1` int(11) DEFAULT '0' COMMENT '一级邀请人',
  `inviter_level2` int(11) DEFAULT '0' COMMENT '二级邀请人',
  `inviter_level3` int(11) DEFAULT '0' COMMENT '三级邀请人',
  `agent_id` int(11) DEFAULT '0' COMMENT '代理ID',
  `invite_path` text COMMENT '邀请路径',
  `is_agent` tinyint(1) DEFAULT '0' COMMENT '是否代理:0否,1是',
  `real_name` varchar(50) DEFAULT '' COMMENT '真实姓名',
  `phone` varchar(20) DEFAULT '' COMMENT '手机号',
  `avatar` varchar(255) DEFAULT '' COMMENT '头像',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT '' COMMENT '最后登录IP',
  `register_ip` varchar(50) DEFAULT '' COMMENT '注册IP',
  `created_by` int(11) DEFAULT '0' COMMENT '创建者ID',
  `email_verified` tinyint(1) DEFAULT '0' COMMENT '邮箱验证:0未验证,1已验证',
  `trade_password` varchar(255) DEFAULT '' COMMENT '交易密码',
  `google_secret` varchar(50) DEFAULT '' COMMENT '谷歌验证密钥',
  `is_google_auth` tinyint(1) DEFAULT '0' COMMENT '是否开启谷歌验证',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `invite_code` (`invite_code`),
  KEY `inviter_id` (`inviter_id`),
  KEY `user_type` (`user_type`),
  KEY `status` (`status`),
  KEY `agent_id` (`agent_id`),
  KEY `role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_users`
--

LOCK TABLES `ce_users` WRITE;
/*!40000 ALTER TABLE `ce_users` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ce_withdrawals`
--

DROP TABLE IF EXISTS `ce_withdrawals`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ce_withdrawals` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `withdrawal_id` varchar(32) NOT NULL COMMENT '提现ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `coin_symbol` varchar(20) NOT NULL COMMENT '币种',
  `amount` decimal(20,8) NOT NULL COMMENT '提现金额',
  `fee` decimal(20,8) DEFAULT '0.********' COMMENT '手续费',
  `actual_amount` decimal(20,8) NOT NULL COMMENT '实际到账金额',
  `to_address` varchar(255) NOT NULL COMMENT '提现地址',
  `txid` varchar(255) DEFAULT '' COMMENT '交易哈希',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态:0待审核,1处理中,2已完成,3已拒绝',
  `remark` text COMMENT '备注',
  `admin_id` int(11) DEFAULT '0' COMMENT '审核管理员ID',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `withdrawal_id` (`withdrawal_id`),
  KEY `user_id` (`user_id`),
  KEY `coin_symbol` (`coin_symbol`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ce_withdrawals`
--

LOCK TABLES `ce_withdrawals` WRITE;
/*!40000 ALTER TABLE `ce_withdrawals` DISABLE KEYS */;
/*!40000 ALTER TABLE `ce_withdrawals` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping events for database 'crypto_exchange'
--

--
-- Dumping routines for database 'crypto_exchange'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-03 15:35:08
