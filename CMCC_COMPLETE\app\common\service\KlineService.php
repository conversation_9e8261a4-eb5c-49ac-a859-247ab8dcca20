<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\Kline;
use think\facade\Db;
use think\facade\Cache;
use think\facade\Log;

/**
 * K线服务类
 */
class KlineService
{
    /**
     * 获取K线数据
     */
    public function getKlineData(string $symbol, string $interval, int $limit = 500, int $startTime = 0, int $endTime = 0): array
    {
        try {
            $cacheKey = "kline_{$symbol}_{$interval}_{$limit}_{$startTime}_{$endTime}";
            
            return Cache::remember($cacheKey, function() use ($symbol, $interval, $limit, $startTime, $endTime) {
                $query = Kline::where('symbol', $symbol)
                             ->where('interval', $interval);
                
                if ($startTime > 0) {
                    $query->where('open_time', '>=', $startTime);
                }
                
                if ($endTime > 0) {
                    $query->where('open_time', '<=', $endTime);
                }
                
                $klines = $query->order('open_time', 'asc')
                               ->limit($limit)
                               ->select()
                               ->toArray();
                
                return Kline::toTradingViewBatch($klines);
            }, 60); // 缓存1分钟
            
        } catch (\Exception $e) {
            Log::error('获取K线数据失败：' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取实时K线数据（WebSocket推送用）
     */
    public function getRealtimeKline(string $symbol, string $interval): array
    {
        try {
            $latest = Kline::getLatestKline($symbol, $interval);
            
            if (!$latest) {
                return [];
            }
            
            return [
                'symbol' => $symbol,
                'interval' => $interval,
                'data' => [
                    'time' => $latest['open_time'] * 1000,
                    'open' => (float)$latest['open_price'],
                    'high' => (float)$latest['high_price'],
                    'low' => (float)$latest['low_price'],
                    'close' => (float)$latest['close_price'],
                    'volume' => (float)$latest['volume']
                ]
            ];
            
        } catch (\Exception $e) {
            Log::error('获取实时K线数据失败：' . $e->getMessage());
            return [];
        }
    }

    /**
     * 更新K线数据（基于交易记录）
     */
    public function updateKlineFromTrade(array $tradeData): bool
    {
        try {
            $symbol = $tradeData['symbol'];
            $price = $tradeData['price'];
            $volume = $tradeData['volume'];
            $timestamp = $tradeData['timestamp'] ?? time();
            
            // 更新所有时间周期的K线
            $intervals = array_keys(Kline::getSupportedIntervals());
            
            foreach ($intervals as $interval) {
                $this->updateKlineForInterval($symbol, $interval, $price, $volume, $timestamp);
            }
            
            return true;
            
        } catch (\Exception $e) {
            Log::error('更新K线数据失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 更新指定时间周期的K线
     */
    private function updateKlineForInterval(string $symbol, string $interval, float $price, float $volume, int $timestamp): void
    {
        $intervalSeconds = Kline::getIntervalSeconds($interval);
        $openTime = intval($timestamp / $intervalSeconds) * $intervalSeconds;
        $closeTime = $openTime + $intervalSeconds - 1;
        
        $existing = Kline::where([
            'symbol' => $symbol,
            'interval' => $interval,
            'open_time' => $openTime
        ])->find();
        
        if ($existing) {
            // 更新现有K线
            $data = [
                'high_price' => max($existing->high_price, $price),
                'low_price' => min($existing->low_price, $price),
                'close_price' => $price,
                'volume' => $existing->volume + $volume,
                'amount' => $existing->amount + ($price * $volume),
                'close_time' => $closeTime,
                'updated_at' => date('Y-m-d H:i:s')
            ];
            $existing->save($data);
        } else {
            // 创建新K线
            $data = [
                'symbol' => $symbol,
                'interval' => $interval,
                'open_time' => $openTime,
                'close_time' => $closeTime,
                'open_price' => $price,
                'high_price' => $price,
                'low_price' => $price,
                'close_price' => $price,
                'volume' => $volume,
                'amount' => $price * $volume,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            Kline::create($data);
        }
        
        // 清除相关缓存
        $this->clearKlineCache($symbol, $interval);
    }

    /**
     * 生成模拟K线数据
     */
    public function generateSimulateData(string $symbol, string $interval, int $count = 100): array
    {
        try {
            Kline::generateKlineData($symbol, $interval, $count);
            
            return [
                'code' => 1,
                'msg' => '模拟数据生成成功',
                'data' => [
                    'symbol' => $symbol,
                    'interval' => $interval,
                    'count' => $count
                ]
            ];
            
        } catch (\Exception $e) {
            Log::error('生成模拟K线数据失败：' . $e->getMessage());
            return [
                'code' => 0,
                'msg' => '生成失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 获取24小时行情统计
     */
    public function get24hTicker(string $symbol): array
    {
        try {
            $cacheKey = "ticker_24h_{$symbol}";
            
            return Cache::remember($cacheKey, function() use ($symbol) {
                $stats = Kline::get24hStats($symbol);
                
                return [
                    'symbol' => $symbol,
                    'price' => $stats['close'],
                    'open' => $stats['open'],
                    'high' => $stats['high'],
                    'low' => $stats['low'],
                    'volume' => $stats['volume'],
                    'amount' => $stats['amount'],
                    'change' => $stats['change'],
                    'change_percent' => $stats['change_percent'],
                    'timestamp' => time()
                ];
            }, 30); // 缓存30秒
            
        } catch (\Exception $e) {
            Log::error('获取24小时行情失败：' . $e->getMessage());
            return [
                'symbol' => $symbol,
                'price' => 0,
                'open' => 0,
                'high' => 0,
                'low' => 0,
                'volume' => 0,
                'amount' => 0,
                'change' => 0,
                'change_percent' => 0,
                'timestamp' => time()
            ];
        }
    }

    /**
     * 获取所有交易对的24小时行情
     */
    public function getAllTickers(): array
    {
        try {
            $symbols = $this->getActiveSymbols();
            $tickers = [];
            
            foreach ($symbols as $symbol) {
                $tickers[] = $this->get24hTicker($symbol);
            }
            
            return [
                'code' => 1,
                'data' => $tickers
            ];
            
        } catch (\Exception $e) {
            Log::error('获取所有行情失败：' . $e->getMessage());
            return [
                'code' => 0,
                'msg' => '获取失败',
                'data' => []
            ];
        }
    }

    /**
     * 获取活跃的交易对列表
     */
    private function getActiveSymbols(): array
    {
        // 从交易对配置表获取，这里先返回默认值
        return ['BTCUSDT', 'ETHUSDT', 'LTCUSDT', 'EOSUSDT', 'XRPUSDT'];
    }

    /**
     * 清除K线缓存
     */
    private function clearKlineCache(string $symbol, string $interval): void
    {
        $patterns = [
            "kline_{$symbol}_{$interval}_*",
            "ticker_24h_{$symbol}"
        ];
        
        foreach ($patterns as $pattern) {
            Cache::tag('kline')->clear();
        }
    }

    /**
     * 获取深度数据（基于最新价格生成模拟深度）
     */
    public function getDepthData(string $symbol, int $limit = 20): array
    {
        try {
            $latest = Kline::getLatestKline($symbol, Kline::INTERVAL_1M);
            
            if (!$latest) {
                return ['bids' => [], 'asks' => []];
            }
            
            $currentPrice = $latest['close_price'];
            $bids = [];
            $asks = [];
            
            // 生成买盘数据
            for ($i = 1; $i <= $limit; $i++) {
                $price = $currentPrice - ($i * 0.01 * $currentPrice); // 价格递减
                $amount = mt_rand(100, 1000) / 100;
                $bids[] = [
                    'price' => round($price, 2),
                    'amount' => $amount,
                    'total' => round($price * $amount, 2)
                ];
            }
            
            // 生成卖盘数据
            for ($i = 1; $i <= $limit; $i++) {
                $price = $currentPrice + ($i * 0.01 * $currentPrice); // 价格递增
                $amount = mt_rand(100, 1000) / 100;
                $asks[] = [
                    'price' => round($price, 2),
                    'amount' => $amount,
                    'total' => round($price * $amount, 2)
                ];
            }
            
            return [
                'symbol' => $symbol,
                'bids' => $bids,
                'asks' => $asks,
                'timestamp' => time()
            ];
            
        } catch (\Exception $e) {
            Log::error('获取深度数据失败：' . $e->getMessage());
            return ['bids' => [], 'asks' => []];
        }
    }

    /**
     * 初始化交易对的K线数据
     */
    public function initSymbolKlines(string $symbol): array
    {
        try {
            $intervals = [
                Kline::INTERVAL_1M,
                Kline::INTERVAL_5M,
                Kline::INTERVAL_15M,
                Kline::INTERVAL_1H,
                Kline::INTERVAL_4H,
                Kline::INTERVAL_1D
            ];
            
            foreach ($intervals as $interval) {
                Kline::generateKlineData($symbol, $interval, 200);
            }
            
            return [
                'code' => 1,
                'msg' => '交易对K线数据初始化成功',
                'data' => [
                    'symbol' => $symbol,
                    'intervals' => $intervals
                ]
            ];
            
        } catch (\Exception $e) {
            Log::error('初始化K线数据失败：' . $e->getMessage());
            return [
                'code' => 0,
                'msg' => '初始化失败：' . $e->getMessage()
            ];
        }
    }
}
