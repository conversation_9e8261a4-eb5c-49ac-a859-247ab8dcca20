<?php
// +----------------------------------------------------------------------
// | 应用设置
// +----------------------------------------------------------------------

use think\facade\Env;

return [
    // 应用地址
    'app_host'         => Env::get('app.host', ''),
    // 应用的命名空间
    'app_namespace'    => '',
    // 是否启用路由
    'with_route'       => true,
    // 是否启用事件
    'with_event'       => true,
    // 开启应用快速访问
    'app_express'      => true,
    // 默认应用
    'default_app'      => 'index',
    // 默认时区
    'default_timezone' => 'Asia/Shanghai',

    // 应用映射（自动多应用模式有效）
    'app_map' => [
        'api'   => 'api',
        'admin' => 'admin', 
        'agent' => 'agent',
    ],

    // 域名绑定（自动多应用模式有效）
    'domain_bind' => [],

    // 禁止URL访问的应用列表（自动多应用模式有效）
    'deny_app_list' => ['common'],

    // 异常页面的模板文件
    'exception_tmpl'   => app()->getThinkPath() . 'tpl/think_exception.tpl',

    // 错误显示信息,非调试模式有效
    'error_message'    => '页面错误！请稍后再试～',
    // 显示错误信息
    'show_error_msg'   => false,

    // 系统配置
    'system' => [
        'name'    => Env::get('system.name', 'GVD数字货币交易平台'),
        'version' => Env::get('system.version', '2.0.0'),
        'author'  => Env::get('system.author', 'GVD Team'),
        'url'     => Env::get('system.url', 'https://gvd-trading.com'),
    ],

    // 交易配置
    'trading' => [
        'fee_rate'      => Env::get('trading.fee_rate', 0.002),
        'leverage_max'  => Env::get('trading.leverage_max', 100),
        'margin_rate'   => Env::get('trading.margin_rate', 0.1),
    ],

    // 安全配置
    'security' => [
        'key'         => Env::get('security.key', 'gvd_security_key_2024'),
        'encrypt_key' => Env::get('security.encrypt_key', 'gvd_encrypt_key_2024'),
    ],
];
