<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 用户模型
 */
class User extends Model
{
    protected $name = 'gvd_users';

    // 用户类型常量
    const USER_TYPE_FORMAL = 1;  // 正式用户（通过前端验证码注册）
    const USER_TYPE_TEST = 2;    // 测试用户（通过管理端或代理端注册）
    
    // 设置字段信息
    protected $schema = [
        'id'                => 'int',
        'username'          => 'string',
        'email'             => 'string',
        'phone'             => 'string',
        'password'          => 'string',
        'real_name'         => 'string',
        'id_card'           => 'string',
        'avatar'            => 'string',
        'level'             => 'int',
        'status'            => 'int',
        'kyc_status'        => 'int',
        'google_auth_secret' => 'string',
        'is_google_auth'    => 'int',
        'invite_code'       => 'string',
        'parent_id'         => 'int',
        'user_type'         => 'int',
        'agent_id'          => 'int',
        'register_ip'       => 'string',
        'last_login_time'   => 'datetime',
        'last_login_ip'     => 'string',
        'created_at'        => 'datetime',
        'updated_at'        => 'datetime'
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 隐藏字段
    protected $hidden = ['password', 'google_auth_secret'];
    
    // 状态常量
    const STATUS_DISABLED = 0;  // 禁用
    const STATUS_ENABLED = 1;   // 启用

    // 用户类型常量
    const USER_TYPE_FORMAL = 1;  // 正式用户（前端注册）
    const USER_TYPE_TEST = 2;    // 测试用户（后台/代理注册）

    // KYC状态常量
    const KYC_PENDING = 0;      // 未认证
    const KYC_REVIEWING = 1;    // 审核中
    const KYC_APPROVED = 2;     // 已认证
    const KYC_REJECTED = 3;     // 已拒绝
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            self::STATUS_DISABLED => '禁用',
            self::STATUS_ENABLED => '启用'
        ];
        return $status[$data['status']] ?? '未知';
    }
    
    /**
     * 获取KYC状态文本
     */
    public function getKycStatusTextAttr($value, $data)
    {
        $status = [
            self::KYC_PENDING => '未认证',
            self::KYC_REVIEWING => '审核中',
            self::KYC_APPROVED => '已认证',
            self::KYC_REJECTED => '已拒绝'
        ];
        return $status[$data['kyc_status']] ?? '未知';
    }
    
    /**
     * 关联用户资产
     */
    public function assets()
    {
        return $this->hasMany(UserAsset::class, 'user_id');
    }
    
    /**
     * 关联订单
     */
    public function orders()
    {
        return $this->hasMany(Order::class, 'user_id');
    }
    
    /**
     * 关联邀请的用户
     */
    public function invitedUsers()
    {
        return $this->hasMany(User::class, 'parent_id');
    }
    
    /**
     * 关联邀请人
     */
    public function inviter()
    {
        return $this->belongsTo(User::class, 'parent_id');
    }
    
    /**
     * 生成邀请码
     */
    public static function generateInviteCode(): string
    {
        do {
            $code = strtoupper(substr(md5(uniqid()), 0, 8));
        } while (self::where('invite_code', $code)->find());
        
        return $code;
    }
    
    /**
     * 根据邀请码获取用户
     */
    public static function getByInviteCode(string $code)
    {
        return self::where('invite_code', $code)->find();
    }
    
    /**
     * 检查用户是否启用
     */
    public function isEnabled(): bool
    {
        return $this->status == self::STATUS_ENABLED;
    }
    
    /**
     * 检查用户是否已KYC认证
     */
    public function isKycApproved(): bool
    {
        return $this->kyc_status == self::KYC_APPROVED;
    }
    
    /**
     * 获取用户等级信息
     */
    public function getLevelInfo()
    {
        // 这里可以根据等级返回相应的权限和费率信息
        $levels = [
            1 => ['name' => '普通用户', 'trade_fee' => 0.002],
            2 => ['name' => 'VIP1', 'trade_fee' => 0.0018],
            3 => ['name' => 'VIP2', 'trade_fee' => 0.0015],
            4 => ['name' => 'VIP3', 'trade_fee' => 0.001],
            5 => ['name' => 'VIP4', 'trade_fee' => 0.0008]
        ];
        
        return $levels[$this->level] ?? $levels[1];
    }
}
