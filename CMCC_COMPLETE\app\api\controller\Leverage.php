<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\common\service\LeverageService;
use think\facade\Validate;

/**
 * 杠杆交易控制器
 */
class Leverage extends BaseController
{
    protected $leverageService;

    public function __construct(\think\App $app, LeverageService $leverageService)
    {
        parent::__construct($app);
        $this->leverageService = $leverageService;
    }

    /**
     * 获取杠杆配置
     */
    public function getConfig()
    {
        $config = [
            'max_leverage' => 100,
            'min_margin' => 10,
            'max_margin' => 100000,
            'fee_rate' => 0.002,
            'supported_symbols' => [
                'BTCUSDT' => [
                    'symbol' => 'BTCUSDT',
                    'base_coin' => 'BTC',
                    'quote_coin' => 'USDT',
                    'min_margin' => 10,
                    'max_leverage' => 100
                ],
                'ETHUSDT' => [
                    'symbol' => 'ETHUSDT',
                    'base_coin' => 'ETH',
                    'quote_coin' => 'USDT',
                    'min_margin' => 10,
                    'max_leverage' => 50
                ]
            ]
        ];

        return $this->success($config, '获取配置成功');
    }

    /**
     * 创建杠杆订单
     */
    public function createOrder()
    {
        $data = $this->getParams([
            'symbol', 'order_type', 'margin', 'leverage',
            'stop_loss_price', 'take_profit_price'
        ]);

        // 添加用户ID
        $data['user_id'] = $this->getUserId();

        // 验证参数
        $validate = Validate::rule([
            'symbol' => 'require|in:BTCUSDT,ETHUSDT',
            'order_type' => 'require|in:1,2', // 1做多 2做空
            'margin' => 'require|float|between:10,100000',
            'leverage' => 'require|integer|between:1,100',
            'stop_loss_price' => 'float|egt:0',
            'take_profit_price' => 'float|egt:0'
        ])->message([
            'symbol.require' => '交易对不能为空',
            'symbol.in' => '不支持的交易对',
            'order_type.require' => '订单类型不能为空',
            'order_type.in' => '订单类型错误',
            'margin.require' => '保证金不能为空',
            'margin.float' => '保证金必须为数字',
            'margin.between' => '保证金范围为10-100000',
            'leverage.require' => '杠杆倍数不能为空',
            'leverage.integer' => '杠杆倍数必须为整数',
            'leverage.between' => '杠杆倍数范围为1-100倍',
            'stop_loss_price.float' => '止损价格必须为数字',
            'stop_loss_price.egt' => '止损价格必须大于等于0',
            'take_profit_price.float' => '止盈价格必须为数字',
            'take_profit_price.egt' => '止盈价格必须大于等于0'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        $result = $this->leverageService->createOrder($data);
        
        if ($result['code'] == 1) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 平仓订单
     */
    public function closeOrder()
    {
        $orderId = $this->request->param('order_id/d');
        
        if (!$orderId) {
            return $this->error('订单ID不能为空');
        }

        $result = $this->leverageService->closeOrder($orderId, $this->getUserId());
        
        if ($result['code'] == 1) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取订单列表
     */
    public function getOrders()
    {
        $params = $this->getPaginationParams();
        $params['status'] = $this->request->param('status');
        $params['symbol'] = $this->request->param('symbol');

        $result = $this->leverageService->getUserOrders($this->getUserId(), $params);
        
        if ($result['code'] == 1) {
            return $this->success($result['data'], $result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 获取单个订单详情
     */
    public function getOrder()
    {
        $orderId = $this->request->param('id/d');
        
        if (!$orderId) {
            return $this->error('订单ID不能为空');
        }

        $order = \think\facade\Db::name('leverage_orders')
            ->where('id', $orderId)
            ->where('user_id', $this->getUserId())
            ->find();

        if (!$order) {
            return $this->error('订单不存在');
        }

        // 如果是持仓中的订单，计算实时盈亏
        if ($order['status'] === 'open') {
            $currentPrice = $this->getCurrentPrice($order['symbol']);
            $order['current_price'] = $currentPrice;
            $order['unrealized_pnl'] = $this->calculateProfitLoss($order, $currentPrice);
        }

        return $this->success($order, '获取成功');
    }

    /**
     * 获取持仓列表
     */
    public function getPositions()
    {
        $positions = \think\facade\Db::name('leverage_orders')
            ->where('user_id', $this->getUserId())
            ->where('status', 'open')
            ->order('id desc')
            ->select()
            ->toArray();

        // 计算实时盈亏
        foreach ($positions as &$position) {
            $currentPrice = $this->getCurrentPrice($position['symbol']);
            $position['current_price'] = $currentPrice;
            $position['unrealized_pnl'] = $this->calculateProfitLoss($position, $currentPrice);
            
            // 计算收益率
            $position['return_rate'] = ($position['unrealized_pnl'] / $position['margin']) * 100;
        }

        return $this->success($positions, '获取成功');
    }

    /**
     * 追加保证金
     */
    public function addMargin()
    {
        $data = $this->getParams(['order_id', 'amount']);

        // 验证参数
        $validate = Validate::rule([
            'order_id' => 'require|integer|gt:0',
            'amount' => 'require|float|gt:0'
        ])->message([
            'order_id.require' => '订单ID不能为空',
            'order_id.integer' => '订单ID必须为整数',
            'order_id.gt' => '订单ID必须大于0',
            'amount.require' => '追加金额不能为空',
            'amount.float' => '追加金额必须为数字',
            'amount.gt' => '追加金额必须大于0'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        try {
            \think\facade\Db::startTrans();

            // 获取订单信息
            $order = \think\facade\Db::name('leverage_orders')
                ->where('id', $data['order_id'])
                ->where('user_id', $this->getUserId())
                ->where('status', 'open')
                ->find();

            if (!$order) {
                throw new \Exception('订单不存在或已平仓');
            }

            // 验证用户资产
            $userAsset = \think\facade\Db::name('user_assets')
                ->where('user_id', $this->getUserId())
                ->where('coin_symbol', 'USDT')
                ->find();

            if (!$userAsset || $userAsset['available'] < $data['amount']) {
                throw new \Exception('余额不足');
            }

            // 扣除资产
            \think\facade\Db::name('user_assets')
                ->where('user_id', $this->getUserId())
                ->where('coin_symbol', 'USDT')
                ->dec('available', $data['amount']);

            // 更新订单保证金
            \think\facade\Db::name('leverage_orders')
                ->where('id', $data['order_id'])
                ->inc('margin', $data['amount']);

            // 重新计算强平价格
            $newMargin = $order['margin'] + $data['amount'];
            $newLiquidationPrice = $this->calculateLiquidationPrice(
                $order['open_price'],
                $order['order_type'],
                $order['leverage'],
                $newMargin,
                $order['quantity']
            );

            \think\facade\Db::name('leverage_orders')
                ->where('id', $data['order_id'])
                ->update(['liquidation_price' => $newLiquidationPrice]);

            // 记录财务流水
            \think\facade\Db::name('financial_records')->insert([
                'user_id' => $this->getUserId(),
                'coin_symbol' => 'USDT',
                'amount' => -$data['amount'],
                'type' => 'add_margin',
                'remark' => '追加保证金',
                'order_id' => $data['order_id'],
                'created_at' => date('Y-m-d H:i:s')
            ]);

            \think\facade\Db::commit();

            return $this->success([
                'new_margin' => $newMargin,
                'new_liquidation_price' => $newLiquidationPrice
            ], '追加保证金成功');

        } catch (\Exception $e) {
            \think\facade\Db::rollback();
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取当前价格
     */
    private function getCurrentPrice(string $symbol): float
    {
        $cacheKey = 'price:' . $symbol;
        $price = \think\facade\Cache::get($cacheKey);
        
        if (!$price) {
            // 模拟价格数据
            $prices = [
                'BTCUSDT' => 50000 + rand(-5000, 5000),
                'ETHUSDT' => 3000 + rand(-500, 500)
            ];
            $price = $prices[$symbol] ?? 50000;
            \think\facade\Cache::set($cacheKey, $price, 10);
        }
        
        return (float)$price;
    }

    /**
     * 计算盈亏
     */
    private function calculateProfitLoss(array $order, float $currentPrice): float
    {
        $priceDiff = $currentPrice - $order['open_price'];
        
        if ($order['order_type'] == 2) { // 做空
            $priceDiff = -$priceDiff;
        }
        
        return ($priceDiff / $order['open_price']) * $order['margin'] * $order['leverage'];
    }

    /**
     * 计算强平价格
     */
    private function calculateLiquidationPrice(float $openPrice, int $orderType, int $leverage, float $margin = null, float $quantity = null): float
    {
        $marginRate = 0.05; // 5%维持保证金率
        
        if ($orderType == 1) { // 做多
            return $openPrice * (1 - (1 / $leverage) + $marginRate);
        } else { // 做空
            return $openPrice * (1 + (1 / $leverage) - $marginRate);
        }
    }
}
