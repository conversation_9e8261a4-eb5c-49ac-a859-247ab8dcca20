<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Db;
use think\facade\Cache;
use think\facade\Log;

/**
 * 系统配置服务类
 */
class SystemConfigService
{
    /**
     * 获取系统配置
     */
    public function getConfig(string $type, string $key = ''): array
    {
        try {
            $cacheKey = "system_config_{$type}" . ($key ? "_{$key}" : '');
            $config = Cache::get($cacheKey);
            
            if (!$config) {
                $query = Db::name('system_config')->where('type', $type);
                
                if ($key) {
                    $query->where('key', $key);
                    $config = $query->find();
                } else {
                    $configs = $query->select()->toArray();
                    $config = [];
                    foreach ($configs as $item) {
                        $config[$item['key']] = $item['value'];
                    }
                }
                
                Cache::set($cacheKey, $config, 3600); // 缓存1小时
            }
            
            return ['code' => 1, 'data' => $config];
            
        } catch (\Exception $e) {
            Log::error('获取系统配置失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取配置失败'];
        }
    }

    /**
     * 更新系统配置
     */
    public function updateConfig(string $type, string $key, string $value, int $adminId): array
    {
        try {
            Db::startTrans();
            
            // 检查配置是否存在
            $exists = Db::name('system_config')
                       ->where('type', $type)
                       ->where('key', $key)
                       ->find();
            
            if ($exists) {
                Db::name('system_config')
                  ->where('type', $type)
                  ->where('key', $key)
                  ->update([
                      'value' => $value,
                      'updated_at' => date('Y-m-d H:i:s')
                  ]);
            } else {
                Db::name('system_config')->insert([
                    'type' => $type,
                    'key' => $key,
                    'value' => $value,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }
            
            // 记录操作日志
            Db::name('admin_logs')->insert([
                'admin_id' => $adminId,
                'action' => 'update_config',
                'content' => "更新系统配置 {$type}.{$key} = {$value}",
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            // 清除缓存
            $this->clearConfigCache($type, $key);
            
            Db::commit();
            
            return ['code' => 1, 'msg' => '配置更新成功'];
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('更新系统配置失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '配置更新失败'];
        }
    }

    /**
     * 获取合约配置
     */
    public function getContractConfig(string $symbol = ''): array
    {
        try {
            $cacheKey = 'contract_config' . ($symbol ? "_{$symbol}" : '');
            $config = Cache::get($cacheKey);
            
            if (!$config) {
                $dbConfig = Db::name('contract_config')->find();
                
                if ($dbConfig) {
                    // 解析JSON字段
                    $config = [
                        'min_amount' => floatval($dbConfig['min_amount']),
                        'max_amount' => floatval($dbConfig['max_amount']),
                        'win_rate' => floatval($dbConfig['win_rate']),
                        'available_times' => json_decode($dbConfig['available_times'] ?? '[]', true),
                        'available_coins' => json_decode($dbConfig['available_coins'] ?? '[]', true),
                        'quick_amounts' => json_decode($dbConfig['quick_amounts'] ?? '[]', true),
                        'profit_rates' => json_decode($dbConfig['profit_rates'] ?? '{}', true)
                    ];
                } else {
                    // 默认配置
                    $config = [
                        'min_amount' => 10,
                        'max_amount' => 10000,
                        'win_rate' => 0.85,
                        'available_times' => [60, 180, 300, 600],
                        'available_coins' => ['BTC', 'ETH', 'LTC', 'EOS', 'XRP'],
                        'quick_amounts' => [50, 100, 500, 1000],
                        'profit_rates' => [
                            60 => 0.80,
                            180 => 0.85,
                            300 => 0.90,
                            600 => 0.95
                        ]
                    ];
                }
                
                Cache::set($cacheKey, $config, 300); // 缓存5分钟
            }
            
            return ['code' => 1, 'data' => $config];
            
        } catch (\Exception $e) {
            Log::error('获取合约配置失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取配置失败'];
        }
    }

    /**
     * 更新合约配置
     */
    public function updateContractConfig(array $data, int $adminId): array
    {
        try {
            Db::startTrans();
            
            // 验证数据
            $configData = [
                'min_amount' => floatval($data['min_amount'] ?? 10),
                'max_amount' => floatval($data['max_amount'] ?? 10000),
                'win_rate' => floatval($data['win_rate'] ?? 0.85),
                'available_times' => json_encode($data['available_times'] ?? [60, 180, 300, 600]),
                'available_coins' => json_encode($data['available_coins'] ?? ['BTC', 'ETH', 'LTC']),
                'quick_amounts' => json_encode($data['quick_amounts'] ?? [50, 100, 500, 1000]),
                'profit_rates' => json_encode($data['profit_rates'] ?? []),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            // 检查是否存在配置
            $exists = Db::name('contract_config')->find();
            
            if ($exists) {
                Db::name('contract_config')->update($configData);
            } else {
                $configData['created_at'] = date('Y-m-d H:i:s');
                Db::name('contract_config')->insert($configData);
            }
            
            // 记录操作日志
            Db::name('admin_logs')->insert([
                'admin_id' => $adminId,
                'action' => 'update_contract_config',
                'content' => '更新合约配置',
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            // 清除缓存
            Cache::delete('contract_config');
            
            Db::commit();
            
            return ['code' => 1, 'msg' => '合约配置更新成功'];
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('更新合约配置失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '配置更新失败'];
        }
    }

    /**
     * 上传图片
     */
    public function uploadImage(string $type, string $key, $file, int $adminId): array
    {
        try {
            // 验证文件
            if (!$file->isValid()) {
                return ['code' => 0, 'msg' => '文件上传失败'];
            }
            
            // 验证文件类型
            $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            $extension = strtolower($file->getOriginalExtension());
            
            if (!in_array($extension, $allowedTypes)) {
                return ['code' => 0, 'msg' => '不支持的文件类型'];
            }
            
            // 验证文件大小（5MB）
            if ($file->getSize() > 5 * 1024 * 1024) {
                return ['code' => 0, 'msg' => '文件大小不能超过5MB'];
            }
            
            // 生成文件名
            $filename = date('Ymd') . '_' . uniqid() . '.' . $extension;
            $savePath = "uploads/system/{$type}/";
            
            // 确保目录存在
            if (!is_dir($savePath)) {
                mkdir($savePath, 0755, true);
            }
            
            // 移动文件
            $file->move($savePath, $filename);
            $url = '/' . $savePath . $filename;
            
            // 更新配置
            $this->updateConfig($type, $key, $url, $adminId);
            
            return [
                'code' => 1,
                'msg' => '图片上传成功',
                'data' => ['url' => $url]
            ];
            
        } catch (\Exception $e) {
            Log::error('上传图片失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '上传失败'];
        }
    }

    /**
     * 获取图片配置
     */
    public function getImageConfig(): array
    {
        try {
            $images = Db::name('system_config')
                       ->where('type', 'image')
                       ->column('value', 'key');
            
            return ['code' => 1, 'data' => $images];
            
        } catch (\Exception $e) {
            Log::error('获取图片配置失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 获取万能验证码
     */
    public function getUniversalCode(): string
    {
        $config = $this->getConfig('system', 'universal_verification_code');
        return $config['data']['value'] ?? '888888';
    }

    /**
     * 设置万能验证码
     */
    public function setUniversalCode(string $code, int $adminId): array
    {
        return $this->updateConfig('system', 'universal_verification_code', $code, $adminId);
    }

    /**
     * 获取前端可配置的图片项
     */
    public function getConfigurableImages(): array
    {
        return [
            'ui' => [
                'logo' => '网站Logo',
                'favicon' => '网站图标',
                'login_bg' => '登录页背景',
                'banner_1' => '首页轮播图1',
                'banner_2' => '首页轮播图2',
                'banner_3' => '首页轮播图3',
                'about_bg' => '关于我们背景',
                'contact_bg' => '联系我们背景'
            ],
            'contract' => [
                'up_icon' => '看涨图标',
                'down_icon' => '看跌图标',
                'win_icon' => '盈利图标',
                'lose_icon' => '亏损图标',
                'bg_image' => '合约背景图'
            ],
            'trading' => [
                'buy_icon' => '买入图标',
                'sell_icon' => '卖出图标',
                'chart_bg' => '图表背景'
            ]
        ];
    }

    /**
     * 获取前端配置（供前端调用）
     */
    public function getFrontendConfig(): array
    {
        try {
            $config = [];
            
            // 获取图片配置
            $imageConfig = $this->getImageConfig();
            if ($imageConfig['code']) {
                $config['images'] = $imageConfig['data'];
            }
            
            // 获取合约配置
            $contractConfig = $this->getContractConfig();
            if ($contractConfig['code']) {
                $config['contract'] = $contractConfig['data'];
            }
            
            // 获取其他系统配置
            $systemConfig = $this->getConfig('system');
            if ($systemConfig['code']) {
                $config['system'] = $systemConfig['data'];
            }
            
            return ['code' => 1, 'data' => $config];
            
        } catch (\Exception $e) {
            Log::error('获取前端配置失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取配置失败'];
        }
    }

    /**
     * 清除配置缓存
     */
    private function clearConfigCache(string $type, string $key): void
    {
        try {
            $cacheKeys = [
                "system_config_{$type}_{$key}",
                "system_config_{$type}",
                "system_config_all"
            ];

            foreach ($cacheKeys as $cacheKey) {
                Cache::delete($cacheKey);
            }

        } catch (\Exception $e) {
            Log::error('清除配置缓存失败: ' . $e->getMessage());
        }
    }
}
