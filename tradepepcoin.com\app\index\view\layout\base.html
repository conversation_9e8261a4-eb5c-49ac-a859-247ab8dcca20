<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>{$title|default='数字货币交易平台'}</title>
    <meta name="description" content="专业的数字货币交易平台，提供比特币、以太坊等主流数字货币交易服务">
    <meta name="keywords" content="数字货币,比特币,以太坊,区块链,交易平台">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="/static/css/style.css" rel="stylesheet">
    
    {block name="css"}{/block}
    <!-- 客服系统样式 -->
    <link rel="stylesheet" href="/static/css/customer-service.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">
                <i class="fas fa-coins me-2"></i>CryptoEx
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            交易
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/trade">币币交易</a></li>
                            <li><a class="dropdown-item" href="/contract">秒合约</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ido">新币认购</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            资产
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/wallet">我的钱包</a></li>
                            <li><a class="dropdown-item" href="/deposit">充值</a></li>
                            <li><a class="dropdown-item" href="/withdraw">提现</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/notice">公告</a></li>
                    <li class="nav-item">
                        <a class="nav-link" href="/help">帮助</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    {if condition="session('user_id')"}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>{:session('user_info.username')}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/user/profile">个人中心</a></li>
                            <li><a class="dropdown-item" href="/user/orders">我的订单</a></li>
                            <li><a class="dropdown-item" href="/user/assets">资产管理</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/auth/logout">退出登录</a></li>
                        </ul>
                    </li>
                    {else}
                    <li class="nav-item">
                        <a class="nav-link" href="/auth/login">登录</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-outline-light ms-2 px-3" href="/auth/register">注册</a>
                    </li>
                    {/if}
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        {block name="content"}{/block}
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-3">
                    <h5 class="mb-3">
                        <i class="fas fa-coins me-2"></i>CryptoEx
                    </h5>
                    <p class="text-muted">专业的数字货币交易平台，为全球用户提供安全、稳定、高效的数字资产交易服务。</p>
                </div>
                <div class="col-md-2">
                    <h6 class="mb-3">交易</h6>
                    <ul class="list-unstyled">
                        <li><a href="/trade" class="text-muted text-decoration-none">币币交易</a></li>
                        <li><a href="/contract" class="text-muted text-decoration-none">秒合约</a></li>
                        <li><a href="/ido" class="text-muted text-decoration-none">新币认购</a></li>
                    </ul>
                </div>
                <div class="col-md-2">
                    <h6 class="mb-3">资产</h6>
                    <ul class="list-unstyled">
                        <li><a href="/wallet" class="text-muted text-decoration-none">我的钱包</a></li>
                        <li><a href="/deposit" class="text-muted text-decoration-none">充值</a></li>
                        <li><a href="/withdraw" class="text-muted text-decoration-none">提现</a></li>
                    </ul>
                </div>
                <div class="col-md-2">
                    <h6 class="mb-3">支持</h6>
                    <ul class="list-unstyled">
                        <li><a href="/help" class="text-muted text-decoration-none">帮助中心</a></li>
                        <li><a href="/notice" class="text-muted text-decoration-none">平台公告</a></li>
                        <li><a href="/about" class="text-muted text-decoration-none">关于我们</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h6 class="mb-3">联系我们</h6>
                    <p class="text-muted mb-2">
                        <i class="fas fa-envelope me-2"></i><EMAIL>
                    </p>
                    <p class="text-muted mb-2">
                        <i class="fas fa-phone me-2"></i>************
                    </p>
                    <div class="mt-3">
                        <a href="#" class="text-muted me-3"><i class="fab fa-twitter fa-lg"></i></a>
                        <a href="#" class="text-muted me-3"><i class="fab fa-telegram fa-lg"></i></a>
                        <a href="#" class="text-muted me-3"><i class="fab fa-discord fa-lg"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2024 CryptoEx. 版权所有</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="/terms" class="text-muted text-decoration-none me-3">服务条款</a>
                    <a href="/privacy" class="text-muted text-decoration-none">隐私政策</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- 客服悬浮按钮 -->
    <div class="customer-service">
        <button class="btn btn-primary rounded-circle" data-bs-toggle="modal" data-bs-target="#customerServiceModal">
            <i class="fas fa-comments"></i>
        </button>
    </div>

    <!-- 客服模态框 -->
    <div class="modal fade" id="customerServiceModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">在线客服</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="chatMessages" class="chat-messages mb-3" style="height: 300px; overflow-y: auto;">
                        <div class="alert alert-info">
                            <i class="fas fa-robot me-2"></i>您好！有什么可以帮助您的吗？
                        </div>
                    </div>
                    <div class="input-group">
                        <input type="text" class="form-control" id="chatInput" placeholder="请输入您的问题...">
                        <button class="btn btn-primary" type="button" onclick="sendMessage()">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- 自定义脚本 -->
    <script src="/static/js/app.js"></script>
    
    {block name="js"}{/block}
    <!-- 客服系统脚本 -->
    <script src="/static/js/customer-service-new.js"></script>
<script>
console.log("内嵌客服代码开始执行");
const serviceBtn = document.createElement("div");
serviceBtn.innerHTML = "客服";
serviceBtn.style.cssText = "position:fixed;bottom:20px;right:20px;width:60px;height:60px;background:#007bff;color:white;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;z-index:9999;font-weight:bold;";
serviceBtn.onclick = function() { alert("客服功能正常！"); };
document.body.appendChild(serviceBtn);
console.log("客服按钮已添加");
</script>
</body>
</html>
