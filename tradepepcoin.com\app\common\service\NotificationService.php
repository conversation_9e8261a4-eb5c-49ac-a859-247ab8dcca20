<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\Notification;
use app\common\model\SystemAnnouncement;
use app\common\model\User;
use think\facade\Log;

/**
 * 通知服务类 - 兼容老系统通知逻辑
 */
class NotificationService
{
    /**
     * 发送系统通知 - 兼容老系统tw_notice
     */
    public function sendSystemNotification(array $data): array
    {
        try {
            $notification = Notification::create([
                'user_id' => $data['user_id'] ?? 0,
                'title' => $data['title'],
                'content' => $data['content'],
                'type' => $data['type'] ?? 1, // 1系统通知 2交易通知 3安全通知
                'is_read' => 0,
                'created_at' => date('Y-m-d H:i:s'),
            ]);
            
            return ['code' => 1, 'msg' => '通知发送成功', 'data' => ['id' => $notification->id]];
            
        } catch (\Exception $e) {
            Log::error('发送系统通知失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '通知发送失败'];
        }
    }
    
    /**
     * 批量发送通知
     */
    public function sendBatchNotification(array $userIds, array $data): array
    {
        try {
            $successCount = 0;
            
            foreach ($userIds as $userId) {
                $notificationData = array_merge($data, ['user_id' => $userId]);
                $result = $this->sendSystemNotification($notificationData);
                
                if ($result['code'] == 1) {
                    $successCount++;
                }
            }
            
            return [
                'code' => 1,
                'msg' => "批量发送完成，成功发送 {$successCount} 条通知",
                'data' => ['success_count' => $successCount]
            ];
            
        } catch (\Exception $e) {
            Log::error('批量发送通知失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '批量发送失败'];
        }
    }
    
    /**
     * 发送交易通知
     */
    public function sendTradeNotification(int $userId, string $type, array $data): array
    {
        $titles = [
            'order_filled' => '订单成交通知',
            'order_cancelled' => '订单撤销通知',
            'contract_settled' => '合约结算通知',
            'deposit_confirmed' => '充值到账通知',
            'withdrawal_processed' => '提现处理通知',
        ];
        
        $title = $titles[$type] ?? '交易通知';
        
        $content = $this->generateTradeNotificationContent($type, $data);
        
        return $this->sendSystemNotification([
            'user_id' => $userId,
            'title' => $title,
            'content' => $content,
            'type' => 2, // 交易通知
        ]);
    }
    
    /**
     * 发送安全通知
     */
    public function sendSecurityNotification(int $userId, string $type, array $data): array
    {
        $titles = [
            'login' => '登录通知',
            'password_changed' => '密码修改通知',
            'kyc_approved' => '实名认证通过',
            'kyc_rejected' => '实名认证被拒',
            'account_frozen' => '账户冻结通知',
            'account_unfrozen' => '账户解冻通知',
        ];
        
        $title = $titles[$type] ?? '安全通知';
        
        $content = $this->generateSecurityNotificationContent($type, $data);
        
        return $this->sendSystemNotification([
            'user_id' => $userId,
            'title' => $title,
            'content' => $content,
            'type' => 3, // 安全通知
        ]);
    }
    
    /**
     * 获取用户通知列表
     */
    public function getUserNotifications(int $userId, array $params = []): array
    {
        try {
            $where = ['user_id' => $userId];
            
            if (isset($params['type'])) {
                $where['type'] = $params['type'];
            }
            
            if (isset($params['is_read'])) {
                $where['is_read'] = $params['is_read'];
            }
            
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 20;
            
            $notifications = Notification::where($where)
                                        ->order('created_at', 'desc')
                                        ->paginate([
                                            'list_rows' => $limit,
                                            'page' => $page
                                        ]);
            
            return [
                'code' => 1,
                'data' => $notifications->items(),
                'total' => $notifications->total(),
                'unread_count' => $this->getUnreadCount($userId)
            ];
            
        } catch (\Exception $e) {
            Log::error('获取用户通知失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取通知失败'];
        }
    }
    
    /**
     * 标记通知为已读
     */
    public function markAsRead(int $userId, $notificationIds): array
    {
        try {
            if (is_array($notificationIds)) {
                $count = Notification::where('user_id', $userId)
                                    ->where('id', 'in', $notificationIds)
                                    ->update(['is_read' => 1]);
            } else {
                $count = Notification::where('user_id', $userId)
                                    ->where('id', $notificationIds)
                                    ->update(['is_read' => 1]);
            }
            
            return ['code' => 1, 'msg' => '标记成功', 'data' => ['count' => $count]];
            
        } catch (\Exception $e) {
            Log::error('标记通知已读失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '标记失败'];
        }
    }
    
    /**
     * 标记所有通知为已读
     */
    public function markAllAsRead(int $userId): array
    {
        try {
            $count = Notification::where('user_id', $userId)
                                ->where('is_read', 0)
                                ->update(['is_read' => 1]);
            
            return ['code' => 1, 'msg' => '全部标记成功', 'data' => ['count' => $count]];
            
        } catch (\Exception $e) {
            Log::error('标记所有通知已读失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '标记失败'];
        }
    }
    
    /**
     * 删除通知
     */
    public function deleteNotification(int $userId, $notificationIds): array
    {
        try {
            if (is_array($notificationIds)) {
                $count = Notification::where('user_id', $userId)
                                    ->where('id', 'in', $notificationIds)
                                    ->delete();
            } else {
                $count = Notification::where('user_id', $userId)
                                    ->where('id', $notificationIds)
                                    ->delete();
            }
            
            return ['code' => 1, 'msg' => '删除成功', 'data' => ['count' => $count]];
            
        } catch (\Exception $e) {
            Log::error('删除通知失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '删除失败'];
        }
    }
    
    /**
     * 获取未读通知数量
     */
    public function getUnreadCount(int $userId): int
    {
        return Notification::where('user_id', $userId)
                          ->where('is_read', 0)
                          ->count();
    }
    
    /**
     * 创建系统公告 - 兼容老系统tw_content
     */
    public function createAnnouncement(array $data): array
    {
        try {
            $announcement = SystemAnnouncement::create([
                'title' => $data['title'],
                'content' => $data['content'],
                'type' => $data['type'] ?? 1, // 1系统公告 2活动公告 3维护公告
                'is_top' => $data['is_top'] ?? 0,
                'status' => $data['status'] ?? 1,
                'created_by' => $data['created_by'] ?? 0,
                'created_at' => date('Y-m-d H:i:s'),
            ]);
            
            return ['code' => 1, 'msg' => '公告创建成功', 'data' => ['id' => $announcement->id]];
            
        } catch (\Exception $e) {
            Log::error('创建系统公告失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '公告创建失败'];
        }
    }
    
    /**
     * 获取系统公告列表
     */
    public function getAnnouncements(array $params = []): array
    {
        try {
            $where = ['status' => 1];
            
            if (!empty($params['type'])) {
                $where['type'] = $params['type'];
            }
            
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 10;
            
            $announcements = SystemAnnouncement::where($where)
                                              ->order('is_top', 'desc')
                                              ->order('created_at', 'desc')
                                              ->paginate([
                                                  'list_rows' => $limit,
                                                  'page' => $page
                                              ]);
            
            return [
                'code' => 1,
                'data' => $announcements->items(),
                'total' => $announcements->total()
            ];
            
        } catch (\Exception $e) {
            Log::error('获取系统公告失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取公告失败'];
        }
    }
    
    /**
     * 生成交易通知内容
     */
    private function generateTradeNotificationContent(string $type, array $data): string
    {
        switch ($type) {
            case 'order_filled':
                return "您的{$data['symbol']}订单已成交，成交价格：{$data['price']}，成交数量：{$data['amount']}";
            case 'order_cancelled':
                return "您的{$data['symbol']}订单已撤销，订单号：{$data['order_id']}";
            case 'contract_settled':
                $result = $data['is_win'] ? '盈利' : '亏损';
                return "您的{$data['coin_name']}合约已结算，结果：{$result}，盈亏：{$data['profit_loss']}";
            case 'deposit_confirmed':
                return "您的{$data['coin_symbol']}充值已到账，金额：{$data['amount']}";
            case 'withdrawal_processed':
                return "您的{$data['coin_symbol']}提现已处理，金额：{$data['amount']}";
            default:
                return '您有新的交易通知';
        }
    }
    
    /**
     * 生成安全通知内容
     */
    private function generateSecurityNotificationContent(string $type, array $data): string
    {
        switch ($type) {
            case 'login':
                return "您的账户于{$data['time']}在{$data['location']}登录，IP：{$data['ip']}";
            case 'password_changed':
                return "您的登录密码已于{$data['time']}修改成功";
            case 'kyc_approved':
                return "恭喜！您的实名认证已通过审核";
            case 'kyc_rejected':
                return "很抱歉，您的实名认证未通过审核，原因：{$data['reason']}";
            case 'account_frozen':
                return "您的账户已被冻结，原因：{$data['reason']}";
            case 'account_unfrozen':
                return "您的账户已解除冻结";
            default:
                return '您有新的安全通知';
        }
    }
}
