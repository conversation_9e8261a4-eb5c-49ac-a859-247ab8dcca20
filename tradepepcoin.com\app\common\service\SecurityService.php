<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Cache;
use think\facade\Request;
use think\facade\Log;

/**
 * 安全服务类
 */
class SecurityService
{
    /**
     * 检查IP是否在白名单
     */
    public function isIpWhitelisted(string $ip): bool
    {
        $whitelist = config('security.ip_whitelist', []);
        
        if (empty($whitelist)) {
            return true; // 如果没有配置白名单，则允许所有IP
        }

        foreach ($whitelist as $allowedIp) {
            if ($this->ipMatch($ip, $allowedIp)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查IP是否在黑名单
     */
    public function isIpBlacklisted(string $ip): bool
    {
        $blacklist = config('security.ip_blacklist', []);
        
        foreach ($blacklist as $blockedIp) {
            if ($this->ipMatch($ip, $blockedIp)) {
                return true;
            }
        }

        return false;
    }

    /**
     * IP匹配检查（支持CIDR格式）
     */
    private function ipMatch(string $ip, string $pattern): bool
    {
        if ($ip === $pattern) {
            return true;
        }

        if (strpos($pattern, '/') !== false) {
            // CIDR格式
            [$subnet, $mask] = explode('/', $pattern);
            $ipLong = ip2long($ip);
            $subnetLong = ip2long($subnet);
            $maskLong = -1 << (32 - (int)$mask);
            
            return ($ipLong & $maskLong) === ($subnetLong & $maskLong);
        }

        return false;
    }

    /**
     * 防刷机制检查
     */
    public function checkRateLimit(string $key, int $maxRequests = 60, int $timeWindow = 60): bool
    {
        $cacheKey = "rate_limit:{$key}";
        $requests = Cache::get($cacheKey, 0);
        
        if ($requests >= $maxRequests) {
            $this->logSecurityEvent('rate_limit_exceeded', [
                'key' => $key,
                'requests' => $requests,
                'max_requests' => $maxRequests,
                'time_window' => $timeWindow
            ]);
            return false;
        }
        
        Cache::set($cacheKey, $requests + 1, $timeWindow);
        return true;
    }

    /**
     * 登录失败次数检查
     */
    public function checkLoginAttempts(string $identifier, int $maxAttempts = 5, int $lockTime = 300): bool
    {
        $cacheKey = "login_attempts:{$identifier}";
        $attempts = Cache::get($cacheKey, 0);
        
        if ($attempts >= $maxAttempts) {
            $this->logSecurityEvent('login_locked', [
                'identifier' => $identifier,
                'attempts' => $attempts,
                'max_attempts' => $maxAttempts
            ]);
            return false;
        }
        
        return true;
    }

    /**
     * 记录登录失败
     */
    public function recordLoginFailure(string $identifier, int $maxAttempts = 5, int $lockTime = 300): void
    {
        $cacheKey = "login_attempts:{$identifier}";
        $attempts = Cache::get($cacheKey, 0) + 1;
        
        Cache::set($cacheKey, $attempts, $lockTime);
        
        $this->logSecurityEvent('login_failure', [
            'identifier' => $identifier,
            'attempts' => $attempts,
            'ip' => Request::ip()
        ]);
    }

    /**
     * 清除登录失败记录
     */
    public function clearLoginAttempts(string $identifier): void
    {
        $cacheKey = "login_attempts:{$identifier}";
        Cache::delete($cacheKey);
    }

    /**
     * 验证谷歌验证器
     */
    public function verifyGoogleAuthenticator(string $secret, string $code, int $tolerance = 1): bool
    {
        $timeSlice = floor(time() / 30);
        
        for ($i = -$tolerance; $i <= $tolerance; $i++) {
            $calculatedCode = $this->getGoogleAuthenticatorCode($secret, $timeSlice + $i);
            if ($calculatedCode === $code) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 生成谷歌验证器密钥
     */
    public function generateGoogleAuthenticatorSecret(): string
    {
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        $secret = '';
        
        for ($i = 0; $i < 16; $i++) {
            $secret .= $chars[random_int(0, strlen($chars) - 1)];
        }
        
        return $secret;
    }

    /**
     * 获取谷歌验证器二维码URL
     */
    public function getGoogleAuthenticatorQrCodeUrl(string $secret, string $label, string $issuer = ''): string
    {
        $issuer = $issuer ?: config('app.name', 'Exchange');
        $url = "otpauth://totp/{$label}?secret={$secret}&issuer=" . urlencode($issuer);
        
        return "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=" . urlencode($url);
    }

    /**
     * 计算谷歌验证器代码
     */
    private function getGoogleAuthenticatorCode(string $secret, int $timeSlice): string
    {
        $secretKey = $this->base32Decode($secret);
        $time = pack('N*', 0) . pack('N*', $timeSlice);
        $hm = hash_hmac('sha1', $time, $secretKey, true);
        $offset = ord(substr($hm, -1)) & 0x0F;
        $hashpart = substr($hm, $offset, 4);
        $value = unpack('N', $hashpart);
        $value = $value[1];
        $value = $value & 0x7FFFFFFF;
        $modulo = pow(10, 6);
        
        return str_pad($value % $modulo, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Base32解码
     */
    private function base32Decode(string $secret): string
    {
        $base32chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        $base32charsArray = str_split($base32chars);
        $base32charsFlipped = array_flip($base32charsArray);
        
        $paddingCharCount = substr_count($secret, '=');
        $allowedValues = [6, 4, 3, 1, 0];
        
        if (!in_array($paddingCharCount, $allowedValues)) {
            return false;
        }
        
        for ($i = 0; $i < 4; $i++) {
            if ($paddingCharCount == $allowedValues[$i] &&
                substr($secret, -($allowedValues[$i])) != str_repeat('=', $allowedValues[$i])) {
                return false;
            }
        }
        
        $secret = str_replace('=', '', $secret);
        $secret = str_split($secret);
        $binaryString = '';
        
        for ($i = 0; $i < count($secret); $i = $i + 8) {
            $x = '';
            if (!in_array($secret[$i], $base32charsArray)) {
                return false;
            }
            for ($j = 0; $j < 8; $j++) {
                $x .= str_pad(base_convert(@$base32charsFlipped[@$secret[$i + $j]], 10, 2), 5, '0', STR_PAD_LEFT);
            }
            $eightBits = str_split($x, 8);
            for ($z = 0; $z < count($eightBits); $z++) {
                $binaryString .= (($y = chr(base_convert($eightBits[$z], 2, 10))) || ord($y) == 48) ? $y : '';
            }
        }
        
        return $binaryString;
    }

    /**
     * 设备指纹验证
     */
    public function verifyDeviceFingerprint(int $userId, string $fingerprint): bool
    {
        $cacheKey = "device_fingerprint:{$userId}";
        $storedFingerprint = Cache::get($cacheKey);
        
        if (!$storedFingerprint) {
            // 首次登录，记录设备指纹
            Cache::set($cacheKey, $fingerprint, 86400 * 30); // 30天
            return true;
        }
        
        if ($storedFingerprint !== $fingerprint) {
            $this->logSecurityEvent('device_fingerprint_mismatch', [
                'user_id' => $userId,
                'stored_fingerprint' => $storedFingerprint,
                'current_fingerprint' => $fingerprint
            ]);
            return false;
        }
        
        return true;
    }

    /**
     * 生成设备指纹
     */
    public function generateDeviceFingerprint(): string
    {
        $userAgent = Request::header('user-agent', '');
        $acceptLanguage = Request::header('accept-language', '');
        $acceptEncoding = Request::header('accept-encoding', '');
        $ip = Request::ip();
        
        $fingerprint = md5($userAgent . $acceptLanguage . $acceptEncoding . $ip);
        
        return $fingerprint;
    }

    /**
     * 检查可疑活动
     */
    public function checkSuspiciousActivity(int $userId, string $action, array $context = []): bool
    {
        $cacheKey = "suspicious_activity:{$userId}:{$action}";
        $activities = Cache::get($cacheKey, []);
        
        $currentTime = time();
        $activities[] = [
            'time' => $currentTime,
            'ip' => Request::ip(),
            'user_agent' => Request::header('user-agent', ''),
            'context' => $context
        ];
        
        // 保留最近1小时的活动记录
        $activities = array_filter($activities, function($activity) use ($currentTime) {
            return $currentTime - $activity['time'] <= 3600;
        });
        
        Cache::set($cacheKey, $activities, 3600);
        
        // 检查是否存在可疑模式
        if ($this->detectSuspiciousPattern($activities, $action)) {
            $this->logSecurityEvent('suspicious_activity_detected', [
                'user_id' => $userId,
                'action' => $action,
                'activities_count' => count($activities),
                'context' => $context
            ]);
            return true;
        }
        
        return false;
    }

    /**
     * 检测可疑模式
     */
    private function detectSuspiciousPattern(array $activities, string $action): bool
    {
        $count = count($activities);
        
        // 频率检查
        if ($count > 50) { // 1小时内超过50次操作
            return true;
        }
        
        // IP变化检查
        $ips = array_unique(array_column($activities, 'ip'));
        if (count($ips) > 5) { // 1小时内使用超过5个不同IP
            return true;
        }
        
        // 用户代理变化检查
        $userAgents = array_unique(array_column($activities, 'user_agent'));
        if (count($userAgents) > 3) { // 1小时内使用超过3个不同用户代理
            return true;
        }
        
        return false;
    }

    /**
     * 记录安全事件
     */
    private function logSecurityEvent(string $event, array $data = []): void
    {
        $logData = [
            'event' => $event,
            'data' => $data,
            'ip' => Request::ip(),
            'user_agent' => Request::header('user-agent', ''),
            'timestamp' => time(),
            'date' => date('Y-m-d H:i:s')
        ];
        
        Log::channel('security')->warning("Security Event: {$event}", $logData);
        
        // 可以在这里添加更多的安全事件处理逻辑
        // 例如发送邮件通知、触发告警等
    }

    /**
     * 验证请求签名
     */
    public function verifyRequestSignature(array $params, string $signature, string $secret): bool
    {
        // 移除签名参数
        unset($params['signature']);
        
        // 按键名排序
        ksort($params);
        
        // 构建查询字符串
        $queryString = http_build_query($params);
        
        // 计算签名
        $expectedSignature = hash_hmac('sha256', $queryString, $secret);
        
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * 生成API密钥对
     */
    public function generateApiKeyPair(): array
    {
        $apiKey = 'ak_' . bin2hex(random_bytes(16));
        $secretKey = bin2hex(random_bytes(32));
        
        return [
            'api_key' => $apiKey,
            'secret_key' => $secretKey
        ];
    }

    /**
     * 加密敏感数据
     */
    public function encryptSensitiveData(string $data, string $key = ''): string
    {
        $key = $key ?: config('app.key');
        $cipher = 'AES-256-CBC';
        $iv = random_bytes(openssl_cipher_iv_length($cipher));
        $encrypted = openssl_encrypt($data, $cipher, $key, 0, $iv);
        
        return base64_encode($iv . $encrypted);
    }

    /**
     * 解密敏感数据
     */
    public function decryptSensitiveData(string $encryptedData, string $key = ''): string
    {
        $key = $key ?: config('app.key');
        $cipher = 'AES-256-CBC';
        $data = base64_decode($encryptedData);
        $ivLength = openssl_cipher_iv_length($cipher);
        $iv = substr($data, 0, $ivLength);
        $encrypted = substr($data, $ivLength);
        
        return openssl_decrypt($encrypted, $cipher, $key, 0, $iv);
    }
}
