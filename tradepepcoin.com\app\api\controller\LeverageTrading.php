<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\BaseController;
use app\common\service\LeverageTradingService;
use app\common\model\LeverageOrder;
use think\facade\Request;
use think\facade\Validate;

/**
 * 杠杆交易API控制器
 */
class LeverageTrading extends BaseController
{
    protected $leverageService;

    public function initialize()
    {
        parent::initialize();
        $this->leverageService = new LeverageTradingService();
    }

    /**
     * 创建杠杆订单
     * POST /api/leverage/order
     */
    public function createOrder()
    {
        if (!Request::isPost()) {
            return $this->error('请求方式错误');
        }

        // 获取用户ID
        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $data = Request::post();
        $data['user_id'] = $userId;

        // 验证数据
        $validate = Validate::rule([
            'symbol' => 'require|alphaNum',
            'type' => 'require|in:1,2',
            'amount' => 'require|float|gt:0',
            'leverage' => 'require|float|between:1,100'
        ])->message([
            'symbol.require' => '交易对不能为空',
            'type.require' => '订单类型不能为空',
            'type.in' => '订单类型无效',
            'amount.require' => '交易数量不能为空',
            'amount.gt' => '交易数量必须大于0',
            'leverage.require' => '杠杆倍数不能为空',
            'leverage.between' => '杠杆倍数必须在1-100倍之间'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        $result = $this->leverageService->createLeverageOrder($data);

        return json($result);
    }

    /**
     * 平仓
     * POST /api/leverage/close
     */
    public function closePosition()
    {
        if (!Request::isPost()) {
            return $this->error('请求方式错误');
        }

        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $orderId = Request::post('order_id');
        $closeAmount = Request::post('close_amount/f', 0);

        if (empty($orderId)) {
            return $this->error('订单ID不能为空');
        }

        $result = $this->leverageService->closePosition($userId, $orderId, $closeAmount);

        return json($result);
    }

    /**
     * 获取用户持仓
     * GET /api/leverage/positions
     */
    public function getPositions()
    {
        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $symbol = Request::get('symbol', '');

        $result = $this->leverageService->getUserPositions($userId, $symbol);

        return json($result);
    }

    /**
     * 获取用户杠杆订单历史
     * GET /api/leverage/orders
     */
    public function getOrderHistory()
    {
        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $symbol = Request::get('symbol', '');
        $page = Request::get('page/d', 1);
        $limit = Request::get('limit/d', 20);

        $result = LeverageOrder::getUserOrderHistory($userId, $symbol, $page, $limit);

        return $this->success('获取成功', $result);
    }

    /**
     * 获取杠杆交易统计
     * GET /api/leverage/stats
     */
    public function getStats()
    {
        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('请先登录', 401);
        }

        $result = $this->leverageService->getUserLeverageStats($userId);

        return json($result);
    }

    /**
     * 获取杠杆交易配置
     * GET /api/leverage/config
     */
    public function getConfig()
    {
        $config = [
            'max_leverage' => 100,
            'min_leverage' => 1,
            'maintenance_margin_rate' => 0.5, // 维持保证金率 0.5%
            'supported_symbols' => [
                'BTCUSDT' => [
                    'max_leverage' => 100,
                    'min_amount' => 0.001,
                    'max_amount' => 1000
                ],
                'ETHUSDT' => [
                    'max_leverage' => 75,
                    'min_amount' => 0.01,
                    'max_amount' => 10000
                ],
                'LTCUSDT' => [
                    'max_leverage' => 50,
                    'min_amount' => 0.1,
                    'max_amount' => 10000
                ]
            ],
            'leverage_options' => [1, 2, 3, 5, 10, 20, 25, 50, 75, 100],
            'risk_warning' => '杠杆交易具有高风险，可能导致全部保证金损失，请谨慎操作。'
        ];

        return $this->success('获取成功', $config);
    }

    /**
     * 计算保证金需求
     * POST /api/leverage/calculate-margin
     */
    public function calculateMargin()
    {
        if (!Request::isPost()) {
            return $this->error('请求方式错误');
        }

        $symbol = Request::post('symbol', 'BTCUSDT');
        $amount = Request::post('amount/f', 0);
        $leverage = Request::post('leverage/f', 1);

        if ($amount <= 0 || $leverage <= 0) {
            return $this->error('参数错误');
        }

        try {
            // 获取当前价格
            $tradingPair = \app\common\model\TradingPair::getBySymbol($symbol);
            if (!$tradingPair) {
                return $this->error('交易对不存在');
            }

            $currentPrice = $tradingPair->getLatestPrice();
            $notionalValue = $amount * $currentPrice;
            $marginRequired = $notionalValue / $leverage;

            // 计算强平价格（估算）
            $maintenanceMarginRate = 0.005; // 0.5%
            $liquidationPriceLong = $currentPrice - (($marginRequired * (1 - $maintenanceMarginRate)) / $amount);
            $liquidationPriceShort = $currentPrice + (($marginRequired * (1 - $maintenanceMarginRate)) / $amount);

            return $this->success('计算成功', [
                'symbol' => $symbol,
                'amount' => $amount,
                'leverage' => $leverage,
                'current_price' => $currentPrice,
                'notional_value' => $notionalValue,
                'margin_required' => $marginRequired,
                'liquidation_price_long' => max(0, $liquidationPriceLong),
                'liquidation_price_short' => $liquidationPriceShort
            ]);

        } catch (\Exception $e) {
            return $this->error('计算失败：' . $e->getMessage());
        }
    }

    /**
     * 强制平仓（管理员功能）
     * POST /api/leverage/liquidate
     */
    public function liquidate()
    {
        if (!Request::isPost()) {
            return $this->error('请求方式错误');
        }

        // 这里应该检查管理员权限
        $adminId = $this->getAdminId();
        if (!$adminId) {
            return $this->error('无权限操作', 403);
        }

        $orderId = Request::post('order_id');
        if (empty($orderId)) {
            return $this->error('订单ID不能为空');
        }

        $result = $this->leverageService->liquidatePosition($orderId);

        return json($result);
    }

    /**
     * 批量检查强平（系统定时任务）
     * POST /api/leverage/check-liquidations
     */
    public function checkLiquidations()
    {
        if (!Request::isPost()) {
            return $this->error('请求方式错误');
        }

        $symbol = Request::post('symbol', 'BTCUSDT');

        $result = $this->leverageService->checkLiquidations($symbol);

        return json($result);
    }

    /**
     * 获取风险提示
     * GET /api/leverage/risk-warning
     */
    public function getRiskWarning()
    {
        $warnings = [
            '杠杆交易具有高风险，可能导致全部保证金损失。',
            '市场价格波动可能触发强制平仓，请密切关注保证金率。',
            '建议设置止盈止损，控制交易风险。',
            '请根据自身风险承受能力选择合适的杠杆倍数。',
            '强烈建议新手从低杠杆开始练习。'
        ];

        return $this->success('获取成功', [
            'warnings' => $warnings,
            'maintenance_margin_rate' => '0.5%',
            'max_leverage' => 100,
            'risk_level' => 'HIGH'
        ]);
    }

    /**
     * 获取当前用户ID
     */
    private function getUserId(): int
    {
        // 这里应该从JWT token或session中获取用户ID
        return Request::header('user-id', 0) ?: 1;
    }

    /**
     * 获取管理员ID
     */
    private function getAdminId(): int
    {
        // 这里应该从JWT token或session中获取管理员ID
        return Request::header('admin-id', 0) ?: 0;
    }
}
