<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 客服消息模型
 */
class CustomerMessage extends Model
{
    protected $name = 'ce_customer_messages';
    
    // 设置字段信息
    protected $schema = [
        'id'         => 'int',
        'user_id'    => 'int',
        'content'    => 'text',
        'type'       => 'string',
        'sender'     => 'string',
        'status'     => 'string',
        'created_at' => 'datetime'
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $updateTime = false; // 只有创建时间
    
    // 发送者常量
    const SENDER_USER = 'user';       // 用户发送
    const SENDER_SERVICE = 'service'; // 客服发送
    
    // 消息类型常量
    const TYPE_TEXT = 'text';   // 文本消息
    const TYPE_IMAGE = 'image'; // 图片消息
    const TYPE_FILE = 'file';   // 文件消息
    
    // 状态常量
    const STATUS_SENT = 'sent'; // 已发送
    const STATUS_READ = 'read'; // 已读
    
    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    
    /**
     * 获取发送者文本
     */
    public function getSenderTextAttr($value, $data)
    {
        $senders = [
            self::SENDER_USER => '用户',
            self::SENDER_SERVICE => '客服'
        ];
        return $senders[$data['sender']] ?? '未知';
    }
    
    /**
     * 获取消息类型文本
     */
    public function getTypeTextAttr($value, $data)
    {
        $types = [
            self::TYPE_TEXT => '文本',
            self::TYPE_IMAGE => '图片',
            self::TYPE_FILE => '文件'
        ];
        return $types[$data['type']] ?? '未知';
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $statuses = [
            self::STATUS_SENT => '已发送',
            self::STATUS_READ => '已读'
        ];
        return $statuses[$data['status']] ?? '未知';
    }
    
    /**
     * 获取用户的消息列表
     */
    public static function getUserMessages(int $userId, int $limit = 50)
    {
        return self::where('user_id', $userId)
                  ->order('created_at desc')
                  ->limit($limit)
                  ->select();
    }
    
    /**
     * 获取用户未读消息数量
     */
    public static function getUnreadCount(int $userId): int
    {
        return self::where('user_id', $userId)
                  ->where('sender', self::SENDER_SERVICE)
                  ->where('status', self::STATUS_SENT)
                  ->count();
    }
    
    /**
     * 标记消息为已读
     */
    public static function markAsRead(int $userId): bool
    {
        return self::where('user_id', $userId)
                  ->where('sender', self::SENDER_SERVICE)
                  ->where('status', self::STATUS_SENT)
                  ->update(['status' => self::STATUS_READ]) !== false;
    }
    
    /**
     * 创建用户消息
     */
    public static function createUserMessage(int $userId, string $content, string $type = self::TYPE_TEXT): self
    {
        return self::create([
            'user_id' => $userId,
            'content' => $content,
            'type' => $type,
            'sender' => self::SENDER_USER,
            'status' => self::STATUS_SENT,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 创建客服消息
     */
    public static function createServiceMessage(int $userId, string $content, string $type = self::TYPE_TEXT): self
    {
        return self::create([
            'user_id' => $userId,
            'content' => $content,
            'type' => $type,
            'sender' => self::SENDER_SERVICE,
            'status' => self::STATUS_SENT,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
}
