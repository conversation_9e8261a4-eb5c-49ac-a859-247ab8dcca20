/* 代理端客服管理JavaScript */

// 全局变量
let currentConversation = null;
let messages = [];
let currentPage = 1;
let isLoading = false;
let websocket = null;

// 加载对话列表
function loadConversations() {
    fetch('/agent/dashboard/get-conversations')
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            updateConversationList(data.data);
        }
    })
    .catch(error => {
        console.error('加载对话列表失败:', error);
    });
}

// 更新对话列表
function updateConversationList(conversations) {
    const container = document.getElementById('conversationList');
    if (!container) return;

    if (conversations.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">💬</div>
                <div class="empty-text">暂无对话</div>
            </div>
        `;
        return;
    }

    container.innerHTML = conversations.map(conversation => `
        <div class="conversation-item ${currentConversation && currentConversation.user_id == conversation.user_id ? 'active' : ''}" 
             data-user-id="${conversation.user_id}" 
             onclick="selectConversation(${conversation.user_id}, '${conversation.username}')">
            <div class="conversation-avatar">
                <img src="${conversation.avatar || '/static/images/default-avatar.png'}" alt="头像">
                ${conversation.unread_count > 0 ? `<span class="unread-badge">${conversation.unread_count}</span>` : ''}
            </div>
            <div class="conversation-content">
                <div class="conversation-header">
                    <span class="conversation-name">${conversation.username}</span>
                    <span class="conversation-time">${formatTime(conversation.last_message_time)}</span>
                </div>
                <div class="conversation-preview">
                    ${getMessagePreview(conversation.last_message_type, conversation.last_message)}
                </div>
            </div>
        </div>
    `).join('');
}

// 获取消息预览
function getMessagePreview(type, content) {
    switch (parseInt(type)) {
        case 1: return content || ''; // 文本
        case 2: return '[图片]';
        case 3: return '[视频]';
        case 4: return content || '[表情]';
        default: return '[消息]';
    }
}

// 选择对话
function selectConversation(userId, username) {
    currentConversation = { user_id: userId, username: username };
    currentPage = 1;
    messages = [];

    // 更新对话列表选中状态
    document.querySelectorAll('.conversation-item').forEach(item => {
        item.classList.remove('active');
        if (item.dataset.userId == userId) {
            item.classList.add('active');
        }
    });

    // 显示聊天界面
    showChatInterface(username);
    
    // 加载消息
    loadMessages(userId);
    
    // 标记为已读
    markConversationRead(userId);
}

// 显示聊天界面
function showChatInterface(username) {
    document.getElementById('chatEmpty').style.display = 'none';
    document.getElementById('chatHeader').style.display = 'flex';
    document.getElementById('chatMessages').style.display = 'block';
    document.getElementById('chatInputArea').style.display = 'block';
    
    document.getElementById('chatUserName').textContent = username;
}

// 加载消息
function loadMessages(userId) {
    if (isLoading) return;
    
    isLoading = true;
    showMessageLoading();

    fetch(`/agent/dashboard/get-conversation-messages?user_id=${userId}&page=${currentPage}`)
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            if (currentPage === 1) {
                messages = data.data.messages;
            } else {
                messages = [...data.data.messages, ...messages];
            }
            renderMessages();
            currentPage++;
        }
    })
    .catch(error => {
        console.error('加载消息失败:', error);
    })
    .finally(() => {
        isLoading = false;
        hideMessageLoading();
    });
}

// 渲染消息
function renderMessages() {
    const container = document.getElementById('chatMessages');
    const loadingEl = document.getElementById('messageLoading');
    
    container.innerHTML = '';
    container.appendChild(loadingEl);

    messages.forEach(message => {
        const messageEl = createMessageElement(message);
        container.insertBefore(messageEl, loadingEl);
    });

    scrollToBottom();
}

// 创建消息元素
function createMessageElement(message) {
    const div = document.createElement('div');
    const isOwn = message.sender_type == 2; // 代理商发送
    
    div.className = `message ${isOwn ? 'message-own' : 'message-other'}`;
    
    let content = '';
    switch (parseInt(message.type)) {
        case 1: // 文本
            content = `<div class="message-text">${escapeHtml(message.content)}</div>`;
            break;
        case 2: // 图片
            content = `<div class="message-image"><img src="${message.media_url}" alt="图片" onclick="showImagePreview('${message.media_url}')"></div>`;
            break;
        case 3: // 视频
            content = `<div class="message-video"><video src="${message.media_url}" controls></video></div>`;
            break;
        case 4: // 表情
            content = `<div class="message-emoji">${message.content}</div>`;
            break;
    }

    div.innerHTML = `
        <div class="message-avatar">
            ${isOwn ? '🎧' : '👤'}
        </div>
        <div class="message-content">
            <div class="message-info">
                <span class="message-sender">${message.sender_name || (isOwn ? '我' : message.username)}</span>
                <span class="message-time">${formatTime(message.created_at)}</span>
            </div>
            ${content}
        </div>
    `;

    return div;
}

// 发送消息
function sendMessage() {
    if (!currentConversation) {
        showNotification('请先选择一个对话', 'warning');
        return;
    }

    const input = document.getElementById('messageInput');
    const content = input.value.trim();
    
    if (!content) return;

    const messageData = {
        user_id: currentConversation.user_id,
        type: 1, // 文本消息
        content: content
    };

    fetch('/agent/dashboard/send-service-message', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(messageData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            input.value = '';
            addMessageToChat(data.data);
            scrollToBottom();
        } else {
            showNotification(data.msg || '发送失败', 'error');
        }
    })
    .catch(error => {
        console.error('发送消息失败:', error);
        showNotification('网络错误，请重试', 'error');
    });
}

// 添加消息到聊天
function addMessageToChat(message) {
    messages.push(message);
    const messageEl = createMessageElement(message);
    const container = document.getElementById('chatMessages');
    const loadingEl = document.getElementById('messageLoading');
    
    container.insertBefore(messageEl, loadingEl);
}

// 标记对话为已读
function markConversationRead(userId) {
    fetch('/agent/dashboard/mark-conversation-read', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ user_id: userId })
    })
    .catch(error => {
        console.error('标记已读失败:', error);
    });
}

// 处理文件上传
function handleFileUpload(file, type) {
    if (!file) return;
    if (!currentConversation) {
        showNotification('请先选择一个对话', 'warning');
        return;
    }

    // 检查文件大小
    const maxSize = type === 'image' ? 5 * 1024 * 1024 : 50 * 1024 * 1024;
    if (file.size > maxSize) {
        showNotification(`文件大小不能超过 ${maxSize / 1024 / 1024}MB`, 'error');
        return;
    }

    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    showUploadProgress();

    fetch('/user/customer-service/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            const messageData = {
                user_id: currentConversation.user_id,
                type: type === 'image' ? 2 : 3,
                content: file.name,
                media_url: data.data.url
            };

            return fetch('/agent/dashboard/send-service-message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(messageData)
            });
        } else {
            throw new Error(data.msg || '上传失败');
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            addMessageToChat(data.data);
            scrollToBottom();
        } else {
            showNotification(data.msg || '发送失败', 'error');
        }
    })
    .catch(error => {
        console.error('文件上传失败:', error);
        showNotification('上传失败，请重试', 'error');
    })
    .finally(() => {
        hideUploadProgress();
    });
}

// 选择图片
function selectImage() {
    document.getElementById('imageInput').click();
}

// 选择视频
function selectVideo() {
    document.getElementById('videoInput').click();
}

// 切换表情面板
function toggleEmojiPanel() {
    const panel = document.getElementById('emojiPanel');
    const isVisible = panel.style.display !== 'none';
    
    // 隐藏其他面板
    document.getElementById('quickRepliesPanel').style.display = 'none';
    
    panel.style.display = isVisible ? 'none' : 'block';
    
    if (!isVisible) {
        loadEmojiPanel();
    }
}

// 加载表情面板
function loadEmojiPanel() {
    const grid = document.querySelector('#emojiPanel .emoji-grid');
    const emojis = [
        '😊', '😂', '😍', '🤔', '😢', '😭', '😡', '😱', '🤗', '😴',
        '🙄', '😎', '🤩', '🥳', '😇', '🤪', '🤭', '🤫', '🤨', '😏',
        '👍', '👎', '👌', '✌️', '🤞', '🤝', '👏', '🙏', '💪', '🎉',
        '❤️', '💔', '💕', '💖', '💗', '💘', '💝', '💞', '💟', '💯'
    ];
    
    grid.innerHTML = emojis.map(emoji => 
        `<span class="emoji-item" onclick="insertEmoji('${emoji}')">${emoji}</span>`
    ).join('');
}

// 插入表情
function insertEmoji(emoji) {
    const input = document.getElementById('messageInput');
    const start = input.selectionStart;
    const end = input.selectionEnd;
    const text = input.value;
    
    input.value = text.substring(0, start) + emoji + text.substring(end);
    input.focus();
    input.setSelectionRange(start + emoji.length, start + emoji.length);
    
    toggleEmojiPanel();
}

// 显示快速回复
function showQuickReplies() {
    const panel = document.getElementById('quickRepliesPanel');
    const isVisible = panel.style.display !== 'none';
    
    // 隐藏其他面板
    document.getElementById('emojiPanel').style.display = 'none';
    
    panel.style.display = isVisible ? 'none' : 'block';
}

// 插入快速回复
function insertQuickReply(text) {
    document.getElementById('messageInput').value = text;
    showQuickReplies(); // 隐藏面板
}

// 清空对话
function clearChat() {
    if (!currentConversation) return;
    
    if (confirm('确定要清空当前对话吗？此操作不可恢复！')) {
        // 这里可以调用清空对话的API
        console.log('清空对话:', currentConversation.user_id);
    }
}

// 删除对话
function deleteConversation() {
    if (!currentConversation) return;
    
    if (confirm('确定要删除当前对话吗？此操作不可恢复！')) {
        fetch('/agent/dashboard/delete-conversation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ user_id: currentConversation.user_id })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 1) {
                showNotification('对话删除成功', 'success');
                loadConversations();
                // 重置聊天界面
                document.getElementById('chatEmpty').style.display = 'block';
                document.getElementById('chatHeader').style.display = 'none';
                document.getElementById('chatMessages').style.display = 'none';
                document.getElementById('chatInputArea').style.display = 'none';
                currentConversation = null;
            } else {
                showNotification(data.msg || '删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('删除对话失败:', error);
            showNotification('网络错误，请重试', 'error');
        });
    }
}

// 加载服务统计
function loadServiceStats() {
    fetch('/agent/dashboard/get-service-stats')
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            updateServiceStats(data.data);
        }
    })
    .catch(error => {
        console.error('加载服务统计失败:', error);
    });
}

// 更新服务统计
function updateServiceStats(stats) {
    const unreadCountEl = document.getElementById('unreadCount');
    if (unreadCountEl) {
        unreadCountEl.textContent = stats.unread_stats.total_unread;
    }
}

// 工具函数
function formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return '刚刚';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
    
    return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function scrollToBottom() {
    const container = document.getElementById('chatMessages');
    container.scrollTop = container.scrollHeight;
}

function showMessageLoading() {
    document.getElementById('messageLoading').style.display = 'flex';
}

function hideMessageLoading() {
    document.getElementById('messageLoading').style.display = 'none';
}

function showUploadProgress() {
    console.log('显示上传进度');
}

function hideUploadProgress() {
    console.log('隐藏上传进度');
}

function showImagePreview(url) {
    // 创建图片预览模态框
    const modal = document.createElement('div');
    modal.className = 'image-preview-modal';
    modal.innerHTML = `
        <div class="image-preview-backdrop" onclick="this.parentElement.remove()">
            <img src="${url}" alt="图片预览">
        </div>
    `;
    document.body.appendChild(modal);
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">×</button>
    `;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 16px;
        border-radius: 4px;
        color: white;
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 8px;
        background: ${type === 'success' ? '#0ECB81' : type === 'error' ? '#F6465D' : type === 'warning' ? '#FF8F00' : '#1890FF'};
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }
    }, 3000);
}
