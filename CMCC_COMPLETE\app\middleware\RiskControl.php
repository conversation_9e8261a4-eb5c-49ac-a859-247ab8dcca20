<?php
declare (strict_types = 1);

namespace app\middleware;

use Closure;
use think\Request;
use think\Response;
use think\facade\Log;
use app\common\service\RiskControlService;

/**
 * 风控中间件
 */
class RiskControl
{
    protected $riskControlService;

    public function __construct()
    {
        $this->riskControlService = new RiskControlService();
    }

    /**
     * 处理请求
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 获取用户ID
        $userId = $this->getUserId($request);
        if (!$userId) {
            return $next($request);
        }

        // 获取请求信息
        $ip = $request->ip();
        $userAgent = $request->header('user-agent', '');
        $action = $request->action();
        $controller = $request->controller();

        try {
            // 登录风控检测
            if ($controller === 'Auth' && $action === 'login') {
                $riskResult = $this->riskControlService->checkLoginRisk($userId, $ip, $userAgent);
                
                if ($riskResult['code'] === 1 && !$riskResult['data']['allow_login']) {
                    return $this->blockRequest($riskResult['data']);
                }
            }

            // 交易风控检测
            if (in_array($controller, ['SpotTrading', 'Leverage', 'Contract']) && 
                in_array($action, ['order', 'buy', 'sell', 'createOrder'])) {
                
                $tradeData = $request->post();
                if (!empty($tradeData)) {
                    $riskResult = $this->riskControlService->checkTradeRisk($userId, $tradeData);
                    
                    if ($riskResult['code'] === 1 && !$riskResult['data']['allow_trade']) {
                        return $this->blockRequest($riskResult['data']);
                    }
                }
            }

            // 提现风控检测
            if ($controller === 'Asset' && $action === 'withdraw') {
                $withdrawData = $request->post();
                if (!empty($withdrawData)) {
                    $riskResult = $this->checkWithdrawRisk($userId, $withdrawData, $ip);
                    
                    if ($riskResult['code'] === 1 && !$riskResult['data']['allow_withdraw']) {
                        return $this->blockRequest($riskResult['data']);
                    }
                }
            }

        } catch (\Exception $e) {
            Log::error('风控中间件执行失败：' . $e->getMessage());
        }

        return $next($request);
    }

    /**
     * 提现风控检测
     */
    private function checkWithdrawRisk(int $userId, array $withdrawData, string $ip): array
    {
        try {
            $riskScore = 0;
            $riskFactors = [];

            // 1. 提现金额检测
            $amount = floatval($withdrawData['amount'] ?? 0);
            if ($amount > 10000) {
                $riskScore += 30;
                $riskFactors[] = '大额提现';
            }

            // 2. 提现频率检测
            $recentWithdraws = \think\facade\Db::name('withdraw_records')
                ->where('user_id', $userId)
                ->where('created_at', '>=', date('Y-m-d H:i:s', time() - 3600))
                ->count();

            if ($recentWithdraws >= 3) {
                $riskScore += 25;
                $riskFactors[] = '提现频率过高';
            }

            // 3. 新地址检测
            $address = $withdrawData['address'] ?? '';
            $isNewAddress = !\think\facade\Db::name('withdraw_records')
                ->where('user_id', $userId)
                ->where('address', $address)
                ->where('status', 1)
                ->find();

            if ($isNewAddress) {
                $riskScore += 20;
                $riskFactors[] = '提现到新地址';
            }

            // 4. IP风险检测
            $ipRisk = $this->riskControlService->checkIpRisk($userId, $ip);
            $riskScore += $ipRisk['score'];
            if ($ipRisk['risk']) {
                $riskFactors[] = $ipRisk['reason'];
            }

            $riskLevel = $this->calculateRiskLevel($riskScore);
            $allowWithdraw = $riskScore < 50; // 风险分数低于50允许提现

            return [
                'code' => 1,
                'data' => [
                    'risk_score' => $riskScore,
                    'risk_level' => $riskLevel,
                    'risk_factors' => $riskFactors,
                    'allow_withdraw' => $allowWithdraw
                ]
            ];

        } catch (\Exception $e) {
            Log::error('提现风控检测失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '风控检测失败'];
        }
    }

    /**
     * 阻止请求
     */
    private function blockRequest(array $riskData): Response
    {
        $message = '操作被风控系统阻止';
        
        if (!empty($riskData['risk_factors'])) {
            $message .= '，原因：' . implode('、', $riskData['risk_factors']);
        }

        return json([
            'code' => 0,
            'msg' => $message,
            'data' => [
                'risk_score' => $riskData['risk_score'],
                'risk_level' => $riskData['risk_level'],
                'blocked_by_risk_control' => true
            ]
        ]);
    }

    /**
     * 获取用户ID
     */
    private function getUserId(Request $request): int
    {
        // 从JWT token中获取用户ID
        $token = $request->header('authorization');
        if (!$token) {
            return 0;
        }

        try {
            $token = str_replace('Bearer ', '', $token);
            // 这里应该解析JWT token获取用户ID
            // 简化处理，从session或其他方式获取
            return (int)session('user_id', 0);
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * 计算风险等级
     */
    private function calculateRiskLevel(int $score): int
    {
        if ($score >= 80) {
            return RiskControlService::RISK_CRITICAL;
        } elseif ($score >= 50) {
            return RiskControlService::RISK_HIGH;
        } elseif ($score >= 20) {
            return RiskControlService::RISK_MEDIUM;
        } else {
            return RiskControlService::RISK_LOW;
        }
    }
}
