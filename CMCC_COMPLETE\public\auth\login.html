<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - GVD交易平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 400px;
            max-width: 90%;
        }

        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .login-header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .login-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .login-form {
            padding: 40px 30px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .login-btn:hover {
            transform: translateY(-2px);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .form-links {
            text-align: center;
            margin-top: 25px;
        }

        .form-links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 10px;
        }

        .form-links a:hover {
            text-decoration: underline;
        }

        .error-message {
            background: #fee;
            color: #c33;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }

        .success-message {
            background: #efe;
            color: #3c3;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 10px;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #667eea;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>GVD交易平台</h1>
            <p>安全、专业的数字货币交易平台</p>
        </div>
        
        <div class="login-form">
            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>
            
            <form id="loginForm">
                <div class="form-group">
                    <label for="account">邮箱/用户名</label>
                    <input type="text" id="account" name="account" required>
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <button type="submit" class="login-btn" id="loginBtn">登录</button>
                <div class="loading" id="loading"></div>
            </form>
            
            <div class="form-links">
                <a href="register.html">注册账号</a>
                <a href="forgot-password.html">忘记密码</a>
            </div>
        </div>
    </div>

    <script>
        class LoginManager {
            constructor() {
                this.form = document.getElementById('loginForm');
                this.loginBtn = document.getElementById('loginBtn');
                this.loading = document.getElementById('loading');
                this.errorMessage = document.getElementById('errorMessage');
                this.successMessage = document.getElementById('successMessage');
                
                this.bindEvents();
            }
            
            bindEvents() {
                this.form.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleLogin();
                });
            }
            
            async handleLogin() {
                const formData = new FormData(this.form);
                const data = {
                    account: formData.get('account'),
                    password: formData.get('password')
                };
                
                if (!this.validateForm(data)) {
                    return;
                }
                
                this.setLoading(true);
                
                try {
                    const response = await fetch('/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(data)
                    });
                    
                    const result = await response.json();
                    
                    if (result.code === 1) {
                        this.showSuccess('登录成功，正在跳转...');
                        
                        // 保存token
                        localStorage.setItem('token', result.data.token);
                        localStorage.setItem('user_info', JSON.stringify(result.data));
                        
                        // 跳转到交易页面
                        setTimeout(() => {
                            window.location.href = '/trade/chart.html';
                        }, 1000);
                    } else {
                        this.showError(result.msg || '登录失败');
                    }
                } catch (error) {
                    console.error('登录请求失败:', error);
                    this.showError('网络错误，请稍后重试');
                } finally {
                    this.setLoading(false);
                }
            }
            
            validateForm(data) {
                if (!data.account) {
                    this.showError('请输入邮箱或用户名');
                    return false;
                }
                
                if (!data.password) {
                    this.showError('请输入密码');
                    return false;
                }
                
                return true;
            }
            
            setLoading(loading) {
                this.loginBtn.disabled = loading;
                this.loading.style.display = loading ? 'block' : 'none';
                this.loginBtn.textContent = loading ? '登录中...' : '登录';
            }
            
            showError(message) {
                this.errorMessage.textContent = message;
                this.errorMessage.style.display = 'block';
                this.successMessage.style.display = 'none';
            }
            
            showSuccess(message) {
                this.successMessage.textContent = message;
                this.successMessage.style.display = 'block';
                this.errorMessage.style.display = 'none';
            }
        }
        
        // 检查是否已登录
        function checkLoginStatus() {
            const token = localStorage.getItem('token');
            if (token) {
                // 验证token是否有效
                fetch('/api/auth/verify-token', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                }).then(response => response.json())
                  .then(result => {
                      if (result.code === 1) {
                          // token有效，跳转到交易页面
                          window.location.href = '/trade/chart.html';
                      }
                  });
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            checkLoginStatus();
            new LoginManager();
        });
    </script>
</body>
</html>
