<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\BaseController;
use think\facade\View;
use think\facade\Session;
use think\facade\Request;
use think\facade\Validate;
use think\facade\Db;

/**
 * 管理端认证控制器
 */
class Auth extends BaseController
{
    /**
     * 管理员登录
     */
    public function login()
    {
        if (Request::isPost()) {
            return $this->doLogin();
        }
        
        // 如果已登录，跳转到管理首页
        if (Session::has('admin_id')) {
            $this->redirect('/admin/');
        }
        
        View::assign([
            'title' => '管理员登录 - GVD管理后台'
        ]);
        
        return View::fetch('admin/auth/login');
    }
    
    /**
     * 执行登录
     */
    private function doLogin()
    {
        $data = Request::post();
        
        // 验证数据
        $validate = Validate::rule([
            'username' => 'require',
            'password' => 'require'
        ])->message([
            'username.require' => '请输入用户名',
            'password.require' => '请输入密码'
        ]);
        
        if (!$validate->check($data)) {
            return json(['code' => 0, 'msg' => $validate->getError()]);
        }
        
        // 查找管理员
        $admin = Db::name('admins')
                  ->where('username', $data['username'])
                  ->where('status', 1)
                  ->find();
        
        if (!$admin) {
            return json(['code' => 0, 'msg' => '用户名或密码错误']);
        }
        
        // 验证密码
        if (!password_verify($data['password'], $admin['password'])) {
            return json(['code' => 0, 'msg' => '用户名或密码错误']);
        }
        
        // 更新登录信息
        Db::name('admins')->where('id', $admin['id'])->update([
            'last_login_time' => date('Y-m-d H:i:s'),
            'last_login_ip' => Request::ip()
        ]);
        
        // 设置Session
        Session::set('admin_id', $admin['id']);
        Session::set('admin_username', $admin['username']);
        Session::set('admin_role', $admin['role']);
        
        return json([
            'code' => 1,
            'msg' => '登录成功',
            'data' => [
                'redirect' => '/admin/'
            ]
        ]);
    }
    
    /**
     * 退出登录
     */
    public function logout()
    {
        Session::clear();
        $this->redirect('/admin/login');
    }
}
