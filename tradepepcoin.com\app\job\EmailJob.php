<?php
declare (strict_types = 1);

namespace app\job;

use think\queue\Job;
use think\facade\Log;
use think\facade\View;

/**
 * 邮件发送任务
 */
class EmailJob
{
    /**
     * 执行任务
     */
    public function fire(Job $job, $data)
    {
        try {
            $to = $data['to'];
            $subject = $data['subject'];
            $template = $data['template'];
            $templateData = $data['data'] ?? [];

            // 渲染邮件模板
            $content = $this->renderEmailTemplate($template, $templateData);

            // 发送邮件
            $result = $this->sendEmail($to, $subject, $content);

            if ($result) {
                Log::info("邮件发送成功: {$to} - {$subject}");
                $job->delete();
            } else {
                Log::error("邮件发送失败: {$to} - {$subject}");
                $this->handleFailure($job, $data);
            }
        } catch (\Exception $e) {
            Log::error("邮件发送异常: " . $e->getMessage());
            $this->handleFailure($job, $data);
        }
    }

    /**
     * 渲染邮件模板
     */
    private function renderEmailTemplate(string $template, array $data): string
    {
        try {
            // 设置邮件模板路径
            $templatePath = "email/{$template}";
            
            // 渲染模板
            View::assign($data);
            return View::fetch($templatePath);
        } catch (\Exception $e) {
            Log::error("邮件模板渲染失败: " . $e->getMessage());
            return $this->getDefaultTemplate($data);
        }
    }

    /**
     * 发送邮件
     */
    private function sendEmail(string $to, string $subject, string $content): bool
    {
        try {
            // 邮件配置
            $config = config('email');
            
            if (empty($config['smtp_host'])) {
                Log::warning("邮件配置未设置，跳过发送");
                return true; // 开发环境可以返回true跳过实际发送
            }

            // 使用PHPMailer或其他邮件库发送
            return $this->sendWithPhpMailer($to, $subject, $content, $config);
        } catch (\Exception $e) {
            Log::error("邮件发送失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 使用PHPMailer发送邮件
     */
    private function sendWithPhpMailer(string $to, string $subject, string $content, array $config): bool
    {
        // 这里应该集成PHPMailer或其他邮件库
        // 简化处理，记录日志
        Log::info("模拟邮件发送", [
            'to' => $to,
            'subject' => $subject,
            'content_length' => strlen($content)
        ]);

        return true;
    }

    /**
     * 获取默认邮件模板
     */
    private function getDefaultTemplate(array $data): string
    {
        $siteName = config('app.name', '交易平台');
        $username = $data['username'] ?? '用户';
        
        return "
        <html>
        <head>
            <meta charset='utf-8'>
            <title>{$siteName}</title>
        </head>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;'>
                    <h1 style='color: #007bff; margin: 0;'>{$siteName}</h1>
                </div>
                
                <div style='background: white; padding: 30px; border: 1px solid #dee2e6; border-radius: 5px;'>
                    <h2>亲爱的 {$username}，</h2>
                    <p>这是来自{$siteName}的通知邮件。</p>
                    <p>如果您有任何疑问，请联系我们的客服团队。</p>
                </div>
                
                <div style='text-align: center; margin-top: 20px; color: #6c757d; font-size: 12px;'>
                    <p>此邮件由系统自动发送，请勿回复。</p>
                    <p>&copy; " . date('Y') . " {$siteName}. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        ";
    }

    /**
     * 处理失败
     */
    private function handleFailure(Job $job, array $data)
    {
        $attempts = $job->attempts();
        $maxAttempts = 3;

        if ($attempts < $maxAttempts) {
            // 重试，延迟时间递增
            $delay = $attempts * 60; // 1分钟、2分钟、3分钟
            $job->release($delay);
            Log::info("邮件任务重试，第{$attempts}次，延迟{$delay}秒");
        } else {
            // 超过最大重试次数，删除任务
            $job->delete();
            Log::error("邮件任务失败，已达到最大重试次数", $data);
        }
    }
}

/**
 * 短信发送任务
 */
class SmsJob
{
    /**
     * 执行任务
     */
    public function fire(Job $job, $data)
    {
        try {
            $phone = $data['phone'];
            $template = $data['template'];
            $templateData = $data['data'] ?? [];

            // 发送短信
            $result = $this->sendSms($phone, $template, $templateData);

            if ($result) {
                Log::info("短信发送成功: {$phone} - {$template}");
                $job->delete();
            } else {
                Log::error("短信发送失败: {$phone} - {$template}");
                $this->handleFailure($job, $data);
            }
        } catch (\Exception $e) {
            Log::error("短信发送异常: " . $e->getMessage());
            $this->handleFailure($job, $data);
        }
    }

    /**
     * 发送短信
     */
    private function sendSms(string $phone, string $template, array $data): bool
    {
        try {
            // 短信配置
            $config = config('sms');
            
            if (empty($config['access_key'])) {
                Log::warning("短信配置未设置，跳过发送");
                return true; // 开发环境可以返回true跳过实际发送
            }

            // 根据不同的短信服务商发送
            return $this->sendWithProvider($phone, $template, $data, $config);
        } catch (\Exception $e) {
            Log::error("短信发送失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 使用短信服务商发送
     */
    private function sendWithProvider(string $phone, string $template, array $data, array $config): bool
    {
        // 这里应该集成阿里云短信、腾讯云短信等服务
        // 简化处理，记录日志
        Log::info("模拟短信发送", [
            'phone' => $phone,
            'template' => $template,
            'data' => $data
        ]);

        return true;
    }

    /**
     * 处理失败
     */
    private function handleFailure(Job $job, array $data)
    {
        $attempts = $job->attempts();
        $maxAttempts = 3;

        if ($attempts < $maxAttempts) {
            // 重试，延迟时间递增
            $delay = $attempts * 30; // 30秒、60秒、90秒
            $job->release($delay);
            Log::info("短信任务重试，第{$attempts}次，延迟{$delay}秒");
        } else {
            // 超过最大重试次数，删除任务
            $job->delete();
            Log::error("短信任务失败，已达到最大重试次数", $data);
        }
    }
}
