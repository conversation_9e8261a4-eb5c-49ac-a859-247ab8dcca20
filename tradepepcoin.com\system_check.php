<?php
/**
 * GVD系统完整性检查脚本
 */

echo "=== GVD系统完整性检查 ===\n\n";

// 1. 检查核心控制器
echo "1. 检查核心控制器：\n";
$controllers = [
    'app/index/controller/Auth.php' => '用户认证控制器',
    'app/index/controller/Finance.php' => '财务控制器',
    'app/index/controller/Contract.php' => '合约控制器',
    'app/index/controller/CustomerService.php' => '客服控制器',
    'app/admin/controller/Auth.php' => '管理端认证控制器',
    'app/admin/controller/User.php' => '管理端用户控制器',
    'app/admin/controller/System.php' => '管理端系统控制器',
    'app/agent/controller/Auth.php' => '代理端认证控制器',
    'app/agent/controller/User.php' => '代理端用户控制器'
];

foreach ($controllers as $file => $desc) {
    if (file_exists($file)) {
        echo "✅ {$desc} - 存在\n";
        
        // 检查语法错误
        $output = shell_exec("php -l {$file} 2>&1");
        if (strpos($output, 'No syntax errors') !== false) {
            echo "   ✅ 语法检查通过\n";
        } else {
            echo "   ❌ 语法错误: {$output}\n";
        }
    } else {
        echo "❌ {$desc} - 不存在\n";
    }
}

echo "\n";

// 2. 检查服务类
echo "2. 检查服务类：\n";
$services = [
    'app/common/service/UserService.php' => '用户服务',
    'app/common/service/FinanceService.php' => '财务服务',
    'app/common/service/ContractService.php' => '合约服务',
    'app/common/service/EmailService.php' => '邮件服务',
    'app/common/service/SmsService.php' => '短信服务',
    'app/common/service/VerificationService.php' => '验证码服务'
];

foreach ($services as $file => $desc) {
    if (file_exists($file)) {
        echo "✅ {$desc} - 存在\n";
        
        // 检查语法错误
        $output = shell_exec("php -l {$file} 2>&1");
        if (strpos($output, 'No syntax errors') !== false) {
            echo "   ✅ 语法检查通过\n";
        } else {
            echo "   ❌ 语法错误: {$output}\n";
        }
    } else {
        echo "❌ {$desc} - 不存在\n";
    }
}

echo "\n";

// 3. 检查模型类
echo "3. 检查模型类：\n";
$models = [
    'app/common/model/User.php' => '用户模型',
    'app/common/model/UserAsset.php' => '用户资产模型',
    'app/common/model/ContractOrder.php' => '合约订单模型',
    'app/common/model/DepositRecord.php' => '充值记录模型',
    'app/common/model/WithdrawRecord.php' => '提现记录模型',
    'app/common/model/DepositAddress.php' => '充值地址模型',
    'app/common/model/CustomerMessage.php' => '客服消息模型'
];

foreach ($models as $file => $desc) {
    if (file_exists($file)) {
        echo "✅ {$desc} - 存在\n";
        
        // 检查语法错误
        $output = shell_exec("php -l {$file} 2>&1");
        if (strpos($output, 'No syntax errors') !== false) {
            echo "   ✅ 语法检查通过\n";
        } else {
            echo "   ❌ 语法错误: {$output}\n";
        }
    } else {
        echo "❌ {$desc} - 不存在\n";
    }
}

echo "\n";

// 4. 检查路由配置
echo "4. 检查路由配置：\n";
$routes = [
    'route/app.php' => '主路由',
    'route/admin.php' => '管理端路由',
    'route/agent.php' => '代理端路由'
];

foreach ($routes as $file => $desc) {
    if (file_exists($file)) {
        echo "✅ {$desc} - 存在\n";
        
        // 检查语法错误
        $output = shell_exec("php -l {$file} 2>&1");
        if (strpos($output, 'No syntax errors') !== false) {
            echo "   ✅ 语法检查通过\n";
        } else {
            echo "   ❌ 语法错误: {$output}\n";
        }
    } else {
        echo "❌ {$desc} - 不存在\n";
    }
}

echo "\n";

// 5. 检查前端模板
echo "5. 检查前端模板：\n";
$templates = [
    'view/index/auth/login.html' => '登录页面',
    'view/index/auth/register.html' => '注册页面',
    'view/index/finance/index.html' => '财务首页',
    'view/index/finance/deposit.html' => '充值页面',
    'view/index/contract/index.html' => '合约交易页面'
];

foreach ($templates as $file => $desc) {
    if (file_exists($file)) {
        echo "✅ {$desc} - 存在\n";
    } else {
        echo "❌ {$desc} - 不存在\n";
    }
}

echo "\n";

// 6. 检查配置文件
echo "6. 检查配置文件：\n";
$configs = [
    '.env' => '环境配置',
    'config/database.php' => '数据库配置',
    'config/app.php' => '应用配置'
];

foreach ($configs as $file => $desc) {
    if (file_exists($file)) {
        echo "✅ {$desc} - 存在\n";
    } else {
        echo "❌ {$desc} - 不存在\n";
    }
}

echo "\n";

// 7. 检查核心功能逻辑
echo "7. 检查核心功能逻辑：\n";

// 检查用户注册逻辑
if (file_exists('app/index/controller/Auth.php')) {
    $authContent = file_get_contents('app/index/controller/Auth.php');
    
    if (strpos($authContent, 'sendEmailCode') !== false) {
        echo "✅ 邮箱验证码发送 - 已实现\n";
    } else {
        echo "❌ 邮箱验证码发送 - 未实现\n";
    }
    
    if (strpos($authContent, 'sendSmsCode') !== false) {
        echo "✅ 短信验证码发送 - 已实现\n";
    } else {
        echo "❌ 短信验证码发送 - 未实现\n";
    }
    
    if (strpos($authContent, 'USER_TYPE_FORMAL') !== false) {
        echo "✅ 用户类型管理 - 已实现\n";
    } else {
        echo "❌ 用户类型管理 - 未实现\n";
    }
}

// 检查合约交易逻辑
if (file_exists('app/common/service/ContractService.php')) {
    $contractContent = file_get_contents('app/common/service/ContractService.php');
    
    if (strpos($contractContent, 'profit_rates') !== false) {
        echo "✅ 合约赔率配置 - 已实现\n";
    } else {
        echo "❌ 合约赔率配置 - 未实现\n";
    }
    
    if (strpos($contractContent, 'quick_amounts') !== false) {
        echo "✅ 快捷金额配置 - 已实现\n";
    } else {
        echo "❌ 快捷金额配置 - 未实现\n";
    }
}

// 检查财务功能
if (file_exists('app/common/service/FinanceService.php')) {
    $financeContent = file_get_contents('app/common/service/FinanceService.php');
    
    if (strpos($financeContent, 'getDepositAddress') !== false) {
        echo "✅ 充值地址生成 - 已实现\n";
    } else {
        echo "❌ 充值地址生成 - 未实现\n";
    }
    
    if (strpos($financeContent, 'submitWithdraw') !== false) {
        echo "✅ 提现申请处理 - 已实现\n";
    } else {
        echo "❌ 提现申请处理 - 未实现\n";
    }
}

echo "\n";

// 8. 检查依赖和扩展
echo "8. 检查PHP扩展：\n";
$extensions = [
    'curl' => 'CURL扩展（短信/邮件发送）',
    'openssl' => 'OpenSSL扩展（加密功能）',
    'pdo_mysql' => 'MySQL PDO扩展',
    'mbstring' => '多字节字符串扩展',
    'json' => 'JSON扩展'
];

foreach ($extensions as $ext => $desc) {
    if (extension_loaded($ext)) {
        echo "✅ {$desc} - 已安装\n";
    } else {
        echo "❌ {$desc} - 未安装\n";
    }
}

echo "\n";

// 9. 检查目录权限
echo "9. 检查目录权限：\n";
$directories = [
    'runtime' => '运行时目录',
    'public/storage' => '存储目录',
    'app/lang' => '语言包目录'
];

foreach ($directories as $dir => $desc) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "✅ {$desc} - 可写\n";
        } else {
            echo "⚠️ {$desc} - 不可写\n";
        }
    } else {
        echo "❌ {$desc} - 不存在\n";
    }
}

echo "\n=== 检查完成 ===\n";

// 总结
echo "\n系统状态总结：\n";
echo "- 如果所有项目都显示 ✅，说明系统完整\n";
echo "- 如果有 ❌ 项目，需要修复相应问题\n";
echo "- 如果有 ⚠️ 项目，建议检查但不影响运行\n";
echo "\n下一步：\n";
echo "1. 修复所有 ❌ 标记的问题\n";
echo "2. 运行数据库迁移脚本\n";
echo "3. 配置邮件和短信服务\n";
echo "4. 测试所有功能模块\n";
?>
