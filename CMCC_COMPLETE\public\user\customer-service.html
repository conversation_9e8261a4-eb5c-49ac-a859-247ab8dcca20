<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线客服 - GVD交易平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            height: 100vh;
            overflow: hidden;
        }

        .chat-container {
            display: flex;
            height: 100vh;
            background: white;
        }

        .chat-sidebar {
            width: 300px;
            background: #2c3e50;
            color: white;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #34495e;
        }

        .sidebar-header h2 {
            font-size: 18px;
            margin-bottom: 5px;
        }

        .online-status {
            font-size: 12px;
            color: #2ecc71;
        }

        .sidebar-content {
            flex: 1;
            padding: 20px;
        }

        .service-info {
            background: #34495e;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .service-info h4 {
            margin-bottom: 10px;
            color: #ecf0f1;
        }

        .service-info p {
            font-size: 14px;
            color: #bdc3c7;
            line-height: 1.5;
        }

        .quick-actions {
            margin-top: 20px;
        }

        .quick-actions h4 {
            margin-bottom: 15px;
            color: #ecf0f1;
        }

        .quick-btn {
            display: block;
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .quick-btn:hover {
            background: #2980b9;
        }

        .chat-main {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: white;
        }

        .chat-header h3 {
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .chat-status {
            color: #27ae60;
            font-size: 14px;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #3498db;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 10px;
        }

        .message.user .message-avatar {
            background: #2ecc71;
            margin-right: 0;
            margin-left: 10px;
            order: 2;
        }

        .message-content {
            max-width: 70%;
            background: white;
            padding: 12px 16px;
            border-radius: 18px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .message.user .message-content {
            background: #3498db;
            color: white;
        }

        .message-text {
            line-height: 1.5;
            word-wrap: break-word;
        }

        .message-time {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 5px;
        }

        .message.user .message-time {
            color: rgba(255, 255, 255, 0.8);
        }

        .message-image {
            max-width: 200px;
            border-radius: 8px;
            cursor: pointer;
        }

        .system-message {
            text-align: center;
            margin: 20px 0;
        }

        .system-message .message-content {
            background: #f39c12;
            color: white;
            display: inline-block;
            max-width: none;
        }

        .chat-input {
            padding: 20px;
            border-top: 1px solid #eee;
            background: white;
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .input-actions {
            display: flex;
            gap: 5px;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: #ecf0f1;
            color: #7f8c8d;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
        }

        .action-btn:hover {
            background: #bdc3c7;
        }

        .message-input {
            flex: 1;
            min-height: 40px;
            max-height: 120px;
            padding: 10px 15px;
            border: 2px solid #ecf0f1;
            border-radius: 20px;
            resize: none;
            font-family: inherit;
            font-size: 14px;
            line-height: 1.5;
            outline: none;
            transition: border-color 0.3s;
        }

        .message-input:focus {
            border-color: #3498db;
        }

        .send-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: #3498db;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s;
        }

        .send-btn:hover {
            background: #2980b9;
        }

        .send-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }

        .typing-indicator {
            padding: 10px 20px;
            color: #7f8c8d;
            font-style: italic;
            display: none;
        }

        .file-input {
            display: none;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
        }

        @media (max-width: 768px) {
            .chat-sidebar {
                display: none;
            }
            
            .message-content {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-sidebar">
            <div class="sidebar-header">
                <h2>在线客服</h2>
                <div class="online-status">● 客服在线</div>
            </div>
            <div class="sidebar-content">
                <div class="service-info">
                    <h4>服务时间</h4>
                    <p>周一至周日<br>9:00 - 21:00</p>
                </div>
                <div class="service-info">
                    <h4>联系方式</h4>
                    <p>邮箱：<EMAIL><br>电话：400-123-4567</p>
                </div>
                <div class="quick-actions">
                    <h4>常见问题</h4>
                    <button class="quick-btn" onclick="sendQuickMessage('如何充值？')">如何充值？</button>
                    <button class="quick-btn" onclick="sendQuickMessage('如何提现？')">如何提现？</button>
                    <button class="quick-btn" onclick="sendQuickMessage('交易手续费是多少？')">交易手续费</button>
                    <button class="quick-btn" onclick="sendQuickMessage('忘记密码怎么办？')">忘记密码</button>
                </div>
            </div>
        </div>

        <div class="chat-main">
            <div class="chat-header">
                <h3>客服支持</h3>
                <div class="chat-status">客服将尽快回复您的消息</div>
            </div>

            <div class="chat-messages" id="chatMessages">
                <div class="loading">加载聊天记录中...</div>
            </div>

            <div class="typing-indicator" id="typingIndicator">
                客服正在输入...
            </div>

            <div class="chat-input">
                <div class="input-container">
                    <div class="input-actions">
                        <button class="action-btn" onclick="selectImage()" title="发送图片">📷</button>
                        <button class="action-btn" onclick="selectFile()" title="发送文件">📎</button>
                    </div>
                    <textarea class="message-input" id="messageInput" placeholder="输入您的问题..." rows="1"></textarea>
                    <button class="send-btn" id="sendBtn" onclick="sendMessage()">➤</button>
                </div>
            </div>
        </div>
    </div>

    <input type="file" class="file-input" id="imageInput" accept="image/*" onchange="uploadImage()">
    <input type="file" class="file-input" id="fileInput" accept=".pdf,.doc,.docx,.txt,.zip,.rar" onchange="uploadFile()">

    <script>
        class CustomerServiceChat {
            constructor() {
                this.token = localStorage.getItem('token');
                this.messages = [];
                this.isLoading = false;
                
                if (!this.token) {
                    window.location.href = '/auth/login.html';
                    return;
                }
                
                this.init();
            }
            
            async init() {
                await this.initService();
                await this.loadMessages();
                this.bindEvents();
                this.scrollToBottom();
            }
            
            bindEvents() {
                const messageInput = document.getElementById('messageInput');
                const sendBtn = document.getElementById('sendBtn');
                
                messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });
                
                messageInput.addEventListener('input', () => {
                    this.autoResize(messageInput);
                    sendBtn.disabled = !messageInput.value.trim();
                });
                
                // 定期检查新消息
                setInterval(() => {
                    this.checkNewMessages();
                }, 5000);
            }
            
            autoResize(textarea) {
                textarea.style.height = 'auto';
                textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
            }
            
            async initService() {
                try {
                    await this.apiRequest('/api/customer-service/init', { method: 'POST' });
                } catch (error) {
                    console.error('初始化客服失败:', error);
                }
            }
            
            async loadMessages() {
                try {
                    const response = await this.apiRequest('/api/customer-service/messages');
                    if (response.code === 1) {
                        this.messages = response.data.messages;
                        this.renderMessages();
                    }
                } catch (error) {
                    console.error('加载消息失败:', error);
                    document.getElementById('chatMessages').innerHTML = '<div class="loading">加载失败</div>';
                }
            }
            
            renderMessages() {
                const chatMessages = document.getElementById('chatMessages');
                chatMessages.innerHTML = '';
                
                this.messages.forEach(message => {
                    const messageElement = this.createMessageElement(message);
                    chatMessages.appendChild(messageElement);
                });
                
                this.scrollToBottom();
            }
            
            createMessageElement(message) {
                const messageDiv = document.createElement('div');
                
                if (message.type === 4) { // 系统消息
                    messageDiv.className = 'system-message';
                    messageDiv.innerHTML = `
                        <div class="message-content">
                            <div class="message-text">${this.formatMessageContent(message)}</div>
                            <div class="message-time">${message.time_ago}</div>
                        </div>
                    `;
                } else {
                    messageDiv.className = `message ${message.is_admin ? 'admin' : 'user'}`;
                    messageDiv.innerHTML = `
                        <div class="message-avatar">${message.is_admin ? '客' : '我'}</div>
                        <div class="message-content">
                            <div class="message-text">${this.formatMessageContent(message)}</div>
                            <div class="message-time">${message.time_ago}</div>
                        </div>
                    `;
                }
                
                return messageDiv;
            }
            
            formatMessageContent(message) {
                if (message.type === 2) { // 图片消息
                    return `<img src="${message.content}" class="message-image" onclick="viewImage('${message.content}')" alt="图片">`;
                } else if (message.type === 3) { // 文件消息
                    try {
                        const fileInfo = JSON.parse(message.content);
                        return `<a href="${fileInfo.url}" target="_blank">📎 ${fileInfo.name}</a>`;
                    } catch (e) {
                        return `<a href="${message.content}" target="_blank">📎 文件</a>`;
                    }
                } else {
                    return message.content.replace(/\n/g, '<br>');
                }
            }
            
            async sendMessage() {
                const messageInput = document.getElementById('messageInput');
                const content = messageInput.value.trim();
                
                if (!content || this.isLoading) return;
                
                this.isLoading = true;
                document.getElementById('sendBtn').disabled = true;
                
                try {
                    const response = await this.apiRequest('/api/customer-service/send', {
                        method: 'POST',
                        body: JSON.stringify({
                            content: content,
                            type: 1
                        })
                    });
                    
                    if (response.code === 1) {
                        messageInput.value = '';
                        this.autoResize(messageInput);
                        this.messages.push(response.data);
                        this.renderMessages();
                    } else {
                        alert(response.msg || '发送失败');
                    }
                } catch (error) {
                    console.error('发送消息失败:', error);
                    alert('网络错误，请稍后重试');
                } finally {
                    this.isLoading = false;
                    document.getElementById('sendBtn').disabled = false;
                }
            }
            
            async checkNewMessages() {
                try {
                    const response = await this.apiRequest('/api/customer-service/messages');
                    if (response.code === 1 && response.data.messages.length > this.messages.length) {
                        this.messages = response.data.messages;
                        this.renderMessages();
                    }
                } catch (error) {
                    // 静默处理错误
                }
            }
            
            async uploadImage() {
                const fileInput = document.getElementById('imageInput');
                const file = fileInput.files[0];
                
                if (!file) return;
                
                const formData = new FormData();
                formData.append('image', file);
                
                try {
                    const response = await fetch('/api/customer-service/upload-image', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${this.token}`
                        },
                        body: formData
                    });
                    
                    const result = await response.json();
                    
                    if (result.code === 1) {
                        this.messages.push(result.data.message);
                        this.renderMessages();
                    } else {
                        alert(result.msg || '图片上传失败');
                    }
                } catch (error) {
                    console.error('上传图片失败:', error);
                    alert('网络错误，请稍后重试');
                } finally {
                    fileInput.value = '';
                }
            }
            
            async uploadFile() {
                const fileInput = document.getElementById('fileInput');
                const file = fileInput.files[0];
                
                if (!file) return;
                
                const formData = new FormData();
                formData.append('file', file);
                
                try {
                    const response = await fetch('/api/customer-service/upload-file', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${this.token}`
                        },
                        body: formData
                    });
                    
                    const result = await response.json();
                    
                    if (result.code === 1) {
                        this.messages.push(result.data.message);
                        this.renderMessages();
                    } else {
                        alert(result.msg || '文件上传失败');
                    }
                } catch (error) {
                    console.error('上传文件失败:', error);
                    alert('网络错误，请稍后重试');
                } finally {
                    fileInput.value = '';
                }
            }
            
            scrollToBottom() {
                const chatMessages = document.getElementById('chatMessages');
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
            
            async apiRequest(url, options = {}) {
                const defaultOptions = {
                    headers: {
                        'Authorization': `Bearer ${this.token}`,
                        'Content-Type': 'application/json'
                    }
                };
                
                const response = await fetch(url, { ...defaultOptions, ...options });
                const result = await response.json();
                
                if (response.status === 401) {
                    localStorage.removeItem('token');
                    localStorage.removeItem('user_info');
                    window.location.href = '/auth/login.html';
                    return;
                }
                
                return result;
            }
        }
        
        function sendQuickMessage(message) {
            document.getElementById('messageInput').value = message;
            customerServiceChat.sendMessage();
        }
        
        function selectImage() {
            document.getElementById('imageInput').click();
        }
        
        function selectFile() {
            document.getElementById('fileInput').click();
        }
        
        function viewImage(src) {
            window.open(src, '_blank');
        }
        
        let customerServiceChat;
        document.addEventListener('DOMContentLoaded', () => {
            customerServiceChat = new CustomerServiceChat();
        });
    </script>
</body>
</html>
