<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线客服系统演示</title>
    <link rel="stylesheet" href="/static/css/modern-theme.css">
    <link rel="stylesheet" href="/static/css/customer-service.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .demo-header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .demo-header p {
            color: #666;
            font-size: 16px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
        }
        
        .feature-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        .feature-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        
        .feature-desc {
            color: #666;
            line-height: 1.6;
        }
        
        .demo-actions {
            text-align: center;
            margin-top: 40px;
        }
        
        .demo-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .demo-note {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
            color: #1565c0;
        }
        
        .demo-note h3 {
            margin-top: 0;
            color: #0d47a1;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🎧 在线客服系统</h1>
            <p>为您的用户提供实时、专业的客服支持</p>
        </div>
        
        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-icon">💬</div>
                <div class="feature-title">实时聊天</div>
                <div class="feature-desc">
                    支持文本、表情、图片、视频等多种消息类型，提供丰富的沟通方式
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔔</div>
                <div class="feature-title">消息提醒</div>
                <div class="feature-desc">
                    新消息实时推送，声音和视觉双重提醒，确保不错过任何客户咨询
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">👥</div>
                <div class="feature-title">分级管理</div>
                <div class="feature-desc">
                    代理商管理下级用户，管理员查看全部对话，权限清晰分明
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📱</div>
                <div class="feature-title">响应式设计</div>
                <div class="feature-desc">
                    完美适配桌面和移动设备，随时随地提供客服支持
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <div class="feature-title">快速回复</div>
                <div class="feature-desc">
                    预设常用回复模板，提高客服响应效率和服务质量
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <div class="feature-title">数据统计</div>
                <div class="feature-desc">
                    详细的客服数据统计，帮助优化服务质量和效率
                </div>
            </div>
        </div>
        
        <div class="demo-actions">
            <button class="demo-btn" onclick="openCustomerService()">
                💬 体验客服功能
            </button>
            <button class="demo-btn" onclick="window.open('/agent/dashboard/customerService', '_blank')">
                🎧 代理端管理
            </button>
            <button class="demo-btn" onclick="window.open('/admin/system/customerService', '_blank')">
                ⚙️ 管理端后台
            </button>
        </div>
        
        <div class="demo-note">
            <h3>🚀 功能特色</h3>
            <ul>
                <li><strong>权限分级：</strong>代理A的邀请码注册的会员，代理A和管理员都能收到消息并回复</li>
                <li><strong>多媒体支持：</strong>支持文本、表情、图片、短视频等多种消息类型</li>
                <li><strong>实时通信：</strong>基于WebSocket的实时消息推送，响应迅速</li>
                <li><strong>移动优化：</strong>完美适配手机端，随时随地提供客服支持</li>
                <li><strong>数据统计：</strong>详细的客服数据分析，帮助提升服务质量</li>
            </ul>
        </div>
    </div>

    <!-- 客服系统 -->
    <script src="/static/js/customer-service.js"></script>
    
    <script>
        // 模拟用户登录状态
        window.mockUserSession = {
            user_id: 1001,
            username: 'demo_user',
            avatar: '/static/images/default-avatar.png'
        };
        
        // 打开客服窗口
        function openCustomerService() {
            if (window.customerService) {
                window.customerService.openWindow();
            } else {
                alert('客服系统正在初始化，请稍后再试');
            }
        }
        
        // 模拟消息推送
        function simulateMessage() {
            if (window.customerService) {
                const message = {
                    id: Date.now(),
                    sender_type: 2, // 代理商发送
                    sender_name: '客服小助手',
                    type: 1,
                    content: '您好！欢迎使用我们的客服系统，有什么可以帮助您的吗？',
                    created_at: new Date().toISOString()
                };
                window.customerService.handleNewMessage(message);
            }
        }
        
        // 页面加载完成后模拟一条消息
        setTimeout(() => {
            simulateMessage();
        }, 3000);
        
        // 演示通知权限
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    new Notification('客服系统', {
                        body: '通知权限已开启，您将收到新消息提醒',
                        icon: '/static/images/service-icon.png'
                    });
                }
            });
        }
    </script>
</body>
</html>
