/* 现代化交易平台主题 - 参考火币设计 */

/* ==================== 基础变量 ==================== */
:root {
  /* 深色主题 */
  --bg-primary: #0B0E11;
  --bg-secondary: #161A1E;
  --bg-tertiary: #1E2329;
  --bg-card: #1E2329;
  --bg-hover: #2B3139;
  
  /* 文字颜色 */
  --text-primary: #EAECEF;
  --text-secondary: #B7BDC6;
  --text-tertiary: #848E9C;
  --text-disabled: #5E6673;
  
  /* 品牌色 */
  --color-primary: #F0B90B;
  --color-primary-hover: #E6A800;
  --color-success: #0ECB81;
  --color-danger: #F6465D;
  --color-warning: #FF8F00;
  --color-info: #1890FF;
  
  /* 边框 */
  --border-color: #2B3139;
  --border-radius: 4px;
  --border-radius-lg: 8px;
  
  /* 阴影 */
  --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
  --shadow-md: 0 4px 12px rgba(0,0,0,0.15);
  --shadow-lg: 0 8px 24px rgba(0,0,0,0.2);
}

/* 亮色主题 */
[data-theme="light"] {
  --bg-primary: #FAFAFA;
  --bg-secondary: #FFFFFF;
  --bg-tertiary: #F5F5F5;
  --bg-card: #FFFFFF;
  --bg-hover: #F0F0F0;
  
  --text-primary: #212833;
  --text-secondary: #5E6673;
  --text-tertiary: #848E9C;
  --text-disabled: #B7BDC6;
  
  --border-color: #EAECEF;
}

/* ==================== 基础重置 ==================== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.5;
  font-size: 14px;
}

/* ==================== 布局组件 ==================== */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.container-fluid {
  width: 100%;
  padding: 0 16px;
}

/* 网格系统 */
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px;
}

.col {
  flex: 1;
  padding: 0 8px;
}

.col-1 { flex: 0 0 8.333333%; }
.col-2 { flex: 0 0 16.666667%; }
.col-3 { flex: 0 0 25%; }
.col-4 { flex: 0 0 33.333333%; }
.col-6 { flex: 0 0 50%; }
.col-8 { flex: 0 0 66.666667%; }
.col-9 { flex: 0 0 75%; }
.col-12 { flex: 0 0 100%; }

/* ==================== 卡片组件 ==================== */
.card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.card-body {
  padding: 20px;
}

.card-footer {
  padding: 16px 20px;
  border-top: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

/* ==================== 按钮组件 ==================== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 36px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--color-primary);
  color: #000;
}

.btn-primary:hover:not(:disabled) {
  background: var(--color-primary-hover);
}

.btn-success {
  background: var(--color-success);
  color: #fff;
}

.btn-danger {
  background: var(--color-danger);
  color: #fff;
}

.btn-outline {
  background: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-primary);
}

.btn-outline:hover:not(:disabled) {
  background: var(--bg-hover);
}

.btn-sm {
  padding: 4px 12px;
  font-size: 12px;
  min-height: 28px;
}

.btn-lg {
  padding: 12px 24px;
  font-size: 16px;
  min-height: 44px;
}

/* ==================== 表单组件 ==================== */
.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--color-primary);
}

.form-control::placeholder {
  color: var(--text-disabled);
}

/* ==================== 表格组件 ==================== */
.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.table th {
  background: var(--bg-secondary);
  font-weight: 600;
  font-size: 12px;
  color: var(--text-secondary);
  text-transform: uppercase;
}

.table tbody tr:hover {
  background: var(--bg-hover);
}

/* ==================== 导航组件 ==================== */
.navbar {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: 0;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.navbar-brand {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  font-size: 20px;
  font-weight: 700;
  color: var(--color-primary);
  text-decoration: none;
}

.navbar-nav {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  position: relative;
}

.nav-link {
  display: block;
  padding: 16px 20px;
  color: var(--text-secondary);
  text-decoration: none;
  transition: color 0.2s ease;
}

.nav-link:hover,
.nav-link.active {
  color: var(--text-primary);
}

/* ==================== 工具类 ==================== */
.text-primary { color: var(--text-primary) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-success { color: var(--color-success) !important; }
.text-danger { color: var(--color-danger) !important; }
.text-warning { color: var(--color-warning) !important; }

.bg-primary { background-color: var(--bg-primary) !important; }
.bg-secondary { background-color: var(--bg-secondary) !important; }
.bg-card { background-color: var(--bg-card) !important; }

.border { border: 1px solid var(--border-color) !important; }
.border-0 { border: none !important; }
.rounded { border-radius: var(--border-radius) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 4px !important; }
.p-2 { padding: 8px !important; }
.p-3 { padding: 16px !important; }
.p-4 { padding: 24px !important; }

.m-0 { margin: 0 !important; }
.m-1 { margin: 4px !important; }
.m-2 { margin: 8px !important; }
.m-3 { margin: 16px !important; }
.m-4 { margin: 24px !important; }

.mb-1 { margin-bottom: 4px !important; }
.mb-2 { margin-bottom: 8px !important; }
.mb-3 { margin-bottom: 16px !important; }
.mb-4 { margin-bottom: 24px !important; }

.d-flex { display: flex !important; }
.d-block { display: block !important; }
.d-none { display: none !important; }

.justify-content-between { justify-content: space-between !important; }
.justify-content-center { justify-content: center !important; }
.align-items-center { align-items: center !important; }

.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

.font-weight-bold { font-weight: 600 !important; }
.font-size-sm { font-size: 12px !important; }
.font-size-lg { font-size: 16px !important; }

/* ==================== 响应式 ==================== */
@media (max-width: 768px) {
  .container {
    padding: 0 12px;
  }
  
  .col-md-6 { flex: 0 0 50%; }
  .col-md-12 { flex: 0 0 100%; }
  
  .card-body {
    padding: 16px;
  }
  
  .table th,
  .table td {
    padding: 8px 12px;
  }
}
