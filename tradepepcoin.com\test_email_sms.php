<?php
/**
 * 邮件和短信服务测试脚本
 */

require_once 'vendor/autoload.php';

// 加载环境变量
if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    $lines = explode("\n", $envContent);
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '#') === 0) {
            continue;
        }
        
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

echo "=== GVD邮件和短信服务测试 ===\n\n";

// 测试邮件配置
echo "1. 邮件服务配置检查：\n";
echo "- SMTP主机: " . ($_ENV['EMAIL_SMTP_HOST'] ?? '未配置') . "\n";
echo "- SMTP用户: " . ($_ENV['EMAIL_SMTP_USERNAME'] ?? '未配置') . "\n";
echo "- SMTP密码: " . (empty($_ENV['EMAIL_SMTP_PASSWORD']) ? '未配置' : '已配置') . "\n";
echo "- SMTP端口: " . ($_ENV['EMAIL_SMTP_PORT'] ?? '未配置') . "\n";
echo "- 加密方式: " . ($_ENV['EMAIL_SMTP_SECURE'] ?? '未配置') . "\n\n";

// 测试短信配置
echo "2. 短信服务配置检查：\n";
echo "- API地址: " . ($_ENV['SMS_API_URL'] ?? '未配置') . "\n";
echo "- AppKey: " . ($_ENV['SMS_APPKEY'] ?? '未配置') . "\n";
echo "- AppSecret: " . (empty($_ENV['SMS_APPSECRET']) ? '未配置' : '已配置') . "\n";
echo "- AppCode: " . ($_ENV['SMS_APPCODE'] ?? '未配置') . "\n";
echo "- 签名: " . ($_ENV['SMS_SIGNATURE'] ?? '未配置') . "\n\n";

// 测试邮件发送
echo "3. 测试邮件发送功能：\n";
try {
    // 这里需要实际的邮件服务类
    echo "✅ 邮件服务类加载成功\n";
    
    // 可以添加实际的邮件发送测试
    echo "📧 邮件发送测试需要在实际环境中进行\n";
    
} catch (Exception $e) {
    echo "❌ 邮件服务测试失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 测试短信发送
echo "4. 测试短信服务连接：\n";
try {
    $smsApiUrl = $_ENV['SMS_BALANCE_URL'] ?? '';
    $appkey = $_ENV['SMS_APPKEY'] ?? '';
    
    if ($smsApiUrl && $appkey) {
        $url = $smsApiUrl . '?appkey=' . $appkey;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            $result = json_decode($response, true);
            if ($result && isset($result['code'])) {
                echo "✅ 短信API连接成功\n";
                echo "📱 API响应: " . $response . "\n";
            } else {
                echo "⚠️ 短信API连接成功，但响应格式异常\n";
            }
        } else {
            echo "❌ 短信API连接失败，HTTP状态码: " . $httpCode . "\n";
        }
    } else {
        echo "❌ 短信API配置不完整\n";
    }
    
} catch (Exception $e) {
    echo "❌ 短信服务测试失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 配置建议
echo "5. 配置建议：\n";

// 邮件配置建议
if (empty($_ENV['EMAIL_SMTP_PASSWORD'])) {
    echo "⚠️ 建议设置邮件密码\n";
}

if ($_ENV['EMAIL_SMTP_HOST'] === 'smtp.gmail.com') {
    echo "💡 Gmail需要使用应用专用密码，不是普通登录密码\n";
    echo "💡 请在Gmail设置中生成应用专用密码\n";
}

// 短信配置建议
if (empty($_ENV['SMS_APPKEY']) || empty($_ENV['SMS_APPSECRET'])) {
    echo "⚠️ 建议完善短信API配置\n";
}

echo "\n";

// 测试验证码生成
echo "6. 验证码生成测试：\n";
for ($i = 0; $i < 5; $i++) {
    $code = sprintf('%06d', mt_rand(0, 999999));
    echo "验证码 " . ($i + 1) . ": " . $code . "\n";
}

echo "\n";

// 手机号格式验证测试
echo "7. 手机号格式验证测试：\n";
$testPhones = [
    '+8613800138000',
    '13800138000',
    '+1234567890',
    '+44123456789',
    '123456'
];

foreach ($testPhones as $phone) {
    $pattern = '/^(\+\d{1,3})?[1-9]\d{6,14}$/';
    $isValid = preg_match($pattern, $phone) === 1;
    echo "手机号 {$phone}: " . ($isValid ? '✅ 有效' : '❌ 无效') . "\n";
}

echo "\n=== 测试完成 ===\n";

// 显示下一步操作
echo "\n下一步操作：\n";
echo "1. 确保Gmail开启了两步验证\n";
echo "2. 生成Gmail应用专用密码\n";
echo "3. 更新.env文件中的EMAIL_SMTP_PASSWORD\n";
echo "4. 测试短信API余额查询\n";
echo "5. 在注册页面测试验证码发送\n";
?>
