<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户中心 - GVD交易平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }

        .header {
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }

        .nav-menu {
            display: flex;
            gap: 30px;
        }

        .nav-menu a {
            text-decoration: none;
            color: #666;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-menu a:hover,
        .nav-menu a.active {
            color: #667eea;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: #667eea;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .logout-btn {
            background: #ff4757;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-card h3 {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .stat-card .value {
            font-size: 28px;
            font-weight: bold;
            color: #333;
        }

        .stat-card.primary .value {
            color: #667eea;
        }

        .stat-card.success .value {
            color: #2ed573;
        }

        .stat-card.warning .value {
            color: #ffa502;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }

        .card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .card-header {
            padding: 20px 25px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-header h2 {
            font-size: 18px;
            color: #333;
        }

        .card-body {
            padding: 25px;
        }

        .asset-list {
            list-style: none;
        }

        .asset-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .asset-item:last-child {
            border-bottom: none;
        }

        .asset-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .asset-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #667eea;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .asset-details h4 {
            margin-bottom: 5px;
            color: #333;
        }

        .asset-details p {
            color: #666;
            font-size: 14px;
        }

        .asset-balance {
            text-align: right;
        }

        .asset-balance .amount {
            font-weight: bold;
            color: #333;
        }

        .asset-balance .value {
            color: #666;
            font-size: 14px;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .action-btn {
            padding: 15px;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
            text-decoration: none;
            text-align: center;
            display: block;
        }

        .action-btn:hover {
            background: #667eea;
            color: white;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr 1fr;
            }
            
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-menu {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">GVD交易平台</div>
            <nav class="nav-menu">
                <a href="/trade/chart.html">交易</a>
                <a href="/user/dashboard.html" class="active">资产</a>
                <a href="/user/orders.html">订单</a>
                <a href="/user/profile.html">设置</a>
            </nav>
            <div class="user-info">
                <div class="user-avatar" id="userAvatar">U</div>
                <span id="username">用户</span>
                <button class="logout-btn" onclick="logout()">退出</button>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="dashboard-grid">
            <div class="stat-card primary">
                <h3>总资产价值</h3>
                <div class="value" id="totalValue">$0.00</div>
            </div>
            <div class="stat-card success">
                <h3>今日盈亏</h3>
                <div class="value" id="todayPnl">+$0.00</div>
            </div>
            <div class="stat-card warning">
                <h3>总订单数</h3>
                <div class="value" id="totalOrders">0</div>
            </div>
            <div class="stat-card">
                <h3>总交易量</h3>
                <div class="value" id="totalVolume">$0.00</div>
            </div>
        </div>

        <div class="content-grid">
            <div class="card">
                <div class="card-header">
                    <h2>我的资产</h2>
                    <button class="action-btn" onclick="refreshAssets()">刷新</button>
                </div>
                <div class="card-body">
                    <div class="loading" id="assetsLoading">加载中...</div>
                    <ul class="asset-list" id="assetList" style="display: none;"></ul>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2>快捷操作</h2>
                </div>
                <div class="card-body">
                    <div class="quick-actions">
                        <a href="/user/deposit.html" class="action-btn">充值</a>
                        <a href="/user/withdraw.html" class="action-btn">提现</a>
                        <a href="/trade/chart.html" class="action-btn">交易</a>
                        <a href="/user/transfer.html" class="action-btn">转账</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class Dashboard {
            constructor() {
                this.token = localStorage.getItem('token');
                this.userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
                
                if (!this.token) {
                    window.location.href = '/auth/login.html';
                    return;
                }
                
                this.init();
            }
            
            async init() {
                this.updateUserInfo();
                await this.loadUserStats();
                await this.loadUserAssets();
            }
            
            updateUserInfo() {
                if (this.userInfo.username) {
                    document.getElementById('username').textContent = this.userInfo.username;
                    document.getElementById('userAvatar').textContent = this.userInfo.username.charAt(0).toUpperCase();
                }
            }
            
            async loadUserStats() {
                try {
                    const response = await this.apiRequest('/api/auth/stats');
                    if (response.code === 1) {
                        const stats = response.data;
                        document.getElementById('totalValue').textContent = `$${stats.total_assets || 0}`;
                        document.getElementById('totalOrders').textContent = stats.total_orders || 0;
                        document.getElementById('totalVolume').textContent = `$${stats.total_volume || 0}`;
                    }
                } catch (error) {
                    console.error('加载用户统计失败:', error);
                }
            }
            
            async loadUserAssets() {
                try {
                    const response = await this.apiRequest('/api/asset/list');
                    if (response.code === 1) {
                        this.renderAssets(response.data.assets);
                        document.getElementById('totalValue').textContent = `$${response.data.total_value}`;
                    }
                } catch (error) {
                    console.error('加载资产失败:', error);
                } finally {
                    document.getElementById('assetsLoading').style.display = 'none';
                    document.getElementById('assetList').style.display = 'block';
                }
            }
            
            renderAssets(assets) {
                const assetList = document.getElementById('assetList');
                assetList.innerHTML = '';
                
                assets.forEach(asset => {
                    const li = document.createElement('li');
                    li.className = 'asset-item';
                    li.innerHTML = `
                        <div class="asset-info">
                            <div class="asset-icon">${asset.coin_symbol.charAt(0)}</div>
                            <div class="asset-details">
                                <h4>${asset.coin_symbol}</h4>
                                <p>${asset.coin_name}</p>
                            </div>
                        </div>
                        <div class="asset-balance">
                            <div class="amount">${asset.total}</div>
                            <div class="value">≈ $${asset.usdt_value}</div>
                        </div>
                    `;
                    assetList.appendChild(li);
                });
            }
            
            async apiRequest(url, options = {}) {
                const defaultOptions = {
                    headers: {
                        'Authorization': `Bearer ${this.token}`,
                        'Content-Type': 'application/json'
                    }
                };
                
                const response = await fetch(url, { ...defaultOptions, ...options });
                const result = await response.json();
                
                if (response.status === 401) {
                    localStorage.removeItem('token');
                    localStorage.removeItem('user_info');
                    window.location.href = '/auth/login.html';
                    return;
                }
                
                return result;
            }
        }
        
        function refreshAssets() {
            document.getElementById('assetsLoading').style.display = 'block';
            document.getElementById('assetList').style.display = 'none';
            dashboard.loadUserAssets();
        }
        
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('token');
                localStorage.removeItem('user_info');
                window.location.href = '/auth/login.html';
            }
        }
        
        let dashboard;
        document.addEventListener('DOMContentLoaded', () => {
            dashboard = new Dashboard();
        });
    </script>
</body>
</html>
