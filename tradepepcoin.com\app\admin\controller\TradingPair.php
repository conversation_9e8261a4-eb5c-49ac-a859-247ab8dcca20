<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\BaseController;
use app\common\model\TradingPair as TradingPairModel;
use app\common\model\Coin;
use think\facade\View;

/**
 * 管理后台交易对管理控制器
 */
class TradingPair extends BaseController
{
    /**
     * 交易对列表
     */
    public function index()
    {
        $keyword = input('keyword', '');
        $status = input('status', '');
        $page = input('page/d', 1);
        $limit = input('limit/d', 20);

        $where = [];
        
        if (!empty($keyword)) {
            $where[] = ['symbol|base_coin|quote_coin', 'like', "%{$keyword}%"];
        }
        
        if ($status !== '') {
            $where['status'] = $status;
        }

        $tradingPairs = TradingPairModel::where($where)
                                       ->with(['baseCoin', 'quoteCoin'])
                                       ->order('sort', 'asc')
                                       ->order('created_at', 'desc')
                                       ->paginate([
                                           'list_rows' => $limit,
                                           'page' => $page
                                       ]);

        if ($this->request->isAjax()) {
            return json([
                'code' => 1,
                'data' => $tradingPairs->items(),
                'total' => $tradingPairs->total(),
                'page' => $page,
                'limit' => $limit
            ]);
        }

        View::assign([
            'tradingPairs' => $tradingPairs,
            'keyword' => $keyword,
            'status' => $status,
            'title' => '交易对管理'
        ]);

        return View::fetch();
    }

    /**
     * 添加交易对
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();
            
            // 验证数据
            $validate = [
                'base_coin' => 'require',
                'quote_coin' => 'require',
                'price_precision' => 'require|integer|between:0,8',
                'amount_precision' => 'require|integer|between:0,8',
                'min_amount' => 'require|float|gt:0',
                'min_total' => 'require|float|gt:0',
                'maker_fee' => 'require|float|egt:0',
                'taker_fee' => 'require|float|egt:0'
            ];

            $result = $this->validate($data, $validate);
            if ($result !== true) {
                return json(['code' => 0, 'msg' => $result]);
            }

            // 验证币种是否存在
            $baseCoin = Coin::getBySymbol($data['base_coin']);
            $quoteCoin = Coin::getBySymbol($data['quote_coin']);
            
            if (!$baseCoin) {
                return json(['code' => 0, 'msg' => '基础币种不存在']);
            }
            
            if (!$quoteCoin) {
                return json(['code' => 0, 'msg' => '计价币种不存在']);
            }

            if ($data['base_coin'] == $data['quote_coin']) {
                return json(['code' => 0, 'msg' => '基础币种和计价币种不能相同']);
            }

            // 生成交易对符号
            $symbol = $data['base_coin'] . '/' . $data['quote_coin'];

            // 检查交易对是否存在
            if (TradingPairModel::where('symbol', $symbol)->count() > 0) {
                return json(['code' => 0, 'msg' => '交易对已存在']);
            }

            // 创建交易对
            $tradingPair = new TradingPairModel();
            $tradingPair->symbol = $symbol;
            $tradingPair->base_coin = $data['base_coin'];
            $tradingPair->quote_coin = $data['quote_coin'];
            $tradingPair->price_precision = $data['price_precision'];
            $tradingPair->amount_precision = $data['amount_precision'];
            $tradingPair->min_amount = $data['min_amount'];
            $tradingPair->max_amount = $data['max_amount'] ?? 0;
            $tradingPair->min_total = $data['min_total'];
            $tradingPair->maker_fee = $data['maker_fee'];
            $tradingPair->taker_fee = $data['taker_fee'];
            $tradingPair->current_price = $data['current_price'] ?? 0;
            $tradingPair->change_24h = 0;
            $tradingPair->volume_24h = 0;
            $tradingPair->high_24h = $data['current_price'] ?? 0;
            $tradingPair->low_24h = $data['current_price'] ?? 0;
            $tradingPair->sort = $data['sort'] ?? 0;
            $tradingPair->status = $data['status'] ?? 1;

            if ($tradingPair->save()) {
                return json(['code' => 1, 'msg' => '交易对添加成功']);
            } else {
                return json(['code' => 0, 'msg' => '交易对添加失败']);
            }
        }

        // 获取所有币种
        $coins = Coin::getEnabled();

        View::assign([
            'coins' => $coins,
            'title' => '添加交易对'
        ]);

        return View::fetch();
    }

    /**
     * 编辑交易对
     */
    public function edit()
    {
        $id = input('id/d', 0);
        $tradingPair = TradingPairModel::find($id);
        
        if (!$tradingPair) {
            $this->error('交易对不存在');
        }

        if ($this->request->isPost()) {
            $data = $this->request->post();
            
            // 验证数据
            $validate = [
                'price_precision' => 'require|integer|between:0,8',
                'amount_precision' => 'require|integer|between:0,8',
                'min_amount' => 'require|float|gt:0',
                'min_total' => 'require|float|gt:0',
                'maker_fee' => 'require|float|egt:0',
                'taker_fee' => 'require|float|egt:0'
            ];

            $result = $this->validate($data, $validate);
            if ($result !== true) {
                return json(['code' => 0, 'msg' => $result]);
            }

            // 更新交易对信息
            $tradingPair->price_precision = $data['price_precision'];
            $tradingPair->amount_precision = $data['amount_precision'];
            $tradingPair->min_amount = $data['min_amount'];
            $tradingPair->max_amount = $data['max_amount'] ?? 0;
            $tradingPair->min_total = $data['min_total'];
            $tradingPair->maker_fee = $data['maker_fee'];
            $tradingPair->taker_fee = $data['taker_fee'];
            $tradingPair->current_price = $data['current_price'] ?? $tradingPair->current_price;
            $tradingPair->sort = $data['sort'] ?? 0;
            $tradingPair->status = $data['status'] ?? 1;

            if ($tradingPair->save()) {
                return json(['code' => 1, 'msg' => '交易对更新成功']);
            } else {
                return json(['code' => 0, 'msg' => '交易对更新失败']);
            }
        }

        View::assign([
            'tradingPair' => $tradingPair,
            'title' => '编辑交易对'
        ]);

        return View::fetch();
    }

    /**
     * 删除交易对
     */
    public function delete()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $id = input('id/d', 0);
        $tradingPair = TradingPairModel::find($id);
        
        if (!$tradingPair) {
            return json(['code' => 0, 'msg' => '交易对不存在']);
        }

        // 检查是否有未完成的订单
        $hasOrders = \app\common\model\Order::where('symbol', $tradingPair->symbol)
                                           ->where('status', 'in', [0, 1])
                                           ->count() > 0;
        if ($hasOrders) {
            return json(['code' => 0, 'msg' => '该交易对有未完成的订单，无法删除']);
        }

        if ($tradingPair->delete()) {
            return json(['code' => 1, 'msg' => '交易对删除成功']);
        } else {
            return json(['code' => 0, 'msg' => '交易对删除失败']);
        }
    }

    /**
     * 修改交易对状态
     */
    public function status()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $id = input('id/d', 0);
        $status = input('status/d', 1);
        
        $tradingPair = TradingPairModel::find($id);
        if (!$tradingPair) {
            return json(['code' => 0, 'msg' => '交易对不存在']);
        }

        $tradingPair->status = $status;
        if ($tradingPair->save()) {
            $action = $status ? '启用' : '禁用';
            return json(['code' => 1, 'msg' => "交易对{$action}成功"]);
        } else {
            return json(['code' => 0, 'msg' => '操作失败']);
        }
    }

    /**
     * 更新市场数据
     */
    public function updateMarketData()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $id = input('id/d', 0);
        $data = $this->request->post();
        
        $tradingPair = TradingPairModel::find($id);
        if (!$tradingPair) {
            return json(['code' => 0, 'msg' => '交易对不存在']);
        }

        // 验证数据
        $validate = [
            'current_price' => 'require|float|gt:0',
            'change_24h' => 'require|float',
            'volume_24h' => 'require|float|egt:0',
            'high_24h' => 'require|float|gt:0',
            'low_24h' => 'require|float|gt:0'
        ];

        $result = $this->validate($data, $validate);
        if ($result !== true) {
            return json(['code' => 0, 'msg' => $result]);
        }

        // 更新市场数据
        $updateData = [
            'current_price' => $data['current_price'],
            'change_24h' => $data['change_24h'],
            'volume_24h' => $data['volume_24h'],
            'high_24h' => $data['high_24h'],
            'low_24h' => $data['low_24h']
        ];

        if ($tradingPair->updateMarketData($updateData)) {
            return json(['code' => 1, 'msg' => '市场数据更新成功']);
        } else {
            return json(['code' => 0, 'msg' => '市场数据更新失败']);
        }
    }

    /**
     * 批量更新排序
     */
    public function updateSort()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $data = input('data/a', []);
        
        if (empty($data)) {
            return json(['code' => 0, 'msg' => '数据不能为空']);
        }

        try {
            foreach ($data as $item) {
                if (isset($item['id']) && isset($item['sort'])) {
                    TradingPairModel::where('id', $item['id'])->update(['sort' => $item['sort']]);
                }
            }
            
            return json(['code' => 1, 'msg' => '排序更新成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '排序更新失败：' . $e->getMessage()]);
        }
    }
}
