<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\common\service\AiTradingService;
use think\Request;
use think\Response;

/**
 * AI智能交易API控制器
 */
class Ai
{
    protected $aiService;

    public function __construct()
    {
        $this->aiService = new AiTradingService();
    }

    /**
     * 创建AI策略
     */
    public function createStrategy(Request $request): Response
    {
        $userId = $request->user_id ?? 0;
        $data = $request->post();

        $result = $this->aiService->createAiStrategy($userId, $data);
        
        return json($result);
    }

    /**
     * 生成市场预测
     */
    public function generatePrediction(Request $request): Response
    {
        $symbol = $request->param('symbol');
        $predictionType = $request->param('prediction_type', 'price');

        $result = $this->aiService->generatePrediction($symbol, $predictionType);
        
        return json($result);
    }

    /**
     * 获取智能投顾建议
     */
    public function getInvestmentAdvice(Request $request): Response
    {
        $userId = $request->user_id ?? 0;
        $preferences = $request->post();

        $result = $this->aiService->getInvestmentAdvice($userId, $preferences);
        
        return json($result);
    }

    /**
     * 风险预警
     */
    public function riskAlert(Request $request): Response
    {
        $userId = $request->user_id ?? 0;

        $result = $this->aiService->riskAlert($userId);
        
        return json($result);
    }

    /**
     * 执行AI交易
     */
    public function executeAiTrade(Request $request): Response
    {
        $strategyId = $request->param('strategy_id');

        $result = $this->aiService->executeAiTrade($strategyId);
        
        return json($result);
    }

    /**
     * 获取AI策略列表
     */
    public function getStrategies(Request $request): Response
    {
        $userId = $request->user_id ?? 0;
        $filters = $request->get();

        $result = $this->aiService->getAiStrategies($userId, $filters);
        
        return json($result);
    }
}
