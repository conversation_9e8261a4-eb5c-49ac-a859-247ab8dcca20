<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

/**
 * 杠杆交易服务类
 */
class LeverageService
{
    /**
     * 创建杠杆订单
     */
    public function createOrder(array $data): array
    {
        try {
            Db::startTrans();

            // 验证用户资产
            $userAsset = $this->getUserAsset($data['user_id'], 'USDT');
            if ($userAsset['available'] < $data['margin']) {
                throw new \Exception('保证金不足');
            }

            // 获取当前价格
            $currentPrice = $this->getCurrentPrice($data['symbol']);
            
            // 计算交易数量
            $quantity = ($data['margin'] * $data['leverage']) / $currentPrice;
            
            // 计算强平价格
            $liquidationPrice = $this->calculateLiquidationPrice(
                $currentPrice, 
                $data['order_type'], 
                $data['leverage']
            );

            // 创建订单
            $orderData = [
                'user_id' => $data['user_id'],
                'symbol' => $data['symbol'],
                'order_type' => $data['order_type'], // 1做多 2做空
                'margin' => $data['margin'],
                'leverage' => $data['leverage'],
                'quantity' => $quantity,
                'open_price' => $currentPrice,
                'liquidation_price' => $liquidationPrice,
                'stop_loss_price' => $data['stop_loss_price'] ?? 0,
                'take_profit_price' => $data['take_profit_price'] ?? 0,
                'status' => 'open',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $orderId = Db::name('leverage_orders')->insertGetId($orderData);

            // 冻结保证金
            $this->freezeAsset($data['user_id'], 'USDT', $data['margin']);

            // 记录财务流水
            $this->recordFinancialLog($data['user_id'], 'USDT', -$data['margin'], 'leverage_open', '开仓冻结保证金', $orderId);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '订单创建成功',
                'data' => [
                    'order_id' => $orderId,
                    'open_price' => $currentPrice,
                    'quantity' => $quantity,
                    'liquidation_price' => $liquidationPrice
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('创建杠杆订单失败: ' . $e->getMessage());
            return [
                'code' => 0,
                'msg' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 平仓订单
     */
    public function closeOrder(int $orderId, int $userId): array
    {
        try {
            Db::startTrans();

            // 获取订单信息
            $order = Db::name('leverage_orders')
                ->where('id', $orderId)
                ->where('user_id', $userId)
                ->where('status', 'open')
                ->find();

            if (!$order) {
                throw new \Exception('订单不存在或已平仓');
            }

            // 获取当前价格
            $currentPrice = $this->getCurrentPrice($order['symbol']);

            // 计算盈亏
            $profitLoss = $this->calculateProfitLoss($order, $currentPrice);

            // 更新订单状态
            Db::name('leverage_orders')->where('id', $orderId)->update([
                'close_price' => $currentPrice,
                'profit_loss' => $profitLoss,
                'status' => 'closed',
                'close_type' => 'manual',
                'closed_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // 解冻保证金并结算盈亏
            $finalAmount = $order['margin'] + $profitLoss;
            $this->unfreezeAsset($userId, 'USDT', $order['margin']);
            
            if ($profitLoss != 0) {
                if ($profitLoss > 0) {
                    $this->addAsset($userId, 'USDT', $profitLoss);
                    $this->recordFinancialLog($userId, 'USDT', $profitLoss, 'leverage_profit', '杠杆交易盈利', $orderId);
                } else {
                    $this->subAsset($userId, 'USDT', abs($profitLoss));
                    $this->recordFinancialLog($userId, 'USDT', $profitLoss, 'leverage_loss', '杠杆交易亏损', $orderId);
                }
            }

            $this->recordFinancialLog($userId, 'USDT', $order['margin'], 'leverage_close', '平仓解冻保证金', $orderId);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '平仓成功',
                'data' => [
                    'close_price' => $currentPrice,
                    'profit_loss' => $profitLoss,
                    'final_amount' => $finalAmount
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('平仓失败: ' . $e->getMessage());
            return [
                'code' => 0,
                'msg' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 获取用户订单列表
     */
    public function getUserOrders(int $userId, array $params = []): array
    {
        $query = Db::name('leverage_orders')->where('user_id', $userId);

        // 状态筛选
        if (!empty($params['status'])) {
            $query->where('status', $params['status']);
        }

        // 交易对筛选
        if (!empty($params['symbol'])) {
            $query->where('symbol', $params['symbol']);
        }

        // 分页
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 20;
        $offset = ($page - 1) * $limit;

        $total = $query->count();
        $orders = $query->order('id desc')
            ->limit($offset, $limit)
            ->select()
            ->toArray();

        // 计算实时盈亏
        foreach ($orders as &$order) {
            if ($order['status'] === 'open') {
                $currentPrice = $this->getCurrentPrice($order['symbol']);
                $order['current_price'] = $currentPrice;
                $order['unrealized_pnl'] = $this->calculateProfitLoss($order, $currentPrice);
            }
        }

        return [
            'code' => 1,
            'msg' => '获取成功',
            'data' => [
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'orders' => $orders
            ]
        ];
    }

    /**
     * 强制平仓检查（包含止盈止损）
     */
    public function checkLiquidation(): void
    {
        $openOrders = Db::name('leverage_orders')
            ->where('status', 'open')
            ->select();

        foreach ($openOrders as $order) {
            $currentPrice = $this->getCurrentPrice($order['symbol']);

            // 检查是否触发强平
            if ($this->shouldLiquidate($order, $currentPrice)) {
                $this->liquidateOrder($order, $currentPrice);
                continue;
            }

            // 检查止盈止损
            if ($this->shouldStopLoss($order, $currentPrice)) {
                $this->stopLossOrder($order, $currentPrice);
                continue;
            }

            if ($this->shouldTakeProfit($order, $currentPrice)) {
                $this->takeProfitOrder($order, $currentPrice);
                continue;
            }
        }
    }

    /**
     * 设置止盈止损
     */
    public function setStopLossTakeProfit(int $orderId, int $userId, float $stopLoss = 0, float $takeProfit = 0): array
    {
        try {
            $order = Db::name('leverage_orders')
                ->where('id', $orderId)
                ->where('user_id', $userId)
                ->where('status', 'open')
                ->find();

            if (!$order) {
                throw new \Exception('订单不存在或已平仓');
            }

            // 验证止盈止损价格
            if ($stopLoss > 0) {
                if ($order['order_type'] == 1 && $stopLoss >= $order['open_price']) {
                    throw new \Exception('做多止损价格必须低于开仓价格');
                }
                if ($order['order_type'] == 2 && $stopLoss <= $order['open_price']) {
                    throw new \Exception('做空止损价格必须高于开仓价格');
                }
            }

            if ($takeProfit > 0) {
                if ($order['order_type'] == 1 && $takeProfit <= $order['open_price']) {
                    throw new \Exception('做多止盈价格必须高于开仓价格');
                }
                if ($order['order_type'] == 2 && $takeProfit >= $order['open_price']) {
                    throw new \Exception('做空止盈价格必须低于开仓价格');
                }
            }

            // 更新订单
            Db::name('leverage_orders')->where('id', $orderId)->update([
                'stop_loss_price' => $stopLoss,
                'take_profit_price' => $takeProfit,
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            return [
                'code' => 1,
                'msg' => '止盈止损设置成功',
                'data' => [
                    'stop_loss_price' => $stopLoss,
                    'take_profit_price' => $takeProfit
                ]
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 计算强平价格
     */
    private function calculateLiquidationPrice(float $openPrice, int $orderType, int $leverage): float
    {
        $marginRate = 0.05; // 5%维持保证金率
        
        if ($orderType == 1) { // 做多
            return $openPrice * (1 - (1 / $leverage) + $marginRate);
        } else { // 做空
            return $openPrice * (1 + (1 / $leverage) - $marginRate);
        }
    }

    /**
     * 计算盈亏
     */
    private function calculateProfitLoss(array $order, float $currentPrice): float
    {
        $priceDiff = $currentPrice - $order['open_price'];
        
        if ($order['order_type'] == 2) { // 做空
            $priceDiff = -$priceDiff;
        }
        
        return ($priceDiff / $order['open_price']) * $order['margin'] * $order['leverage'];
    }

    /**
     * 获取当前价格
     */
    private function getCurrentPrice(string $symbol): float
    {
        $cacheKey = 'price:' . $symbol;
        $price = Cache::get($cacheKey);
        
        if (!$price) {
            // 从数据库或API获取价格
            $priceData = Db::name('market_prices')
                ->where('symbol', $symbol)
                ->order('id desc')
                ->find();
            
            $price = $priceData ? $priceData['price'] : 50000; // 默认价格
            Cache::set($cacheKey, $price, 10); // 缓存10秒
        }
        
        return (float)$price;
    }

    /**
     * 获取用户资产
     */
    private function getUserAsset(int $userId, string $coinSymbol): array
    {
        return Db::name('user_assets')
            ->where('user_id', $userId)
            ->where('coin_symbol', $coinSymbol)
            ->find() ?: ['available' => 0, 'frozen' => 0];
    }

    /**
     * 冻结资产
     */
    private function freezeAsset(int $userId, string $coinSymbol, float $amount): void
    {
        Db::name('user_assets')
            ->where('user_id', $userId)
            ->where('coin_symbol', $coinSymbol)
            ->dec('available', $amount)
            ->inc('frozen', $amount);
    }

    /**
     * 解冻资产
     */
    private function unfreezeAsset(int $userId, string $coinSymbol, float $amount): void
    {
        Db::name('user_assets')
            ->where('user_id', $userId)
            ->where('coin_symbol', $coinSymbol)
            ->dec('frozen', $amount)
            ->inc('available', $amount);
    }

    /**
     * 增加资产
     */
    private function addAsset(int $userId, string $coinSymbol, float $amount): void
    {
        Db::name('user_assets')
            ->where('user_id', $userId)
            ->where('coin_symbol', $coinSymbol)
            ->inc('available', $amount);
    }

    /**
     * 减少资产
     */
    private function subAsset(int $userId, string $coinSymbol, float $amount): void
    {
        Db::name('user_assets')
            ->where('user_id', $userId)
            ->where('coin_symbol', $coinSymbol)
            ->dec('available', $amount);
    }

    /**
     * 记录财务日志
     */
    private function recordFinancialLog(int $userId, string $coinSymbol, float $amount, string $type, string $remark, int $orderId): void
    {
        Db::name('financial_records')->insert([
            'user_id' => $userId,
            'coin_symbol' => $coinSymbol,
            'amount' => $amount,
            'type' => $type,
            'remark' => $remark,
            'order_id' => $orderId,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 检查是否应该强平
     */
    private function shouldLiquidate(array $order, float $currentPrice): bool
    {
        if ($order['order_type'] == 1) { // 做多
            return $currentPrice <= $order['liquidation_price'];
        } else { // 做空
            return $currentPrice >= $order['liquidation_price'];
        }
    }

    /**
     * 执行强平
     */
    private function liquidateOrder(array $order, float $currentPrice): void
    {
        try {
            Db::startTrans();

            // 计算强平损失
            $loss = $order['margin'] * 0.95; // 保留5%作为强平费用

            // 更新订单状态
            Db::name('leverage_orders')->where('id', $order['id'])->update([
                'close_price' => $currentPrice,
                'profit_loss' => -$loss,
                'status' => 'liquidated',
                'close_type' => 'liquidation',
                'closed_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // 解冻保证金（扣除损失）
            $this->unfreezeAsset($order['user_id'], 'USDT', $order['margin'] - $loss);

            // 记录强平日志
            $this->recordFinancialLog($order['user_id'], 'USDT', -$loss, 'liquidation', '强制平仓', $order['id']);

            Db::commit();

            Log::info('强制平仓执行成功', ['order_id' => $order['id'], 'user_id' => $order['user_id']]);

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('强制平仓失败: ' . $e->getMessage());
        }
    }

    /**
     * 检查是否触发止损
     */
    private function shouldStopLoss(array $order, float $currentPrice): bool
    {
        if ($order['stop_loss_price'] <= 0) {
            return false;
        }

        if ($order['order_type'] == 1) { // 做多
            return $currentPrice <= $order['stop_loss_price'];
        } else { // 做空
            return $currentPrice >= $order['stop_loss_price'];
        }
    }

    /**
     * 检查是否触发止盈
     */
    private function shouldTakeProfit(array $order, float $currentPrice): bool
    {
        if ($order['take_profit_price'] <= 0) {
            return false;
        }

        if ($order['order_type'] == 1) { // 做多
            return $currentPrice >= $order['take_profit_price'];
        } else { // 做空
            return $currentPrice <= $order['take_profit_price'];
        }
    }

    /**
     * 执行止损
     */
    private function stopLossOrder(array $order, float $currentPrice): void
    {
        try {
            Db::startTrans();

            // 计算盈亏
            $profitLoss = $this->calculateProfitLoss($order, $currentPrice);

            // 更新订单状态
            Db::name('leverage_orders')->where('id', $order['id'])->update([
                'close_price' => $currentPrice,
                'profit_loss' => $profitLoss,
                'status' => 'closed',
                'close_type' => 'stop_loss',
                'closed_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // 解冻保证金并结算盈亏
            $finalAmount = $order['margin'] + $profitLoss;
            $this->unfreezeAsset($order['user_id'], 'USDT', $order['margin']);

            if ($profitLoss != 0) {
                if ($profitLoss > 0) {
                    $this->addAsset($order['user_id'], 'USDT', $profitLoss);
                } else {
                    $this->subAsset($order['user_id'], 'USDT', abs($profitLoss));
                }
            }

            // 记录日志
            $this->recordFinancialLog($order['user_id'], 'USDT', $profitLoss, 'stop_loss', '止损平仓', $order['id']);

            Db::commit();

            Log::info('止损执行成功', ['order_id' => $order['id'], 'user_id' => $order['user_id']]);

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('止损执行失败: ' . $e->getMessage());
        }
    }

    /**
     * 执行止盈
     */
    private function takeProfitOrder(array $order, float $currentPrice): void
    {
        try {
            Db::startTrans();

            // 计算盈亏
            $profitLoss = $this->calculateProfitLoss($order, $currentPrice);

            // 更新订单状态
            Db::name('leverage_orders')->where('id', $order['id'])->update([
                'close_price' => $currentPrice,
                'profit_loss' => $profitLoss,
                'status' => 'closed',
                'close_type' => 'take_profit',
                'closed_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // 解冻保证金并结算盈亏
            $finalAmount = $order['margin'] + $profitLoss;
            $this->unfreezeAsset($order['user_id'], 'USDT', $order['margin']);

            if ($profitLoss > 0) {
                $this->addAsset($order['user_id'], 'USDT', $profitLoss);
            }

            // 记录日志
            $this->recordFinancialLog($order['user_id'], 'USDT', $profitLoss, 'take_profit', '止盈平仓', $order['id']);

            Db::commit();

            Log::info('止盈执行成功', ['order_id' => $order['id'], 'user_id' => $order['user_id']]);

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('止盈执行失败: ' . $e->getMessage());
        }
    }
}
