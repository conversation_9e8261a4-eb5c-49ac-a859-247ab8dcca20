<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use Workerman\Worker;
use Workerman\Connection\TcpConnection;
use think\facade\Db;
use think\facade\Cache;
use think\facade\Log;

/**
 * WebSocket客服服务
 */
class WebSocketServer extends Command
{
    protected $connections = [];
    protected $userConnections = [];

    protected function configure()
    {
        $this->setName('websocket:start')
            ->setDescription('启动WebSocket客服服务');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('启动WebSocket客服服务...');

        // 创建WebSocket服务器
        $ws_worker = new Worker("websocket://0.0.0.0:2346");
        $ws_worker->count = 1;
        $ws_worker->name = 'GVD-CustomerService';

        // 当客户端连接时
        $ws_worker->onConnect = function($connection) {
            $this->onConnect($connection);
        };

        // 当收到消息时
        $ws_worker->onMessage = function($connection, $data) {
            $this->onMessage($connection, $data);
        };

        // 当连接关闭时
        $ws_worker->onClose = function($connection) {
            $this->onClose($connection);
        };

        // 当出现错误时
        $ws_worker->onError = function($connection, $code, $msg) {
            $this->onError($connection, $code, $msg);
        };

        // 启动定时器
        $this->startTimers($ws_worker);

        Worker::runAll();
    }

    /**
     * 客户端连接事件
     */
    private function onConnect(TcpConnection $connection): void
    {
        $connection->id = uniqid();
        $this->connections[$connection->id] = $connection;
        
        Log::info("WebSocket客户端连接: {$connection->id}");
        
        // 发送连接成功消息
        $this->sendToConnection($connection, [
            'type' => 'connected',
            'data' => [
                'connection_id' => $connection->id,
                'server_time' => time()
            ]
        ]);
    }

    /**
     * 收到消息事件
     */
    private function onMessage(TcpConnection $connection, string $data): void
    {
        try {
            $message = json_decode($data, true);
            
            if (!$message || !isset($message['type'])) {
                $this->sendError($connection, '消息格式错误');
                return;
            }

            switch ($message['type']) {
                case 'auth':
                    $this->handleAuth($connection, $message['data'] ?? []);
                    break;
                    
                case 'send_message':
                    $this->handleSendMessage($connection, $message['data'] ?? []);
                    break;
                    
                case 'join_session':
                    $this->handleJoinSession($connection, $message['data'] ?? []);
                    break;
                    
                case 'leave_session':
                    $this->handleLeaveSession($connection, $message['data'] ?? []);
                    break;
                    
                case 'typing':
                    $this->handleTyping($connection, $message['data'] ?? []);
                    break;
                    
                case 'heartbeat':
                    $this->handleHeartbeat($connection);
                    break;
                    
                default:
                    $this->sendError($connection, '未知消息类型');
            }
            
        } catch (\Exception $e) {
            Log::error('WebSocket消息处理错误: ' . $e->getMessage());
            $this->sendError($connection, '服务器内部错误');
        }
    }

    /**
     * 连接关闭事件
     */
    private function onClose(TcpConnection $connection): void
    {
        $this->cleanupConnection($connection);
        Log::info("WebSocket客户端断开: {$connection->id}");
    }

    /**
     * 错误事件
     */
    private function onError(TcpConnection $connection, int $code, string $msg): void
    {
        Log::error("WebSocket错误 [{$code}]: {$msg}");
    }

    /**
     * 处理用户认证
     */
    private function handleAuth(TcpConnection $connection, array $data): void
    {
        if (empty($data['token']) || empty($data['user_type'])) {
            $this->sendError($connection, '认证参数不完整');
            return;
        }

        // 验证Token
        $userInfo = $this->validateToken($data['token'], $data['user_type']);
        if (!$userInfo) {
            $this->sendError($connection, '认证失败');
            return;
        }

        // 保存用户连接信息
        $connection->user_id = $userInfo['id'];
        $connection->user_type = $data['user_type'];
        $connection->authenticated = true;

        $userKey = $data['user_type'] . '_' . $userInfo['id'];
        $this->userConnections[$userKey] = $connection;

        // 更新在线状态
        $this->updateOnlineStatus($userInfo['id'], $data['user_type'], 'online', $connection->id);

        // 发送认证成功消息
        $this->sendToConnection($connection, [
            'type' => 'auth_success',
            'data' => [
                'user_id' => $userInfo['id'],
                'user_type' => $data['user_type'],
                'username' => $userInfo['username']
            ]
        ]);

        Log::info("用户认证成功: {$data['user_type']}_{$userInfo['id']}");
    }

    /**
     * 处理发送消息
     */
    private function handleSendMessage(TcpConnection $connection, array $data): void
    {
        if (!$connection->authenticated) {
            $this->sendError($connection, '请先认证');
            return;
        }

        if (empty($data['session_id']) || empty($data['content'])) {
            $this->sendError($connection, '消息参数不完整');
            return;
        }

        // 保存消息到数据库
        $messageData = [
            'session_id' => $data['session_id'],
            'sender_id' => $connection->user_id,
            'sender_type' => $connection->user_type,
            'message_type' => $data['message_type'] ?? 'text',
            'content' => $data['content']
        ];

        $customerService = new \app\common\service\CustomerService();
        $result = $customerService->sendMessage($messageData);

        if ($result['code'] == 1) {
            // 广播消息给相关用户
            $this->broadcastMessage($data['session_id'], $result['data']);
            
            // 发送成功确认
            $this->sendToConnection($connection, [
                'type' => 'message_sent',
                'data' => $result['data']
            ]);
        } else {
            $this->sendError($connection, $result['msg']);
        }
    }

    /**
     * 处理加入会话
     */
    private function handleJoinSession(TcpConnection $connection, array $data): void
    {
        if (!$connection->authenticated) {
            $this->sendError($connection, '请先认证');
            return;
        }

        if (empty($data['session_id'])) {
            $this->sendError($connection, '会话ID不能为空');
            return;
        }

        $connection->session_id = $data['session_id'];

        // 标记消息为已读
        $customerService = new \app\common\service\CustomerService();
        $customerService->markMessagesAsRead(
            $data['session_id'], 
            $connection->user_id, 
            $connection->user_type
        );

        $this->sendToConnection($connection, [
            'type' => 'session_joined',
            'data' => ['session_id' => $data['session_id']]
        ]);
    }

    /**
     * 处理离开会话
     */
    private function handleLeaveSession(TcpConnection $connection, array $data): void
    {
        $connection->session_id = null;
        
        $this->sendToConnection($connection, [
            'type' => 'session_left',
            'data' => []
        ]);
    }

    /**
     * 处理正在输入
     */
    private function handleTyping(TcpConnection $connection, array $data): void
    {
        if (!$connection->authenticated || !$connection->session_id) {
            return;
        }

        // 广播正在输入状态
        $this->broadcastToSession($connection->session_id, [
            'type' => 'user_typing',
            'data' => [
                'user_id' => $connection->user_id,
                'user_type' => $connection->user_type,
                'typing' => $data['typing'] ?? true
            ]
        ], $connection);
    }

    /**
     * 处理心跳
     */
    private function handleHeartbeat(TcpConnection $connection): void
    {
        $this->sendToConnection($connection, [
            'type' => 'heartbeat_response',
            'data' => ['server_time' => time()]
        ]);
    }

    /**
     * 广播消息给会话相关用户
     */
    private function broadcastMessage(string $sessionId, array $message): void
    {
        // 获取会话信息
        $session = Db::name('customer_sessions')
            ->where('session_id', $sessionId)
            ->find();

        if (!$session) {
            return;
        }

        $targets = [];
        
        // 添加用户
        if ($session['user_id']) {
            $targets[] = 'user_' . $session['user_id'];
        }
        
        // 添加代理
        if ($session['agent_id']) {
            $targets[] = 'agent_' . $session['agent_id'];
        }
        
        // 添加在线管理员
        $onlineAdmins = Db::name('customer_online_status')
            ->where('user_type', 'admin')
            ->where('status', 'online')
            ->column('user_id');
            
        foreach ($onlineAdmins as $adminId) {
            $targets[] = 'admin_' . $adminId;
        }

        // 发送消息
        foreach ($targets as $target) {
            if (isset($this->userConnections[$target])) {
                $this->sendToConnection($this->userConnections[$target], [
                    'type' => 'new_message',
                    'data' => $message
                ]);
            }
        }
    }

    /**
     * 广播消息给会话中的其他用户
     */
    private function broadcastToSession(string $sessionId, array $data, TcpConnection $excludeConnection = null): void
    {
        foreach ($this->connections as $connection) {
            if ($connection->session_id === $sessionId && $connection !== $excludeConnection) {
                $this->sendToConnection($connection, $data);
            }
        }
    }

    /**
     * 发送消息给指定连接
     */
    private function sendToConnection(TcpConnection $connection, array $data): void
    {
        try {
            $connection->send(json_encode($data, JSON_UNESCAPED_UNICODE));
        } catch (\Exception $e) {
            Log::error('发送WebSocket消息失败: ' . $e->getMessage());
        }
    }

    /**
     * 发送错误消息
     */
    private function sendError(TcpConnection $connection, string $message): void
    {
        $this->sendToConnection($connection, [
            'type' => 'error',
            'data' => ['message' => $message]
        ]);
    }

    /**
     * 验证Token
     */
    private function validateToken(string $token, string $userType): ?array
    {
        try {
            // 这里实现JWT Token验证逻辑
            // 简化版本，实际应该使用JWT库
            $userService = new \app\common\service\UserService();
            return $userService->validateToken($token);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 更新在线状态
     */
    private function updateOnlineStatus(int $userId, string $userType, string $status, string $socketId): void
    {
        $customerService = new \app\common\service\CustomerService();
        $customerService->updateOnlineStatus($userId, $userType, $status, $socketId);
    }

    /**
     * 清理连接
     */
    private function cleanupConnection(TcpConnection $connection): void
    {
        // 从连接列表中移除
        unset($this->connections[$connection->id]);

        // 从用户连接映射中移除
        if (isset($connection->user_id) && isset($connection->user_type)) {
            $userKey = $connection->user_type . '_' . $connection->user_id;
            unset($this->userConnections[$userKey]);

            // 更新离线状态
            $this->updateOnlineStatus($connection->user_id, $connection->user_type, 'offline', '');
        }
    }

    /**
     * 启动定时器
     */
    private function startTimers(Worker $worker): void
    {
        // 每30秒清理过期连接
        \Workerman\Lib\Timer::add(30, function() {
            $this->cleanupExpiredConnections();
        });

        // 每分钟统计在线用户
        \Workerman\Lib\Timer::add(60, function() {
            $this->updateOnlineStatistics();
        });
    }

    /**
     * 清理过期连接
     */
    private function cleanupExpiredConnections(): void
    {
        $expiredTime = time() - 300; // 5分钟无活动视为过期
        
        foreach ($this->connections as $id => $connection) {
            if (isset($connection->last_heartbeat) && $connection->last_heartbeat < $expiredTime) {
                $connection->close();
            }
        }
    }

    /**
     * 更新在线统计
     */
    private function updateOnlineStatistics(): void
    {
        $stats = [
            'total_connections' => count($this->connections),
            'authenticated_users' => count($this->userConnections),
            'timestamp' => time()
        ];

        Cache::set('websocket_stats', $stats, 300);
    }
}
