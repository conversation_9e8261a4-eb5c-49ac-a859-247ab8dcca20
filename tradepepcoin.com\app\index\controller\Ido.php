<?php
declare (strict_types = 1);

namespace app\index\controller;

use app\BaseController;
use app\common\model\IdoProject;
use app\common\model\IdoOrder;
use app\common\model\UserAsset;
use app\common\service\IdoService;
use think\facade\View;
use think\facade\Session;

/**
 * 新币认购控制器
 */
class Ido extends BaseController
{
    protected $idoService;

    public function initialize()
    {
        parent::initialize();
        $this->idoService = new IdoService();
    }

    /**
     * 认购项目列表
     */
    public function index()
    {
        $status = input('status', ''); // all, upcoming, ongoing, ended
        $page = input('page/d', 1);
        $limit = input('limit/d', 12);

        $where = [];
        $now = date('Y-m-d H:i:s');

        switch ($status) {
            case 'upcoming':
                $where[] = ['start_time', '>', $now];
                break;
            case 'ongoing':
                $where[] = ['start_time', '<=', $now];
                $where[] = ['end_time', '>', $now];
                break;
            case 'ended':
                $where[] = ['end_time', '<=', $now];
                break;
            default:
                // 显示所有项目
                break;
        }

        $projects = IdoProject::where($where)
                             ->where('status', 1)
                             ->order('created_at', 'desc')
                             ->paginate([
                                 'list_rows' => $limit,
                                 'page' => $page
                             ]);

        if ($this->request->isAjax()) {
            return json([
                'code' => 1,
                'data' => $projects->items(),
                'total' => $projects->total(),
                'page' => $page,
                'limit' => $limit
            ]);
        }

        View::assign([
            'projects' => $projects,
            'status' => $status,
            'title' => '新币认购'
        ]);

        return View::fetch();
    }

    /**
     * 项目详情
     */
    public function detail()
    {
        $id = input('id/d', 0);
        
        $project = IdoProject::find($id);
        if (!$project || $project->status != 1) {
            $this->error('项目不存在或已下架');
        }

        // 获取用户认购记录（如果已登录）
        $userOrder = null;
        if (Session::has('user_id')) {
            $userId = Session::get('user_id');
            $userOrder = IdoOrder::where('user_id', $userId)
                                ->where('project_id', $id)
                                ->find();
            
            // 获取用户支付币种余额
            $paymentAsset = UserAsset::getOrCreate($userId, $project->payment_coin);
            View::assign('paymentAsset', $paymentAsset);
        }

        // 获取认购统计
        $stats = $this->idoService->getProjectStats($id);

        View::assign([
            'project' => $project,
            'userOrder' => $userOrder,
            'stats' => $stats,
            'title' => $project->name
        ]);

        return View::fetch();
    }

    /**
     * 认购
     */
    public function purchase()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        // 检查登录状态
        if (!Session::has('user_id')) {
            return json(['code' => 0, 'msg' => '请先登录']);
        }

        $data = $this->request->post();
        
        // 验证参数
        $validate = [
            'project_id' => 'require|integer|gt:0',
            'amount' => 'require|float|gt:0',
        ];

        $result = $this->validate($data, $validate);
        if ($result !== true) {
            return json(['code' => 0, 'msg' => $result]);
        }

        // 执行认购
        $userId = Session::get('user_id');
        $result = $this->idoService->createOrder($userId, $data);

        return json($result);
    }

    /**
     * 认购订单列表
     */
    public function orders()
    {
        // 检查登录状态
        if (!Session::has('user_id')) {
            $this->redirect('/auth/login');
        }

        $userId = Session::get('user_id');
        $status = input('status', '');
        $page = input('page/d', 1);
        $limit = input('limit/d', 20);

        $where = ['user_id' => $userId];
        
        if ($status !== '') {
            $where['status'] = $status;
        }

        $orders = IdoOrder::where($where)
                         ->with(['project'])
                         ->order('created_at', 'desc')
                         ->paginate([
                             'list_rows' => $limit,
                             'page' => $page
                         ]);

        if ($this->request->isAjax()) {
            return json([
                'code' => 1,
                'data' => $orders->items(),
                'total' => $orders->total(),
                'page' => $page,
                'limit' => $limit
            ]);
        }

        View::assign([
            'orders' => $orders,
            'status' => $status,
            'title' => '我的认购'
        ]);

        return View::fetch();
    }

    /**
     * 获取项目统计
     */
    public function projectStats()
    {
        $id = input('id/d', 0);
        
        $stats = $this->idoService->getProjectStats($id);
        
        return json([
            'code' => 1,
            'data' => $stats
        ]);
    }

    /**
     * 获取用户认购统计
     */
    public function userStats()
    {
        // 检查登录状态
        if (!Session::has('user_id')) {
            return json(['code' => 0, 'msg' => '请先登录']);
        }

        $userId = Session::get('user_id');
        $stats = $this->idoService->getUserStats($userId);
        
        return json([
            'code' => 1,
            'data' => $stats
        ]);
    }

    /**
     * 获取释放记录
     */
    public function releaseRecords()
    {
        // 检查登录状态
        if (!Session::has('user_id')) {
            return json(['code' => 0, 'msg' => '请先登录']);
        }

        $userId = Session::get('user_id');
        $orderId = input('order_id');
        
        if (empty($orderId)) {
            return json(['code' => 0, 'msg' => '订单ID不能为空']);
        }

        $records = $this->idoService->getReleaseRecords($userId, $orderId);
        
        return json([
            'code' => 1,
            'data' => $records
        ]);
    }

    /**
     * 手动释放
     */
    public function release()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        // 检查登录状态
        if (!Session::has('user_id')) {
            return json(['code' => 0, 'msg' => '请先登录']);
        }

        $orderId = input('order_id');
        
        if (empty($orderId)) {
            return json(['code' => 0, 'msg' => '订单ID不能为空']);
        }

        $userId = Session::get('user_id');
        $result = $this->idoService->releaseTokens($userId, $orderId);

        return json($result);
    }

    /**
     * 获取热门项目
     */
    public function hotProjects()
    {
        $limit = input('limit/d', 6);
        
        $projects = IdoProject::where('status', 1)
                             ->order('sale_amount', 'desc')
                             ->limit($limit)
                             ->select();

        return json([
            'code' => 1,
            'data' => $projects
        ]);
    }

    /**
     * 搜索项目
     */
    public function search()
    {
        $keyword = input('keyword', '');
        $page = input('page/d', 1);
        $limit = input('limit/d', 12);

        if (empty($keyword)) {
            return json(['code' => 0, 'msg' => '搜索关键词不能为空']);
        }

        $where = [
            ['status', '=', 1],
            ['name|symbol|description', 'like', "%{$keyword}%"]
        ];

        $projects = IdoProject::where($where)
                             ->order('created_at', 'desc')
                             ->paginate([
                                 'list_rows' => $limit,
                                 'page' => $page
                             ]);

        return json([
            'code' => 1,
            'data' => $projects->items(),
            'total' => $projects->total(),
            'page' => $page,
            'limit' => $limit
        ]);
    }
}
