<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 用户资产模型
 */
class UserAsset extends Model
{
    protected $table = 'gvd_user_assets';
    
    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'user_id'     => 'int',
        'coin_symbol' => 'string',
        'available'   => 'decimal',
        'frozen'      => 'decimal',
        'total'       => 'decimal',
        'created_at'  => 'datetime',
        'updated_at'  => 'datetime',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 类型转换
    protected $type = [
        'user_id'   => 'integer',
        'available' => 'decimal:8',
        'frozen'    => 'decimal:8',
        'total'     => 'decimal:8',
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联币种
     */
    public function coin()
    {
        return $this->belongsTo(Coin::class, 'coin_symbol', 'symbol');
    }

    /**
     * 获取或创建用户资产
     */
    public static function getOrCreate(int $userId, string $coinSymbol): UserAsset
    {
        $asset = self::where('user_id', $userId)
                    ->where('coin_symbol', $coinSymbol)
                    ->find();
        
        if (!$asset) {
            $asset = new self();
            $asset->user_id = $userId;
            $asset->coin_symbol = $coinSymbol;
            $asset->available = 0;
            $asset->frozen = 0;
            $asset->total = 0;
            $asset->save();
        }
        
        return $asset;
    }

    /**
     * 增加可用余额
     */
    public function addAvailable(float $amount): bool
    {
        if ($amount <= 0) {
            return false;
        }
        
        $this->available += $amount;
        $this->total = $this->available + $this->frozen;
        
        return $this->save();
    }

    /**
     * 减少可用余额
     */
    public function subAvailable(float $amount): bool
    {
        if ($amount <= 0 || $this->available < $amount) {
            return false;
        }
        
        $this->available -= $amount;
        $this->total = $this->available + $this->frozen;
        
        return $this->save();
    }

    /**
     * 冻结余额
     */
    public function freeze(float $amount): bool
    {
        if ($amount <= 0 || $this->available < $amount) {
            return false;
        }
        
        $this->available -= $amount;
        $this->frozen += $amount;
        
        return $this->save();
    }

    /**
     * 解冻余额
     */
    public function unfreeze(float $amount): bool
    {
        if ($amount <= 0 || $this->frozen < $amount) {
            return false;
        }
        
        $this->frozen -= $amount;
        $this->available += $amount;
        
        return $this->save();
    }

    /**
     * 扣除冻结余额
     */
    public function subFrozen(float $amount): bool
    {
        if ($amount <= 0 || $this->frozen < $amount) {
            return false;
        }
        
        $this->frozen -= $amount;
        $this->total = $this->available + $this->frozen;
        
        return $this->save();
    }

    /**
     * 格式化金额显示
     */
    public function getAvailableFormatAttr($value, $data)
    {
        return number_format($data['available'], 8, '.', '');
    }

    /**
     * 格式化冻结金额显示
     */
    public function getFrozenFormatAttr($value, $data)
    {
        return number_format($data['frozen'], 8, '.', '');
    }

    /**
     * 格式化总金额显示
     */
    public function getTotalFormatAttr($value, $data)
    {
        return number_format($data['total'], 8, '.', '');
    }
}
