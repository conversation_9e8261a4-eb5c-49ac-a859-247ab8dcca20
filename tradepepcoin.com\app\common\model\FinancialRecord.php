<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 财务记录模型
 */
class FinancialRecord extends Model
{
    protected $table = 'ce_financial_records';
    
    // 设置字段信息 - 兼容老系统字段
    protected $schema = [
        'id'             => 'int',
        'user_id'        => 'int',
        'username'       => 'string',
        'coin_symbol'    => 'string',
        'type'           => 'int',
        'amount'         => 'decimal',
        'balance_before' => 'decimal',
        'balance_after'  => 'decimal',
        'direction'      => 'int',
        'related_id'     => 'string',
        'description'    => 'string',
        'remark'         => 'string',
        'created_at'     => 'datetime',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = 'datetime';
    protected $updateTime = false;

    // 类型转换
    protected $type = [
        'user_id'        => 'integer',
        'type'           => 'integer',
        'direction'      => 'integer',
        'amount'         => 'decimal:8',
        'balance_before' => 'decimal:8',
        'balance_after'  => 'decimal:8',
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联币种
     */
    public function coin()
    {
        return $this->belongsTo(Coin::class, 'coin_symbol', 'symbol');
    }

    /**
     * 类型文本 - 兼容老系统类型定义
     */
    public function getTypeTextAttr($value, $data)
    {
        $types = [
            1 => '管理员操作',
            2 => '提币申请',
            3 => '冻结资产',
            4 => '解冻资产',
            5 => '交易买入',
            6 => '交易卖出',
            7 => '合约收益',
            8 => '认购收益',
            9 => '充值',
            10 => '提现',
            11 => '佣金收入',
            12 => '转账',
            13 => '手续费',
            14 => '体验金',
            15 => '系统调整',
            16 => '提币驳回'
        ];
        return $types[$data['type']] ?? '未知类型';
    }

    /**
     * 类型颜色
     */
    public function getTypeColorAttr($value, $data)
    {
        $colors = [
            'deposit' => 'success',
            'withdraw' => 'warning',
            'trade' => 'info',
            'contract' => 'primary',
            'ido' => 'purple',
            'commission' => 'success',
            'transfer' => 'secondary',
            'adjustment' => 'danger'
        ];
        return $colors[$data['type']] ?? 'secondary';
    }

    /**
     * 金额方向文本 - 兼容老系统
     */
    public function getDirectionTextAttr($value, $data)
    {
        $directions = [
            1 => '收入',
            2 => '支出',
            3 => '冻结',
            4 => '解冻'
        ];
        return $directions[$data['direction']] ?? '未知';
    }

    /**
     * 金额方向颜色 - 兼容老系统
     */
    public function getDirectionColorAttr($value, $data)
    {
        $colors = [
            1 => 'success', // 收入
            2 => 'danger',  // 支出
            3 => 'warning', // 冻结
            4 => 'info'     // 解冻
        ];
        return $colors[$data['direction']] ?? 'secondary';
    }

    /**
     * 格式化金额
     */
    public function getAmountFormatAttr($value, $data)
    {
        $amount = abs($data['amount']);
        return number_format($amount, 8, '.', '');
    }

    /**
     * 带符号的格式化金额
     */
    public function getAmountSignedFormatAttr($value, $data)
    {
        $prefix = $data['amount'] >= 0 ? '+' : '-';
        $amount = abs($data['amount']);
        return $prefix . number_format($amount, 8, '.', '');
    }

    /**
     * 格式化余额（之前）
     */
    public function getBalanceBeforeFormatAttr($value, $data)
    {
        return number_format($data['balance_before'], 8, '.', '');
    }

    /**
     * 格式化余额（之后）
     */
    public function getBalanceAfterFormatAttr($value, $data)
    {
        return number_format($data['balance_after'], 8, '.', '');
    }

    /**
     * 获取用户财务统计
     */
    public static function getUserStats(int $userId, string $coinSymbol = ''): array
    {
        $where = ['user_id' => $userId];
        if (!empty($coinSymbol)) {
            $where['coin_symbol'] = $coinSymbol;
        }

        // 总收入
        $totalIncome = self::where($where)->where('amount', '>', 0)->sum('amount');

        // 总支出
        $totalExpense = self::where($where)->where('amount', '<', 0)->sum('amount');

        // 净收入
        $netIncome = $totalIncome + $totalExpense;

        // 今日收入
        $todayIncome = self::where($where)
                          ->where('amount', '>', 0)
                          ->whereTime('created_at', 'today')
                          ->sum('amount');

        // 今日支出
        $todayExpense = self::where($where)
                           ->where('amount', '<', 0)
                           ->whereTime('created_at', 'today')
                           ->sum('amount');

        // 本月收入
        $monthIncome = self::where($where)
                          ->where('amount', '>', 0)
                          ->whereTime('created_at', 'month')
                          ->sum('amount');

        // 本月支出
        $monthExpense = self::where($where)
                           ->where('amount', '<', 0)
                           ->whereTime('created_at', 'month')
                           ->sum('amount');

        // 各类型统计
        $typeStats = self::where($where)
                        ->field('type, SUM(amount) as total_amount, COUNT(*) as total_count')
                        ->group('type')
                        ->select()
                        ->toArray();

        return [
            'total_income' => $totalIncome,
            'total_expense' => abs($totalExpense),
            'net_income' => $netIncome,
            'today_income' => $todayIncome,
            'today_expense' => abs($todayExpense),
            'month_income' => $monthIncome,
            'month_expense' => abs($monthExpense),
            'type_stats' => $typeStats
        ];
    }

    /**
     * 获取用户财务明细
     */
    public static function getUserRecords(int $userId, array $params = []): array
    {
        $where = ['user_id' => $userId];

        if (!empty($params['coin_symbol'])) {
            $where['coin_symbol'] = $params['coin_symbol'];
        }

        if (!empty($params['type'])) {
            $where['type'] = $params['type'];
        }

        if (!empty($params['direction'])) {
            if ($params['direction'] == 'in') {
                $where[] = ['amount', '>', 0];
            } else {
                $where[] = ['amount', '<', 0];
            }
        }

        if (!empty($params['start_time'])) {
            $where[] = ['created_at', '>=', $params['start_time']];
        }

        if (!empty($params['end_time'])) {
            $where[] = ['created_at', '<=', $params['end_time']];
        }

        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 20;

        return self::where($where)
                  ->order('created_at', 'desc')
                  ->paginate([
                      'list_rows' => $limit,
                      'page' => $page
                  ])
                  ->toArray();
    }

    /**
     * 获取币种流水统计
     */
    public static function getCoinStats(string $coinSymbol): array
    {
        $where = ['coin_symbol' => $coinSymbol];

        // 总流入
        $totalInflow = self::where($where)->where('amount', '>', 0)->sum('amount');

        // 总流出
        $totalOutflow = self::where($where)->where('amount', '<', 0)->sum('amount');

        // 净流入
        $netInflow = $totalInflow + $totalOutflow;

        // 今日流入
        $todayInflow = self::where($where)
                          ->where('amount', '>', 0)
                          ->whereTime('created_at', 'today')
                          ->sum('amount');

        // 今日流出
        $todayOutflow = self::where($where)
                           ->where('amount', '<', 0)
                           ->whereTime('created_at', 'today')
                           ->sum('amount');

        // 活跃用户数
        $activeUsers = self::where($where)
                          ->whereTime('created_at', 'today')
                          ->count('DISTINCT user_id');

        return [
            'total_inflow' => $totalInflow,
            'total_outflow' => abs($totalOutflow),
            'net_inflow' => $netInflow,
            'today_inflow' => $todayInflow,
            'today_outflow' => abs($todayOutflow),
            'active_users' => $activeUsers
        ];
    }

    /**
     * 获取财务报表数据
     */
    public static function getReportData(array $params = []): array
    {
        $where = [];

        if (!empty($params['coin_symbol'])) {
            $where['coin_symbol'] = $params['coin_symbol'];
        }

        if (!empty($params['type'])) {
            $where['type'] = $params['type'];
        }

        if (!empty($params['start_time'])) {
            $where[] = ['created_at', '>=', $params['start_time']];
        }

        if (!empty($params['end_time'])) {
            $where[] = ['created_at', '<=', $params['end_time']];
        }

        // 按日期分组统计
        $dailyStats = self::where($where)
                         ->field('DATE(created_at) as date, SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as income, SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as expense')
                         ->group('DATE(created_at)')
                         ->order('date', 'desc')
                         ->select()
                         ->toArray();

        // 按类型分组统计
        $typeStats = self::where($where)
                        ->field('type, SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as income, SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as expense, COUNT(*) as count')
                        ->group('type')
                        ->select()
                        ->toArray();

        // 按币种分组统计
        $coinStats = self::where($where)
                        ->field('coin_symbol, SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as income, SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as expense, COUNT(*) as count')
                        ->group('coin_symbol')
                        ->select()
                        ->toArray();

        return [
            'daily_stats' => $dailyStats,
            'type_stats' => $typeStats,
            'coin_stats' => $coinStats
        ];
    }

    /**
     * 获取财务统计 - 只统计正式用户
     */
    public static function getFinancialStats(): array
    {
        // 总充值金额（只统计正式用户）
        $totalDeposit = self::alias('fr')
                           ->join('ce_users u', 'fr.user_id = u.id')
                           ->where('fr.type', 9)
                           ->where('u.user_type', 1)
                           ->sum('fr.amount');

        // 总提现金额（只统计正式用户）
        $totalWithdraw = self::alias('fr')
                            ->join('ce_users u', 'fr.user_id = u.id')
                            ->where('fr.type', 10)
                            ->where('u.user_type', 1)
                            ->sum('fr.amount');

        // 今日充值（只统计正式用户）
        $todayDeposit = self::alias('fr')
                           ->join('ce_users u', 'fr.user_id = u.id')
                           ->where('fr.type', 9)
                           ->where('u.user_type', 1)
                           ->whereTime('fr.created_at', 'today')
                           ->sum('fr.amount');

        // 今日提现（只统计正式用户）
        $todayWithdraw = self::alias('fr')
                            ->join('ce_users u', 'fr.user_id = u.id')
                            ->where('fr.type', 10)
                            ->where('u.user_type', 1)
                            ->whereTime('fr.created_at', 'today')
                            ->sum('fr.amount');

        // 本月充值（只统计正式用户）
        $monthDeposit = self::alias('fr')
                           ->join('ce_users u', 'fr.user_id = u.id')
                           ->where('fr.type', 9)
                           ->where('u.user_type', 1)
                           ->whereTime('fr.created_at', 'month')
                           ->sum('fr.amount');

        return [
            'total_deposit' => $totalDeposit ?: 0,
            'total_withdraw' => $totalWithdraw ?: 0,
            'today_deposit' => $todayDeposit ?: 0,
            'today_withdraw' => $todayWithdraw ?: 0,
            'month_deposit' => $monthDeposit ?: 0,
        ];
    }

    /**
     * 获取代理商下级财务统计 - 只统计正式用户
     */
    public static function getAgentFinancialStats(int $agentId): array
    {
        // 下级总充值金额（只统计正式用户）
        $totalDeposit = self::alias('fr')
                           ->join('ce_users u', 'fr.user_id = u.id')
                           ->where('fr.type', 9)
                           ->where('u.user_type', 1)
                           ->where('u.agent_id', $agentId)
                           ->sum('fr.amount');

        // 下级总提现金额（只统计正式用户）
        $totalWithdraw = self::alias('fr')
                            ->join('ce_users u', 'fr.user_id = u.id')
                            ->where('fr.type', 10)
                            ->where('u.user_type', 1)
                            ->where('u.agent_id', $agentId)
                            ->sum('fr.amount');

        // 下级今日充值（只统计正式用户）
        $todayDeposit = self::alias('fr')
                           ->join('ce_users u', 'fr.user_id = u.id')
                           ->where('fr.type', 9)
                           ->where('u.user_type', 1)
                           ->where('u.agent_id', $agentId)
                           ->whereTime('fr.created_at', 'today')
                           ->sum('fr.amount');

        // 下级本月充值（只统计正式用户）
        $monthDeposit = self::alias('fr')
                           ->join('ce_users u', 'fr.user_id = u.id')
                           ->where('fr.type', 9)
                           ->where('u.user_type', 1)
                           ->where('u.agent_id', $agentId)
                           ->whereTime('fr.created_at', 'month')
                           ->sum('fr.amount');

        return [
            'total_deposit' => $totalDeposit ?: 0,
            'total_withdraw' => $totalWithdraw ?: 0,
            'today_deposit' => $todayDeposit ?: 0,
            'month_deposit' => $monthDeposit ?: 0,
        ];
    }
}
