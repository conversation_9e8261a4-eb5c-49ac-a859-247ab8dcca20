<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

/**
 * 系统监控服务类
 * 监控系统性能、资源使用、业务指标等
 */
class SystemMonitorService
{
    // 监控类型
    const TYPE_SYSTEM = 'system';
    const TYPE_DATABASE = 'database';
    const TYPE_CACHE = 'cache';
    const TYPE_QUEUE = 'queue';
    const TYPE_BUSINESS = 'business';
    const TYPE_SECURITY = 'security';

    // 告警级别
    const ALERT_INFO = 'info';
    const ALERT_WARNING = 'warning';
    const ALERT_ERROR = 'error';
    const ALERT_CRITICAL = 'critical';

    private $config;
    private $thresholds;

    public function __construct()
    {
        $this->config = config('monitor');
        $this->thresholds = $this->config['thresholds'] ?? [];
    }

    /**
     * 获取系统监控数据
     */
    public function getSystemMetrics(): array
    {
        try {
            $metrics = [
                'timestamp' => time(),
                'system' => $this->getSystemInfo(),
                'performance' => $this->getPerformanceMetrics(),
                'database' => $this->getDatabaseMetrics(),
                'cache' => $this->getCacheMetrics(),
                'queue' => $this->getQueueMetrics(),
                'business' => $this->getBusinessMetrics(),
                'security' => $this->getSecurityMetrics()
            ];

            // 检查告警
            $alerts = $this->checkAlerts($metrics);
            $metrics['alerts'] = $alerts;

            // 保存监控数据
            $this->saveMetrics($metrics);

            return [
                'code' => 1,
                'msg' => '获取成功',
                'data' => $metrics
            ];

        } catch (\Exception $e) {
            Log::error('获取系统监控数据失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 获取系统信息
     */
    private function getSystemInfo(): array
    {
        $loadAvg = sys_getloadavg();
        
        return [
            'hostname' => gethostname(),
            'os' => PHP_OS,
            'php_version' => PHP_VERSION,
            'server_time' => date('Y-m-d H:i:s'),
            'uptime' => $this->getSystemUptime(),
            'load_average' => [
                '1min' => $loadAvg[0] ?? 0,
                '5min' => $loadAvg[1] ?? 0,
                '15min' => $loadAvg[2] ?? 0
            ],
            'cpu' => $this->getCpuInfo(),
            'memory' => $this->getMemoryInfo(),
            'disk' => $this->getDiskInfo(),
            'network' => $this->getNetworkInfo()
        ];
    }

    /**
     * 获取性能指标
     */
    private function getPerformanceMetrics(): array
    {
        return [
            'response_time' => $this->getAverageResponseTime(),
            'throughput' => $this->getThroughput(),
            'error_rate' => $this->getErrorRate(),
            'concurrent_users' => $this->getConcurrentUsers(),
            'active_sessions' => $this->getActiveSessions(),
            'php_processes' => $this->getPhpProcesses()
        ];
    }

    /**
     * 获取数据库指标
     */
    private function getDatabaseMetrics(): array
    {
        try {
            // MySQL状态
            $status = Db::query('SHOW STATUS');
            $statusMap = [];
            foreach ($status as $item) {
                $statusMap[$item['Variable_name']] = $item['Value'];
            }

            // 连接信息
            $connections = [
                'current' => (int)($statusMap['Threads_connected'] ?? 0),
                'max' => (int)($statusMap['Max_used_connections'] ?? 0),
                'total' => (int)($statusMap['Connections'] ?? 0)
            ];

            // 查询统计
            $queries = [
                'total' => (int)($statusMap['Queries'] ?? 0),
                'select' => (int)($statusMap['Com_select'] ?? 0),
                'insert' => (int)($statusMap['Com_insert'] ?? 0),
                'update' => (int)($statusMap['Com_update'] ?? 0),
                'delete' => (int)($statusMap['Com_delete'] ?? 0)
            ];

            // 慢查询
            $slowQueries = (int)($statusMap['Slow_queries'] ?? 0);

            // 表锁
            $tableLocks = [
                'immediate' => (int)($statusMap['Table_locks_immediate'] ?? 0),
                'waited' => (int)($statusMap['Table_locks_waited'] ?? 0)
            ];

            return [
                'status' => 'online',
                'connections' => $connections,
                'queries' => $queries,
                'slow_queries' => $slowQueries,
                'table_locks' => $tableLocks,
                'innodb_buffer_pool_size' => $this->formatBytes((int)($statusMap['Innodb_buffer_pool_size'] ?? 0)),
                'query_cache_hit_rate' => $this->calculateQueryCacheHitRate($statusMap)
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取缓存指标
     */
    private function getCacheMetrics(): array
    {
        try {
            $redis = Cache::store('redis')->handler();
            $info = $redis->info();

            return [
                'status' => 'online',
                'memory_used' => $this->formatBytes($info['used_memory'] ?? 0),
                'memory_peak' => $this->formatBytes($info['used_memory_peak'] ?? 0),
                'keys_total' => $info['db0']['keys'] ?? 0,
                'keys_expires' => $info['db0']['expires'] ?? 0,
                'hits' => $info['keyspace_hits'] ?? 0,
                'misses' => $info['keyspace_misses'] ?? 0,
                'hit_rate' => $this->calculateHitRate($info['keyspace_hits'] ?? 0, $info['keyspace_misses'] ?? 0),
                'connected_clients' => $info['connected_clients'] ?? 0,
                'ops_per_sec' => $info['instantaneous_ops_per_sec'] ?? 0
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取队列指标
     */
    private function getQueueMetrics(): array
    {
        try {
            $queueService = new QueueService();
            $stats = $queueService->getQueueStats();

            $totalJobs = 0;
            $pendingJobs = 0;
            $failedJobs = 0;

            foreach ($stats as $queue => $queueStats) {
                if (is_array($queueStats)) {
                    $totalJobs += $queueStats['pending'] ?? 0;
                    $pendingJobs += $queueStats['pending'] ?? 0;
                    $failedJobs += $queueStats['failed'] ?? 0;
                }
            }

            return [
                'status' => 'online',
                'total_jobs' => $totalJobs,
                'pending_jobs' => $pendingJobs,
                'failed_jobs' => $failedJobs,
                'queues' => $stats,
                'processing_rate' => $this->getQueueProcessingRate()
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取业务指标
     */
    private function getBusinessMetrics(): array
    {
        try {
            $today = date('Y-m-d');
            $yesterday = date('Y-m-d', strtotime('-1 day'));

            return [
                'users' => [
                    'total' => Db::name('users')->count(),
                    'active_today' => Db::name('users')->where('last_login_time', '>=', $today)->count(),
                    'new_today' => Db::name('users')->where('created_at', '>=', $today)->count()
                ],
                'trading' => [
                    'volume_24h' => $this->getTradingVolume24h(),
                    'orders_today' => Db::name('orders')->where('created_at', '>=', $today)->count(),
                    'trades_today' => Db::name('trades')->where('created_at', '>=', $today)->count()
                ],
                'assets' => [
                    'total_balance' => $this->getTotalBalance(),
                    'deposits_today' => $this->getDepositsToday(),
                    'withdraws_today' => $this->getWithdrawsToday()
                ],
                'system' => [
                    'api_calls_today' => $this->getApiCallsToday(),
                    'errors_today' => $this->getErrorsToday(),
                    'response_time_avg' => $this->getAverageResponseTime()
                ]
            ];

        } catch (\Exception $e) {
            return [
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取安全指标
     */
    private function getSecurityMetrics(): array
    {
        try {
            $today = date('Y-m-d');

            return [
                'failed_logins' => Db::name('login_logs')
                    ->where('status', 0)
                    ->where('created_at', '>=', $today)
                    ->count(),
                'suspicious_ips' => $this->getSuspiciousIPs(),
                'blocked_ips' => $this->getBlockedIPs(),
                'security_events' => Db::name('security_logs')
                    ->where('created_at', '>=', $today)
                    ->count(),
                'risk_transactions' => $this->getRiskTransactions(),
                'kyc_pending' => Db::name('kyc_records')
                    ->where('status', 'pending')
                    ->count()
            ];

        } catch (\Exception $e) {
            return [
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 检查告警
     */
    private function checkAlerts(array $metrics): array
    {
        $alerts = [];

        // CPU使用率告警
        if (isset($metrics['system']['cpu']['usage']) && $metrics['system']['cpu']['usage'] > 80) {
            $alerts[] = [
                'type' => 'cpu_high',
                'level' => self::ALERT_WARNING,
                'message' => 'CPU使用率过高: ' . $metrics['system']['cpu']['usage'] . '%',
                'value' => $metrics['system']['cpu']['usage'],
                'threshold' => 80
            ];
        }

        // 内存使用率告警
        if (isset($metrics['system']['memory']['usage_percent']) && $metrics['system']['memory']['usage_percent'] > 85) {
            $alerts[] = [
                'type' => 'memory_high',
                'level' => self::ALERT_WARNING,
                'message' => '内存使用率过高: ' . $metrics['system']['memory']['usage_percent'] . '%',
                'value' => $metrics['system']['memory']['usage_percent'],
                'threshold' => 85
            ];
        }

        // 磁盘使用率告警
        if (isset($metrics['system']['disk']['usage_percent']) && $metrics['system']['disk']['usage_percent'] > 90) {
            $alerts[] = [
                'type' => 'disk_high',
                'level' => self::ALERT_ERROR,
                'message' => '磁盘使用率过高: ' . $metrics['system']['disk']['usage_percent'] . '%',
                'value' => $metrics['system']['disk']['usage_percent'],
                'threshold' => 90
            ];
        }

        // 数据库连接数告警
        if (isset($metrics['database']['connections']['current']) && $metrics['database']['connections']['current'] > 100) {
            $alerts[] = [
                'type' => 'db_connections_high',
                'level' => self::ALERT_WARNING,
                'message' => '数据库连接数过高: ' . $metrics['database']['connections']['current'],
                'value' => $metrics['database']['connections']['current'],
                'threshold' => 100
            ];
        }

        // 错误率告警
        if (isset($metrics['performance']['error_rate']) && $metrics['performance']['error_rate'] > 5) {
            $alerts[] = [
                'type' => 'error_rate_high',
                'level' => self::ALERT_ERROR,
                'message' => '错误率过高: ' . $metrics['performance']['error_rate'] . '%',
                'value' => $metrics['performance']['error_rate'],
                'threshold' => 5
            ];
        }

        // 发送告警通知
        foreach ($alerts as $alert) {
            $this->sendAlert($alert);
        }

        return $alerts;
    }

    /**
     * 获取CPU信息
     */
    private function getCpuInfo(): array
    {
        $cpuUsage = 0;
        
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            $cpuUsage = $load[0] * 100; // 简化计算
        }

        return [
            'usage' => min(100, max(0, $cpuUsage)),
            'cores' => $this->getCpuCores()
        ];
    }

    /**
     * 获取内存信息
     */
    private function getMemoryInfo(): array
    {
        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);
        $memoryLimit = $this->parseSize(ini_get('memory_limit'));

        return [
            'used' => $this->formatBytes($memoryUsage),
            'peak' => $this->formatBytes($memoryPeak),
            'limit' => $this->formatBytes($memoryLimit),
            'usage_percent' => $memoryLimit > 0 ? round(($memoryUsage / $memoryLimit) * 100, 2) : 0
        ];
    }

    /**
     * 获取磁盘信息
     */
    private function getDiskInfo(): array
    {
        $path = '/';
        $totalBytes = disk_total_space($path);
        $freeBytes = disk_free_space($path);
        $usedBytes = $totalBytes - $freeBytes;

        return [
            'total' => $this->formatBytes($totalBytes),
            'used' => $this->formatBytes($usedBytes),
            'free' => $this->formatBytes($freeBytes),
            'usage_percent' => $totalBytes > 0 ? round(($usedBytes / $totalBytes) * 100, 2) : 0
        ];
    }

    /**
     * 获取网络信息
     */
    private function getNetworkInfo(): array
    {
        // 简化实现，实际应该读取 /proc/net/dev
        return [
            'connections' => $this->getNetworkConnections(),
            'bandwidth_in' => '0 MB/s',
            'bandwidth_out' => '0 MB/s'
        ];
    }

    /**
     * 格式化字节数
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * 解析大小字符串
     */
    private function parseSize(string $size): int
    {
        $unit = preg_replace('/[^bkmgtpezy]/i', '', $size);
        $size = preg_replace('/[^0-9\.]/', '', $size);
        
        if ($unit) {
            return round($size * pow(1024, stripos('bkmgtpezy', $unit[0])));
        }
        
        return (int)$size;
    }

    /**
     * 计算命中率
     */
    private function calculateHitRate(int $hits, int $misses): float
    {
        $total = $hits + $misses;
        return $total > 0 ? round(($hits / $total) * 100, 2) : 0;
    }

    /**
     * 保存监控数据
     */
    private function saveMetrics(array $metrics): void
    {
        try {
            Db::name('system_metrics')->insert([
                'type' => 'system',
                'data' => json_encode($metrics, JSON_UNESCAPED_UNICODE),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            Log::error('保存监控数据失败：' . $e->getMessage());
        }
    }

    /**
     * 发送告警
     */
    private function sendAlert(array $alert): void
    {
        try {
            // 防止告警风暴
            $alertKey = "alert:" . $alert['type'];
            if (Cache::get($alertKey)) {
                return;
            }
            Cache::set($alertKey, true, 300); // 5分钟内不重复告警

            // 记录告警
            Db::name('system_alerts')->insert([
                'type' => $alert['type'],
                'level' => $alert['level'],
                'message' => $alert['message'],
                'data' => json_encode($alert, JSON_UNESCAPED_UNICODE),
                'created_at' => date('Y-m-d H:i:s')
            ]);

            // 发送通知（邮件、短信、钉钉等）
            $this->sendAlertNotification($alert);

        } catch (\Exception $e) {
            Log::error('发送告警失败：' . $e->getMessage());
        }
    }

    /**
     * 发送告警通知
     */
    private function sendAlertNotification(array $alert): void
    {
        // 根据告警级别选择通知方式
        switch ($alert['level']) {
            case self::ALERT_CRITICAL:
                // 发送短信和邮件
                $this->sendSmsAlert($alert);
                $this->sendEmailAlert($alert);
                break;
            case self::ALERT_ERROR:
                // 发送邮件
                $this->sendEmailAlert($alert);
                break;
            case self::ALERT_WARNING:
                // 发送钉钉通知
                $this->sendDingTalkAlert($alert);
                break;
        }
    }

    /**
     * 发送短信告警
     */
    private function sendSmsAlert(array $alert): void
    {
        // 实现短信告警
    }

    /**
     * 发送邮件告警
     */
    private function sendEmailAlert(array $alert): void
    {
        // 实现邮件告警
    }

    /**
     * 发送钉钉告警
     */
    private function sendDingTalkAlert(array $alert): void
    {
        // 实现钉钉告警
    }

    // 其他辅助方法的简化实现
    private function getSystemUptime(): string { return '0 days'; }
    private function getCpuCores(): int { return 1; }
    private function getAverageResponseTime(): float { return 0.0; }
    private function getThroughput(): int { return 0; }
    private function getErrorRate(): float { return 0.0; }
    private function getConcurrentUsers(): int { return 0; }
    private function getActiveSessions(): int { return 0; }
    private function getPhpProcesses(): int { return 1; }
    private function calculateQueryCacheHitRate(array $status): float { return 0.0; }
    private function getQueueProcessingRate(): float { return 0.0; }
    private function getTradingVolume24h(): float { return 0.0; }
    private function getTotalBalance(): float { return 0.0; }
    private function getDepositsToday(): float { return 0.0; }
    private function getWithdrawsToday(): float { return 0.0; }
    private function getApiCallsToday(): int { return 0; }
    private function getErrorsToday(): int { return 0; }
    private function getSuspiciousIPs(): array { return []; }
    private function getBlockedIPs(): array { return []; }
    private function getRiskTransactions(): int { return 0; }
    private function getNetworkConnections(): int { return 0; }
}
