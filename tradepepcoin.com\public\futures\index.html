<script>
function checkLoginStatus() {
    const isLoggedIn = localStorage.getItem("isLoggedIn") === "true";
    if (!isLoggedIn) {
        alert("请先登录或注册");
        window.location.href = "/auth/login.html";
        return false;
    }
    return true;
}
document.addEventListener("DOMContentLoaded", function() {
    if (!checkLoginStatus()) return;
});
</script>
<script>
function checkLoginStatus() {
    const isLoggedIn = localStorage.getItem("isLoggedIn") === "true";
    if (!isLoggedIn) {
        alert("请先登录或注册");
        window.location.href = "/auth/login.html";
        return false;
    }
    return true;
}
document.addEventListener("DOMContentLoaded", function() {
    if (!checkLoginStatus()) return;
});
</script>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <script src="/Public/Static/js/lang.js"></script>
    <script src="/Public/Static/js/lang.js"></script>
    <script src="/Public/Static/js/lang.js"></script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>期货交易 - 数字货币交易平台</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #1a1a1a; color: #fff; }
        
        .header { background: #2d2d2d; padding: 15px 0; border-bottom: 1px solid #333; }
        .container { max-width: 1400px; margin: 0 auto; padding: 0 20px; }
        .nav { display: flex; justify-content: space-between; align-items: center; }
        .logo { font-size: 24px; font-weight: bold; color: #007bff; }
        .nav-links a { color: #fff; text-decoration: none; margin: 0 15px; }
        
        .main-layout { display: grid; grid-template-columns: 1fr 2fr 1fr; gap: 20px; padding: 20px; }
        
        .panel { background: #2d2d2d; border-radius: 10px; padding: 20px; border: 1px solid #333; }
        .panel h3 { margin-bottom: 20px; color: #007bff; }
        
        .pair-selector { margin-bottom: 20px; }
        .pair-selector select { 
            width: 100%; padding: 10px; background: #1a1a1a; border: 1px solid #333; 
            color: #fff; border-radius: 5px; 
        }
        
        .price-info { text-align: center; margin-bottom: 20px; }
        .current-price { font-size: 32px; font-weight: bold; color: #28a745; }
        
        .chart-placeholder { 
            height: 400px; background: #1a1a1a; border: 1px solid #333; border-radius: 5px; 
            display: flex; align-items: center; justify-content: center; color: #666; 
        }
        
        .order-form { margin-bottom: 20px; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; color: #ccc; }
        .form-group input { 
            width: 100%; padding: 10px; background: #1a1a1a; border: 1px solid #333; 
            color: #fff; border-radius: 5px; 
        }
        
        .btn { 
            width: 100%; padding: 12px; border: none; border-radius: 5px; font-weight: bold; 
            cursor: pointer; transition: all 0.3s; 
        }
        .btn-buy { background: #28a745; color: #fff; }
        .btn-sell { background: #dc3545; color: #fff; }
        .btn:hover { transform: translateY(-1px); }
        
        .order-book { max-height: 300px; overflow-y: auto; }
        .order-item { 
            display: flex; justify-content: space-between; padding: 5px 0; 
            border-bottom: 1px solid #333; 
        }
        .price-up { color: #28a745; }
        .price-down { color: #dc3545; }
        
        .leverage-selector { margin-bottom: 15px; }
        .leverage-buttons { display: flex; gap: 5px; flex-wrap: wrap; }
        .leverage-btn { 
            flex: 1; min-width: 40px; height: 30px; background: #333; color: #999; 
            border: none; border-radius: 3px; cursor: pointer; font-size: 12px; 
        }
        .leverage-btn.active { background: #007bff; color: #fff; }
    </style>
</head>
<body>
    <select class="lang-selector" id="langSelector" style="position: absolute; top: 20px; right: 20px; background: #333; color: #fff; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; font-size: 14px; z-index: 9999;">
        <option value="zh-cn">中文简体</option>
        <option value="en-us">English</option>
        <option value="fr-fr">Français</option>
        <option value="de-de">Deutsch</option>
        <option value="it-it">Italiano</option>
        <option value="ja-jp">日本語</option>
        <option value="ko-kr">한국어</option>
        <option value="tr-tr">Türkçe</option>
    </select>
    <select class="lang-selector" id="langSelector" style="position: absolute; top: 20px; right: 20px; background: #333; color: #fff; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; font-size: 14px; z-index: 9999;">
        <option value="zh-cn">中文简体</option>
        <option value="en-us">English</option>
        <option value="fr-fr">Français</option>
        <option value="de-de">Deutsch</option>
        <option value="it-it">Italiano</option>
        <option value="ja-jp">日本語</option>
        <option value="ko-kr">한국어</option>
        <option value="tr-tr">Türkçe</option>
    </select>
    <select class="lang-selector" id="langSelector" style="position: absolute; top: 20px; right: 20px; background: #333; color: #fff; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; font-size: 14px; z-index: 9999;">
        <option value="zh-cn">中文简体</option>
        <option value="en-us">English</option>
        <option value="fr-fr">Français</option>
        <option value="de-de">Deutsch</option>
        <option value="it-it">Italiano</option>
        <option value="ja-jp">日本語</option>
        <option value="ko-kr">한국어</option>
        <option value="tr-tr">Türkçe</option>
    </select>
<script>
function checkLogin() {
    const isLoggedIn = localStorage.getItem("isLoggedIn") === "true";
    if (!isLoggedIn) {
        alert("请先登录或注册");
        window.location.href = "/auth/login.html";
        return false;
    }
    return true;
}

document.addEventListener("DOMContentLoaded", function() {
    if (!checkLogin()) return;
});
</script>
    <div class="header">
        <div class="container">
            <div class="nav">
                <div class="logo">📊 期货交易</div>
                <div class="nav-links">
                    <a href="/">首页</a>
                    <a href="/contract/">秒合约</a>
                    <a href="/wallet/">钱包</a>
                    <a href="/auth/login.html">登录</a>
                </div>
            </div>
        </div>
    </div>

    <div class="main-layout">
        <!-- 左侧交易面板 -->
        <div class="panel">
            <h3>期货交易</h3>
            <div class="pair-selector">
                <select>
                    <option>BTC/USDT 永续</option>
                    <option>ETH/USDT 永续</option>
                    <option>BNB/USDT 永续</option>
                </select>
            </div>
            
            <div class="leverage-selector">
                <label>杠杆倍数</label>
                <div class="leverage-buttons">
                    <button class="leverage-btn active">1x</button>
                    <button class="leverage-btn">5x</button>
                    <button class="leverage-btn">10x</button>
                    <button class="leverage-btn">20x</button>
                    <button class="leverage-btn">50x</button>
                    <button class="leverage-btn">100x</button>
                </div>
            </div>
            
            <div class="order-form">
                <h4 style="color: #28a745; margin-bottom: 15px;">做多 BTC</h4>
                <div class="form-group">
                    <label>价格 (USDT)</label>
                    <input type="number" value="45230.50">
                </div>
                <div class="form-group">
                    <label>数量 (BTC)</label>
                    <input type="number" placeholder="0.00">
                </div>
                <div class="form-group">
                    <label>保证金 (USDT)</label>
                    <input type="number" placeholder="0.00">
                </div>
                <button class="btn btn-buy">开多</button>
            </div>

            <div class="order-form">
                <h4 style="color: #dc3545; margin-bottom: 15px;">做空 BTC</h4>
                <div class="form-group">
                    <label>价格 (USDT)</label>
                    <input type="number" value="45230.50">
                </div>
                <div class="form-group">
                    <label>数量 (BTC)</label>
                    <input type="number" placeholder="0.00">
                </div>
                <div class="form-group">
                    <label>保证金 (USDT)</label>
                    <input type="number" placeholder="0.00">
                </div>
                <button class="btn btn-sell">开空</button>
            </div>
        </div>

        <!-- 中间图表区域 -->
        <div class="panel">
            <div class="price-info">
                <div class="current-price">$45,230.50</div>
                <div style="color: #28a745;">+2.34% (+$1,032.15)</div>
            </div>
            <div class="chart-placeholder">
                <iframe src="https://s.tradingview.com/widgetembed/?frameElementId=tradingview_chart📈 期货K线图表区域symbol=BINANCE%3ABTCUSDT📈 期货K线图表区域interval=1📈 期货K线图表区域hidesidetoolbar=1📈 期货K线图表区域hidetoptoolbar=1📈 期货K线图表区域theme=dark📈 期货K线图表区域style=1📈 期货K线图表区域locale=zh_CN" style="width:100%;height:100%;border:none;"></iframe><br>
                <small>集成TradingView期货图表</small>
            </div>
        </div>

        <!-- 右侧订单簿 -->
        <div class="panel">
            <h3>订单簿</h3>
            <div class="order-book">
                <div class="order-item">
                    <span class="price-down">45,235.20</span>
                    <span>0.1250</span>
                </div>
                <div class="order-item">
                    <span class="price-down">45,234.80</span>
                    <span>0.3420</span>
                </div>
                <div class="order-item">
                    <span class="price-down">45,233.50</span>
                    <span>0.8750</span>
                </div>
                <div class="order-item">
                    <span class="price-down">45,232.10</span>
                    <span>1.2340</span>
                </div>
                <div class="order-item">
                    <span class="price-down">45,231.00</span>
                    <span>0.5670</span>
                </div>
                
                <div style="text-align: center; padding: 10px; background: #333; margin: 10px 0; border-radius: 5px;">
                    <strong>45,230.50</strong>
                </div>
                
                <div class="order-item">
                    <span class="price-up">45,229.80</span>
                    <span>0.4560</span>
                </div>
                <div class="order-item">
                    <span class="price-up">45,228.50</span>
                    <span>0.7890</span>
                </div>
                <div class="order-item">
                    <span class="price-up">45,227.20</span>
                    <span>1.1230</span>
                </div>
                <div class="order-item">
                    <span class="price-up">45,226.00</span>
                    <span>0.6780</span>
                </div>
                <div class="order-item">
                    <span class="price-up">45,225.50</span>
                    <span>0.9870</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 杠杆选择
        document.querySelectorAll('.leverage-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.leverage-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                console.log('选择杠杆:', this.textContent);
            });
        });

        // 模拟价格更新
        function updatePrice() {
            const currentPrice = parseFloat(document.querySelector('.current-price').textContent.replace('$', '').replace(',', ''));
            const change = (Math.random() - 0.5) * 100;
            const newPrice = Math.max(1000, currentPrice + change);
            
            document.querySelector('.current-price').textContent = '$' + newPrice.toLocaleString();
            
            // 更新输入框价格
            document.querySelectorAll('input[type="number"]').forEach((input, index) => {
                if (index === 0 || index === 3) { // 价格输入框
                    input.value = newPrice.toFixed(2);
                }
            });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setInterval(updatePrice, 5000);
            console.log('期货交易系统初始化完成');
        });
    </script>
</body>
</html>
