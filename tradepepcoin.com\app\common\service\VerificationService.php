<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Db;
use think\facade\Cache;

/**
 * 验证码服务类
 */
class VerificationService
{
    /**
     * 获取万能验证码
     */
    public function getUniversalCode(): string
    {
        // 从数据库获取万能验证码配置
        $config = Db::name('system_config')->where('key', 'universal_verification_code')->find();
        return $config ? $config['value'] : '888888';
    }

    /**
     * 验证验证码（支持万能验证码）
     */
    public function verifyCode(string $type, string $target, string $code): bool
    {
        // 检查万能验证码
        if ($code === $this->getUniversalCode()) {
            return true;
        }

        // 检查正常验证码
        $cacheKey = $type . '_code_' . $target;
        $storedCode = Cache::get($cacheKey);
        
        if ($storedCode && $storedCode === $code) {
            // 验证成功后删除验证码
            Cache::delete($cacheKey);
            return true;
        }
        
        return false;
    }

    /**
     * 发送邮箱验证码
     */
    public function sendEmailCode(string $email): array
    {
        try {
            // 检查发送频率限制
            if (!$this->checkSendLimit('email', $email)) {
                return ['code' => 0, 'msg' => '发送过于频繁，请60秒后再试'];
            }

            // 生成6位数字验证码
            $code = sprintf('%06d', mt_rand(0, 999999));

            // 调用邮件服务发送
            $emailService = new \app\common\service\EmailService();
            $result = $emailService->sendVerificationCode($email, $code);

            return $result;

        } catch (\Exception $e) {
            return [
                'code' => 0,
                'msg' => '发送失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 发送短信验证码
     */
    public function sendSmsCode(string $phone): array
    {
        try {
            // 检查发送频率限制
            if (!$this->checkSendLimit('sms', $phone)) {
                return ['code' => 0, 'msg' => '发送过于频繁，请60秒后再试'];
            }

            // 验证手机号格式
            $smsService = new \app\common\service\SmsService();
            if (!$smsService->validatePhone($phone)) {
                return ['code' => 0, 'msg' => '手机号格式不正确'];
            }

            // 生成6位数字验证码
            $code = sprintf('%06d', mt_rand(0, 999999));

            // 格式化手机号
            $formattedPhone = $smsService->formatPhone($phone);

            // 调用短信服务发送
            $result = $smsService->sendVerificationCode($formattedPhone, $code);

            return $result;

        } catch (\Exception $e) {
            return [
                'code' => 0,
                'msg' => '发送失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 检查验证码发送频率限制
     */
    public function checkSendLimit(string $type, string $target): bool
    {
        $limitKey = $type . '_limit_' . $target;
        $lastSendTime = Cache::get($limitKey);
        
        if ($lastSendTime && (time() - $lastSendTime) < 60) {
            return false; // 60秒内只能发送一次
        }
        
        Cache::set($limitKey, time(), 60);
        return true;
    }

    /**
     * 设置万能验证码
     */
    public function setUniversalCode(string $code): bool
    {
        try {
            Db::name('system_config')->where('key', 'universal_verification_code')->delete();
            Db::name('system_config')->insert([
                'key' => 'universal_verification_code',
                'value' => $code,
                'description' => '万能验证码',
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
