<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 通知模型
 */
class Notification extends Model
{
    protected $table = 'notifications';

    // 设置字段信息
    protected $schema = [
        'id' => 'int',
        'user_id' => 'int',
        'type' => 'string',
        'title' => 'string',
        'content' => 'text',
        'data' => 'json',
        'read_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // JSON字段
    protected $json = ['data'];

    // 通知类型常量
    const TYPE_SYSTEM = 'system';           // 系统通知
    const TYPE_TRADE = 'trade';             // 交易通知
    const TYPE_DEPOSIT = 'deposit';         // 充值通知
    const TYPE_WITHDRAW = 'withdraw';       // 提现通知
    const TYPE_SECURITY = 'security';       // 安全通知
    const TYPE_PROMOTION = 'promotion';     // 推广通知
    const TYPE_ORDER = 'order';             // 订单通知
    const TYPE_COMMISSION = 'commission';   // 佣金通知

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 创建通知
     */
    public static function createNotification(int $userId, string $type, string $title, string $content, array $data = []): bool
    {
        $notification = new self();
        $notification->user_id = $userId;
        $notification->type = $type;
        $notification->title = $title;
        $notification->content = $content;
        $notification->data = $data;

        return $notification->save();
    }

    /**
     * 标记为已读
     */
    public function markAsRead(): bool
    {
        if ($this->read_at) {
            return true;
        }

        $this->read_at = date('Y-m-d H:i:s');
        return $this->save();
    }

    /**
     * 批量标记为已读
     */
    public static function markAsReadByUser(int $userId, array $ids = []): bool
    {
        $where = ['user_id' => $userId, 'read_at' => null];
        
        if (!empty($ids)) {
            $where[] = ['id', 'in', $ids];
        }

        return self::where($where)->update(['read_at' => date('Y-m-d H:i:s')]);
    }

    /**
     * 获取未读数量
     */
    public static function getUnreadCount(int $userId, string $type = ''): int
    {
        $where = ['user_id' => $userId, 'read_at' => null];
        
        if ($type) {
            $where['type'] = $type;
        }

        return self::where($where)->count();
    }

    /**
     * 获取用户通知列表
     */
    public static function getUserNotifications(int $userId, string $type = '', int $page = 1, int $limit = 20): array
    {
        $where = ['user_id' => $userId];
        
        if ($type) {
            $where['type'] = $type;
        }

        $notifications = self::where($where)
                            ->order('created_at', 'desc')
                            ->paginate([
                                'list_rows' => $limit,
                                'page' => $page
                            ]);

        return [
            'data' => $notifications->items(),
            'total' => $notifications->total(),
            'page' => $page,
            'limit' => $limit,
            'unread_count' => self::getUnreadCount($userId)
        ];
    }

    /**
     * 删除过期通知
     */
    public static function deleteExpiredNotifications(int $days = 30): int
    {
        $expiredDate = date('Y-m-d H:i:s', time() - $days * 86400);
        
        return self::where('created_at', '<', $expiredDate)->delete();
    }

    /**
     * 获取通知类型文本
     */
    public function getTypeTextAttr(): string
    {
        $typeTexts = [
            self::TYPE_SYSTEM => '系统通知',
            self::TYPE_TRADE => '交易通知',
            self::TYPE_DEPOSIT => '充值通知',
            self::TYPE_WITHDRAW => '提现通知',
            self::TYPE_SECURITY => '安全通知',
            self::TYPE_PROMOTION => '推广通知',
            self::TYPE_ORDER => '订单通知',
            self::TYPE_COMMISSION => '佣金通知'
        ];

        return $typeTexts[$this->type] ?? '未知类型';
    }

    /**
     * 获取是否已读
     */
    public function getIsReadAttr(): bool
    {
        return !empty($this->read_at);
    }

    /**
     * 获取格式化时间
     */
    public function getCreatedTimeAttr(): string
    {
        return date('Y-m-d H:i:s', strtotime($this->created_at));
    }

    /**
     * 获取相对时间
     */
    public function getRelativeTimeAttr(): string
    {
        $time = strtotime($this->created_at);
        $diff = time() - $time;

        if ($diff < 60) {
            return '刚刚';
        } elseif ($diff < 3600) {
            return floor($diff / 60) . '分钟前';
        } elseif ($diff < 86400) {
            return floor($diff / 3600) . '小时前';
        } elseif ($diff < 2592000) {
            return floor($diff / 86400) . '天前';
        } else {
            return date('Y-m-d', $time);
        }
    }

    /**
     * 搜索器：按类型
     */
    public function searchTypeAttr($query, $value)
    {
        if ($value) {
            $query->where('type', $value);
        }
    }

    /**
     * 搜索器：按用户
     */
    public function searchUserIdAttr($query, $value)
    {
        if ($value) {
            $query->where('user_id', $value);
        }
    }

    /**
     * 搜索器：按已读状态
     */
    public function searchIsReadAttr($query, $value)
    {
        if ($value === '1') {
            $query->whereNotNull('read_at');
        } elseif ($value === '0') {
            $query->whereNull('read_at');
        }
    }

    /**
     * 搜索器：按关键词
     */
    public function searchKeywordAttr($query, $value)
    {
        if ($value) {
            $query->where('title|content', 'like', "%{$value}%");
        }
    }
}
