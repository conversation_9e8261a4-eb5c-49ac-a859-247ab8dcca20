<?php
declare (strict_types = 1);

namespace app\index\controller;

use app\BaseController;
use app\common\model\Notification as NotificationModel;
use think\facade\Session;
use think\facade\View;

/**
 * 通知控制器
 */
class Notification extends BaseController
{
    /**
     * 通知列表
     */
    public function index()
    {
        $userId = Session::get('user_id');
        if (!$userId) {
            $this->redirect('/auth/login');
        }

        $type = input('type', '');
        $page = input('page/d', 1);
        $limit = input('limit/d', 20);

        $result = NotificationModel::getUserNotifications($userId, $type, $page, $limit);

        if ($this->request->isAjax()) {
            return $this->success($result);
        }

        View::assign([
            'notifications' => $result['data'],
            'total' => $result['total'],
            'page' => $page,
            'limit' => $limit,
            'unread_count' => $result['unread_count'],
            'current_type' => $type,
            'title' => '我的通知'
        ]);

        return View::fetch();
    }

    /**
     * 通知详情
     */
    public function detail()
    {
        $userId = Session::get('user_id');
        if (!$userId) {
            return $this->error('请先登录');
        }

        $id = input('id/d', 0);
        $notification = NotificationModel::where('id', $id)
                                        ->where('user_id', $userId)
                                        ->find();

        if (!$notification) {
            return $this->error('通知不存在');
        }

        // 标记为已读
        $notification->markAsRead();

        if ($this->request->isAjax()) {
            return $this->success([
                'id' => $notification->id,
                'type' => $notification->type,
                'type_text' => $notification->type_text,
                'title' => $notification->title,
                'content' => $notification->content,
                'data' => $notification->data,
                'is_read' => $notification->is_read,
                'created_at' => $notification->created_at,
                'relative_time' => $notification->relative_time
            ]);
        }

        View::assign([
            'notification' => $notification,
            'title' => '通知详情'
        ]);

        return View::fetch();
    }

    /**
     * 标记为已读
     */
    public function markAsRead()
    {
        if (!$this->request->isPost()) {
            return $this->error('请求方式错误');
        }

        $userId = Session::get('user_id');
        if (!$userId) {
            return $this->error('请先登录');
        }

        $ids = input('ids/a', []);
        
        if (empty($ids)) {
            // 标记所有未读为已读
            $result = NotificationModel::markAsReadByUser($userId);
        } else {
            // 标记指定通知为已读
            $result = NotificationModel::markAsReadByUser($userId, $ids);
        }

        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->error('操作失败');
        }
    }

    /**
     * 删除通知
     */
    public function delete()
    {
        if (!$this->request->isPost()) {
            return $this->error('请求方式错误');
        }

        $userId = Session::get('user_id');
        if (!$userId) {
            return $this->error('请先登录');
        }

        $ids = input('ids/a', []);
        if (empty($ids)) {
            return $this->error('请选择要删除的通知');
        }

        $count = NotificationModel::where('user_id', $userId)
                                 ->where('id', 'in', $ids)
                                 ->delete();

        if ($count > 0) {
            return $this->success("成功删除 {$count} 条通知");
        } else {
            return $this->error('删除失败');
        }
    }

    /**
     * 获取未读数量
     */
    public function unreadCount()
    {
        $userId = Session::get('user_id');
        if (!$userId) {
            return $this->error('请先登录');
        }

        $type = input('type', '');
        $count = NotificationModel::getUnreadCount($userId, $type);

        return $this->success(['count' => $count]);
    }

    /**
     * 通知设置
     */
    public function settings()
    {
        $userId = Session::get('user_id');
        if (!$userId) {
            $this->redirect('/auth/login');
        }

        if ($this->request->isPost()) {
            $data = $this->request->post();
            
            // 保存通知设置
            $settings = [
                'email_trade' => $data['email_trade'] ?? 0,
                'email_deposit' => $data['email_deposit'] ?? 0,
                'email_withdraw' => $data['email_withdraw'] ?? 0,
                'email_security' => $data['email_security'] ?? 1,
                'sms_trade' => $data['sms_trade'] ?? 0,
                'sms_deposit' => $data['sms_deposit'] ?? 0,
                'sms_withdraw' => $data['sms_withdraw'] ?? 0,
                'sms_security' => $data['sms_security'] ?? 1,
                'internal_trade' => $data['internal_trade'] ?? 1,
                'internal_deposit' => $data['internal_deposit'] ?? 1,
                'internal_withdraw' => $data['internal_withdraw'] ?? 1,
                'internal_security' => $data['internal_security'] ?? 1,
                'internal_promotion' => $data['internal_promotion'] ?? 1,
                'internal_system' => $data['internal_system'] ?? 1
            ];

            // 保存到用户设置表或缓存
            cache("notification_settings_{$userId}", $settings, 86400 * 30);

            return $this->success('设置保存成功');
        }

        // 获取当前设置
        $settings = cache("notification_settings_{$userId}") ?: [
            'email_trade' => 0,
            'email_deposit' => 1,
            'email_withdraw' => 1,
            'email_security' => 1,
            'sms_trade' => 0,
            'sms_deposit' => 0,
            'sms_withdraw' => 1,
            'sms_security' => 1,
            'internal_trade' => 1,
            'internal_deposit' => 1,
            'internal_withdraw' => 1,
            'internal_security' => 1,
            'internal_promotion' => 1,
            'internal_system' => 1
        ];

        View::assign([
            'settings' => $settings,
            'title' => '通知设置'
        ]);

        return View::fetch();
    }

    /**
     * 获取通知类型统计
     */
    public function typeStats()
    {
        $userId = Session::get('user_id');
        if (!$userId) {
            return $this->error('请先登录');
        }

        $types = [
            NotificationModel::TYPE_SYSTEM,
            NotificationModel::TYPE_TRADE,
            NotificationModel::TYPE_DEPOSIT,
            NotificationModel::TYPE_WITHDRAW,
            NotificationModel::TYPE_SECURITY,
            NotificationModel::TYPE_PROMOTION,
            NotificationModel::TYPE_COMMISSION
        ];

        $stats = [];
        foreach ($types as $type) {
            $stats[] = [
                'type' => $type,
                'type_text' => (new NotificationModel(['type' => $type]))->type_text,
                'total' => NotificationModel::where('user_id', $userId)->where('type', $type)->count(),
                'unread' => NotificationModel::getUnreadCount($userId, $type)
            ];
        }

        return $this->success($stats);
    }
}
