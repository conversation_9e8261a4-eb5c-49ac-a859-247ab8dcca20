<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 跨链桥模型
 */
class CrossChainBridge extends Model
{
    protected $table = 'cross_chain_bridges';

    // 设置字段信息
    protected $schema = [
        'id' => 'int',
        'bridge_id' => 'string',
        'name' => 'string',
        'description' => 'text',
        'source_network' => 'string',
        'target_network' => 'string',
        'bridge_type' => 'string',
        'supported_tokens' => 'json',
        'min_amount' => 'decimal',
        'max_amount' => 'decimal',
        'daily_limit' => 'decimal',
        'fee_rate' => 'decimal',
        'confirmation_blocks' => 'int',
        'contract_address_source' => 'string',
        'contract_address_target' => 'string',
        'validator_nodes' => 'json',
        'security_threshold' => 'decimal',
        'total_volume' => 'decimal',
        'total_transactions' => 'int',
        'success_rate' => 'decimal',
        'average_time' => 'int',
        'liquidity_source' => 'decimal',
        'liquidity_target' => 'decimal',
        'status' => 'int',
        'is_maintenance' => 'int',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // JSON字段
    protected $json = ['supported_tokens', 'validator_nodes'];

    // 桥接状态
    const STATUS_INACTIVE = 0;   // 未激活
    const STATUS_ACTIVE = 1;     // 活跃
    const STATUS_PAUSED = 2;     // 暂停
    const STATUS_DEPRECATED = 3; // 已弃用

    // 桥接类型
    const TYPE_LOCK_MINT = 'lock_mint';
    const TYPE_BURN_MINT = 'burn_mint';
    const TYPE_ATOMIC_SWAP = 'atomic_swap';
    const TYPE_LIQUIDITY = 'liquidity';

    /**
     * 关联跨链交易
     */
    public function crossChainTransactions()
    {
        return $this->hasMany(CrossChainTransaction::class, 'bridge_id', 'bridge_id');
    }

    /**
     * 关联源网络
     */
    public function sourceNetwork()
    {
        return $this->belongsTo(BlockchainNetwork::class, 'source_network', 'network_code');
    }

    /**
     * 关联目标网络
     */
    public function targetNetwork()
    {
        return $this->belongsTo(BlockchainNetwork::class, 'target_network', 'network_code');
    }

    /**
     * 激活桥接
     */
    public function activate(): bool
    {
        if ($this->status !== self::STATUS_INACTIVE) {
            return false;
        }

        $this->status = self::STATUS_ACTIVE;
        return $this->save();
    }

    /**
     * 暂停桥接
     */
    public function pause(): bool
    {
        if ($this->status !== self::STATUS_ACTIVE) {
            return false;
        }

        $this->status = self::STATUS_PAUSED;
        return $this->save();
    }

    /**
     * 恢复桥接
     */
    public function resume(): bool
    {
        if ($this->status !== self::STATUS_PAUSED) {
            return false;
        }

        $this->status = self::STATUS_ACTIVE;
        return $this->save();
    }

    /**
     * 弃用桥接
     */
    public function deprecate(): bool
    {
        $this->status = self::STATUS_DEPRECATED;
        return $this->save();
    }

    /**
     * 开启维护模式
     */
    public function enableMaintenance(): bool
    {
        $this->is_maintenance = 1;
        return $this->save();
    }

    /**
     * 关闭维护模式
     */
    public function disableMaintenance(): bool
    {
        $this->is_maintenance = 0;
        return $this->save();
    }

    /**
     * 检查是否支持代币
     */
    public function supportsToken(string $token): bool
    {
        return in_array($token, $this->supported_tokens ?: []);
    }

    /**
     * 添加支持的代币
     */
    public function addSupportedToken(string $token): bool
    {
        $tokens = $this->supported_tokens ?: [];
        
        if (!in_array($token, $tokens)) {
            $tokens[] = $token;
            $this->supported_tokens = $tokens;
            return $this->save();
        }
        
        return true;
    }

    /**
     * 移除支持的代币
     */
    public function removeSupportedToken(string $token): bool
    {
        $tokens = $this->supported_tokens ?: [];
        $index = array_search($token, $tokens);
        
        if ($index !== false) {
            unset($tokens[$index]);
            $this->supported_tokens = array_values($tokens);
            return $this->save();
        }
        
        return true;
    }

    /**
     * 检查转账金额是否在限制范围内
     */
    public function isAmountValid(float $amount): bool
    {
        if ($amount < $this->min_amount) {
            return false;
        }
        
        if ($this->max_amount > 0 && $amount > $this->max_amount) {
            return false;
        }
        
        return true;
    }

    /**
     * 检查日限额
     */
    public function checkDailyLimit(float $amount): bool
    {
        if ($this->daily_limit <= 0) {
            return true;
        }

        $todayVolume = $this->getTodayVolume();
        return ($todayVolume + $amount) <= $this->daily_limit;
    }

    /**
     * 获取今日交易量
     */
    public function getTodayVolume(): float
    {
        $today = date('Y-m-d');
        
        return $this->crossChainTransactions()
                   ->where('created_at', '>=', $today . ' 00:00:00')
                   ->where('created_at', '<=', $today . ' 23:59:59')
                   ->where('status', CrossChainTransaction::STATUS_COMPLETED)
                   ->sum('amount');
    }

    /**
     * 更新统计数据
     */
    public function updateStatistics(float $amount, bool $success, int $duration): bool
    {
        $this->total_volume += $amount;
        $this->total_transactions++;
        
        // 更新成功率
        $successCount = $this->crossChainTransactions()
                            ->where('status', CrossChainTransaction::STATUS_COMPLETED)
                            ->count();
        
        $this->success_rate = ($successCount / $this->total_transactions) * 100;
        
        // 更新平均时间
        $this->average_time = (($this->average_time * ($this->total_transactions - 1)) + $duration) / $this->total_transactions;
        
        return $this->save();
    }

    /**
     * 更新流动性
     */
    public function updateLiquidity(float $sourceLiquidity, float $targetLiquidity): bool
    {
        $this->liquidity_source = $sourceLiquidity;
        $this->liquidity_target = $targetLiquidity;
        
        return $this->save();
    }

    /**
     * 获取桥接健康度
     */
    public function getHealthScore(): array
    {
        $score = 100;
        $issues = [];

        // 检查成功率
        if ($this->success_rate < 95) {
            $score -= 20;
            $issues[] = '成功率较低';
        }

        // 检查流动性
        if ($this->bridge_type === self::TYPE_LIQUIDITY) {
            if ($this->liquidity_source < $this->max_amount * 10) {
                $score -= 15;
                $issues[] = '源链流动性不足';
            }
            
            if ($this->liquidity_target < $this->max_amount * 10) {
                $score -= 15;
                $issues[] = '目标链流动性不足';
            }
        }

        // 检查验证节点
        $activeValidators = count($this->validator_nodes ?: []);
        if ($activeValidators < 3) {
            $score -= 25;
            $issues[] = '验证节点数量不足';
        }

        // 检查平均处理时间
        if ($this->average_time > 1800) { // 30分钟
            $score -= 10;
            $issues[] = '处理时间较长';
        }

        return [
            'score' => max(0, $score),
            'level' => $this->getHealthLevel($score),
            'issues' => $issues
        ];
    }

    /**
     * 获取健康等级
     */
    private function getHealthLevel(int $score): string
    {
        if ($score >= 90) {
            return 'excellent';
        } elseif ($score >= 70) {
            return 'good';
        } elseif ($score >= 50) {
            return 'fair';
        } else {
            return 'poor';
        }
    }

    /**
     * 获取桥接路径
     */
    public function getRoutePath(): string
    {
        return $this->source_network . ' → ' . $this->target_network;
    }

    /**
     * 计算手续费
     */
    public function calculateFee(float $amount): float
    {
        return $amount * $this->fee_rate;
    }

    /**
     * 获取预估处理时间
     */
    public function getEstimatedTime(): string
    {
        $minutes = $this->confirmation_blocks * 2; // 假设每个区块2分钟
        
        if ($minutes < 60) {
            return $minutes . '分钟';
        } else {
            $hours = round($minutes / 60, 1);
            return $hours . '小时';
        }
    }

    /**
     * 检查是否可用
     */
    public function isAvailable(): bool
    {
        return $this->status === self::STATUS_ACTIVE && !$this->is_maintenance;
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr(): string
    {
        $statusTexts = [
            self::STATUS_INACTIVE => '未激活',
            self::STATUS_ACTIVE => '活跃',
            self::STATUS_PAUSED => '暂停',
            self::STATUS_DEPRECATED => '已弃用'
        ];

        return $statusTexts[$this->status] ?? '未知状态';
    }

    /**
     * 获取类型文本
     */
    public function getTypeTextAttr(): string
    {
        $typeTexts = [
            self::TYPE_LOCK_MINT => '锁定铸造',
            self::TYPE_BURN_MINT => '销毁铸造',
            self::TYPE_ATOMIC_SWAP => '原子交换',
            self::TYPE_LIQUIDITY => '流动性桥'
        ];

        return $typeTexts[$this->bridge_type] ?? '未知类型';
    }

    /**
     * 获取安全等级
     */
    public function getSecurityLevelAttr(): string
    {
        if ($this->security_threshold >= 0.8) {
            return 'high';
        } elseif ($this->security_threshold >= 0.6) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * 获取格式化费率
     */
    public function getFormattedFeeRateAttr(): string
    {
        return number_format($this->fee_rate * 100, 3) . '%';
    }

    /**
     * 搜索器：按网络
     */
    public function searchNetworkAttr($query, $value)
    {
        if ($value) {
            $query->where('source_network|target_network', 'like', "%{$value}%");
        }
    }

    /**
     * 搜索器：按类型
     */
    public function searchTypeAttr($query, $value)
    {
        if ($value) {
            $query->where('bridge_type', $value);
        }
    }

    /**
     * 搜索器：按状态
     */
    public function searchStatusAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('status', $value);
        }
    }

    /**
     * 搜索器：按代币
     */
    public function searchTokenAttr($query, $value)
    {
        if ($value) {
            $query->whereRaw("JSON_CONTAINS(supported_tokens, '\"" . $value . "\"')");
        }
    }

    /**
     * 搜索器：按关键词
     */
    public function searchKeywordAttr($query, $value)
    {
        if ($value) {
            $query->where('name|description', 'like', "%{$value}%");
        }
    }
}
