<?php
declare (strict_types = 1);

namespace app\index\controller;

use app\BaseController;
use app\common\model\Coin;
use app\common\model\UserAsset;
use app\common\model\DepositRecord;
use app\common\model\WithdrawRecord;
use app\common\service\WalletService;
use app\common\service\AssetService;
use think\facade\View;
use think\facade\Session;

/**
 * 钱包管理控制器
 */
class Wallet extends BaseController
{
    protected $walletService;
    protected $assetService;

    public function initialize()
    {
        parent::initialize();
        $this->walletService = new WalletService();
        $this->assetService = new AssetService();

        // 检查登录状态
        if (!Session::has('user_id')) {
            $this->redirect('/auth/login');
        }
    }

    /**
     * 钱包首页
     */
    public function index()
    {
        $userId = Session::get('user_id');
        
        // 获取用户所有资产
        $assets = UserAsset::where('user_id', $userId)
                          ->with('coin')
                          ->order('total', 'desc')
                          ->select();

        // 计算总资产价值
        $totalValue = $this->calculateTotalValue($assets);

        // 获取最近记录
        $recentRecords = $this->getRecentRecords($userId);

        View::assign([
            'assets' => $assets,
            'totalValue' => $totalValue,
            'recentRecords' => $recentRecords,
            'title' => '我的钱包'
        ]);

        return View::fetch();
    }

    /**
     * 充值页面
     */
    public function deposit()
    {
        if ($this->request->isPost()) {
            return $this->doDeposit();
        }

        $coinSymbol = input('coin_symbol', 'USDT');
        
        // 获取支持充值的币种
        $coins = Coin::getDepositEnabled();
        
        // 获取选中币种信息
        $selectedCoin = Coin::getBySymbol($coinSymbol);
        if (!$selectedCoin || !$selectedCoin->is_deposit) {
            $coinSymbol = 'USDT';
            $selectedCoin = Coin::getBySymbol($coinSymbol);
        }

        // 生成充值地址
        $depositAddress = $this->walletService->getDepositAddress(Session::get('user_id'), $coinSymbol);

        View::assign([
            'coins' => $coins,
            'selectedCoin' => $selectedCoin,
            'coinSymbol' => $coinSymbol,
            'depositAddress' => $depositAddress,
            'title' => '充值'
        ]);

        return View::fetch();
    }

    /**
     * 执行充值
     */
    private function doDeposit()
    {
        $data = $this->request->post();
        
        // 验证参数
        if (empty($data['coin_symbol']) || empty($data['amount']) || empty($data['txid'])) {
            return json(['code' => 0, 'msg' => '请填写完整信息']);
        }

        if ($data['amount'] <= 0) {
            return json(['code' => 0, 'msg' => '充值金额必须大于0']);
        }

        $userId = Session::get('user_id');
        $result = $this->walletService->createDepositRecord($userId, $data);

        return json($result);
    }

    /**
     * 提现页面
     */
    public function withdraw()
    {
        if ($this->request->isPost()) {
            return $this->doWithdraw();
        }

        $coinSymbol = input('coin_symbol', 'USDT');
        
        // 获取支持提现的币种
        $coins = Coin::getWithdrawEnabled();
        
        // 获取选中币种信息
        $selectedCoin = Coin::getBySymbol($coinSymbol);
        if (!$selectedCoin || !$selectedCoin->is_withdraw) {
            $coinSymbol = 'USDT';
            $selectedCoin = Coin::getBySymbol($coinSymbol);
        }

        // 获取用户该币种资产
        $userId = Session::get('user_id');
        $asset = UserAsset::getOrCreate($userId, $coinSymbol);

        View::assign([
            'coins' => $coins,
            'selectedCoin' => $selectedCoin,
            'coinSymbol' => $coinSymbol,
            'asset' => $asset,
            'title' => '提现'
        ]);

        return View::fetch();
    }

    /**
     * 执行提现
     */
    private function doWithdraw()
    {
        $data = $this->request->post();
        
        // 验证参数
        if (empty($data['coin_symbol']) || empty($data['amount']) || empty($data['address'])) {
            return json(['code' => 0, 'msg' => '请填写完整信息']);
        }

        if ($data['amount'] <= 0) {
            return json(['code' => 0, 'msg' => '提现金额必须大于0']);
        }

        $userId = Session::get('user_id');
        $result = $this->walletService->createWithdrawRecord($userId, $data);

        return json($result);
    }

    /**
     * 资产记录
     */
    public function records()
    {
        $userId = Session::get('user_id');
        $type = input('type', 'all'); // all, deposit, withdraw
        $coinSymbol = input('coin_symbol', '');
        $page = input('page/d', 1);
        $limit = input('limit/d', 20);

        $records = [];
        $total = 0;

        if ($type == 'all' || $type == 'deposit') {
            // 获取充值记录
            $depositWhere = ['user_id' => $userId];
            if (!empty($coinSymbol)) {
                $depositWhere['coin_symbol'] = $coinSymbol;
            }

            $depositRecords = DepositRecord::where($depositWhere)
                                          ->order('created_at', 'desc')
                                          ->select()
                                          ->toArray();

            foreach ($depositRecords as &$record) {
                $record['type'] = 'deposit';
            }

            $records = array_merge($records, $depositRecords);
        }

        if ($type == 'all' || $type == 'withdraw') {
            // 获取提现记录
            $withdrawWhere = ['user_id' => $userId];
            if (!empty($coinSymbol)) {
                $withdrawWhere['coin_symbol'] = $coinSymbol;
            }

            $withdrawRecords = WithdrawRecord::where($withdrawWhere)
                                            ->order('created_at', 'desc')
                                            ->select()
                                            ->toArray();

            foreach ($withdrawRecords as &$record) {
                $record['type'] = 'withdraw';
            }

            $records = array_merge($records, $withdrawRecords);
        }

        // 按时间排序
        usort($records, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });

        // 分页处理
        $total = count($records);
        $offset = ($page - 1) * $limit;
        $records = array_slice($records, $offset, $limit);

        if ($this->request->isAjax()) {
            return json([
                'code' => 1,
                'data' => $records,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]);
        }

        View::assign([
            'records' => $records,
            'type' => $type,
            'coinSymbol' => $coinSymbol,
            'total' => $total,
            'title' => '资产记录'
        ]);

        return View::fetch();
    }

    /**
     * 获取充值地址
     */
    public function getDepositAddress()
    {
        $coinSymbol = input('coin_symbol');
        
        if (empty($coinSymbol)) {
            return json(['code' => 0, 'msg' => '币种不能为空']);
        }

        $userId = Session::get('user_id');
        $address = $this->walletService->getDepositAddress($userId, $coinSymbol);

        return json([
            'code' => 1,
            'data' => [
                'address' => $address,
                'coin_symbol' => $coinSymbol
            ]
        ]);
    }

    /**
     * 验证提现地址
     */
    public function validateAddress()
    {
        $coinSymbol = input('coin_symbol');
        $address = input('address');
        
        if (empty($coinSymbol) || empty($address)) {
            return json(['code' => 0, 'msg' => '参数不能为空']);
        }

        $isValid = $this->walletService->validateAddress($coinSymbol, $address);

        return json([
            'code' => 1,
            'data' => [
                'is_valid' => $isValid
            ]
        ]);
    }

    /**
     * 获取提现手续费
     */
    public function getWithdrawFee()
    {
        $coinSymbol = input('coin_symbol');
        $amount = input('amount/f', 0);
        
        if (empty($coinSymbol)) {
            return json(['code' => 0, 'msg' => '币种不能为空']);
        }

        $fee = $this->walletService->calculateWithdrawFee($coinSymbol, $amount);

        return json([
            'code' => 1,
            'data' => [
                'fee' => $fee,
                'coin_symbol' => $coinSymbol
            ]
        ]);
    }

    /**
     * 计算总资产价值
     */
    private function calculateTotalValue($assets): float
    {
        $totalValue = 0;
        
        foreach ($assets as $asset) {
            if ($asset->total > 0) {
                if ($asset->coin_symbol == 'USDT') {
                    $totalValue += $asset->total;
                } else {
                    // 获取汇率
                    $rate = $this->getCoinRate($asset->coin_symbol);
                    $totalValue += $asset->total * $rate;
                }
            }
        }

        return $totalValue;
    }

    /**
     * 获取最近记录
     */
    private function getRecentRecords(int $userId): array
    {
        $records = [];

        // 获取最近充值记录
        $deposits = DepositRecord::where('user_id', $userId)
                                ->order('created_at', 'desc')
                                ->limit(5)
                                ->select()
                                ->toArray();

        foreach ($deposits as $deposit) {
            $deposit['type'] = 'deposit';
            $records[] = $deposit;
        }

        // 获取最近提现记录
        $withdraws = WithdrawRecord::where('user_id', $userId)
                                  ->order('created_at', 'desc')
                                  ->limit(5)
                                  ->select()
                                  ->toArray();

        foreach ($withdraws as $withdraw) {
            $withdraw['type'] = 'withdraw';
            $records[] = $withdraw;
        }

        // 按时间排序
        usort($records, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });

        return array_slice($records, 0, 10);
    }

    /**
     * 获取币种汇率
     */
    private function getCoinRate(string $coinSymbol): float
    {
        // 这里应该从交易对获取实时汇率
        $rates = [
            'BTC' => 45000,
            'ETH' => 2800,
            'BNB' => 300,
            'ADA' => 0.45
        ];

        return $rates[$coinSymbol] ?? 1;
    }

    // ==================== 老系统兼容功能 ====================

    /**
     * 获取用户资产 - 兼容老系统API格式
     */
    public function getUserAssets()
    {
        $userId = Session::get('user_id');
        $result = $this->assetService->getUserAssets($userId);

        return json($result);
    }

    /**
     * 获取单个币种资产 - 兼容老系统
     */
    public function getCoinAsset()
    {
        $userId = Session::get('user_id');
        $coinSymbol = input('coin_symbol', 'USDT');

        $asset = UserAsset::getOrCreate($userId, strtoupper($coinSymbol));

        $result = [
            'code' => 1,
            'num' => $asset->available,
            'numd' => $asset->frozen,
            'total' => $asset->total,
            'coin_symbol' => $coinSymbol,
        ];

        return json($result);
    }

    /**
     * 资产转账 - 内部转账功能
     */
    public function transfer()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();

            $fromUserId = Session::get('user_id');
            $toUsername = $data['to_username'] ?? '';
            $coinSymbol = $data['coin_symbol'] ?? '';
            $amount = floatval($data['amount'] ?? 0);
            $paypassword = $data['paypassword'] ?? '';

            // 验证参数
            if (empty($toUsername) || empty($coinSymbol) || $amount <= 0) {
                return json(['code' => 0, 'msg' => '参数错误']);
            }

            // 验证交易密码
            $fromUser = \app\common\model\User::find($fromUserId);
            if (!$fromUser->verifyPayPassword($paypassword)) {
                return json(['code' => 0, 'msg' => '交易密码错误']);
            }

            // 查找目标用户
            $toUser = \app\common\model\User::where('username', $toUsername)->find();
            if (!$toUser) {
                return json(['code' => 0, 'msg' => '目标用户不存在']);
            }

            if ($fromUserId == $toUser->id) {
                return json(['code' => 0, 'msg' => '不能转账给自己']);
            }

            // 执行转账
            $result = $this->executeTransfer($fromUserId, $toUser->id, $coinSymbol, $amount);

            return json($result);
        }

        // 获取支持的币种
        $coins = Coin::where('status', 1)->select();

        View::assign([
            'coins' => $coins,
            'title' => '资产转账'
        ]);

        return View::fetch();
    }

    /**
     * 执行转账操作
     */
    private function executeTransfer(int $fromUserId, int $toUserId, string $coinSymbol, float $amount): array
    {
        try {
            Db::startTrans();

            // 扣除发送方资产
            $subResult = $this->assetService->subAsset($fromUserId, $coinSymbol, $amount, '转账给用户', 12);
            if ($subResult['code'] != 1) {
                Db::rollback();
                return $subResult;
            }

            // 增加接收方资产
            $addResult = $this->assetService->addAsset($toUserId, $coinSymbol, $amount, '接收转账', 12);
            if ($addResult['code'] != 1) {
                Db::rollback();
                return $addResult;
            }

            Db::commit();
            return ['code' => 1, 'msg' => '转账成功'];

        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 0, 'msg' => '转账失败：' . $e->getMessage()];
        }
    }

    /**
     * 资产明细 - 兼容老系统
     */
    public function assetDetail()
    {
        $userId = Session::get('user_id');
        $coinSymbol = input('coin_symbol', '');
        $type = input('type', '');
        $page = input('page', 1);

        $params = [
            'coin_symbol' => $coinSymbol,
            'type' => $type,
            'page' => $page,
            'limit' => 20
        ];

        $records = \app\common\model\FinancialRecord::getUserRecords($userId, $params);

        if ($this->request->isAjax()) {
            return json(['code' => 1, 'data' => $records]);
        }

        // 获取币种列表
        $coins = Coin::where('status', 1)->select();

        View::assign([
            'records' => $records,
            'coins' => $coins,
            'current_coin' => $coinSymbol,
            'current_type' => $type,
            'title' => '资产明细'
        ]);

        return View::fetch();
    }
}
