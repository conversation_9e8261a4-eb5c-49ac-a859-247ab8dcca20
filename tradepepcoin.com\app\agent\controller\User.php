<?php
declare (strict_types = 1);

namespace app\agent\controller;

use app\BaseController;
use app\common\model\User as UserModel;
use app\common\service\UserService;
use think\facade\View;
use think\facade\Session;
use think\facade\Request;
use think\facade\Validate;

/**
 * 代理端用户控制器
 */
class User extends BaseController
{
    protected $userService;

    public function initialize()
    {
        parent::initialize();
        $this->userService = new UserService();
        
        // 检查代理登录状态
        if (!Session::has('agent_id')) {
            $this->redirect('/agent/login');
        }
    }

    /**
     * 我的用户列表（只显示自己代理的用户）
     */
    public function index()
    {
        $agentId = Session::get('agent_id');
        $keyword = Request::get('keyword', '');
        $page = Request::get('page', 1);
        
        // 只显示当前代理下的用户
        $users = UserModel::where('agent_id', $agentId)
                ->when($keyword, function($query, $keyword) {
                    return $query->where('username|email', 'like', '%' . $keyword . '%');
                })
                ->order('created_at desc')
                ->paginate([
                    'list_rows' => 20,
                    'page' => $page
                ]);
        
        // 统计数据
        $stats = [
            'total_users' => UserModel::where('agent_id', $agentId)->count(),
            'formal_users' => UserModel::where('agent_id', $agentId)
                                      ->where('user_type', UserModel::USER_TYPE_FORMAL)
                                      ->count(),
            'test_users' => UserModel::where('agent_id', $agentId)
                                    ->where('user_type', UserModel::USER_TYPE_TEST)
                                    ->count(),
            'today_register' => UserModel::where('agent_id', $agentId)
                                        ->whereTime('created_at', 'today')
                                        ->count()
        ];
        
        View::assign([
            'users' => $users,
            'stats' => $stats,
            'keyword' => $keyword,
            'title' => '我的用户 - GVD代理后台'
        ]);
        
        return View::fetch('agent/user/index');
    }

    /**
     * 添加测试用户
     */
    public function add()
    {
        if (Request::isPost()) {
            return $this->doAdd();
        }
        
        View::assign([
            'title' => '添加测试用户 - GVD代理后台'
        ]);
        
        return View::fetch('agent/user/add');
    }

    /**
     * 执行添加用户
     */
    private function doAdd()
    {
        $data = Request::post();
        $agentId = Session::get('agent_id');
        
        // 验证数据
        $validate = Validate::rule([
            'username' => 'require|alphaDash|length:3,20|unique:ce_users',
            'email' => 'require|email|unique:ce_users',
            'password' => 'require|min:6'
        ])->message([
            'username.require' => '请输入用户名',
            'username.unique' => '用户名已存在',
            'email.require' => '请输入邮箱',
            'email.unique' => '邮箱已被注册',
            'password.require' => '请输入密码'
        ]);

        if (!$validate->check($data)) {
            return json(['code' => 0, 'msg' => $validate->getError()]);
        }
        
        // 创建测试用户，绑定到当前代理
        $result = $this->userService->createTestUser($data, 0, $agentId);
        
        return json($result);
    }

    /**
     * 用户详情
     */
    public function detail()
    {
        $id = Request::get('id');
        $agentId = Session::get('agent_id');
        
        // 只能查看自己代理的用户
        $user = UserModel::where('id', $id)
                         ->where('agent_id', $agentId)
                         ->find();
        
        if (!$user) {
            $this->error('用户不存在或无权限查看');
        }
        
        // 获取用户资产
        $assets = $user->assets;
        
        // 获取用户交易统计
        $tradeStats = [
            'total_trades' => $user->orders()->count(),
            'total_amount' => $user->orders()->sum('amount'),
            'profit_loss' => $user->orders()->sum('profit_loss')
        ];
        
        View::assign([
            'user' => $user,
            'assets' => $assets,
            'trade_stats' => $tradeStats,
            'title' => '用户详情 - GVD代理后台'
        ]);
        
        return View::fetch('agent/user/detail');
    }

    /**
     * 编辑用户
     */
    public function edit()
    {
        $id = Request::get('id');
        $agentId = Session::get('agent_id');
        
        if (Request::isPost()) {
            return $this->doEdit($id, $agentId);
        }
        
        // 只能编辑自己代理的用户
        $user = UserModel::where('id', $id)
                         ->where('agent_id', $agentId)
                         ->find();
        
        if (!$user) {
            $this->error('用户不存在或无权限编辑');
        }
        
        View::assign([
            'user' => $user,
            'title' => '编辑用户 - GVD代理后台'
        ]);
        
        return View::fetch('agent/user/edit');
    }

    /**
     * 执行编辑用户
     */
    private function doEdit($id, $agentId)
    {
        $data = Request::post();
        
        $user = UserModel::where('id', $id)
                         ->where('agent_id', $agentId)
                         ->find();
        
        if (!$user) {
            return json(['code' => 0, 'msg' => '用户不存在或无权限编辑']);
        }
        
        // 代理只能修改部分信息
        $user->status = $data['status'] ?? $user->status;
        $user->level = $data['level'] ?? $user->level;
        
        if ($user->save()) {
            return json(['code' => 1, 'msg' => '更新成功']);
        } else {
            return json(['code' => 0, 'msg' => '更新失败']);
        }
    }

    /**
     * 我的统计数据
     */
    public function statistics()
    {
        $agentId = Session::get('agent_id');
        
        $stats = [
            'total_users' => UserModel::where('agent_id', $agentId)->count(),
            'formal_users' => UserModel::where('agent_id', $agentId)
                                      ->where('user_type', UserModel::USER_TYPE_FORMAL)
                                      ->count(),
            'test_users' => UserModel::where('agent_id', $agentId)
                                    ->where('user_type', UserModel::USER_TYPE_TEST)
                                    ->count(),
            'today_register' => UserModel::where('agent_id', $agentId)
                                        ->whereTime('created_at', 'today')
                                        ->count(),
            'this_month_register' => UserModel::where('agent_id', $agentId)
                                            ->whereTime('created_at', 'month')
                                            ->count()
        ];
        
        return json(['code' => 1, 'data' => $stats]);
    }
}
