const languages = {
    'zh-cn': {
        home: '首页',
        trade: '交易',
        login: '登录',
        register: '注册',
        loginRequired: '请先登录或注册'
    },
    'en-us': {
        home: 'Home',
        trade: 'Trade',
        login: 'Login',
        register: 'Register',
        loginRequired: 'Please login or register first'
    }
};

function switchLanguage(lang) {
    const texts = languages[lang];
    if (!texts) return;
    document.querySelectorAll('[data-lang]').forEach(element => {
        const key = element.getAttribute('data-lang');
        if (texts[key]) {
            element.textContent = texts[key];
        }
    });
    localStorage.setItem('selectedLanguage', lang);
}

function initLanguage() {
    const savedLang = localStorage.getItem('selectedLanguage') || 'zh-cn';
    switchLanguage(savedLang);
    const selector = document.getElementById('langSelector');
    if (selector) selector.value = savedLang;
}

document.addEventListener('DOMContentLoaded', initLanguage);
window.switchLanguage = switchLanguage;
window.initLanguage = initLanguage;
