<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;
use think\facade\Cache;

/**
 * 系统配置模型
 */
class SystemConfig extends Model
{
    protected $table = 'ce_system_configs';
    
    protected $pk = 'id';
    
    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'type'        => 'string',
        'key'         => 'string',
        'value'       => 'text',
        'description' => 'string',
        'status'      => 'int',
        'created_at'  => 'datetime',
        'updated_at'  => 'datetime'
    ];

    /**
     * 获取配置值
     */
    public static function getValue(string $type, string $key, $default = null)
    {
        $cacheKey = "system_config_{$type}_{$key}";
        $value = Cache::get($cacheKey);
        
        if ($value === null) {
            $config = self::where('type', $type)
                         ->where('key', $key)
                         ->where('status', 1)
                         ->find();
            
            if ($config) {
                $value = $config->value;
                // 尝试解析JSON
                $decoded = json_decode($value, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $value = $decoded;
                }
                Cache::set($cacheKey, $value, 3600);
            } else {
                $value = $default;
            }
        }
        
        return $value;
    }

    /**
     * 设置配置值
     */
    public static function setValue(string $type, string $key, $value, string $description = ''): bool
    {
        $config = self::where('type', $type)->where('key', $key)->find();
        
        $data = [
            'type' => $type,
            'key' => $key,
            'value' => is_array($value) ? json_encode($value) : $value,
            'description' => $description,
            'status' => 1
        ];
        
        if ($config) {
            $data['updated_at'] = date('Y-m-d H:i:s');
            $result = $config->save($data);
        } else {
            $data['created_at'] = date('Y-m-d H:i:s');
            $result = self::create($data);
        }
        
        if ($result) {
            // 清除缓存
            $cacheKey = "system_config_{$type}_{$key}";
            Cache::delete($cacheKey);
        }
        
        return (bool)$result;
    }

    /**
     * 获取某类型的所有配置
     */
    public static function getTypeConfigs(string $type): array
    {
        $cacheKey = "system_config_{$type}";
        $configs = Cache::get($cacheKey);
        
        if ($configs === null) {
            $configList = self::where('type', $type)
                             ->where('status', 1)
                             ->select();
            
            $configs = [];
            foreach ($configList as $config) {
                $value = $config->value;
                // 尝试解析JSON
                $decoded = json_decode($value, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $value = $decoded;
                }
                
                $configs[$config->key] = [
                    'value' => $value,
                    'description' => $config->description
                ];
            }
            
            Cache::set($cacheKey, $configs, 3600);
        }
        
        return $configs;
    }

    /**
     * 获取所有配置
     */
    public static function getAllConfigs(): array
    {
        $cacheKey = "system_config_all";
        $configs = Cache::get($cacheKey);
        
        if ($configs === null) {
            $configList = self::where('status', 1)->select();
            
            $configs = [];
            foreach ($configList as $config) {
                if (!isset($configs[$config->type])) {
                    $configs[$config->type] = [];
                }
                
                $value = $config->value;
                // 尝试解析JSON
                $decoded = json_decode($value, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $value = $decoded;
                }
                
                $configs[$config->type][$config->key] = [
                    'value' => $value,
                    'description' => $config->description
                ];
            }
            
            Cache::set($cacheKey, $configs, 3600);
        }
        
        return $configs;
    }

    /**
     * 删除配置
     */
    public static function deleteConfig(string $type, string $key): bool
    {
        $config = self::where('type', $type)->where('key', $key)->find();
        
        if ($config) {
            $result = $config->delete();
            
            if ($result) {
                // 清除缓存
                $cacheKeys = [
                    "system_config_{$type}_{$key}",
                    "system_config_{$type}",
                    "system_config_all"
                ];
                
                foreach ($cacheKeys as $cacheKey) {
                    Cache::delete($cacheKey);
                }
            }
            
            return (bool)$result;
        }
        
        return false;
    }

    /**
     * 批量设置配置
     */
    public static function batchSetConfigs(string $type, array $configs): bool
    {
        try {
            foreach ($configs as $key => $config) {
                $value = $config['value'] ?? $config;
                $description = $config['description'] ?? '';
                
                self::setValue($type, $key, $value, $description);
            }
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 清除所有配置缓存
     */
    public static function clearAllCache(): void
    {
        $types = self::distinct(true)->column('type');
        
        foreach ($types as $type) {
            Cache::delete("system_config_{$type}");
            
            $keys = self::where('type', $type)->column('key');
            foreach ($keys as $key) {
                Cache::delete("system_config_{$type}_{$key}");
            }
        }
        
        Cache::delete("system_config_all");
    }
}
