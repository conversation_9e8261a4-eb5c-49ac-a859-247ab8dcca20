<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\User;
use app\common\model\UserAsset;
use app\common\model\DepositRecord;
use app\common\model\WithdrawRecord;
use app\common\model\DepositAddress;
use app\common\model\FinancialRecord;
use think\facade\Db;
use think\facade\Cache;

/**
 * 财务服务类
 */
class FinanceService
{
    /**
     * 计算用户总资产（折合USDT）
     */
    public function calculateTotalAsset(int $userId): array
    {
        $userAssets = UserAsset::where('user_id', $userId)->select();
        
        $totalAvailable = 0;
        $totalFrozen = 0;
        $totalValue = 0;
        
        foreach ($userAssets as $asset) {
            $price = $this->getCoinPrice($asset->coin_symbol);
            
            $availableValue = $asset->available * $price;
            $frozenValue = $asset->frozen * $price;
            
            $totalAvailable += $availableValue;
            $totalFrozen += $frozenValue;
            $totalValue += $availableValue + $frozenValue;
        }
        
        return [
            'total_available' => round($totalAvailable, 2),
            'total_frozen' => round($totalFrozen, 2),
            'total_value' => round($totalValue, 2)
        ];
    }

    /**
     * 获取充值地址（每个用户独立地址，取消公共地址逻辑）
     */
    public function getDepositAddress(int $userId, string $coinSymbol): array
    {
        // 查找已有地址
        $depositAddress = DepositAddress::where('user_id', $userId)
                                       ->where('coin_symbol', $coinSymbol)
                                       ->where('status', 1)
                                       ->find();

        if (!$depositAddress) {
            // 为每个用户生成独立的充值地址
            $address = $this->generateDepositAddress($coinSymbol, $userId);

            $depositAddress = DepositAddress::create([
                'user_id' => $userId,
                'coin_symbol' => $coinSymbol,
                'address' => $address,
                'status' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        }

        return [
            'coin_symbol' => $coinSymbol,
            'address' => $depositAddress->address,
            'qr_code' => $this->generateQrCode($depositAddress->address),
            'memo' => $depositAddress->memo ?? '',
            'min_amount' => $this->getDepositConfig($coinSymbol)['min_amount'],
            'is_personal' => true // 标识为个人专属地址
        ];
    }

    /**
     * 获取充值配置
     */
    public function getDepositConfig(string $coinSymbol): array
    {
        $configs = [
            'USDT' => [
                'min_amount' => 10,
                'confirmations' => 12,
                'network' => 'TRC20',
                'fee' => 0
            ],
            'BTC' => [
                'min_amount' => 0.001,
                'confirmations' => 3,
                'network' => 'Bitcoin',
                'fee' => 0
            ],
            'ETH' => [
                'min_amount' => 0.01,
                'confirmations' => 12,
                'network' => 'Ethereum',
                'fee' => 0
            ]
        ];
        
        return $configs[$coinSymbol] ?? $configs['USDT'];
    }

    /**
     * 获取提现配置
     */
    public function getWithdrawConfig(string $coinSymbol): array
    {
        $configs = [
            'USDT' => [
                'min_amount' => 20,
                'max_amount' => 50000,
                'fee' => 2,
                'daily_limit' => 100000,
                'network' => 'TRC20'
            ],
            'BTC' => [
                'min_amount' => 0.002,
                'max_amount' => 10,
                'fee' => 0.0005,
                'daily_limit' => 50,
                'network' => 'Bitcoin'
            ],
            'ETH' => [
                'min_amount' => 0.02,
                'max_amount' => 100,
                'fee' => 0.005,
                'daily_limit' => 500,
                'network' => 'Ethereum'
            ]
        ];
        
        return $configs[$coinSymbol] ?? $configs['USDT'];
    }

    /**
     * 提交提现申请
     */
    public function submitWithdraw(int $userId, array $data): array
    {
        try {
            Db::startTrans();
            
            // 验证交易密码
            $user = User::find($userId);
            if (!$user->trade_password || !password_verify($data['trade_password'], $user->trade_password)) {
                return ['code' => 0, 'msg' => '交易密码错误'];
            }
            
            // 检查用户资产
            $userAsset = UserAsset::where('user_id', $userId)
                                 ->where('coin_symbol', $data['coin_symbol'])
                                 ->find();
            
            if (!$userAsset) {
                return ['code' => 0, 'msg' => '币种不存在'];
            }
            
            // 获取提现配置
            $config = $this->getWithdrawConfig($data['coin_symbol']);
            $totalAmount = $data['amount'] + $config['fee'];
            
            // 检查余额
            if ($userAsset->available < $totalAmount) {
                return ['code' => 0, 'msg' => '余额不足'];
            }
            
            // 检查提现限额
            if ($data['amount'] < $config['min_amount']) {
                return ['code' => 0, 'msg' => '提现金额不能小于' . $config['min_amount']];
            }
            
            if ($data['amount'] > $config['max_amount']) {
                return ['code' => 0, 'msg' => '提现金额不能大于' . $config['max_amount']];
            }
            
            // 检查日限额
            $todayWithdraw = WithdrawRecord::where('user_id', $userId)
                                          ->where('coin_symbol', $data['coin_symbol'])
                                          ->where('status', 'completed')
                                          ->whereTime('created_at', 'today')
                                          ->sum('amount');
            
            if ($todayWithdraw + $data['amount'] > $config['daily_limit']) {
                return ['code' => 0, 'msg' => '超出日提现限额'];
            }
            
            // 创建提现记录
            $withdraw = WithdrawRecord::create([
                'user_id' => $userId,
                'coin_symbol' => $data['coin_symbol'],
                'address' => $data['address'],
                'amount' => $data['amount'],
                'fee' => $config['fee'],
                'actual_amount' => $data['amount'] - $config['fee'],
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            // 冻结用户资产
            $userAsset->available -= $totalAmount;
            $userAsset->frozen += $totalAmount;
            $userAsset->save();
            
            // 记录财务流水
            FinancialRecord::create([
                'user_id' => $userId,
                'type' => 'withdraw_freeze',
                'coin_symbol' => $data['coin_symbol'],
                'amount' => -$totalAmount,
                'balance' => $userAsset->available,
                'description' => '提现冻结 - ' . $data['coin_symbol'],
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            Db::commit();
            
            return [
                'code' => 1,
                'msg' => '提现申请提交成功',
                'data' => [
                    'withdraw_id' => $withdraw->id,
                    'amount' => $data['amount'],
                    'fee' => $config['fee'],
                    'actual_amount' => $data['amount'] - $config['fee']
                ]
            ];
            
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 0, 'msg' => '提现申请失败：' . $e->getMessage()];
        }
    }

    /**
     * 检查充值状态
     */
    public function checkDepositStatus(int $userId, string $txid): array
    {
        $deposit = DepositRecord::where('user_id', $userId)
                               ->where('txid', $txid)
                               ->find();
        
        if (!$deposit) {
            return ['code' => 0, 'msg' => '充值记录不存在'];
        }
        
        return [
            'code' => 1,
            'data' => [
                'status' => $deposit->status,
                'status_text' => $deposit->status_text,
                'confirmations' => $deposit->confirmations,
                'required_confirmations' => $deposit->required_confirmations
            ]
        ];
    }

    /**
     * 取消提现申请
     */
    public function cancelWithdraw(int $userId, int $withdrawId): array
    {
        try {
            Db::startTrans();
            
            $withdraw = WithdrawRecord::where('id', $withdrawId)
                                     ->where('user_id', $userId)
                                     ->where('status', 'pending')
                                     ->find();
            
            if (!$withdraw) {
                return ['code' => 0, 'msg' => '提现记录不存在或无法取消'];
            }
            
            // 更新提现状态
            $withdraw->status = 'cancelled';
            $withdraw->save();
            
            // 解冻用户资产
            $totalAmount = $withdraw->amount + $withdraw->fee;
            $userAsset = UserAsset::where('user_id', $userId)
                                 ->where('coin_symbol', $withdraw->coin_symbol)
                                 ->find();
            
            if ($userAsset) {
                $userAsset->available += $totalAmount;
                $userAsset->frozen -= $totalAmount;
                $userAsset->save();
                
                // 记录财务流水
                FinancialRecord::create([
                    'user_id' => $userId,
                    'type' => 'withdraw_cancel',
                    'coin_symbol' => $withdraw->coin_symbol,
                    'amount' => $totalAmount,
                    'balance' => $userAsset->available,
                    'description' => '取消提现 - ' . $withdraw->coin_symbol,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }
            
            Db::commit();
            
            return ['code' => 1, 'msg' => '提现申请已取消'];
            
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 0, 'msg' => '取消失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取币种价格（对USDT）
     */
    private function getCoinPrice(string $coinSymbol): float
    {
        if ($coinSymbol === 'USDT') {
            return 1.0;
        }
        
        // 这里应该对接真实的价格API
        $cacheKey = 'coin_price_' . $coinSymbol;
        $price = Cache::get($cacheKey);
        
        if (!$price) {
            // 模拟价格数据
            $prices = [
                'BTC' => 45000,
                'ETH' => 3200,
                'LTC' => 180,
                'EOS' => 4.5,
                'XRP' => 0.8
            ];
            
            $price = $prices[$coinSymbol] ?? 1;
            Cache::set($cacheKey, $price, 300); // 缓存5分钟
        }
        
        return (float)$price;
    }

    /**
     * 生成充值地址
     */
    private function generateDepositAddress(string $coinSymbol): string
    {
        // 这里应该对接真实的区块链钱包API
        // 现在返回模拟地址
        $prefixes = [
            'USDT' => 'TR',
            'BTC' => '1',
            'ETH' => '0x'
        ];
        
        $prefix = $prefixes[$coinSymbol] ?? 'T';
        $randomString = substr(md5(uniqid() . time()), 0, 32);
        
        return $prefix . $randomString;
    }

    /**
     * 生成二维码
     */
    private function generateQrCode(string $content): string
    {
        // 这里应该使用真实的二维码生成库
        // 现在返回模拟的二维码URL
        return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    }

    /**
     * 处理充值到账
     */
    public function processDeposit(array $data): array
    {
        try {
            Db::startTrans();

            // 查找充值地址
            $depositAddress = DepositAddress::where('address', $data['address'])
                                           ->where('coin_symbol', $data['coin_symbol'])
                                           ->find();

            if (!$depositAddress) {
                return ['code' => 0, 'msg' => '充值地址不存在'];
            }

            // 检查是否已处理
            $existDeposit = DepositRecord::where('txid', $data['txid'])->find();
            if ($existDeposit) {
                return ['code' => 0, 'msg' => '该交易已处理'];
            }

            // 创建充值记录
            $deposit = DepositRecord::create([
                'user_id' => $depositAddress->user_id,
                'coin_symbol' => $data['coin_symbol'],
                'address' => $data['address'],
                'amount' => $data['amount'],
                'txid' => $data['txid'],
                'confirmations' => $data['confirmations'] ?? 0,
                'required_confirmations' => $this->getDepositConfig($data['coin_symbol'])['confirmations'],
                'status' => $data['confirmations'] >= $this->getDepositConfig($data['coin_symbol'])['confirmations'] ? 'completed' : 'pending',
                'created_at' => date('Y-m-d H:i:s')
            ]);

            // 如果确认数足够，直接到账
            if ($deposit->status === 'completed') {
                $this->creditUserAsset($depositAddress->user_id, $data['coin_symbol'], $data['amount']);
            }

            Db::commit();

            return ['code' => 1, 'msg' => '充值处理成功'];

        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 0, 'msg' => '充值处理失败：' . $e->getMessage()];
        }
    }

    /**
     * 增加用户资产
     */
    private function creditUserAsset(int $userId, string $coinSymbol, float $amount): void
    {
        $userAsset = UserAsset::where('user_id', $userId)
                             ->where('coin_symbol', $coinSymbol)
                             ->find();

        if (!$userAsset) {
            $userAsset = UserAsset::create([
                'user_id' => $userId,
                'coin_symbol' => $coinSymbol,
                'available' => 0,
                'frozen' => 0,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        }

        $userAsset->available += $amount;
        $userAsset->save();

        // 记录财务流水
        FinancialRecord::create([
            'user_id' => $userId,
            'type' => 'deposit',
            'coin_symbol' => $coinSymbol,
            'amount' => $amount,
            'balance' => $userAsset->available,
            'description' => '充值到账 - ' . $coinSymbol,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 获取用户今日提现额度
     */
    public function getTodayWithdrawLimit(int $userId, string $coinSymbol): array
    {
        $config = $this->getWithdrawConfig($coinSymbol);

        $todayWithdraw = WithdrawRecord::where('user_id', $userId)
                                      ->where('coin_symbol', $coinSymbol)
                                      ->where('status', 'completed')
                                      ->whereTime('created_at', 'today')
                                      ->sum('amount');

        return [
            'daily_limit' => $config['daily_limit'],
            'used_today' => $todayWithdraw,
            'remaining' => max(0, $config['daily_limit'] - $todayWithdraw)
        ];
    }
}
