<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 管理员模型
 */
class Admin extends Model
{
    protected $name = 'gvd_admins';
    
    protected $type = [
        'is_active' => 'boolean',
        'last_login_time' => 'timestamp'
    ];

    // 隐藏字段
    protected $hidden = ['password'];

    // 管理员状态
    const STATUS_INACTIVE = 0;  // 禁用
    const STATUS_ACTIVE = 1;    // 正常

    // 管理员角色
    const ROLE_SUPER = 1;       // 超级管理员
    const ROLE_ADMIN = 2;       // 普通管理员
    const ROLE_OPERATOR = 3;    // 操作员

    /**
     * 密码修改器
     */
    public function setPasswordAttr($value)
    {
        return password_hash($value, PASSWORD_DEFAULT);
    }

    /**
     * 验证密码
     */
    public function verifyPassword(string $password): bool
    {
        return password_verify($password, $this->password);
    }

    /**
     * 生成JWT Token
     */
    public function generateToken(): string
    {
        $payload = [
            'admin_id' => $this->id,
            'username' => $this->username,
            'role' => $this->role,
            'iat' => time(),
            'exp' => time() + 28800 // 8小时过期
        ];

        return $this->createJWT($payload);
    }

    /**
     * 创建JWT
     */
    private function createJWT(array $payload): string
    {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode($payload);
        
        $headerEncoded = $this->base64UrlEncode($header);
        $payloadEncoded = $this->base64UrlEncode($payload);
        
        $signature = hash_hmac('sha256', $headerEncoded . "." . $payloadEncoded, 'admin-secret-key', true);
        $signatureEncoded = $this->base64UrlEncode($signature);
        
        return $headerEncoded . "." . $payloadEncoded . "." . $signatureEncoded;
    }

    /**
     * Base64 URL编码
     */
    private function base64UrlEncode(string $data): string
    {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    /**
     * 验证JWT Token
     */
    public static function verifyToken(string $token): ?array
    {
        $parts = explode('.', $token);
        if (count($parts) !== 3) {
            return null;
        }

        [$headerEncoded, $payloadEncoded, $signatureEncoded] = $parts;
        
        $signature = hash_hmac('sha256', $headerEncoded . "." . $payloadEncoded, 'admin-secret-key', true);
        $expectedSignature = self::base64UrlDecode($signatureEncoded);
        
        if (!hash_equals($signature, $expectedSignature)) {
            return null;
        }

        $payload = json_decode(self::base64UrlDecode($payloadEncoded), true);
        
        if ($payload['exp'] < time()) {
            return null; // Token已过期
        }

        return $payload;
    }

    /**
     * Base64 URL解码
     */
    private static function base64UrlDecode(string $data): string
    {
        return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
    }

    /**
     * 管理员登录
     */
    public static function login(string $username, string $password): array
    {
        try {
            $admin = self::where('username', $username)->find();

            if (!$admin) {
                return ['code' => 0, 'msg' => '管理员不存在'];
            }

            if (!$admin->verifyPassword($password)) {
                return ['code' => 0, 'msg' => '密码错误'];
            }

            if ($admin->status != self::STATUS_ACTIVE) {
                return ['code' => 0, 'msg' => '账户已被禁用'];
            }

            // 更新登录信息
            $admin->last_login_time = time();
            $admin->last_login_ip = request()->ip();
            $admin->save();

            return [
                'code' => 1,
                'msg' => '登录成功',
                'data' => [
                    'admin_id' => $admin->id,
                    'username' => $admin->username,
                    'role' => $admin->role,
                    'role_name' => $admin->getRoleName(),
                    'token' => $admin->generateToken()
                ]
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '登录失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取角色名称
     */
    public function getRoleName(): string
    {
        $roles = [
            self::ROLE_SUPER => '超级管理员',
            self::ROLE_ADMIN => '普通管理员',
            self::ROLE_OPERATOR => '操作员'
        ];

        return $roles[$this->role] ?? '未知';
    }

    /**
     * 检查权限
     */
    public function hasPermission(string $permission): bool
    {
        // 超级管理员拥有所有权限
        if ($this->role == self::ROLE_SUPER) {
            return true;
        }

        // 这里可以实现更复杂的权限检查逻辑
        $permissions = [
            self::ROLE_ADMIN => [
                'user.view', 'user.edit', 'user.freeze',
                'asset.view', 'asset.freeze', 'asset.unfreeze',
                'order.view', 'order.cancel',
                'system.config'
            ],
            self::ROLE_OPERATOR => [
                'user.view', 'asset.view', 'order.view'
            ]
        ];

        $rolePermissions = $permissions[$this->role] ?? [];
        return in_array($permission, $rolePermissions);
    }

    /**
     * 获取管理员信息
     */
    public function getProfile(): array
    {
        return [
            'admin_id' => $this->id,
            'username' => $this->username,
            'email' => $this->email,
            'role' => $this->role,
            'role_name' => $this->getRoleName(),
            'status' => $this->status,
            'created_at' => $this->created_at,
            'last_login_time' => $this->last_login_time,
            'last_login_ip' => $this->last_login_ip
        ];
    }

    /**
     * 创建管理员
     */
    public static function createAdmin(array $data): array
    {
        try {
            // 验证用户名是否已存在
            $exists = self::where('username', $data['username'])->find();
            if ($exists) {
                return ['code' => 0, 'msg' => '用户名已存在'];
            }

            // 验证邮箱是否已存在
            if (isset($data['email'])) {
                $exists = self::where('email', $data['email'])->find();
                if ($exists) {
                    return ['code' => 0, 'msg' => '邮箱已被使用'];
                }
            }

            $adminData = [
                'username' => $data['username'],
                'email' => $data['email'] ?? '',
                'password' => $data['password'],
                'role' => $data['role'] ?? self::ROLE_OPERATOR,
                'status' => self::STATUS_ACTIVE,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $admin = self::create($adminData);

            return [
                'code' => 1,
                'msg' => '管理员创建成功',
                'data' => $admin->getProfile()
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '创建失败：' . $e->getMessage()];
        }
    }

    /**
     * 更新管理员信息
     */
    public function updateProfile(array $data): array
    {
        try {
            $allowedFields = ['email', 'role', 'status'];
            $updateData = [];

            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }

            if (empty($updateData)) {
                return ['code' => 0, 'msg' => '没有需要更新的数据'];
            }

            $updateData['updated_at'] = date('Y-m-d H:i:s');
            $this->save($updateData);

            return [
                'code' => 1,
                'msg' => '更新成功',
                'data' => $this->getProfile()
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '更新失败：' . $e->getMessage()];
        }
    }

    /**
     * 修改密码
     */
    public function changePassword(string $oldPassword, string $newPassword): array
    {
        try {
            if (!$this->verifyPassword($oldPassword)) {
                return ['code' => 0, 'msg' => '原密码错误'];
            }

            $this->password = $newPassword;
            $this->updated_at = date('Y-m-d H:i:s');
            $this->save();

            return ['code' => 1, 'msg' => '密码修改成功'];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '密码修改失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取管理员列表
     */
    public static function getAdminList(array $params): array
    {
        try {
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 20;
            $keyword = $params['keyword'] ?? '';

            $query = self::query();

            if (!empty($keyword)) {
                $query->where('username', 'like', "%{$keyword}%")
                     ->whereOr('email', 'like', "%{$keyword}%");
            }

            $admins = $query->order('created_at', 'desc')
                           ->paginate([
                               'list_rows' => $limit,
                               'page' => $page
                           ]);

            $list = [];
            foreach ($admins->items() as $admin) {
                $list[] = $admin->getProfile();
            }

            return [
                'code' => 1,
                'data' => [
                    'list' => $list,
                    'total' => $admins->total(),
                    'page' => $page,
                    'limit' => $limit
                ]
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '获取失败：' . $e->getMessage()];
        }
    }
}
