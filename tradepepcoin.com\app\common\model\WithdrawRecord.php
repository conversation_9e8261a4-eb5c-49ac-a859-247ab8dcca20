<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 提现记录模型
 */
class WithdrawRecord extends Model
{
    protected $name = 'ce_withdraw_records';
    
    // 设置字段信息
    protected $schema = [
        'id'            => 'int',
        'user_id'       => 'int',
        'coin_symbol'   => 'string',
        'address'       => 'string',
        'amount'        => 'decimal',
        'fee'           => 'decimal',
        'actual_amount' => 'decimal',
        'txid'          => 'string',
        'status'        => 'string',
        'remark'        => 'string',
        'created_at'    => 'datetime',
        'updated_at'    => 'datetime'
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 状态常量
    const STATUS_PENDING = 'pending';       // 待审核
    const STATUS_PROCESSING = 'processing'; // 处理中
    const STATUS_COMPLETED = 'completed';   // 已完成
    const STATUS_FAILED = 'failed';         // 失败
    const STATUS_CANCELLED = 'cancelled';   // 已取消
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            self::STATUS_PENDING => '待审核',
            self::STATUS_PROCESSING => '处理中',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_FAILED => '失败',
            self::STATUS_CANCELLED => '已取消'
        ];
        return $status[$data['status']] ?? '未知';
    }
    
    /**
     * 获取状态颜色
     */
    public function getStatusColorAttr($value, $data)
    {
        $colors = [
            self::STATUS_PENDING => 'warning',
            self::STATUS_PROCESSING => 'info',
            self::STATUS_COMPLETED => 'success',
            self::STATUS_FAILED => 'danger',
            self::STATUS_CANCELLED => 'secondary'
        ];
        return $colors[$data['status']] ?? 'secondary';
    }
    
    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    
    /**
     * 获取用户提现记录
     */
    public static function getUserWithdraws(int $userId, string $coinSymbol = '', string $status = '', int $page = 1, int $limit = 20)
    {
        $query = self::where('user_id', $userId);
        
        if ($coinSymbol) {
            $query = $query->where('coin_symbol', $coinSymbol);
        }
        
        if ($status) {
            $query = $query->where('status', $status);
        }
        
        return $query->order('created_at desc')
                    ->paginate([
                        'list_rows' => $limit,
                        'page' => $page
                    ]);
    }
    
    /**
     * 获取提现统计
     */
    public static function getWithdrawStats(int $userId): array
    {
        $total = self::where('user_id', $userId)->count();
        $completed = self::where('user_id', $userId)->where('status', self::STATUS_COMPLETED)->count();
        $pending = self::where('user_id', $userId)->where('status', self::STATUS_PENDING)->count();
        $processing = self::where('user_id', $userId)->where('status', self::STATUS_PROCESSING)->count();
        
        $totalAmount = self::where('user_id', $userId)
                          ->where('status', self::STATUS_COMPLETED)
                          ->sum('amount');
        
        $totalFee = self::where('user_id', $userId)
                       ->where('status', self::STATUS_COMPLETED)
                       ->sum('fee');
        
        return [
            'total' => $total,
            'completed' => $completed,
            'pending' => $pending,
            'processing' => $processing,
            'total_amount' => $totalAmount,
            'total_fee' => $totalFee
        ];
    }
    
    /**
     * 检查是否可以取消
     */
    public function canCancel(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }
    
    /**
     * 检查是否可以重新提交
     */
    public function canResubmit(): bool
    {
        return in_array($this->status, [self::STATUS_FAILED, self::STATUS_CANCELLED]);
    }
    
    /**
     * 获取今日提现总额
     */
    public static function getTodayWithdrawAmount(int $userId, string $coinSymbol): float
    {
        return (float)self::where('user_id', $userId)
                          ->where('coin_symbol', $coinSymbol)
                          ->where('status', self::STATUS_COMPLETED)
                          ->whereTime('created_at', 'today')
                          ->sum('amount');
    }
    
    /**
     * 获取待处理的提现申请
     */
    public static function getPendingWithdraws()
    {
        return self::where('status', self::STATUS_PENDING)
                  ->order('created_at asc')
                  ->select();
    }
}
