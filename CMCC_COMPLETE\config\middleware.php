<?php
// +----------------------------------------------------------------------
// | 中间件配置
// +----------------------------------------------------------------------

return [
    // 别名或分组
    'alias' => [
        'cors'       => app\middleware\AllowCrossDomain::class,
        'rate_limit' => app\middleware\ApiRateLimit::class,
        'api_auth'   => app\middleware\ApiAuth::class,
        'admin_auth' => app\middleware\AdminAuth::class,
        'agent_auth' => app\middleware\AgentAuth::class,
        'check_maintenance' => app\middleware\CheckMaintenance::class,
    ],

    // 优先级设置，此数组中的中间件会按照数组中的顺序优先执行
    'priority' => [
        app\middleware\AllowCrossDomain::class,
        app\middleware\CheckMaintenance::class,
        app\middleware\ApiRateLimit::class,
        app\middleware\ApiAuth::class,
        app\middleware\AdminAuth::class,
        app\middleware\AgentAuth::class,
    ],
];
