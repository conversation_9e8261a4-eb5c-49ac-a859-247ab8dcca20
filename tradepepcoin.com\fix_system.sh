#!/bin/bash

echo "=== GVD交易平台系统修复脚本 ==="

# 1. 恢复修复后的文件到服务器
echo "1. 复制修复后的控制器文件..."
cp app/index/controller/Auth.php /www/wwwroot/tradepepcoin.com/app/index/controller/
cp app/index/controller/Index.php /www/wwwroot/tradepepcoin.com/app/index/controller/
cp app/index/controller/Trade.php /www/wwwroot/tradepepcoin.com/app/index/controller/

echo "2. 复制服务类和模型..."
cp app/common/service/UserService.php /www/wwwroot/tradepepcoin.com/app/common/service/
cp app/common/model/User.php /www/wwwroot/tradepepcoin.com/app/common/model/

echo "3. 恢复路由配置..."
cp route/app.php /www/wwwroot/tradepepcoin.com/route/

echo "4. 设置文件权限..."
chown -R www:www /www/wwwroot/tradepepcoin.com/app/
chmod -R 755 /www/wwwroot/tradepepcoin.com/app/

echo "5. 清除缓存..."
rm -rf /www/wwwroot/tradepepcoin.com/runtime/*

echo "6. 测试关键功能..."
echo "测试首页..."
curl -I https://tradepepcoin.com/

echo "测试登录页面..."
curl -I https://tradepepcoin.com/auth/login

echo "测试注册页面..."
curl -I https://tradepepcoin.com/auth/register

echo "测试交易页面..."
curl -I https://tradepepcoin.com/trade/

echo "=== 修复完成 ==="
echo ""
echo "修复内容："
echo "✅ 恢复完整的Auth控制器（登录、注册、忘记密码）"
echo "✅ 创建UserService服务类"
echo "✅ 创建User模型"
echo "✅ 清理路由配置"
echo "✅ 修复Trade控制器登录检查"
echo ""
echo "现在您可以测试："
echo "- 登录注册功能：https://tradepepcoin.com/auth/login"
echo "- 交易功能：https://tradepepcoin.com/trade/"
echo "- K线图和合约交易应该正常工作"
