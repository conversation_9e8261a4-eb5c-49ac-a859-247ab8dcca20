<?php
declare (strict_types = 1);

namespace app\middleware;

use Closure;
use think\Request;
use think\Response;
use think\facade\Session;
use app\common\service\AdminService;

/**
 * 管理员认证中间件
 */
class AdminAuth
{
    protected $adminService;

    public function __construct(AdminService $adminService)
    {
        $this->adminService = $adminService;
    }

    /**
     * 处理请求
     *
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 获取管理员ID
        $adminId = Session::get('admin_id');
        
        if (empty($adminId)) {
            if ($request->isAjax()) {
                return json([
                    'code' => 401,
                    'msg'  => '请先登录',
                    'data' => null
                ])->code(401);
            } else {
                return redirect('/admin/login');
            }
        }

        // 获取管理员信息
        $admin = $this->adminService->getAdminById($adminId);
        
        if (!$admin || $admin['status'] != 1) {
            Session::delete('admin_id');
            
            if ($request->isAjax()) {
                return json([
                    'code' => 401,
                    'msg'  => '管理员不存在或已被禁用',
                    'data' => null
                ])->code(401);
            } else {
                return redirect('/admin/login');
            }
        }

        // 将管理员信息存储到请求中
        $request->admin = $admin;
        $request->admin_id = $admin['id'];

        return $next($request);
    }
}
