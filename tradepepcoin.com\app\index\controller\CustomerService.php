<?php
declare (strict_types = 1);

namespace app\index\controller;

use app\BaseController;
use app\common\model\CustomerMessage;
use think\facade\View;
use think\facade\Session;
use think\facade\Request;
use think\facade\Db;

/**
 * 客服系统控制器
 */
class CustomerService extends BaseController
{
    public function initialize()
    {
        parent::initialize();
        
        // 检查登录状态
        if (!Session::has('user_id')) {
            $this->redirect('/auth/login');
        }
    }

    /**
     * 客服聊天页面
     */
    public function index()
    {
        $userId = Session::get('user_id');
        
        // 获取客服配置
        $serviceConfig = $this->getCustomerServiceConfig();
        
        // 获取聊天记录
        $messages = CustomerMessage::where('user_id', $userId)
                                  ->order('created_at desc')
                                  ->limit(50)
                                  ->select();
        
        View::assign([
            'messages' => array_reverse($messages->toArray()),
            'service_config' => $serviceConfig,
            'title' => '在线客服 - GVD'
        ]);
        
        return View::fetch('customer_service/index');
    }

    /**
     * 发送消息
     */
    public function sendMessage()
    {
        if (!Request::isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }
        
        $userId = Session::get('user_id');
        $content = Request::post('content');
        $type = Request::post('type', 'text'); // text, image, file
        
        if (empty($content)) {
            return json(['code' => 0, 'msg' => '消息内容不能为空']);
        }
        
        try {
            $message = CustomerMessage::create([
                'user_id' => $userId,
                'content' => $content,
                'type' => $type,
                'sender' => 'user',
                'status' => 'sent',
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            // 自动回复逻辑
            $this->autoReply($userId, $content);
            
            return json([
                'code' => 1,
                'msg' => '发送成功',
                'data' => [
                    'id' => $message->id,
                    'content' => $content,
                    'time' => date('H:i:s')
                ]
            ]);
            
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '发送失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取消息列表
     */
    public function getMessages()
    {
        $userId = Session::get('user_id');
        $lastId = Request::get('last_id', 0);
        
        $messages = CustomerMessage::where('user_id', $userId)
                                  ->where('id', '>', $lastId)
                                  ->order('created_at asc')
                                  ->select();
        
        return json([
            'code' => 1,
            'data' => $messages
        ]);
    }

    /**
     * 标记消息已读
     */
    public function markRead()
    {
        $userId = Session::get('user_id');
        
        CustomerMessage::where('user_id', $userId)
                      ->where('sender', 'service')
                      ->where('status', 'sent')
                      ->update(['status' => 'read']);
        
        return json(['code' => 1, 'msg' => '标记成功']);
    }

    /**
     * 上传文件
     */
    public function uploadFile()
    {
        $file = Request::file('file');
        
        if (!$file) {
            return json(['code' => 0, 'msg' => '请选择文件']);
        }
        
        try {
            $savename = \think\facade\Filesystem::disk('public')->putFile('customer_service', $file);
            $url = '/storage/' . $savename;
            
            return json([
                'code' => 1,
                'msg' => '上传成功',
                'data' => ['url' => $url]
            ]);
            
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '上传失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取未读消息数量
     */
    public function getUnreadCount()
    {
        $userId = Session::get('user_id');
        
        $count = CustomerMessage::where('user_id', $userId)
                               ->where('sender', 'service')
                               ->where('status', 'sent')
                               ->count();
        
        return json([
            'code' => 1,
            'data' => ['count' => $count]
        ]);
    }

    /**
     * 获取客服配置
     */
    private function getCustomerServiceConfig(): array
    {
        $config = Db::name('system_config')->where('key', 'customer_service')->find();
        
        if ($config) {
            return json_decode($config['value'], true);
        }
        
        // 默认配置
        return [
            'qq' => '',
            'wechat' => '',
            'telegram' => '',
            'email' => '<EMAIL>',
            'phone' => '',
            'work_time' => '9:00-18:00'
        ];
    }

    /**
     * 自动回复
     */
    private function autoReply(int $userId, string $content): void
    {
        $replies = [
            '你好' => '您好！欢迎使用GVD客服系统，请问有什么可以帮助您的吗？',
            '充值' => '充值相关问题请查看充值页面说明，如有疑问请详细描述您的问题。',
            '提现' => '提现一般在24小时内处理完成，请耐心等待。如有紧急情况请提供订单号。',
            '合约' => '合约交易有风险，请谨慎操作。如需了解规则请查看帮助文档。',
            '客服' => '我是智能客服，工作时间内会有人工客服为您服务。工作时间：9:00-18:00'
        ];
        
        foreach ($replies as $keyword => $reply) {
            if (strpos($content, $keyword) !== false) {
                CustomerMessage::create([
                    'user_id' => $userId,
                    'content' => $reply,
                    'type' => 'text',
                    'sender' => 'service',
                    'status' => 'sent',
                    'created_at' => date('Y-m-d H:i:s')
                ]);
                break;
            }
        }
    }
}
