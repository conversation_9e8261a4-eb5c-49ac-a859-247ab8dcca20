<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\common\service\MarketService;

/**
 * 市场数据同步命令
 */
class MarketDataSync extends Command
{
    protected function configure()
    {
        $this->setName('market:sync')
             ->setDescription('同步市场数据');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始同步市场数据...');

        $marketService = new MarketService();
        
        try {
            // 同步交易对价格
            $result = $marketService->syncTickers();
            if ($result['code']) {
                $output->writeln('价格数据同步成功');
            } else {
                $output->writeln('价格数据同步失败: ' . $result['msg']);
            }

            // 同步K线数据
            $result = $marketService->syncKlineData();
            if ($result['code']) {
                $output->writeln('K线数据同步成功');
            } else {
                $output->writeln('K线数据同步失败: ' . $result['msg']);
            }

            // 同步深度数据
            $result = $marketService->syncDepthData();
            if ($result['code']) {
                $output->writeln('深度数据同步成功');
            } else {
                $output->writeln('深度数据同步失败: ' . $result['msg']);
            }

        } catch (\Exception $e) {
            $output->writeln('市场数据同步异常: ' . $e->getMessage());
            return 1;
        }

        $output->writeln('市场数据同步完成');
        return 0;
    }
}
