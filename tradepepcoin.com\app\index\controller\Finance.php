<?php
declare (strict_types = 1);

namespace app\index\controller;

use app\BaseController;
use app\common\model\User;
use app\common\model\UserAsset;
use app\common\model\DepositRecord;
use app\common\model\WithdrawRecord;
use app\common\model\DepositAddress;
use app\common\service\FinanceService;
use think\facade\View;
use think\facade\Session;
use think\facade\Request;
use think\facade\Validate;

/**
 * 充值提现控制器
 */
class Finance extends BaseController
{
    protected $financeService;

    public function initialize()
    {
        parent::initialize();
        $this->financeService = new FinanceService();
        
        // 检查登录状态
        if (!Session::has('user_id')) {
            $this->redirect('/auth/login');
        }
    }

    /**
     * 资产总览
     */
    public function index()
    {
        $userId = Session::get('user_id');
        
        // 获取用户资产
        $userAssets = UserAsset::where('user_id', $userId)
                              ->where('available', '>', 0)
                              ->whereOr('frozen', '>', 0)
                              ->select();
        
        // 计算总资产（折合USDT）
        $totalAsset = $this->financeService->calculateTotalAsset($userId);
        
        // 获取最近记录
        $recentDeposits = DepositRecord::where('user_id', $userId)
                                     ->order('created_at desc')
                                     ->limit(5)
                                     ->select();
        
        $recentWithdraws = WithdrawRecord::where('user_id', $userId)
                                        ->order('created_at desc')
                                        ->limit(5)
                                        ->select();
        
        View::assign([
            'user_assets' => $userAssets,
            'total_asset' => $totalAsset,
            'recent_deposits' => $recentDeposits,
            'recent_withdraws' => $recentWithdraws,
            'title' => '我的资产 - GVD数字货币交易平台'
        ]);
        
        return View::fetch('finance/index');
    }

    /**
     * 充值页面
     */
    public function deposit()
    {
        $userId = Session::get('user_id');
        $coinSymbol = Request::get('coin', 'USDT');
        
        // 获取充值地址
        $depositAddress = $this->financeService->getDepositAddress($userId, $coinSymbol);
        
        // 获取充值配置
        $depositConfig = $this->financeService->getDepositConfig($coinSymbol);
        
        // 获取充值记录
        $depositRecords = DepositRecord::where('user_id', $userId)
                                     ->where('coin_symbol', $coinSymbol)
                                     ->order('created_at desc')
                                     ->limit(10)
                                     ->select();
        
        View::assign([
            'coin_symbol' => $coinSymbol,
            'deposit_address' => $depositAddress,
            'deposit_config' => $depositConfig,
            'deposit_records' => $depositRecords,
            'title' => $coinSymbol . '充值 - GVD数字货币交易平台'
        ]);
        
        return View::fetch('finance/deposit');
    }

    /**
     * 提现页面
     */
    public function withdraw()
    {
        $userId = Session::get('user_id');
        $coinSymbol = Request::get('coin', 'USDT');
        
        // 检查KYC状态
        $user = User::find($userId);
        if ($user->kyc_status != User::KYC_APPROVED) {
            $this->error('请先完成实名认证', '/kyc/');
        }
        
        // 获取用户资产
        $userAsset = UserAsset::where('user_id', $userId)
                             ->where('coin_symbol', $coinSymbol)
                             ->find();
        
        // 获取提现配置
        $withdrawConfig = $this->financeService->getWithdrawConfig($coinSymbol);
        
        // 获取提现记录
        $withdrawRecords = WithdrawRecord::where('user_id', $userId)
                                        ->where('coin_symbol', $coinSymbol)
                                        ->order('created_at desc')
                                        ->limit(10)
                                        ->select();
        
        View::assign([
            'coin_symbol' => $coinSymbol,
            'user_asset' => $userAsset,
            'withdraw_config' => $withdrawConfig,
            'withdraw_records' => $withdrawRecords,
            'title' => $coinSymbol . '提现 - GVD数字货币交易平台'
        ]);
        
        return View::fetch('finance/withdraw');
    }

    /**
     * 提交提现申请
     */
    public function submitWithdraw()
    {
        if (!Request::isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }
        
        $userId = Session::get('user_id');
        $data = Request::post();
        
        // 验证数据
        $validate = Validate::rule([
            'coin_symbol' => 'require',
            'address' => 'require',
            'amount' => 'require|float|gt:0',
            'trade_password' => 'require'
        ])->message([
            'coin_symbol.require' => '请选择提现币种',
            'address.require' => '请输入提现地址',
            'amount.require' => '请输入提现金额',
            'amount.float' => '金额格式错误',
            'amount.gt' => '金额必须大于0',
            'trade_password.require' => '请输入交易密码'
        ]);

        if (!$validate->check($data)) {
            return json(['code' => 0, 'msg' => $validate->getError()]);
        }
        
        // 提交提现申请
        $result = $this->financeService->submitWithdraw($userId, $data);
        
        return json($result);
    }

    /**
     * 充值记录
     */
    public function depositHistory()
    {
        $userId = Session::get('user_id');
        $coinSymbol = Request::get('coin', '');
        $status = Request::get('status', '');
        $page = Request::get('page', 1);
        
        $deposits = DepositRecord::where('user_id', $userId);
        
        if ($coinSymbol) {
            $deposits = $deposits->where('coin_symbol', $coinSymbol);
        }
        
        if ($status !== '') {
            $deposits = $deposits->where('status', $status);
        }
        
        $deposits = $deposits->order('created_at desc')
                           ->paginate([
                               'list_rows' => 20,
                               'page' => $page
                           ]);
        
        if (Request::isAjax()) {
            return json([
                'code' => 1,
                'data' => $deposits->items(),
                'total' => $deposits->total()
            ]);
        }
        
        View::assign([
            'deposits' => $deposits,
            'coin_symbol' => $coinSymbol,
            'status' => $status,
            'title' => '充值记录 - GVD数字货币交易平台'
        ]);
        
        return View::fetch('finance/deposit_history');
    }

    /**
     * 提现记录
     */
    public function withdrawHistory()
    {
        $userId = Session::get('user_id');
        $coinSymbol = Request::get('coin', '');
        $status = Request::get('status', '');
        $page = Request::get('page', 1);
        
        $withdraws = WithdrawRecord::where('user_id', $userId);
        
        if ($coinSymbol) {
            $withdraws = $withdraws->where('coin_symbol', $coinSymbol);
        }
        
        if ($status !== '') {
            $withdraws = $withdraws->where('status', $status);
        }
        
        $withdraws = $withdraws->order('created_at desc')
                             ->paginate([
                                 'list_rows' => 20,
                                 'page' => $page
                             ]);
        
        if (Request::isAjax()) {
            return json([
                'code' => 1,
                'data' => $withdraws->items(),
                'total' => $withdraws->total()
            ]);
        }
        
        View::assign([
            'withdraws' => $withdraws,
            'coin_symbol' => $coinSymbol,
            'status' => $status,
            'title' => '提现记录 - GVD数字货币交易平台'
        ]);
        
        return View::fetch('finance/withdraw_history');
    }

    /**
     * 获取充值地址
     */
    public function getDepositAddress()
    {
        $userId = Session::get('user_id');
        $coinSymbol = Request::get('coin_symbol');
        
        if (!$coinSymbol) {
            return json(['code' => 0, 'msg' => '币种参数错误']);
        }
        
        $address = $this->financeService->getDepositAddress($userId, $coinSymbol);
        
        return json([
            'code' => 1,
            'data' => $address
        ]);
    }

    /**
     * 检查充值状态
     */
    public function checkDeposit()
    {
        $userId = Session::get('user_id');
        $txid = Request::get('txid');
        
        if (!$txid) {
            return json(['code' => 0, 'msg' => '交易哈希不能为空']);
        }
        
        $result = $this->financeService->checkDepositStatus($userId, $txid);
        
        return json($result);
    }

    /**
     * 取消提现申请
     */
    public function cancelWithdraw()
    {
        if (!Request::isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }
        
        $userId = Session::get('user_id');
        $withdrawId = Request::post('withdraw_id');
        
        if (!$withdrawId) {
            return json(['code' => 0, 'msg' => '提现ID不能为空']);
        }
        
        $result = $this->financeService->cancelWithdraw($userId, $withdrawId);
        
        return json($result);
    }
}
