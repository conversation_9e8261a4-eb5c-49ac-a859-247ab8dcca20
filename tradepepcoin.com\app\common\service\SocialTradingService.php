<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\TradingStrategy;
use app\common\model\StrategySubscription;
use app\common\model\TraderRanking;
use app\common\model\SocialPost;
use app\common\model\FollowRelation;
use app\common\model\CopyTrade;
use app\common\model\User;
use app\common\model\UserAsset;
use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

/**
 * 社交交易服务
 */
class SocialTradingService
{
    // 策略状态
    const STRATEGY_STATUS_DRAFT = 0;      // 草稿
    const STRATEGY_STATUS_PUBLISHED = 1;  // 已发布
    const STRATEGY_STATUS_PAUSED = 2;     // 暂停
    const STRATEGY_STATUS_CLOSED = 3;     // 关闭

    // 订阅状态
    const SUBSCRIPTION_STATUS_ACTIVE = 1;    // 活跃
    const SUBSCRIPTION_STATUS_PAUSED = 2;    // 暂停
    const SUBSCRIPTION_STATUS_CANCELLED = 3; // 取消

    // 跟单模式
    const COPY_MODE_FIXED_AMOUNT = 'fixed_amount';     // 固定金额
    const COPY_MODE_FIXED_RATIO = 'fixed_ratio';       // 固定比例
    const COPY_MODE_PROPORTIONAL = 'proportional';     // 按比例

    // 帖子类型
    const POST_TYPE_ANALYSIS = 'analysis';    // 分析
    const POST_TYPE_SIGNAL = 'signal';        // 信号
    const POST_TYPE_DISCUSSION = 'discussion'; // 讨论
    const POST_TYPE_NEWS = 'news';            // 新闻

    /**
     * 创建交易策略
     */
    public function createTradingStrategy(int $userId, array $data): array
    {
        try {
            // 验证用户权限
            $user = User::find($userId);
            if (!$user || $user->kyc_level < 2) {
                return ['code' => 0, 'msg' => '需要高级认证才能发布策略'];
            }

            // 验证策略数据
            $validation = $this->validateStrategyData($data);
            if (!$validation['code']) {
                return $validation;
            }

            // 创建策略
            $strategyData = [
                'strategy_id' => $this->generateStrategyId(),
                'user_id' => $userId,
                'title' => $data['title'],
                'description' => $data['description'],
                'strategy_type' => $data['strategy_type'],
                'risk_level' => $data['risk_level'],
                'min_investment' => $data['min_investment'] ?? 100,
                'max_investment' => $data['max_investment'] ?? 10000,
                'performance_fee' => $data['performance_fee'] ?? 0.2,
                'management_fee' => $data['management_fee'] ?? 0.02,
                'symbols' => $data['symbols'] ?? [],
                'parameters' => $data['parameters'] ?? [],
                'status' => self::STRATEGY_STATUS_DRAFT,
                'created_at' => date('Y-m-d H:i:s')
            ];

            $strategy = TradingStrategy::create($strategyData);

            if ($strategy) {
                Log::info("交易策略创建成功", [
                    'strategy_id' => $strategy->strategy_id,
                    'user_id' => $userId,
                    'title' => $data['title']
                ]);

                return [
                    'code' => 1,
                    'msg' => '交易策略创建成功',
                    'data' => ['strategy_id' => $strategy->strategy_id]
                ];
            } else {
                return ['code' => 0, 'msg' => '交易策略创建失败'];
            }
        } catch (\Exception $e) {
            Log::error('交易策略创建失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '交易策略创建失败'];
        }
    }

    /**
     * 发布策略
     */
    public function publishStrategy(string $strategyId, int $userId): array
    {
        try {
            $strategy = TradingStrategy::where('strategy_id', $strategyId)
                                     ->where('user_id', $userId)
                                     ->find();

            if (!$strategy) {
                return ['code' => 0, 'msg' => '策略不存在'];
            }

            if ($strategy->status !== self::STRATEGY_STATUS_DRAFT) {
                return ['code' => 0, 'msg' => '策略状态不允许发布'];
            }

            // 验证策略完整性
            $validation = $this->validateStrategyForPublish($strategy);
            if (!$validation['code']) {
                return $validation;
            }

            // 更新策略状态
            $strategy->status = self::STRATEGY_STATUS_PUBLISHED;
            $strategy->published_at = date('Y-m-d H:i:s');
            $strategy->save();

            // 更新交易员排行榜
            $this->updateTraderRanking($userId);

            Log::info("交易策略发布成功", ['strategy_id' => $strategyId]);

            return [
                'code' => 1,
                'msg' => '策略发布成功'
            ];
        } catch (\Exception $e) {
            Log::error('策略发布失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '策略发布失败'];
        }
    }

    /**
     * 订阅策略
     */
    public function subscribeStrategy(int $userId, string $strategyId, array $subscriptionData): array
    {
        try {
            $strategy = TradingStrategy::where('strategy_id', $strategyId)->find();
            if (!$strategy || $strategy->status !== self::STRATEGY_STATUS_PUBLISHED) {
                return ['code' => 0, 'msg' => '策略不存在或未发布'];
            }

            // 检查是否已订阅
            $existingSubscription = StrategySubscription::where('user_id', $userId)
                                                       ->where('strategy_id', $strategyId)
                                                       ->where('status', self::SUBSCRIPTION_STATUS_ACTIVE)
                                                       ->find();

            if ($existingSubscription) {
                return ['code' => 0, 'msg' => '已订阅该策略'];
            }

            // 验证投资金额
            $investmentAmount = $subscriptionData['investment_amount'];
            if ($investmentAmount < $strategy->min_investment || $investmentAmount > $strategy->max_investment) {
                return ['code' => 0, 'msg' => '投资金额超出策略限制'];
            }

            // 验证用户资产
            $assetCheck = $this->checkUserAssets($userId, $investmentAmount);
            if (!$assetCheck['code']) {
                return $assetCheck;
            }

            // 创建订阅记录
            $subscriptionRecord = [
                'subscription_id' => $this->generateSubscriptionId(),
                'user_id' => $userId,
                'strategy_id' => $strategyId,
                'investment_amount' => $investmentAmount,
                'copy_mode' => $subscriptionData['copy_mode'] ?? self::COPY_MODE_FIXED_AMOUNT,
                'copy_ratio' => $subscriptionData['copy_ratio'] ?? 1.0,
                'stop_loss' => $subscriptionData['stop_loss'] ?? 0,
                'take_profit' => $subscriptionData['take_profit'] ?? 0,
                'status' => self::SUBSCRIPTION_STATUS_ACTIVE,
                'subscribed_at' => date('Y-m-d H:i:s')
            ];

            $subscription = StrategySubscription::create($subscriptionRecord);

            if ($subscription) {
                // 冻结投资资金
                $this->freezeInvestmentFunds($userId, $investmentAmount);

                // 更新策略统计
                $this->updateStrategyStats($strategy);

                Log::info("策略订阅成功", [
                    'subscription_id' => $subscription->subscription_id,
                    'user_id' => $userId,
                    'strategy_id' => $strategyId,
                    'amount' => $investmentAmount
                ]);

                return [
                    'code' => 1,
                    'msg' => '策略订阅成功',
                    'data' => ['subscription_id' => $subscription->subscription_id]
                ];
            } else {
                return ['code' => 0, 'msg' => '策略订阅失败'];
            }
        } catch (\Exception $e) {
            Log::error('策略订阅失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '策略订阅失败'];
        }
    }

    /**
     * 跟单交易
     */
    public function copyTrade(string $originalOrderId, array $tradeData): array
    {
        try {
            // 获取原始订单信息
            $originalOrder = $this->getOriginalOrder($originalOrderId);
            if (!$originalOrder) {
                return ['code' => 0, 'msg' => '原始订单不存在'];
            }

            // 获取该策略的所有订阅者
            $subscriptions = StrategySubscription::where('strategy_id', $originalOrder['strategy_id'])
                                                ->where('status', self::SUBSCRIPTION_STATUS_ACTIVE)
                                                ->select();

            $copyTrades = [];

            foreach ($subscriptions as $subscription) {
                // 计算跟单数量
                $copyAmount = $this->calculateCopyAmount($subscription, $originalOrder);
                
                if ($copyAmount > 0) {
                    // 创建跟单订单
                    $copyTradeResult = $this->createCopyTradeOrder($subscription, $originalOrder, $copyAmount);
                    
                    if ($copyTradeResult['code']) {
                        $copyTrades[] = $copyTradeResult['data'];
                        
                        // 记录跟单记录
                        CopyTrade::create([
                            'copy_trade_id' => $this->generateCopyTradeId(),
                            'original_order_id' => $originalOrderId,
                            'copy_order_id' => $copyTradeResult['data']['order_id'],
                            'subscription_id' => $subscription->subscription_id,
                            'user_id' => $subscription->user_id,
                            'strategy_id' => $subscription->strategy_id,
                            'copy_amount' => $copyAmount,
                            'copy_ratio' => $subscription->copy_ratio,
                            'created_at' => date('Y-m-d H:i:s')
                        ]);
                    }
                }
            }

            return [
                'code' => 1,
                'data' => [
                    'original_order_id' => $originalOrderId,
                    'copy_trades_count' => count($copyTrades),
                    'copy_trades' => $copyTrades
                ]
            ];
        } catch (\Exception $e) {
            Log::error('跟单交易失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '跟单交易失败'];
        }
    }

    /**
     * 关注交易员
     */
    public function followTrader(int $userId, int $traderId): array
    {
        try {
            if ($userId === $traderId) {
                return ['code' => 0, 'msg' => '不能关注自己'];
            }

            // 检查是否已关注
            $existingFollow = FollowRelation::where('follower_id', $userId)
                                           ->where('following_id', $traderId)
                                           ->find();

            if ($existingFollow) {
                return ['code' => 0, 'msg' => '已关注该交易员'];
            }

            // 创建关注关系
            $followData = [
                'follower_id' => $userId,
                'following_id' => $traderId,
                'followed_at' => date('Y-m-d H:i:s')
            ];

            $follow = FollowRelation::create($followData);

            if ($follow) {
                // 更新关注统计
                $this->updateFollowStats($userId, $traderId);

                return [
                    'code' => 1,
                    'msg' => '关注成功'
                ];
            } else {
                return ['code' => 0, 'msg' => '关注失败'];
            }
        } catch (\Exception $e) {
            Log::error('关注交易员失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '关注失败'];
        }
    }

    /**
     * 发布社交帖子
     */
    public function createSocialPost(int $userId, array $data): array
    {
        try {
            // 验证帖子数据
            $validation = $this->validatePostData($data);
            if (!$validation['code']) {
                return $validation;
            }

            // 创建帖子
            $postData = [
                'post_id' => $this->generatePostId(),
                'user_id' => $userId,
                'type' => $data['type'],
                'title' => $data['title'] ?? '',
                'content' => $data['content'],
                'images' => $data['images'] ?? [],
                'symbols' => $data['symbols'] ?? [],
                'tags' => $data['tags'] ?? [],
                'is_premium' => $data['is_premium'] ?? 0,
                'view_count' => 0,
                'like_count' => 0,
                'comment_count' => 0,
                'created_at' => date('Y-m-d H:i:s')
            ];

            $post = SocialPost::create($postData);

            if ($post) {
                // 通知关注者
                $this->notifyFollowers($userId, $post);

                return [
                    'code' => 1,
                    'msg' => '帖子发布成功',
                    'data' => ['post_id' => $post->post_id]
                ];
            } else {
                return ['code' => 0, 'msg' => '帖子发布失败'];
            }
        } catch (\Exception $e) {
            Log::error('帖子发布失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '帖子发布失败'];
        }
    }

    /**
     * 获取交易员排行榜
     */
    public function getTraderRanking(array $filters = []): array
    {
        $query = TraderRanking::with(['user']);

        // 按时间范围筛选
        if (!empty($filters['period'])) {
            $query->where('period', $filters['period']);
        }

        // 按收益率排序
        $rankings = $query->order('return_rate', 'desc')
                         ->limit($filters['limit'] ?? 50)
                         ->select();

        return [
            'code' => 1,
            'data' => $rankings->toArray()
        ];
    }

    /**
     * 获取策略列表
     */
    public function getStrategies(array $filters = []): array
    {
        $query = TradingStrategy::where('status', self::STRATEGY_STATUS_PUBLISHED)
                               ->with(['user']);

        if (!empty($filters['strategy_type'])) {
            $query->where('strategy_type', $filters['strategy_type']);
        }

        if (!empty($filters['risk_level'])) {
            $query->where('risk_level', $filters['risk_level']);
        }

        $strategies = $query->order('subscribers_count', 'desc')
                           ->limit($filters['limit'] ?? 20)
                           ->select();

        return [
            'code' => 1,
            'data' => $strategies->toArray()
        ];
    }

    /**
     * 获取社交动态
     */
    public function getSocialFeed(int $userId, array $filters = []): array
    {
        // 获取关注的用户ID列表
        $followingIds = FollowRelation::where('follower_id', $userId)
                                     ->column('following_id');

        $query = SocialPost::whereIn('user_id', $followingIds)
                          ->with(['user']);

        if (!empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        $posts = $query->order('created_at', 'desc')
                      ->limit($filters['limit'] ?? 20)
                      ->select();

        return [
            'code' => 1,
            'data' => $posts->toArray()
        ];
    }

    /**
     * 验证策略数据
     */
    private function validateStrategyData(array $data): array
    {
        if (empty($data['title'])) {
            return ['code' => 0, 'msg' => '策略标题不能为空'];
        }

        if (empty($data['description'])) {
            return ['code' => 0, 'msg' => '策略描述不能为空'];
        }

        if (empty($data['strategy_type'])) {
            return ['code' => 0, 'msg' => '策略类型不能为空'];
        }

        return ['code' => 1, 'msg' => '验证通过'];
    }

    /**
     * 验证帖子数据
     */
    private function validatePostData(array $data): array
    {
        if (empty($data['content'])) {
            return ['code' => 0, 'msg' => '帖子内容不能为空'];
        }

        if (empty($data['type']) || !in_array($data['type'], [
            self::POST_TYPE_ANALYSIS,
            self::POST_TYPE_SIGNAL,
            self::POST_TYPE_DISCUSSION,
            self::POST_TYPE_NEWS
        ])) {
            return ['code' => 0, 'msg' => '帖子类型不正确'];
        }

        return ['code' => 1, 'msg' => '验证通过'];
    }

    /**
     * 其他辅助方法
     */
    private function generateStrategyId(): string
    {
        return 'ST' . date('YmdHis') . mt_rand(1000, 9999);
    }

    private function generateSubscriptionId(): string
    {
        return 'SUB' . date('YmdHis') . mt_rand(1000, 9999);
    }

    private function generateCopyTradeId(): string
    {
        return 'COPY' . date('YmdHis') . mt_rand(1000, 9999);
    }

    private function generatePostId(): string
    {
        return 'POST' . date('YmdHis') . mt_rand(1000, 9999);
    }

    private function validateStrategyForPublish(TradingStrategy $strategy): array
    {
        // 验证策略发布条件
        return ['code' => 1, 'msg' => '验证通过'];
    }

    private function updateTraderRanking(int $userId): void
    {
        // 更新交易员排行榜
    }

    private function checkUserAssets(int $userId, float $amount): array
    {
        // 检查用户资产
        return ['code' => 1, 'msg' => '资产充足'];
    }

    private function freezeInvestmentFunds(int $userId, float $amount): void
    {
        // 冻结投资资金
    }

    private function updateStrategyStats(TradingStrategy $strategy): void
    {
        // 更新策略统计
    }

    private function getOriginalOrder(string $orderId): ?array
    {
        // 获取原始订单
        return null;
    }

    private function calculateCopyAmount(StrategySubscription $subscription, array $originalOrder): float
    {
        // 计算跟单数量
        return 0.0;
    }

    private function createCopyTradeOrder(StrategySubscription $subscription, array $originalOrder, float $amount): array
    {
        // 创建跟单订单
        return ['code' => 1, 'data' => []];
    }

    private function updateFollowStats(int $followerId, int $followingId): void
    {
        // 更新关注统计
    }

    private function notifyFollowers(int $userId, SocialPost $post): void
    {
        // 通知关注者
    }
}
