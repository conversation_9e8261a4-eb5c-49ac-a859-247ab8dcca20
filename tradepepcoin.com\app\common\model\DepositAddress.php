<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 充值地址模型
 */
class DepositAddress extends Model
{
    protected $name = 'ce_deposit_addresses';
    
    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'user_id'     => 'int',
        'coin_symbol' => 'string',
        'address'     => 'string',
        'memo'        => 'string',
        'private_key' => 'string',
        'status'      => 'int',
        'created_at'  => 'datetime',
        'updated_at'  => 'datetime'
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 隐藏字段
    protected $hidden = ['private_key'];
    
    // 状态常量
    const STATUS_ACTIVE = 1;    // 活跃
    const STATUS_INACTIVE = 0;  // 停用
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            self::STATUS_ACTIVE => '活跃',
            self::STATUS_INACTIVE => '停用'
        ];
        return $status[$data['status']] ?? '未知';
    }
    
    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    
    /**
     * 关联充值记录
     */
    public function deposits()
    {
        return $this->hasMany(DepositRecord::class, 'address', 'address');
    }
    
    /**
     * 获取用户的充值地址
     */
    public static function getUserAddress(int $userId, string $coinSymbol)
    {
        return self::where('user_id', $userId)
                  ->where('coin_symbol', $coinSymbol)
                  ->where('status', self::STATUS_ACTIVE)
                  ->find();
    }
    
    /**
     * 创建充值地址
     */
    public static function createAddress(int $userId, string $coinSymbol, string $address, string $privateKey = '', string $memo = ''): self
    {
        return self::create([
            'user_id' => $userId,
            'coin_symbol' => $coinSymbol,
            'address' => $address,
            'private_key' => $privateKey,
            'memo' => $memo,
            'status' => self::STATUS_ACTIVE,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 检查地址是否存在
     */
    public static function addressExists(string $address): bool
    {
        return self::where('address', $address)->count() > 0;
    }
    
    /**
     * 根据地址获取用户ID
     */
    public static function getUserIdByAddress(string $address): int
    {
        $depositAddress = self::where('address', $address)->find();
        return $depositAddress ? $depositAddress->user_id : 0;
    }
    
    /**
     * 获取地址的充值统计
     */
    public function getDepositStats(): array
    {
        $deposits = $this->deposits()->where('status', DepositRecord::STATUS_COMPLETED);
        
        return [
            'total_count' => $deposits->count(),
            'total_amount' => $deposits->sum('amount'),
            'last_deposit_time' => $deposits->order('created_at desc')->value('created_at')
        ];
    }
    
    /**
     * 停用地址
     */
    public function deactivate(): bool
    {
        $this->status = self::STATUS_INACTIVE;
        return $this->save();
    }
    
    /**
     * 激活地址
     */
    public function activate(): bool
    {
        $this->status = self::STATUS_ACTIVE;
        return $this->save();
    }
}
