dist: xenial
language: php

matrix:
  fast_finish: true
  include:
    - php: 7.2
    - php: 7.3
    - php: 8.0

cache:
  directories:
    - $HOME/.composer/cache

services:
  - memcached
  - redis-server
  - mysql

before_install:
  - echo "extension = memcached.so" >> ~/.phpenv/versions/$(phpenv version-name)/etc/php.ini
  - echo 'xdebug.mode = coverage' >> ~/.phpenv/versions/$(phpenv version-name)/etc/conf.d/travis.ini
  - printf "\n" | pecl install -f redis
  - travis_retry composer self-update
  - mysql -e 'CREATE DATABASE test;'

install:
  - travis_retry composer update --prefer-dist --no-interaction --prefer-stable --no-suggest

script:
  - vendor/bin/phpunit --coverage-clover build/logs/coverage.xml

after_script:
  - travis_retry wget https://scrutinizer-ci.com/ocular.phar
  - php ocular.phar code-coverage:upload --format=php-clover build/logs/coverage.xml
