<?php
declare (strict_types = 1);

namespace app;

use think\App;
use think\exception\ValidateException;
use think\Validate;

/**
 * 控制器基础类
 */
abstract class BaseController
{
    /**
     * Request实例
     * @var \think\Request
     */
    protected $request;

    /**
     * 应用实例
     * @var \think\App
     */
    protected $app;

    /**
     * 是否批量验证
     * @var bool
     */
    protected $batchValidate = false;

    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [];

    /**
     * 构造方法
     * @access public
     * @param  App  $app  应用对象
     */
    public function __construct(App $app)
    {
        $this->app     = $app;
        $this->request = $this->app->request;

        // 控制器初始化
        $this->initialize();
    }

    // 初始化
    protected function initialize()
    {}

    /**
     * 验证数据
     * @access protected
     * @param  array        $data     数据
     * @param  string|array $validate 验证器名或者验证规则数组
     * @param  array        $message  提示信息
     * @param  bool         $batch    是否批量验证
     * @return array|string|true
     * @throws ValidateException
     */
    protected function validate(array $data, $validate, array $message = [], bool $batch = false)
    {
        if (is_array($validate)) {
            $v = new Validate();
            $v->rule($validate);
        } else {
            if (strpos($validate, '.')) {
                // 支持场景
                [$validate, $scene] = explode('.', $validate);
            }
            $class = false !== strpos($validate, '\\') ? $validate : $this->app->parseClass('validate', $validate);
            $v     = new $class();
            if (!empty($scene)) {
                $v->scene($scene);
            }
        }

        $v->message($message);

        // 是否批量验证
        if ($batch || $this->batchValidate) {
            $v->batch(true);
        }

        return $v->failException(true)->check($data);
    }

    /**
     * 成功响应
     * @param mixed $data 响应数据
     * @param string $msg 响应消息
     * @param int $code 响应码
     * @return \think\Response
     */
    protected function success($data = [], string $msg = 'success', int $code = 1)
    {
        return json([
            'code' => $code,
            'msg' => $msg,
            'data' => $data,
            'timestamp' => time()
        ]);
    }

    /**
     * 错误响应
     * @param string $msg 错误消息
     * @param int $code 错误码
     * @param mixed $data 响应数据
     * @return \think\Response
     */
    protected function error(string $msg = 'error', int $code = 0, $data = [])
    {
        $httpCode = 200;
        
        // 根据错误码设置HTTP状态码
        if ($code == 401) {
            $httpCode = 401;
        } elseif ($code == 403) {
            $httpCode = 403;
        } elseif ($code == 404) {
            $httpCode = 404;
        } elseif ($code >= 500) {
            $httpCode = 500;
        }

        return json([
            'code' => $code,
            'msg' => $msg,
            'data' => $data,
            'timestamp' => time()
        ], $httpCode);
    }

    /**
     * 分页响应
     * @param mixed $data 数据
     * @param int $total 总数
     * @param int $page 当前页
     * @param int $limit 每页数量
     * @param string $msg 消息
     * @return \think\Response
     */
    protected function paginate($data, int $total, int $page, int $limit, string $msg = 'success')
    {
        return json([
            'code' => 1,
            'msg' => $msg,
            'data' => $data,
            'pagination' => [
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ],
            'timestamp' => time()
        ]);
    }

    /**
     * 获取客户端IP
     * @return string
     */
    protected function getClientIp(): string
    {
        return $this->request->ip();
    }

    /**
     * 获取用户代理
     * @return string
     */
    protected function getUserAgent(): string
    {
        return $this->request->header('user-agent', '');
    }

    /**
     * 记录操作日志
     * @param string $action 操作
     * @param string $description 描述
     * @param array $data 数据
     */
    protected function logOperation(string $action, string $description = '', array $data = [])
    {
        // 这里可以实现操作日志记录
        // 例如记录到数据库或日志文件
        \think\facade\Log::info("Operation: {$action}", [
            'description' => $description,
            'data' => $data,
            'ip' => $this->getClientIp(),
            'user_agent' => $this->getUserAgent(),
            'url' => $this->request->url(true),
            'method' => $this->request->method(),
            'timestamp' => time()
        ]);
    }

    /**
     * 检查请求频率限制
     * @param string $key 限制键
     * @param int $maxRequests 最大请求数
     * @param int $timeWindow 时间窗口（秒）
     * @return bool
     */
    protected function checkRateLimit(string $key, int $maxRequests = 60, int $timeWindow = 60): bool
    {
        $cacheKey = "rate_limit:{$key}";
        $requests = \think\facade\Cache::get($cacheKey, 0);
        
        if ($requests >= $maxRequests) {
            return false;
        }
        
        \think\facade\Cache::set($cacheKey, $requests + 1, $timeWindow);
        return true;
    }

    /**
     * 验证签名
     * @param array $params 参数
     * @param string $signature 签名
     * @param string $secret 密钥
     * @return bool
     */
    protected function verifySignature(array $params, string $signature, string $secret): bool
    {
        // 移除签名参数
        unset($params['signature']);
        
        // 按键名排序
        ksort($params);
        
        // 构建查询字符串
        $queryString = http_build_query($params);
        
        // 计算签名
        $expectedSignature = hash_hmac('sha256', $queryString, $secret);
        
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * 生成随机字符串
     * @param int $length 长度
     * @param string $chars 字符集
     * @return string
     */
    protected function generateRandomString(int $length = 32, string $chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'): string
    {
        $str = '';
        $max = strlen($chars) - 1;
        
        for ($i = 0; $i < $length; $i++) {
            $str .= $chars[mt_rand(0, $max)];
        }
        
        return $str;
    }

    /**
     * 格式化金额
     * @param float $amount 金额
     * @param int $decimals 小数位数
     * @return string
     */
    protected function formatAmount(float $amount, int $decimals = 8): string
    {
        return number_format($amount, $decimals, '.', '');
    }

    /**
     * 安全的浮点数比较
     * @param float $a
     * @param float $b
     * @param float $epsilon 精度
     * @return bool
     */
    protected function floatEquals(float $a, float $b, float $epsilon = 0.00000001): bool
    {
        return abs($a - $b) < $epsilon;
    }

    /**
     * 检查是否为移动端
     * @return bool
     */
    protected function isMobile(): bool
    {
        $userAgent = $this->getUserAgent();
        return preg_match('/(android|iphone|ipad|mobile)/i', $userAgent);
    }

    /**
     * 获取语言
     * @return string
     */
    protected function getLanguage(): string
    {
        return $this->request->param('lang', 'zh-cn');
    }

    /**
     * 跳转到指定URL
     * @param string $url
     * @param int $code
     */
    protected function redirect(string $url, int $code = 302)
    {
        header("Location: {$url}", true, $code);
        exit;
    }
}
