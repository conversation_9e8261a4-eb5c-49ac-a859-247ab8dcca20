# GVD客服系统部署指南

## 📋 系统概述

GVD客服系统是一个基于WebSocket的实时客服解决方案，提供用户端、代理端和管理端的完整客服体验。

### 🎯 主要功能

- **实时聊天**: WebSocket实时通信，毫秒级消息推送
- **多端支持**: 用户端、代理端、管理端三套界面
- **消息管理**: 文本、图片、表情包多类型消息
- **会话管理**: 会话创建、转接、关闭等完整流程
- **快捷回复**: 提高客服效率的快捷回复系统
- **统计分析**: 完整的工作量和性能统计

## 🛠️ 系统要求

### 服务器环境
- **操作系统**: Linux (推荐 Ubuntu 20.04+ / CentOS 8+)
- **PHP版本**: >= 7.4.0 (推荐 8.0+)
- **MySQL版本**: >= 5.7.0 (推荐 8.0+)
- **内存**: >= 2GB (推荐 4GB+)
- **磁盘**: >= 10GB 可用空间

### PHP扩展要求
```bash
# 必需扩展
php-pdo
php-pdo-mysql
php-json
php-mbstring
php-openssl
php-curl
php-fileinfo
php-gd
php-zip

# 推荐扩展
php-redis
php-swoole
php-event
```

### 端口要求
- **HTTP**: 80/443 (Web服务)
- **WebSocket**: 2346 (客服WebSocket服务)
- **MySQL**: 3306 (数据库)

## 📦 安装步骤

### 1. 环境检查

运行系统检查脚本：
```bash
cd /path/to/CMCC_COMPLETE
php scripts/check-customer-service.php
```

### 2. 数据库初始化

导入客服系统数据表：
```bash
mysql -u root -p gvd_trading < database/gvd_trading.sql
```

### 3. 配置文件设置

编辑客服系统配置：
```bash
cp config/customer.php.example config/customer.php
vim config/customer.php
```

关键配置项：
```php
// WebSocket服务配置
'websocket' => [
    'host' => '0.0.0.0',
    'port' => 2346,
    'worker_num' => 1,
    'max_connections' => 1000,
],

// 消息配置
'message' => [
    'max_length' => 1000,
    'retention_days' => 30,
],

// 文件上传配置
'upload' => [
    'max_size' => 5 * 1024 * 1024, // 5MB
    'path' => 'uploads/customer/',
],
```

### 4. 权限设置

设置必要的目录权限：
```bash
chmod -R 755 public/
chmod -R 777 runtime/
chmod -R 777 uploads/
```

### 5. 启动服务

使用启动脚本：
```bash
# 赋予执行权限
chmod +x scripts/start-customer-service.sh

# 启动WebSocket服务
./scripts/start-customer-service.sh start

# 检查服务状态
./scripts/start-customer-service.sh status
```

## 🔧 Nginx配置

### WebSocket代理配置

在Nginx配置中添加WebSocket代理：
```nginx
# WebSocket代理
location /ws/ {
    proxy_pass http://127.0.0.1:2346;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_read_timeout 86400;
}

# 静态文件缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# 上传文件访问
location /uploads/ {
    alias /path/to/CMCC_COMPLETE/uploads/;
    expires 1y;
}
```

### SSL配置 (推荐)

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/key.pem;
    
    # WebSocket SSL代理
    location /ws/ {
        proxy_pass http://127.0.0.1:2346;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_ssl_verify off;
    }
}
```

## 🔄 进程管理

### 使用Systemd (推荐)

安装系统服务：
```bash
sudo ./scripts/start-customer-service.sh install
```

管理服务：
```bash
# 启动服务
sudo systemctl start gvd-customer-service

# 停止服务
sudo systemctl stop gvd-customer-service

# 重启服务
sudo systemctl restart gvd-customer-service

# 查看状态
sudo systemctl status gvd-customer-service

# 查看日志
sudo journalctl -u gvd-customer-service -f
```

### 使用Supervisor

创建Supervisor配置：
```ini
[program:gvd-websocket]
command=php think websocket:start
directory=/path/to/CMCC_COMPLETE
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/gvd-websocket.log
```

## 📊 监控和维护

### 日志管理

查看WebSocket服务日志：
```bash
./scripts/start-customer-service.sh logs websocket
```

查看清理任务日志：
```bash
./scripts/start-customer-service.sh logs cleanup
```

### 定时任务

添加到crontab：
```bash
# 每天凌晨2点运行数据清理
0 2 * * * cd /path/to/CMCC_COMPLETE && php think customer:cleanup

# 每小时检查服务状态
0 * * * * cd /path/to/CMCC_COMPLETE && ./scripts/start-customer-service.sh status
```

### 性能监控

监控关键指标：
- WebSocket连接数
- 消息处理速度
- 内存使用情况
- 磁盘空间使用

## 🚀 性能优化

### 1. 数据库优化

```sql
-- 为客服表添加索引
ALTER TABLE gvd_customer_sessions ADD INDEX idx_user_status (user_id, status);
ALTER TABLE gvd_customer_messages ADD INDEX idx_session_time (session_id, created_at);
ALTER TABLE gvd_customer_online_status ADD INDEX idx_user_type_status (user_type, status);
```

### 2. Redis缓存

配置Redis缓存以提高性能：
```php
// config/cache.php
'redis' => [
    'type' => 'redis',
    'host' => '127.0.0.1',
    'port' => 6379,
    'password' => '',
    'select' => 0,
    'timeout' => 0,
    'expire' => 0,
    'persistent' => false,
    'prefix' => 'gvd_customer:',
],
```

### 3. 文件存储优化

使用CDN或对象存储：
```php
// 配置阿里云OSS或腾讯云COS
'upload' => [
    'driver' => 'oss', // 或 'cos'
    'bucket' => 'your-bucket',
    'region' => 'your-region',
    'access_key' => 'your-access-key',
    'secret_key' => 'your-secret-key',
],
```

## 🔒 安全配置

### 1. 防火墙设置

```bash
# 只允许必要端口
ufw allow 80
ufw allow 443
ufw allow 2346
ufw enable
```

### 2. 访问控制

在Nginx中限制管理端访问：
```nginx
location /admin/ {
    allow ***********/24;  # 允许内网访问
    deny all;              # 拒绝其他访问
}
```

### 3. 速率限制

```nginx
# 限制WebSocket连接频率
limit_req_zone $binary_remote_addr zone=ws:10m rate=10r/m;

location /ws/ {
    limit_req zone=ws burst=5;
    # ... 其他配置
}
```

## 🐛 故障排除

### 常见问题

1. **WebSocket连接失败**
   - 检查端口是否开放
   - 检查防火墙设置
   - 查看WebSocket服务日志

2. **消息发送失败**
   - 检查数据库连接
   - 查看PHP错误日志
   - 检查文件权限

3. **文件上传失败**
   - 检查uploads目录权限
   - 检查PHP上传限制
   - 查看磁盘空间

### 调试命令

```bash
# 检查WebSocket端口
netstat -tlnp | grep 2346

# 检查进程状态
ps aux | grep websocket

# 查看系统资源
top -p $(cat runtime/pids/websocket.pid)

# 测试数据库连接
php -r "new PDO('mysql:host=localhost;dbname=gvd_trading', 'user', 'pass');"
```

## 📈 扩展部署

### 负载均衡

使用多个WebSocket服务实例：
```nginx
upstream websocket_backend {
    server 127.0.0.1:2346;
    server 127.0.0.1:2347;
    server 127.0.0.1:2348;
}

location /ws/ {
    proxy_pass http://websocket_backend;
    # ... 其他配置
}
```

### 集群部署

使用Redis作为消息队列：
```php
// 配置Redis消息队列
'queue' => [
    'driver' => 'redis',
    'connection' => 'default',
    'queue' => 'customer_messages',
],
```

## 📞 技术支持

如遇到部署问题，请：

1. 查看系统日志
2. 运行检查脚本
3. 参考故障排除指南
4. 联系技术支持团队

---

**部署完成后，请访问以下地址测试功能：**

- 用户端客服: `https://your-domain.com/` (右下角客服按钮)
- 管理端客服: `https://your-domain.com/admin/customer.html`
- 代理端客服: `https://your-domain.com/agent/customer.html`
