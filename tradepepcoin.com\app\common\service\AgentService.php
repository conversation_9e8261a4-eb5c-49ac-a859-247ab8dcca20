<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\User;
use app\common\model\UserAsset;
use app\common\model\Order;
use app\common\model\ContractOrder;
use app\common\model\DepositRecord;
use app\common\model\WithdrawRecord;
use app\common\model\FinancialRecord;
use think\facade\Db;
use think\facade\Log;

/**
 * 代理系统服务
 */
class AgentService
{
    /**
     * 获取代理下级用户列表
     */
    public function getAgentUsers(int $agentId, array $filters = []): array
    {
        try {
            // 验证代理权限
            $agent = User::find($agentId);
            if (!$agent || !in_array($agent->role, ['admin', 'agent'])) {
                return ['code' => 0, 'msg' => '无代理权限'];
            }

            $query = User::where('agent_id', $agentId);

            // 筛选条件
            if (!empty($filters['username'])) {
                $query->where('username', 'like', '%' . $filters['username'] . '%');
            }

            if (!empty($filters['email'])) {
                $query->where('email', 'like', '%' . $filters['email'] . '%');
            }

            if (isset($filters['user_type'])) {
                $query->where('user_type', $filters['user_type']);
            }

            if (isset($filters['status'])) {
                $query->where('status', $filters['status']);
            }

            if (isset($filters['kyc_level'])) {
                $query->where('kyc_level', $filters['kyc_level']);
            }

            // 时间范围
            if (!empty($filters['start_date'])) {
                $query->where('created_at', '>=', $filters['start_date']);
            }

            if (!empty($filters['end_date'])) {
                $query->where('created_at', '<=', $filters['end_date'] . ' 23:59:59');
            }

            $users = $query->order('created_at', 'desc')
                          ->limit($filters['limit'] ?? 50)
                          ->select();

            // 获取用户资产信息
            $userList = [];
            foreach ($users as $user) {
                $userInfo = $user->toArray();
                
                // 获取用户总资产
                $totalAssets = UserAsset::where('user_id', $user->id)->sum('total');
                $userInfo['total_assets'] = $totalAssets;

                // 获取用户交易统计
                $tradeStats = $this->getUserTradeStats($user->id);
                $userInfo['trade_stats'] = $tradeStats;

                $userList[] = $userInfo;
            }

            return [
                'code' => 1,
                'data' => $userList,
                'total' => count($userList)
            ];

        } catch (\Exception $e) {
            Log::error('获取代理用户列表失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取用户列表失败'];
        }
    }

    /**
     * 获取代理用户详情
     */
    public function getAgentUserDetail(int $agentId, int $userId): array
    {
        try {
            // 验证代理权限
            $agent = User::find($agentId);
            if (!$agent || !in_array($agent->role, ['admin', 'agent'])) {
                return ['code' => 0, 'msg' => '无代理权限'];
            }

            // 验证用户归属
            $user = User::find($userId);
            if (!$user) {
                return ['code' => 0, 'msg' => '用户不存在'];
            }

            // 管理员可以查看所有用户，代理只能查看自己的下级
            if ($agent->role === 'agent' && $user->agent_id !== $agentId) {
                return ['code' => 0, 'msg' => '无权限查看该用户'];
            }

            $userInfo = $user->toArray();

            // 获取用户资产
            $assets = UserAsset::where('user_id', $userId)->select();
            $userInfo['assets'] = $assets->toArray();

            // 获取交易统计
            $userInfo['trade_stats'] = $this->getUserTradeStats($userId);

            // 获取最近订单
            $recentOrders = Order::where('user_id', $userId)
                                ->order('created_at', 'desc')
                                ->limit(10)
                                ->select();
            $userInfo['recent_orders'] = $recentOrders->toArray();

            // 获取最近合约订单
            $recentContracts = ContractOrder::where('user_id', $userId)
                                           ->order('created_at', 'desc')
                                           ->limit(10)
                                           ->select();
            $userInfo['recent_contracts'] = $recentContracts->toArray();

            // 获取充值记录
            $deposits = DepositRecord::where('user_id', $userId)
                                   ->order('created_at', 'desc')
                                   ->limit(10)
                                   ->select();
            $userInfo['deposits'] = $deposits->toArray();

            // 获取提币记录
            $withdraws = WithdrawRecord::where('user_id', $userId)
                                     ->order('created_at', 'desc')
                                     ->limit(10)
                                     ->select();
            $userInfo['withdraws'] = $withdraws->toArray();

            return [
                'code' => 1,
                'data' => $userInfo
            ];

        } catch (\Exception $e) {
            Log::error('获取代理用户详情失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取用户详情失败'];
        }
    }

    /**
     * 获取代理统计数据
     */
    public function getAgentStats(int $agentId): array
    {
        try {
            // 验证代理权限
            $agent = User::find($agentId);
            if (!$agent || !in_array($agent->role, ['admin', 'agent'])) {
                return ['code' => 0, 'msg' => '无代理权限'];
            }

            $stats = [];

            // 用户统计
            $userQuery = User::where('agent_id', $agentId);
            $stats['total_users'] = $userQuery->count();
            $stats['formal_users'] = $userQuery->where('user_type', UserService::USER_TYPE_FORMAL)->count();
            $stats['test_users'] = $userQuery->where('user_type', UserService::USER_TYPE_TEST)->count();
            $stats['active_users'] = $userQuery->where('status', 1)->count();

            // 今日新增用户
            $stats['today_new_users'] = $userQuery->where('created_at', '>=', date('Y-m-d'))->count();

            // 本月新增用户
            $stats['month_new_users'] = $userQuery->where('created_at', '>=', date('Y-m-01'))->count();

            // 资产统计
            $userIds = $userQuery->column('id');
            if (!empty($userIds)) {
                $totalAssets = UserAsset::whereIn('user_id', $userIds)->sum('total');
                $stats['total_assets'] = $totalAssets;

                // 交易统计
                $totalTrades = Order::whereIn('user_id', $userIds)->count();
                $stats['total_trades'] = $totalTrades;

                $totalTradeVolume = Order::whereIn('user_id', $userIds)->sum('total');
                $stats['total_trade_volume'] = $totalTradeVolume;

                // 合约统计
                $totalContracts = ContractOrder::whereIn('user_id', $userIds)->count();
                $stats['total_contracts'] = $totalContracts;

                $totalContractVolume = ContractOrder::whereIn('user_id', $userIds)->sum('amount');
                $stats['total_contract_volume'] = $totalContractVolume;

                // 充值统计
                $totalDeposits = DepositRecord::whereIn('user_id', $userIds)
                                             ->where('status', 1)
                                             ->sum('amount');
                $stats['total_deposits'] = $totalDeposits;

                // 提币统计
                $totalWithdraws = WithdrawRecord::whereIn('user_id', $userIds)
                                               ->where('status', 1)
                                               ->sum('amount');
                $stats['total_withdraws'] = $totalWithdraws;
            } else {
                $stats['total_assets'] = 0;
                $stats['total_trades'] = 0;
                $stats['total_trade_volume'] = 0;
                $stats['total_contracts'] = 0;
                $stats['total_contract_volume'] = 0;
                $stats['total_deposits'] = 0;
                $stats['total_withdraws'] = 0;
            }

            return [
                'code' => 1,
                'data' => $stats
            ];

        } catch (\Exception $e) {
            Log::error('获取代理统计失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取统计数据失败'];
        }
    }

    /**
     * 代理创建用户
     */
    public function createUser(int $agentId, array $userData): array
    {
        try {
            // 验证代理权限
            $agent = User::find($agentId);
            if (!$agent || !in_array($agent->role, ['admin', 'agent'])) {
                return ['code' => 0, 'msg' => '无代理权限'];
            }

            // 调用用户服务创建用户
            $userService = new UserService();
            $result = $userService->adminRegister($userData, $agentId);

            if ($result['code']) {
                Log::info("代理创建用户成功", [
                    'agent_id' => $agentId,
                    'user_id' => $result['data']['user_id'],
                    'username' => $userData['username']
                ]);
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('代理创建用户失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '创建用户失败'];
        }
    }

    /**
     * 获取用户交易统计
     */
    private function getUserTradeStats(int $userId): array
    {
        $stats = [];

        // 现货交易统计
        $stats['spot_trades'] = Order::where('user_id', $userId)->count();
        $stats['spot_volume'] = Order::where('user_id', $userId)->sum('total');

        // 合约交易统计
        $stats['contract_trades'] = ContractOrder::where('user_id', $userId)->count();
        $stats['contract_volume'] = ContractOrder::where('user_id', $userId)->sum('amount');

        // 盈亏统计
        $winContracts = ContractOrder::where('user_id', $userId)
                                   ->where('status', ContractService::STATUS_WIN)
                                   ->count();
        $loseContracts = ContractOrder::where('user_id', $userId)
                                    ->where('status', ContractService::STATUS_LOSE)
                                    ->count();

        $stats['win_contracts'] = $winContracts;
        $stats['lose_contracts'] = $loseContracts;
        $stats['win_rate'] = ($winContracts + $loseContracts) > 0 ? 
                            round($winContracts / ($winContracts + $loseContracts) * 100, 2) : 0;

        return $stats;
    }

    /**
     * 验证代理权限
     */
    public function checkAgentPermission(int $agentId, int $userId): bool
    {
        $agent = User::find($agentId);
        if (!$agent) {
            return false;
        }

        // 管理员有所有权限
        if ($agent->role === 'admin') {
            return true;
        }

        // 代理只能管理自己的下级用户
        if ($agent->role === 'agent') {
            $user = User::find($userId);
            return $user && $user->agent_id === $agentId;
        }

        return false;
    }

    /**
     * 获取代理佣金统计
     */
    public function getCommissionStats(int $agentId, string $startDate, string $endDate): array
    {
        try {
            // 验证代理权限
            $agent = User::find($agentId);
            if (!$agent || !in_array($agent->role, ['admin', 'agent'])) {
                return ['code' => 0, 'msg' => '无代理权限'];
            }

            // 这里应该从佣金表统计，暂时返回模拟数据
            $stats = [
                'total_commission' => 0,
                'period_commission' => 0,
                'commission_users' => 0,
                'commission_orders' => 0
            ];

            return [
                'code' => 1,
                'data' => $stats
            ];

        } catch (\Exception $e) {
            Log::error('获取代理佣金统计失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取佣金统计失败'];
        }
    }

    /**
     * 获取团队业绩
     */
    public function getTeamPerformance(int $agentId, string $period): array
    {
        try {
            // 验证代理权限
            $agent = User::find($agentId);
            if (!$agent || !in_array($agent->role, ['admin', 'agent'])) {
                return ['code' => 0, 'msg' => '无代理权限'];
            }

            // 获取时间范围
            switch ($period) {
                case 'today':
                    $startDate = date('Y-m-d');
                    $endDate = date('Y-m-d');
                    break;
                case 'week':
                    $startDate = date('Y-m-d', strtotime('-7 days'));
                    $endDate = date('Y-m-d');
                    break;
                case 'month':
                    $startDate = date('Y-m-01');
                    $endDate = date('Y-m-d');
                    break;
                default:
                    $startDate = date('Y-m-d');
                    $endDate = date('Y-m-d');
            }

            // 获取下级用户
            $userIds = User::where('agent_id', $agentId)->column('id');

            $performance = [
                'period' => $period,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'total_users' => count($userIds),
                'active_users' => 0,
                'total_trades' => 0,
                'total_volume' => 0,
                'total_commission' => 0
            ];

            if (!empty($userIds)) {
                // 统计交易数据
                $performance['total_trades'] = Order::whereIn('user_id', $userIds)
                    ->where('created_at', '>=', $startDate)
                    ->where('created_at', '<=', $endDate . ' 23:59:59')
                    ->count();

                $performance['total_volume'] = Order::whereIn('user_id', $userIds)
                    ->where('created_at', '>=', $startDate)
                    ->where('created_at', '<=', $endDate . ' 23:59:59')
                    ->sum('total');

                // 活跃用户数
                $performance['active_users'] = Order::whereIn('user_id', $userIds)
                    ->where('created_at', '>=', $startDate)
                    ->where('created_at', '<=', $endDate . ' 23:59:59')
                    ->distinct()
                    ->count('user_id');
            }

            return [
                'code' => 1,
                'data' => $performance
            ];

        } catch (\Exception $e) {
            Log::error('获取团队业绩失败: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取团队业绩失败'];
        }
    }

    /**
     * 获取用户资产统计
     */
    public function getUserAssetStats(int $userId): array
    {
        try {
            $assets = UserAsset::where('user_id', $userId)->select();

            $stats = [
                'total_assets' => 0,
                'available_assets' => 0,
                'frozen_assets' => 0,
                'asset_details' => []
            ];

            foreach ($assets as $asset) {
                $stats['total_assets'] += $asset->total;
                $stats['available_assets'] += $asset->available;
                $stats['frozen_assets'] += $asset->frozen;

                $stats['asset_details'][] = [
                    'coin_symbol' => $asset->coin_symbol,
                    'available' => $asset->available,
                    'frozen' => $asset->frozen,
                    'total' => $asset->total
                ];
            }

            return $stats;

        } catch (\Exception $e) {
            Log::error('获取用户资产统计失败: ' . $e->getMessage());
            return [];
        }
    }
}
