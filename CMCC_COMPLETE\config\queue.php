<?php
// +----------------------------------------------------------------------
// | 队列设置
// +----------------------------------------------------------------------

use think\facade\Env;

return [
    // 默认队列连接
    'default'     => Env::get('queue.connector', 'redis'),

    // 队列连接配置
    'connections' => [
        'sync' => [
            'type' => 'sync',
        ],

        'database' => [
            'type'       => 'database',
            'queue'      => 'default',
            'table'      => 'jobs',
            'connection' => null,
        ],

        'redis' => [
            'type'       => 'redis',
            'host'       => Env::get('redis.hostname', '127.0.0.1'),
            'port'       => Env::get('redis.port', 6379),
            'password'   => Env::get('redis.password', ''),
            'select'     => Env::get('redis.select', 0),
            'timeout'    => 0,
            'persistent' => false,
            'queue'      => Env::get('queue.queue', 'gvd_queue'),
        ],
    ],

    // 任务失败配置
    'failed' => [
        'type'  => 'none',
        'table' => 'failed_jobs',
    ],
];
