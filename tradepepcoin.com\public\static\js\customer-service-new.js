console.log("客服系统开始加载");

class CustomerService {
    constructor() {
        console.log("CustomerService构造函数执行");
        this.createButton();
    }
    
    createButton() {
        console.log("创建客服按钮");
        const button = document.createElement('div');
        button.innerHTML = '<div style="position:fixed;bottom:20px;right:20px;width:60px;height:60px;background:#007bff;border-radius:50%;display:flex;align-items:center;justify-content:center;color:white;cursor:pointer;z-index:9999;font-size:12px;">客服</div>';
        document.body.appendChild(button);
        
        button.onclick = function() {
            alert('客服功能正常！');
        };
    }
}

window.CustomerService = CustomerService;
document.addEventListener('DOMContentLoaded', function() {
    new CustomerService();
});
