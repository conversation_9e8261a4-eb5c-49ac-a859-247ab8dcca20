<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * IDO订单模型
 */
class IdoOrder extends Model
{
    protected $name = 'gvd_ido_orders';
    
    protected $type = [
        'amount' => 'float',
        'token_amount' => 'float',
        'token_price' => 'float'
    ];

    // 订单状态
    const STATUS_PENDING = 1;     // 待处理
    const STATUS_SUCCESS = 2;     // 成功
    const STATUS_FAILED = 3;      // 失败
    const STATUS_CANCELLED = 4;   // 已取消

    /**
     * 获取状态文本
     */
    public function getStatusText(): string
    {
        $statusMap = [
            self::STATUS_PENDING => '待处理',
            self::STATUS_SUCCESS => '成功',
            self::STATUS_FAILED => '失败',
            self::STATUS_CANCELLED => '已取消'
        ];

        return $statusMap[$this->status] ?? '未知';
    }

    /**
     * 获取订单信息
     */
    public function getOrderInfo(): array
    {
        return [
            'id' => $this->id,
            'order_id' => $this->order_id,
            'user_id' => $this->user_id,
            'project_id' => $this->project_id,
            'amount' => $this->amount,
            'token_amount' => $this->token_amount,
            'token_price' => $this->token_price,
            'status' => $this->status,
            'status_text' => $this->getStatusText(),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }

    /**
     * 创建认购订单
     */
    public static function createOrder(int $userId, int $projectId, float $amount): array
    {
        try {
            \think\facade\Db::startTrans();

            // 获取项目信息
            $project = IdoProject::find($projectId);
            if (!$project) {
                throw new \Exception('项目不存在');
            }

            // 检查项目状态
            if (!$project->canPurchase()) {
                throw new \Exception('项目当前不可认购');
            }

            // 检查认购金额
            if ($amount < $project->min_purchase) {
                throw new \Exception('认购金额不能少于最小金额');
            }

            if ($amount > $project->max_purchase) {
                throw new \Exception('认购金额不能超过最大金额');
            }

            // 检查用户已认购金额
            $userPurchased = $project->getUserPurchaseAmount($userId);
            if ($userPurchased + $amount > $project->max_purchase) {
                throw new \Exception('超过个人最大认购限额');
            }

            // 检查项目剩余额度
            $remaining = $project->target_amount - $project->raised_amount;
            if ($amount > $remaining) {
                throw new \Exception('认购金额超过项目剩余额度');
            }

            // 检查用户USDT余额
            $userAsset = UserAsset::getUserAsset($userId, 'USDT');
            if (!$userAsset || $userAsset->available < $amount) {
                throw new \Exception('USDT余额不足');
            }

            // 冻结用户资产
            if (!$userAsset->freezeBalance($amount)) {
                throw new \Exception('资产冻结失败');
            }

            // 计算获得的代币数量
            $tokenAmount = $amount / $project->token_price;

            // 创建订单
            $orderData = [
                'order_id' => self::generateOrderId(),
                'user_id' => $userId,
                'project_id' => $projectId,
                'amount' => $amount,
                'token_amount' => $tokenAmount,
                'token_price' => $project->token_price,
                'status' => self::STATUS_SUCCESS, // 直接成功
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $order = self::create($orderData);

            // 扣除冻结资产
            $userAsset->subBalance($amount, 'frozen');

            // 增加项目认购金额
            $project->addRaisedAmount($amount);

            // 给用户添加代币资产
            $tokenAsset = UserAsset::getUserAsset($userId, $project->symbol);
            if (!$tokenAsset) {
                // 创建新的代币资产
                UserAsset::create([
                    'user_id' => $userId,
                    'coin_symbol' => $project->symbol,
                    'coin_name' => $project->name,
                    'available' => $tokenAmount,
                    'frozen' => 0,
                    'total' => $tokenAmount,
                    'decimals' => 8,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            } else {
                $tokenAsset->addBalance($tokenAmount, 'available');
            }

            \think\facade\Db::commit();

            return [
                'code' => 1,
                'msg' => '认购成功',
                'data' => $order->getOrderInfo()
            ];

        } catch (\Exception $e) {
            \think\facade\Db::rollback();
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 生成订单ID
     */
    private static function generateOrderId(): string
    {
        return 'IDO' . date('YmdHis') . mt_rand(1000, 9999);
    }

    /**
     * 获取用户订单列表
     */
    public static function getUserOrders(int $userId, array $params = []): array
    {
        try {
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 20;

            $orders = self::where('user_id', $userId)
                         ->with(['project'])
                         ->order('created_at', 'desc')
                         ->paginate([
                             'list_rows' => $limit,
                             'page' => $page
                         ]);

            $list = [];
            foreach ($orders->items() as $order) {
                $orderInfo = $order->getOrderInfo();
                if ($order->project) {
                    $orderInfo['project_name'] = $order->project->name;
                    $orderInfo['project_symbol'] = $order->project->symbol;
                }
                $list[] = $orderInfo;
            }

            return [
                'code' => 1,
                'data' => [
                    'list' => $list,
                    'total' => $orders->total(),
                    'page' => $page,
                    'limit' => $limit
                ]
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '获取失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取项目订单列表
     */
    public static function getProjectOrders(int $projectId, array $params = []): array
    {
        try {
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 20;

            $orders = self::where('project_id', $projectId)
                         ->where('status', self::STATUS_SUCCESS)
                         ->with(['user'])
                         ->order('created_at', 'desc')
                         ->paginate([
                             'list_rows' => $limit,
                             'page' => $page
                         ]);

            $list = [];
            foreach ($orders->items() as $order) {
                $orderInfo = $order->getOrderInfo();
                if ($order->user) {
                    $orderInfo['username'] = $order->user->username;
                }
                $list[] = $orderInfo;
            }

            return [
                'code' => 1,
                'data' => [
                    'list' => $list,
                    'total' => $orders->total(),
                    'page' => $page,
                    'limit' => $limit
                ]
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '获取失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取订单统计
     */
    public static function getOrderStats(int $userId = 0): array
    {
        try {
            $query = self::query();
            
            if ($userId > 0) {
                $query->where('user_id', $userId);
            }

            $totalOrders = $query->count();
            $successOrders = $query->where('status', self::STATUS_SUCCESS)->count();
            $totalAmount = $query->where('status', self::STATUS_SUCCESS)->sum('amount');

            return [
                'code' => 1,
                'data' => [
                    'total_orders' => $totalOrders,
                    'success_orders' => $successOrders,
                    'total_amount' => $totalAmount
                ]
            ];

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '获取失败：' . $e->getMessage()];
        }
    }

    /**
     * 关联项目
     */
    public function project()
    {
        return $this->belongsTo(IdoProject::class, 'project_id');
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
