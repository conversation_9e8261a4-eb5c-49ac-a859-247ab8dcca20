<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理商登录 - 数字货币交易平台</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: Arial, sans-serif; background: #1a1a1a; color: #fff; 
            min-height: 100vh; display: flex; align-items: center; justify-content: center;
            background-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .container { 
            max-width: 400px; width: 100%; padding: 40px; background: #2d2d2d; 
            border-radius: 10px; border: 1px solid #333; box-shadow: 0 10px 30px rgba(0,0,0,0.5);
        }
        .logo { 
            text-align: center; font-size: 32px; font-weight: bold; margin-bottom: 30px; 
            color: #007bff; 
        }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; color: #ccc; }
        .form-group input { 
            width: 100%; padding: 12px; border: 1px solid #333; background: #1a1a1a; 
            color: #fff; border-radius: 5px; font-size: 16px; transition: border-color 0.3s;
        }
        .form-group input:focus { outline: none; border-color: #007bff; }
        .btn { 
            width: 100%; padding: 15px; background: #007bff; color: #fff; border: none; 
            border-radius: 5px; font-size: 16px; font-weight: bold; cursor: pointer; 
            transition: background 0.3s; 
        }
        .btn:hover { background: #0056b3; }
        .back-home { text-align: center; margin-bottom: 20px; }
        .back-home a { color: #999; text-decoration: none; }
        .notice { 
            background: #333; padding: 15px; border-radius: 5px; margin-bottom: 20px;
            font-size: 12px; color: #ffc107; text-align: center;
        }
        .notice h4 { margin-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="back-home">
            <a href="/">← 返回首页</a>
        </div>
        <div class="logo">🏢 代理商登录</div>
        
        <div class="notice">
            <h4>⚠️ 重要提示</h4>
            <p>代理商账号由管理员开通</p>
            <p>如需申请代理，请联系客服</p>
        </div>
        
        <form id="agentLoginForm" action="/agent/login" method="post">
            <div class="form-group">
                <label>代理商账号</label>
                <input type="text" name="username" placeholder="请输入代理商账号" required>
            </div>
            <div class="form-group">
                <label>密码</label>
                <input type="password" name="password" placeholder="请输入密码" required>
            </div>
            <button type="submit" class="btn">登录代理后台</button>
        </form>
        
        <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
            <p>代理商不能自己注册</p>
            <p>需要管理员开通代理权限</p>
        </div>
    </div>

    <script>
        // 代理登录表单提交
        document.getElementById('agentLoginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const username = formData.get('username');
            const password = formData.get('password');
            
            // 这里应该提交到ThinkPHP的代理登录接口
            console.log('代理登录:', { username, password });
            
            // 模拟登录验证
            if (username && password) {
                // 实际应该调用API
                fetch('/agent/login', {
                    method: 'POST',
                    body: formData
                }).then(response => {
                    if (response.ok) {
                        window.location.href = '/agent/dashboard';
                    } else {
                        alert('登录失败，请检查账号密码');
                    }
                }).catch(error => {
                    console.error('登录错误:', error);
                    alert('网络错误，请稍后重试');
                });
            }
        });
    </script>
</body>
</html>
