<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

/**
 * 钱包服务类 - 适配现有数据库
 * 管理用户数字货币资产
 */
class WalletService
{
    // 交易类型
    const TRANSACTION_DEPOSIT = 'deposit';       // 充值
    const TRANSACTION_WITHDRAW = 'withdraw';     // 提现
    const TRANSACTION_TRADE = 'trade';           // 交易
    const TRANSACTION_TRANSFER = 'transfer';     // 转账
    const TRANSACTION_FEE = 'fee';               // 手续费
    const TRANSACTION_BONUS = 'bonus';           // 奖励
    const TRANSACTION_ADMIN = 'admin';           // 管理员调整

    // 状态
    const STATUS_PENDING = 'pending';
    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * 获取用户资产余额
     */
    public function getUserBalance(int $userId, string $coinSymbol = 'USDT'): array
    {
        try {
            $asset = Db::name('gvd_user_assets')
                ->where('user_id', $userId)
                ->where('coin_symbol', $coinSymbol)
                ->find();

            if (!$asset) {
                // 创建资产记录
                $asset = $this->createUserAsset($userId, $coinSymbol);
            }

            return [
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'available' => (float)$asset['available'],
                    'frozen' => (float)$asset['frozen'],
                    'total' => (float)$asset['total'],
                    'coin_symbol' => $asset['coin_symbol'],
                    'coin_name' => $asset['coin_name']
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取用户资产余额失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '获取失败'];
        }
    }

    /**
     * 创建用户资产记录
     */
    public function createUserAsset(int $userId, string $coinSymbol): array
    {
        try {
            // 获取币种信息
            $coinInfo = $this->getCoinInfo($coinSymbol);

            $assetData = [
                'user_id' => $userId,
                'coin_symbol' => $coinSymbol,
                'coin_name' => $coinInfo['name'],
                'available' => 0,
                'frozen' => 0,
                'total' => 0,
                'decimals' => $coinInfo['decimals'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $assetId = Db::name('gvd_user_assets')->insertGetId($assetData);
            $assetData['id'] = $assetId;

            return $assetData;

        } catch (\Exception $e) {
            Log::error('创建用户资产失败：' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 获取币种信息
     */
    private function getCoinInfo(string $coinSymbol): array
    {
        $coinMap = [
            'USDT' => ['name' => 'Tether USD', 'decimals' => 8],
            'BTC' => ['name' => 'Bitcoin', 'decimals' => 8],
            'ETH' => ['name' => 'Ethereum', 'decimals' => 8],
            'BNB' => ['name' => 'Binance Coin', 'decimals' => 8],
        ];

        return $coinMap[$coinSymbol] ?? ['name' => $coinSymbol, 'decimals' => 8];
    }

    /**
     * 增加用户余额
     */
    public function addBalance(int $userId, string $coinSymbol, float $amount, string $type = self::TRANSACTION_ADMIN, string $remark = ''): array
    {
        try {
            Db::startTrans();

            // 获取用户资产
            $asset = Db::name('gvd_user_assets')
                ->where('user_id', $userId)
                ->where('coin_symbol', $coinSymbol)
                ->find();

            if (!$asset) {
                $asset = $this->createUserAsset($userId, $coinSymbol);
            }

            // 更新余额
            $newAvailable = $asset['available'] + $amount;
            $newTotal = $newAvailable + $asset['frozen'];

            Db::name('gvd_user_assets')
                ->where('id', $asset['id'])
                ->update([
                    'available' => $newAvailable,
                    'total' => $newTotal,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            // 记录交易流水
            $this->recordTransaction($userId, $coinSymbol, $amount, $type, $remark);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '余额更新成功',
                'data' => [
                    'amount' => $amount,
                    'new_balance' => $newAvailable
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('增加余额失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '操作失败'];
        }
    }

    /**
     * 记录资产变动
     */
    private function recordTransaction(int $userId, string $coinSymbol, float $amount, string $type, string $remark): void
    {
        try {
            // 获取变动后的余额
            $asset = Db::name('gvd_user_assets')
                ->where('user_id', $userId)
                ->where('coin_symbol', $coinSymbol)
                ->find();

            $balance = $asset ? $asset['available'] : 0;

            Db::name('gvd_asset_records')->insert([
                'user_id' => $userId,
                'coin_symbol' => $coinSymbol,
                'amount' => $amount,
                'balance' => $balance,
                'type' => $type,
                'description' => $remark,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            Log::error('记录资产变动失败：' . $e->getMessage());
        }
    }

    /**
     * 冻结用户余额
     */
    public function freezeBalance(int $userId, string $coinSymbol, float $amount): array
    {
        try {
            Db::startTrans();

            $asset = Db::name('gvd_user_assets')
                ->where('user_id', $userId)
                ->where('coin_symbol', $coinSymbol)
                ->find();

            if (!$asset || $asset['available'] < $amount) {
                Db::rollback();
                return ['code' => 0, 'msg' => '余额不足'];
            }

            // 更新余额
            $newAvailable = $asset['available'] - $amount;
            $newFrozen = $asset['frozen'] + $amount;

            Db::name('gvd_user_assets')
                ->where('id', $asset['id'])
                ->update([
                    'available' => $newAvailable,
                    'frozen' => $newFrozen,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '余额冻结成功',
                'data' => [
                    'frozen_amount' => $amount,
                    'available' => $newAvailable,
                    'frozen' => $newFrozen
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('冻结余额失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '冻结失败'];
        }
    }

    /**
     * 解冻用户余额
     */
    public function unfreezeBalance(int $userId, string $coinSymbol, float $amount): array
    {
        try {
            Db::startTrans();

            $asset = Db::name('gvd_user_assets')
                ->where('user_id', $userId)
                ->where('coin_symbol', $coinSymbol)
                ->find();

            if (!$asset || $asset['frozen'] < $amount) {
                Db::rollback();
                return ['code' => 0, 'msg' => '冻结余额不足'];
            }

            // 更新余额
            $newAvailable = $asset['available'] + $amount;
            $newFrozen = $asset['frozen'] - $amount;

            Db::name('gvd_user_assets')
                ->where('id', $asset['id'])
                ->update([
                    'available' => $newAvailable,
                    'frozen' => $newFrozen,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '余额解冻成功',
                'data' => [
                    'unfrozen_amount' => $amount,
                    'available' => $newAvailable,
                    'frozen' => $newFrozen
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('解冻余额失败：' . $e->getMessage());
            return ['code' => 0, 'msg' => '解冻失败'];
        }
    }

}
