<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 高级订单模型
 */
class AdvancedOrder extends Model
{
    protected $table = 'advanced_orders';

    // 设置字段信息
    protected $schema = [
        'id' => 'int',
        'user_id' => 'int',
        'order_id' => 'string',
        'symbol' => 'string',
        'type' => 'string',
        'side' => 'string',
        'amount' => 'decimal',
        'trigger_price' => 'decimal',
        'order_price' => 'decimal',
        'stop_price' => 'decimal',
        'take_profit_price' => 'decimal',
        'trailing_percent' => 'decimal',
        'status' => 'int',
        'triggered_at' => 'datetime',
        'executed_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 订单类型常量
    const TYPE_STOP_LOSS = 'stop_loss';           // 止损单
    const TYPE_TAKE_PROFIT = 'take_profit';       // 止盈单
    const TYPE_STOP_LIMIT = 'stop_limit';         // 止损限价单
    const TYPE_TRAILING_STOP = 'trailing_stop';   // 追踪止损单
    const TYPE_OCO = 'oco';                       // OCO订单(一取消全部)

    // 订单状态
    const STATUS_PENDING = 0;     // 等待触发
    const STATUS_TRIGGERED = 1;   // 已触发
    const STATUS_EXECUTED = 2;    // 已执行
    const STATUS_CANCELLED = 3;   // 已取消
    const STATUS_EXPIRED = 4;     // 已过期

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联交易对
     */
    public function tradingPair()
    {
        return $this->belongsTo(TradingPair::class, 'symbol', 'symbol');
    }

    /**
     * 创建高级订单
     */
    public static function createAdvancedOrder(array $data): array
    {
        try {
            $order = new self();
            $order->user_id = $data['user_id'];
            $order->order_id = self::generateOrderId();
            $order->symbol = $data['symbol'];
            $order->type = $data['type'];
            $order->side = $data['side'];
            $order->amount = $data['amount'];
            $order->trigger_price = $data['trigger_price'] ?? 0;
            $order->order_price = $data['order_price'] ?? 0;
            $order->stop_price = $data['stop_price'] ?? 0;
            $order->take_profit_price = $data['take_profit_price'] ?? 0;
            $order->trailing_percent = $data['trailing_percent'] ?? 0;
            $order->status = self::STATUS_PENDING;

            if ($order->save()) {
                return ['code' => 1, 'msg' => '高级订单创建成功', 'data' => $order];
            } else {
                return ['code' => 0, 'msg' => '高级订单创建失败'];
            }
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '创建失败：' . $e->getMessage()];
        }
    }

    /**
     * 触发订单
     */
    public function trigger(): bool
    {
        if ($this->status !== self::STATUS_PENDING) {
            return false;
        }

        $this->status = self::STATUS_TRIGGERED;
        $this->triggered_at = date('Y-m-d H:i:s');

        return $this->save();
    }

    /**
     * 执行订单
     */
    public function execute(): bool
    {
        if ($this->status !== self::STATUS_TRIGGERED) {
            return false;
        }

        // 创建普通订单
        $orderData = [
            'user_id' => $this->user_id,
            'symbol' => $this->symbol,
            'type' => $this->side,
            'order_type' => $this->order_price > 0 ? 'limit' : 'market',
            'amount' => $this->amount,
            'price' => $this->order_price
        ];

        $tradeService = new \app\common\service\TradeService();
        $result = $tradeService->createOrder($this->user_id, $orderData);

        if ($result['code']) {
            $this->status = self::STATUS_EXECUTED;
            $this->executed_at = date('Y-m-d H:i:s');
            return $this->save();
        }

        return false;
    }

    /**
     * 取消订单
     */
    public function cancel(): bool
    {
        if (!in_array($this->status, [self::STATUS_PENDING, self::STATUS_TRIGGERED])) {
            return false;
        }

        $this->status = self::STATUS_CANCELLED;
        $this->cancelled_at = date('Y-m-d H:i:s');

        return $this->save();
    }

    /**
     * 检查是否应该触发
     */
    public function shouldTrigger(float $currentPrice): bool
    {
        if ($this->status !== self::STATUS_PENDING) {
            return false;
        }

        switch ($this->type) {
            case self::TYPE_STOP_LOSS:
                return $this->side === 'sell' ? 
                    $currentPrice <= $this->trigger_price : 
                    $currentPrice >= $this->trigger_price;

            case self::TYPE_TAKE_PROFIT:
                return $this->side === 'sell' ? 
                    $currentPrice >= $this->trigger_price : 
                    $currentPrice <= $this->trigger_price;

            case self::TYPE_STOP_LIMIT:
                return $this->side === 'sell' ? 
                    $currentPrice <= $this->trigger_price : 
                    $currentPrice >= $this->trigger_price;

            case self::TYPE_TRAILING_STOP:
                return $this->checkTrailingStop($currentPrice);

            default:
                return false;
        }
    }

    /**
     * 检查追踪止损
     */
    private function checkTrailingStop(float $currentPrice): bool
    {
        // 获取历史最高/最低价
        $cacheKey = "trailing_stop_{$this->id}";
        $extremePrice = cache($cacheKey);

        if ($extremePrice === null) {
            $extremePrice = $currentPrice;
        }

        if ($this->side === 'sell') {
            // 卖出追踪止损，跟踪最高价
            if ($currentPrice > $extremePrice) {
                $extremePrice = $currentPrice;
                cache($cacheKey, $extremePrice, 86400);
            }

            $stopPrice = $extremePrice * (1 - $this->trailing_percent / 100);
            return $currentPrice <= $stopPrice;
        } else {
            // 买入追踪止损，跟踪最低价
            if ($currentPrice < $extremePrice) {
                $extremePrice = $currentPrice;
                cache($cacheKey, $extremePrice, 86400);
            }

            $stopPrice = $extremePrice * (1 + $this->trailing_percent / 100);
            return $currentPrice >= $stopPrice;
        }
    }

    /**
     * 生成订单ID
     */
    private static function generateOrderId(): string
    {
        return 'ADV' . date('YmdHis') . mt_rand(1000, 9999);
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr(): string
    {
        $statusTexts = [
            self::STATUS_PENDING => '等待触发',
            self::STATUS_TRIGGERED => '已触发',
            self::STATUS_EXECUTED => '已执行',
            self::STATUS_CANCELLED => '已取消',
            self::STATUS_EXPIRED => '已过期'
        ];

        return $statusTexts[$this->status] ?? '未知状态';
    }

    /**
     * 获取类型文本
     */
    public function getTypeTextAttr(): string
    {
        $typeTexts = [
            self::TYPE_STOP_LOSS => '止损单',
            self::TYPE_TAKE_PROFIT => '止盈单',
            self::TYPE_STOP_LIMIT => '止损限价单',
            self::TYPE_TRAILING_STOP => '追踪止损单',
            self::TYPE_OCO => 'OCO订单'
        ];

        return $typeTexts[$this->type] ?? '未知类型';
    }

    /**
     * 搜索器：按用户
     */
    public function searchUserIdAttr($query, $value)
    {
        if ($value) {
            $query->where('user_id', $value);
        }
    }

    /**
     * 搜索器：按交易对
     */
    public function searchSymbolAttr($query, $value)
    {
        if ($value) {
            $query->where('symbol', $value);
        }
    }

    /**
     * 搜索器：按类型
     */
    public function searchTypeAttr($query, $value)
    {
        if ($value) {
            $query->where('type', $value);
        }
    }

    /**
     * 搜索器：按状态
     */
    public function searchStatusAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('status', $value);
        }
    }
}
