<?php
declare (strict_types = 1);

namespace app\agent\controller;

use app\BaseController;
use think\facade\View;
use think\facade\Session;
use think\facade\Request;
use think\facade\Validate;
use think\facade\Db;

/**
 * 代理端认证控制器
 */
class Auth extends BaseController
{
    /**
     * 代理登录
     */
    public function login()
    {
        if (Request::isPost()) {
            return $this->doLogin();
        }
        
        // 如果已登录，跳转到代理首页
        if (Session::has('agent_id')) {
            $this->redirect('/agent/');
        }
        
        View::assign([
            'title' => '代理登录 - GVD代理后台'
        ]);
        
        return View::fetch('agent/auth/login');
    }
    
    /**
     * 执行登录
     */
    private function doLogin()
    {
        $data = Request::post();
        
        // 验证数据
        $validate = Validate::rule([
            'username' => 'require',
            'password' => 'require'
        ])->message([
            'username.require' => '请输入用户名',
            'password.require' => '请输入密码'
        ]);
        
        if (!$validate->check($data)) {
            return json(['code' => 0, 'msg' => $validate->getError()]);
        }
        
        // 查找代理
        $agent = Db::name('agents')
                  ->where('username', $data['username'])
                  ->where('status', 1)
                  ->find();
        
        if (!$agent) {
            return json(['code' => 0, 'msg' => '用户名或密码错误']);
        }
        
        // 验证密码
        if (!password_verify($data['password'], $agent['password'])) {
            return json(['code' => 0, 'msg' => '用户名或密码错误']);
        }
        
        // 更新登录信息
        Db::name('agents')->where('id', $agent['id'])->update([
            'last_login_time' => date('Y-m-d H:i:s'),
            'last_login_ip' => Request::ip()
        ]);
        
        // 设置Session
        Session::set('agent_id', $agent['id']);
        Session::set('agent_username', $agent['username']);
        Session::set('agent_level', $agent['level']);
        
        return json([
            'code' => 1,
            'msg' => '登录成功',
            'data' => [
                'redirect' => '/agent/'
            ]
        ]);
    }
    
    /**
     * 退出登录
     */
    public function logout()
    {
        Session::clear();
        $this->redirect('/agent/login');
    }
}
