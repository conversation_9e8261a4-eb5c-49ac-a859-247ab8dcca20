<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\User;
use think\facade\Db;
use think\facade\Cache;
use think\facade\Mail;

/**
 * 用户服务类
 */
class UserService
{
    /**
     * 用户登录
     */
    public function login(string $username, string $password): array
    {
        try {
            // 查找用户
            $user = User::where('username', $username)
                       ->whereOr('email', $username)
                       ->whereOr('phone', $username)
                       ->find();
            
            if (!$user) {
                return ['code' => 0, 'msg' => '用户不存在'];
            }
            
            // 检查用户状态
            if ($user->status != 1) {
                return ['code' => 0, 'msg' => '账户已被禁用'];
            }
            
            // 验证密码
            if (!password_verify($password, $user->password)) {
                return ['code' => 0, 'msg' => '密码错误'];
            }
            
            // 更新登录信息
            $user->last_login_time = date('Y-m-d H:i:s');
            $user->last_login_ip = request()->ip();
            $user->save();
            
            return [
                'code' => 1,
                'msg' => '登录成功',
                'data' => [
                    'id' => $user->id,
                    'username' => $user->username,
                    'email' => $user->email,
                    'avatar' => $user->avatar,
                    'level' => $user->level
                ]
            ];
            
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '登录失败：' . $e->getMessage()];
        }
    }
    
    /**
     * 用户注册
     */
    public function register(array $data): array
    {
        try {
            Db::startTrans();
            
            // 创建用户
            $user = User::create([
                'username' => $data['username'],
                'email' => $data['email'],
                'password' => password_hash($data['password'], PASSWORD_DEFAULT),
                'invite_code' => User::generateInviteCode(),
                'register_ip' => request()->ip(),
                'status' => 1,
                'level' => 1,
                'kyc_status' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            // 初始化用户资产
            $this->initUserAssets($user->id);
            
            Db::commit();
            
            return ['code' => 1, 'msg' => '注册成功'];
            
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 0, 'msg' => '注册失败：' . $e->getMessage()];
        }
    }
    
    /**
     * 初始化用户资产
     */
    private function initUserAssets(int $userId): void
    {
        // 获取所有币种
        $coins = Db::name('coins')->where('status', 1)->select();
        
        foreach ($coins as $coin) {
            Db::name('user_assets')->insert([
                'user_id' => $userId,
                'coin_id' => $coin['id'],
                'coin_symbol' => $coin['symbol'],
                'available' => 0,
                'frozen' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }
    }
    
    /**
     * 发送重置密码邮件
     */
    public function sendResetPasswordEmail(string $email): array
    {
        try {
            $user = User::where('email', $email)->find();
            
            if (!$user) {
                return ['code' => 0, 'msg' => '邮箱不存在'];
            }
            
            // 生成重置token
            $token = md5($email . time() . rand(1000, 9999));
            
            // 缓存token，有效期30分钟
            Cache::set('reset_password_' . $token, $user->id, 1800);
            
            // 发送邮件（这里需要配置邮件服务）
            // Mail::send(...);
            
            return ['code' => 1, 'msg' => '重置密码邮件已发送'];
            
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '发送失败：' . $e->getMessage()];
        }
    }
    
    /**
     * 发送邮箱验证码
     */
    public function sendEmailCode(string $email): array
    {
        try {
            // 生成验证码
            $code = rand(100000, 999999);
            
            // 缓存验证码，有效期5分钟
            Cache::set('email_code_' . $email, $code, 300);
            
            // 发送邮件（这里需要配置邮件服务）
            // Mail::send(...);
            
            return ['code' => 1, 'msg' => '验证码已发送'];
            
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '发送失败：' . $e->getMessage()];
        }
    }

    /**
     * 创建测试用户（管理端/代理端使用）
     */
    public function createTestUser(array $data, int $adminId = 0, int $agentId = 0): array
    {
        try {
            Db::startTrans();

            // 创建测试用户
            $user = User::create([
                'username' => $data['username'],
                'email' => $data['email'],
                'password' => password_hash($data['password'], PASSWORD_DEFAULT),
                'invite_code' => User::generateInviteCode(),
                'user_type' => User::USER_TYPE_TEST, // 测试用户
                'agent_id' => $agentId,
                'register_ip' => request()->ip(),
                'status' => 1,
                'level' => 1,
                'kyc_status' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // 初始化用户资产
            $this->initUserAssets($user->id);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '测试用户创建成功',
                'data' => [
                    'user_id' => $user->id,
                    'username' => $user->username,
                    'user_type' => 'test'
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 0, 'msg' => '创建失败：' . $e->getMessage()];
        }
    }

    /**
     * 发送密码重置邮件
     */
    public function sendResetPasswordEmail(string $email): array
    {
        try {
            // 检查用户是否存在
            $user = User::where('email', $email)->find();
            if (!$user) {
                return ['code' => 0, 'msg' => '该邮箱未注册'];
            }

            // 生成重置令牌
            $token = md5($email . time() . uniqid());

            // 缓存重置令牌，有效期24小时
            Cache::set('reset_token_' . $token, $user->id, 86400);

            // 生成重置链接
            $resetLink = request()->domain() . '/auth/reset-password?token=' . $token;

            // 发送邮件
            $emailService = new \app\common\service\EmailService();
            $result = $emailService->sendPasswordResetEmail($email, $resetLink);

            return $result;

        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '发送失败：' . $e->getMessage()];
        }
    }
}
