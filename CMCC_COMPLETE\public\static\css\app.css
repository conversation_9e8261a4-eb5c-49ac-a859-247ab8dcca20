/* GVD数字货币交易平台 - 火币风格样式 */

/* CSS变量定义 */
:root {
    /* 火币风格色彩系统 */
    --primary-bg: #0b1426;
    --secondary-bg: #1e2329;
    --card-bg: #2b3139;
    --border-color: #3c4043;
    --text-primary: #ffffff;
    --text-secondary: #848e9c;
    --text-muted: #5e6673;

    /* 渐变色彩 */
    --gradient-primary: linear-gradient(135deg, #1890ff 0%, #00d4aa 100%);
    --gradient-secondary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-gold: linear-gradient(135deg, #f0b90b 0%, #ffd700 100%);

    /* 功能色彩 */
    --color-success: #02c076;
    --color-danger: #f84960;
    --color-warning: #f0b90b;
    --color-info: #1890ff;

    /* 阴影效果 */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);
    --shadow-glow: 0 0 20px rgba(24, 144, 255, 0.3);

    /* 动画时间 */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* 全局重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: var(--primary-bg);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 14px;
    overflow-x: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: var(--secondary-bg);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #5e6673;
}

/* 容器样式 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.container-fluid {
    width: 100%;
    padding: 0 20px;
}

/* 火币风格头部导航 */
.header {
    background: var(--secondary-bg);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;
    max-width: 1440px;
    margin: 0 auto;
    padding: 0 24px;
}

/* GVD Logo设计 */
.logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    font-size: 28px;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.5px;
    transition: var(--transition-normal);
}

.logo:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 0 8px rgba(24, 144, 255, 0.5));
}

.logo::before {
    content: "⚡";
    margin-right: 8px;
    font-size: 24px;
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* 火币风格导航菜单 */
.nav {
    display: flex;
    align-items: center;
    list-style: none;
    gap: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 20px 24px;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition-fast);
    border-bottom: 2px solid transparent;
}

.nav-link:hover {
    color: var(--text-primary);
    background: rgba(24, 144, 255, 0.1);
}

.nav-link.active {
    color: var(--color-info);
    border-bottom-color: var(--color-info);
    background: rgba(24, 144, 255, 0.1);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: var(--transition-normal);
    transform: translateX(-50%);
}

.nav-link:hover::after {
    width: 80%;
}

/* 用户菜单区域 */
.user-menu {
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    background: var(--card-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 12px;
}

.user-name {
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 500;
}

.user-balance {
    color: var(--text-secondary);
    font-size: 12px;
}

/* 火币风格按钮系统 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    line-height: 1;
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    user-select: none;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-secondary {
    background: var(--card-bg);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--border-color);
    border-color: var(--text-secondary);
}

.btn-outline {
    background: transparent;
    color: var(--color-info);
    border: 1px solid var(--color-info);
}

.btn-outline:hover:not(:disabled) {
    background: var(--color-info);
    color: white;
}

.btn-success {
    background: var(--color-success);
    color: white;
}

.btn-danger {
    background: var(--color-danger);
    color: white;
}

.btn-warning {
    background: var(--color-warning);
    color: var(--primary-bg);
}

/* 按钮尺寸 */
.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-lg {
    padding: 14px 28px;
    font-size: 16px;
    font-weight: 600;
}

.btn-xl {
    padding: 18px 36px;
    font-size: 18px;
    font-weight: 600;
}

/* 按钮图标 */
.btn-icon {
    padding: 10px;
    width: 40px;
    height: 40px;
}

.btn .icon {
    margin-right: 6px;
    font-size: 16px;
}

.btn-icon .icon {
    margin: 0;
}

/* 主要内容区域 */
.main-content {
    min-height: calc(100vh - 64px);
}

/* 火币风格英雄区域 */
.hero-section {
    position: relative;
    background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
    padding: 80px 0 120px;
    overflow: hidden;
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 50%, rgba(24, 144, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 212, 170, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(240, 185, 11, 0.05) 0%, transparent 50%);
    animation: heroGlow 8s ease-in-out infinite alternate;
}

@keyframes heroGlow {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-text {
    max-width: 600px;
}

.hero-title {
    font-size: 48px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 24px;
    color: var(--text-primary);
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 18px;
    line-height: 1.6;
    color: var(--text-secondary);
    margin-bottom: 40px;
}

.hero-stats {
    display: flex;
    gap: 40px;
    margin-bottom: 40px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.stat-label {
    font-size: 14px;
    color: var(--text-muted);
}

.hero-actions {
    display: flex;
    gap: 20px;
    align-items: center;
}

/* 英雄区域可视化卡片 */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.trading-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 24px;
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
    animation: cardFloat 6s ease-in-out infinite;
}

@keyframes cardFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color);
}

.card-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.status-indicator {
    width: 8px;
    height: 8px;
    background: var(--color-success);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(2, 192, 118, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(2, 192, 118, 0); }
    100% { box-shadow: 0 0 0 0 rgba(2, 192, 118, 0); }
}

.price-display {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.price-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: var(--secondary-bg);
    border-radius: 8px;
    transition: var(--transition-fast);
}

.price-item:hover {
    background: var(--border-color);
    transform: translateX(4px);
}

.symbol {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.price {
    font-size: 16px;
    font-weight: 700;
    color: var(--text-primary);
    font-family: 'Courier New', monospace;
}

.change {
    font-size: 12px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 4px;
}

.change.positive {
    color: var(--color-success);
    background: rgba(2, 192, 118, 0.1);
}

.change.negative {
    color: var(--color-danger);
    background: rgba(248, 73, 96, 0.1);
}

/* 市场行情区域 */
.market-section {
    padding: 80px 0;
    background: var(--secondary-bg);
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-size: 36px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.section-subtitle {
    font-size: 18px;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.market-tabs {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 32px;
}

.tab-btn {
    padding: 12px 24px;
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.tab-btn:hover {
    color: var(--text-primary);
    border-color: var(--color-info);
}

.tab-btn.active {
    background: var(--gradient-primary);
    border-color: transparent;
    color: white;
}

.market-table-container {
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.market-table {
    width: 100%;
}

.table-header {
    display: grid;
    grid-template-columns: 2fr 1.5fr 1fr 1.5fr 1fr;
    gap: 16px;
    padding: 20px 24px;
    background: var(--secondary-bg);
    border-bottom: 1px solid var(--border-color);
}

.th {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table-body {
    max-height: 400px;
    overflow-y: auto;
}

.table-row {
    display: grid;
    grid-template-columns: 2fr 1.5fr 1fr 1.5fr 1fr;
    gap: 16px;
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition-fast);
    cursor: pointer;
}

.table-row:hover {
    background: var(--secondary-bg);
}

.table-row:last-child {
    border-bottom: none;
}

.td {
    display: flex;
    align-items: center;
    font-size: 14px;
}

.symbol-cell {
    display: flex;
    align-items: center;
    gap: 12px;
}

.symbol-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
    font-weight: 600;
}

.symbol-info {
    display: flex;
    flex-direction: column;
}

.symbol-name {
    font-weight: 600;
    color: var(--text-primary);
}

.symbol-desc {
    font-size: 12px;
    color: var(--text-muted);
}

.price-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--text-primary);
}

.change-cell {
    font-weight: 600;
}

.volume-cell {
    color: var(--text-secondary);
}

.action-cell {
    justify-content: flex-end;
}

.trade-btn {
    padding: 6px 16px;
    background: var(--gradient-primary);
    border: none;
    border-radius: 6px;
    color: white;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.trade-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

/* 产品特色区域 */
.features-section {
    padding: 80px 0;
    background: var(--primary-bg);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 32px;
    margin-top: 60px;
}

.feature-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 32px;
    text-align: center;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(24, 144, 255, 0.1), transparent);
    transition: var(--transition-slow);
}

.feature-card:hover::before {
    left: 100%;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
    border-color: var(--color-info);
}

.feature-icon {
    font-size: 48px;
    margin-bottom: 24px;
    display: block;
}

.feature-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.feature-desc {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 20px;
}

.feature-highlight {
    display: inline-block;
    padding: 6px 12px;
    background: var(--gradient-primary);
    color: white;
    font-size: 12px;
    font-weight: 600;
    border-radius: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 交易界面样式 */
.trading-layout {
    display: grid;
    grid-template-columns: 1fr 320px;
    gap: 24px;
    height: calc(100vh - 64px);
    padding: 24px;
}

.chart-container {
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.chart-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.chart-controls {
    display: flex;
    gap: 8px;
}

.chart-btn {
    padding: 6px 12px;
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-secondary);
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition-fast);
}

.chart-btn:hover,
.chart-btn.active {
    background: var(--color-info);
    border-color: var(--color-info);
    color: white;
}

.chart-content {
    flex: 1;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
}

.trading-panel {
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* 数据统计区域 */
.stats-section {
    padding: 60px 0;
    background: var(--secondary-bg);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
}

.stat-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: var(--transition-normal);
}

.stat-card:hover::before {
    transform: scaleX(1);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary);
    border-radius: 12px;
    color: white;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
    font-family: 'Courier New', monospace;
}

.stat-label {
    font-size: 14px;
    color: var(--text-secondary);
}

/* 新闻公告区域 */
.news-section {
    padding: 80px 0;
    background: var(--primary-bg);
}

.news-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
}

.more-link {
    color: var(--color-info);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition-fast);
}

.more-link:hover {
    color: var(--text-primary);
}

.news-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 24px;
}

.news-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 24px;
    transition: var(--transition-normal);
    position: relative;
    cursor: pointer;
}

.news-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--color-info);
}

.news-card.featured {
    grid-row: span 2;
}

.news-badge {
    position: absolute;
    top: 16px;
    right: 16px;
    background: var(--color-danger);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.news-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
    line-height: 1.4;
}

.news-card.featured .news-title {
    font-size: 20px;
    margin-bottom: 16px;
}

.news-desc {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 16px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.news-card.featured .news-desc {
    -webkit-line-clamp: 4;
}

.news-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: var(--text-muted);
}

.news-date {
    font-family: 'Courier New', monospace;
}

.news-category {
    background: var(--secondary-bg);
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
}

.trading-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid #2b3139;
}

.tab-button {
    background: none;
    border: none;
    color: #848e9c;
    padding: 10px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-button.active {
    color: #f0b90b;
    border-bottom-color: #f0b90b;
}

.form-group {
    margin-bottom: 15px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    color: #848e9c;
    font-size: 12px;
    text-transform: uppercase;
}

.form-control {
    width: 100%;
    padding: 10px;
    background: #2b3139;
    border: 1px solid #3c4043;
    border-radius: 4px;
    color: #ffffff;
    font-size: 14px;
}

.form-control:focus {
    outline: none;
    border-color: #f0b90b;
}

.form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23848e9c' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 8px center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: 40px;
}

/* 数据表格样式 */
.data-table {
    width: 100%;
    border-collapse: collapse;
    background: #1e2329;
    border-radius: 8px;
    overflow: hidden;
}

.data-table th,
.data-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #2b3139;
}

.data-table th {
    background: #2b3139;
    color: #848e9c;
    font-weight: 500;
    font-size: 12px;
    text-transform: uppercase;
}

.data-table td {
    color: #ffffff;
}

.text-success {
    color: #02c076;
}

.text-danger {
    color: #f84960;
}

.text-warning {
    color: #f0b90b;
}

/* 卡片样式 */
.card {
    background: #1e2329;
    border-radius: 8px;
    border: 1px solid #2b3139;
    overflow: hidden;
}

.card-header {
    padding: 15px 20px;
    border-bottom: 1px solid #2b3139;
    background: #2b3139;
}

.card-title {
    margin: 0;
    color: #ffffff;
    font-size: 16px;
    font-weight: 500;
}

.card-body {
    padding: 20px;
}

/* 响应式设计 - 火币风格 */

/* 平板设备 */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .hero-title {
        font-size: 40px;
    }

    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    .news-grid {
        grid-template-columns: 1fr;
    }

    .news-card.featured {
        grid-row: span 1;
    }
}

/* 移动设备 */
@media (max-width: 768px) {
    .header-content {
        padding: 0 16px;
        height: 56px;
    }

    .nav {
        display: none;
    }

    .logo {
        font-size: 24px;
    }

    .user-menu {
        gap: 8px;
    }

    .btn-sm {
        padding: 6px 12px;
        font-size: 12px;
    }

    .hero-section {
        padding: 40px 0 60px;
    }

    .hero-title {
        font-size: 28px;
        margin-bottom: 16px;
    }

    .hero-subtitle {
        font-size: 16px;
        margin-bottom: 24px;
    }

    .hero-stats {
        flex-direction: column;
        gap: 16px;
        margin-bottom: 32px;
    }

    .hero-actions {
        flex-direction: column;
        gap: 12px;
        width: 100%;
    }

    .btn-xl {
        width: 100%;
        padding: 16px;
    }

    .trading-card {
        padding: 16px;
    }

    .section-title {
        font-size: 28px;
    }

    .section-subtitle {
        font-size: 16px;
    }

    .market-tabs {
        flex-wrap: wrap;
        gap: 4px;
    }

    .tab-btn {
        padding: 8px 16px;
        font-size: 12px;
    }

    .table-header,
    .table-row {
        grid-template-columns: 2fr 1fr 1fr;
        gap: 8px;
        padding: 12px 16px;
    }

    .table-header .th:nth-child(4),
    .table-header .th:nth-child(5),
    .table-row .td:nth-child(4),
    .table-row .td:nth-child(5) {
        display: none;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .feature-card {
        padding: 24px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }

    .stat-card {
        padding: 16px;
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .stat-icon {
        width: 48px;
        height: 48px;
        font-size: 24px;
    }

    .stat-number {
        font-size: 20px;
    }

    .news-section .section-header {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }

    .trading-layout {
        grid-template-columns: 1fr;
        height: auto;
        padding: 16px;
        gap: 16px;
    }

    .container {
        padding: 0 16px;
    }
}

/* 小屏幕设备 */
@media (max-width: 480px) {
    .hero-title {
        font-size: 24px;
    }

    .hero-subtitle {
        font-size: 14px;
    }

    .section-title {
        font-size: 24px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .table-header,
    .table-row {
        grid-template-columns: 1.5fr 1fr;
        gap: 8px;
    }

    .table-header .th:nth-child(3),
    .table-row .td:nth-child(3) {
        display: none;
    }

    .symbol-desc {
        display: none;
    }

    .feature-card {
        padding: 20px;
    }

    .feature-title {
        font-size: 18px;
    }

    .news-card {
        padding: 16px;
    }

    .news-title {
        font-size: 14px;
    }

    .news-desc {
        font-size: 12px;
        -webkit-line-clamp: 2;
    }
}

/* 移动端导航菜单 */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
        background: none;
        border: none;
        color: var(--text-primary);
        font-size: 20px;
        cursor: pointer;
        padding: 8px;
    }

    .mobile-menu {
        position: fixed;
        top: 56px;
        left: 0;
        right: 0;
        background: var(--secondary-bg);
        border-bottom: 1px solid var(--border-color);
        transform: translateY(-100%);
        transition: var(--transition-normal);
        z-index: 999;
    }

    .mobile-menu.active {
        transform: translateY(0);
    }

    .mobile-nav {
        display: flex;
        flex-direction: column;
        padding: 20px;
    }

    .mobile-nav .nav-link {
        padding: 16px 0;
        border-bottom: 1px solid var(--border-color);
        border-radius: 0;
    }

    .mobile-nav .nav-link:last-child {
        border-bottom: none;
    }
}

/* 高分辨率屏幕优化 */
@media (min-width: 1440px) {
    .container {
        max-width: 1320px;
    }

    .hero-title {
        font-size: 56px;
    }

    .hero-subtitle {
        font-size: 20px;
    }

    .section-title {
        font-size: 42px;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #2b3139;
    border-radius: 50%;
    border-top-color: #f0b90b;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: #ffffff;
    font-weight: 500;
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
}

.notification.success {
    background-color: #02c076;
}

.notification.error {
    background-color: #f84960;
}

.notification.warning {
    background-color: #f0b90b;
    color: #000000;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
