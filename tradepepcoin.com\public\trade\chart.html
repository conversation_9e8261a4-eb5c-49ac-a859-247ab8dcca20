<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业K线图表 - GVD交易平台</title>
    <link rel="stylesheet" href="/static/css/trade.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
        }
        
        .chart-container {
            width: 100%;
            height: 100vh;
            position: relative;
            background: #1a1a1a;
        }
        
        .chart-header {
            height: 60px;
            background: #2a2a2a;
            border-bottom: 1px solid #333;
            display: flex;
            align-items: center;
            padding: 0 20px;
            justify-content: space-between;
        }
        
        .symbol-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .symbol-name {
            font-size: 18px;
            font-weight: bold;
            color: #ffffff;
        }
        
        .price-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .current-price {
            font-size: 20px;
            font-weight: bold;
            color: #00d4aa;
        }
        
        .price-change {
            font-size: 14px;
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        .price-change.positive {
            background: rgba(0, 212, 170, 0.2);
            color: #00d4aa;
        }
        
        .price-change.negative {
            background: rgba(255, 77, 79, 0.2);
            color: #ff4d4f;
        }
        
        .chart-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .interval-selector {
            display: flex;
            gap: 5px;
        }
        
        .interval-btn {
            padding: 6px 12px;
            background: #333;
            border: 1px solid #444;
            color: #ccc;
            cursor: pointer;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.3s;
        }
        
        .interval-btn:hover {
            background: #444;
            color: #fff;
        }
        
        .interval-btn.active {
            background: #1890ff;
            border-color: #1890ff;
            color: #fff;
        }
        
        .symbol-selector {
            padding: 8px 12px;
            background: #333;
            border: 1px solid #444;
            color: #fff;
            border-radius: 4px;
            cursor: pointer;
        }
        
        #tradingview_chart {
            width: 100%;
            height: calc(100vh - 60px);
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ccc;
            font-size: 16px;
        }
        
        .error-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ff4d4f;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="chart-container">
        <div class="chart-header">
            <div class="symbol-info">
                <div class="symbol-name" id="symbolName">BTC/USDT</div>
                <div class="price-info">
                    <div class="current-price" id="currentPrice">$45,000.00</div>
                    <div class="price-change positive" id="priceChange">+2.5%</div>
                </div>
            </div>
            
            <div class="chart-controls">
                <select class="symbol-selector" id="symbolSelector">
                    <option value="BTCUSDT">BTC/USDT</option>
                    <option value="ETHUSDT">ETH/USDT</option>
                    <option value="LTCUSDT">LTC/USDT</option>
                    <option value="EOSUSDT">EOS/USDT</option>
                    <option value="XRPUSDT">XRP/USDT</option>
                </select>
                
                <div class="interval-selector">
                    <div class="interval-btn" data-interval="1m">1分</div>
                    <div class="interval-btn" data-interval="5m">5分</div>
                    <div class="interval-btn" data-interval="15m">15分</div>
                    <div class="interval-btn active" data-interval="1h">1时</div>
                    <div class="interval-btn" data-interval="4h">4时</div>
                    <div class="interval-btn" data-interval="1d">1日</div>
                </div>
            </div>
        </div>
        
        <div id="tradingview_chart">
            <div class="loading" id="loadingText">正在加载图表...</div>
        </div>
    </div>

    <!-- TradingView Charting Library -->
    <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
    <!-- WebSocket客户端 -->
    <script src="/static/js/websocket-client.js"></script>
    
    <script>
        class TradingChart {
            constructor() {
                this.chart = null;
                this.candlestickSeries = null;
                this.volumeSeries = null;
                this.currentSymbol = 'BTCUSDT';
                this.currentInterval = '1h';
                this.ws = null;
                this.wsClient = null;

                this.init();
            }
            
            init() {
                this.createChart();
                this.bindEvents();
                this.loadInitialData();
                this.setupWebSocket();
            }
            
            createChart() {
                const chartContainer = document.getElementById('tradingview_chart');
                
                this.chart = LightweightCharts.createChart(chartContainer, {
                    width: chartContainer.clientWidth,
                    height: chartContainer.clientHeight,
                    layout: {
                        backgroundColor: '#1a1a1a',
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: {
                            color: '#2a2a2a',
                        },
                        horzLines: {
                            color: '#2a2a2a',
                        },
                    },
                    crosshair: {
                        mode: LightweightCharts.CrosshairMode.Normal,
                    },
                    rightPriceScale: {
                        borderColor: '#485c7b',
                    },
                    timeScale: {
                        borderColor: '#485c7b',
                        timeVisible: true,
                        secondsVisible: false,
                    },
                });
                
                // 创建K线图
                this.candlestickSeries = this.chart.addCandlestickSeries({
                    upColor: '#00d4aa',
                    downColor: '#ff4d4f',
                    borderDownColor: '#ff4d4f',
                    borderUpColor: '#00d4aa',
                    wickDownColor: '#ff4d4f',
                    wickUpColor: '#00d4aa',
                });
                
                // 创建成交量图
                this.volumeSeries = this.chart.addHistogramSeries({
                    color: '#26a69a',
                    priceFormat: {
                        type: 'volume',
                    },
                    priceScaleId: '',
                    scaleMargins: {
                        top: 0.8,
                        bottom: 0,
                    },
                });
                
                // 响应式调整
                window.addEventListener('resize', () => {
                    this.chart.applyOptions({ 
                        width: chartContainer.clientWidth,
                        height: chartContainer.clientHeight 
                    });
                });
                
                // 隐藏加载提示
                document.getElementById('loadingText').style.display = 'none';
            }
            
            bindEvents() {
                // 时间周期切换
                document.querySelectorAll('.interval-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        document.querySelectorAll('.interval-btn').forEach(b => b.classList.remove('active'));
                        e.target.classList.add('active');
                        this.currentInterval = e.target.dataset.interval;
                        this.loadKlineData();
                    });
                });
                
                // 交易对切换
                document.getElementById('symbolSelector').addEventListener('change', (e) => {
                    this.currentSymbol = e.target.value;
                    this.updateSymbolInfo();
                    this.loadKlineData();
                    this.reconnectWebSocket();
                });
            }
            
            async loadInitialData() {
                await this.loadKlineData();
                await this.updateTicker();
            }
            
            async loadKlineData() {
                try {
                    const response = await fetch(`/api/kline/data?symbol=${this.currentSymbol}&interval=${this.currentInterval}&limit=500`);
                    const result = await response.json();
                    
                    if (result.code === 1 && result.data) {
                        const klineData = result.data.map(item => ({
                            time: item.time / 1000, // 转换为秒时间戳
                            open: item.open,
                            high: item.high,
                            low: item.low,
                            close: item.close
                        }));
                        
                        const volumeData = result.data.map(item => ({
                            time: item.time / 1000,
                            value: item.volume,
                            color: item.close >= item.open ? '#00d4aa' : '#ff4d4f'
                        }));
                        
                        this.candlestickSeries.setData(klineData);
                        this.volumeSeries.setData(volumeData);
                    }
                } catch (error) {
                    console.error('加载K线数据失败:', error);
                    this.showError('加载K线数据失败');
                }
            }
            
            async updateTicker() {
                try {
                    const response = await fetch(`/api/kline/ticker24hr?symbol=${this.currentSymbol}`);
                    const result = await response.json();
                    
                    if (result.code === 1 && result.data) {
                        const ticker = result.data;
                        document.getElementById('currentPrice').textContent = `$${ticker.price.toLocaleString()}`;
                        
                        const changeElement = document.getElementById('priceChange');
                        const changePercent = ticker.change_percent;
                        changeElement.textContent = `${changePercent >= 0 ? '+' : ''}${changePercent.toFixed(2)}%`;
                        changeElement.className = `price-change ${changePercent >= 0 ? 'positive' : 'negative'}`;
                    }
                } catch (error) {
                    console.error('更新行情失败:', error);
                }
            }
            
            updateSymbolInfo() {
                const symbolName = this.currentSymbol.replace('USDT', '/USDT');
                document.getElementById('symbolName').textContent = symbolName;
            }
            
            setupWebSocket() {
                try {
                    // 创建WebSocket客户端
                    this.wsClient = new GVDWebSocket('ws://localhost:9501');

                    // 设置事件回调
                    this.wsClient.onOpen = () => {
                        console.log('WebSocket连接成功');
                        this.subscribeToData();
                    };

                    this.wsClient.onClose = () => {
                        console.log('WebSocket连接断开');
                    };

                    this.wsClient.onError = (error) => {
                        console.error('WebSocket错误:', error);
                        // 降级到HTTP轮询
                        this.fallbackToPolling();
                    };

                } catch (error) {
                    console.error('WebSocket初始化失败:', error);
                    this.fallbackToPolling();
                }
            }

            subscribeToData() {
                if (!this.wsClient || !this.wsClient.isConnected) {
                    return;
                }

                // 订阅K线数据
                this.wsClient.subscribeKline(this.currentSymbol, this.currentInterval, (data) => {
                    this.updateKlineFromWebSocket(data);
                });

                // 订阅24小时行情
                this.wsClient.subscribeTicker(this.currentSymbol, (data) => {
                    this.updateTickerFromWebSocket(data);
                });
            }

            updateKlineFromWebSocket(klineData) {
                try {
                    const formattedData = WebSocketUtils.formatKlineForTradingView(klineData);

                    // 更新图表
                    this.candlestickSeries.update(formattedData);

                    // 更新成交量
                    this.volumeSeries.update({
                        time: formattedData.time / 1000,
                        value: formattedData.volume,
                        color: formattedData.close >= formattedData.open ? '#00d4aa' : '#ff4d4f'
                    });

                } catch (error) {
                    console.error('更新K线数据失败:', error);
                }
            }

            updateTickerFromWebSocket(tickerData) {
                try {
                    const ticker = WebSocketUtils.formatTickerData(tickerData);

                    // 更新价格显示
                    document.getElementById('currentPrice').textContent = `$${ticker.price.toLocaleString()}`;

                    const changeElement = document.getElementById('priceChange');
                    changeElement.textContent = `${ticker.changePercent >= 0 ? '+' : ''}${ticker.changePercent.toFixed(2)}%`;
                    changeElement.className = `price-change ${ticker.changePercent >= 0 ? 'positive' : 'negative'}`;

                } catch (error) {
                    console.error('更新行情数据失败:', error);
                }
            }

            fallbackToPolling() {
                console.log('降级到HTTP轮询模式');

                // 定时更新数据
                setInterval(() => {
                    this.updateTicker();
                }, 5000);
            }
            
            reconnectWebSocket() {
                // 重新订阅当前交易对的数据
                if (this.wsClient && this.wsClient.isConnected) {
                    this.subscribeToData();
                } else {
                    this.setupWebSocket();
                }
            }
            
            showError(message) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.innerHTML = `<h3>错误</h3><p>${message}</p>`;
                document.getElementById('tradingview_chart').appendChild(errorDiv);
            }
        }
        
        // 初始化图表
        document.addEventListener('DOMContentLoaded', () => {
            new TradingChart();
        });
    </script>
</body>
</html>
