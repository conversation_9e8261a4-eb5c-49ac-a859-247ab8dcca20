/**
 * 主题管理器
 * 支持多种主题切换和自定义主题
 */

class ThemeManager {
    constructor() {
        this.currentTheme = 'light';
        this.themes = {
            light: {
                name: '浅色主题',
                colors: {
                    primary: '#409eff',
                    success: '#67c23a',
                    warning: '#e6a23c',
                    danger: '#f56c6c',
                    info: '#909399',
                    background: '#ffffff',
                    surface: '#f5f7fa',
                    text: '#303133',
                    textSecondary: '#606266',
                    textPlaceholder: '#c0c4cc',
                    border: '#dcdfe6',
                    borderLight: '#e4e7ed',
                    borderLighter: '#ebeef5',
                    shadow: 'rgba(0, 0, 0, 0.12)'
                }
            },
            dark: {
                name: '深色主题',
                colors: {
                    primary: '#409eff',
                    success: '#67c23a',
                    warning: '#e6a23c',
                    danger: '#f56c6c',
                    info: '#909399',
                    background: '#1a1a1a',
                    surface: '#2d2d2d',
                    text: '#ffffff',
                    textSecondary: '#cccccc',
                    textPlaceholder: '#999999',
                    border: '#404040',
                    borderLight: '#505050',
                    borderLighter: '#606060',
                    shadow: 'rgba(0, 0, 0, 0.3)'
                }
            },
            blue: {
                name: '蓝色主题',
                colors: {
                    primary: '#1890ff',
                    success: '#52c41a',
                    warning: '#faad14',
                    danger: '#ff4d4f',
                    info: '#1890ff',
                    background: '#f0f5ff',
                    surface: '#ffffff',
                    text: '#262626',
                    textSecondary: '#595959',
                    textPlaceholder: '#bfbfbf',
                    border: '#d9d9d9',
                    borderLight: '#e8e8e8',
                    borderLighter: '#f0f0f0',
                    shadow: 'rgba(24, 144, 255, 0.15)'
                }
            },
            green: {
                name: '绿色主题',
                colors: {
                    primary: '#52c41a',
                    success: '#52c41a',
                    warning: '#faad14',
                    danger: '#ff4d4f',
                    info: '#1890ff',
                    background: '#f6ffed',
                    surface: '#ffffff',
                    text: '#262626',
                    textSecondary: '#595959',
                    textPlaceholder: '#bfbfbf',
                    border: '#d9d9d9',
                    borderLight: '#e8e8e8',
                    borderLighter: '#f0f0f0',
                    shadow: 'rgba(82, 196, 26, 0.15)'
                }
            },
            purple: {
                name: '紫色主题',
                colors: {
                    primary: '#722ed1',
                    success: '#52c41a',
                    warning: '#faad14',
                    danger: '#ff4d4f',
                    info: '#1890ff',
                    background: '#f9f0ff',
                    surface: '#ffffff',
                    text: '#262626',
                    textSecondary: '#595959',
                    textPlaceholder: '#bfbfbf',
                    border: '#d9d9d9',
                    borderLight: '#e8e8e8',
                    borderLighter: '#f0f0f0',
                    shadow: 'rgba(114, 46, 209, 0.15)'
                }
            },
            trading: {
                name: '交易主题',
                colors: {
                    primary: '#f7931a',
                    success: '#00d4aa',
                    warning: '#ffa726',
                    danger: '#ff5252',
                    info: '#42a5f5',
                    background: '#0d1421',
                    surface: '#1e2329',
                    text: '#ffffff',
                    textSecondary: '#b7bdc6',
                    textPlaceholder: '#848e9c',
                    border: '#2b3139',
                    borderLight: '#363c47',
                    borderLighter: '#474d57',
                    shadow: 'rgba(0, 0, 0, 0.5)',
                    // 交易专用颜色
                    buyColor: '#00d4aa',
                    sellColor: '#ff5252',
                    chartGrid: '#2b3139',
                    chartText: '#b7bdc6'
                }
            }
        };

        this.customThemes = this.loadCustomThemes();
        this.init();
    }

    /**
     * 初始化主题管理器
     */
    init() {
        // 从本地存储加载主题
        const savedTheme = localStorage.getItem('user_theme');
        if (savedTheme && this.themes[savedTheme]) {
            this.currentTheme = savedTheme;
        } else {
            // 检测系统主题偏好
            this.currentTheme = this.detectSystemTheme();
        }

        // 应用主题
        this.applyTheme(this.currentTheme);

        // 监听系统主题变化
        this.watchSystemTheme();

        // 绑定事件
        this.bindEvents();
    }

    /**
     * 切换主题
     */
    switchTheme(themeName) {
        if (!this.themes[themeName] && !this.customThemes[themeName]) {
            console.error(`主题 ${themeName} 不存在`);
            return false;
        }

        this.currentTheme = themeName;
        this.applyTheme(themeName);
        this.saveTheme(themeName);

        // 触发主题变更事件
        this.dispatchThemeChangeEvent(themeName);

        return true;
    }

    /**
     * 应用主题
     */
    applyTheme(themeName) {
        const theme = this.themes[themeName] || this.customThemes[themeName];
        if (!theme) return;

        // 设置CSS变量
        this.setCSSVariables(theme.colors);

        // 设置body类名
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        document.body.classList.add(`theme-${themeName}`);

        // 更新meta标签
        this.updateMetaThemeColor(theme.colors.primary);

        // 应用特殊样式
        this.applySpecialStyles(themeName, theme);
    }

    /**
     * 设置CSS变量
     */
    setCSSVariables(colors) {
        const root = document.documentElement;
        
        Object.entries(colors).forEach(([key, value]) => {
            root.style.setProperty(`--color-${this.kebabCase(key)}`, value);
        });

        // 设置通用变量
        root.style.setProperty('--theme-transition', 'all 0.3s ease');
    }

    /**
     * 应用特殊样式
     */
    applySpecialStyles(themeName, theme) {
        // 移除之前的特殊样式
        const existingStyle = document.getElementById('theme-special-styles');
        if (existingStyle) {
            existingStyle.remove();
        }

        // 创建新的样式
        const style = document.createElement('style');
        style.id = 'theme-special-styles';
        
        let css = '';

        // 交易主题特殊样式
        if (themeName === 'trading') {
            css += `
                .trading-chart {
                    background: ${theme.colors.surface};
                    border: 1px solid ${theme.colors.border};
                }
                
                .buy-button {
                    background: ${theme.colors.buyColor} !important;
                    border-color: ${theme.colors.buyColor} !important;
                }
                
                .sell-button {
                    background: ${theme.colors.sellColor} !important;
                    border-color: ${theme.colors.sellColor} !important;
                }
                
                .price-up {
                    color: ${theme.colors.buyColor} !important;
                }
                
                .price-down {
                    color: ${theme.colors.sellColor} !important;
                }
            `;
        }

        // 深色主题特殊样式
        if (themeName === 'dark') {
            css += `
                .el-table {
                    background-color: ${theme.colors.surface};
                    color: ${theme.colors.text};
                }
                
                .el-table th {
                    background-color: ${theme.colors.background};
                    color: ${theme.colors.text};
                }
                
                .el-input__inner {
                    background-color: ${theme.colors.surface};
                    border-color: ${theme.colors.border};
                    color: ${theme.colors.text};
                }
                
                .el-card {
                    background-color: ${theme.colors.surface};
                    border-color: ${theme.colors.border};
                }
            `;
        }

        style.textContent = css;
        document.head.appendChild(style);
    }

    /**
     * 创建自定义主题
     */
    createCustomTheme(name, colors) {
        const customTheme = {
            name: name,
            colors: { ...this.themes.light.colors, ...colors },
            custom: true,
            created: Date.now()
        };

        this.customThemes[name] = customTheme;
        this.saveCustomThemes();

        return customTheme;
    }

    /**
     * 删除自定义主题
     */
    deleteCustomTheme(name) {
        if (this.customThemes[name]) {
            delete this.customThemes[name];
            this.saveCustomThemes();
            
            // 如果当前使用的是被删除的主题，切换到默认主题
            if (this.currentTheme === name) {
                this.switchTheme('light');
            }
            
            return true;
        }
        return false;
    }

    /**
     * 获取所有主题
     */
    getAllThemes() {
        return {
            ...this.themes,
            ...this.customThemes
        };
    }

    /**
     * 获取当前主题
     */
    getCurrentTheme() {
        return this.currentTheme;
    }

    /**
     * 获取主题颜色
     */
    getThemeColors(themeName = null) {
        const theme = themeName ? 
            (this.themes[themeName] || this.customThemes[themeName]) :
            (this.themes[this.currentTheme] || this.customThemes[this.currentTheme]);
        
        return theme ? theme.colors : null;
    }

    /**
     * 检测系统主题
     */
    detectSystemTheme() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return 'dark';
        }
        return 'light';
    }

    /**
     * 监听系统主题变化
     */
    watchSystemTheme() {
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.addEventListener('change', (e) => {
                // 只有在用户没有手动设置主题时才自动切换
                if (!localStorage.getItem('user_theme')) {
                    this.switchTheme(e.matches ? 'dark' : 'light');
                }
            });
        }
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 主题选择器事件
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-theme]')) {
                const themeName = e.target.getAttribute('data-theme');
                this.switchTheme(themeName);
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Shift + T 切换主题
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                this.toggleTheme();
            }
        });
    }

    /**
     * 快速切换主题（浅色/深色）
     */
    toggleTheme() {
        const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
        this.switchTheme(newTheme);
    }

    /**
     * 保存主题设置
     */
    saveTheme(themeName) {
        localStorage.setItem('user_theme', themeName);
    }

    /**
     * 保存自定义主题
     */
    saveCustomThemes() {
        localStorage.setItem('custom_themes', JSON.stringify(this.customThemes));
    }

    /**
     * 加载自定义主题
     */
    loadCustomThemes() {
        try {
            const saved = localStorage.getItem('custom_themes');
            return saved ? JSON.parse(saved) : {};
        } catch (e) {
            return {};
        }
    }

    /**
     * 更新meta主题颜色
     */
    updateMetaThemeColor(color) {
        let metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (!metaThemeColor) {
            metaThemeColor = document.createElement('meta');
            metaThemeColor.name = 'theme-color';
            document.head.appendChild(metaThemeColor);
        }
        metaThemeColor.content = color;
    }

    /**
     * 触发主题变更事件
     */
    dispatchThemeChangeEvent(themeName) {
        const event = new CustomEvent('themeChanged', {
            detail: {
                theme: themeName,
                colors: this.getThemeColors(themeName)
            }
        });
        window.dispatchEvent(event);
    }

    /**
     * 转换为kebab-case
     */
    kebabCase(str) {
        return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase();
    }

    /**
     * 生成主题选择器HTML
     */
    generateThemeSelector(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const allThemes = this.getAllThemes();
        
        const selectorHTML = `
            <div class="theme-selector">
                <div class="theme-selector-header">
                    <h4>选择主题</h4>
                    <button class="theme-close-btn">&times;</button>
                </div>
                <div class="theme-grid">
                    ${Object.entries(allThemes).map(([key, theme]) => `
                        <div class="theme-option ${key === this.currentTheme ? 'active' : ''}" 
                             data-theme="${key}">
                            <div class="theme-preview" style="background: ${theme.colors.background}; border-color: ${theme.colors.border};">
                                <div class="theme-preview-header" style="background: ${theme.colors.primary};"></div>
                                <div class="theme-preview-content" style="background: ${theme.colors.surface};"></div>
                            </div>
                            <div class="theme-name">${theme.name}</div>
                            ${theme.custom ? '<div class="theme-custom-badge">自定义</div>' : ''}
                        </div>
                    `).join('')}
                </div>
                <div class="theme-actions">
                    <button class="theme-create-btn">创建自定义主题</button>
                </div>
            </div>
        `;

        container.innerHTML = selectorHTML;

        // 添加样式
        this.addThemeSelectorStyles();
    }

    /**
     * 添加主题选择器样式
     */
    addThemeSelectorStyles() {
        if (document.getElementById('theme-selector-styles')) return;

        const style = document.createElement('style');
        style.id = 'theme-selector-styles';
        style.textContent = `
            .theme-selector {
                background: var(--color-background);
                border: 1px solid var(--color-border);
                border-radius: 8px;
                padding: 20px;
                box-shadow: 0 4px 12px var(--color-shadow);
            }
            
            .theme-selector-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
            }
            
            .theme-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
                gap: 15px;
                margin-bottom: 20px;
            }
            
            .theme-option {
                cursor: pointer;
                text-align: center;
                transition: transform 0.2s;
            }
            
            .theme-option:hover {
                transform: translateY(-2px);
            }
            
            .theme-option.active .theme-preview {
                border-color: var(--color-primary);
                box-shadow: 0 0 0 2px var(--color-primary);
            }
            
            .theme-preview {
                width: 100%;
                height: 80px;
                border: 2px solid transparent;
                border-radius: 6px;
                overflow: hidden;
                margin-bottom: 8px;
                transition: all 0.2s;
            }
            
            .theme-preview-header {
                height: 20px;
                width: 100%;
            }
            
            .theme-preview-content {
                height: 60px;
                width: 100%;
            }
            
            .theme-name {
                font-size: 12px;
                color: var(--color-text);
            }
            
            .theme-custom-badge {
                font-size: 10px;
                color: var(--color-primary);
                margin-top: 2px;
            }
            
            .theme-actions {
                text-align: center;
            }
            
            .theme-create-btn {
                background: var(--color-primary);
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
            }
        `;
        
        document.head.appendChild(style);
    }

    /**
     * 导出主题配置
     */
    exportTheme(themeName) {
        const theme = this.themes[themeName] || this.customThemes[themeName];
        if (!theme) return null;

        return {
            name: theme.name,
            colors: theme.colors,
            exported: Date.now(),
            version: '1.0'
        };
    }

    /**
     * 导入主题配置
     */
    importTheme(themeData) {
        if (!themeData.name || !themeData.colors) {
            throw new Error('无效的主题数据');
        }

        const themeName = themeData.name.toLowerCase().replace(/\s+/g, '-');
        return this.createCustomTheme(themeName, themeData.colors);
    }
}

// 创建全局主题管理器实例
window.themeManager = new ThemeManager();

// 导出类供模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeManager;
}
