/**
 * 全局JavaScript应用
 */

// 全局配置
const App = {
    config: {
        apiUrl: '/api',
        wsUrl: 'ws://localhost:9501',
        debug: true
    },
    
    // 初始化
    init: function() {
        this.bindEvents();
        this.initTooltips();
        this.initCustomerService();
        this.checkLoginStatus();
    },
    
    // 绑定事件
    bindEvents: function() {
        // 全局AJAX设置
        $.ajaxSetup({
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            timeout: 30000,
            error: function(xhr, status, error) {
                if (xhr.status === 401) {
                    App.showAlert('warning', '登录已过期，请重新登录');
                    setTimeout(() => {
                        window.location.href = '/auth/login';
                    }, 2000);
                } else if (xhr.status === 403) {
                    App.showAlert('danger', '权限不足');
                } else if (xhr.status === 500) {
                    App.showAlert('danger', '服务器内部错误');
                } else if (status === 'timeout') {
                    App.showAlert('warning', '请求超时，请稍后重试');
                } else {
                    App.showAlert('danger', '网络错误，请检查网络连接');
                }
            }
        });
        
        // 返回顶部按钮
        this.initBackToTop();
        
        // 数字格式化
        this.formatNumbers();
    },
    
    // 初始化工具提示
    initTooltips: function() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    },
    
    // 初始化客服系统
    initCustomerService: function() {
        // 客服消息处理
        window.sendMessage = function() {
            const input = $('#chatInput');
            const message = input.val().trim();
            
            if (!message) return;
            
            // 添加用户消息
            App.addChatMessage(message, 'user');
            input.val('');
            
            // 模拟客服回复
            setTimeout(() => {
                const replies = [
                    '感谢您的咨询，我们会尽快为您处理。',
                    '请问还有其他问题需要帮助吗？',
                    '您可以查看帮助中心获取更多信息。',
                    '如需人工客服，请稍等片刻。'
                ];
                const reply = replies[Math.floor(Math.random() * replies.length)];
                App.addChatMessage(reply, 'service');
            }, 1000);
        };
        
        // 回车发送消息
        $('#chatInput').on('keypress', function(e) {
            if (e.which === 13) {
                sendMessage();
            }
        });
    },
    
    // 添加聊天消息
    addChatMessage: function(message, type) {
        const chatMessages = $('#chatMessages');
        const messageClass = type === 'user' ? 'chat-message user' : 'chat-message service';
        const icon = type === 'user' ? '<i class="fas fa-user me-2"></i>' : '<i class="fas fa-headset me-2"></i>';
        
        const messageHtml = `
            <div class="${messageClass}">
                ${icon}${message}
                <small class="d-block mt-1 opacity-75">${new Date().toLocaleTimeString()}</small>
            </div>
        `;
        
        chatMessages.append(messageHtml);
        chatMessages.scrollTop(chatMessages[0].scrollHeight);
    },
    
    // 检查登录状态
    checkLoginStatus: function() {
        // 这里可以添加检查登录状态的逻辑
        const userId = this.getCookie('user_id');
        if (userId) {
            // 用户已登录，可以初始化一些需要登录的功能
            this.initUserFeatures();
        }
    },
    
    // 初始化用户功能
    initUserFeatures: function() {
        // 初始化用户相关功能
        this.loadUserAssets();
        this.initNotifications();
    },
    
    // 加载用户资产
    loadUserAssets: function() {
        // 这里可以添加加载用户资产的逻辑
    },
    
    // 初始化通知
    initNotifications: function() {
        // 这里可以添加通知相关的逻辑
    },
    
    // 返回顶部
    initBackToTop: function() {
        // 创建返回顶部按钮
        const backToTopBtn = $(`
            <button class="btn btn-primary position-fixed" id="backToTop" 
                    style="bottom: 100px; right: 30px; z-index: 999; display: none; border-radius: 50%; width: 50px; height: 50px;">
                <i class="fas fa-arrow-up"></i>
            </button>
        `);
        
        $('body').append(backToTopBtn);
        
        // 滚动事件
        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                $('#backToTop').fadeIn();
            } else {
                $('#backToTop').fadeOut();
            }
        });
        
        // 点击返回顶部
        $('#backToTop').click(function() {
            $('html, body').animate({scrollTop: 0}, 800);
        });
    },
    
    // 数字格式化
    formatNumbers: function() {
        $('.format-number').each(function() {
            const number = parseFloat($(this).text());
            if (!isNaN(number)) {
                $(this).text(App.formatNumber(number));
            }
        });
    },
    
    // 格式化数字
    formatNumber: function(num, decimals = 2) {
        if (num >= 1e9) {
            return (num / 1e9).toFixed(decimals) + 'B';
        } else if (num >= 1e6) {
            return (num / 1e6).toFixed(decimals) + 'M';
        } else if (num >= 1e3) {
            return (num / 1e3).toFixed(decimals) + 'K';
        }
        return num.toFixed(decimals);
    },
    
    // 格式化价格
    formatPrice: function(price, decimals = 2) {
        return parseFloat(price).toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
    
    // 显示提示信息
    showAlert: function(type, message, duration = 5000) {
        const alertId = 'alert_' + Date.now();
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show position-fixed" 
                 id="${alertId}" style="top: 100px; right: 20px; z-index: 9999; min-width: 300px;">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        $('body').append(alertHtml);
        
        // 自动消失
        setTimeout(() => {
            $(`#${alertId}`).fadeOut(() => {
                $(`#${alertId}`).remove();
            });
        }, duration);
    },
    
    // 显示加载状态
    showLoading: function(element, text = '加载中...') {
        const $element = $(element);
        $element.data('original-html', $element.html());
        $element.html(`<i class="fas fa-spinner fa-spin me-2"></i>${text}`).prop('disabled', true);
    },
    
    // 隐藏加载状态
    hideLoading: function(element) {
        const $element = $(element);
        const originalHtml = $element.data('original-html');
        if (originalHtml) {
            $element.html(originalHtml).prop('disabled', false);
        }
    },
    
    // 确认对话框
    confirm: function(message, callback) {
        if (confirm(message)) {
            callback();
        }
    },
    
    // 获取Cookie
    getCookie: function(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
    },
    
    // 设置Cookie
    setCookie: function(name, value, days = 7) {
        const expires = new Date();
        expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
        document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
    },
    
    // 删除Cookie
    deleteCookie: function(name) {
        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
    },
    
    // 复制到剪贴板
    copyToClipboard: function(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                this.showAlert('success', '复制成功');
            });
        } else {
            // 兼容旧浏览器
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showAlert('success', '复制成功');
        }
    },
    
    // 防抖函数
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // 节流函数
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
};

// 页面加载完成后初始化
$(document).ready(function() {
    App.init();
});

// 导出到全局
window.App = App;
