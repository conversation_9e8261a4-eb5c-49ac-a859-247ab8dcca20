<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>充值 - GVD交易平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }

        .header {
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }

        .nav-menu {
            display: flex;
            gap: 30px;
        }

        .nav-menu a {
            text-decoration: none;
            color: #666;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-menu a:hover,
        .nav-menu a.active {
            color: #667eea;
        }

        .back-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }

        .container {
            max-width: 800px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 30px;
            color: #333;
        }

        .deposit-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .coin-selector {
            padding: 30px;
            border-bottom: 1px solid #eee;
        }

        .coin-selector h3 {
            margin-bottom: 20px;
            color: #333;
        }

        .coin-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .coin-item {
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
        }

        .coin-item:hover,
        .coin-item.active {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .coin-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #667eea;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin: 0 auto 10px;
        }

        .coin-name {
            font-weight: 600;
            color: #333;
        }

        .coin-symbol {
            font-size: 12px;
            color: #666;
        }

        .deposit-info {
            padding: 30px;
            display: none;
        }

        .deposit-info.active {
            display: block;
        }

        .address-section {
            margin-bottom: 30px;
        }

        .address-section h4 {
            margin-bottom: 15px;
            color: #333;
        }

        .address-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e1e5e9;
            margin-bottom: 15px;
        }

        .address-text {
            font-family: monospace;
            font-size: 14px;
            word-break: break-all;
            color: #333;
            margin-bottom: 10px;
        }

        .address-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-outline {
            background: transparent;
            color: #667eea;
            border: 1px solid #667eea;
        }

        .btn:hover {
            opacity: 0.8;
        }

        .qr-code {
            text-align: center;
            margin-bottom: 20px;
        }

        .qr-code img {
            width: 200px;
            height: 200px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
        }

        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .warning-box h5 {
            color: #856404;
            margin-bottom: 10px;
        }

        .warning-box ul {
            color: #856404;
            padding-left: 20px;
        }

        .warning-box li {
            margin-bottom: 5px;
        }

        .deposit-history {
            margin-top: 30px;
        }

        .deposit-history h4 {
            margin-bottom: 15px;
            color: #333;
        }

        .history-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .history-table th,
        .history-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .history-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .status-pending {
            color: #ffa502;
        }

        .status-success {
            color: #2ed573;
        }

        .status-failed {
            color: #ff4757;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        @media (max-width: 768px) {
            .coin-list {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .address-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">GVD交易平台</div>
            <nav class="nav-menu">
                <a href="/trade/index.html">交易</a>
                <a href="/user/dashboard.html">资产</a>
                <a href="/user/orders.html">订单</a>
                <a href="/user/profile.html">设置</a>
            </nav>
            <a href="/user/dashboard.html" class="back-btn">返回资产</a>
        </div>
    </div>

    <div class="container">
        <h1 class="page-title">充值</h1>
        
        <div class="deposit-container">
            <div class="coin-selector">
                <h3>选择充值币种</h3>
                <div class="coin-list" id="coinList">
                    <div class="loading">加载中...</div>
                </div>
            </div>
            
            <div class="deposit-info" id="depositInfo">
                <div class="warning-box">
                    <h5>⚠️ 重要提示</h5>
                    <ul>
                        <li>请确保选择正确的网络进行充值</li>
                        <li>最小充值金额：<span id="minDeposit">1 USDT</span></li>
                        <li>充值需要网络确认，到账时间可能需要几分钟到几小时</li>
                        <li>请勿向此地址充值其他币种，否则资产将无法找回</li>
                    </ul>
                </div>
                
                <div class="address-section">
                    <h4>充值地址</h4>
                    <div class="address-display">
                        <div class="address-text" id="depositAddress">加载中...</div>
                        <div class="address-actions">
                            <button class="btn btn-primary" onclick="copyAddress()">复制地址</button>
                            <button class="btn btn-outline" onclick="downloadQR()">下载二维码</button>
                        </div>
                    </div>
                </div>
                
                <div class="qr-code">
                    <img id="qrCodeImage" src="" alt="充值地址二维码" style="display: none;">
                </div>
            </div>
        </div>
        
        <div class="deposit-history">
            <h4>充值记录</h4>
            <table class="history-table">
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>币种</th>
                        <th>金额</th>
                        <th>状态</th>
                        <th>交易哈希</th>
                    </tr>
                </thead>
                <tbody id="depositHistory">
                    <tr>
                        <td colspan="5" class="loading">加载中...</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        class DepositPage {
            constructor() {
                this.token = localStorage.getItem('token');
                this.selectedCoin = null;
                
                if (!this.token) {
                    window.location.href = '/auth/login.html';
                    return;
                }
                
                this.init();
            }
            
            async init() {
                await this.loadSupportedCoins();
                await this.loadDepositHistory();
            }
            
            async loadSupportedCoins() {
                try {
                    const response = await this.apiRequest('/api/asset/coins');
                    if (response.code === 1) {
                        this.renderCoinList(response.data);
                    }
                } catch (error) {
                    console.error('加载支持币种失败:', error);
                    document.getElementById('coinList').innerHTML = '<div class="empty-state">加载失败</div>';
                }
            }
            
            renderCoinList(coins) {
                const coinList = document.getElementById('coinList');
                coinList.innerHTML = '';
                
                coins.forEach(coin => {
                    const coinItem = document.createElement('div');
                    coinItem.className = 'coin-item';
                    coinItem.onclick = () => this.selectCoin(coin);
                    coinItem.innerHTML = `
                        <div class="coin-icon">${coin.symbol.charAt(0)}</div>
                        <div class="coin-name">${coin.name}</div>
                        <div class="coin-symbol">${coin.symbol}</div>
                    `;
                    coinList.appendChild(coinItem);
                });
            }
            
            async selectCoin(coin) {
                // 更新选中状态
                document.querySelectorAll('.coin-item').forEach(item => {
                    item.classList.remove('active');
                });
                event.currentTarget.classList.add('active');
                
                this.selectedCoin = coin;
                
                // 更新最小充值金额
                document.getElementById('minDeposit').textContent = `${coin.min_deposit} ${coin.symbol}`;
                
                // 加载充值地址
                await this.loadDepositAddress(coin.symbol);
                
                // 显示充值信息
                document.getElementById('depositInfo').classList.add('active');
            }
            
            async loadDepositAddress(coinSymbol) {
                try {
                    document.getElementById('depositAddress').textContent = '加载中...';
                    
                    const response = await this.apiRequest(`/api/asset/deposit-address?coin_symbol=${coinSymbol}`);
                    if (response.code === 1) {
                        document.getElementById('depositAddress').textContent = response.data.address;
                        
                        // 显示二维码
                        if (response.data.qr_code) {
                            const qrImage = document.getElementById('qrCodeImage');
                            qrImage.src = response.data.qr_code;
                            qrImage.style.display = 'block';
                        }
                    } else {
                        document.getElementById('depositAddress').textContent = '获取地址失败';
                    }
                } catch (error) {
                    console.error('获取充值地址失败:', error);
                    document.getElementById('depositAddress').textContent = '获取地址失败';
                }
            }
            
            async loadDepositHistory() {
                try {
                    const response = await this.apiRequest('/api/asset/records?type=deposit');
                    if (response.code === 1) {
                        this.renderDepositHistory(response.data.records);
                    }
                } catch (error) {
                    console.error('加载充值记录失败:', error);
                    document.getElementById('depositHistory').innerHTML = '<tr><td colspan="5" class="empty-state">加载失败</td></tr>';
                }
            }
            
            renderDepositHistory(records) {
                const historyElement = document.getElementById('depositHistory');
                
                if (records.length === 0) {
                    historyElement.innerHTML = '<tr><td colspan="5" class="empty-state">暂无充值记录</td></tr>';
                    return;
                }
                
                historyElement.innerHTML = '';
                records.forEach(record => {
                    const row = document.createElement('tr');
                    const statusClass = this.getStatusClass(record.status);
                    const statusText = this.getStatusText(record.status);
                    
                    row.innerHTML = `
                        <td>${new Date(record.created_at).toLocaleString()}</td>
                        <td>${record.coin_symbol}</td>
                        <td>${record.amount}</td>
                        <td class="${statusClass}">${statusText}</td>
                        <td>${record.tx_hash || '-'}</td>
                    `;
                    historyElement.appendChild(row);
                });
            }
            
            getStatusClass(status) {
                const statusMap = {
                    'pending': 'status-pending',
                    'success': 'status-success',
                    'failed': 'status-failed'
                };
                return statusMap[status] || 'status-pending';
            }
            
            getStatusText(status) {
                const statusMap = {
                    'pending': '处理中',
                    'success': '成功',
                    'failed': '失败'
                };
                return statusMap[status] || '未知';
            }
            
            async apiRequest(url, options = {}) {
                const defaultOptions = {
                    headers: {
                        'Authorization': `Bearer ${this.token}`,
                        'Content-Type': 'application/json'
                    }
                };
                
                const response = await fetch(url, { ...defaultOptions, ...options });
                const result = await response.json();
                
                if (response.status === 401) {
                    localStorage.removeItem('token');
                    localStorage.removeItem('user_info');
                    window.location.href = '/auth/login.html';
                    return;
                }
                
                return result;
            }
        }
        
        function copyAddress() {
            const address = document.getElementById('depositAddress').textContent;
            if (address && address !== '加载中...' && address !== '获取地址失败') {
                navigator.clipboard.writeText(address).then(() => {
                    alert('地址已复制到剪贴板');
                }).catch(() => {
                    // 降级方案
                    const textArea = document.createElement('textarea');
                    textArea.value = address;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    alert('地址已复制到剪贴板');
                });
            }
        }
        
        function downloadQR() {
            const qrImage = document.getElementById('qrCodeImage');
            if (qrImage.src) {
                const link = document.createElement('a');
                link.href = qrImage.src;
                link.download = `${depositPage.selectedCoin?.symbol || 'deposit'}_qr.png`;
                link.click();
            }
        }
        
        let depositPage;
        document.addEventListener('DOMContentLoaded', () => {
            depositPage = new DepositPage();
        });
    </script>
</body>
</html>
