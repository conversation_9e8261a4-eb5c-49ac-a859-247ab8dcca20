<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- GVD Favicon设计 -->
  <defs>
    <linearGradient id="faviconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1890ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00d4aa;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="lightningGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f0b90b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffd700;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="16" cy="16" r="15" fill="url(#faviconGradient)"/>
  
  <!-- 内部圆形 -->
  <circle cx="16" cy="16" r="12" fill="#0b1426" opacity="0.8"/>
  
  <!-- 闪电图标 -->
  <path d="M12 6L20 16H16L18 26L10 16H14L12 6Z" fill="url(#lightningGradient)"/>
  
  <!-- 装饰点 -->
  <circle cx="8" cy="8" r="1" fill="url(#lightningGradient)" opacity="0.6"/>
  <circle cx="24" cy="8" r="1" fill="url(#lightningGradient)" opacity="0.6"/>
  <circle cx="8" cy="24" r="1" fill="url(#lightningGradient)" opacity="0.6"/>
  <circle cx="24" cy="24" r="1" fill="url(#lightningGradient)" opacity="0.6"/>
</svg>
